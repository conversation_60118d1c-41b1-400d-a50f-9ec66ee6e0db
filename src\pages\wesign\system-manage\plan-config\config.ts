import type { TableColumnItem } from "@/types"
import type { FormRules } from "element-plus"

export const tabsRouterList = [
  {
    label: "文书分类",
    path: "/system-manage/plan-config/doc",
    name: "systemManage_planConfig_docType"
  },
  {
    label: "目录管理",
    path: "/system-manage/plan-config/directory",
    name: "systemManage_planConfig_directoryManage"
  },
  {
    label: "方案管理",
    path: "/system-manage/plan-config/plan",
    name: "systemManage_planConfig_planManage"
  }
]

/* ======================== 文书类型 ======================== */

export const docTypeFormRules: FormRules = {
  mrClassCode: [{ required: true, message: "请输入分类代码", trigger: "blur" }],
  mrClassName: [{ required: true, message: "请输入分类名称", trigger: "blur" }],
  mrStandardDicWsid: [{ required: false, message: "请选择标准文书类型", trigger: "blur" }],
  mrDataSourceType: [{ required: true, message: "请选择类型", trigger: "blur" }]
}

export const docTypeTableColumns: Array<TableColumnItem> = [
  { prop: "mrClassCode", label: "分类代码", minWidth: 100 },
  { prop: "mrClassName", minWidth: 150, label: "分类名称" },
  { prop: "mrStandardName", label: "标准文书类型", minWidth: 120 },
  { prop: "mrClassLabelValue", label: "文书标签", minWidth: 120 },
  { prop: "dataSourceTypeName", label: "类型", minWidth: 120 },
  { prop: "templateName", label: "合成文书模板", minWidth: 120 },
  { prop: "operation", label: "操作", width: 240, fixed: "right" }
]

/* ======================== 目录管理 ======================== */

export const directoryManageColumns: Array<TableColumnItem> = [
  { prop: "name", label: "目录名称", minWidth: 100 },
  { prop: "type", label: "类型", minWidth: 80 },
  { prop: "code", label: "目录/分类代码", minWidth: 150 },
  { prop: "operation", label: "操作", width: 200, fixed: "right" }
]

export const directoryManageFormRules: FormRules = {
  parentWsid: [{ required: true, message: "请选择所属目录", trigger: "blur" }],
  catalogueName: [{ required: true, message: "请输入目录名称", trigger: "blur" }]
}

/* ======================== 方案管理 ======================== */

export const planManageTableColumns: Array<TableColumnItem> = [
  { prop: "schemeName", label: "方案名称", minWidth: 100 },
  { prop: "schemeCode", minWidth: 150, label: "方案代码" },
  { prop: "describe", minWidth: 150, label: "描述" },
  { prop: "operation", label: "操作", width: 160, fixed: "right" }
]

// 方案表单校验规则
export const schemaFormRules = {
  schemeName: [{ required: true, message: "请输入方案名称", trigger: "blur" }],
  schemeCode: [{ required: true, message: "请输入方案代码", trigger: "blur" }],
  categoryWsid: [{ required: true, message: "请选择方案类型", trigger: "blur" }]
}

export const catalogColumns: Array<TableColumnItem> = [
  { prop: "name", label: "目录名称", minWidth: 100 },
  { prop: "code", minWidth: 150, label: "目录代码" }
]
