<template>
  <PageContainer separate>
    <template #search>
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <CommonInputFormItem v-model="formData.name" label="规则名称" />
        <CommonSelectFormItem v-model="formData.status" label="状态" :options="statusOptions" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="integrityRuleTableRef"
        :table-columns="integrityRuleColumns"
        :request-api="getCompleteRuleListApi"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <template #header>
          <AddButton @click="updateRule('add', '')">新增</AddButton>
        </template>

        <template #applicationDept="{ row }">
          {{ row.applicationDept?.map(item => item.deptName)?.join(",") }}
        </template>

        <template #documentDtos="{ row }">
          {{ row.documentDtos?.map(item => item.documentClassName)?.join(",") }}
        </template>

        <template #operation="{ row }">
          <TableButton @click="updateRule('edit', row)">编辑</TableButton>
          <TableButton @click="modifyStatus(row)">
            {{ row.status === "ENABLE" ? "禁用" : "启用" }}
          </TableButton>
          <TableButton @click="handleDelete(row)">删除</TableButton>
        </template>
      </CommonTable>
    </template>

    <DialogContainer
      v-model:visible="state.dialogVisible"
      class="code-container-dialog"
      :title="state.updateRuleType === 'edit' ? '编辑' : '新增'"
      :width="1200"
      :confirm-callback="handleConfirm"
    >
      <div class="form-container">
        <el-form
          ref="ruleFormRef"
          label-position="right"
          label-width="120px"
          label-suffix="："
          :model="ruleData"
          :rules="integrityRuleFormRules"
        >
          <CommonInputFormItem v-model="ruleData.name" label="规则名称" prop="name" />

          <!-- 条件规则 -->
          <el-form-item label="" prop="conditionDtos" class="container-form">
            <div class="condition-container">
              <div class="condition-container-header">满足条件时</div>
              <div class="condition-container-content">
                <div class="form-list-container">
                  <VueDraggable
                    v-if="ruleData.conditionDtos.length > 0"
                    v-model="ruleData.conditionDtos"
                    :animation="150"
                    handle=".handle"
                    class="filter-template-list"
                  >
                    <div v-for="(conditionDto, index) in ruleData.conditionDtos" :key="index" class="param-item">
                      <i class="handle ri-draggable"></i>

                      <!-- 条件类型 -->
                      <el-select
                        v-model="conditionDto.conditionType"
                        filterable
                        @change="$event => updateConditionType($event, conditionDto)"
                      >
                        <el-option
                          v-for="condition in conditions"
                          :key="condition.value"
                          :label="condition.value"
                          :value="condition.key"
                        />
                      </el-select>

                      <!-- 子条件 -->
                      <el-select
                        v-model="conditionDto.subConditionName"
                        placeholder="请选择"
                        filterable
                        @change="updateSubConditionName(conditionDto)"
                      >
                        <el-option
                          v-for="subCondition in conditions.find(data => data.key === conditionDto.conditionType)
                            ?.subConditions"
                          :key="subCondition.value"
                          :label="subCondition.value"
                          :value="subCondition.key"
                        />
                      </el-select>

                      <!-- 操作符号 -->
                      <el-select
                        v-model="conditionDto.operator"
                        placeholder="请选择"
                        filterable
                        @change="handleOperatorChange($event, conditionDto)"
                      >
                        <el-option
                          v-for="comparisonSymbol in comparisonSymbols"
                          :key="comparisonSymbol.value"
                          :label="comparisonSymbol.value"
                          :value="comparisonSymbol.key"
                        />
                      </el-select>

                      <!-- 条件值 -->
                      <template v-if="conditionDto.formValueType !== 3">
                        <!-- 当前formType为下拉时 -->
                        <template v-if="conditionDto.formType === 'SELECT'">
                          <!-- 存在option -->
                          <el-select
                            v-if="hasOp(conditionDto)"
                            v-model="conditionDto.conditionValue"
                            placeholder="请选择"
                            filterable
                            :multiple="conditionDto.formValueType === 2"
                          >
                            <el-option
                              v-for="condition in getSelectOptions(conditionDto, 'op')"
                              :key="condition.value"
                              :label="condition.value"
                              :value="condition.key"
                            />
                          </el-select>

                          <!-- 后端返回url -->
                          <el-select
                            v-else-if="hasUrl(conditionDto)"
                            v-model="conditionDto.conditionValue"
                            :multiple="conditionDto.formValueType === 2"
                            fit-input-width
                            filterable
                            remote
                            reserve-keyword
                            placeholder="请选择"
                            :remote-method="val => handleOptionsChange(val, conditionDto)"
                          >
                            <el-option
                              v-for="condition in getSelectOptions(conditionDto, 'url')"
                              :key="condition.value"
                              :label="condition.value"
                              :value="condition.key"
                            />
                          </el-select>

                          <!-- 返回standardOpKey -->
                          <el-select
                            v-else-if="hasStandardOpKey(conditionDto)"
                            v-model="conditionDto.conditionValue"
                            filterable
                            :multiple="conditionDto.formValueType === 2"
                            placeholder="请选择"
                          >
                            <el-option
                              v-for="condition in getSelectOptions(conditionDto, 'standardOpKey')"
                              :key="condition.value"
                              :label="condition.value"
                              :value="condition.key"
                            />
                          </el-select>
                        </template>

                        <!-- 日期选择 -->
                        <template v-else-if="conditionDto.formType === 'DATE'">
                          <el-date-picker
                            v-model="conditionDto.conditionValue"
                            :style="{ minWidth: conditionDto.formValueType === 1 ? '200px' : '340px' }"
                            :type="conditionDto.formValueType === 1 ? 'datetime' : 'datetimerange'"
                            placeholder="请选择日期"
                            value-format="x"
                          />
                        </template>

                        <!-- 文本框 -->
                        <el-input
                          v-else-if="conditionDto.formType === 'TEXT'"
                          v-model="conditionDto.conditionValue"
                          :placeholder="getInputPlaceholder(conditionDto)"
                        />
                      </template>

                      <!-- 逻辑类型 -->
                      <el-select
                        v-model="conditionDto.logicalOperator"
                        style="min-width: 130px"
                        placeholder="请选择逻辑类型"
                        filterable
                        clearable
                        @change="$event => selectLogicalOperator($event, index)"
                      >
                        <el-option
                          v-for="option in logicalOperatorOptions"
                          :key="option.value"
                          :label="option.label"
                          :value="option.value"
                        ></el-option>
                      </el-select>

                      <!-- 删除 -->
                      <el-button
                        v-if="ruleData.conditionDtos.length > 1"
                        link
                        @click="handleDeleteParam('conditionDtos', conditionDto.sn)"
                      >
                        <i class="ri-delete-bin-line delete-icon"></i>
                      </el-button>
                    </div>
                  </VueDraggable>
                </div>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="关联文书分类" prop="documentDtos">
            <div class="form-list-container">
              <div v-for="(documentDto, index) in ruleData.documentDtos" :key="index" class="param-item">
                <el-select
                  v-model="documentDto.documentClassCode"
                  filterable
                  @change="$event => handleSelectDocument($event, documentDto)"
                >
                  <el-option
                    v-for="dataMrOption in dataMrOptions"
                    :key="dataMrOption.value"
                    :label="dataMrOption.label"
                    :value="dataMrOption.value"
                  />
                </el-select>
                <el-input v-model="documentDto.documentName" placeholder="请输入文书名称" />
                <el-button link @click="handleDeleteParam('documentDtos', index)">
                  <i class="ri-delete-bin-line delete-icon"></i>
                </el-button>
              </div>
            </div>
            <el-button :icon="Plus" plain type="primary" style="margin-top: 10px" @click="handleAdd('documentDtos')">
              添加
            </el-button>
          </el-form-item>

          <CommonInputFormItem v-model="ruleData.description" label="描述" prop="description" />

          <el-form-item label="应用科室" prop="applicationDept">
            <el-select v-model="ruleData.selectDept" clearable filterable multiple @change="handleChangeDept">
              <el-option
                v-for="department in departmentList"
                :key="department.deptCode"
                :label="department.deptName"
                :value="department.deptCode"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </DialogContainer>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from "vue"
import { cloneDeep, debounce } from "lodash"
import { VueDraggable } from "vue-draggable-plus"
import { Plus } from "@element-plus/icons-vue"
import {
  CommonSelectFormItem,
  CommonInputFormItem,
  SearchContainer,
  AddButton,
  TableButton,
  CommonTable,
  DialogContainer,
  PageContainer
} from "@/base-components"
import { useCommonOptions, useTableSearch } from "@/hooks"
import { getMrClassList, searchMetaCodeTableFieldApi } from "@/interfaces"
import axios from "@/interfaces/axios-instance"
import { SystemPrompt, Message, toastError, SystemAlert, formatDatetime } from "@/utils"
import {
  editCompleteRuleStatusApi,
  deleteCompleteRuleApi,
  getCascadeConditionApi,
  getDepartmentListApi,
  getCompleteRuleListApi,
  addCompleteRuleApi,
  editCompleteRuleApi
} from "../interface"
import {
  integrityRuleColumns,
  checkTypeOptions,
  statusOptions,
  getQcPointDesc,
  integrityRuleFormRules,
  logicalOperatorOptions
} from "./config"
import type { FormInstance } from "element-plus"

const { getOptionLabel: getCheckTypeDesc } = useCommonOptions({ optionsDataSource: checkTypeOptions })

// 获取所有文书分类选项
const { options: dataMrOptions } = useCommonOptions({
  getOptionsApi: getMrClassList,
  labelAlias: "mrClassName",
  valueAlias: "mrClassCode"
})

/* ======================== 搜索相关数据及方法 ======================== */

const formData = reactive({
  checkType: "",
  name: "",
  qcPoint: "",
  status: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(formData)

/* ======================== 表格相关方法 ======================== */

const integrityRuleTableRef = ref<InstanceType<typeof CommonTable>>()

function dataCallback(data: Array<Record<string, any>>) {
  return data.map(item => ({
    ...item,
    checkTypeEnum: getCheckTypeDesc(item.checkType),
    qcPointEnum: getQcPointDesc(item.qcPoint),
    statusText: item.status === "ENABLE" ? "已启用" : "已禁用"
  }))
}

// 切换启用&禁用状态
const modifyStatus = row => {
  const nextStatus = row.status === "ENABLE" ? "DISABLE" : "ENABLE"
  SystemPrompt(`是否确定${row.status === "ENABLE" ? "禁用" : "启用"}规则${row.name} `).then(() => {
    modifyDataSourcesInfo(row.wsid, nextStatus)
  })
}

// 删除
function handleDelete(row) {
  SystemPrompt(`您确定要删除规则${row.name} ?`).then(() => {
    deleteCompleteRuleApi(row.wsid)
      .then(() => {
        Message.success("删除数据成功")
        integrityRuleTableRef.value?.refreshTableData()
      })
      .catch(err => toastError(err))
  })
}

const modifyDataSourcesInfo = (wsid: string, nextStatus: "ENABLE" | "DISABLE" | "DEL") => {
  editCompleteRuleStatusApi({ wsid: wsid, status: nextStatus })
    .then(() => {
      Message.success("修改状态成功")
      integrityRuleTableRef.value?.refreshTableData()
    })
    .catch(err => toastError(err))
}

/* ======================== 弹窗相关数据及方法 ======================== */

const ruleFormRef = ref<FormInstance>()

const state = reactive({
  dialogVisible: false,
  associateScoreStandard: false,
  elementMetadata: "",
  elementMetadataList: [] as Array<any>,
  updateRuleType: ""
})

interface departmentListOption {
  deptCode: string
  deptName: string
  wsid: string
}
const departmentList = ref<Array<departmentListOption>>([])

const ruleData = reactive({
  wsid: "", //规则wsid
  name: "", //规则名称
  //逻辑条件
  conditionDtos: [
    {
      conditionType: "",
      subConditionName: "",
      operator: "",
      conditionValue: "",
      logicalOperator: "",
      formType: "", // 表单类型（不需要传递给后端）
      formValueType: 1, // form值的类型 1 单选 2 多选 3 无 （不需要传给后端）
      sn: 0
    }
  ],
  // 应用科室
  applicationDept: [
    {
      deptCode: "",
      deptName: "",
      wsid: ""
    }
  ],
  //关联文书分类
  documentDtos: [
    {
      documentClassCode: "",
      documentClassName: "",
      documentName: ""
    }
  ],
  description: "", //描述
  selectDept: [] as Array<string>
})

// 新增或编辑规则
const updateRule = (type: string, data: any) => {
  console.log(data)
  state.dialogVisible = true
  state.updateRuleType = type
  Object.keys(ruleData).forEach(key => {
    ruleData[key] = data ? data[key] : ruleData[key]
  })
  ruleData.conditionDtos.forEach(conditionDto => {
    // 设置formType
    const currentCondition = conditions.value.find(condition => condition.key === conditionDto.conditionType)
    const formType = currentCondition?.subConditions.find(
      subCondition => subCondition.key === conditionDto.subConditionName
    ).formType
    conditionDto.formType = formType || ""
    // 设置formTypeValue
    const formValueType = comparisonSymbols.value.find(symbol => symbol.key === conditionDto.operator)?.valueType
    conditionDto.formValueType = formValueType

    // 当表单为日期范围时，且值不为数组，切分为数组结构的时间戳展示（conditionValue = 2022-01-01 00:00:00,2022-01-01 00:00:00）
    if (formType === "DATE" && formValueType === 2 && !Array.isArray(conditionDto.conditionValue)) {
      // @ts-ignore
      conditionDto.conditionValue = conditionDto.conditionValue.split(",").map(val => new Date(val))
    }
    // 当表单为下拉表单时，且值不为数组，切分为数组结构
    if (formType === "SELECT" && formValueType === 2 && !Array.isArray(conditionDto.conditionValue)) {
      // @ts-ignore
      conditionDto.conditionValue = conditionDto.conditionValue.split(",")
    }
  })
  ruleData.selectDept = ruleData?.applicationDept?.map(item => item.deptCode)
  if (type === "add") {
    ruleData.conditionDtos = [
      {
        conditionType: "",
        subConditionName: "",
        operator: "",
        conditionValue: "",
        logicalOperator: "",
        formType: "",
        formValueType: 1,
        sn: 0
      }
    ]
    // 关联科室
    ruleData.applicationDept = [
      {
        deptCode: "",
        deptName: "",
        wsid: ""
      }
    ] // 应用科室
    ruleData.documentDtos = [
      {
        documentClassCode: "",
        documentClassName: "",
        documentName: ""
      }
    ]
    ruleData.selectDept = []
    ruleData.name = ""
    ruleData.description = ""
  }
}

// 保存数据
function handleConfirm() {
  const requestData = cloneDeep(ruleData)
  requestData.documentDtos.forEach(documentDto => {
    if (documentDto.documentClassName && !documentDto.documentName) {
      documentDto.documentName = documentDto.documentClassName
    }
  })
  for (let conditionDto of requestData.conditionDtos) {
    // 当表单形式为日期时，将时间戳转为字符串
    if (conditionDto.formType === "DATE") {
      // 单个日期
      if (conditionDto.formValueType === 1) {
        if (!conditionDto.conditionValue) {
          SystemAlert("请选择日期")
          return
        }
        conditionDto.conditionValue = formatDatetime(new Date(conditionDto.conditionValue))
      }
      // 日期范围
      else if (conditionDto.formValueType === 2) {
        if (!conditionDto.conditionValue[0] || !conditionDto.conditionValue[1]) {
          SystemAlert("请选择日期范围")
          return
        }
        // @ts-ignore
        conditionDto.conditionValue[0] = formatDatetime(new Date(conditionDto.conditionValue[0]))
        // @ts-ignore
        conditionDto.conditionValue[1] = formatDatetime(new Date(conditionDto.conditionValue[1]))
      }
    }
    // 数组形式的值使用逗号连接
    if (Array.isArray(conditionDto.conditionValue)) {
      conditionDto.conditionValue = conditionDto.conditionValue.join(",")
    }
    // 当表单为输入框时，判断conditionValue是否用逗号连接，否则提示错误
    if (conditionDto.formType === "TEXT" && conditionDto.formValueType === 2) {
      const originConditionValue = conditionDto.conditionValue
      conditionDto.conditionValue = originConditionValue.replaceAll("，", ",")
      const commaCount = conditionDto.conditionValue.split(",").length - 1
      if (commaCount !== 1) {
        SystemAlert(`您输入的条件值为${originConditionValue}的条件值格式错误，请使用逗号分隔区间`)
        return
      }
    }
  }
  for (let i = 0; i < requestData.conditionDtos.length; i++) {
    if (i !== requestData.conditionDtos.length - 1 && !requestData.conditionDtos[i].logicalOperator) {
      SystemAlert(`请选择第${i + 1}个逻辑类型`)
      return
    }
  }
  ruleFormRef.value?.validate(valid => {
    if (!valid) return
    const handler = state.updateRuleType === "add" ? addCompleteRuleApi(requestData) : editCompleteRuleApi(requestData)
    handler
      .then(() => {
        Message.success(state.updateRuleType === "add" ? "添加规则成功" : "编辑规则成功")
        state.dialogVisible = false
        integrityRuleTableRef.value?.refreshTableData()
      })
      .catch(err => toastError(err))
  })
}

/* ======================== 弹窗相关数据及方法 ======================== */
// 比较符号
const comparisonSymbols = ref<Array<Record<string, any>>>([])
// 级联条件
const conditions = ref<Array<Record<string, any>>>([])

// 初始化树形数据
onMounted(async () => {
  // 关联数据
  const res = (await getCascadeConditionApi())?.data?.data
  comparisonSymbols.value = res?.comparisonSymbols
  conditions.value = res?.conditions
  conditions.value.forEach(async item => {
    if (item.subConditions) {
      item.subConditions.forEach(async data => {
        if (data.url) {
          data.options = await getOptions(data.url)
        } else if (data.standardOpKey) {
          data.options = await getStandardOptions(data.standardOpKey)
        }
      })
    }
  })

  // 科室列表数据
  departmentList.value = (await getDepartmentListApi())?.data?.map(item => {
    return {
      deptName: item.deptName,
      deptCode: item.deptCode,
      wsid: item.wsid
    }
  })
})

// 选择逻辑类型时，新增下一个条件
const selectLogicalOperator = (e, index) => {
  if (!e) return
  if (!ruleData.conditionDtos[index + 1]) {
    ruleData.conditionDtos.push({
      conditionType: "",
      subConditionName: "",
      operator: "",
      conditionValue: "",
      logicalOperator: "",
      formType: "",
      formValueType: 1,
      sn: ruleData.conditionDtos.length
    })
  }
}

//表单增加操作 (条件、文书分类)
const handleAdd = key => {
  if (key === "documentDtos") {
    ruleData[key].push({
      documentClassCode: "",
      documentClassName: "",
      documentName: ""
    })
  } else {
    ruleData.conditionDtos.push({
      conditionType: "",
      subConditionName: "",
      operator: "",
      conditionValue: "",
      logicalOperator: "",
      formType: "",
      formValueType: 1,
      sn: ruleData.conditionDtos.length
    })
  }
}

// 根据url获取option
const getOptions = async (url: string) => {
  return (
    await axios({
      method: "get",
      url: `api${url}`
    })
  )?.data?.data?.map(item => {
    return {
      key: item.code || item.key,
      value: item.name || item.value
    }
  })
}

// 根据standardOpKey获取option
const getStandardOptions = async (standardOpKey: string) => {
  return (
    await searchMetaCodeTableFieldApi({
      offset: 0,
      limit: 25,
      filters: `code=${standardOpKey}`
    })
  )?.data?.data?.rows?.map(item => {
    return {
      key: item.code,
      value: item.value
    }
  })
}

const hasOp = conditionDto => {
  return conditions.value
    .find(condition => condition.key === conditionDto.conditionType)
    ?.subConditions?.find(subCondition => subCondition.key === conditionDto.subConditionName)?.op
}

const hasUrl = conditionDto => {
  return conditions.value
    .find(condition => condition.key === conditionDto.conditionType)
    ?.subConditions?.find(subCondition => subCondition.key === conditionDto.subConditionName)?.url
}

const hasStandardOpKey = conditionDto => {
  return conditions.value
    .find(condition => condition.key === conditionDto.conditionType)
    ?.subConditions?.find(subCondition => subCondition.key === conditionDto.subConditionName)?.standardOpKey
}

const getSelectOptions = (conditionDto, type: "url" | "op" | "standardOpKey") => {
  const subCondition = conditions.value
    .find(condition => condition.key === conditionDto.conditionType)
    ?.subConditions?.find(subCondition => subCondition.key === conditionDto.subConditionName)
  if (type === "op") {
    return subCondition.op
  } else {
    return subCondition.options
  }
}

// 选择科室
const handleChangeDept = val => {
  ruleData.applicationDept = departmentList.value.filter(item => val.includes(item.deptCode))
}

// 选择文书
const handleSelectDocument = (event, item) => {
  item.documentClassName = dataMrOptions.value.find(data => data.value === event)?.label
}

// 删除操作
const handleDeleteParam = (key, index) => {
  ruleData[key].splice(index, 1)
}

// 模糊搜索
const handleOptionsChange = debounce(async (val, conditionDto) => {
  let url = conditions.value
    .find(data => data.key === conditionDto.conditionType)
    ?.subConditions?.find(data => data.key === conditionDto.subConditionName)?.url
  url = url.split("?")[0] + "?item=" + val
  const currentSubConditions = conditions.value.find(data => data.key === conditionDto.conditionType)?.subConditions
  if (currentSubConditions) {
    currentSubConditions.find(data => data.key === conditionDto.subConditionName).options = await getOptions(url)
  }
}, 500)

// 重置条件
const updateConditionType = (e, conditionDto) => {
  conditionDto.subConditionName = ""
  conditionDto.conditionValue = ""
}

// 子条件改变，获取当前子条件的formType
const updateSubConditionName = conditionDto => {
  const currentCondition = conditions.value.find(condition => condition.key === conditionDto.conditionType)
  const formType = currentCondition?.subConditions.find(
    subCondition => subCondition.key === conditionDto.subConditionName
  ).formType
  conditionDto.formType = formType || ""
  initConditionValue(conditionDto)
}

// 条件类型改变
const handleOperatorChange = (e, conditionDto) => {
  const formValueType = comparisonSymbols.value.find(symbol => symbol.key === e)?.valueType
  conditionDto.formValueType = formValueType
  initConditionValue(conditionDto)
}

// 初始化条件值
const initConditionValue = conditionDto => {
  const { formType, formValueType } = conditionDto
  if (formType === "SELECT") {
    if (formValueType === 1) {
      conditionDto.conditionValue = ""
    } else if (formValueType === 2) {
      conditionDto.conditionValue = []
    }
  } else if (formType === "DATE") {
    if (formValueType === 1) {
      conditionDto.conditionValue = ""
    } else if (formValueType === 2) {
      conditionDto.conditionValue = ["", ""]
    }
  } else {
    conditionDto.conditionValue = ""
  }
}

// 获取条件值input的提示语
const getInputPlaceholder = conditionDto => {
  if (conditionDto.formValueType === 1) {
    return "请输入"
  } else if (conditionDto.formValueType === 2) {
    return "请使用逗号分割区间"
  } else {
    return "请输入"
  }
}
</script>

<style lang="less" scoped>
.form-container {
  display: flex;

  :deep(.el-form) {
    width: 100%;
    margin-right: 15px;
  }

  .search-container {
    width: 30%;
    padding-bottom: 18px;

    .search-wrap {
      height: 100%;
      // border: 1px solid #ebedf0;
    }

    :deep(.el-input__wrapper) {
      border-radius: 0;
    }

    .search-type {
      display: flex;
    }

    .search-result {
      display: flex;
      flex-wrap: wrap;
      border: 1px solid #ebedf0;
    }
    .title {
      width: 50%;
      text-align: center;
      height: 30px;
      line-height: 30px;
      background: #ebedf0;
    }

    .result-wrap {
      // height: 515px;
      height: 600px;
      overflow-y: auto;
      width: 100%;
    }

    .result {
      width: 100%;
      display: flex;
      line-height: 30px;
      height: 30px;
      cursor: pointer;
      &:hover {
        background: #ebedf0;
      }

      span {
        width: 50%;
        text-align: center;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        height: 100%;
      }
    }
  }
}

.tips-content {
  color: #fff;
}

.search-title {
  width: 100%;

  :deep(.el-radio-button) {
    width: 50%;
  }

  :deep(.el-radio-button__inner) {
    width: 100%;
  }
}

.annotated {
  white-space: pre-wrap;
}

.fun-result-wrap {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:deep(.cm-editor) {
  border: 1px solid #dcdfe6;
}

:deep(.cm-focused) {
  outline: none !important;
}

:deep(.cm-lineNumbers) {
  border-right: 1px solid #dcdfe6;
}

.code-container-dialog {
  :deep(.el-dialog__body) {
    max-height: 80vh;
    overflow-y: auto;
  }
}
.param-item {
  display: flex;
  justify-content: space-between;
  width: 100%;
  gap: 15px;
  margin-bottom: 10px;
}

.form-list-container {
  max-height: 20vh;
  overflow-y: auto;
  width: calc(100% - 10px);
  padding-right: 10px;
}
.delete-icon {
  color: #fe4f3d;
}

.condition-container {
  width: 100%;
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;

  &-header {
    background: #e1e4e9;
    padding: 8px 20px;
    line-height: 1;
    font-weight: bold;
    font-size: 14px;
    color: #0a1633;
    height: 24px;
    display: flex;
    align-items: center;
  }

  &-content {
    background-color: #eff2f7;
    padding: 16px 20px;

    .or-btn {
      background-color: #f2a900;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8px;
      color: #fff;
      margin: 20px 0;
    }
  }
}

.container-form {
  :deep(.el-form-item__content) {
    margin-left: 0 !important;
  }
}
</style>
