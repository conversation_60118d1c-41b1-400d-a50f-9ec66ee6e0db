import axios from "@/interfaces/axios-instance"

// 获取公告列表
export function getAnnouncementsApi(params: IPaginationRequestParams) {
  return axios({
    method: "GET",
    url: "/api/system/user-current/notices",
    params
  })
}

interface MessageParams extends IPaginationRequestParams {
  typeName: string
}

// 获取待处理/系统通知列表
export function getMessageOrDisposeApi(params: MessageParams) {
  return axios({
    method: "GET",
    url: "/api/system/user-current/messages",
    params
  })
}

// 一键已读公告
export function readAllAnnouncementsApi() {
  return axios({
    method: "PUT",
    url: "/api/system/user-current/notices/readAll"
  })
}

// 一键已读待处理/系统通知
export function readAllMessageOrDisposeApi(params) {
  return axios({
    method: "PUT",
    url: "/api/system/user-current/messages/readAll",
    params: params
  })
}

// 单击已读系统公告
export function readAnnouncementApi(id: string) {
  return axios({
    method: "PUT",
    url: `/api/system/user-current/notices/${id}`
  })
}

// 单击已读待处理/系统通知
export function readMessageOrDisposeApi(id: string) {
  return axios({
    method: "PUT",
    url: `/api/system/user-current/messages/${id}`
  })
}
