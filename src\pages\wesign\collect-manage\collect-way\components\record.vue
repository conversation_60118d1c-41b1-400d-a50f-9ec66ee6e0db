<template>
  <DialogContainer
    v-model:visible="state.recordVisibleDialog"
    title="执行记录"
    :width="800"
    :no-footer="true"
    :close-callback="handleClose"
  >
    <SearchContainer @query-btn-click="handleRecordQuery" @reset-btn-click="handleRecordReset">
      <el-form-item label="采集时间" style="max-width: 400px">
        <el-date-picker
          v-model="recordSearchState.executionTime"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :clearable="true"
          @change="changeDate"
        />
      </el-form-item>
    </SearchContainer>
    <div v-loading="confirmLoading" class="table-container">
      <BaseTable ref="commonTableRef" :columns="recordTableColumns" :data="recordSearchState.displayTableData">
        <template #executionTime="{ row }">
          {{ formatDatetime(row.executionTime) }}
        </template>
      </BaseTable>
    </div>
    <el-pagination
      v-model:current-page="paginationConfig.currentPage"
      v-model:page-size="paginationConfig.pageSize"
      :page-sizes="[10, 25, 50, 100]"
      :background="true"
      layout="sizes, prev, pager, next, jumper, total"
      :total="recordSearchState.tableData.length"
      @update:page-size="resetCurrentPage"
    />
  </DialogContainer>
</template>

<script setup lang="ts">
import { reactive, ref, watch } from "vue"
import { cloneDeep } from "lodash-es"
import { SearchContainer, DialogContainer, BaseTable } from "@/base-components"
import { formatDatetime, toastError } from "@/utils"
import { recordTableColumns } from "../config"
import { getCollectRecordList } from "../interface"
import type { DateModelType } from "element-plus"

const emits = defineEmits(["close"])

const changeDate = value => {
  if (value && value.length === 2) {
    const [start, end] = value
    const maxRange = 7 * 24 * 60 * 60 * 1000 // 7天的毫秒数
    if (end.getTime() - start.getTime() > maxRange) {
      // 如果选择范围超过7天，则重置为7天
      end.setTime(start.getTime() + maxRange)
    } else if (start.getTime() === end.getTime()) {
      // 如果选择的是同一天
      end.setTime(start.getTime() + (24 * 60 * 60 * 1000 - 1000))
    }
    recordSearchState.executionTime = [start.getTime(), end.getTime()] // 更新日期范围
  }
}

interface RecordProps {
  recordVisibleDialog: boolean
  selectedRow: Record<string, any>
}

const props = withDefaults(defineProps<RecordProps>(), {
  recordVisibleDialog: false
})

const state = reactive({
  recordVisibleDialog: false
})

watch(
  () => props.recordVisibleDialog,
  val => {
    state.recordVisibleDialog = cloneDeep(val)
  }
)

const recordSearchParams = ref("")

const recordSearchState = reactive({
  executionTime: [new Date().getTime() - 7 * 24 * 60 * 60 * 1000, new Date().getTime()] as [
    DateModelType,
    DateModelType
  ],
  tableData: [],
  displayTableData: []
})

const paginationConfig = reactive({
  currentPage: 1,
  pageSize: 25
})
const confirmLoading = ref(false)

// 查询，设置filters
const handleRecordQuery = () => {
  if (recordSearchState.executionTime?.length) {
    const integrationWsid =
      props.selectedRow.dbCollectIntrarationWsid ||
      props.selectedRow.ftpCollectIntrarationWsid ||
      props.selectedRow.interfaceCollectIntrarationWsid ||
      props.selectedRow.targetWsid
    console.log(`output->integrationWsid`, integrationWsid)
    recordSearchParams.value = `executionTime>=${recordSearchState.executionTime[0]},executionTime<=${recordSearchState.executionTime[1]},integrationWsid=${integrationWsid}`
  } else {
    recordSearchParams.value = ""
  }
}

const getTableData = async () => {
  if (confirmLoading.value) return
  confirmLoading.value = true
  await getCollectRecordList({
    filters: recordSearchParams.value,
    dataType: props.selectedRow?.dataType,
    scene: props.selectedRow?.scene
  })
    .then(res => {
      recordSearchState.tableData = res.data.data.rows
      recordSearchState.displayTableData = cloneDeep(recordSearchState.tableData).splice(
        (paginationConfig.currentPage - 1) * paginationConfig.pageSize,
        paginationConfig.currentPage * paginationConfig.pageSize
      )
      confirmLoading.value = false
    })
    .catch(err => {
      confirmLoading.value = false
      toastError(err, "获取数据失败")
    })
}

// 只有日期值变化时才重新请求数据
watch(
  () => recordSearchParams.value,
  val => {
    getTableData()
  }
)

// 打开弹窗时重新请求
watch(
  () => props.recordVisibleDialog,
  val => {
    if (val) {
      handleRecordReset()
    }
  }
)

// 翻页时更新table数据
watch(
  () => paginationConfig,
  val => {
    recordSearchState.displayTableData = cloneDeep(recordSearchState.tableData).splice(
      (paginationConfig.currentPage - 1) * paginationConfig.pageSize,
      paginationConfig.currentPage * paginationConfig.pageSize
    )
  },
  {
    deep: true
  }
)

// 重置搜索
const handleRecordReset = () => {
  const integrationWsid =
    props.selectedRow.dbCollectIntrarationWsid ||
    props.selectedRow.ftpCollectIntrarationWsid ||
    props.selectedRow.interfaceCollectIntrarationWsid ||
    props.selectedRow.targetWsid
  recordSearchState.executionTime = [new Date().getTime() - 7 * 24 * 60 * 60 * 1000, new Date().getTime()]
  recordSearchParams.value = `executionTime>=${recordSearchState.executionTime[0]},executionTime<=${recordSearchState.executionTime[1]},integrationWsid=${integrationWsid}`
}

// 重置页码为首页
function resetCurrentPage() {
  paginationConfig.currentPage = 1
}

// 关闭弹窗
const handleClose = () => {
  emits("close")
}
</script>

<style lang="less" scoped>
.common-table-container {
  height: 500px !important;
}

:deep(.el-dialog) {
  height: 800px !important;
}

:deep(.el-dialog__body) {
  height: calc(100% - 96px);
  display: flex;
  flex-direction: column;
}

.table-container {
  height: 100%;
  margin-bottom: 20px;
  flex: 1;
}
</style>
