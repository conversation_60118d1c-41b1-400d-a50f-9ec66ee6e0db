<template>
  <PageContainer separate>
    <template #search>
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <CommonSelectFormItem v-model="formData.checkType" label="检查类型" :options="checkTypeOptions" />

        <CommonInputFormItem v-model="formData.name" label="规则名称" />

        <CommonSelectFormItem v-model="formData.qcPoint" filterable label="质控点" :options="qcPointOptions" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="ruleConfigTableRef"
        :table-columns="ruleConfigColumns"
        :request-api="getQualityControlRuleList"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <template #header>
          <AddButton @click="updateRule('add', '')">新增</AddButton>
        </template>
        <template #operation="{ row }">
          <TableButton @click="updateRule('edit', row)">编辑</TableButton>
          <TableButton @click="modifyStatus(row)">
            {{ row.status === "ENABLE" ? "禁用" : "启用" }}
          </TableButton>
          <TableButton @click="handleDelete(row)">删除</TableButton>
        </template>
      </CommonTable>
    </template>

    <DialogContainer
      v-model:visible="state.dialogVisible"
      class="code-container-dialog"
      :title="state.updateRuleType === 'edit' ? '修改规则配置' : '添加规则配置'"
      :width="980"
      :confirm-callback="handleConfirm"
    >
      <div class="form-container">
        <el-form
          ref="ruleFormRef"
          label-position="right"
          label-width="130px"
          label-suffix="："
          :model="ruleData"
          :rules="ruleConfigFormRules"
        >
          <CommonInputFormItem v-model="ruleData.name" label="规则名称" prop="name" />

          <CommonSelectFormItem
            v-model="ruleData.type"
            label="质控类型"
            prop="type"
            :options="typeOptions"
            @change="getQcTargetOptions"
          />
          <el-form-item label="质控点" prop="qcPoint">
            <el-select
              v-model="ruleData.qcPoint"
              class="score-select"
              placeholder="请选择质控点"
              filterable
              fit-input-width
              reserve-keyword
              multiple
            >
              <el-option
                v-for="item in qcPointOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="规则表达式" prop="ruleExpression">
            <template #label>
              <el-popover
                placement="top"
                width="800"
                trigger="click"
                :popper-style="{ background: 'rgba(0, 0, 0, 0.5)' }"
              >
                <div class="tips-content">
                  <p>{{ "常见的比较运算符==，！= ，>,<,>=,<=" }}</p>
                  <p>contains,判断左边的值是否包含右边的值，如： var contains "Foo"</p>
                  <p>is/instance of ,判断左边的值是否是右边的类的实例，如:var instanceof Integer</p>
                  <p>strsim,比较两个字符串的相似度，返回一个百分数，如； "foobie" strsim "foobar"</p>
                  <p>soundslike,比较两个字符串的发音，如："foobar" soundslike "fubar"</p>
                  <hr />
                  <p>逻辑运算符：</p>
                  <p>&&，|| or，用于多个值间进行逻辑或运算，如：foo or bar or barfoo or 'N/A'</p>
                  <p>
                    ~=，正则表达式匹配符，如：foo ~= '[a-z].+' 注意 有转义的符号要转义 如：A29 ~=
                    '^[\\u4e00-\\u9fa5]{0,}$'
                  </p>
                </div>
                <template #reference>
                  <div style="display: flex; align-items: center">
                    <span>规则表达式</span>
                    <el-icon><QuestionFilled /></el-icon>
                    <span>：</span>
                  </div>
                </template>
              </el-popover>
            </template>

            <!-- <el-input
              v-model="ruleData.ruleExpression"
              placeholder="请输入规则表达式"
              type="textarea"
              resize="none"
              :rows="5"
            ></el-input> -->
            <Codemirror
              ref="code"
              v-model="ruleData.ruleExpression"
              placeholder="请输入规则表达式"
              :tab-size="10"
              :extensions="extensions"
              :style="{ height: '100%', width: '100%' }"
            />
          </el-form-item>

          <el-form-item :error="validationMessage">
            <template #label>{{ ` ` }}</template>
            <el-input v-model="validationInput" placeholder="请输入规则表达式的测试值">
              <template #append>
                <el-button
                  type="primary"
                  style="background-color: var(--el-color-primary); color: #fff; border-radius: 0"
                  @click="handleExpressionCheck"
                >
                  点击验证
                </el-button>
              </template>
            </el-input>
          </el-form-item>

          <CommonInputFormItem v-model="ruleData.promptContent" label="提示内容" prop="promptContent" />

          <!-- <CommonInputFormItem v-model="ruleData.consult" label="参考" prop="consult" /> -->
          <el-form-item label="界面定位元素" prop="qcTarget">
            <el-tree-select
              v-model="ruleData.qcTarget"
              class="score-select"
              placeholder="界面定位元素"
              filterable
              fit-input-width
              reserve-keyword
              default-expand-all
              :data="qcTargetOptions"
            >
              <template #title="{ node, data }">
                <div class="content">
                  <span style="margin-right: 4px">{{ data.fileName }}</span>
                </div>
              </template>
              <!-- <el-option
                v-for="item in qcTargetOptions"
                :key="item.wsid"
                :label="item.label"
                :value="item.wsid"
              ></el-option> -->
            </el-tree-select>
          </el-form-item>

          <CommonSelectFormItem
            v-model="ruleData.checkType"
            label="检查类型"
            prop="checkType"
            :options="checkTypeOptions"
          />

          <el-form-item label="控制级别" prop="controlLevel">
            <el-radio-group v-model="ruleData.controlLevel">
              <el-radio label="REMIND">提醒</el-radio>
              <el-radio label="WARN">警告</el-radio>
              <el-radio label="ERROR">错误</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="ruleData.qcPoint !== 'HQMS_REPORT'" label="关联评分项" prop="associateScoreStandard">
            <el-radio-group v-model="state.associateScoreStandard">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="state.associateScoreStandard" label="评分项" prop="qcScoreStandardWsid">
            <el-select
              v-model="ruleData.qcScoreStandardWsid"
              class="score-select"
              placeholder="请选择评分项"
              filterable
              fit-input-width
              reserve-keyword
            >
              <el-option
                v-for="item in state.scoreList"
                :key="item.wsid"
                :label="item.label"
                :value="item.wsid"
              ></el-option>
            </el-select>
          </el-form-item>

          <CommonInputFormItem v-model="ruleData.score" label="规则扣分" prop="score" />
        </el-form>

        <div class="search-container">
          <div class="search-wrap">
            <el-radio-group v-model="selectValue" class="search-title">
              <el-radio-button label="environmentVariable">环境变量</el-radio-button>
              <el-radio-button label="function">表达式函数</el-radio-button>
            </el-radio-group>
            <el-input
              v-model="state.elementMetadata"
              :placeholder="selectValue === 'environmentVariable' ? '输入变量名' : '输入函数名'"
              :prefix-icon="Search"
              @input="getElementMetadata"
            ></el-input>

            <ul v-loading="searchLoading" class="search-result">
              <li class="title">{{ selectValue === "environmentVariable" ? "环境变量名" : "函数名" }}</li>
              <li class="title">{{ selectValue === "environmentVariable" ? "环境变量代码" : "函数代码" }}</li>
              <div class="result-wrap">
                <li
                  v-for="item in state.elementMetadataList"
                  :key="item.wsid"
                  class="result"
                  @click="selectExpression(item)"
                >
                  <template v-if="selectValue === 'environmentVariable'">
                    <span>{{ item.value }}</span>
                    <span>{{ item.key }}</span>
                  </template>
                  <template v-else>
                    <el-popover
                      width="auto"
                      placement="right-start"
                      trigger="hover"
                      :popper-style="{ background: 'rgba(0, 0, 0, 0.5)' }"
                    >
                      <div class="tips-content annotated" v-html="item?.annotated"></div>
                      <template #reference>
                        <div class="fun-result-wrap">
                          <span>{{ item.value }}</span>
                          <span>{{ item.key }}</span>
                        </div>
                      </template>
                    </el-popover>
                  </template>
                </li>
              </div>
            </ul>
          </div>
        </div>
      </div>
    </DialogContainer>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, watch } from "vue"
import { java } from "@codemirror/lang-java"
import { Codemirror } from "vue-codemirror"
import { Search, QuestionFilled } from "@element-plus/icons-vue"
import {
  CommonSelectFormItem,
  CommonInputFormItem,
  SearchContainer,
  AddButton,
  TableButton,
  CommonTable,
  DialogContainer,
  PageContainer
} from "@/base-components"
import {
  getQualityControlRuleList,
  getElementMetadataList,
  addQualityControlRule,
  editQualityControlRule,
  modifyRuleStatus,
  checkQualityControlExpression,
  getStandardsFieldFuzzyApi,
  getFuzzyQcFunction
} from "../interface"
import {
  ruleConfigColumns,
  checkTypeOptions,
  qcPointOptions,
  getQcPointDesc,
  typeOptions,
  ruleConfigFormRules,
  myTheme
} from "./config"
import type { FormInstance } from "element-plus"
import { useCommonOptions, useTableSearch } from "@/hooks"
import { queryScoreStandard, getCatalogStandardList } from "@/interfaces"
import { SystemPrompt, Message, toastError } from "@/utils"

const { getOptionLabel: getCheckTypeDesc } = useCommonOptions({ optionsDataSource: checkTypeOptions })

const selectValue = ref("environmentVariable")

const initRuleExpression = `//请输入规则表达式\n\n\n\n\n\n\n\n\n\n`

/* ======================== 搜索相关数据及方法 ======================== */

const formData = reactive({
  checkType: "",
  name: "",
  qcPoint: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(formData)

/* ======================== 表格相关方法 ======================== */

const ruleConfigTableRef = ref<InstanceType<typeof CommonTable>>()

function dataCallback(data: Array<Record<string, any>>) {
  return data.map(item => ({
    ...item,
    checkTypeEnum: getCheckTypeDesc(item.checkType),
    qcPointEnum: item.qcPoint.map(item => getQcPointDesc(item)).join(","),
    statusText: item.status === "ENABLE" ? "已启用" : "已禁用"
  }))
}

// 切换启用&禁用状态
const modifyStatus = row => {
  const nextStatus = row.status === "ENABLE" ? "DISABLE" : "ENABLE"
  SystemPrompt(`是否确定${row.status === "ENABLE" ? "禁用" : "启用"}规则${row.name} `).then(() => {
    modifyDataSourcesInfo(row.wsid, nextStatus)
  })
}

// 删除
function handleDelete(row) {
  SystemPrompt(`您确定要删除规则${row.name} ?`).then(() => modifyDataSourcesInfo(row.wsid, "DEL"))
}

const modifyDataSourcesInfo = (wsid: string, nextStatus: "ENABLE" | "DISABLE" | "DEL") => {
  modifyRuleStatus({ wsid: wsid, status: nextStatus })
    .then(() => {
      Message.success(nextStatus === "DEL" ? "删除数据成功" : "修改状态成功")
      ruleConfigTableRef.value?.refreshTableData()
    })
    .catch(err => toastError(err))
}

/* ======================== 弹窗相关数据及方法 ======================== */

const ruleFormRef = ref<FormInstance>()

const state = reactive({
  dialogVisible: false,
  associateScoreStandard: false,
  treeList: [],
  elementMetadata: "",
  elementMetadataList: [] as Array<any>,
  updateRuleType: "",
  scoreList: [] as Array<any>
})

const validationInput = ref("") // 规则表达式校验值输入框
const validationMessage = ref("") // 规则表达式校验错误信息

const ruleData = reactive({
  name: "", //规则代码
  type: "", //质控类型
  qcPoint: "", // 质控点
  ruleExpression: "", //规则表达式
  promptContent: "", // 提示内容
  consult: "", // 参考
  checkType: "", // 检查类型
  controlLevel: "", //控制级别
  qcScoreStandardWsid: "", // 关联的评分配置项wsid
  score: "", //规则扣分
  wsid: "", // 质控规则wsid
  qcTarget: "" //界面定位元素
})

const searchLoading = ref(false)

// 输入变量名查询
const getElementMetadata = (searchText = "") => {
  if (searchLoading.value) return
  searchLoading.value = true
  console.log(`output->selectValue`, selectValue.value)
  console.log(`output->ruleData`, ruleData)
  const handle = selectValue.value === "environmentVariable" ? getStandardsFieldFuzzyApi : getFuzzyQcFunction
  const params =
    selectValue.value === "environmentVariable"
      ? {
          item: searchText,
          typeName: ruleData.type === "OUT_PATIENT_FIRST_HOME_PAGE" ? "OUTPATIENT_SERVICE" : "HOME_PAGE"
        }
      : { name: searchText }

  handle(params)
    .then(res => {
      searchLoading.value = false
      if (selectValue.value === "environmentVariable") {
        state.elementMetadataList = res.data.data.map(item => {
          return {
            key: item?.key,
            value: item?.value
          }
        })
      } else {
        state.elementMetadataList = res.data.data
          .filter(item => item.status === "ENABLE")
          .map(item => {
            return {
              key: item?.code,
              value: item?.funName,
              params: item?.params,
              annotated: item?.annotated
            }
          })
      }
    })
    .catch(err => {
      searchLoading.value = false
      toastError(err)
    })
}

watch(
  () => ruleData.type,
  val => {
    if (val) {
      getElementMetadata()
    }
  },
  {
    immediate: true,
    deep: true
  }
)

watch(
  () => selectValue.value,
  () => {
    state.elementMetadata = ""
    getElementMetadata()
  }
)

// 选择变量名生成规则表达式
const selectExpression = data => {
  state.elementMetadata = data.value
  if (ruleData.ruleExpression === initRuleExpression) {
    ruleData.ruleExpression = ""
  }
  if (selectValue.value === "environmentVariable") {
    ruleData.ruleExpression = ruleData.ruleExpression + ` ${data.key}`
  } else {
    if (data.params) {
      ruleData.ruleExpression =
        ruleData.ruleExpression +
        `${data.key}(${data.params.map(item => (item.defaultValue ? item.defaultValue : item.paramDesc)).join(",")})`
    } else {
      ruleData.ruleExpression = ruleData.ruleExpression + `${data.key}()`
    }
  }
}

// 校验表达式规则
async function handleExpressionCheck() {
  try {
    const result = await checkQualityControlExpression({
      condition: ruleData.ruleExpression,
      data: validationInput.value
    })
    if (result) {
      validationMessage.value = ""
      Message.success("校验成功")
    } else if (result === false) {
      Message.error("校验失败")
      validationMessage.value = "校验失败"
    }
  } catch (err: any) {
    toastError(err, "校验失败")
  }
}

// 新增或编辑规则
const updateRule = (type: string, data: any) => {
  validationInput.value = ""
  validationMessage.value = ""
  state.dialogVisible = true
  state.updateRuleType = type
  Object.keys(ruleData).forEach(key => {
    // ruleData[key] = data ? data[key] || ruleData[key] : ""
    ruleData[key] = type === "add" ? "" : data[key] ?? ""
  })
  if (type === "add") {
    ruleData.type = "FIRST_HOME_PAGE"
    ruleData.ruleExpression = initRuleExpression
  }
  getQcTargetOptions(ruleData.type)
  // 上一次选中的评分项已被禁用，则清空该字段
  if (state.scoreList?.every(item => item.wsid !== ruleData.qcScoreStandardWsid)) {
    ruleData.qcScoreStandardWsid = ""
  }

  // 有已选评分项，则默认选中“是”
  state.associateScoreStandard = data?.qcScoreStandardWsid ? true : false
}

// 保存数据
function handleConfirm() {
  ruleFormRef.value?.validate(valid => {
    if (!valid) return
    ruleData.qcScoreStandardWsid = state.associateScoreStandard ? ruleData.qcScoreStandardWsid : ""
    // 若质控类型为HQMS上报，则评分项字段必须传空
    if (ruleData.qcPoint === "HQMS_REPORT") ruleData.qcScoreStandardWsid = ""
    const handler = state.updateRuleType === "add" ? addQualityControlRule(ruleData) : editQualityControlRule(ruleData)
    handler
      .then(() => {
        Message.success(state.updateRuleType === "add" ? "添加规则成功" : "编辑规则成功")
        state.dialogVisible = false
        ruleConfigTableRef.value?.refreshTableData()
      })
      .catch(err => toastError(err))
  })
}

/* ======================== 树形结构相关数据及方法 ======================== */

// 获取评分项
const flatData = data => {
  let result = data
  data.forEach(item => {
    if (item.children.length) {
      result = result.concat(item.children)
      item.children.forEach(child => {
        if (child.children.length) {
          result = result.concat(child.children)
        }
      })
    }
  })
  result = result.filter(item => item.type === "SCORE_ITEM")
  return result
}

// 初始化树形数据
onMounted(() => {
  queryScoreStandard({ documentType: "FIRST_HOME_PAGE", status: "ENABLE" }).then(resData => {
    state.treeList = resData
    state.scoreList = flatData(resData)
  })
})

const qcTargetOptions = ref<Array<Record<string, any>>>([])

const formatTreeInfo = (treeInfo: Array<Record<string, any>>) => {
  treeInfo.forEach(item => {
    item.label = item?.fileName ?? ""
    item.value = item?.code ?? ""
    item.disabled = item.catalogType === "CATALOG"
    if (item.children) return formatTreeInfo(item.children)
  })
}

const getQcTargetOptions = async val => {
  qcTargetOptions.value = (
    await getCatalogStandardList({
      catalogType: "FIELD",
      catalogFileType: "SYSTEM",
      // filters: `scenario_type=${val}`
      filters: `scenario_type=${val === "" ? "" : val === "FIRST_HOME_PAGE" ? "HOME_PAGE" : "OUTPATIENT_SERVICE"}`
    })
  )?.data?.data
  formatTreeInfo(qcTargetOptions.value)
  console.log(`output->qcTargetOptions.value`, qcTargetOptions.value)
}

// 代码块
const extensions = [java(), myTheme]
</script>

<style lang="less" scoped>
.form-container {
  display: flex;

  :deep(.el-form) {
    width: 70%;
    margin-right: 15px;
  }

  .search-container {
    width: 30%;
    padding-bottom: 18px;

    .search-wrap {
      height: 100%;
      // border: 1px solid #ebedf0;
    }

    :deep(.el-input__wrapper) {
      border-radius: 0;
    }

    .search-type {
      display: flex;
    }

    .search-result {
      display: flex;
      flex-wrap: wrap;
      border: 1px solid #ebedf0;
    }
    .title {
      width: 50%;
      text-align: center;
      height: 30px;
      line-height: 30px;
      background: #ebedf0;
    }

    .result-wrap {
      // height: 515px;
      height: 600px;
      overflow-y: auto;
      width: 100%;
    }

    .result {
      width: 100%;
      display: flex;
      line-height: 30px;
      height: 30px;
      cursor: pointer;
      &:hover {
        background: #ebedf0;
      }

      span {
        width: 50%;
        text-align: center;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        height: 100%;
      }
    }
  }
}

.tips-content {
  color: #fff;
}

.search-title {
  width: 100%;

  :deep(.el-radio-button) {
    width: 50%;
  }

  :deep(.el-radio-button__inner) {
    width: 100%;
  }
}

.annotated {
  white-space: pre-wrap;
}

.fun-result-wrap {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:deep(.cm-editor) {
  border: 1px solid #dcdfe6;
}

:deep(.cm-focused) {
  outline: none !important;
}

:deep(.cm-lineNumbers) {
  border-right: 1px solid #dcdfe6;
}

.code-container-dialog {
  :deep(.el-dialog__body) {
    max-height: 80vh;
    overflow-y: auto;
  }
}
</style>
