import axios from "@/interfaces/axios-instance"

/**
 * @method GET
 * @desc   查询待归档数据列表
 */
export function getWaitingArchiveListApi(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/document/bag/archive/archive-wait`,
    params
  })
}

interface IArchiveBagParam {
  wsid: string
}
/**
 * @method POST
 * @desc   归档指定病案
 */
export function archiveBag(obj: IArchiveBagParam) {
  const { wsid } = obj
  return axios({
    method: "post",
    url: `/api/document/bag/archive/${wsid}`,
    data: {}
  })
}

/**
 * @method GET
 * @desc   查询已归档数据列表
 */
export function getArchivedList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/document/bag/archive/already`,
    params
  })
}

// 获取已归档所有版本
export const getArchiveVersionsApi = (inpNo: string) => {
  return axios({
    method: "get",
    url: `/api/document/bags/${inpNo}/supplement-archive/version`
  })
}

// 获取补充归档申请的分类列表
export const getSupplementArchiveDataApi = (inpNo: string) => {
  return axios({
    method: "GET",
    url: `/api/document/bags/${inpNo}/supplement-archive/classes`
  })
}

// 补充归档申请
export const addSupplementArchiveApi = params => {
  const { file, registerNumber, fileName, inpNo } = params
  const formData = new FormData()
  file.forEach(item => {
    formData.append("file", item?.raw)
  })

  return axios({
    method: "post",
    url: `/api/document/bags/${inpNo}/supplement-archive`,
    data: formData,
    params: {
      registerNumber: registerNumber,
      fileName: fileName
    },
    headers: {
      "Content-Type": "multipart/form-data",
      "Access-Control-Allow-Headers": "*"
    }
  })
}

// 归档通过
export const archivePassApi = (wsid: string) => {
  return axios({
    method: "post",
    url: `/api/document/bag/archive/${wsid}/audit/pass`
  })
}

interface IArchiveRefuseData {
  wsid: string
  reason?: string
}

// 归档拒绝
export const archiveRefuseApi = (data: IArchiveRefuseData) => {
  return axios({
    method: "post",
    url: `/api/document/bag/archive/${data.wsid}/audit/refuse?reason=${data.reason}`
  })
}
