<template>
  <el-button
    style="margin-left: 16px"
    :icon="h('i', { class: 'ri-upload-2-line' })"
    :type="props.type"
    @click="$emit('click')"
  >
    <slot></slot>
  </el-button>
</template>

<script setup lang="ts">
import { h } from "vue"
import { ButtonType } from "element-plus"

const props = withDefaults(defineProps<{ type?: ButtonType }>(), {
  type: "default"
})

defineEmits(["click"])
</script>
