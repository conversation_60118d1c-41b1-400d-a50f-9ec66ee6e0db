import axios from "@/interfaces/axios-instance"

/**
 * @method POST
 * @desc   新增首页标准字段
 */
export function addHomeMetaField(homeMetaData) {
  return axios({
    method: "post",
    url: "/api/catalog/standard",
    data: {
      type: "BASE_INFO",
      ...homeMetaData
    }
  })
}

/**
 * @method PUT
 * @desc   编辑首页标准字段
 */
export function editHomeMetaField(homeMetaData) {
  return axios({
    method: "put",
    url: `/api/catalog/standard`,
    data: {
      type: "BASE_INFO",
      ...homeMetaData
    }
  })
}

/**
 * @method DELETE
 * @desc   删除指定的首页标准字段数据
 */
export function deleteHomeMetaField(id: string) {
  return axios({
    method: "delete",
    url: `/api/catalog/standard/${id}`
  })
}

/**
 * @method PUT
 * @desc   批量设置首页标准字段的编目状态(启用/禁用)
 */
type TypeBatchSetStandard = {
  collectFieldName?: string
  catalogStatus?: string
}
export function batchSetStandardStatus(data: Array<TypeBatchSetStandard>) {
  return axios({
    method: "put",
    url: "/api/catalog/standard/batch",
    data
  })
}

/**
 * @method GET
 * @desc   下载导入首页标准字段范围模板
 */
export function downloadMetaCodeTemplateApi() {
  return axios({
    method: "get",
    url: "/api/catalog/standard/options/template",
    responseType: "arraybuffer"
  })
}

/**
 * @method POST
 * @desc   导入首页标准字段范围
 */
export function metaCodeImportApi({ file }: { file: File }) {
  const formData = new FormData()
  formData.append("file", file)
  return axios({
    method: "post",
    url: "/api/catalog/standard/options/import",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  })
}

/**
 * @method POST
 * @desc   导出首页标准字段范围数据
 */
export function metaCodeExportApi() {
  return axios({
    method: "post",
    url: "/api/catalog/standard/options/export",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    responseType: "arraybuffer"
  })
}

/* ======================== 医保编目元数据 ======================== */

/**
 * @method GET
 * @desc   模糊查询首页标准字段
 */
export function fuzzyQueryStandardsFieldApi(params) {
  return axios({
    method: "get",
    url: `/api/catalog/standard/fuzzy-query`,
    params: params
  }).then(res => res.data?.data ?? [])
}

interface IStandardsFieldData extends IPaginationRequestParams {
  scenarioType: string
}

/**
 * @method GET
 * @desc   标准字段-分页查询
 */
export function getStandardsFieldApi(params) {
  const { offset, limit, filters, queryParams } = params
  console.log(`output->queryParams`, queryParams)
  // debugger
  return axios({
    method: "get",
    url: `/api/catalog/scenario/standards/${queryParams.scenarioType}`,
    params: {
      offset: offset,
      limit: limit,
      filters: filters,
      ...queryParams
    }
  })
}

/**
 * @method POST
 * @desc   标准字段-新增
 */
export function addStandardsFieldApi(data: Record<string, any>) {
  const { scenarioType, bodyData } = data
  return axios({
    method: "post",
    url: `/api/catalog/scenario/standards/${scenarioType}`,
    data: bodyData
  })
}

/**
 * @method PUT
 * @desc   标准字段-编辑
 */
export function editStandardsFieldApi(data: Record<string, any>) {
  const { scenarioType, bodyData } = data
  return axios({
    method: "put",
    url: `/api/catalog/scenario/standards/${scenarioType}/${bodyData.name}`,
    data: bodyData
  })
}

/**
 * @method DELETE
 * @desc   标准字段-删除
 */
export function deleteStandardsFieldApi(data: Record<string, any>) {
  const { scenarioType, name } = data
  console.log(`output->data`, data)
  return axios({
    method: "delete",
    url: `/api/catalog/scenario/standards/${scenarioType}/${name}`
  })
}

/**
 * @method PUT
 * @desc   标准字段-改变状态
 */
export function changeStandardsFieldStatusApi(data: Record<string, any>) {
  const { scenarioType, names, status } = data
  return axios({
    method: "put",
    url: `/api/catalog/scenario/standards/${scenarioType}/change-status`,
    data: {
      params: names
    },
    params: {
      status: status
    }
  })
}

interface IBatchModifyStandardData {
  ids?: Array<number>
  widgetType?: string
  parentId: number | null
  fileType: "SYSTEM" | "REPORT"
}

/**
 * @method PUT
 * @desc   批量修改标准字段
 */
export function batchModifyStandardApi(data: IBatchModifyStandardData) {
  return axios({
    method: "put",
    url: `/api/catalog/standard_batch`,
    data: data
  })
}
