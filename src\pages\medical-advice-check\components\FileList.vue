<template>
  <div class="container">
    <div class="aside-container" :style="{ width: isCollapse ? '0px' : '214px' }">
      <!-- 病案文件列表 -->
      <div class="record-tree">
        <CommonTree
          ref="submissionTreeRef"
          node-key="wsid"
          :data="props.documentTree"
          show-delete
          @click-node="setPdfSrc"
        >
          <template #title="{ node, data }">
            <!-- 文件名 -->

            <span v-if="data.type === 'FILE'" style="margin-right: 4px">
              <i v-if="!data.isSupplement" class="ri-file-text-line electric-icon"></i>
              <i v-else class="ri-camera-line paper-icon"></i>
            </span>
            <!-- 文本 -->
            <OverflowTooltip :content="getNodeTooltipContent(node, data, false)" max-width="80%"></OverflowTooltip>
          </template>
        </CommonTree>
      </div>
    </div>
    <!-- 展开/收缩 -->
    <div
      class="collapse-icon"
      :style="{
        boxShadow: isCollapse ? '0px 0px 3px 1px rgba(0, 35, 114, 0.1)' : '3px 0px 3px 1px rgba(0, 35, 114, 0.1)',
        borderRadius: isCollapse ? '5px' : '0px 5px 5px 0px'
      }"
      @click="() => (isCollapse = !isCollapse)"
    >
      <el-icon v-if="!isCollapse"><ArrowLeft /></el-icon>
      <el-icon v-if="isCollapse"><ArrowRight /></el-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, watch, nextTick, onMounted } from "vue"
import { ArrowLeft, ArrowRight } from "@element-plus/icons-vue"
import { CommonTree, OverflowTooltip } from "@/base-components"
import { getNodeTooltipContent } from "@/utils"

const isCollapse = ref(false)
const emits = defineEmits(["setPdfSrc"])

interface FileListProps {
  documentTree: Array<Record<string, any>>
}

const props = withDefaults(defineProps<FileListProps>(), {})

const setPdfSrc = node => {
  if (node.type === "FILE") emits("setPdfSrc", node)
}
</script>

<style lang="less" scoped>
.container {
  height: 100%;
  position: relative;
}

.aside-container {
  // min-width: 300px;
  // width: 350px;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  flex-direction: column;
  height: 100%;
  position: relative;
  transition: width 0.5s;
}
.collapse-icon {
  width: 24px;
  height: 48px;
  background: #fff;
  right: -24px;
  padding: 16px 4px;
  box-sizing: border-box;
  position: absolute;
  top: 0px;
  bottom: 0px;
  margin: auto;
  z-index: 999;
  cursor: pointer;
  &:hover {
    color: #999;
  }
}
</style>
