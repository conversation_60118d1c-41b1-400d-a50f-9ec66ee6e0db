<template>
  <div class="record-compare-container">
    <el-container>
      <!-- 头部操作 -->
      <el-header>
        <div class="back-action">医嘱核对</div>
        <!-- <el-checkbox v-model="moveTogether">同步滚动</el-checkbox> -->
        <el-button type="primary" @click="close">关闭</el-button>
      </el-header>
      <div class="patient-info">
        <span class="patient-name">{{ route.query.patientName }}</span>
        <span>住院号：{{ route.query.inpNo }}</span>
        <span>
          住院时间： {{ formatDatetime(Number(route.query.inHospitalDatetime), "YYYY/MM/DD") }} -{{
            formatDatetime(Number(route.query.outHospitalDatetime), "YYYY/MM/DD")
          }}
        </span>
      </div>
      <el-main class="compare-container">
        <!-- 医嘱单 -->
        <div class="version-root">
          <!-- <div class="version-choose">
            召回版本：
            <el-select v-model="recallState.activeRecallVersion" @change="getRecallDetail">
              <el-option
                v-for="option in recallState.data"
                :key="option.versionName"
                :label="option.versionName"
                :value="option.wsid"
              />
            </el-select>
          </div> -->
          <div class="version-container">
            <div v-loading="loading" class="view-left">
              <FileList :document-tree="doctorAdviceDoc" @set-pdf-src="node => handlePdfClick(node, 'recall')" />
            </div>
            <div class="view-middle common-box-shadow">
              <PdfPreviewComponent ref="recallPdfRef" :src="doctorAdviceDocSrc" />
            </div>
          </div>
        </div>
        <!-- 检查、检验报告 -->
        <div class="version-root">
          <!-- <div class="version-choose">
            归档版本：
            <el-select v-model="archiveState.activeRecallVersion" @change="getArchiveDetail">
              <el-option
                v-for="option in archiveState.data"
                :key="option.versionName"
                :label="option.versionName"
                :value="option.code"
              />
            </el-select>
          </div> -->
          <div class="version-container">
            <div v-loading="loading" class="view-left">
              <FileList :document-tree="examDoc" @set-pdf-src="node => handlePdfClick(node, 'archive')" />
            </div>
            <div class="view-middle common-box-shadow">
              <PdfPreviewComponent ref="archivistPdfRef" :src="examDocSrc" />
            </div>
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, ref, watch } from "vue"
import { useRoute } from "vue-router"
import { ArrowLeft } from "@element-plus/icons-vue"
import { PdfPreviewComponent } from "@/base-components"
import { MedicalRecordTree } from "@/page-components"
// import { getRecallApprovedDetailApi, getRecallVersionApi, getArchiveVersionApi } from "../../interface"
import FileList from "./components/FileList.vue"
import { getArchiveVersionDetailApi, getDoctorAdviceCheckApi } from "@/interfaces"
import { decryptStr, toastError, formatDatetime } from "@/utils"

const route = useRoute()

const archivistPdfRef = ref()
const recallPdfRef = ref()

const moveTogether = ref(false)

// 同步滚动开启/关闭
watch(
  () => moveTogether.value,
  val => {
    const archivistContent = archivistPdfRef.value.pdfAreaRef
    const recallContent = recallPdfRef.value.pdfAreaRef
    archivistContent.onscroll = val ? e => scrollTogether(archivistContent, recallContent) : () => {}
    recallContent.onscroll = val ? e => scrollTogether(recallContent, archivistContent) : () => {}
  }
)

// 同步滚动
function scrollTogether(source, target) {
  const sourceScrollWidth = source.scrollWidth
  const targetScrollWidth = target.scrollWidth
  //   横向滚动：可能有一方的目录收缩隐藏了，因此需要乘上比例
  const scrollScale = targetScrollWidth / sourceScrollWidth
  target.scrollLeft = source.scrollLeft * scrollScale
  target.scrollTop = source.scrollTop
}

// 点击目录
const handlePdfClick = (node, type) => {
  if (type === "recall") {
    doctorAdviceDocSrc.value = "/api/files/" + node?.wsid
  } else if (type === "archive") {
    examDocSrc.value = "/api/files/" + node?.wsid
  }
}

const formatTree = (data: Record<string, any>) => {
  return [
    {
      ...data,
      name: data?.mrClassName,
      type: "CATALOGUE",
      wsid: data?.mrClassCode,
      children: data?.docs?.map(item => ({
        ...item,
        title: item?.fileName,
        type: "FILE",
        wsid: item?.fileWsid
      }))
    }
  ]
}

const doctorAdviceDoc = ref<Array<Record<string, any>>>([])
const doctorAdviceDocSrc = ref("")

const examDoc = ref<Array<Record<string, any>>>([])
const examDocSrc = ref("")
const loading = ref(false)

const getData = async () => {
  try {
    loading.value = true
    const res = (await getDoctorAdviceCheckApi(route.query.inpNo as string))?.data
    doctorAdviceDoc.value = formatTree(res.data?.doctorAdviceDoc)
    examDoc.value = formatTree(res.data?.examDoc).concat(formatTree(res.data?.checkDoc))
    loading.value = false
  } catch (error: any) {
    loading.value = false
    console.log(`output->error`, error)
    toastError(error, "获取医嘱数据失败")
  }
}

onMounted(async () => {
  await getData()
  if (examDoc.value[0]?.children?.length) handlePdfClick({ wsid: examDoc.value[0]?.children[0]?.wsid }, "archive")

  if (doctorAdviceDoc.value[0]?.children?.length)
    handlePdfClick({ wsid: doctorAdviceDoc.value[0]?.children[0]?.wsid }, "recall")
})

/* ========================= 召回版本 ========================= */
const recallState = reactive({
  // 召回版本
  versionLoading: false,
  data: [] as Array<Record<string, any>>,
  activeRecallVersion: "",

  // 召回详情
  detailLoading: false,
  baseInfo: {} as any, // 患者基础信息
  pdfSrc: "",
  targetNode: {} as any, // 右侧元数据需要的信息
  firstPageFields: [] as any[], // 首页字段
  treeInfo: {} as any // 左侧tree需要的原始数据
})

const close = () => {
  window.close()
}
</script>

<style lang="less" scoped>
.record-compare-container {
  width: 100vw;
  height: 100vh;
  .el-container {
    height: 100%;
  }
  .el-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
    background-color: white;
    border-bottom: solid 1px rgb(225 226 230);
    padding-left: 0px;
    .back-action {
      display: flex;
      align-items: center;
      height: 100%;
      cursor: pointer;
      padding: 0px 20px;
      font-weight: bold;
      font-size: 16px;
      color: #000000;
      .el-icon {
        margin-right: 8px;
      }
      &:hover {
        color: #73767a;
      }
    }
  }
  .compare-container {
    display: flex;
    height: calc(100% - 101px);
    column-gap: 20px;
    .version-root {
      flex: 1;
      min-width: 0px;
      display: flex;
      flex-direction: column;
      .version-choose {
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        .el-select {
          width: 183px;
        }
      }
    }
    .version-container {
      display: flex;
      height: calc(100% - 48px);
      .view-middle {
        flex: 1;
        min-width: 0px;
      }
    }
  }
}

.patient-info {
  padding: 10px 20px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e1e2e6;

  span {
    font-weight: 500;
    font-size: 12px;
    color: rgba(10, 22, 51, 0.6);
    margin-right: 12px;
  }

  .patient-name {
    font-weight: bold;
    font-size: 14px;
    color: #0a1633;
  }
}
</style>
