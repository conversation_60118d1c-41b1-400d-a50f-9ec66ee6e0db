<template>
  <PageContainer separate>
    <template #search>
      <!-- <TabsRouter :tabs-config="tabsConfig" /> -->
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
        />

        <DepartmentFormItem v-model="searchFormState.outHospitalDeptWsid" label="出院科室" />

        <DaterangeFormItem v-model="searchFormState.outHospitalDatetime" label="出院时间" />

        <CommonSelectFormItem
          v-model:model-value="searchFormState.returnStatus"
          label="退回状态"
          :options="returnedStatusOptions"
          filterable
        />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="tableRef"
        table-id="waitingCatalogTableIdent"
        :table-columns="returnedColumns"
        :request-api="getReturnedListApi"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <!-- 患者姓名 -->
        <template #patientName="{ row }">
          <div style="display: flex; align-items: center; flex-wrap: wrap">
            <el-tooltip effect="dark" content="业务详情" placement="top">
              <div class="business-tip" @click.stop="handleMedical(row)">
                <div class="business-tip-placeholder"></div>
                <img
                  class="business-tip-icon"
                  :style="{ filter: `drop-shadow(500px 0 ${bulbColor(row)})` }"
                  src="@/assets/home/<USER>"
                />
              </div>
            </el-tooltip>

            <span style="margin: 0 16px">{{ row.patientName }}</span>
            <PatientLabelTag :row="row" />
          </div>
        </template>

        <!-- 病案状态 -->
        <template #statusEnumName="{ row }">
          <div @click.stop="handleMedical(row)">
            <MedicalRecordStatusTag
              style="cursor: pointer; border: none"
              :status="row.statusEnumName"
              :submit-status="row.submitStatusEnum"
            />
          </div>
        </template>

        <!-- 退回状态 -->
        <template #returnStatus="{ row }">
          <div style="cursor: pointer" @click.stop="handleMedical(row)">
            {{ row.returnStatus }}
          </div>
        </template>

        <!-- 特殊病案 -->
        <template #isSpecial="{ row }">
          <el-button
            v-if="row.isSpecialEnum === 'YES'"
            type="primary"
            link
            @click.stop="showSpecialDialog(row, 'check')"
          >
            是
          </el-button>
          <el-button v-else link @click.stop>否</el-button>
        </template>

        <template #operation="{ row }">
          <TableButton @click="handleSubmit(row)">提交</TableButton>
        </template>
      </CommonTable>
    </template>

    <!-- 提交进度 -->
    <SubmitProgressDialog ref="progressDialogRef" :inp-no="operationState.selectedRow?.inpNo" />

    <!-- 业务详情的抽屉 -->
    <BusinessDetailDrawer ref="businessDetailDrawerRef" />
    <!-- 特殊病案弹窗 -->
    <SpecialDialog
      ref="specialDialogRef"
      :selected-row="operationState.selectedRow"
      @success="tableRef?.refreshTableData"
    />
  </PageContainer>
</template>

<script setup lang="ts">
import { ref, nextTick, reactive } from "vue"
import { useRouter } from "vue-router"
import {
  SearchContainer,
  PatientFilterFormItem,
  CommonTable,
  DaterangeFormItem,
  PageContainer,
  PatientLabelTag,
  DepartmentFormItem,
  MedicalRecordStatusTag,
  TableButton,
  CommonSelectFormItem
} from "@/base-components"
import { TabsRouter } from "@/page-components"
import SubmitProgressDialog from "../components/SubmitProgressDialog.vue"
import { getReturnedListApi, getSubmitProgressApi } from "../interface"
import { BusinessDetailDrawer } from "../medical-record-home/components"
import { getSubmitConfigApi } from "../medical-record-home/interface"
import { returnedColumns, returnedStatusOptions } from "./config"
import useTableSearch from "@/hooks/useTableSearch_v2"
import { useGlobalOptionsStore, useSystemStore } from "@/stores"
import { formatDatetime, toastError, SystemAlert } from "@/utils"

const globalOptionsStore = useGlobalOptionsStore()
const systemStore = useSystemStore()
const router = useRouter()

/* ======================== 搜索相关数据及方法 ======================== */

const initialSearchData = {
  patientFilterValue: "",
  patientFilterProp: "",
  outHospitalDeptWsid: "",
  outHospitalDatetime: "",
  returnStatus: ""
}

const { searchFormState, searchParams, handleQuery, handleReset } = useTableSearch({
  filtersInitialData: initialSearchData
})

/* ======================== 表格相关方法 ======================== */

const tableRef = ref<InstanceType<typeof CommonTable>>()

const dataCallback = data => {
  data.forEach(row => {
    row.inHospitalDatetime = formatDatetime(row.inHospitalDatetime)
    row.outHospitalDatetime = formatDatetime(row.outHospitalDatetime)
    row.receiptDatetime = formatDatetime(row.receiptDatetime)
    row.returnStatus = returnedStatusOptions.find(item => item.value === row.returnStatus)?.label
  })
  console.log(`output->data`, data)
  return data
}

// 灯泡颜色
const bulbColor = row => {
  return globalOptionsStore.medicalRecordStatusOptions.find(item => item.statusEnumName === row.statusEnumName)?.color
}

/*============================ 业务详情 ============================*/

const businessDetailDrawerRef = ref<InstanceType<typeof BusinessDetailDrawer>>()

const handleMedical = row => {
  businessDetailDrawerRef.value?.show(row)
}

const specialDialogRef = ref()

const operationState = reactive({
  selectedRow: {} as Record<string, any>,
  routerPath: "",
  key: ""
})
const showSpecialDialog = (row, type) => {
  operationState.selectedRow = row
  nextTick(() => specialDialogRef.value?.show(type))
}

let query = "" // 跳转到提交详情的参数

// 生成跳转参数
const generateQuery = (deletionSubmit: "YES" | "NO") => {
  const _query = `inpNo=${operationState.selectedRow?.inpNo}&patientName=${operationState.selectedRow?.patientName}&lastSyncTime=${operationState.selectedRow?.syncDatetime}&allowMissing=${deletionSubmit}&mrNo=${operationState.selectedRow?.mrNo}&outHospitalDeptName=${operationState.selectedRow?.outHospitalDeptName}&inHospitalDatetime=${operationState.selectedRow?.inHospitalDatetime}&outHospitalDatetime=${operationState.selectedRow?.outHospitalDatetime}`
  // if (sealDialogRef.value?.secretKey) {
  //   query + `&secretKey=${encryptStr(sealDialogRef.value.secretKey)}`
  // }
  query = _query
}

const handleSubmit = async row => {
  try {
    operationState.selectedRow = row
    systemStore.showLoading()
    const { needSign, deletionSubmit } = (await getSubmitConfigApi(operationState.selectedRow?.wsid)).data.data
    generateQuery(deletionSubmit)
    systemStore.hideLoading()
    // 根据配置跳转不同的页面 - 是否需要签名
    if (needSign === "YES") {
      checkSubmissionProgress()
    } else {
      query += "&needSign=NO"
      router.push(`/medical-submit?${query}&returnUrl=/medical-record/returned`)
    }
  } catch (error: any) {
    toastError(error, "初始化配置失败")
    systemStore.hideLoading()
  }
}

const progressDialogRef = ref()
// 检查病案是否提交
const checkSubmissionProgress = () => {
  getSubmitProgressApi(operationState.selectedRow?.inpNo)
    .then(res => {
      // completeFlag 1未提交；2提交中；3提交完成
      const { completeFlag } = res.data.data
      // 提交成功
      if (completeFlag === 2) {
        SystemAlert("该病案已提交成功，无需再次提交")
      }
      // 已经提交但在提交中
      else if (completeFlag === 1) {
        progressDialogRef.value?.init(res.data.data.submitCount, res.data.data.total)
      }
      // 正常提交流程
      else {
        query += "&needSign=YES"
        router.push(`/medical-submit?${query}&returnUrl=/medical-record/returned`)
      }
    })
    .catch(error => {
      toastError(error)
    })
}
</script>

<style lang="less" scoped>
.business-tip {
  position: relative;

  .business-tip-placeholder {
    width: 20px;
    height: 20px;
    cursor: pointer;
  }

  .business-tip-icon {
    transform: translateX(-500px) translateY(-50%);
    position: absolute;
    left: 0;
    top: 50%;
  }
}
</style>
