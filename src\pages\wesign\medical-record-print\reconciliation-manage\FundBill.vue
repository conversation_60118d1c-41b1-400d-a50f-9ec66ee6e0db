<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <DaterangeFormItem v-model:model-value="searchFormState.billDate" label="账单日期" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="fundBillTableRef"
        :table-columns="fundBillColumns"
        :request-api="getFundBillList"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <template #header>
          <ComplexExportButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Export)"
            :table-info="fundBillTableRef?.tableState"
            :export-api="exportFundBillDetail"
            row-id-key="id"
            :search-filters="searchParams.filters"
            :sorts="'-billDate'"
          />
        </template>
      </CommonTable>
    </template>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import { SearchContainer, CommonTable, DaterangeFormItem, ComplexExportButton, PageContainer } from "@/base-components"
import { TabsRouter } from "@/page-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { useUserStore } from "@/stores"
import { formatDate } from "@/utils"
import { getFundBillList, exportFundBillDetail } from "../interface"
import { fundBillColumns, tabsRouterList, menuId } from "./config"

const { hasOperationPermission } = useUserStore()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  billDate: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/* ======================== 表格相关方法 ======================== */
const fundBillTableRef = ref()

const dataCallback = (data: Array<Record<string, any>>) => {
  return data.map(item => ({
    ...item,
    billDate: formatDate(item.billDate)
  }))
}
</script>
