import { EditorView } from "codemirror"
import type { TableColumnItem } from "@/types"
import type { FormRules } from "element-plus"

// 业务类型
export const sceneOptions = [
  { label: "住院", value: "IN_HOSPITAL" },
  { label: "门诊", value: "OUT_HOSPITAL" },
  // { label: "急诊", value: "URGENT_CARE" },
  { label: "体检", value: "PHYSICAL_EXAMINATION" },
  { label: "历史住院", value: "IN_HOSPITAL_HISTORY" } // TODO 历史住院枚举
]

export const dataTypeOptions = [
  { label: "科室", value: "DEPARTMENT" },
  { label: "医护", value: "DOCTOR_NURSE" },
  { label: "患者", value: "PATIENT" },
  { label: "首页", value: "HOME" },
  { label: "文书", value: "DOCUMENT" },
  { label: "医保对照", value: "MEDIEVALISTS" },
  { label: "质控结果", value: "QC_RESULT" },
  { label: "医保结算清单", value: "MEDICAL_SETTLEMENT" },
  { label: "医嘱", value: "DOCTOR_ADVICE" },
  { label: "医嘱项目", value: "DOCTOR_ADVICE_ITEM" }
]

export const tableColumns: TableColumnItem[] = [
  { prop: "systemName", label: "厂商系统", minWidth: 120 },
  { prop: "scene", label: "业务类型", minWidth: 110 },
  { prop: "remark", label: "配置描述", minWidth: 120 },
  { prop: "dataType", label: "数据接收类型", minWidth: 120 },
  { prop: "operation", label: "操作", minWidth: 120 }
]

export const formRules: FormRules = {
  scene: [{ required: true, message: "请选择业务类型", trigger: "blur" }],
  systemWsid: [{ required: true, message: "请选择厂商系统", trigger: "blur" }],
  dataType: [{ required: true, message: "请选择数据类型", trigger: "blur" }]
}

/* ================= 其他 ================ */

export const myTheme = EditorView.theme(
  {
    // 输入的字体颜色
    "&": {
      color: "#0052D9",
      backgroundColor: "#FFFFFF"
    },
    ".cm-content": {
      caretColor: "#0052D9"
    },
    // 激活背景色
    ".cm-activeLine": {
      backgroundColor: "#FFFFDD"
    },
    // 激活序列的背景色
    ".cm-activeLineGutter": {
      backgroundColor: "#FAFAFA"
    },
    //光标的颜色
    "&.cm-focused .cm-cursor": {
      borderLeftColor: "#0052D9"
    },
    // 选中的状态
    "&.cm-focused .cm-selectionBackground, ::selection": {
      backgroundColor: "#0052D9",
      color: "#dcdfe6"
    },
    // 左侧侧边栏的颜色
    ".cm-gutters": {
      backgroundColor: "#FFFFFF",
      color: "#ddd", //侧边栏文字颜色
      border: "none"
    }
  },
  { dark: true }
)
