<template>
  <div class="user-manage">
    <div class="user-manage-left">
      <div class="search">
        <el-input
          v-model="inputValue"
          placeholder="请输入部门名称"
          :prefix-icon="Search"
          clearable
          @clear="state.searchValue = ''"
          @keyup.enter="state.searchValue = inputValue"
        />
      </div>
      <div class="company-user">
        <div class="company-name">{{ state.systemName }}</div>
        <div class="company-user-list">
          <div
            v-for="item in userClassificationList"
            :key="item.name"
            class="company-user-item"
            :class="{ active: state.classificationConfig.value === item.type }"
            @click="changeUserClassification(item)"
          >
            <div style="display: flex; align-items: center">
              <el-icon style="margin-right: 6px; font-size: 16px"><span :class="item.class"></span></el-icon>
              <span>{{ item.name }}</span>
            </div>
            <div>{{ userStatistics[item.type] }}</div>
          </div>
        </div>
      </div>

      <div class="department-tree">
        <div
          v-for="dept in deptList"
          :key="dept.wsid"
          :class="{
            'department-item': true,
            active: dept.wsid === state.classificationConfig.value
          }"
          @click="changeDeptClassification(dept)"
        >
          {{ dept.deptName }}
        </div>
      </div>
    </div>
    <div class="user-manage-right">
      <UserTable
        ref="userTableRef"
        :dept-list="deptList"
        :click-dept="state.classificationConfig"
        :dept-name="state.classificationConfig.name"
        :get-user-statistics="getUserStatistics"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, watch } from "vue"
import { Search } from "@element-plus/icons-vue"
import { getHospitalDeptsApi } from "@/interfaces"
import { UserTable } from "./components"
import { userClassificationList } from "./config"
import { getUserStatisticsApi } from "./interface"
import type { UserClassificationType, UserClassificationItem, DeptOptionItem } from "./config"

// 子组件引用
const userTableRef = ref<InstanceType<typeof UserTable>>()

/* ======================== 首次加载获取表单选项 ======================== */

const deptList = ref<Array<DeptOptionItem>>([]) // 科室列表

/* ======================== 过滤筛选及统计 ======================== */

// 左侧用户数量统计数据
const userStatistics = reactive<{
  [key in UserClassificationType]: number
}>({
  allUser: 0,
  unassignedUser: 0,
  disableUser: 0
})

const inputValue = ref("")
const state = reactive({
  searchValue: "",
  systemName: "",
  classificationConfig: {
    type: "User",
    name: "全部用户",
    value: "allUser",
    filterValue: ""
  }
})

watch(
  () => state.searchValue,
  val => {
    getHospitalDeptsApi(val).then(res => {
      const { data = [] } = res.data
      deptList.value = data
    })
  },
  { immediate: true }
)

// 切换用户分组过滤
function changeUserClassification(item: UserClassificationItem) {
  const userFilterMap = new Map([
    ["allUser", ""],
    ["unassignedUser", "noDeptWsid=1"],
    ["disableUser", "sysUserStatus=DISABLE"]
  ])
  state.classificationConfig = {
    type: "User",
    name: item.name,
    value: item.type,
    filterValue: userFilterMap.get(item.type) || ""
  }
}

// 切换科室分组过滤
function changeDeptClassification(dept: DeptOptionItem) {
  state.classificationConfig = {
    type: "Dept",
    name: dept.deptName,
    value: dept.wsid,
    filterValue: `deptWsid=${dept.wsid}`
  }
}

onMounted(() => {
  getUserStatistics()
})
// 获取左侧统计数据
function getUserStatistics() {
  getUserStatisticsApi({ item: "" }).then(res => {
    const { totalCount = 0, disableCount = 0, noDeptCount = 0, systemName = "" } = res.data.data
    userStatistics.allUser = totalCount
    userStatistics.unassignedUser = noDeptCount
    userStatistics.disableUser = disableCount
    state.systemName = systemName
  })
}
</script>

<style lang="less" scoped>
.user-manage {
  display: flex;
  justify-content: space-between;
  min-width: 820px;
  height: calc(100%);
  font-size: 14px;
  color: rgb(90 93 101);
  .user-manage-left {
    display: flex;
    width: 265px;
    min-width: 265px;
    padding: 24px 16px;
    margin-right: 16px;
    background-color: white;
    box-shadow: 0 2px 8px rgb(0 35 114 / 10%);
    box-sizing: border-box;
    flex-direction: column;
    .search {
      width: 100%;
      margin-bottom: 24px;
    }
    .company-user {
      margin-bottom: 16px;
      .company-name {
        padding-bottom: 12px;
        font-size: 16px;
        border-bottom: 1px solid rgb(***********);
      }
      .company-user-list {
        padding: 16px 0;
        border-bottom: 1px solid rgb(***********);
        .company-user-item {
          display: flex;
          justify-content: space-between;
          height: 40px;
          padding: 0 12px;
          font-size: 14px;
          color: rgb(3 8 20 / 65%);
          line-height: 40px;
          cursor: pointer;
          box-sizing: border-box;
          &:hover {
            font-weight: 650;
            background-color: rgb(***********);
          }
          &.active {
            color: var(--el-color-primary);
            background-color: rgb(50 128 252 / 9.8%);
            font-weight: 650;
          }
        }
      }
    }

    .department-tree {
      flex: 1;
      overflow-y: auto;
      height: calc(100% - 36px);
      .department-item {
        height: 40px;
        padding-left: 16px;
        color: rgb(3 8 20 / 84.7%);
        border-radius: 4px;
        line-height: 40px;
        box-sizing: border-box;
        cursor: pointer;
        &:hover {
          font-weight: 600;
          background-color: rgb(***********);
        }
        &.active {
          font-weight: 600;
          color: var(--el-color-primary);
          background-color: rgb(44 104 255 / 9.8%);
        }
      }
    }
  }
  .user-manage-right {
    width: calc(100% - 16px - 265px);
    min-width: 790px;
    height: 100%;
    flex: 1;
    .user-manage-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .user-table-wrapper {
      display: flex;
      flex-direction: column;
      flex: 1;
      height: calc(100% - 40px);
    }
  }
}
</style>
