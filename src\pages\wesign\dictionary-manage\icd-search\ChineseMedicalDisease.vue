<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <IcdSearch ref="icdSearchRef" v-model:filters="requestParams.filters" :icd-type="ICDTypeEnum.ICD_ZY" />
    </template>

    <template #table>
      <CommonTable
        ref="commonTableRef"
        :table-columns="outpatientDiseaseTableColumns"
        :request-api="searchIcdBzApi"
        :request-params="requestParams"
      >
        <template #header>
          <AddButton @click="handleAdd">新增</AddButton>
        </template>
        <template #operation="{ row }">
          <TableButton @click="handleEdit(row)">编辑</TableButton>
          <TableButton @click="handleDelete(row)">删除</TableButton>
        </template>
      </CommonTable>
    </template>

    <DialogContainer
      :visible="dialogVisible"
      :title="isEdit ? '编辑' : '新增'"
      :confirm-callback="handleConfirm"
      :cancel-callback="handleCancel"
      :close-callback="handleCancel"
      :confirm-loading="isSubmitting"
    >
      <el-form ref="ruleFormRef" :rules="icdCommonFormRules" :label-width="70" label-suffix="：" :model="dialogForm">
        <el-form-item label="编码" required prop="code">
          <el-input
            v-model.trim="dialogForm.code"
            :show-word-limit="true"
            maxlength="40"
            placeholder="请输入编码"
            clearable
            type="text"
          ></el-input>
        </el-form-item>
        <el-form-item label="名称" required prop="name">
          <el-input
            v-model.trim="dialogForm.name"
            :show-word-limit="true"
            maxlength="100"
            placeholder="请输入名称"
            clearable
            type="text"
          ></el-input>
        </el-form-item>
      </el-form>
    </DialogContainer>
  </PageContainer>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted } from "vue"
import { PageContainer, DialogContainer, AddButton, TableButton, CommonTable } from "@/base-components"
import { TabsRouter } from "@/page-components"
import { getIcdLevelConfigApi } from "@/interfaces"
import { Message, toastError, SystemPrompt, SystemAlert } from "@/utils/message-tool"
import IcdSearch from "./components/IcdSearch.vue"
import { tabsRouterList, outpatientDiseaseTableColumns, icdCommonFormRules, ICDTypeEnum } from "./config"
import { addIcdBzApi, searchIcdBzApi, deleteIcdCodeApi, editIcdBzApi } from "./interface"
import type { FormInstance } from "element-plus"

const requestParams = reactive({ filters: `icdVersionId=${ICDTypeEnum.ICD_ZY}` })

const dialogVisible = ref(false)
const isEdit = ref(false)

/*=============================通过 type 获取 icdVersionId================*/
const icdVersionId = ref(0)

onMounted(() => {
  getIcdLevelConfigApi({ type: ICDTypeEnum.ICD_ZY }).then(res => {
    icdVersionId.value = res.data.data?.[0]?.id
  })
})

const dialogForm = reactive({
  id: -1,
  code: "",
  name: ""
})

const icdSearchRef = ref<InstanceType<typeof IcdSearch>>()
const commonTableRef = ref<InstanceType<typeof CommonTable>>()

const isSubmitting = ref(false)

const ruleFormRef = ref<FormInstance>()
// 新增
function handleAdd() {
  isEdit.value = false
  for (let prop in dialogForm) {
    dialogForm[prop] = ""
  }
  dialogVisible.value = true
}

// 编辑
function handleEdit(row) {
  isEdit.value = true
  for (let prop in dialogForm) {
    dialogForm[prop] = row[prop]
  }
  dialogVisible.value = true
}

// 删除
function handleDelete(row) {
  SystemPrompt("确定删除该条记录吗？", "warning").then(() => {
    if (!icdVersionId.value) return SystemAlert("请先选择版本", "warning")
    deleteIcdCodeApi({ id: row.id, icdVersionId: icdVersionId.value })
      .then(() => {
        Message.success("删除成功")
        commonTableRef.value?.refreshTableData()
      })
      .catch(err => toastError(err))
  })
}

// 确认
function handleConfirm() {
  if (!icdVersionId.value) return SystemAlert("请先选择版本", "warning")

  ruleFormRef.value?.validate(valid => {
    if (!valid) return
    isSubmitting.value = true

    const requestApi = isEdit.value ? editIcdBzApi : addIcdBzApi
    const params = {
      code: dialogForm.code,
      name: dialogForm.name,
      icdVersionId: icdVersionId.value,
      ...(isEdit.value ? { id: dialogForm.id } : {})
    } as any
    return requestApi(params)
      .then(() => {
        if (isEdit.value) {
          Message.success("修改成功")
        } else {
          Message.success("添加成功")
        }
        commonTableRef.value?.refreshTableData()
        dialogVisible.value = false
      })
      .catch(err => toastError(err))
      .finally(() => (isSubmitting.value = false))
  })
}

// 关闭新建/编辑弹窗
function handleCancel() {
  dialogVisible.value = false
  for (let prop in dialogForm) {
    if (prop === "greyCode") {
      dialogForm[prop] = false
    } else {
      dialogForm[prop] = ""
    }
  }
}
</script>
