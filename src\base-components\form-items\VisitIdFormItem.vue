<template>
  <el-form-item :label="$props.label">
    <el-input v-model="visitIdValue" type="number">
      <template #prepend>
        <el-select v-model="visitIdType" style="width: 100px">
          <el-option label="大于" value=">" />
          <el-option label="等于" value="=" />
          <el-option label="小于" value="<" />
        </el-select>
      </template>
    </el-input>
  </el-form-item>
</template>

<script setup lang="ts">
import { computed } from "vue"

const props = defineProps({
  label: { type: String, required: true },
  inputValue: { type: String, required: true },
  selectValue: { type: String, required: true }
})
const emits = defineEmits(["update:inputValue", "update:selectValue"])

const visitIdValue = computed({
  get: () => props.inputValue,
  set: val => emits("update:inputValue", val)
})

const visitIdType = computed({
  get: () => props.selectValue,
  set: val => emits("update:selectValue", val)
})
</script>
