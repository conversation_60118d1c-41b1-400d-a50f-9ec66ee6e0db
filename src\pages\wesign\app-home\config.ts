import moment from "moment"
import { StatisticTypeEnum } from "@/configs/enums"
import type { TableColumnItem } from "@/types"

// 待办选项icon
const todoIconMap = new Map([
  ["提交", "submit.svg"],
  ["终末质控", "fqc.svg"],
  ["编目", "catalog.svg"],
  ["科室质控", "qc.svg"],
  ["上级医师审核", "medical-qc.svg"],
  ["质控护士签收", "nurse-qc.svg"],
  ["归档", "archive.svg"],
  ["封存审核", "sealing-review.svg"],
  ["召回审核", "retrieve.svg"],
  ["借阅审核", "borrow.svg"],
  // ["首页质控", "home-page-qc.svg"],
  ["打印审核", "print.svg"],
  ["病案签收", "sign.svg"]
])

// 获取待办选项icon
export const getTodoIcon = (todoName: string) => {
  const iconName = todoIconMap.get(todoName)
  if (iconName) {
    return new URL(`/src/assets/home/<USER>
  }
  return ""
}

// 待办选项卡对应路由
export const todoRoutePathMap = new Map([
  ["提交", "/medical-record/list/recently?status=0"],
  ["终末质控", "/final-quality-control/manage/waiting-control"],
  ["编目", "/coding"],
  ["科室质控", "/department-quality-control/medical-qc/waiting-control"],

  ["病案签收", "/medical-record/sign-for"],
  ["上级医师审核", "/department-quality-control/medical-qc/waiting-control"],
  ["质控护士签收", "/department-quality-control/nurse-qc/waiting-control"],

  ["归档", "/archive"],
  ["封存审核", "/sealing/approval/waiting-approve"],
  ["召回审核", "/recall/approval/pending"],
  ["借阅审核", "/borrow/approval/wait"],
  ["打印审核", "/print/approval/waiting-approved"]
])

export interface DateOptionItem {
  label: string
  value: StatisticTypeEnum | ""
}

// 日期选项
export const dateOptions: Array<DateOptionItem> = [
  { label: "今日", value: StatisticTypeEnum.DAY },
  { label: "本周", value: StatisticTypeEnum.WEEK },
  { label: "本月", value: StatisticTypeEnum.MONTH },
  { label: "近三月", value: StatisticTypeEnum.THREEMONTHS },
  { label: "近六月", value: StatisticTypeEnum.SIXMONTHS },
  { label: "近一年", value: StatisticTypeEnum.PASTYEAR }
]

// 科室选项
export const deptOptions: Array<Record<string, any>> = [
  { label: "全部科室", value: "ALL" },
  { label: "科室排名", value: "SOME" },
  { label: "自定义", value: "CUSTOM" }
]

// 根据statisticType获取时间范围
export const getRangeDateByType = (val: StatisticTypeEnum) => {
  let startTime = moment()

  switch (val) {
    case "DAY": {
      // 当日
      startTime = moment().startOf("day")
      break
    }
    case "WEEK": {
      // 本周
      startTime = moment().startOf("isoWeek")
      break
    }
    case "MONTH": {
      // 本月
      startTime = moment().startOf("month")
      break
    }
    case "THREEMONTHS": {
      // 近三月
      startTime = moment().subtract(3, "months")
      break
    }
    case "SIXMONTHS": {
      // 近六月
      startTime = moment().subtract(6, "months")
      break
    }
    case "PASTYEAR": {
      // 近一年
      startTime = moment().subtract(1, "years")
      break
    }
    default: {
      startTime = moment().startOf("day")
      break
    }
  }
  return [startTime.toDate().getTime(), new Date().getTime()]
}

// 消息类型
export enum MessageConfigEnum {
  ANNOUNCEMENT = "ANNOUNCEMENT",
  WAIT_DISPOSE = "WAIT_DISPOSE",
  MESSAGE = "MESSAGE"
}
export const messageConfig = [
  { title: "待处理", key: MessageConfigEnum.WAIT_DISPOSE },
  { title: "系统通知", key: MessageConfigEnum.MESSAGE },
  { title: "系统公告", key: MessageConfigEnum.ANNOUNCEMENT }
]

// 消息结构
export interface MessageItemType {
  title: string
  content: string
  contentHtml?: string
  dialogType: string
  createDateTimeCh: string
  createDateTime: string
  id: number
  isRead: string
}

// 前端自己定义的科室统计所需的column列名
export const departmentDataMap = {
  collect: "已采集",
  outHospital: "已出院",
  submit: "已提交",
  DeptQualityControl: "科室质控",
  repair: "返修",
  archived: "已归档",
  archiveFail: "归档失败",
  lateArchive: "迟归",
  recall: "召回"
}

// 科室统计column
export const departmentStatisticalColumns: Array<TableColumnItem> = [
  { prop: "deptName", label: "科室", minWidth: 100, sortable: true, fixed: "left" },
  { prop: "collect", label: "采集", minWidth: 80, sortable: true },
  { prop: "outHospital", label: "出院", minWidth: 80, sortable: true },
  { prop: "submit", label: "提交", minWidth: 80, sortable: true },
  { prop: "DeptQualityControl", label: "科室质控", minWidth: 120, sortable: true },
  { prop: "repair", label: "返修", minWidth: 80, sortable: true },
  { prop: "archived", label: "已归档", minWidth: 100, sortable: true },
  { prop: "archiveFail", label: "归档失败", minWidth: 120, sortable: true },
  { prop: "lateArchive", label: "迟归", minWidth: 100, sortable: true },
  { prop: "recall", label: "召回", minWidth: 100, sortable: true }
]

// 将数据处理成table所需格式
export const formatDepartmentStatisticalData = departmentStatistics => {
  return departmentStatistics.map(item => {
    const result = { deptName: item.deptName }
    for (const key in departmentDataMap) {
      for (const index in item.opers) {
        if (item.opers[index].name === departmentDataMap[key]) {
          result[key] = item.opers[index].count
        }
      }
    }
    return result
  })
}
