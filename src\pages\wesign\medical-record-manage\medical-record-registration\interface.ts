import axios from "@/interfaces/axios-instance"

// 根据科室获取文书分类
export const getRecordTypeByDeptApi = (deptCode: string) => {
  return axios({
    method: "get",
    url: "/api/document/mrClassDept",
    params: {
      deptCode: deptCode
    }
  })
}

/*===================== 缺失登记 =====================*/

// 获取缺失登记列表
export const getRegistrationWaitListApi = params => {
  return axios({
    method: "get",
    url: "/api/document/deletion",
    params: params
  })
}

// 获取病案登记的缺失表格数据
export const getMissingReportListApi = (documentBagWsid: string) => {
  return axios({
    method: "get",
    url: `/api/document/deletion/${documentBagWsid}`
  })
}

// 提交病案缺失记录
export const submitMissingReportApi = (documentBagWsid: string, inpNo: string, deletionDtoList: any[]) => {
  return axios({
    method: "post",
    url: "/api/document/deletion",
    data: {
      documentBagWsid: documentBagWsid,
      inpNo: inpNo,
      deletionDtoList: deletionDtoList
    }
  })
}

/*===================== 登记记录 =====================*/

// 获取登记记录列表
export const getRegistrationRecordListApi = params => {
  return axios({
    method: "get",
    url: "/api/document/deletion/record",
    params: params
  })
}

// 获取提交缺失登记详情
export const getMissingReportDetailApi = (documentBagWsid: string) => {
  return axios({
    method: "get",
    url: `/api/document/deletion/record/${documentBagWsid}`
  })
}
