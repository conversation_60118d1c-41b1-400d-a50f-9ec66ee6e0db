# # 本地
# FROM nginx:1.25.5
# #进入nginx目录
# WORKDIR /usr/share/nginx
# #删除原有html文件夹
# RUN rm -rf html
# #将dist拷贝到nginx静态资源目录下
# ADD dist html
# #暴露8080端口
# EXPOSE 8080


# Jenkins
FROM registry.cn-chengdu.aliyuncs.com/signit-fe/wesign-hospital-env:0.0.4 as builder

# 将所有文件复制过来
COPY . .

# 设置时区
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' > /etc/timezone

# 打包dist
RUN npm run build-image

FROM registry.cn-chengdu.aliyuncs.com/webase/nginx:1.25.5  

#进入nginx目录
WORKDIR /usr/share/nginx

RUN rm -rf html

# #将dist拷贝到nginx静态资源目录下
COPY --from=builder /usr/share/nginx/dist html

#暴露8080端口
EXPOSE 8080


# FROM registry.cn-chengdu.aliyuncs.com/signit-fe/wesign-hospital-env:0.0.3 as builder

# COPY . .

# RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
# RUN echo 'Asia/Shanghai' > /etc/timezone

# RUN npm run build-image

# FROM registry.cn-chengdu.aliyuncs.com/webase/nginx:1.25.5  

# #进入nginx目录
# WORKDIR /usr/share/nginx

# RUN rm -rf html

# # #将dist拷贝到nginx静态资源目录下
# # ADD  /usr/share/nginx/dist html
# COPY --from=builder /usr/share/nginx/dist html

# #暴露8080端口
# EXPOSE 8080
