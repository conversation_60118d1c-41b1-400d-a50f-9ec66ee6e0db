<template>
  <DialogContainer
    v-model:visible="userConfigDialogVisible"
    title="自定义配置"
    :width="500"
    :confirm-callback="handleConfirm"
    :system-default-callback="handleSystemDefault"
    :confirm-loading="confirmLoading"
    :default-btn="true"
  >
    <div class="title-wrapper flex-between">
      <span>选择需要显示的列字段</span>
      <el-checkbox v-model="checkedAll" :indeterminate="isIndeterminate">全选</el-checkbox>
    </div>
    <div v-loading="userConfigLoading" class="user-checkbox-container">
      <div class="user-checkbox-item" v-for="configItem in userArchivalConfigData">
        <div class="label-item">{{ configItem.archiveItemTitle }}</div>
        <div class="checkbox-wrapper">
          <el-checkbox v-model="configItem.archiveCountCheck" :label="configItem.archiveCountTitle" @change="handleChangeCheckbox"></el-checkbox>
          <el-checkbox
            v-if="configItem.archivePercentageTitle"
            v-model="configItem.archivePercentageCheck"
            :label="configItem.archivePercentageTitle"
            @change="handleChangeCheckbox"
          ></el-checkbox>
        </div>
      </div>
    </div>
  </DialogContainer>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from "vue"
import { getUserArchivalConfigApi, updateUserArchivalConfigApi } from "../../interface"
import { DialogContainer } from "@/base-components"
import { Message, toastError } from "@/utils"

const userConfigDialogVisible = ref(false)

const checkedAll = ref(false)

const userConfigLoading = ref(false)
const confirmLoading = ref(false)
const userArchivalConfigData = ref<Array<Record<string, any>>>([])

// 中间态
const isIndeterminate = ref(false)

const emits = defineEmits(["update:config"])

watch(() => checkedAll.value, (val) => {
  setUserArchivalConfig(val)
  isAllCheck(userArchivalConfigData.value)
}, { immediate: true })

function getUserArchivalConfigData() {
  userConfigLoading.value = true
  getUserArchivalConfigApi()
    .then(res => {
      userArchivalConfigData.value = res.data.data
      isAllCheck(userArchivalConfigData.value)
    })
    .catch(err => {
      toastError(err)
    })
    .finally(() => {
      userConfigLoading.value = false
    })
}

// 设置配置项
function setUserArchivalConfig(val) {
  userArchivalConfigData.value.forEach(item => {
    item.archiveCountCheck = val
    item.archivePercentageCheck = val
  })
}

// 是否已经全选
function isAllCheck(configData) {
  const status: Array<Boolean> = []
  configData.forEach(item => {
    if (item.hasOwnProperty('archiveCountCheck')) status.push(item.archiveCountCheck)
    if (item.hasOwnProperty('archivePercentageCheck')) status.push(item.archivePercentageCheck)
  })
  if (status.every(statusItem => statusItem === true)) {
    isIndeterminate.value = false
    checkedAll.value = true
  } else if (status.every(statusItem => statusItem === false)) {
    isIndeterminate.value = false
    checkedAll.value = false
  } else {
    isIndeterminate.value = true
  }
}

function handleChangeCheckbox() {
  isAllCheck(userArchivalConfigData.value)
}

// 确认操作
function handleConfirm() {
  const data = [] as any
  const itemCode = [] as any
  userArchivalConfigData.value.forEach(item => {
    const obj = {
      archiveCountTitle: item.archiveCountTitle,
      archiveCountCode: item.archiveCountCode,
      archiveCountCheck: item.archiveCountCheck,
      archivePercentageTitle: item.archivePercentageTitle,
      archivePercentageCode: item.archivePercentageCode,
      archivePercentageCheck: item.archivePercentageCheck
    }
    data.push(obj)
    if (item.archiveCountCode && item.archiveCountCheck) {
      itemCode.push(item.archiveCountCode)
    }
    if (item.archivePercentageCode && item.archivePercentageCheck) {
      itemCode.push(item.archivePercentageCode)
    }
  })
  confirmLoading.value = true
  updateUserArchivalConfigApi(data)
    .then(() => {
      Message.success("更新配置成功")
      emits("update:config")
    })
    .catch(err => {
      toastError(err)
    })
    .finally(() => {
      confirmLoading.value = false
      userConfigDialogVisible.value = false
    })
}

// 设置系统默认
function handleSystemDefault() {
  userArchivalConfigData.value.forEach(item => {
    if ([0, 2, 3, 5, 7].includes(item.archiveItemDays)) {
      item.archiveCountCheck = true
      item.archivePercentageCheck = true
    } else {
      item.archiveCountCheck = false
      item.archivePercentageCheck = false
    }
  })
}

defineExpose({
  showUserConfigDialog() {
    userConfigDialogVisible.value = true
    checkedAll.value = false
    getUserArchivalConfigData()
  }
})
</script>

<style lang="less" scoped>
.title-wrapper {
  padding: 4px 12px;
  background: #f0f4fb;
}

.user-checkbox-container {
  display: flex;
  flex-wrap: wrap;
  max-height: 450px;
  overflow: auto;
  .user-checkbox-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 8px;
    gap: 30px;
    .label-item {
      width: 120px;
    }
    .checkbox-wrapper {
      display: flex;
    }
  }
}
</style>
