import moment from "moment"
import type { TableColumnItem, BaseOptionItem } from "@/types"
// 病案质量报表column
export const medicalQualityReportColumn: Array<TableColumnItem> = [
  { prop: "outHospitalDeptName", label: "科室", minWidth: 120, fixed: "left" },
  { prop: "medicalCount", label: "终末质控病案数", minWidth: 150, sortable: true },
  { prop: "averageScore", label: "平均分", minWidth: 120, sortable: true },
  { prop: "AMedical", label: "甲级病案", minWidth: 120, sortable: true },
  { prop: "AMedicalPercentage", label: "甲级率", minWidth: 120, sortable: true },
  { prop: "BMedical", label: "乙级病案", minWidth: 120, sortable: true },
  { prop: "BMedicalPercentage", label: "乙级率", minWidth: 120, sortable: true },
  { prop: "CMedical", label: "丙级病案", minWidth: 120, sortable: true },
  { prop: "CMedicalPercentage", label: "丙级率", minWidth: 120, sortable: true }
]

// 科室病案质量options
export const deptMedicalOptions: BaseOptionItem[] = [
  { label: "病案数", value: "medicalCount" },
  { label: "甲级病案", value: "AMedical" },
  { label: "甲级率", value: "AMedicalPercentage" },
  { label: "乙级病案", value: "BMedical" },
  { label: "乙级率", value: "BMedicalPercentage" },
  { label: "丙级病案", value: "CMedical" },
  { label: "丙级率", value: "CMedicalPercentage" }
]

// 获取某个月份的天数
const getMonthDays = (year, month) => {
  return new Date(year, month, 0).getDate()
}

/**
 * @length x轴的长度
 * @startTime x轴的开始值
 * @unit x轴间隔值
 * @format 日期格式
 * @isMonth 是否重新计算月份天数
 */
export const getTimeList = (length: number, startTime: number, unit: number, format: string, isMonth?: boolean) => {
  let timeList = [] as string[]
  timeList = new Array(length)
  for (let i = 0; i <= length; i++) {
    if (isMonth) {
      //选择日期范围时，若超过一个月，需重新计算范围内每个月份的天数
      let monthDays = 0
      const year = new Date(startTime + monthDays * i).getFullYear()
      const month = new Date(startTime + monthDays * i).getMonth()
      monthDays = getMonthDays(year, month + 1) * 3600000 * 24
      timeList[i] = moment(startTime + monthDays * i).format(format)
    } else {
      timeList[i] = moment(startTime + unit * i).format(format)
    }
  }
  return timeList
}

// 格式化返回数据中的dateTime
const formatResDataTime = (type: "DAY" | "WEEK" | "MONTH" | "YEAR" | "OVERMONTH", time) => {
  switch (type) {
    case "DAY":
      return moment(time).format("HH:mm")
    case "WEEK":
    case "MONTH":
      return moment(time).format("MM-DD")
    case "OVERMONTH":
      return moment(time).format("YYYY-MM")
    case "YEAR":
      return moment(time).format("YYYY")
  }
}

// 获取series数据
export const getAverageScoreSeriesData = (type, timeList: Array<any>, resData) => {
  // 将后台返回的时间转换为和前端计算的x轴一样的格式
  const baseData = resData.map((item: any) => {
    return {
      ...item,
      outHospitalDatetimeStr: formatResDataTime(type, new Date(item.outHospitalDatetimeStr).getTime())
    }
  })
  //获取resDate对应的字段数据
  const data = new Array(timeList.length).fill(0)
  timeList.map((time, index) => {
    const target = baseData.find(item => item.outHospitalDatetimeStr === time)
    data[index] = target ? target.averageScore : 0
  })
  return data
}
// 获取0点的毫秒数
export function getMidnightMillis(inputMillis) {
  // 从输入的毫秒数创建一个 Date 对象
  const date = new Date(inputMillis)

  // 提取年份、月份和日期
  const year = date.getFullYear()
  const month = date.getMonth() // 月份是从0开始的
  const day = date.getDate()

  // 创建一个新的 Date 对象，表示该日期的0点
  const midnight = new Date(year, month, day, 0, 0, 0)

  // 获取0点的毫秒数
  return midnight.getTime()
}
