import axios from "./axios-instance"
import { toastError } from "@/utils"

export * from "./file" // 文件相关公共接口
export * from "./sealing" // 封存相关公共接口
export * from "./user-dept-query" // 用户及科室查询相关公共接口
export * from "./system-config" // 系统配置相关公共接口
export * from "./borrow" // 借阅相关公共接口
export * from "./icd" // icd查询公共接口
export * from "./quality-control" //质控公共接口
export * from "./complex-search"
export * from "./scan"

/* ======================== 其他公共接口 ======================== */

/**
 * @method GET
 * @desc   获取模板列表
 */
export function getTemplateList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/collect/templates`,
    params
  })
}

/**
 * @method GET
 * @desc   获取所有模板
 */
export function getAllTemplate() {
  return axios
    .request<IPaginationResponseData>({
      method: "get",
      url: `/api/collect/templates`,
      params: {
        limit: 1000,
        offset: 0
      }
    })
    .then(res => res.data?.data?.rows ?? [])
}

interface IRecordViewDataParams {
  inpNo: string
  sealKey?: string
}

/**
 * @method GET
 * @desc   查看病案文档
 */
export function getRecordViewData(params: IRecordViewDataParams) {
  const { inpNo, sealKey = "" } = params
  return axios({
    method: "get",
    url: `/api/document/document-bags/view/${inpNo}`,
    params: {
      sealKey
    }
  })
}

/**
 * @method POST
 * @desc   用户信息设置-修改密码
 */
export function updatePassword(obj) {
  const { oldPassword, newPassword } = obj
  return axios({
    method: "post",
    url: "/api/system/users/change-password",
    data: { oldPassword, newPassword }
  })
}

/**
 * @method POST
 * @desc   退出登录
 */
export function logoutApi() {
  return axios({
    method: "post",
    url: "/api/auth/loginout"
  })
}

/**
 * @method GET
 * @desc   病案验签及签名查询
 */
export function getDocumentSignInfoApi(params) {
  return axios({
    method: "GET",
    url: `/api/document/sign-verify`,
    params: {
      fileWsid: params.fileWsid
    }
  })
}

// 获取未读消息列表
export function getHeaderMessageListApi() {
  return axios({
    method: "GET",
    url: "/api/system/user-current/wait-messages"
  })
}

// 获取未读消息数量列表
export function getHeaderMessageCountApi() {
  return axios({
    method: "GET",
    url: "/api/system/user-current/wait-messages/count"
  })
}

/**
 * @method GET
 * @desc   查询首页标准字段的取值范围
 */
export function searchAllMetaCodeTableFieldApi(params: { filters: string }) {
  return axios
    .request<IResponseData>({
      method: "get",
      url: "/api/catalog/standard/all-options",
      params: {
        filters: params.filters
      }
    })
    .then(res => res.data?.data ?? [])
}

interface IScoreStandardParams {
  documentType?: string
  status?: string
  scoreType?: string
}

/**
 * @method GET
 * @desc   查询评分配置
 */
export function queryScoreStandard(params: IScoreStandardParams) {
  return axios
    .request<IResponseData>({
      method: "get",
      url: `/api/qc/score-standard`,
      params
    })
    .then(res => res.data?.data ?? [])
    .catch(err => {
      toastError(err, "获取评分配置数据失败")
      return []
    })
}

/**
 * @method GET
 * @desc   获取所有文书分类列表
 */
export function getMrClassList() {
  return axios
    .request<IResponseData>({
      method: "get",
      url: "/api/document/mrClassList"
    })
    .then(res => res.data?.data ?? [])
    .catch(err => {
      toastError(err, "获取文书分类列表失败")
      return []
    })
}

/**
 * @method GET
 * @desc   获取打印类型
 */
export function getPrintType() {
  return axios
    .request<IResponseData>({
      method: "get",
      url: "/api/print/group"
    })
    .then(res => res.data?.data ?? [])
}

/**
 * @method GET
 * @desc   分页获取病案列表
 */
export const getRecordListApi = (params: IPaginationRequestParams) => {
  return axios({
    method: "get",
    url: "/api/document/bags",
    params
  })
}

/**
 * @method GET
 * @desc   获取水印
 */
export const getWatermarkApi = () => {
  return axios({
    method: "get",
    url: "/api/system/system-config/watermark"
  })
}

/**
 * @method GET
 * @desc   获取职称列表
 */
export function getJobTitlesApi() {
  return axios({
    method: "get",
    url: "/api/hospital/job-title"
  })
}

/**
 * @method GET
 * @desc   获取专业列表
 */
export function getSpecialtiesApi() {
  return axios({
    method: "get",
    url: "/api/hospital/specialties"
  })
}

/**
 * @method GET
 * @desc   获取职称列表
 */
export function getInpatientAreaApi() {
  return axios({
    method: "get",
    url: "/api/hospital/inpatient-area"
  })
}

/**
 *  @desc 查询已审核节点
 * @param businessKey 申请id
 * @param taskId 流程任务id
 */
export const getAuditNodeApi = (params: { businessKey: string; taskId: string }) => {
  return axios({
    method: "get",
    url: "/api/workflow/audit-node",
    params
  })
}

/**
 * @method get
 * @desc   查询任务回退节点列表
 */
export function getApprovalNodeApi(params) {
  const { businessKey, taskId } = params
  return axios({
    method: "get",
    url: `/api/workflow/processTask/rollback-def`,
    params: { businessKey, taskId }
  }).then(res => {
    return res?.data?.data || []
  })
}

/**
 * @method get
 * @desc   查询审核详情(按节点)
 */
export function getProcessActInstance(params) {
  return axios({
    method: "get",
    url: `/api/workflow/processInstance/hi/act-instance`,
    params: { businessKey: params.businessKey }
  })
}

/**
 * @method get
 * @desc   查询流程图实时审核路线
 */
export function getProcessInstance(params) {
  return axios({
    method: "get",
    url: `/api/workflow/processInstance/realTime/act-instance`,
    params: { businessKey: params.businessKey }
  })
}

/**
 * @method post
 * @desc   通过用户一组wsid批量查询系统用户信息
 */
export const batchGetUserAvatarsByWsid = (wsids: string[]) => {
  return axios({
    method: "post",
    url: "/api/system/users/get/batch",
    data: wsids
  })
}

// 通过流程id获取流程详情
export function getApprovalProcessDetailApi(flowDefinedId: string) {
  return axios({
    method: "get",
    url: `/api/workflow/processDefine/${flowDefinedId}`
  })
}

// 下载归档病案证书
export const downloadArchiveCertificateApi = (documentWsid: string) => {
  return axios({
    method: "get",
    url: `/api/document/bag/archive/certificate/${documentWsid}`,
    responseType: "blob"
  })
}

// 获取敏感关键词位置（用于隐藏文档首页患者信息）
export const getSenseKeywordsPositionApi = (documentWsid: string, status = 1) => {
  return axios({
    method: "get",
    url: `/api/document/file/home-page/${status}/${documentWsid}/position`
  })
}

interface ArchiveVersionBagParams {
  inpNo: string
  versionCode: string
  sealKey?: string
  coverView?: boolean
}

/**
 * @method GET
 * @desc 查看指定已归档版本的数据
 */
export const getArchiveVersionDetailApi = (params: ArchiveVersionBagParams) => {
  const { inpNo, versionCode, sealKey, coverView = true } = params
  return axios({
    method: "get",
    url: `/api/document/bags/${inpNo}/supplement-archive/view`,
    params: {
      versionCode,
      sealKey,
      coverView
    }
  })
}

/**
 * @method GET
 * @desc   获取数据字段数据
 */
export function getApproveReasonApi(params: { groupKey: string }) {
  return axios({
    method: "get",
    url: `/api/system/dictionary/group-list?key=${params.groupKey}`
  }).then(res => res.data?.data ?? [])
}

/**
 * @method GET
 * @desc   获取数据字段数据
 */
export function permissionCheckApi(inpNo: string) {
  return axios({
    method: "get",
    url: `/api/document/bags/${inpNo}/permission-check`
  })
}

/**
 * @method GET
 * @desc   获取病案袋所有状态列表
 */
export function getBagsStatusListApi() {
  return axios({
    method: "get",
    url: `/api/document/bags/status`
  }).then(res => res.data?.data ?? [])
}

/**
 * @method GET
 * @desc   保密管理-列表
 */
export function getSecreciesList() {
  return axios({
    method: "get",
    url: `/api/document/secrecies`
  }).then(res => {
    res.data.data.rows = res.data.data
    return res
  })
}

/**
 * @method GET
 * @desc   获取指定用户休假日期
 */
export function getUserHolidayApi(userWsid: string) {
  return axios({
    method: "get",
    url: `/api/system/users/holiday/${userWsid}`
  })
}

// 根据当前登录人以及病案判断是否有提交和撤销的权限
export const getSubmitPermissionApi = (inpNo: string) => {
  return axios({
    method: "get",
    url: `/api/document/bags/${inpNo}/submit-permission`
  })
}

interface IQuashMedicalParams {
  inpNo: string
  quashCause?: string
}

// 病案撤销
export const quashMedicalApi = (params: IQuashMedicalParams) => {
  return axios({
    method: "post",
    url: `/api/document/bags/${params.inpNo}/quash?quashCause=${params?.quashCause}`
  })
}

// 获取操作日志枚举
export const getOperLogEnumApi = () => {
  return axios({
    method: "get",
    url: `/api/document/bags/oper-log-enum`
  })
}

interface IDownloadFileParams {
  inpNo: string
  type: string
  source: string
  sealKey?: string
  ext?: string
}

// 通用下载病案文件
export const downloadFileApi = (params: IDownloadFileParams) => {
  return axios({
    method: "get",
    url: `/api/document/bags/download-file/${params.inpNo}`,
    params: params,
    responseType: "arraybuffer"
  })
}

// 编目-首页字段值的选型组

export const getCatalogStandardOptionsCodeApi = (code: string) => {
  return axios({
    method: "get",
    url: `/api/catalog/standard/options/code?code=${code}`
  })
}

// 获取病案所有文件列表
export const getDocumentBagsFilesApi = (inpNo: string) => {
  return axios({
    method: "get",
    url: `/api/document/bags/${inpNo}/files`
  })
}

interface IDynamicFormParams {
  appScenario: string
  businessDataWsid: string
  formData?: string
}

// 门急诊hqms-获取编目数据
export const getDynamicFormCatalogApi = (params: IDynamicFormParams) => {
  return axios({
    method: "get",
    url: `/api/catalog/dynamic-form-catalog/${params.appScenario}/${params.businessDataWsid}`
  })
}

export const getDifferenceDynamicFormCatalogApi = (params: IDynamicFormParams) => {
  return axios({
    method: "get",
    url: `/api/catalog/dynamic-form-catalog/record/${params.appScenario}/${params.businessDataWsid}`
  })
}

// 门急诊hqms-完成编目
export const confirmDynamicFormCatalogApi = (data: IDynamicFormParams) => {
  return axios({
    method: "put",
    url: `/api/catalog/dynamic-form-catalog/${data.appScenario}/${data.businessDataWsid}`,
    data: {
      formData: data.formData
    }
  })
}

// 门急诊hqms-质控校验
export const checkDynamicFormCatalogApi = (data: IDynamicFormParams) => {
  return axios({
    method: "post",
    url: `/api/catalog/dynamic-form-catalog/qc-check`,
    data: data
  })
}

/**
 * @method GET
 * @desc   查询首页标准字段范围-分页
 */
export function searchMetaCodeTableFieldApi(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: "/api/catalog/standard/options",
    params
  })
}

// 门急诊hqms-首页导出
export const exportDynamicFormCatalogApi = (registerNo: string) => {
  return axios({
    method: "get",
    url: `/api/catalog/outpatient-hqms/${registerNo}/first-page-export`,
    responseType: "blob"
  })
}

// 门急诊hqms-暂存
export const temporaryDynamicFormCatalogApi = (data: IDynamicFormParams) => {
  return axios({
    method: "put",
    url: `/api/catalog/dynamic-form-catalog/${data.appScenario}/${data.businessDataWsid}/temporary`,
    data: {
      formData: data.formData
    }
  })
}

// 门急诊hqms-撤销
export const cancelDynamicFormCatalogApi = (data: IDynamicFormParams) => {
  return axios({
    method: "put",
    url: `/api/catalog/dynamic-form-catalog/${data.appScenario}/${data.businessDataWsid}/cancel`
  })
}

// 检查提交的病案的完整性
export const checkIntegralityApi = (inpNo: string) => {
  return axios({
    method: "get",
    url: `/api/document/bags/${inpNo}/submit-check`
  })
    .then(res => {
      return res.data.data
    })
    .catch(error => {
      throw error
    })
}

// 检查签名完整性
export const checkSignIntegralityApi = (inpNo: string) => {
  return axios({
    method: "get",
    url: `/api/document/bags/${inpNo}/submit-check/sign`
  })
    .then(res => {
      return res.data.data || {}
    })
    .catch(error => {
      throw error
    })
}

// 发送整改通知
export const sendModifyMessageApi = (inpNo: string) => {
  return axios({
    method: "post",
    url: `/api/document/bags/${inpNo}/submit/sendRectifyNotice`
  })
}

// 删除文档节点
export const deleteFileApi = (documentWsid: string) => {
  return axios({
    method: "post",
    url: "/api/document/bag/file-delete",
    data: { wsids: [documentWsid] }
  })
}

// 获取病案文档树形列表
export const getSubmissionTreeApi = (inpNo: string, sealKey: string) => {
  return axios({
    method: "get",
    url: `/api/document/document-bags/view/${inpNo}`,
    params: { sealKey: sealKey }
  })
}

interface IUnassignedDocParams {
  inpNo?: string
  beforeDays?: string
}
// 获取待归档的文档列表
export const getInactiveDocumentsApi = (params: IUnassignedDocParams) => {
  return axios({
    method: "get",
    // url: `/api/document/bags/${inpNo}/inactive`
    url: `/api/document/unassigned/docs`,
    params
  })
}

// 获取文件的提交进度
export const getSubmitProgressApi = (inpNo: string) => {
  return axios({
    method: "get",
    url: `/api/document/bags/${inpNo}/submit-count`
  })
}

export interface SubmitMedicalRecordParams {
  inpNo: string // 病案号
  confirmDocumentWsids: string[]
  signInfos?: SignInfo[]
  submitRemark?: string
}

export interface SignInfo {
  documentWsid: string
  templateFormName: string // 表单名称
  jobIds: string[] // 工号组
}

// 提交病案
export const submitMedicalRecordApi = (params: SubmitMedicalRecordParams) => {
  return axios({
    method: "post",
    url: `/api/document/bags/${params.inpNo}/sign-reclaim`,
    data: {
      confirmDocumentWsids: params.confirmDocumentWsids,
      signInfos: params.signInfos || [],
      submitRemark: params.submitRemark
    }
  })
}

// 模糊查询当前科室成员
export const getDepartmentMemberApi = (keyword?: string) => {
  return axios({
    method: "get",
    url: `/api/hospital/users/fuzzy-query-current`,
    params: {
      item: keyword || ""
    }
  })
}

// 获取需要签名的文件列表
export const getNeedSignDocumentsApi = (inpNo: string) => {
  return axios({
    method: "get",
    url: `/api/document/bags/${inpNo}/need-sign`
  })
}

// 获取需要签署的表单
export const getSignFormsApi = (documentWsid: string) => {
  return axios({
    method: "get",
    url: `/api/document/platform-sign/${documentWsid}/position`
  })
    .then(res => {
      return res.data.data
    })
    .catch(error => {
      toastError(error)
    })
}

// 获取签署者信息 图片/静默签是否开启
export const getSignerInfoApi = (jobIdList: string[]) => {
  return axios({
    method: "get",
    url: "/api/document/user/sign-pic",
    params: { jobIds: jobIdList.join(",") }
  })
    .then(res => {
      return res.data.data
    })
    .catch(error => {
      toastError(error)
    })
}

export interface GetSignPositionParams {
  signPositions: {
    formName: string
    keywordData: Record<string, any>[] // form的位置属性
    joIds: string[]
  }[]
}

// 获取每个签名的基于签名框的位置
export const getEverySignPositionApi = (params: GetSignPositionParams) => {
  return axios({
    method: "post",
    url: "/api/document/user-sign-position",
    data: {
      signPositions: params.signPositions
    }
  })
    .then(res => {
      return res.data.data
    })
    .catch(error => {
      toastError(error)
    })
}

// 删除病案缺失记录
export const deleteMissingReportApi = (wsid: string) => {
  return axios({
    method: "delete",
    url: "/api/document/deletion",
    params: { wsid: wsid }
  })
}

// 获取病案登记的缺失表格数据
export const getMissingReportListApi = (documentBagWsid: string) => {
  return axios({
    method: "get",
    url: `/api/document/deletion/${documentBagWsid}`
  })
}

// 根据科室获取文书分类
export const getRecordTypeByDeptApi = (deptCode: string) => {
  return axios({
    method: "get",
    url: "/api/document/mrClassDept",
    params: {
      deptCode: deptCode
    }
  })
}

// 提交病案缺失记录
export const submitMissingReportApi = (documentBagWsid: string, inpNo: string, missingReport: any) => {
  return axios({
    method: "post",
    url: "/api/document/deletion/addOrUpdate",
    data: {
      documentBagWsid: documentBagWsid,
      inpNo: inpNo,
      ...missingReport
    }
  })
}

/**
 * @method DELETE
 * @desc   编码-退回
 */
export function catalogSendBack(data: Record<string, any>) {
  return axios({
    method: "delete",
    url: `/api/catalog/catalogs/${data.inpNo}/send-back`,
    data: {
      desc: data.desc,
      // operator: data.operator,
      // operatorName: data.operatorName,
      limitedModifyTime: data.limitedModifyTime
    }
  })
}

// 根据科室获取文书分类
export const getUnreceivedFileApi = (inpNo: string) => {
  return axios({
    method: "get",
    url: `/api/document/bags/${inpNo}/submit-check/unreceived`
  })
}

/**
 * @method GET
 * @desc   编码-版本记录
 */
export function getCatalogVersionRecord(inpNo: string) {
  return axios({
    method: "get",
    url: `/api/catalog/catalogs/${inpNo}/sna-history/home-page`
  })
}

/**
 * @method POST
 * @desc   编码-门诊诊断转换（临床-医保）
 */
export function outpatientClinicalToInsuranceApi(data: Array<Record<string, any>>) {
  return axios({
    method: "post",
    url: `/api/catalog/clinical-to-insurance/outpatient`,
    data: {
      params: data
    }
  })
}

/**
 * @method POST
 * @desc   编码-出院诊断转换（临床-医保）
 */
export function diagnosisClinicalToInsuranceApi(data: Array<Record<string, any>>) {
  return axios({
    method: "post",
    url: `/api/catalog/clinical-to-insurance/discharge-diagnosis`,
    data: {
      params: data
    }
  })
}

interface IClinicalToInsuranceData {
  formDataList: Array<Record<string, any>>
  name: string
  code: string
  icdType: string
}

/**
 * @method POST
 * @desc  根据类型转换动态表单数据为医保数据
 */
export function clinicalToInsuranceApi(data: IClinicalToInsuranceData) {
  return axios({
    method: "post",
    url: `/api/catalog/clinical-to-insurance/by-type`,
    data: data
  })
}

/**
 * @method GET
 * @desc   查看编目记录详情（根据应用场景和住院号)
 */
export function getCatalogRecordByinpNoApi(params: IDynamicFormParams) {
  return axios({
    method: "get",
    url: `/api/catalog/dynamic-form-catalog/record-by-inpNo/${params.appScenario}/${params.businessDataWsid}`
  })
}

// 获取患者标签
export function getPatientLabelOptionsApi() {
  return axios({
    method: "get",
    url: `/api/document/bags/patient-label`
  }).then(res => res.data?.data ?? [])
}

/**
 * @method GET
 * @desc   医嘱文书核对
 */
export function getDoctorAdviceCheckApi(inpNo: string) {
  return axios({
    method: "get",
    url: `/api/document/bags/doctor-advice-check/data?inpNo=${inpNo}`
  })
}

/**
 * @method GET
 * @desc   医嘱文书核对
 */
export function getUsedMrClassApi() {
  return axios({
    method: "get",
    url: `/api/document/bags/collect-used-mr-class`
  })
}

/**
 * @method GET
 * @desc   窗口打印-获取终端列表
 */
export function getClientListApi(clientType: string) {
  return axios({
    method: "get",
    url: `/api/print/sse/client-list?clientType=${clientType}`
  })
}

/**
 * @method GET
 * @desc   打印窗口获取邮寄地址
 */
export function getPrintAddressApi(idCode?: string) {
  return axios({
    method: "get",
    url: `/api/print/user/address-with-idCode?idCode=${idCode}`
  })
}

interface IPushSelectDocData {
  formClientId: string
  toClientId: string
  idCard: string
  name: string
  bags: Array<Record<string, any>>
}

/**
 * @method POST
 * @desc  窗口打印-选择文件推送消息到签字版
 */
export function pushSelectDocApi(data: IPushSelectDocData) {
  return axios({
    method: "post",
    url: `/api/print/window-print/select-doc-complete`,
    data: data
  })
}

/**
 * @method POST
 * @desc  SSE-心跳
 */
export function sseHeartbeatApi(clientId: string) {
  return axios({
    method: "post",
    url: `/api/print/sse/heartbeat`,
    data: {
      clientId: clientId
    }
  })
}
