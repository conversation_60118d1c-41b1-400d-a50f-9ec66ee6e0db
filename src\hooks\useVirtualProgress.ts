//虚拟进度条
// 1. 从0到90%每100ms增加10%的进度
// 2. 从90%到99%每100ms增加1%的进度
// 3. 99%后等待导出结果
// 4. 导出结果后进度条到100%
// 5. 导出失败则卡死在99%

import { ref } from "vue"

const useVirtualProgress = () => {
  const progress = ref(0)
  let progressTime
  const startProgress = () => {
    resetProgress()
    progressTime = setInterval(() => {
      if (progress.value < 90) progress.value += 10
      else if (progress.value < 99) progress.value += 1
      // 到达99还没有导出成功则卡死在99等待导出结果
      else if (progress.value === 99) {
        clearInterval(progressTime)
      }
    }, 100)
  }

  const endProgress = () => {
    clearInterval(progressTime)
    progress.value = 100
  }

  const resetProgress = () => {
    progress.value = 0
  }

  return {
    progress,
    startProgress,
    endProgress
  }
}

export default useVirtualProgress
