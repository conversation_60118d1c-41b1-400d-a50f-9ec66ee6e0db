<template>
  <div class="form-group-header">
    <div class="title-bar"></div>
    <div>{{ title }}</div>
    <slot name="content"></slot>
  </div>
</template>

<script setup lang="ts">
defineProps({
  title: { type: String, required: true }
})
</script>

<style lang="less" scoped>
.form-group-header {
  background-color: #ecf0fe;
  height: 30px;
  font-size: 16px;
  line-height: 30px;
  display: flex;
  align-items: center;
  padding: 12px 0;
  font-weight: bold;
  .title-bar {
    height: 16px;
    width: 4px;
    border-radius: 2px;
    background-color: var(--el-color-primary);
    margin: 0 10px;
  }
}
</style>
