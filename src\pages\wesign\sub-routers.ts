import medicalInsuranceReportRouter from "./medical-insurance-report/router"
import type { RouteRecordRaw } from "vue-router"
import homeRouter from "@/pages/wesign/app-home/router"
import borrowManageRouter from "@/pages/wesign/borrow-manage/router"
import collectManageRouter from "@/pages/wesign/collect-manage/router"
import dataReportRouter from "@/pages/wesign/data-report/router"
import departmentQualityControlRouter from "@/pages/wesign/department-quality-control/router"
import dictionaryManageRouter from "@/pages/wesign/dictionary-manage/router"
import finalQualityControlRouter from "@/pages/wesign/final-quality-control/router"
import inventoryManagementRouter from "@/pages/wesign/inventory-management/router"
import largeScreenMonitorRouter from "@/pages/wesign/large-screen-monitor/router"
import archiveRouter from "@/pages/wesign/medical-record-archive/router"
import medicalRecordCodingRouter from "@/pages/wesign/medical-record-coding/router"
import medicalRecordRouter from "@/pages/wesign/medical-record-manage/router"
import printRouter from "@/pages/wesign/medical-record-print/router"
import sealingRouter from "@/pages/wesign/medical-record-sealing/router"
import organizationMemberManageRouter from "@/pages/wesign/organization-member-manage/router"
import physicalExaminationFileRouter from "@/pages/wesign/physical-examination-file/router"
import qualityControlConfigRouter from "@/pages/wesign/quality-control-config/router"
import recallManagementRouter from "@/pages/wesign/recall-management/router"
// import resourceConfigRouter from "@/pages/wesign/resource-config/router"
import secrecyManageRouter from "@/pages/wesign/secrecy-manage/router"
import statisticalStatementRouter from "@/pages/wesign/statistical-statement/router"
import systemManageRouter from "@/pages/wesign/system-manage/router"
// import systemToolRouter from "@/pages/wesign/system-tool/router"

/* ======================== 模块路由配置 ======================== */

// 需要通过授权的模块
const controlledModules: Array<RouteRecordRaw> = [
  homeRouter,
  medicalRecordRouter,
  borrowManageRouter,
  sealingRouter,
  printRouter,
  archiveRouter,
  departmentQualityControlRouter,
  finalQualityControlRouter,
  recallManagementRouter,
  secrecyManageRouter,
  statisticalStatementRouter,
  inventoryManagementRouter
]

// 仅管理员可进入的模块
const adminModules: Array<RouteRecordRaw> = [
  largeScreenMonitorRouter,
  collectManageRouter,
  qualityControlConfigRouter,
  organizationMemberManageRouter,
  dictionaryManageRouter,
  // systemToolRouter,
  systemManageRouter
  // resourceConfigRouter
]

// 病案编码模块
const codingModules = [medicalRecordCodingRouter, dataReportRouter, medicalInsuranceReportRouter]
// 体检无纸化路由
const physicalExaminationModules: Array<RouteRecordRaw> = [physicalExaminationFileRouter]

const subRouters: Array<RouteRecordRaw> = [
  ...controlledModules,
  ...adminModules,
  ...physicalExaminationModules,
  ...codingModules
]

export default subRouters

/* ======================== 基于路由配置生成的路径及菜单配置 ======================== */

// 提取路由路径
function extractRoutePath(routes: Array<RouteRecordRaw>) {
  const pathList: Array<string> = []
  routes.forEach(route => {
    pathList.push(route.path)
    if (route.children) {
      pathList.push(...extractRoutePath(route.children))
    }
  })
  return pathList
}

// 所有可用的页面路径
const validPathList = extractRoutePath(subRouters)

// 路由配置生成菜单权限配置树
function parseRouteConfig(routeConfig: RouteRecordRaw, index: number, level: 1 | 2) {
  const operations = (routeConfig.meta?.operations ?? []) as Array<Record<string, any>>
  const menuConfig = {
    index,
    name: routeConfig.meta?.title,
    id: routeConfig.path,
    path: routeConfig.path,
    icon: routeConfig.meta?.icon,
    status: 1,
    level,
    children: [] as Array<Record<string, any>>,
    functions: [] as Array<string>, // 关联子功能或子页面
    operations: operations.map((item, index) => ({
      index,
      id: `${routeConfig.path}__${item.id}`,
      name: `${routeConfig.meta?.title}-${item.name}`,
      status: 1
    })), // 关联操作
    ext: level === 1 ? { type: routeConfig.meta?.type } : {}
  }
  if (level === 1 && routeConfig.meta?.type === "single" && routeConfig.children) {
    menuConfig.functions = routeConfig.children.map(item => item.path)
  } else if (level === 1 && routeConfig.meta?.type === "multiple" && routeConfig.children) {
    // menuConfig.functions = routeConfig.children.filter(item => item.meta?.hideMenu).map(item => item.path)
    menuConfig.children = routeConfig.children
      // .filter(item => !item.meta?.hideMenu)
      .map((item, index) => parseRouteConfig(item, index, 2))
  } else if (level === 2 && routeConfig.children) {
    menuConfig.functions = routeConfig.children.map(item => item.path)
  }
  return menuConfig
}

// 需要通过授权的模块菜单树
const initMenuData: Array<any> = []
for (let i = 0; i < controlledModules.length; i++) {
  initMenuData.push(parseRouteConfig(controlledModules[i], i, 1))
}

// 后台管理（仅管理员可进）模块菜单树
const adminMenuData: Array<any> = []
for (let i = 0; i < adminModules.length; i++) {
  adminMenuData.push(parseRouteConfig(adminModules[i], i, 1))
}

// 后台管理路由前缀列表
const adminModulePathList = adminModules.map(item => item.path)

// 病案编码模块菜单树
const codingMenuData: Array<any> = []
for (let i = 0; i < codingModules.length; i++) {
  codingMenuData.push(parseRouteConfig(codingModules[i], i, 1))
}

// 病案编码路由前缀列表
const codingModulePathList = codingModules.map(item => item.path)

// 体检无纸化模块菜单树
const physicalExaminationMenuData: Array<any> = []
for (let i = 0; i < physicalExaminationModules.length; i++) {
  physicalExaminationMenuData.push(parseRouteConfig(physicalExaminationModules[i], i, 1))
}

// 体检无纸化路由前缀列表
const physicalExaminationModulePathList = physicalExaminationModules.map(item => item.path)

const allMenuData = initMenuData.concat(adminMenuData.concat(codingMenuData))

export {
  validPathList,
  initMenuData,
  adminMenuData,
  adminModulePathList,
  codingMenuData,
  codingModulePathList,
  physicalExaminationMenuData,
  physicalExaminationModulePathList,
  allMenuData
}
