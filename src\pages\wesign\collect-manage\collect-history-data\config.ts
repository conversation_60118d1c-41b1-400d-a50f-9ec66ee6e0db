import { EditorView } from "codemirror"
import type { TableColumnItem } from "@/types"
import type { FormRules } from "element-plus"

export const tabsRouterList = [
  { path: "/collect/collect-history-data/database", label: "数据库" },
  { path: "/collect/collect-history-data/api", label: "接口配置" },
  { path: "/collect/collect-history-data/reception", label: "接收配置" }
]

// 采集模式
export const extractTypeOptions = [
  { label: "全量", value: "ALL" },
  { label: "增量", value: "INCREMENT" }
  // { label: "固定时间范围", value: "FIXED_TIME_RANGE" }
]

// 编码格式
export const charsetOptions = [
  { value: "GBK", label: "GBK" },
  { value: "GB2312", label: "GB2312" },
  { value: "UTF-8", label: "UTF-8" }
]

// 数据类型选项
export const dataTypeOptions = [
  { label: "病案内容", value: "MR_CONTENT" },
  { label: "科室部门信息", value: "DEPARTMENT" },
  { label: "医护人员信息", value: "HOSPITAL_USER" },
  { label: "在院患者信息", value: "PATIENT_IN_HOSPITAL" },
  { label: "患者基础信息", value: "MR_BASE_INFO" },
  { label: "出院诊断信息", value: "MR_DISCHARGE_DIAGNOSIS" },
  { label: "手术信息", value: "MR_OPERATION" },
  { label: "费用信息", value: "MR_FEE_INFO" }
]

// 自增类型
export const incrementTypeOptions = [
  { label: "时间", value: "TIME" },
  { label: "数字", value: "NUMBER" }
]

export const methodTypeOptions = [
  { label: "GET", value: "GET" },
  { label: "POST", value: "POST" }
]

// 返回体类型
export const resultTypeOptions = [
  { label: "JSON", value: 1, status: "JSON" },
  { label: "XML", value: 2, status: "XML" },
  { label: "TEXT_XML", value: 3, status: "TEXT_XML" }
]

// 返回数据类型
export const resultDataTypeOptions = [
  { label: "数据", value: "DATA_KEY_VALUE" },
  { label: "文件", value: "FILE" }
]

export function getDataTypeDesc(val: string) {
  return dataTypeOptions.find(option => option.value === val)?.label || "--"
}

export const getExtractTypeDesc = (val: string) => {
  return extractTypeOptions.find(option => option.value === val)?.label || "--"
}

/* ================= 数据库 ================ */

export const databaseTableColumns: Array<TableColumnItem> = [
  { prop: "name", label: "名称", minWidth: 150 },
  { prop: "systemName", label: "厂商系统", minWidth: 120 },
  { prop: "dataSourceWsid", label: "数据源名称", minWidth: 120 },
  { prop: "dataType", label: "数据类型", minWidth: 120 },
  { prop: "mrClassCode", label: "文书分类", minWidth: 120 },
  { prop: "querySql", label: "SQL语句配置", minWidth: 150 },
  { prop: "extractType", label: "采集模式", minWidth: 110 },
  { prop: "status", label: "状态", minWidth: 100 },
  { prop: "operation", label: "操作", width: 250, fixed: "right" }
]

export const databaseFormRules: FormRules = {
  name: [{ required: true, message: "请输入名称", trigger: "blur" }],
  systemWsid: [{ required: true, message: "请选择厂商系统", trigger: "blur" }],
  dataSourceWsid: [{ required: true, message: "请选择数据源名称", trigger: "blur" }],
  dataType: [{ required: true, message: "请选择数据类型", trigger: "blur" }],
  mrClassCode: [{ required: true, message: "请选择文书分类", trigger: "blur" }],
  resultDataType: [{ required: true, message: "请选择返回数据类型", trigger: "blur" }],
  extractType: [{ required: true, message: "请选择采集模式", trigger: "blur" }],
  incrementFieldKey: [{ required: true, message: "请输入自增key", trigger: "blur" }],
  incrementFieldType: [{ required: true, message: "请选择自增key类型", trigger: "blur" }],
  incrementLastValue: [{ required: true, message: "请输入/选择最近自增值", trigger: "blur" }],
  incrementDelta: [{ required: true, message: "请输入增幅", trigger: "blur" }],
  cron: [{ required: true, message: "请输入采集频率", trigger: "blur" }]
}

/* ================= 接口 ================ */

export const apiTableColumns: Array<TableColumnItem> = [
  { prop: "name", label: "接口名称", minWidth: 200 },
  { prop: "systemName", label: "厂商系统", minWidth: 120 },
  { prop: "dataType", label: "数据类型", minWidth: 120 },
  { prop: "mrClassName", label: "文书分类", minWidth: 150 },
  { prop: "url", label: "URL", minWidth: 120 },
  { prop: "method", label: "请求方式", minWidth: 120 },
  { prop: "authorization", label: "认证信息", minWidth: 120 },
  { prop: "queryParams", label: "请求参数", minWidth: 120 },
  { prop: "bodyParams", label: "请求体", minWidth: 120 },
  { prop: "resultType", label: "返回体", minWidth: 120 },
  { prop: "resultField", label: "返回数据取值", minWidth: 120 },
  { prop: "timeout", label: "超时时间(秒)", minWidth: 120 },
  { prop: "extractType", label: "采集模式", minWidth: 110 },
  { prop: "cron", label: "采集频率(cron)", minWidth: 180 },
  { prop: "status", label: "状态", minWidth: 90 },
  { prop: "operation", label: "操作", width: 280, fixed: "right" }
]

export const apiFormRules: FormRules = {
  name: [{ required: true, message: "请输入接口名称", trigger: "blur" }],
  systemWsid: [{ required: true, message: "请选择厂商系统", trigger: "blur" }],
  dataType: [{ required: true, message: "请选择数据类型", trigger: "blur" }],
  mrClassCode: [{ required: true, message: "请选择标准文书类型", trigger: "blur" }],
  url: [{ required: true, message: "请输入URL", trigger: "blur" }],
  method: [{ required: true, message: "请选择请求方式", trigger: "blur" }],
  // queryParams: [{ required: true, message: "输入请求参数", trigger: "blur" }],
  // bodyParams: [{ required: true, message: "输入请求体", trigger: "blur" }],
  contentType: [{ required: true, message: "输选择content-type", trigger: "blur" }],
  resultType: [{ required: true, message: "请选择响应体类型", trigger: "blur" }],
  resultField: [{ required: true, message: "请输入返回数据取值", trigger: "blur" }],
  resultDataType: [{ required: true, message: "请选择返回数据类型", trigger: "blur" }],
  charset: [{ required: true, message: "请选择编码格式", trigger: "blur" }],
  extractType: [{ required: true, message: "请选择采集模式", trigger: "blur" }],
  incrementFieldKey: [{ required: true, message: "请输入自增key", trigger: "blur" }],
  incrementFieldType: [{ required: true, message: "请选择自增key类型", trigger: "blur" }],
  incrementLastValue: [{ required: true, message: "请输入/选择最近自增值", trigger: "blur" }],
  incrementDelta: [{ required: true, message: "请输入增幅", trigger: "blur" }],
  cron: [{ required: true, message: "请输入cron", trigger: "blur" }]
}

/* ================= 接收 ================ */

export const receptionTableColumns: Array<TableColumnItem> = [
  { prop: "systemName", label: "厂商系统", minWidth: 120 },
  { prop: "dataType", label: "数据类型", minWidth: 120 },
  { prop: "remark", label: "配置描述", minWidth: 100 },
  { prop: "operation", label: "操作", width: 200, fixed: "right" }
]

export const receptionFormRules: FormRules = {
  systemWsid: [{ required: true, message: "请选择厂商系统", trigger: "blur" }],
  dataType: [{ required: true, message: "请选择数据类型", trigger: "blur" }]
}

// 数据类型选项
export const receptionDataTypeOptions = [
  { label: "患者", value: "PATIENT" },
  { label: "文书", value: "DOCUMENT" }
]

/* ================= 其他 ================ */

export const myTheme = EditorView.theme(
  {
    // 输入的字体颜色
    "&": {
      color: "#0052D9",
      backgroundColor: "#FFFFFF"
    },
    ".cm-content": {
      caretColor: "#0052D9"
    },
    // 激活背景色
    ".cm-activeLine": {
      backgroundColor: "#FFFFDD"
    },
    // 激活序列的背景色
    ".cm-activeLineGutter": {
      backgroundColor: "#FAFAFA"
    },
    //光标的颜色
    "&.cm-focused .cm-cursor": {
      borderLeftColor: "#0052D9"
    },
    // 选中的状态
    "&.cm-focused .cm-selectionBackground, ::selection": {
      backgroundColor: "#0052D9",
      color: "#dcdfe6"
    },
    // 左侧侧边栏的颜色
    ".cm-gutters": {
      backgroundColor: "#FFFFFF",
      color: "#ddd", //侧边栏文字颜色
      border: "none"
    }
  },
  { dark: true }
)
