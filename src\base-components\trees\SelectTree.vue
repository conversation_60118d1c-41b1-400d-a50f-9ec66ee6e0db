<template>
  <div class="select-tree">
    <el-tree
      ref="elTreeRef"
      :data="props.treeData"
      node-key="treeId"
      :props="defaultProps"
      :highlight-current="true"
      :show-checkbox="true"
      :default-checked-keys="props.defaultCheckedKeys"
      v-bind="$attrs"
    >
      <template #default="{ node, data }">
        <div class="custom-tree-node">
          <span style="width: 150px" class="common-text-ellipsis">{{ data.label }}</span>
        </div>
      </template>
    </el-tree>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue"
export interface ISelectTreeProps {
  treeData?: any[]
  defaultCheckedKeys?: any[]
}

const props = withDefaults(defineProps<ISelectTreeProps>(), {
  treeData: () => [],
  defaultCheckedKeys: () => []
})

const elTreeRef = ref()

const defaultProps = {
  children: "children",
  label: "label",
  isLeaf: "leaf"
}
//暴露
const getCheckedKeys = (flag = false) => {
  return elTreeRef.value?.getCheckedKeys(flag)
}
defineExpose({ getCheckedKeys })
</script>

<style lang="less" scoped>
.select-tree {
  :deep(.el-tree) {
    font-size: 12px;
    font-family: "PingFang SC-Medium", "PingFang SC";
    color: rgb(10 22 51 / 85%);
    background-color: transparent;
    font-weight: 500;

    .el-tree-node__content > .el-checkbox {
      position: absolute !important;
      right: 0 !important;
      margin-right: 0 !important;
    }
  }
  :deep(.el-tree-node__children) {
    .not-checkout {
      .el-checkbox {
        display: none;
      }
    }
  }
}
</style>
