<template>
  <PageContainer separate>
    <!-- 头部搜索 -->
    <template #search>
      <TabsRouter :tabs-config="medicalQcTabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
        />

        <VisitIdFormItem
          v-model:input-value="searchFormState.visitIdCount"
          v-model:select-value="searchFormState.visitIdSymbol"
          label="住院次数"
        />

        <CountRangeFormItem
          v-model:from="searchFormState.inHospitalDaysFrom"
          v-model:to="searchFormState.inHospitalDaysTo"
          :min="0"
          label="住院天数"
          unit="天"
        />

        <el-form-item label="主治医师">
          <el-input v-model="searchFormState.doctorName" />
        </el-form-item>

        <DaterangeFormItem v-model:model-value="searchFormState.outHospitalDatetime" label="出院时间" />

        <PatientLabelFormItem v-model="searchFormState.patientLabel" />
      </SearchContainer>
    </template>

    <!-- 待质控列表 -->
    <template #table>
      <CommonTable
        table-id="waitingQualityControlTableIdent"
        :table-columns="waitingQualityControlColumns"
        :request-api="getAllWaitingDeptQcList"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <template #patientName="{ row }">
          <div style="display: flex; align-items: center; flex-wrap: wrap">
            <span style="margin: 0 16px">{{ row.patientName }}</span>
            <PatientLabelTag :row="row" />
          </div>
        </template>
        <template #status="{ row }">
          <el-button
            :color="globalOptionsStore.medicalRecordStatusOptions.find(item => row.status === item.value)?.color"
            style="color: #fff"
          >
            {{ globalOptionsStore.medicalRecordStatusOptions.find(item => row.status === item.value)?.label }}
          </el-button>
        </template>

        <template #operation="{ row }">
          <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.QC)" @click="qualityControlItem(row)">
            质控
          </TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>

  <!-- 封存病案密钥 -->
  <SealDialog ref="sealDialogRef" :selected-row="medicalRecord" :confirm-callback="confirmOperation" />
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import { useRouter } from "vue-router"
import { cloneDeep } from "lodash-es"

import {
  SearchContainer,
  CommonTable,
  TableButton,
  PatientFilterFormItem,
  DaterangeFormItem,
  VisitIdFormItem,
  PageContainer,
  CountRangeFormItem,
  PatientLabelFormItem,
  PatientLabelTag
} from "@/base-components"
import { TabsRouter, SealDialog } from "@/page-components"
import { waitingQualityControlColumns, medicalQcTabsRouterList, menuId } from "../config"
import { getAllWaitingDeptQcList } from "../interface"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { useUserStore, useGlobalOptionsStore } from "@/stores"
import { formatDatetime, encryptStr } from "@/utils"

const { hasOperationPermission } = useUserStore()
const globalOptionsStore = useGlobalOptionsStore()

const router = useRouter()

/* ======================== 搜索相关数据及方法 ======================== */
const initFormData = {
  patientFilterValue: "",
  patientFilterProp: "",
  outHospitalDeptWsid: "",
  outHospitalDatetime: "",
  visitIdSymbol: "",
  visitIdCount: "",
  doctorName: "",
  inHospitalDaysFrom: "",
  inHospitalDaysTo: "",
  death: "",
  patientLabel: "" //患者标签
}

const searchFormState = reactive(cloneDeep(initFormData))

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState, "")

/* ======================== 表格相关方法 ======================== */
const medicalRecord = ref<Record<string, any> | null>(null) // 用户点击的病案
const sealDialogRef = ref() // SealDialog组件ref

function dataCallback(data: Array<Record<string, any>>) {
  data.forEach(item => {
    item.outHospitalDatetime = formatDatetime(item.outHospitalDatetime)
    item.inHospitalDatetime = formatDatetime(item.inHospitalDatetime)
    item.submitterTime = formatDatetime(item.submitterTime)
  })
  return data
}

// 质控
const qualityControlItem = row => {
  medicalRecord.value = row
  if (sealDialogRef.value) sealDialogRef.value.checkSealed()
}

// 检查封存之后的回调
const confirmOperation = () => {
  const query = {
    inpNo: medicalRecord.value?.inpNo,
    wsid: medicalRecord.value?.wsid,
    isControl: "true",
    urlType: medicalRecord.value?.qcType,
    returnUrl: "/department-quality-control/medical-qc/waiting-control"
  }
  if (sealDialogRef.value.secretKey) {
    query["secretKey"] = encryptStr(sealDialogRef.value.secretKey)
  }
  router.push({
    path: "/department-quality-control/medical-qc/view",
    query: query
  })
}
</script>

<style lang="less" scoped>
.in-hospital-days-input {
  width: 70px;
}
:deep(.el-input) {
  input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
  }
}

.distribute-table {
  margin-top: 10px;
  height: 320px;
}
</style>
