import type { TableColumnItem, BaseOptionItem } from "@/types"

export const menuId = "/print/distribution"

export const distributionColumns: Array<TableColumnItem> = [
  { type: "selection", width: 45, fixed: "left" },
  { prop: "orderCode", label: "订单单号", minWidth: 200, must: true },
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 120 },
  { prop: "patientIdCard", label: "身份证号", minWidth: 200 },
  { prop: "patientOutHospitalDatetime", label: "出院日期", minWidth: 180, sortable: true },
  { prop: "printTypeName", label: "打印类型", minWidth: 120 },
  { prop: "duplicateNum", label: "复印份数", minWidth: 120, sortable: true },
  { prop: "pageCount", label: "总页数", minWidth: 100, sortable: true },
  { prop: "createdDatetime", label: "申请时间", minWidth: 180, sortable: true },
  { prop: "name", label: "申请人", minWidth: 100, must: true },
  { prop: "phone", label: "申请人手机", minWidth: 150 },
  { prop: "packageStatusStr", label: "状态", minWidth: 100, must: true },
  { prop: "operation", label: "操作", width: 80, fixed: "right", must: true }
]

export const distributionProcessColumns: Array<TableColumnItem> = [
  { prop: "fileName", label: "文件名", minWidth: 120 },
  { prop: "fileTotalPage", label: "总页数", minWidth: 100, sortable: true },
  { prop: "statusStr", label: "状态", minWidth: 100 },
  { prop: "packTime", label: "发送时间", minWidth: 180, sortable: true },
  { prop: "log", label: "发送日志", minWidth: 150 }
]

export const statusOptions: Array<BaseOptionItem> = [
  { label: "待处理", value: "0" },
  { label: "发送中", value: "1" },
  { label: "发送失败", value: "2" },
  { label: "已完成", value: "3" }
]
