<template>
  <div
    class="table-item"
    :style="{
      border:
        props.index === props.activeTableConfigIndex && props.activeTableConfig.prop === props.tableConfig.prop
          ? '1px solid #409eff'
          : '1px dashed transparent',
      backgroundColor: isMouseOver ? 'rgba(56,96,244,0.08)' : '',
      width: tableConfig.tableArrangement === 'list' ? `${Math.floor(100 / tableConfig.column) - 1}%` : '',
      flex: tableConfig.tableArrangement === 'list' ? 'auto' : 1
    }"
    @mouseover="handleMouseOver"
    @mouseleave="handleMouseLeave"
  >
    <div class="table-header">
      {{ props.config.name }}

      <el-tooltip v-if="props.config.desc" placement="top" :content="props.config.desc">
        <el-icon><QuestionFilled /></el-icon>
      </el-tooltip>
    </div>
    <div class="table-body">
      <el-input
        v-if="props.config.type === FormItemType.BaseInput"
        size="large"
        :value="props.config.default"
        readonly
        :placeholder="props.config.placeholder ? props.config.placeholder : `请输入${props.config.name}`"
      />

      <el-input
        v-if="props.config.type === FormItemType.BloodDonationBarcode"
        size="large"
        :value="props.config.default"
        readonly
        :placeholder="props.config.placeholder ? props.config.placeholder : `请输入${props.config.name}`"
      />

      <!-- 数字输入框 -->

      <NumberInput
        v-if="props.config.type === FormItemType.BaseNumber"
        size="large"
        readonly
        :value="props.config.default"
        :placeholder="props.config.placeholder ? props.config.placeholder : `请输入${props.config.name}`"
        :unit="props.config.unit"
        :precision="props.config?.allowDecimal ? props.config.precision : undefined"
      >
        <!-- <span v-if="props.config.unit && props.config.unit?.length > 0">{{ props.config.unit }}</span> -->
      </NumberInput>

      <!-- 多行文本域 -->
      <el-input
        v-if="props.config.type === FormItemType.BaseTextarea"
        :value="props.config.default"
        readonly
        size="large"
        type="textarea"
        :placeholder="props.config.placeholder ? props.config.placeholder : `请输入${props.config.name}`"
      />

      <!-- 说明文本 -->

      <div v-if="props.config.type === FormItemType.DescText" class="output" v-html="props.config.content"></div>

      <!-- 日期选择 -->
      <el-date-picker
        v-else-if="props.config.type === FormItemType.BaseDate"
        :model-value="defaultDate"
        readonly
        size="large"
        :type="getDateType(props.config.dateFormat)"
        :format="props.config.dateFormat"
        value-format="x"
        :placeholder="props.config.placeholder ? props.config.placeholder : `请选择${props.config.name}`"
      />

      <!-- 单选框 -->
      <el-radio-group
        v-else-if="[FormItemType.BaseRadio].includes(props.config.type)"
        size="large"
        :model-value="props.config.default"
        :style="choiceFormItemStyle"
      >
        <el-radio v-for="option in props.config.options" :key="option.value" :label="option.value">
          {{ option.label }}
        </el-radio>
      </el-radio-group>

      <!-- 下拉选择 -->
      <div v-else-if="[FormItemType.BaseSelect].includes(props.config.type)" class="select-output">
        <div>{{ selectValue }}</div>
        &nbsp;
        <i class="ri-arrow-down-s-line"></i>
      </div>

      <!-- 多选框 -->
      <el-checkbox-group
        v-else-if="props.config.type === FormItemType.BaseCheckbox"
        size="large"
        :style="choiceFormItemStyle"
        :value="[]"
        :model-value="props.config.default"
      >
        <el-checkbox v-for="option in props.config.options" :key="option.value" :label="option.label"></el-checkbox>
      </el-checkbox-group>

      <!-- 用户签名 -->
      <div
        v-else-if="props.config.type === FormItemType.UserSign"
        :style="{
          width: '100%',
          height: '100%',
          backgroundColor: '#f8f9fc',
          color: '#606266',
          fontSize: '20px',
          padding: '20px 0px',
          textAlign: 'center'
        }"
      >
        {{ props.config.name || "献血者签名" }}
      </div>

      <!-- 知情同意书签名 -->
      <div
        v-else-if="props.config.type === FormItemType.InformedConsentFormSign"
        :style="{
          width: '100%',
          height: '100%',
          backgroundColor: '#f8f9fc',
          color: '#606266',
          fontSize: '20px',
          padding: '20px 0px',
          textAlign: 'center'
        }"
      >
        {{ props.config.name || "知情同意书签名" }}
      </div>

      <!-- 工作人员签名 -->
      <div
        v-else-if="props.config.type === FormItemType.StaffSign"
        :style="{
          width: '100%',
          height: '100%',
          backgroundColor: '#f8f9fc',
          color: '#606266',
          fontSize: '20px',
          padding: '20px 0px',
          textAlign: 'center'
        }"
      >
        工作人员签名
      </div>

      <!-- 地址 -->
      <div
        v-if="props.config.type === FormItemType.BaseAddress"
        :style="{
          width: '100%',
          height: '100%'
        }"
      >
        <div class="select-output">
          <div>{{ props.config.addressSelect?.join("/") }}</div>
          &nbsp;
          <i class="ri-arrow-down-s-line"></i>
        </div>

        <el-input
          :value="props.config.default"
          readonly
          size="large"
          type="textarea"
          :placeholder="props.config.placeholder ? props.config.placeholder : `请输入${props.config.name}`"
        />
      </div>
    </div>
    <!-- 编辑&删除按钮 -->
    <div v-if="isMouseOver" class="operation-buttons" @click.stop>
      <el-tooltip effect="dark" content="编辑" placement="top">
        <i
          v-if="config.type !== FormItemType.IdCardGroup && config.type !== FormItemType.BloodDonationBarcode"
          class="ri-edit-line operation-icon"
          @click.stop="editCard(config)"
        ></i>
      </el-tooltip>

      <el-tooltip effect="dark" content="复制" placement="top">
        <i
          v-if="config.type !== FormItemType.IdCardGroup && config.type !== FormItemType.BloodDonationBarcode"
          class="ri-file-copy-2-line operation-icon"
          @click.stop="copyCard(config)"
        ></i>
      </el-tooltip>

      <el-tooltip effect="dark" content="删除" placement="top">
        <i class="ri-close-line operation-icon" @click.stop="deleteCard(config.prop)"></i>
      </el-tooltip>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from "vue"
import { NumberInput } from "@/base-components"
import { FormItemType, FormItemConfig } from "@/configs"
import { formatDatetime, getDateType } from "@/utils"
// interface PropsType {
//   config: FormItemConfig
// }
// const props = withDefaults(defineProps<PropsType>(), {})

const props = defineProps<{
  prop: string
  config: FormItemConfig
  index: number
  activeTableConfigIndex: number
  tableConfig: FormItemConfig
  activeTableConfig: FormItemConfig
  editCard: (prop: any) => void
  copyCard: (config: FormItemConfig) => void
  deleteCard: (prop: string) => void
}>()

// 单选和多选框样式
const choiceFormItemStyle = computed(() => {
  const arrangement = props.config.arrangement
  if (arrangement === "vertical") {
    return {
      flexDirection: "column",
      alignItems: "flex-start",
      display: "flex",
      overflowX: "hidden"
    }
  }
  return {
    flexDirection: "row",
    alignItems: "center",
    display: "flex",
    overflowX: "hidden"
  }
})

// 日期的默认值
const defaultDate = computed(() => {
  let datetime = "" as string | Date | number
  if (props.config.default === "custom") {
    datetime = props.config.customValue
  } else {
    datetime = new Date().getTime()
  }
  // 将日期按照props.config.format格式化
  return formatDatetime(datetime as number, props.config.dateFormat)
})

// 下拉值
const selectValue = computed(() => {
  //如果default是数组，说明是多选,需要用,拼接
  if (Array.isArray(props.config.default)) {
    let str = ""
    props.config.default.forEach((item, index) => {
      str += props.config.options?.find(option => option.value === item)?.label
      if (index < props.config.default.length - 1) {
        str += ","
      }
    })
    return str
  }
  return props.config.options?.find(option => option.value === props.config.default)?.label
})

/* ==================== 鼠标悬停处理 ====================== */

const isMouseOver = ref(false)

const handleMouseOver = () => {
  isMouseOver.value = true
}

const handleMouseLeave = () => {
  isMouseOver.value = false
}
</script>

<style lang="less" scoped>
.select-output {
  width: calc(100% - 16px);
  border: 1px solid var(--el-border-color);
  color: #606266;
  font-size: var(--custom-larger-font-size);
  border-radius: 4px;
  font-size: var(--custom-small-font-size);
  padding-left: 4px;
  display: flex;
  height: 38px;
  line-height: 38px;
  padding-right: 12px;
  div {
    display: inline-block;
    width: calc(100% - 20px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.table-item {
  position: relative;
  border: 1px solid #ccc;
  border-right: none;
  height: 100%;
  display: flex;
  align-items: center;
  flex: 1;
  // margin-right: 1px;
  flex-direction: column;

  &:last-child {
    border-right: 1px solid #ccc;
  }

  &:hover {
    border: 1px solid #3860f4;
  }

  .table-header {
    background: #f5f7fa;
    box-shadow: inset 0px -1px 0px 0px #ebeef5;
    padding: 0 8px;
    width: calc(100% - 16px);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .table-body {
    padding: 8px;
    width: calc(100% - 16px);
  }
  .operation-buttons {
    position: absolute;
    right: 0;
    top: -10px;
    display: flex;
    .operation-icon {
      color: #aaa;
      cursor: pointer;
      margin-left: 6px;
      background: #fff;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0px 4px 8px 0px rgba(51, 51, 51, 0.15);
      border-radius: 4px;
    }
  }
}
:deep(.el-textarea__inner) {
  height: 40px;
}

:deep(.el-radio-group) {
  flex-wrap: nowrap !important;
}
</style>
