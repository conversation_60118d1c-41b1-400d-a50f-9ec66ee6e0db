import { sessionLessAxios } from "@/utils"

export const getDocumentBagsInfoApi = (inpNo: string) => {
  return sessionLessAxios({
    method: "get",
    url: `/api/document/document-bags/view/${inpNo}`
  })
}

// 获取病案袋分类
export const getDocumentBagsTypeApi = (idCard: string) => {
  return sessionLessAxios({
    method: "get",
    url: `/api/document/document-bags/type/${idCard}`
  })
}

// 根据档案类型和档案号查询病案袋信息
export const getDocumentBagsInfoByTypeApi = (bagNum: string, bagType: string) => {
  return sessionLessAxios({
    method: "get",
    url: `/api/document/document-bags/info/${bagNum}`,
    params: { bagType: bagType }
  })
}
