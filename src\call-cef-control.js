// 调用CEF控件注册的相关方法

export const call_OnLoginSucceeded = (username, jobid, deptname) => {
  // 调用一个 C++ 注册过的方法
  let params = {
    realName: username,
    jobID: jobid,
    deptName: deptname
  }
  console.log("Call OnLoginSucceeded:", params)
  NimCefWebInstance.call("OnLoginSucceeded", params, (error, result) => {
    console.log(result)
  })
}
export const call_GetApplicationConfig = message => {
  // 创建一个 Promise 对象
  return new Promise((resolve, reject) => {
    // 调用一个 C++ 注册过的方法
    NimCefWebInstance.call("GetApplicationConfig", { message }, (error, result) => {
      if (error) {
        // 如果有错误，通过 reject 返回错误信息
        reject(error)
      } else {
        // 如果成功，通过 resolve 返回结果
        resolve(result)
      }
    })
  })
}
// export const call_GetApplicaitonConfig = message => {
//   // 调用一个 C++ 注册过的方法
//   NimCefWebInstance.call("GetApplicationConfig", { message }, (error, result) => {
//     console.log(result)
//   })
// }

export const call_OnLogout = message => {
  // 调用一个 C++ 注册过的方法
  NimCefWebInstance.call("OnLogout", { message }, (error, result) => {
    console.log(result)
  })
}
