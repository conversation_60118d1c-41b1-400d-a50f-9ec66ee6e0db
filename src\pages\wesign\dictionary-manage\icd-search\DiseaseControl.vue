<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <IcdSearch ref="icdSearchRef" v-model:filters="requestParams.filters" :icd-type="ICDTypeEnum.ICD_BZ_DM" />
    </template>

    <template #table>
      <CommonTable
        ref="commonTableRef"
        :table-columns="diseaseControlTableColumns"
        :request-api="searchIcdDzApi"
        :request-params="requestParams"
      >
        <template #header>
          <AddButton @click="handleAdd">新增</AddButton>
        </template>
        <template #operation="{ row }">
          <TableButton @click="handleEdit(row)">编辑</TableButton>
          <TableButton @click="handleDelete(row)">删除</TableButton>
        </template>
      </CommonTable>
    </template>

    <DialogContainer
      v-model:visible="dialogVisible"
      :confirm-callback="handleConfirm"
      :cancel-callback="closeDialog"
      :close-callback="closeDialog"
      :title="isEdit ? '编辑' : '新增'"
    >
      <el-form ref="ruleFormRef" :label-width="100" label-suffix="：" :model="dialogForm">
        <el-form-item label="国临版">
          <el-select
            v-model="dialogForm.version"
            placeholder="请选择"
            style="width: 240px"
            value-key="code"
            clearable
            popper-class="icd-select-container"
          >
            <template #header>
              <div>
                <el-input
                  v-model="codeState.searchValue"
                  style="z-index: 10000"
                  placeholder="请选择编码/名称"
                  :prefix-icon="Search"
                  clearable
                  @input="handleSearchCode"
                ></el-input>
                <div class="select-header">
                  <span>编码</span>
                  <span>名称</span>
                </div>
              </div>
            </template>
            <div v-infinite-scroll="scrollRenderCode" class="item-content">
              <el-option v-for="item in codeState.renderOptions" :key="item.code" :label="item.name" :value="item">
                <span class="item-code">
                  {{ item.code }}
                </span>
                <span class="item-name">
                  {{ item.name }}
                </span>
              </el-option>
            </div>
          </el-select>
        </el-form-item>
        <el-form-item label="医保版">
          <el-select
            v-model="dialogForm.contrast"
            placeholder="请选择"
            style="width: 240px"
            popper-class="icd-select-container"
            value-key="code"
            clearable
          >
            <template #header>
              <div>
                <el-input
                  v-model="contrastCodeState.searchValue"
                  placeholder="请选择编码/名称"
                  :prefix-icon="Search"
                  clearable
                  @input="handleSearchContrastCode"
                ></el-input>
                <div class="select-header">
                  <span>编码</span>
                  <span>名称</span>
                </div>
              </div>
            </template>
            <div v-infinite-scroll="scrollRenderContrastCode" class="item-content">
              <el-option
                v-for="item in contrastCodeState.renderOptions"
                :key="item.code"
                :label="item.name"
                :value="item"
              >
                <span class="item-code">
                  {{ item.code }}
                </span>
                <span class="item-name">
                  {{ item.name }}
                </span>
              </el-option>
            </div>
          </el-select>
        </el-form-item>
      </el-form>
    </DialogContainer>
  </PageContainer>
</template>

<script lang="ts" setup>
import { reactive, ref, watch, onMounted } from "vue"
import { debounce } from "lodash-es"
import { Search } from "@element-plus/icons-vue"
import { PageContainer, AddButton, TableButton, CommonTable } from "@/base-components"
import { TabsRouter } from "@/page-components"
import DialogContainer from "@/base-components/containers/DialogContainer.vue"
import { getIcdListApi, getIcdLevelConfigApi } from "@/interfaces"
import { Message, toastError, SystemPrompt, SystemAlert } from "@/utils/message-tool"
import IcdSearch from "./components/IcdSearch.vue"
import { tabsRouterList, diseaseControlTableColumns, ICDTypeEnum } from "./config"
import { addIcdDzApi, searchIcdDzApi, editIcdDzApi, deleteIcdDzApi } from "./interface"
import type { FormInstance } from "element-plus"

const requestParams = reactive({ filters: `icdVersionId=9` })

const dialogVisible = ref(false)
const isEdit = ref(false)
const dialogForm = reactive({
  id: -1,
  version: {
    code: "",
    name: ""
  },
  contrast: {
    code: "",
    name: ""
  }
})

const ruleFormRef = ref<FormInstance>()

const icdSearchRef = ref<InstanceType<typeof IcdSearch>>()
const commonTableRef = ref<InstanceType<typeof CommonTable>>()

const isSubmitting = ref(false)

/* ======================== 首次加载获取版本选项 ======================== */

interface IcdVersionOptionItem {
  id: number
  name: string
}

/*=============================通过 type 获取 icdVersionId================*/
const icdVersionId = ref(0)

const icdVersionOptions = ref<Array<IcdVersionOptionItem>>([])

onMounted(() => {
  getIcdLevelConfigApi({ type: ICDTypeEnum.ICD_BZ_DM }).then(res => {
    icdVersionId.value = res.data.data?.[0]?.id
  })
  getIcdLevelConfigApi({ type: ICDTypeEnum.ICD_10 }).then(res => {
    const options = res.data?.data ?? []
    icdVersionOptions.value = options
  })
})
/*==================国临版搜索下拉数据源==================*/

const codeState = reactive({
  searchValue: "",
  renderOptions: [] as Array<any>,
  totalSize: 0
})
// 搜索国临版
const scrollRenderCode = async (isSearchValueChange = false) => {
  const SCROLL_SIZE = 20
  if (isSearchValueChange || codeState.renderOptions?.length < codeState.totalSize) {
    let filters = `icdVersionId=${icdVersionOptions.value?.[0].id}`
    if (codeState.searchValue) {
      filters += `,keyword=${codeState.searchValue}`
    }
    const params = {
      filters: filters,
      limit: SCROLL_SIZE,
      offset: codeState.renderOptions.length
    }
    await getIcdListApi(params).then(res => {
      codeState.renderOptions = codeState.renderOptions.concat(res.data?.data?.rows)
      codeState.totalSize = res.data?.data?.page?.totalElements
    })
    const isHasSelectedCode = codeState.renderOptions.find(item => item.code === dialogForm.version.code)
    if (!isHasSelectedCode) {
      getIcdListApi({
        filters: `icdVersionId=${icdVersionOptions.value?.[0].id},code=${dialogForm.version.code}`
      }).then(res => {
        if (res.data.data.rows.length > 0) {
          // 将这些数据放在最前面，有可能搜出来的不止一个，可能有几个
          codeState.renderOptions = [...res.data.data.rows, ...codeState.renderOptions]
          // 然后去掉id重复的数据
          codeState.renderOptions = codeState.renderOptions.filter((item, index, arr) => {
            return arr.findIndex(item2 => item2.id === item.id) === index
          })
        }
      })
    }
  }
}

const handleSearchCode = debounce(() => {
  codeState.renderOptions = []
  codeState.totalSize = 0
  scrollRenderCode(true)
}, 300)

/*==================医保版搜索下拉数据源==================*/
const contrastCodeState = reactive({
  searchValue: "",
  renderOptions: [] as Array<any>,
  totalSize: 0
})

// 医保版搜索下拉数据源
const scrollRenderContrastCode = async (isSearchValueChange = false) => {
  const SCROLL_SIZE = 20
  if (isSearchValueChange || contrastCodeState.renderOptions?.length < contrastCodeState.totalSize) {
    let filters = `icdVersionId=${icdVersionOptions.value?.[1].id}`
    if (contrastCodeState.searchValue) {
      filters += `,keyword=${contrastCodeState.searchValue}`
    }

    const params = {
      filters: filters,
      limit: SCROLL_SIZE,
      offset: contrastCodeState.renderOptions.length
    }
    await getIcdListApi(params).then(res => {
      contrastCodeState.renderOptions = contrastCodeState.renderOptions.concat(res.data?.data?.rows)
      contrastCodeState.totalSize = res.data?.data?.page?.totalElements
    })
    const isHasSelectedCode = contrastCodeState.renderOptions.find(item => item.code === dialogForm.contrast.code)

    if (!isHasSelectedCode) {
      getIcdListApi({
        filters: `icdVersionId=${icdVersionOptions.value?.[1].id},code=${dialogForm.contrast.code}`
      }).then(res => {
        if (res.data.data.rows.length > 0) {
          // 将这些数据放在最前面，有可能搜出来的不止一个，可能有几个
          contrastCodeState.renderOptions = [...res.data.data.rows, ...contrastCodeState.renderOptions]
          // 然后去掉id重复的数据
          contrastCodeState.renderOptions = contrastCodeState.renderOptions.filter((item, index, arr) => {
            return arr.findIndex(item2 => item2.id === item.id) === index
          })
        }
      })
    }
  }
}

// 搜索值改变，重新搜索
const handleSearchContrastCode = debounce(() => {
  contrastCodeState.renderOptions = []
  contrastCodeState.totalSize = 0
  scrollRenderContrastCode(true)
}, 300)

// 打开弹窗
function openDialog() {
  dialogVisible.value = true
  scrollRenderCode(true)
  scrollRenderContrastCode(true)
}

function closeDialog() {
  dialogVisible.value = false
  codeState.searchValue = ""
  contrastCodeState.searchValue = ""
  codeState.renderOptions = []
  contrastCodeState.renderOptions = []
  codeState.totalSize = 0
  contrastCodeState.totalSize = 0
}

// 新增
function handleAdd() {
  isEdit.value = false
  dialogForm.version.code = ""
  dialogForm.version.name = ""
  dialogForm.contrast.code = ""
  dialogForm.contrast.name = ""
  dialogForm.id = -1
  openDialog()
}

// 编辑
function handleEdit(row) {
  isEdit.value = true
  dialogForm.version.code = row.code
  dialogForm.version.name = row.name
  dialogForm.contrast.code = row.contrastCode
  dialogForm.contrast.name = row.contrastName
  dialogForm.id = row.id
  openDialog()
}

// 删除
function handleDelete(row) {
  SystemPrompt("确定删除该条记录吗？", "warning").then(() => {
    if (!icdVersionId.value) return SystemAlert("请先选择版本", "warning")
    deleteIcdDzApi({ id: row.id, icdVersionId: icdVersionId.value })
      .then(() => {
        Message.success("删除成功")
        commonTableRef.value?.refreshTableData()
      })
      .catch(err => toastError(err))
  })
}

// 确认
function handleConfirm() {
  if (!icdVersionId.value) return SystemAlert("请先选择版本", "warning")

  ruleFormRef.value?.validate(valid => {
    if (!valid) return
    isSubmitting.value = true
    const requestApi = isEdit.value ? editIcdDzApi : addIcdDzApi
    const params = {
      code: dialogForm.version.code,
      name: dialogForm.version.name,
      contrastCode: dialogForm.contrast.code,
      contrastName: dialogForm.contrast.name,
      icdVersionId: icdVersionId.value,
      ...(isEdit.value ? { id: dialogForm.id } : {})
    }
    requestApi(params as any)
      .then(() => {
        if (isEdit.value) {
          Message.success("修改成功")
        } else {
          Message.success("添加成功")
        }
        commonTableRef.value?.refreshTableData()
        dialogVisible.value = false
        closeDialog()
      })
      .catch(err => toastError(err))
      .finally(() => {
        isSubmitting.value = false
      })
  })
}
</script>

<style lang="less">
.icd-select-container {
  .el-select-dropdown__header {
    border-bottom: none;
    padding: 0;
    margin: 10px 20px 0 20px;
  }
  .el-select-dropdown__list {
    margin: 0 !important;
  }
  .el-select-dropdown__item {
    padding: 0;
    margin: 0 20px;
    border: 1px solid #dcdfe6;
    border-bottom: none;
    &:not(:last-child) {
      border-bottom: none;
    }
  }
  .select-header {
    font-size: 14px;
    margin-top: 10px;
    display: flex;
    justify-content: space-around;
    border: 1px solid #dcdfe6;
    border-bottom: none;
    background-color: #f5f7fa;
    span {
      display: inline-block;
      padding: 6px 10px;
      width: 120px;
      &:first-child {
        border-right: 1px solid #dcdfe6;
        width: 103px;
      }
    }
  }
  .item-content {
    max-height: 300px;
    padding-bottom: 10px;
    li {
      &:last-child {
        border-bottom: 1px solid #dcdfe6;
      }
    }
    .item-code {
      width: 120px;
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      border-right: 1px solid #dcdfe6;
      padding-left: 4px;
    }
    .item-name {
      width: 140px;
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      padding-left: 4px;
    }
  }
}
</style>
