<template>
  <PageContainer>
    <TabsRouter :tabs-config="tabsRouterList" />
    <div v-loading="state.loading" class="login-config">
      <div class="config-item">
        <el-form label-position="left">
          <el-form-item label="水印内容：">
            <el-checkbox-group v-model="formData.watermarkContent">
              <el-checkbox label="IP地址"></el-checkbox>
              <el-checkbox label="系统当前时间"></el-checkbox>
              <el-checkbox label="用户名"></el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="水印颜色：">
            <el-color-picker v-model="formData.watermarkColor" color-format="rgb" show-alpha></el-color-picker>
          </el-form-item>

          <el-form-item label="水印大小：">
            <el-slider v-model="formData.watermarkSize" :min="12" :max="48" :marks="sizeMarks" />
          </el-form-item>

          <el-form-item label="水印间距：">
            <el-slider v-model="formData.watermarkSpacing" :min="50" :max="300" :marks="spacingMarks" />
          </el-form-item>

          <el-form-item label="水印不透明度：">
            <el-slider
              v-model="formData.watermarkTransparency"
              :step="0.1"
              :min="0.1"
              :max="1"
              :marks="transparencyMarks"
            />
          </el-form-item>

          <el-form-item label="水印方向：">
            <ul class="direction-container">
              <li :class="formData.watermarkDirection === '45' ? 'active' : ''" @click.prevent="selectDirection('45')">
                <span class="direction-45">ABC</span>
              </li>
              <li :class="formData.watermarkDirection === '90' ? 'active' : ''" @click.prevent="selectDirection('90')">
                <span class="direction-90">ABC</span>
              </li>
              <li :class="formData.watermarkDirection === '0' ? 'active' : ''" @click.prevent="selectDirection('0')">
                <span class="direction-180">ABC</span>
              </li>
            </ul>
          </el-form-item>
          <el-form-item label=" ">
            <div class="btn-container">
              <el-button type="primary" @click="save">保存</el-button>
              <el-button @click="reset">重置</el-button>
            </div>
          </el-form-item>
        </el-form>
        <canvas ref="watermarkRef" class="watermark-preview-root" :width="240" :height="320"></canvas>
      </div>
    </div>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, onMounted, watch, ref } from "vue"
import { toNumber } from "lodash-es"
import { PageContainer } from "@/base-components"
import { TabsRouter } from "@/page-components"
import { querySystemConfig, updateSystemConfig } from "@/interfaces"
import { Message } from "@/utils"
import { tabsRouterList } from "./config"
import type { CSSProperties } from "vue"

interface FunctionConfigFormData {
  watermarkContent: []
  watermarkColor: string
  watermarkSize: number
  watermarkSpacing: number
  watermarkTransparency: number
  watermarkDirection: string
}

const formData = reactive<FunctionConfigFormData>({
  watermarkContent: [] as any,
  watermarkColor: "",
  watermarkSize: 0,
  watermarkSpacing: 0,
  watermarkTransparency: 0,
  watermarkDirection: ""
})

interface State {
  loading: boolean
  data: Array<{ key: string; value: any }>
}

const state = reactive<State>({
  loading: false,
  data: []
})

// 滑块刻度
interface Mark {
  style: CSSProperties
  label: string
}

type Marks = Record<number, Mark | string>

const sizeMarks = reactive<Marks>({
  12: "小",
  30: "中",
  48: "大"
})

const spacingMarks = reactive<Marks>({
  50: "小",
  175: "中",
  300: "大"
})

const transparencyMarks = reactive<Marks>({
  0.1: "小",
  0.5: "中",
  1: "大"
})

onMounted(() => {
  querySystemConfig({ type: "WATERMARK" }).then(res => {
    res.data.data.forEach(item => {
      if (item.value) {
        formData[item.key] = item.value
        if (formData["watermarkColor"].indexOf("rgb") === -1) {
          formData["watermarkColor"] = "rgb(" + formData["watermarkColor"] + ")"
        }
        if (item.key === "watermarkSize" || item.key === "watermarkSpacing" || item.key === "watermarkTransparency") {
          formData[item.key] = Number(item.value)
        }
      }
    })
    // 转换为checkbox的label
    formData.watermarkContent = formData.watermarkContent
      .split(",")
      .map(value => {
        return transformValueToLabel(value)
      })
      .filter(value => value)
  })
})

const transformLabelToValue = (label: string) => {
  switch (label) {
    case "用户名":
      return "USER_NAME"
    case "IP地址":
      return "IP"
    case "系统当前时间":
      return "SYSTEM_TIME"
    default:
      return ""
  }
}

const transformValueToLabel = (value: string) => {
  switch (value) {
    case "USER_NAME":
      return "用户名"
    case "IP":
      return "IP地址"
    case "SYSTEM_TIME":
      return "系统当前时间"
    default:
      return ""
  }
}

const selectDirection = value => {
  formData.watermarkDirection = value
}

const reset = () => {
  for (let key in formData) {
    formData[key] = ""
    formData.watermarkSize = 50
    formData.watermarkSpacing = 500
    formData.watermarkTransparency = 0.5
    state.data.push({ key: key, value: formData[key] })
  }
}

const save = () => {
  if (state.loading) return
  state.loading = true
  for (let key in formData) {
    if (key === "watermarkContent") {
      const value = formData[key].map(label => transformLabelToValue(label)).join(",")
      state.data.push({ key: key, value: value })
    } else if (key === "watermarkColor") {
      state.data.push({ key: key, value: formData[key].slice(5, formData[key].length - 1) })
    } else {
      state.data.push({ key: key, value: formData[key] })
    }
  }

  updateSystemConfig(state.data)
    .then(() => {
      Message.success("保存成功")
      state.loading = false
      state.data = []
    })
    .catch(error => {
      state.loading = false
      if (error.response.errMessage) {
        Message.error(`${error.response.errMessage}`)
      } else {
        Message.error("保存失败")
      }
      state.data = []
    })
}

const watermarkRef = ref()

watch(
  () => formData,
  val => {
    refreshCanvas(val)
  },
  { deep: true, immediate: true }
)

function refreshCanvas(data) {
  if (!watermarkRef.value) return
  const ctx = watermarkRef.value.getContext("2d")
  // 存储原点状态
  ctx.save()
  let drawStringArray: Array<string> = []
  if (data.watermarkContent.indexOf("IP地址") !== -1) drawStringArray.push("IP地址")
  if (data.watermarkContent.indexOf("系统当前时间") !== -1) drawStringArray.push("系统当前时间")
  if (data.watermarkContent.indexOf("用户名") !== -1) drawStringArray.push("用户名")
  const drawString = drawStringArray.join("_")
  const canvas = {
    width: watermarkRef.value.width,
    height: watermarkRef.value.height
  }
  // 绘制之前清空画布
  ctx.clearRect(0, 0, canvas.width, canvas.height)
  // 设置画笔属性
  ctx.font = `${data.watermarkSize}px serif`
  ctx.fillStyle = data.watermarkColor
  ctx.globalAlpha = data.watermarkTransparency
  // 更改画布中心（更改旋转的中心）
  // 平移画布后，左上角不再是0，0, 而是（-canvas.width / 2， -canvas.height / 2）
  ctx.translate(canvas.width / 2, canvas.height / 2)
  // 旋转画布
  // 如果是90度旋转，实际画板宽高交换，左下角是（-canvas.height / 2, -canvas.width/ 2)
  // 这是因为画板的中心点始终是dom的中心没有移动，旋转后导致实际画板和dom的任一边都不重合
  ctx.rotate((-toNumber(data.watermarkDirection) / 180) * Math.PI)
  const stringWidth = ctx.measureText(drawString).width
  const spacing = data.watermarkSpacing
  // 旋转45度初始x往前挪动是为了避免空三角形(就只是为了好看)
  const initX =
    toNumber(data.watermarkDirection) === 45
      ? -((canvas.width * 3) / 4)
      : toNumber(data.watermarkDirection) === 90
      ? -canvas.height / 2
      : -(canvas.width / 2)
  const initY = toNumber(data.watermarkDirection) === 90 ? -canvas.width / 2 : -canvas.height / 2
  // 下一列的坐标(prevX + 字段长度 + space, prevY + 字号大小 + space)
  for (let i = 0; i < 6; i++) {
    for (let j = 0; j < 20; j++) {
      ctx.fillText(
        drawString,
        initX + i * (stringWidth + spacing),
        initY + data.watermarkSize + j * (data.watermarkSize + spacing)
      )
    }
  }
  ctx.restore()
}
</script>

<style lang="less" scoped>
.login-config {
  position: relative;
  //   overflow-y: scroll;
  height: 100%;

  :deep(.el-slider) {
    width: 50%;
  }
  :deep(.el-input) {
    width: 20%;
  }

  :deep(.el-form) {
    padding-left: 28px;
  }

  :deep(.el-form-item__label) {
    justify-content: flex-end;
    width: 110px;
  }

  :deep(.el-form-item) {
    margin-bottom: 40px;
  }
  :deep(.el-input-group__append) {
    width: 5%;
  }
  .config-item {
    width: 100%;
    // padding: 40px 0;
    padding-top: 40px;
    padding-bottom: 20px;
    position: relative;
    // border-bottom: 1px solid #ebedf0;

    .checkbox-container {
      padding-left: 20px;
      span {
        font-size: 12px;
        color: #7f8997;
      }
    }

    .direction-container {
      display: flex;
      width: 20%;
      justify-content: space-between;
      li {
        height: 60px;
        width: 60px;
        border-radius: 4px;
        border: 1px solid #ebedf0;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        span {
          letter-spacing: 2px;
        }
        .direction-45 {
          transform: rotate(-45deg);
        }

        .direction-90 {
          transform: rotate(-90deg);
        }
      }
    }
  }
  .watermark-preview-root {
    border: 1px solid #eee;
    position: absolute;
    left: 540px;
    right: 0px;
    top: 40px;
    margin: auto;
  }
}

.active {
  border: 1px solid var(--el-color-primary) !important;
}
</style>
