<template>
  <div class="form-item-widget">
    <i :class="$props.icon" class="icon"></i>
    <span class="name">{{ $props.name }}</span>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  icon: string
  type: string
  name: string
}>()
</script>

<style lang="less" scoped>
.form-item-widget {
  cursor: pointer;
  // background-color: rgba(200, 200, 200, 0.1);
  // font-size: 12px;
  // display: flex;
  // align-items: center;
  // column-gap: 12px;
  // border-radius: 4px;
  // border: 1px solid #ccc;
  // padding: 4px 8px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #f3f3f3;
  padding: 8px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  // font-size: 14px;
  // color: #333333;

  &:hover {
    border: 1px solid #0c7ffc;
    .icon,
    .name {
      color: #0c7ffc !important;
    }
  }

  .icon {
    color: rgb(190, 200, 211);
    margin-right: 8px;
    font-size: 16px;
  }

  .name {
    flex: 1;
    font-weight: 500;
    font-size: 14px;
    color: #0a1633;
  }
}
</style>
