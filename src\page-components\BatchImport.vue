<template>
  <DialogContainer
    v-model:visible="importState.isVisible"
    v-loading="importState.loading"
    title="批量导入"
    :width="450"
    :confirm-callback="confirmImport"
  >
    <div class="import-wrapper">
      <span class="label">上传文件：</span>
      <div class="upload-container" @click="selectExcel">
        <div class="upload-desc">
          <span class="ri-upload-cloud-line upload-icon"></span>
          <span v-if="importState.file && importState.file.name" class="upload-filename">
            {{ importState.file.name }}
          </span>
          <span v-else>上传文件</span>
        </div>
        <span v-if="importState.file" class="re-upload-desc">点击重新上传</span>
      </div>
    </div>
    <div class="download-template-wrapper">
      <span>请按照模板格式上传，单次不超过10000条，</span>
      <span class="download-template-text" @click="clickImportBtn">下载模板</span>
    </div>
  </DialogContainer>
</template>

<script setup lang="ts">
import { reactive } from "vue"
import { noop } from "lodash-es"
import { DialogContainer } from "@/base-components"
import { Message, selectFile, downloadFile, toastError } from "@/utils"

const props = defineProps({
  downloadTemplateApi: { type: Function, required: true },
  importFileApi: { type: Function, required: true },
  importParams: {
    type: Object,
    default() {
      return {}
    }
  },
  confirmCallback: { type: Function, default: noop }
})

interface IUserImportState {
  isVisible: boolean
  loading: boolean
  file: null | File
}

const importState = reactive<IUserImportState>({
  isVisible: false,
  loading: false,
  file: null
})

// 确认上传
function confirmImport() {
  if (!importState.file) return Message.error("未上传文件")
  importState.loading = true
  props
    .importFileApi({ file: importState.file, ...props.importParams })
    .then(res => {
      Message.success("导入成功")
      importState.isVisible = false
      importState.loading = false
      props.confirmCallback(res.data.data)
    })
    .catch(err => {
      toastError(err, "导入失败")
      importState.loading = false
    })
}

// 选择文件
function selectExcel() {
  selectFile(".xls,.xlsx", 1024 * 1024 * 30)
    .then(file => {
      Message.success("文件上传成功")
      importState.file = file
    })
    .catch(err => {
      if (err.message === "ERROR_FILE_TYPE") {
        Message.error("选择的文件类型错误，请选择正确的文件")
      } else if (err.message === "ERROR_FILE_SIZE") {
        Message.error("文件的大小超过限制")
      }
    })
}

// 下载模板
function clickImportBtn() {
  props.downloadTemplateApi().then(res => {
    Message.success("开始下载模板")
    downloadFile({ fileData: res.data, fileType: "application/vnd.ms-excel", fileName: "下载模板.xlsx" })
  })
}

defineExpose({
  openUserImportDialog: () => {
    importState.isVisible = true
    importState.file = null
  }
})
</script>

<style lang="less" scoped>
.import-wrapper {
  display: flex;
  align-items: center;
  .label {
    width: 75px;
  }
  .upload-container {
    display: flex;
    justify-content: space-between;
    height: 32px;
    font-size: 12px;
    color: rgb(3 8 20 / 44.7%);
    background-color: rgb(250 250 250);
    border: 1px dashed #dcdfe6;
    flex: 1;
    line-height: 32px;
    cursor: pointer;

    .upload-desc {
      display: flex;
      align-items: center;
      margin-left: 12px;

      .upload-icon {
        margin-right: 5px;
        font-size: 18px;
        color: var(--el-color-primary);
      }
      .upload-filename {
        max-width: 180px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .re-upload-desc {
      margin-right: 12px;
    }
  }
}
.download-template-wrapper {
  height: 32px;
  margin-left: 75px;
  font-size: 12px;
  color: #272b4c;
  line-height: 32px;
  .download-template-text {
    cursor: pointer;
    color: var(--el-color-primary);
  }
}
</style>
