import axios from "@/interfaces/axios-instance"

/**
 * @method GET
 * @desc   病案页数报表查询
 */
export function getPaperDetail(params: Record<string, any>) {
  const { offset, limit, filters, queryParams } = params
  return axios({
    method: "get",
    url: `/api/document/statistics/paper/detail`,
    params: {
      offset: offset,
      limit: limit,
      filters: filters,
      ...queryParams
    }
  })
}

/**
 * @method GET
 * @desc   病案页数报表-导出
 */
export function exportPaperDetail(params: Record<string, any>) {
  const { offset, limit, filters, queryParams } = params
  return axios({
    method: "get",
    url: `/api/document/statistics/paper/detail/export`,
    responseType: "arraybuffer",
    params: {
      offset: offset,
      limit: limit,
      filters: filters,
      ...queryParams
    }
  })
}
