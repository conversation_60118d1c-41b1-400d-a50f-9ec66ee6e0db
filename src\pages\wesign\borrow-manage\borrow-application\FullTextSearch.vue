<!-- 全文检索 -->
<template>
  <PageContainer class="full-text-search-container" :separate="haveSearch">
    <!-- 搜索页面 -->
    <div v-if="!haveSearch" class="search-container">
      <TabsRouter :tabs-config="borrowTab" />
      <div class="search-wrapper">
        <div class="search-title">病案全文检索</div>
        <el-input
          v-model="searchText"
          class="search-input"
          size="large"
          placeholder="请输入搜索内容"
          clearable
          :prefix-icon="Search"
          @keydown.enter="search"
        >
          <template #append>
            <TooltipButton
              icon=""
              :tooltip="!searchText ? '请先输入关键词' : ''"
              :disabled="!searchText"
              @click="search"
            >
              搜索
            </TooltipButton>
          </template>
        </el-input>

        <div v-if="userStore.fullTextHistory.length > 0" class="history-hint">最近搜索：</div>
        <div class="history-list">
          <div
            v-for="item in userStore.fullTextHistory"
            :key="item"
            class="history-item"
            @click="clickItemSearch(item)"
          >
            <OverflowTooltip class="history-item-name" :content="item" />
            <i class="ri-close-circle-fill" @click.stop="deleteHistoryItem(item)"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- 结果页面 -->
    <!-- 头部搜索 -->
    <template #search>
      <div v-if="haveSearch" class="result-container">
        <TabsRouter :tabs-config="borrowTab" />
        <div class="result-search-container">
          <el-input
            v-model="searchText"
            class="search-input"
            placeholder="请输入搜索内容"
            clearable
            :prefix-icon="Search"
            @keydown.enter="search"
          >
            <template #append>
              <TooltipButton
                icon=""
                :tooltip="!searchText ? '请先输入关键词' : ''"
                :disabled="!searchText"
                @click="search"
              >
                搜索
              </TooltipButton>
            </template>
          </el-input>

          <el-badge style="margin-left: auto" :is-dot="advancedOptions ? true : false">
            <TooltipButton
              icon=""
              :tooltip="!searchText ? '请先输入关键词' : ''"
              :disabled="!searchText"
              :type="advancedOptions ? 'primary' : 'default'"
              @click="ComplexSearchRef?.openDrawer()"
            >
              高级检索
            </TooltipButton>
          </el-badge>
        </div>
      </div>
    </template>

    <!-- 结果列表 -->
    <template #table>
      <div v-if="haveSearch" v-loading="resultState.loading" class="result-wrapper">
        <div>
          <BatchOperationButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Borrow)"
            type="primary"
            :disabled="!selectedRowsWsid.length"
            :icon="Tickets"
            @click="handleBorrowMulti"
          >
            批量借阅
          </BatchOperationButton>
        </div>

        <el-checkbox-group v-if="resultState.data.length > 0" v-model="selectedRowsWsid" class="result-list">
          <el-checkbox v-for="item in resultState.data" :key="item.id" class="result-item" :label="item.wsid">
            <template #default>
              <div class="user-info-wrapper">
                <div class="user-info">{{ `姓名：${item.patientName}` }}</div>
                <div class="user-info">{{ `病案号：${item.mrNo}` }}</div>
                <div class="user-info">{{ `患者编号：${item.patientId}` }}</div>
                <div class="user-info">{{ `性别：${item.patientSex}` }}</div>
                <div class="user-info">{{ `出院科室：${item.outHospitalDeptName}` }}</div>
                <div class="user-info">{{ `出院时间：${formatDate(item.outHospitalDatetime)}` }}</div>
              </div>
              <div class="detail-info">
                <div class="title-container">
                  <img :src="PDFIcon" />
                  <div class="title-wrapper">
                    <div class="title">入院记录</div>
                    <el-tooltip
                      :disabled="item.isBorrow !== 'NO'"
                      effect="dark"
                      :content="item.notBorrowReason"
                      placement="top"
                    >
                      <div
                        class="borrow-item-btn"
                        :class="item.isBorrow === 'NO' ? 'is-disabled' : ''"
                        @click="handleBorrowSingle(item)"
                      >
                        借阅
                      </div>
                    </el-tooltip>
                  </div>
                </div>
                <div class="detail-content">
                  <div v-html="getHighlights(item)"></div>
                </div>
              </div>
            </template>
          </el-checkbox>
        </el-checkbox-group>
        <EmptyContent v-else desc="暂无数据" />

        <el-pagination
          v-if="resultState.data.length > 0"
          v-model:current-page="paginationConfig.currentPage"
          v-model:page-size="paginationConfig.pageSize"
          class="result-pagination"
          :page-sizes="[10, 25, 50, 100]"
          :background="true"
          layout="sizes, prev, pager, next, jumper, total"
          :total="resultState.total"
          @update:page-size="resetCurrentPage"
        />
      </div>
      <ComplexSearch
        ref="ComplexSearchRef"
        filter-type="FULL_TEXT_SEARCH"
        @search="search"
        @reset="
          () => {
            ComplexSearchRef?.resetValue()
            search()
          }
        "
      />

      <BorrowDialog ref="borrowDialogRef" :borrow-tree="borrowState.borrowTree" :packet-list="borrowState.packetList" />
    </template>
  </PageContainer>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from "vue"
import { Search, Tickets } from "@element-plus/icons-vue"
import { PageContainer, OverflowTooltip, EmptyContent, BatchOperationButton, TooltipButton } from "@/base-components"
import { ComplexSearch, TabsRouter } from "@/page-components"
import PDFIcon from "@/assets/png/pdf-icon.png"
import { MenuOperationEnum } from "@/configs"
import { useUserStore } from "@/stores"
import { formatDate, toastError, Message, formatRecordTree } from "@/utils"
import { BorrowDialog } from "./components"
import { borrowTab, BorrowState, getBorrowInfo, menuId } from "./config"
import { searchBorrowApplicationListApi } from "./interface"

const userStore = useUserStore()

const { hasOperationPermission } = userStore

const ComplexSearchRef = ref()
const borrowDialogRef = ref()

const haveSearch = ref(false)
const searchText = ref("")
const resultState = reactive({
  loading: false,
  data: [] as Array<Record<string, any>>,
  total: 0
})

const advancedOptions = computed(() => {
  let haveFilter = false

  if (ComplexSearchRef.value?.advancedFilters.filters) {
    haveFilter = true
  }
  return haveFilter
})

/* ======================= 历史记录 ======================= */
// 删除历史记录
function deleteHistoryItem(text) {
  const data = userStore.fullTextHistory.filter(item => item !== text)
  userStore.setFullTextHistory(data)
}
// 点击历史记录搜索
function clickItemSearch(item) {
  searchText.value = item
  search()
}

/* ======================= 搜索 ======================= */

/* ================= 分页器 ================= */
const paginationConfig = reactive({
  currentPage: 1,
  pageSize: 25
})

function resetCurrentPage() {
  paginationConfig.currentPage = 1
}

/* ================= 搜索 ================= */
function search() {
  if (!searchText.value) return Message.warning("请先输入检索关键词")
  if (!userStore.fullTextHistory.includes(searchText.value)) {
    userStore.setFullTextHistory([...userStore.fullTextHistory, searchText.value])
  }
  haveSearch.value = true
  ComplexSearchRef.value?.getAdvancedFilters()
  const params = {
    filters: ComplexSearchRef.value?.advancedFilters.filters || "",
    orFieldName: ComplexSearchRef.value?.advancedFilters.orFieldName || "",
    offset: (paginationConfig.currentPage - 1) * paginationConfig.pageSize,
    limit: paginationConfig.pageSize,
    searchText: searchText.value
  }

  if (!params.filters && !params.searchText) return

  resultState.loading = true
  searchBorrowApplicationListApi(params)
    .then(res => {
      resultState.data = res.data?.data?.rows || []
      resultState.total = res.data?.data?.page?.totalElements || 0
      resultState.loading = false
    })
    .catch(err => {
      resultState.loading = false
      toastError(err, "获取检索结果失败")
    })
}

// 获取关键词信息
function getHighlights(row) {
  const highLight = row.highlightFields?.find(
    item => item.fieldName === "fileContents.content" || item.fieldName === "fileContents.filename"
  )
  return highLight?.highlightedText.replaceAll("<em>", "<span>").replaceAll("</em>", "</span>")
}

/* ======================= 借阅 ======================= */

const borrowState = reactive<BorrowState>({
  dialogVisible: false,
  borrowTree: [],
  packetList: [],
  borrowType: "single"
})

// 借阅单条数据
const handleBorrowSingle = (row: any) => {
  if (row.isBorrow === "NO") return
  if (row.borrowStatus === "CAN_NOT_BORROW") {
    Message.error("存在未归档的病案，不能借阅")
    return
  }
  borrowState.borrowType = "single"

  return getBorrowInfo([row.wsid], "single", setBorrowInfo)
}

const selectedRowsWsid = ref<Array<string>>([])
const selectedRows = computed(() => {
  return resultState.data.filter(item => selectedRowsWsid.value.includes(item.wsid))
})

// 借阅多条数据
const handleBorrowMulti = () => {
  for (let record of selectedRows.value) {
    if (record.borrowStatus === "CAN_NOT_BORROW") {
      Message.error("存在未归档的病案，不能借阅")
      return
    }
    if (record.isBorrow === "NO") {
      Message.error("所选数据存在已借阅的病案或无法借阅的特殊病案")
      return
    }
  }
  const borrowType = selectedRowsWsid.value.length > 1 ? "multi" : "single"
  borrowState.borrowType = borrowType
  return getBorrowInfo(selectedRowsWsid.value, borrowType, setBorrowInfo)
}

// 设置可借阅信息
const setBorrowInfo = (borrowTree, packetList) => {
  borrowDialogRef.value.open()
  borrowState.borrowTree = formatRecordTree(borrowTree)
  borrowState.packetList = packetList.map(packet => {
    return `${packet.mrNo}(${packet.inpNo})，${packet.patientName}`
  })
}
</script>

<style lang="less" scoped>
.full-text-search-container {
  .search-container {
    .search-wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      .search-title {
        margin-top: 70px;
        color: rgba(10, 22, 51, 0.85);
        font-size: 36px;
        font-weight: 600;
        letter-spacing: 4px;
      }
      .search-input {
        margin-top: 30px;
      }

      .history-hint {
        color: rgba(10, 22, 51, 0.6);
        line-height: 24px;
        margin-top: 70px;
        width: 630px;
      }

      .history-list {
        width: 630px;
        margin-top: 12px;
        display: flex;
        flex-wrap: wrap;
        gap: 24px;
        .history-item {
          flex-shrink: 0;
          background: #f6f6f7;
          padding: 2px 16px;
          border-radius: 45px;
          box-sizing: border-box;
          cursor: pointer;
          max-width: 100px;
          position: relative;
          display: flex;

          .ri-close-circle-fill {
            position: absolute;
            top: 0px;
            right: 0px;
            color: #848a99;
            display: none;
          }

          &:hover {
            .ri-close-circle-fill {
              display: unset;
            }
          }
        }
      }
    }
  }

  .result-container {
    .result-search-container {
      margin: 20px 0px 14px 0px;
      display: flex;
      justify-content: space-between;
      .search-input {
        width: 410px;
      }
    }
  }

  .result-wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;

    .result-list {
      margin: 32px 0px;
      flex: 1;
      min-height: 0px;
      overflow: auto;
      padding-right: 10px;
      box-sizing: border-box;
      :deep(.el-checkbox) {
        height: 130px;
        width: 100%;
        margin-right: 0px;
        align-items: flex-start;

        .el-checkbox__input {
          padding: 17px 19px;
          box-sizing: border-box;
          border-bottom: 1px solid #f0f4fb;
        }
        .el-checkbox__label {
          flex: 1;
          min-width: 0px;
          padding: 0px;
        }
      }
      .result-item {
        width: 100%;

        .user-info-wrapper {
          display: flex;
          gap: 12px;
          border-bottom: 1px solid #f0f4fb;
          height: 48px;
          line-height: 48px;

          .user-info {
            flex: 1;
            min-width: 0px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            color: rgba(10, 22, 51, 0.85);
          }
        }

        .detail-info {
          padding: 12px 0px;
          box-sizing: border-box;
          border-bottom: 1px solid #f0f4fb;

          .title-container {
            display: flex;
            align-items: center;
            img {
              width: 16px;
              height: 16px;
            }
            .title-wrapper {
              margin-left: 8px;
              flex: 1;
              min-width: 0px;
              display: flex;
              justify-content: space-between;
              line-height: 24px;
              .title {
                color: rgba(10, 22, 51, 0.85);
                font-weight: bold;
              }
              .borrow-item-btn {
                color: #3860f4;
                cursor: pointer;
                &:hover {
                  color: #3860f4cc;
                }
              }
              .is-disabled {
                color: #999;
                cursor: not-allowed;
                &:hover {
                  color: #999;
                }
              }
            }
          }
          .detail-content {
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 24px;
            margin-top: 8px;
            color: rgba(10, 22, 51, 0.6);
            :deep(span) {
              color: red;
            }
          }
        }
      }
    }
    .result-pagination {
      align-self: flex-end;
    }
  }

  .search-input {
    width: 630px;
    --el-component-size-large: 44px;
    :deep(.el-input__wrapper) {
      border-radius: 4px 0px 0px 4px;
    }
    :deep(.el-input-group__append) {
      border-radius: 0px 4px 4px 0px;

      background: var(--el-color-primary);
      border-color: var(--el-color-primary);
      color: var(--el-color-white);
      box-shadow: 1px 0 0 var(--el-color-primary) inset, 0 -1px 0 0 var(--el-color-primary) inset,
        -1px 0 0 0 var(--el-color-primary) inset;

      &:hover {
        color: var(--el-color-white);
        border-color: var(--el-color-primary-light-3);
        background-color: var(--el-color-primary-light-3);
      }
    }
  }
}
</style>
