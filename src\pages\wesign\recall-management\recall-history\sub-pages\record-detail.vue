<template>
  <div id="medical-record-detail">
    <!-- 病案v9-召回比对入口 -->
    <el-button type="primary" class="compare-btn" @click="checkSeal">对比查看</el-button>
    <div v-loading="state.loading" class="view-left">
      <MedicalRecordTree
        can-collapse
        :base-info="state.baseInfo"
        :tree-info="state.treeInfo"
        @click-pdf="handlePdfClick"
      />
    </div>
    <div class="view-middle common-box-shadow">
      <PdfPreviewComponent ref="pdfPreviewRef" :src="state.pdfSrc" />
    </div>
    <SealDialog ref="sealDialogRef" :selected-row="{ inpNo: $route.query.inpNo }" :confirm-callback="jumpCompare" />
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, ref } from "vue"
import { useRoute } from "vue-router"
import { PdfPreviewComponent } from "@/base-components"
import { MedicalRecordTree, SealDialog } from "@/page-components"
import { Message, toastError, encryptStr } from "@/utils"
import { getRecallApprovedDetailApi } from "../../interface"

const route = useRoute()

const state = reactive({
  loading: false,
  baseInfo: {} as any, // 患者基础信息
  pdfSrc: "",
  targetNode: {} as any, // 右侧元数据需要的信息
  firstPageFields: [] as any[], // 首页字段
  treeInfo: {} as any // 左侧tree需要的原始数据
})

const handlePdfClick = node => {
  state.pdfSrc = "/api" + node?.contentFilePath
  state.targetNode = node
}

onMounted(() => {
  const wsid = route.query?.wsid
  state.loading = true
  if (!wsid) return Message.error("未获取到病历数据")
  getRecallApprovedDetailApi(wsid)
    .then(res => {
      if (res.data.code !== "100100000") return
      const recordDetail = res.data.data
      state.baseInfo = recordDetail.baseInfo || {}
      state.firstPageFields = recordDetail.firstPageFields || []
      state.treeInfo = recordDetail.treeInfo
      state.loading = false
    })
    .catch(error => {
      state.loading = false
      toastError(error, "获取数据失败")
    })
})

const sealDialogRef = ref()
function checkSeal() {
  sealDialogRef.value?.checkSealed()
}

function jumpCompare() {
  if (sealDialogRef.value.secretKey)
    window.open(
      `/recall/history/compare?wsid=${route.query.wsid}&inpNo=${route.query.inpNo}&sealKey=${encryptStr(
        sealDialogRef.value.secretKey
      )}`
    )
  else window.open(`/recall/history/compare?wsid=${route.query.wsid}&inpNo=${route.query.inpNo}`)
}
</script>

<style lang="less" scoped>
.compare-btn {
  position: absolute;
  top: -42px;
  right: 0px;
}
#medical-record-detail {
  flex: 1;
  min-height: 0px;
  display: flex;
  justify-content: space-between;
  height: 100%;
  position: relative;
  .view-middle {
    overflow-x: auto;
    min-width: 400px;
    flex: 1;
    box-sizing: border-box;
  }
}
</style>
