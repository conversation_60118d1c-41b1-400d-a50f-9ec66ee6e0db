import { Message, SystemAlert } from "./message-tool"

// blob转base64
export function blobToBase64(blob: Blob) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(blob)
    reader.onload = () => resolve(reader.result)
    reader.onerror = error => reject(error)
  })
}

// base64 转 blob
export function base64ToBlob(baseData) {
  if (!baseData) return Message.warning("数据转换失败，请重试")
  // 1. 去掉 Base64 数据 URL 的前缀部分
  const data = baseData.split(",")[1]

  // 2. 解码 Base64 字符串
  const binaryString = atob(data)

  // 3. 创建一个 Uint8Array 对象
  const binaryLen = binaryString.length
  const bytes = new Uint8Array(binaryLen)
  for (let i = 0; i < binaryLen; i++) {
    bytes[i] = binaryString.charCodeAt(i)
  }

  // 4. 创建一个 Blob 对象
  const blob = new Blob([bytes], { type: "image/png" })
  return blob
}

// url图片转base64
export function getUrlBase64(url) {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.src = url
    img.onload = function () {
      const canvas = document.createElement("canvas")
      canvas.width = img.width
      canvas.height = img.height
      const ctx = canvas.getContext("2d")
      ctx?.drawImage(img, 0, 0, img.width, img.height)
      resolve(canvas.toDataURL())
    }
  })
}

// 选取文件函数
export const selectFile = (function () {
  const input = document.createElement("input")
  input.type = "file"
  input.style.opacity = "0"

  return function (accept = "", size) {
    const accepts = accept
      .split(",")
      .map(type => type.trim())
      .filter(type => type)
    input.accept = accepts.join(",")

    const acceptTests = accepts.map(type => {
      if (/^\./.test(type)) {
        //为后缀
        return {
          target: "name", //检查名称
          regExp: new RegExp(`${type.replace(".", "\\.")}$`, "i")
        }
      } else {
        //为MIME类型
        if (/\/\*$/.test(type)) {
          //泛匹配
          return {
            target: "type", //检查名称
            regExp: new RegExp(`^${type.replace("*", "\\S+")}$`, "i")
          }
        } else {
          return {
            target: "type", //检查名称
            regExp: new RegExp(`^${type}$`, "i")
          }
        }
      }
    })

    return new Promise((resolve, reject) => {
      input.onchange = e => {
        const file = input.files[0]
        if (!file) return

        if (size && file.size > size) {
          reject(new TypeError("ERROR_FILE_SIZE"))
          return
        }

        let result = true
        if (acceptTests.length > 0) {
          result = acceptTests.some(test => {
            return test.regExp.test(file[test.target])
          })
        }

        if (result) {
          resolve(file)
        } else {
          reject(new TypeError("ERROR_FILE_TYPE"))
        }

        input.value = ""
      }

      //兼容IE Input不在DOM树上无法触发选择的问题
      document.body.appendChild(input)
      input.click()
      document.body.removeChild(input)
    })
  }
})()

interface DownloadFileParams {
  fileData: any
  fileType: string
  fileName: string
}

// 下载文件
export function downloadFile({ fileData, fileType, fileName }: DownloadFileParams) {
  const blob = new Blob([fileData], { type: fileType })
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement("a")
  link.style.display = "none"
  link.href = url
  link.setAttribute("download", fileName)
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

export const defaultPdfRenderConfigs = {
  cMapUrl: "/src/plugins/pdfjs-dist/cmaps/",
  cMapPacked: true,
  rangeChunkSize: 256 * 1024, // 256kb
  disableStream: true,
  disableAutoFetch: true
}

// 指定目录下载
export const downloadFileToFolder = async (fileName: string, fileData: any) => {
  // @ts-ignore
  const directoryHandle = await window.showDirectoryPicker() // 请求用户选择一个文件夹
  // 检测文件名是否已存在
  try {
    await directoryHandle.getFileHandle(fileName)
    const nameWithoutExtension = fileName.slice(0, fileName.lastIndexOf(".")) // 获取文件名（不含扩展名）
    const extension = fileName.split(".").pop() // 获取文件扩展名
    fileName = `${nameWithoutExtension}_${new Date().getTime()}.${extension}` // 修改文件名
  } catch (error) {
    console.log("文件不存在，使用原文件名")
  }
  const fileHandle = await directoryHandle.getFileHandle(fileName, { create: true }) // 在用户选择的文件夹中创建文件
  const writable = await fileHandle.createWritable() // 获取文件的写入权限
  await writable.write(fileData) // 将内容写入文件
  await writable.close() // 关闭文件
  SystemAlert("文件保存成功！", "success")
}
