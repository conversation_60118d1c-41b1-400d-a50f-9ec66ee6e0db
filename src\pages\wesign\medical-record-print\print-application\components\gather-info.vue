<template>
  <div class="gather-info-root">
    <div class="view-left">
      <div class="title-wrapper">已采集图片</div>
      <!-- 需要采集的信息 -->
      <el-tree
        class="info-type-tree"
        :data="certificateInfoTree"
        default-expand-all
        highlight-current
        @node-click="setActiveInfoNode"
      >
        <template #default="{ node, data }">
          <img style="margin: 0px 8px 0px 16px" :src="data.relationship ? FolderIcon : ImageIcon" />
          {{ data.label }}
        </template>
        <template #empty>
          <el-empty style="height: 100%" :image="NoDataImage" description="请先选择申请人信息" :image-size="100" />
        </template>
      </el-tree>
    </div>
    <!-- 采集的信息 -->
    <div class="view-right">
      <div v-if="props.edit" class="title-wrapper" style="justify-content: flex-end">
        <el-button :loading="uploadLoading" :disabled="!activeInfoNode" @click="handleUpload">本地上传</el-button>
        <el-button
          :disabled="!activeInfoNode || uploadLoading"
          :loading="CameraDialogRef?.cameraLoading"
          type="primary"
          @click="() => CameraDialogRef?.openDialog()"
        >
          拍摄
        </el-button>
      </div>
      <!-- 照片展示 -->
      <div v-if="activeInfoNode" class="image-preview">
        <img v-if="!certificateInfo[activeInfoNode.data.prop]" class="preview-hint-img" :src="previewHintImg" />
        <BlobImage v-else class="preview-img" :file-wsid="certificateInfo[activeInfoNode.data.prop]" />
        <div class="preview-hint">{{ activeInfoNode.data.label }}</div>
      </div>
      <el-empty
        v-else
        style="flex: 1; min-height: 0px"
        :image="NoDataImage"
        description="请先在左侧需要预览的信息"
        :image-size="100"
      />
    </div>
  </div>

  <CameraDialog
    ref="CameraDialogRef"
    :submit-loading="uploadLoading"
    :default-rotate="cameraRotate"
    :multiple="false"
    @submit="takePhoto"
  />
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue"
import { BlobImage } from "@/base-components"
import { CameraDialog } from "@/page-components"
import FolderIcon from "@/assets/png/folder-icon.png"
import ImageIcon from "@/assets/png/image-icon.png"
import NationalEmblemImg from "@/assets/png/national-emblem.png"
import NoDataImage from "@/assets/png/nodata.png"
import OtherCertificateImg from "@/assets/png/other-certificate.png"
import PortraitImg from "@/assets/png/portrait.png"
import { uploadSystemFile } from "@/interfaces"
import { getUmiOcrData } from "@/interfaces/umiocr"
import { RelationshipType, RelationshipTypeEnum } from "@/types"
import { base64ToBlob, blobToBase64, selectFile, toastError } from "@/utils"
import { ownInfo, agentInfo, guardianInfo, inheritorInfo, agentInheritorInfo, CertificateTypeEnum } from "../config"
interface PropsType {
  relationship: RelationshipType
  defaultImgs?: Record<string, any>
  edit?: boolean
}
const props = withDefaults(defineProps<PropsType>(), {
  edit: true,
  defaultImgs: () => {
    return {}
  }
})

const CameraDialogRef = ref()
const emits = defineEmits(["ocrUpdate"])
const certificateInfoTree = computed(() => {
  let data: Array<any> = []
  switch (props.relationship) {
    case RelationshipTypeEnum.OWN: {
      data = ownInfo
      break
    }
    case RelationshipTypeEnum.AGENT: {
      data = agentInfo
      break
    }
    case RelationshipTypeEnum.GUARDIAN: {
      data = guardianInfo
      break
    }
    case RelationshipTypeEnum.HEIR: {
      data = inheritorInfo
      break
    }
    case RelationshipTypeEnum.HEIR_AGENT: {
      data = agentInheritorInfo
      break
    }

    default:
      data = []
  }

  return data
})
const activeInfoNode = ref<null | Record<string, any>>(null)
const certificateInfo = ref({})

// 设置初始值
watch(
  [() => props.defaultImgs, () => certificateInfoTree.value],
  val => {
    if (!certificateInfoTree.value || certificateInfoTree.value.length === 0) {
      activeInfoNode.value = null
      certificateInfo.value = {}
      return
    }

    if (JSON.stringify(props.defaultImgs) !== "{}") certificateInfo.value = props.defaultImgs
    else {
      let data = {}
      certificateInfoTree.value.forEach(parent => {
        parent.children?.forEach(item => {
          data[item.prop] = ""
        })
      })
      certificateInfo.value = data
    }
  },
  { deep: true, immediate: true }
)

// 选中当前需要收集的信息
function setActiveInfoNode(data, node) {
  if (node.parent && node.data.prop) activeInfoNode.value = { data: node.data, parent: node.parent.data }
  else activeInfoNode.value = null
}

// 无数据时需要展示的图片
const previewHintImg = computed(() => {
  if (!activeInfoNode.value) return ""
  switch (activeInfoNode.value.data.type) {
    case CertificateTypeEnum.Portrait:
      return PortraitImg
    case CertificateTypeEnum.NationalEmblem:
      return NationalEmblemImg
    default:
      return OtherCertificateImg
  }
})

// 本地上传
function handleUpload() {
  selectFile(".jpg,.jpeg,.png", 1024 * 1024 * 30).then(async file => {
    uploadFile(file)
  })
}

const cameraRotate = computed(() => {
  return [CertificateTypeEnum.Portrait, CertificateTypeEnum.NationalEmblem].includes(activeInfoNode.value?.data.type)
    ? 0
    : 90
})
// 拍摄上传
function takePhoto(file) {
  uploadFile(file.raw)
}

const uploadLoading = ref(false)
async function uploadFile(file) {
  uploadLoading.value = true
  uploadSystemFile({ file })
    .then(async res => {
      certificateInfo.value[activeInfoNode.value?.data.prop] = res.data.data.fileWsid

      // ocr 识别
      try {
        if (!file.base64) {
          file.base64 = await blobToBase64(new Blob([file], { type: file.type }))
        }

        const ocrData = await getUmiOcrData({
          base64: file.base64.split("base64,")[1],
          options: {
            "data.format": "text"
          }
        })
        if (ocrData.data?.code === 100)
          emits("ocrUpdate", { data: ocrData.data?.data, activeInfoNode: activeInfoNode.value })
      } catch (err: any) {
        toastError(err, "ocr识别失败")
      }
      uploadLoading.value = false
      CameraDialogRef.value?.closeDialog()
    })
    .catch(err => {
      uploadLoading.value = false
      toastError(err, "上传失败")
    })
}

defineExpose({ certificateInfo, uploadLoading })
</script>

<style lang="less" scoped>
.gather-info-root {
  background: #fff;
  flex: 1;
  min-height: 0px;
  display: flex;
  .view-left {
    width: 256px;
    flex-shrink: 0;
    border-right: 1px solid rgba(0, 0, 0, 0.1);
  }
  .view-right {
    flex: 1;
    min-width: 0px;
    display: flex;
    flex-direction: column;
  }

  .title-wrapper {
    height: 48px;
    display: flex;
    align-items: center;
    padding: 0px 16px;
    background: #f9faff;
    border-top: 1px solid rgba(10, 22, 51, 0.1);
    border-bottom: 1px solid rgba(10, 22, 51, 0.1);
    font-size: 14px;
    font-weight: bold;
  }

  .info-type-tree {
    height: calc(100% - 50px);
    overflow: auto;
    :deep(.el-tree-node__expand-icon) {
      display: none;
    }
  }
  .image-preview {
    flex: 1;
    min-height: 0px;
    padding: 48px 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
    .preview-hint-img {
      width: 300px;
      height: 190px;
    }
    .preview-img {
      min-width: 300px;
      min-height: 190px;
      :deep(img) {
        object-fit: contain;
      }
    }
    .preview-hint {
      margin-top: 15px;
      color: rgba(10, 22, 51, 0.3);
      font-size: 14px;
      cursor: default;
    }
  }
}
</style>
