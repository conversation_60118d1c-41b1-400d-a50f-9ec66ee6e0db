<template>
  <PageContainer separate>
    <template #search>
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
        />

        <el-form-item label="手术名称">
          <el-select
            v-model="searchFormState.operationName"
            filterable
            remote
            placeholder="请输入选择"
            :remote-method="fetchOperationTypeOptions"
            remote-show-suffix
            :loading="operationLoading"
          >
            <el-option v-for="item in operationTypeOptions" :key="item.name" :label="item.name" :value="item.name" />
          </el-select>
        </el-form-item>

        <DepartmentFormItem v-model:model-value="searchFormState.inHospitalDeptWsid" label="入院科室" />

        <DepartmentFormItem v-model:model-value="searchFormState.outHospitalDeptWsid" label="出院科室" />

        <CommonSelectFormItem
          v-model:model-value="searchFormState.secrecyGrade"
          label="保密等级"
          :options="state.secrecyGradeOptions"
        />

        <DaterangeFormItem v-model:model-value="searchFormState.outHospitalDatetime" label="出院时间" />

        <CommonSelectFormItem
          v-model="searchFormState.status"
          label="病案状态"
          :options="globalOptionsStore.medicalRecordStatusOptions"
        />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="secrecyTableRef"
        table-id="secrecyManageTable"
        :table-columns="secrecyTableColumns"
        :request-api="getSecrecyListApi"
        :request-params="searchParams"
      >
        <template #header>
          <BatchOperationButton
            type="primary"
            :icon="h('i', { class: 'ri-lock-line' })"
            :disabled="!selectedRows.length"
            @click="setMultiSecrecy"
          >
            批量设置保密
          </BatchOperationButton>
        </template>
        <template #inHospitalDatetime="{ row }">
          <span>{{ formatDatetime(row.inHospitalDatetime) }}</span>
        </template>
        <template #outHospitalDatetime="{ row }">
          <span>{{ formatDatetime(row.outHospitalDatetime) }}</span>
        </template>
        <template #operationDatetime="{ row }">
          <span>{{ formatDatetime(row.operationDatetime) }}</span>
        </template>
        <template #status="{ row }">
          <MedicalRecordStatusTag :status="row.status" />
        </template>
        <template #operation="{ row }">
          <TableButton @click="setSingleSecrecy(row)">编辑</TableButton>
        </template>
      </CommonTable>
    </template>

    <DialogContainer
      v-model:visible="secrecyDialogState.visible"
      title="确认保密"
      :width="425"
      class="secrecy-dialog"
      :confirm-callback="confirmSecrecy"
      :cancel-callback="resetDialogState"
      :close-callback="resetDialogState"
    >
      <el-form
        ref="secrecyFormRef"
        v-loading="secrecyDialogState.loading"
        :model="secrecyDialogState.form"
        :rules="secrecyFormRules"
        label-width="110px"
      >
        <el-form-item label="病案保密类型" prop="secrecyScope">
          <el-select
            v-model="secrecyDialogState.form.secrecyScope"
            placeholder="请选择"
            style="width: 100%"
            :disabled="secrecyDialogState.isMulti"
            @change="handleSecrecyScopeChange"
          >
            <el-option
              v-for="option in state.secrecyScopeOptions"
              :key="option.value"
              :label="option.key"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <div v-if="secrecyDialogState.form.secrecyScope === 'CUSTOM'" style="margin: 0 0 18px 110px">
          <div class="dialog-tree-wrapper">
            <div class="dialog-tree-header">
              选择文件
              <div class="check-all">
                <el-checkbox
                  v-model="secrecyDialogState.checkAll"
                  size="large"
                  style="float: right"
                  @change="checkAllNodes"
                >
                  全选
                </el-checkbox>
              </div>
            </div>
            <div class="dialog-tree-body">
              <CommonTree ref="treeRef" :data="secrecyDialogState.documentTree" :show-checkbox="true" />
            </div>
          </div>
        </div>
        <el-form-item label="病案保密等级" prop="secrecyGrade" style="margin-bottom: 0">
          <el-select v-model="secrecyDialogState.form.secrecyGrade" placeholder="请选择" style="width: 100%">
            <el-option
              v-for="item in state.secrecyGradeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </DialogContainer>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, h, computed, nextTick, watch } from "vue"
import { cloneDeep } from "lodash-es"
import {
  CommonTable,
  TableButton,
  PatientFilterFormItem,
  CommonSelectFormItem,
  DaterangeFormItem,
  DepartmentFormItem,
  SearchContainer,
  PageContainer,
  DialogContainer,
  CommonTree,
  BatchOperationButton,
  MedicalRecordStatusTag
} from "@/base-components"
import { secrecyLevelOptions } from "@/configs"
import { useTableSearch } from "@/hooks"
import { getSecreciesList } from "@/interfaces"
import { useGlobalOptionsStore } from "@/stores"
import {
  Message,
  toastError,
  formatRecordTree,
  getSpecifiedPropFileNodeList,
  formatDatetime,
  flattenTree
} from "@/utils"
import { secrecyTableColumns, secrecyFormRules } from "./config"
import {
  getSecrecyListApi,
  getSecrecyOptionsApi,
  searchKeywordOperationApi,
  setSecrecyConfigApi,
  getSecrecyDetailApi,
  getMultipleSecrecyDetailApi
} from "./interface"
import type { FormInstance } from "element-plus"

const globalOptionsStore = useGlobalOptionsStore()

/* ======================== 搜索相关数据及方法 ======================== */

const secrecyTableRef = ref<InstanceType<typeof CommonTable>>()
const selectedRows = computed(() => secrecyTableRef.value?.tableState?.selectedRows ?? [])

const searchFormState = reactive({
  patientFilterProp: "",
  patientFilterValue: "",
  operationName: "",
  inHospitalDeptWsid: "",
  outHospitalDeptWsid: "",
  secrecyGrade: "",
  outHospitalDatetime: "",
  status: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/* ======================== 基础与选项加载 ======================== */

interface SecrecyOption {
  key: string
  value: string
}

const state = reactive({
  anesthesiaOptions: [] as SecrecyOption[], // 麻醉方式
  incisionOptions: [] as SecrecyOption[], // 切口等级
  secrecyScopeOptions: [] as SecrecyOption[], // 保密范围
  secrecyGradeOptions: [] as any // 保密等级
  // documentBagStatusOptions: [] as SecrecyOption[] // 病案袋状态
})

// 加载各种配置
onMounted(async () => {
  getSecrecyOptionsApi().then(res => {
    if (res.data.code !== "100100000") return
    const { anesthesia, incision, secrecyScope, secrecyGrade, documentBagStatus } = res.data.data
    state.anesthesiaOptions = anesthesia
    state.incisionOptions = incision
    state.secrecyScopeOptions = secrecyScope
    // state.secrecyGradeOptions = secrecyGrade
    // state.documentBagStatusOptions = documentBagStatus
  })
  state.secrecyGradeOptions = (await getSecreciesList()).data.data.map(item => {
    return { label: item.secrecyName, value: item.secrecyValue }
  })
})

/* ======================== 手术类型  ======================== */

const operationTypeOptions = ref<any[]>([])
const operationLoading = ref(false)

// 动态搜索手术名称
const fetchOperationTypeOptions = (keyword: string) => {
  if (keyword) {
    operationLoading.value = true
    searchKeywordOperationApi(keyword)
      .then(resData => {
        operationTypeOptions.value = resData
      })
      .finally(() => {
        operationLoading.value = false
      })
  }
}

/* ======================== 保密弹窗 ======================== */

const secrecyFormRef = ref<FormInstance>()
const treeRef = ref()

const secrecyDialogState = reactive({
  loading: false,
  documentTree: [] as any,
  visible: false,
  form: {
    secrecyScope: "", // 保密类型
    secrecyGrade: "" as any, // 保密等级
    documentWsids: [] as Array<string>, // 需要保密的病案文档
    documentBagWsids: [] as Array<string> // 需要保密的病案袋
  },
  checkAll: false,
  isMulti: false // 是否是批量保密
})

const checkedNodes = computed(() => {
  return treeRef.value?.getCheckedNodes()
})

// 切换全选
const checkAllNodes = value => {
  treeRef.value?.checkAllNodes(value)
}

watch(
  () => checkedNodes.value,
  val => {
    if (val) secrecyDialogState.checkAll = val.length === flattenTree(secrecyDialogState.documentTree).length
  }
)

// 改变保密类型
const handleSecrecyScopeChange = value => {
  if (value === "WHOLE") secrecyDialogState.form.documentWsids = []
}

// 设置单条数据保密
const setSingleSecrecy = row => {
  secrecyDialogState.visible = true
  secrecyDialogState.loading = true
  const currentSecrecyScope = state.secrecyScopeOptions?.find(item => item.key === row.secrecyScope)
  if (currentSecrecyScope) secrecyDialogState.form.secrecyScope = currentSecrecyScope.value // 设置当前保密类型
  // const currentSecrecyGrade = state.secrecyGradeOptions?.find(item => item.key === row.secrecyGrade)
  // if (currentSecrecyGrade) secrecyDialogState.form.secrecyGrade = currentSecrecyGrade.value // 设置当前保密等级
  secrecyDialogState.form.documentBagWsids = [row.wsid]
  getSecrecyDetailApi(row.inpNo)
    .then(res => {
      if (res.data.code !== "100100000") return
      secrecyDialogState.form.secrecyGrade = String(res.data.data.baseInfo.secrecyGrade)
      if (state.secrecyGradeOptions.every(item => item.value !== secrecyDialogState.form.secrecyGrade)) {
        secrecyDialogState.form.secrecyGrade = "不保密"
      }
      secrecyDialogState.documentTree = formatRecordTree(res.data.data.treeInfo)
      secrecyDialogState.loading = false
      // 获取当前保密等级的文件列表，并设置勾选
      const secretFileNodeList = getSpecifiedPropFileNodeList(
        secrecyDialogState.documentTree,
        "secrecyGrade",
        // currentSecrecyGrade?.value
        secrecyDialogState.form.secrecyGrade
      )
      const checkedFileWsidList = secretFileNodeList.map(node => node.wsid)
      nextTick(() => treeRef.value?.setCheckedKeys(checkedFileWsidList))
    })
    .catch(error => {
      secrecyDialogState.loading = false
      toastError(error)
    })
}

// 设置多条数据保密
const setMultiSecrecy = () => {
  if (!selectedRows.value?.length) return
  const wsids: Array<string> = []
  selectedRows.value?.forEach(item => wsids.push(item.wsid))
  // 批量设置时只能选整体保密
  secrecyDialogState.form.secrecyScope = "WHOLE"
  secrecyDialogState.isMulti = true
  getMultipleSecrecyDetailApi({ documentBagWsids: wsids }).then(res => {
    if (res.data.code !== "100100000") return
    secrecyDialogState.documentTree = formatRecordTree(res.data.data.treeInfo)
    secrecyDialogState.form.documentBagWsids = wsids
    secrecyDialogState.visible = true
  })
}

// 确认保密
const confirmSecrecy = () => {
  if (secrecyDialogState.form.secrecyScope !== "WHOLE") {
    const checkedNodes = treeRef.value.getCheckedNodes()
    checkedNodes.forEach(node => {
      if (node.type === "FILE") {
        secrecyDialogState.form.documentWsids.push(node.wsid)
      }
    })
  }
  secrecyFormRef.value?.validate(valid => {
    if (!valid) return
    const forms = cloneDeep(secrecyDialogState.form)
    forms.secrecyGrade = forms.secrecyGrade === "不保密" ? 99 : forms.secrecyGrade
    setSecrecyConfigApi({ ...forms })
      .then(() => {
        secrecyDialogState.visible = false
        handleReset()
        Message.success("设置成功")
        secrecyTableRef.value?.refreshTableData()
      })
      .catch(err => {
        toastError(err, "设置失败")
      })
  })
}

// 取消
const resetDialogState = () => {
  secrecyDialogState.visible = false
  secrecyDialogState.checkAll = false
  secrecyDialogState.documentTree = []
  secrecyDialogState.isMulti = false
  secrecyDialogState.loading = false
  secrecyDialogState.form = {
    secrecyScope: "",
    secrecyGrade: "",
    documentWsids: [],
    documentBagWsids: []
  }
}
</script>

<style lang="less" scoped>
.secrecy-dialog {
  .dialog-tree-wrapper {
    width: 100%;
    font-size: 14px;
    border: 1px solid rgb(225 226 230);
    border-radius: 5px;

    .dialog-tree-header {
      position: relative;
      height: 40px;
      padding: 0 15px;
      color: rgb(3 8 20 / 85%);
      background-color: #f8f9fc;
      background-color: rgb(248 249 252);
      line-height: 40px;
      font-weight: 700;

      .check-all {
        position: absolute;
        top: 0;
        right: 22px;
        height: 40px;
        font-weight: 100;
        color: #333;
      }
    }

    .dialog-tree-body {
      overflow-y: auto;
      height: 200px;
      padding: 0 0 0 15px;
    }
  }
}
</style>
