<template>
  <PageContainer separate>
    <!-- 头部搜索 -->
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList"></TabsRouter>
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
        />

        <VisitIdFormItem
          v-model:input-value="searchFormState.visitIdCount"
          v-model:select-value="searchFormState.visitIdSymbol"
          label="住院次数"
        />

        <DepartmentFormItem v-model:model-value="searchFormState.outHospitalDeptWsid" label="出院科室" />

        <CountRangeFormItem
          v-model:from="searchFormState.inHospitalDaysFrom"
          v-model:to="searchFormState.inHospitalDaysTo"
          :min="0"
          label="住院天数"
          unit="天"
        />

        <DaterangeFormItem v-model:model-value="searchFormState.outHospitalDatetime" label="出院时间" />

        <!-- <PatientLabelFormItem v-model="searchFormState.patientLabel" /> -->
      </SearchContainer>
    </template>

    <!-- 已质控表格 -->
    <template #table>
      <CommonTable
        table-id="finalQualityControlledTableIdent"
        :table-columns="finalQualityControlledColumns"
        :request-api="getDistributeQcTaskListApi"
        :request-params="{ qcType: QualityControlTypeEnum.FINAL_QC, ...searchParams }"
        :data-callback="dataCallback"
      >
        <!-- 患者姓名 -->
        <template #patientName="{ row }">
          <div style="display: flex; align-items: center; flex-wrap: wrap">
            <span style="margin: 0 16px">{{ row.patientName }}</span>
            <PatientLabelTag :row="row" />
          </div>
        </template>
        <template #qcFinalStatusEnum="{ row }">
          <el-tag :type="getQcStatusTagType(row.qcStatus)">{{ getQcStatus(row.qcStatus) }}</el-tag>
        </template>
        <template #patientPrintCount="{ row }">
          {{ row.patientPrintCount > 0 ? "已打印" : "未打印" }}
        </template>
        <template #operation="{ row }">
          <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.View)" @click="handleRowClick(row)">
            查看
          </TableButton>
          <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.Track)" @click="openQcTraceDrawer(row)">
            质控轨迹
          </TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>

  <!-- 封存病案密钥 -->
  <SealDialog ref="sealDialogRef" :selected-row="medicalRecord" :confirm-callback="confirmOperation" />

  <!-- 质控轨迹抽屉 -->
  <TraceDrawer v-model:visible="qcTraceVisible" :inp-no="activeInpNo" />
</template>

<script setup lang="ts">
import { ref, reactive } from "vue"
import { useRouter } from "vue-router"
import {
  SearchContainer,
  CommonTable,
  TableButton,
  PatientFilterFormItem,
  DepartmentFormItem,
  DaterangeFormItem,
  VisitIdFormItem,
  PageContainer,
  CountRangeFormItem,
  PatientLabelTag,
  PatientLabelFormItem
} from "@/base-components"
import { TabsRouter, SealDialog, TraceDrawer } from "@/page-components"
import { MenuOperationEnum, QualityControlTypeEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { getDistributeQcTaskListApi } from "@/interfaces"
import { useUserStore } from "@/stores"
import { formatDatetime, encryptStr, getQcStatusTagType, getQcStatus } from "@/utils"
import { finalQualityControlledColumns, tabsRouterList } from "./config"

const menuId = "/final-quality-control/manage"
const { hasOperationPermission } = useUserStore()

const router = useRouter()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  patientFilterValue: "",
  patientFilterProp: "patientName",
  outHospitalDeptWsid: "",
  outHospitalDatetime: "",
  visitIdSymbol: "",
  visitIdCount: "",
  inHospitalDaysFrom: "",
  inHospitalDaysTo: "",
  death: "",
  patientLabel: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState, `qcStatus!=WAIT`)

/* ======================== 表格相关方法 ======================== */
const medicalRecord = ref<Record<string, any> | null>(null) // 用户点击的病案
const sealDialogRef = ref() // SealDialog组件ref

function dataCallback(data: Array<Record<string, any>>) {
  data.forEach(item => {
    item.inHospitalDatetime = formatDatetime(item.inHospitalDatetime)
    item.outHospitalDatetime = formatDatetime(item.outHospitalDatetime)
    item.qcDatetime = formatDatetime(item.qcDatetime)
    item.outClinicalDeptDatetime = formatDatetime(item.outClinicalDeptDatetime)
  })
  return data
}

const handleRowClick = row => {
  medicalRecord.value = row
  if (sealDialogRef.value) sealDialogRef.value.checkSealed()
}

// 检查封存之后的回调
const confirmOperation = () => {
  const query = {
    inpNo: medicalRecord.value?.inpNo,
    wsid: medicalRecord.value?.wsid,
    urlType: QualityControlTypeEnum.FINAL_QC
  }
  if (sealDialogRef.value.secretKey) {
    query["secretKey"] = encryptStr(sealDialogRef.value.secretKey)
  }
  router.push({
    path: "/final-quality-control/manage/view",
    query: query
  })
}

/* ======================== 质控轨迹抽屉 ======================== */
const qcTraceVisible = ref(false)
const activeInpNo = ref("")

function openQcTraceDrawer(row) {
  qcTraceVisible.value = true
  activeInpNo.value = row.inpNo
}
</script>
