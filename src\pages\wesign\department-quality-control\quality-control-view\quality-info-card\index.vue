<template>
  <div class="quality-info-card" :class="route.query.urlType === 'FINAL_QC' ? 'final' : ''">
    <CardContainer sign>
      <template #header>
        <!-- <div class="tabs-container"> -->
        <el-tabs v-model="activeTab">
          <el-tab-pane label="病案完整性" name="self-inspection" />
          <el-tab-pane label="科室质控评分" name="quality-control-score" />
          <el-tab-pane
            v-if="route.query.urlType === 'FINAL_QC'"
            label="终末质控评分"
            name="final-quality-control-score"
          />
          <el-tab-pane label="质控记录" name="quality-control-record" />
        </el-tabs>
        <!-- </div> -->
      </template>

      <!-- 系统自检 -->
      <SelfInspection v-if="activeTab === 'self-inspection'" :document-bag-wsid="props.documentBagWsid" />

      <!-- 科室质控评分 -->
      <QualityControlScore v-if="activeTab === 'quality-control-score'" :is-control="isControl" :is-final-qc="false" />

      <!-- 终末质控评分 -->
      <QualityControlScore
        v-if="activeTab === 'final-quality-control-score'"
        :is-control="isControl"
        :is-final-qc="true"
      />

      <!-- 质控记录 -->
      <QualityControlRecord
        v-if="activeTab === 'quality-control-record'"
        ref="qualityControlRecord"
        :is-control="isControl"
        @refresh="refresh"
      />

      <!-- 第三方质控结果 -->
      <div
        v-if="route.query.urlType === 'FINAL_QC'"
        class="qc-result-container"
        :style="{ marginRight: scoreInfoState.isCollapse ? '0px' : '16px' }"
      >
        <div
          class="record-metadata"
          :style="{
            width: scoreInfoState.isCollapse ? '0px' : '296px',
            height: scoreInfoState.isCollapse ? '0px' : 'calc(100% - 40px)',
            padding: scoreInfoState.isCollapse ? '0px' : '20px'
          }"
        >
          <FormGroupHeader :title="qualityControlInfo?.qcSystemName"></FormGroupHeader>
          <div v-if="qualityControlInfo" style="color: #999">
            <span>质控评分：{{ qualityControlInfo?.score }}</span>
            <span style="margin-left: 10px">病案等级：{{ qualityControlInfo?.grade }}</span>
          </div>
          <el-table
            :data="qualityControlInfo?.qcInfo"
            border
            :header-cell-style="{ background: '#f5f5f5', position: 'sticky' }"
            class="qc-table"
          >
            <el-table-column prop="name" min-width="120px" label="评分项目" />
            <el-table-column prop="desc" min-width="120px" label="异常描述" />
          </el-table>
        </div>

        <!-- 折叠按钮 -->
        <div
          class="collapse-icon"
          :style="{
            boxShadow: scoreInfoState.isCollapse
              ? '0px 0px 3px 1px rgba(0, 35, 114, 0.1)'
              : ' 3px 0px 3px 1px rgba(0, 35, 114, 0.1)',
            left: scoreInfoState.isCollapse ? '-40px' : '-24px'
          }"
          @click="() => (scoreInfoState.isCollapse = !scoreInfoState.isCollapse)"
        >
          <el-icon v-if="!scoreInfoState.isCollapse"><ArrowRight /></el-icon>
          <el-icon v-if="scoreInfoState.isCollapse"><ArrowLeft /></el-icon>
          <span>{{ qualityControlInfo?.qcSystemName }}</span>
        </div>
      </div>
    </CardContainer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineExpose } from "vue"
import { useRoute } from "vue-router"
import { CardContainer, FormGroupHeader } from "@/base-components"
import { getQCInfoApi } from "@/interfaces"
import { SelfInspection, QualityControlScore, QualityControlRecord } from "./index"

const route = useRoute()

const isControl = route.query.isControl === "true" ? true : false

const activeTab = ref("self-inspection")

const props = defineProps({
  documentBagWsid: { type: String, default: "" }
})

const emits = defineEmits(["refresh"])

const refresh = () => {
  emits("refresh")
}

// 第三方系统质控结果
const qualityControlInfo = ref<Record<string, any>>({})

const scoreInfoState = reactive({
  isCollapse: true
})

onMounted(async () => {
  qualityControlInfo.value = await getQCInfoApi(route.query.inpNo as string)
})

// 质控不通过时自动切换到质控记录
const updateActiveTab = val => {
  activeTab.value = val
}

const qualityControlRecord = ref()
const openAddSuggest = () => {
  qualityControlRecord.value?.openAddSuggest()
}

defineExpose({
  openAddSuggest,
  updateActiveTab
})
</script>

<style lang="less" scoped>
.quality-info-card {
  width: 100%;
  height: 100%;
  :deep(.custom-card-header) {
    justify-content: center;
  }
  :deep(.el-tabs) {
    width: 95%;
  }
  :deep(.el-tabs__nav-scroll) {
    display: flex;
    justify-content: center;
  }

  :deep(.custom-card-body) {
    padding: 0px;
  }
  :deep(.el-tabs) {
    .el-tabs__header {
      margin: 0px;
    }
    .el-tabs__active-bar {
      display: none;
    }
    .el-tabs__nav-wrap::after {
      display: none;
    }
  }
}
.final {
  :deep(.el-tabs__item) {
    padding: 0 10px;
  }
}

.qc-result-container {
  position: absolute;
  z-index: 10;
  right: 4px;
  top: 0;
  height: 100%;
}

.record-metadata {
  overflow: hidden;
  padding: 20px;
  height: calc(100% - 40px);
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgb(0 35 114 / 10%);
  display: flex;
  flex-direction: column;
  transition: width 0.3s;
}

.collapse-icon {
  width: 24px;
  // height: 48px;
  height: 160px;
  background: #fff;
  left: -24px;
  padding: 16px 4px;
  box-sizing: border-box;
  position: absolute;
  // top: 0px;
  top: 30vh;
  // bottom: 0px;
  margin: auto;
  z-index: 999;
  cursor: pointer;
  border-radius: 5px 0px 0px 5px;
  &:hover {
    color: #fff;
    background-color: var(--el-color-primary);
  }
}
</style>
