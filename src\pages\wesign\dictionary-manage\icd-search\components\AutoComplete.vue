<template>
  <el-tooltip
    ref="popperRef"
    :visible="suggestionVisible"
    placement="bottom-start"
    :fallback-placements="['bottom-start', 'top-start']"
    :popper-class="[ns.e('popper'), popperClass]"
    :teleported="teleported"
    :gpu-acceleration="false"
    pure
    manual-mode
    effect="light"
    trigger="click"
    persistent
    @before-show="onSuggestionShow"
  >
    <div ref="listboxRef" :class="[ns.b(), $attrs.class]" :style="styles" :aria-expanded="suggestionVisible">
      <el-input
        ref="inputRef"
        v-bind="attrs"
        :clearable="clearable"
        :disabled="disabled"
        :name="name"
        :model-value="modelValue"
        :aria-label="ariaLabel"
        @input="handleInput"
        @change="handleChange"
        @focus="handleFocus"
        @blur="handleBlur"
        @clear="handleClear"
        @keydown.enter="handleKeyEnter"
        @keydown.tab="close"
        @keydown.esc="handleKeyEscape"
        @mousedown="handleMouseDown"
      ></el-input>
    </div>
    <template #content>
      <div
        ref="regionRef"
        :class="[ns.b('suggestion'), ns.is('loading', suggestionLoading)]"
        :style="{
          [fitInputWidth ? 'width' : 'minWidth']: dropdownWidth
        }"
      >
        <div>
          <li v-if="suggestionLoading">
            <slot name="loading">
              <Loading />
            </slot>
          </li>
          <template v-else>
            <div
              :style="{
                margin: 'auto 10px'
              }"
            >
              <BaseTable
                :columns="columns"
                :style="{ height: '300px' }"
                :data="suggestions"
                :border="true"
                @row-click="row => handleSelect(row)"
              />
            </div>
          </template>
        </div>
      </div>
    </template>
  </el-tooltip>
</template>

<script lang="ts" setup>
import { computed, ref, useAttrs as useRawAttrs } from "vue"
import {
  CHANGE_EVENT,
  INPUT_EVENT,
  UPDATE_MODEL_EVENT,
  useFormDisabled,
  useAttrs,
  useId,
  useNamespace
} from "element-plus"
import { onClickOutside } from "@vueuse/core"
import { isArray } from "lodash/fp"
import { Loading } from "@element-plus/icons-vue"
import { BaseTable } from "@/base-components"
import type { TableColumnItem } from "@/types"
import type { TooltipInstance, InputInstance } from "element-plus"
import type { StyleValue } from "vue"

const columns = [
  { prop: "code", label: "编码", width: 120 },
  { prop: "name", label: "名称", width: 140 }
] as Array<TableColumnItem>

interface AutocompleteProps {
  modelValue: string
  valueKey?: string
  clearable?: boolean
  name?: string
  ariaLabel?: string
  placement?: string
  popperClass?: string
  teleported?: boolean
  fitInputWidth?: boolean
  hideLoading?: boolean
  triggerOnFocus?: boolean

  fetchSuggestions: any
  highlightFirstItem?: boolean
  selectWhenUnmatched?: boolean
}

const props = withDefaults(defineProps<AutocompleteProps>(), {
  valueKey: "value",
  clearable: false,
  name: "",
  ariaLabel: "",
  placement: "bottom-start",
  popperClass: "",
  teleported: false,
  fitInputWidth: false,
  hideLoading: false,
  triggerOnFocus: true,
  highlightFirstItem: false,
  selectWhenUnmatched: false
})
const emit = defineEmits(["update:modelValue", "input", "change", "focus", "blur", "clear", "select"])

const attrs = useAttrs()
const rawAttrs = useRawAttrs()
const disabled = useFormDisabled()
const ns = useNamespace("autocomplete")

const inputRef = ref<InputInstance>()
const regionRef = ref<HTMLElement>()
const popperRef = ref<TooltipInstance>()
const listboxRef = ref<HTMLElement>()

let readonly = false
let ignoreFocusEvent = false
const suggestions = ref<Record<string, any>[]>([])
const highlightedIndex = ref(-1)

const dropdownWidth = ref("")
const activated = ref(false)
const suggestionDisabled = ref(false)
const loading = ref(false)

const listboxId = useId()
const styles = computed(() => rawAttrs.style as StyleValue)

const suggestionVisible = computed(() => {
  const isValidData = suggestions.value.length > 0
  return (isValidData || loading.value) && activated.value
})

const suggestionLoading = computed(() => !props.hideLoading && loading.value)

const refInput = computed<HTMLInputElement[]>(() => {
  if (inputRef.value) {
    return Array.from<HTMLInputElement>(inputRef.value.$el.querySelectorAll("input"))
  }
  return []
})

const onSuggestionShow = () => {
  if (suggestionVisible.value) {
    dropdownWidth.value = `${inputRef.value!.$el.offsetWidth}px`
  }
}

const getData = async (queryString: string) => {
  if (suggestionDisabled.value) return

  const cb = (suggestionList: Record<string, any>[]) => {
    loading.value = false
    if (suggestionDisabled.value) return

    if (isArray(suggestionList)) {
      suggestions.value = suggestionList
      highlightedIndex.value = props.highlightFirstItem ? 0 : -1
    }
  }

  loading.value = true
  if (isArray(props.fetchSuggestions)) {
    cb(props.fetchSuggestions)
  } else {
    const result = await props.fetchSuggestions(queryString, cb)
    if (isArray(result)) cb(result)
  }
}

const handleInput = (value: string) => {
  const valuePresented = !!value

  emit(INPUT_EVENT, value)
  emit(UPDATE_MODEL_EVENT, value)

  suggestionDisabled.value = false
  activated.value ||= valuePresented

  if (!props.triggerOnFocus && !value) {
    suggestionDisabled.value = true
    suggestions.value = []
    return
  }

  getData(value)
}

const handleMouseDown = (event: MouseEvent) => {
  if (disabled.value) return
  if (
    (event.target as HTMLElement)?.tagName !== "INPUT" ||
    refInput.value.includes(document.activeElement as HTMLInputElement)
  ) {
    activated.value = true
  }
}

const handleChange = (value: string) => {
  emit(CHANGE_EVENT, value)
}

const handleFocus = (evt: FocusEvent) => {
  if (!ignoreFocusEvent) {
    activated.value = true
    emit("focus", evt)

    if (props.triggerOnFocus && !readonly) {
      getData(String(props.modelValue))
    }
  } else {
    ignoreFocusEvent = false
  }
}

const handleBlur = (event: FocusEvent) => {
  setTimeout(() => {
    if (popperRef.value?.isFocusInsideContent()) {
      ignoreFocusEvent = true
      return
    }
    activated.value && close()
    emit("blur", event)
  })
}

const handleClear = () => {
  activated.value = false
  emit(UPDATE_MODEL_EVENT, "")
  emit("clear")
}

const handleKeyEnter = async () => {
  if (suggestionVisible.value && highlightedIndex.value >= 0 && highlightedIndex.value < suggestions.value.length) {
    handleSelect(suggestions.value[highlightedIndex.value])
  } else if (props.selectWhenUnmatched) {
    emit("select", { value: props.modelValue })
    suggestions.value = []
    highlightedIndex.value = -1
  }
}

const handleKeyEscape = (evt: Event) => {
  if (suggestionVisible.value) {
    evt.preventDefault()
    evt.stopPropagation()
    close()
  }
}

const close = () => {
  activated.value = false
}

const focus = () => {
  inputRef.value?.focus()
}

const blur = () => {
  inputRef.value?.blur()
}

const handleSelect = async (item: any) => {
  emit(INPUT_EVENT, item[props.valueKey])
  emit(UPDATE_MODEL_EVENT, item[props.valueKey])
  emit("select", item)
  suggestions.value = []
  highlightedIndex.value = -1
}

const highlight = (index: number) => {
  if (!suggestionVisible.value || loading.value) return

  if (index < 0) {
    highlightedIndex.value = -1
    return
  }

  if (index >= suggestions.value.length) {
    index = suggestions.value.length - 1
  }
  const suggestion = regionRef.value!.querySelector(`.${ns.be("suggestion", "wrap")}`)!
  const suggestionList = suggestion.querySelectorAll<HTMLElement>(`.${ns.be("suggestion", "list")} li`)!
  const highlightItem = suggestionList[index]
  const scrollTop = suggestion.scrollTop
  const { offsetTop, scrollHeight } = highlightItem

  if (offsetTop + scrollHeight > scrollTop + suggestion.clientHeight) {
    suggestion.scrollTop += scrollHeight
  }
  if (offsetTop < scrollTop) {
    suggestion.scrollTop -= scrollHeight
  }
  highlightedIndex.value = index
}

onClickOutside(listboxRef, () => {
  suggestionVisible.value && close()
})

defineExpose({
  highlightedIndex,
  activated,
  loading,
  inputRef,
  popperRef,
  suggestions,
  handleSelect,
  handleKeyEnter,
  focus,
  blur,
  close,
  highlight
})
</script>
