import axios from "@/interfaces/axios-instance"

/* ======================== 系统公告 ======================== */

// 获取系统公告列表
export function getSystemNoticeListApi(params: IPaginationRequestParams) {
  const { filters } = params
  return axios({
    method: "get",
    url: "/api/system/notices",
    params: {
      filters,
      sorts: "-createDateTime"
    }
  })
}

interface SystemNoticeData {
  title: string
  contentHtml: string
  sendDateTime: null | number
}

// 新增系统公告
export function addSystemNoticeApi(noticeData: SystemNoticeData) {
  return axios({
    method: "post",
    url: "/api/system/notices",
    data: noticeData
  })
}

// 编辑系统公告
export function editSystemNoticeApi(noticeWsid: string, noticeData: SystemNoticeData) {
  return axios({
    method: "put",
    url: `/api/system/notices/${noticeWsid}`,
    data: noticeData
  })
}

// 删除系统公告
export function deleteSystemNoticeApi(noticeWsid: string) {
  return axios({
    method: "delete",
    url: `/api/system/notices/${noticeWsid}`
  })
}

// 获取系统公告内容详情（暂未使用）
export function getSystemNoticeDetailApi(noticeWsid: string) {
  return axios({
    method: "get",
    url: `/api/system/notices/${noticeWsid}`
  })
}

/* ======================== 登录日志 ======================== */

// 获取登录日志
export function getLoginLogApi(params: IPaginationRequestParams) {
  const { filters } = params
  return axios({
    method: "get",
    url: "/api/system/logs/login",
    params: {
      filters,
      sorts: "-operationDateTime"
    }
  })
}

// 获取操作类型选项列表
export function getOperationTypeApi() {
  return axios
    .request<IResponseData>({
      method: "get",
      url: "/api/system/logs/business/types"
    })
    .then(res => res.data?.data ?? [])
}

// 获取操作日志
export function getOperationLogApi(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: "/api/system/logs/business",
    params: {
      ...params,
      sorts: "-operationDateTime"
    }
  })
}

// 获取审批流程配置列表
export function getApprovalProcessListApi(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: "/api/system/system-flow",
    params: {
      ...params,
      sorts: "-createdDatetime"
    }
  })
}

// 删除审批流程
export function deleteApprovalProcessApi(processId: string) {
  return axios({
    method: "delete",
    url: `/api/system/system-flow/${processId}`
  })
}

// 修改审批流程状态
export function changeApprovalProcessStatusApi(processId: string, status: string) {
  const formData = new FormData()
  formData.append("status", status)
  return axios({
    method: "post",
    url: `/api/system/system-flow/changeStatus/${processId}`,
    data: formData
  })
}

// 新建和编辑流程配置参数
export interface UpdateApprovalProcessParams {
  id?: string // 新建时不传，编辑时传
  flowName: string
  flowType: string
  applyDeptCode: string
  applyDeptName: string
  flowDefine: any
}

// 新建/编辑审批流程
export function updateApprovalProcessApi(data: UpdateApprovalProcessParams) {
  return axios({
    method: "post",
    url: `/api/system/system-flow/addOrUpdate`,
    data
  })
}

/* ======================== 综合查询 ======================== */

// 综合查询--分页数据
export function getIntegratedQueryListApi(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/document/comprehensive-query/paging`,
    params
  })
}

// 获取业务流程配置
export function getBusinessProcessConfigApi() {
  return axios({
    method: "get",
    url: "/api/system/system-flow-bns-node"
  })
}

interface BusinessProcessConfigData {
  businessNodeCode: string //业务节点编号
  status: string // 状态 ，DISABLE:禁用，ENABLE:启用
  nextNodeCode: string // 下一个节点编号
  beforeNodeCode: string // 上一个节点编号
  configs: Array<{
    businesskey: string
    value: string // 数据值
    [key: string]: any
  }>
}

// 更新业务流程配置
export function updateBusinessProcessConfigApi(data: BusinessProcessConfigData) {
  return axios({
    method: "post",
    url: "/api/system/system-flow-bns-node",
    data
  })
}

// 查询流程里部门负责人
export function getDeptAdminApi() {
  return axios({
    method: "get",
    url: `/api/system/system-flow/role/dept-admin`
  })
}

/* ======================== 消息推送 ======================== */
export function getBagPushConfigApi(params: Record<string, any>) {
  return axios({
    method: "get",
    url: "/api/document/bag/push-config",
    params: params
  })
}

// 新增推送消息

export function addBagPushConfigApi(data: Record<string, any>) {
  return axios({
    method: "post",
    url: "/api/document/bag/push-config",
    data
  })
}

// 编辑推送消息
export function editBagPushConfigApi(data: Record<string, any>) {
  return axios({
    method: "put",
    url: `/api/document/bag/push-config`,
    data
  })
}

// 改变推送消息状态
export function changeBagPushConfigStatusApi(documentPushConfigWsid: string, status: string) {
  return axios({
    method: "put",
    url: `/api/document/bag/push-config/${documentPushConfigWsid}`,
    params: {
      status
    }
  })
}

// 删除指定推送消息
export function deleteBagPushConfigApi(documentPushConfigWsid: string) {
  return axios({
    method: "delete",
    url: `/api/document/bag/push-config/${documentPushConfigWsid}`
  })
}

// 获取推送设置枚举
export function getBagPushConfigEnumApi() {
  return axios.get("/api/document/bag/push-config/enums")
}

// 查询指定推送消息详情
export function getBagPushConfigDetailApi(documentPushConfigWsid: string) {
  return axios({
    method: "get",
    url: `/api/document/bag/push-config/${documentPushConfigWsid}`
  })
}

/* ======================== 表单配置 ======================== */

// 分页查询表单配置列表
export function getFormConfigsApi(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: "/api/catalog/form-configs",
    params: params
  })
}

interface IAddFormConfigsData {
  name: string
  appTypeCode: string
  appTypeName: string
  catalogFormConfigWsid?: string
}

// 新增表单配置
export function addFormConfigsApi(data: IAddFormConfigsData) {
  return axios({
    method: "post",
    url: "/api/catalog/form-configs",
    data
  })
}

// 复制表单配置
export function copyFormConfigsApi(data: IAddFormConfigsData) {
  return axios({
    method: "post",
    url: "/api/catalog/form-configs/copy",
    data
  })
}

// 删除指定表单配置
export function deleteFormConfigsApi(wsid: string) {
  return axios({
    method: "delete",
    url: `/api/catalog/form-configs/${wsid}`
  })
}

// 改变推送消息状态
export function modifyFormConfigsApi(catalogFormConfigWsid: string, status: string) {
  return axios({
    method: "put",
    url: `/api/catalog/form-configs/update-status`,
    data: {
      catalogFormConfigWsid,
      status
    }
  })
}
