<template>
  <PageContainer separate>
    <!-- 头部搜索 -->
    <template #search>
      <TabsRouter :tabs-config="medicalTaskTabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
        />

        <VisitIdFormItem
          v-model:input-value="searchFormState.visitIdCount"
          v-model:select-value="searchFormState.visitIdSymbol"
          label="住院次数"
        />

        <CountRangeFormItem
          v-model:from="searchFormState.inHospitalDaysFrom"
          v-model:to="searchFormState.inHospitalDaysTo"
          :min="0"
          label="住院天数"
          unit="天"
        />

        <el-form-item label="主治医师">
          <el-input v-model="searchFormState.doctorName" />
        </el-form-item>

        <DaterangeFormItem v-model:model-value="searchFormState.outHospitalDatetime" label="出院时间" />

        <PatientLabelFormItem v-model="searchFormState.patientLabel" />
      </SearchContainer>
    </template>

    <!-- 已质控任务列表 -->
    <template #table>
      <CommonTable
        :table-columns="DeptTaskQualityControlledColumns"
        :request-api="getDistributeQcTaskListApi"
        :request-params="{ qcType: QualityControlTypeEnum.CROSS_QC_MEDICAL, ...searchParams }"
      >
        <!-- 患者姓名 -->
        <template #patientName="{ row }">
          <div style="display: flex; align-items: center; flex-wrap: wrap">
            <span style="margin: 0 16px">{{ row.patientName }}</span>
            <PatientLabelTag :row="row" />
          </div>
        </template>
        <template #qcStatus="{ row }">
          <el-tag :type="getQcStatusTagType(row.qcStatus)">{{ getQcStatus(row.qcStatus) }}</el-tag>
        </template>
        <template #inHospitalDatetime="{ row }">
          <span>{{ formatDatetime(row.inHospitalDatetime) }}</span>
        </template>
        <template #outHospitalDatetime="{ row }">
          <span>{{ formatDatetime(row.outHospitalDatetime) }}</span>
        </template>
        <template #qcDatetime="{ row }">
          {{ formatDatetime(row.qcDatetime) }}
        </template>
        <template #operation="{ row }">
          <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.QC)" @click="qualityControlItem(row)">
            查看
          </TableButton>
          <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.Track)" @click="openQcTraceDrawer(row)">
            质控轨迹
          </TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>

  <!-- 封存病案密钥 -->
  <SealDialog ref="sealDialogRef" :selected-row="medicalRecord" :confirm-callback="confirmOperation" />

  <!-- 质控轨迹抽屉 -->
  <TraceDrawer v-model:visible="qcTraceVisible" :inp-no="activeInpNo" />
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import { useRouter } from "vue-router"
import { cloneDeep } from "lodash-es"
import {
  CommonTable,
  DaterangeFormItem,
  PageContainer,
  SearchContainer,
  TableButton,
  PatientFilterFormItem,
  VisitIdFormItem,
  CountRangeFormItem,
  PatientLabelTag,
  PatientLabelFormItem
} from "@/base-components"
import { SealDialog, TabsRouter, TraceDrawer } from "@/page-components"
import { MenuOperationEnum, QualityControlTypeEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { getDistributeQcTaskListApi } from "@/interfaces"
import { useUserStore } from "@/stores"
import { encryptStr, formatDatetime, getQcStatusTagType, getQcStatus } from "@/utils"
import { DeptTaskQualityControlledColumns, medicalTaskTabsRouterList } from "../config"
const router = useRouter()

const { hasOperationPermission } = useUserStore()
const menuId = "/department-quality-control/medical-task"

/* ====================搜索==================== */

const initFormData = {
  patientFilterValue: "",
  patientFilterProp: "patientName",
  outHospitalDeptWsid: "",
  outHospitalDatetime: "",
  visitIdSymbol: "",
  visitIdCount: "",
  doctorName: "",
  inHospitalDaysFrom: "",
  inHospitalDaysTo: "",
  death: "",
  patientLabel: ""
}

const searchFormState = reactive(cloneDeep(initFormData))

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState, "qcStatus!=WAIT")

/* ====================表格操作==================== */
const medicalRecord = ref()
const sealDialogRef = ref()
// 质控
const qualityControlItem = row => {
  medicalRecord.value = row
  if (sealDialogRef.value) sealDialogRef.value.checkSealed()
}

// 检查封存之后的回调
const confirmOperation = () => {
  const query = {
    inpNo: medicalRecord.value?.inpNo,
    wsid: medicalRecord.value?.wsid,
    urlType: QualityControlTypeEnum.CROSS_QC_MEDICAL
  }
  if (sealDialogRef.value.secretKey) {
    query["secretKey"] = encryptStr(sealDialogRef.value.secretKey)
  }
  router.push({
    path: "/department-quality-control/medical-task/view",
    query: query
  })
}

/* ======================== 质控轨迹抽屉 ======================== */
const qcTraceVisible = ref(false)
const activeInpNo = ref("")

function openQcTraceDrawer(row) {
  qcTraceVisible.value = true
  activeInpNo.value = row.inpNo
}
</script>
