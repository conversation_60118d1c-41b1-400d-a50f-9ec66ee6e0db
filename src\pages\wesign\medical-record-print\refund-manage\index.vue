<template>
  <PageContainer separate>
    <template #search>
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
          :filter-prop-options="refundManageOptions"
        />

        <el-form-item label="订单号">
          <el-input v-model="searchFormState.orderCode" style="width: 240px" placeholder="请输入订单号" clearable />
        </el-form-item>

        <DaterangeFormItem v-model:model-value="searchFormState.refundApplyDatetime" label="申请时间" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="refundManageTableRef"
        table-id="refundManageTableIdent"
        :table-columns="refundColumns"
        :request-api="getRefundApplyList"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <template #header>
          <div style="margin-bottom: 12px">
            <BatchOperationButton type="primary" :disabled="!selectedRows.length" :icon="Money" @click="batchRefund">
              批量退款
            </BatchOperationButton>
          </div>
        </template>
        <template #operation="{ row }">
          <TableButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Refund)"
            :disabled="row.refundStatus !== 'WAIT_APPROVE'"
            :tooltip="`${row.refundStatusStr}订单不可点击`"
            @click="refundDialogConfirm(row, true)"
          >
            退款
          </TableButton>
          <TableButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Reject)"
            :disabled="row.refundStatus !== 'WAIT_APPROVE'"
            :tooltip="`${row.refundStatusStr}订单不可点击`"
            @click="handle(row, false)"
          >
            驳回
          </TableButton>
          <TableButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Refund)"
            :disabled="row.refundStatus !== 'REFUND_FAIL'"
            :tooltip="`${row.refundStatusStr}订单不可点击`"
            @click="refund(row)"
          >
            重新退款
          </TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>

  <DialogContainer
    v-model:visible="visible"
    title="驳回申请"
    :width="450"
    :cancel-callback="resetRefundDialog"
    :confirm-callback="refundDialogConfirm"
  >
    <el-form-item label="请输入驳回原因：">
      <el-input v-model="refundApplyApproveData.reason" type="textarea" maxlength="100" />
    </el-form-item>
  </DialogContainer>
</template>

<script setup lang="ts">
import { reactive, ref, computed } from "vue"
import { Money } from "@element-plus/icons-vue"
import {
  SearchContainer,
  CommonTable,
  DialogContainer,
  TableButton,
  BatchOperationButton,
  DaterangeFormItem,
  PatientFilterFormItem,
  PageContainer
} from "@/base-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { useUserStore } from "@/stores"
import { formatDatetime, Message, extractErrorMsg, toastError } from "@/utils"
import { getRefundApplyList, refundApplyApi, refundApplyApprove } from "../interface"
import { refundManageOptions } from "../print-config"
import { refundColumns, menuId } from "./config"

const { hasOperationPermission } = useUserStore()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  patientFilterValue: "",
  patientFilterProp: "queryName",
  orderCode: "",
  refundApplyDatetime: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/* ======================== 表格相关方法 ======================== */

const refundManageTableRef = ref<InstanceType<typeof CommonTable>>()

// 表格中被选中项
const selectedRows = computed(() => refundManageTableRef.value?.tableState?.selectedRows ?? [])

const dataCallback = (data: Array<Record<string, any>>) => {
  return data.map(item => ({
    ...item,
    payDatetime: formatDatetime(item.payDatetime),
    refundApplyDatetime: formatDatetime(item.refundApplyDatetime)
    // patientOutHospitalDatetime: formatDatetime(item.patientOutHospitalDatetime)
  }))
}

const visible = ref(false)

const refundApplyApproveData = reactive({
  wsid: "",
  status: false,
  reason: ""
})

// 重置
const resetRefundDialog = () => {
  refundApplyApproveData.wsid = ""
  refundApplyApproveData.status = false
  refundApplyApproveData.reason = ""
  visible.value = false
}

const handle = (row, status: boolean) => {
  visible.value = true
  refundApplyApproveData.wsid = row.wsid
  refundApplyApproveData.status = status
}

// 确认提交
const refundDialogConfirm = (row?: any, status?: any) => {
  // 退款确认
  if (status) {
    refundApplyApproveData.wsid = row.wsid
    refundApplyApproveData.status = status
  }
  // 驳回确认
  if (!refundApplyApproveData.status) {
    if (refundApplyApproveData.reason === "") {
      Message.warning("请输入驳回原因")
      return
    }
  }

  refundApplyApprove({
    wsid: refundApplyApproveData.wsid,
    status: refundApplyApproveData.status,
    reason: refundApplyApproveData.status ? "" : refundApplyApproveData.reason
  })
    .then(() => {
      refundManageTableRef.value?.refreshTableData()
      Message.success("操作成功")
      visible.value = false
    })
    .catch(err => {
      toastError(err)
    })
}

//重新退款
const refund = row => {
  refundApplyApi(row.wsid)
    .then(() => {
      refundManageTableRef.value?.refreshTableData()
      Message.success("操作成功")
    })
    .catch(err => {
      Message.error(extractErrorMsg(err, "操作失败"))
    })
}

// 批量退款
const batchRefund = () => {
  refundApplyApprove({
    wsid: selectedRows.value.map(item => item.wsid).join(","),
    status: true,
    reason: ""
  })
    .then(() => {
      refundManageTableRef.value?.refreshTableData()
      Message.success("操作成功")
    })
    .catch(err => {
      Message.error(extractErrorMsg(err, "操作失败"))
    })
}
</script>
