import axios from "@/interfaces/axios-instance"

/**
 * @method GET
 * @desc   用户管理-左侧统计数据
 */
export function getUserStatisticsApi(obj) {
  const { item = "" } = obj
  return axios({
    method: "get",
    url: "/api/hospital/users/statistica",
    params: {
      item
    }
  })
}

/**
 * @method GET
 * @desc   用户管理-excel模板下载
 */
export function downloadTemplatesExcelApi() {
  return axios({
    method: "get",
    url: "/api/hospital/users/import-template",
    responseType: "arraybuffer"
  })
}

/**
 * @method POST
 * @desc   用户管理-excel导入
 */
export function importUserApi(obj) {
  const { file = {} } = obj
  const formData = new FormData()
  formData.append("file", file)
  return axios({
    method: "post",
    url: "/api/hospital/users/import",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  })
}

/**
 * @method PUT
 * @desc   用户管理-重置密码
 */
export function changeUserPasswordApi({ userWsid }) {
  return axios({
    method: "put",
    url: `/api/system/users/${userWsid}/reset-password`
  })
}

/**
 * @method PUT
 * @desc   用户信息-更改状态
 */
export function changeUserStatusApi(obj) {
  const { wsid = "", status = "" } = obj
  return axios({
    method: "put",
    url: `/api/hospital/users/${wsid}/change-status`,
    params: {
      status
    }
  })
}

/**
 * @method POST
 * @desc   用户信息-创建
 */
export function createUserInfoApi(data) {
  return axios({
    method: "post",
    url: `/api/hospital/users`,
    data: data
  })
}

/**
 * @method PUT
 * @desc   用户信息-编辑
 */
export function changeUserInfoApi(data) {
  return axios({
    method: "put",
    url: `/api/hospital/users/${data.wsid}`,
    data: data
  })
}

/**
 * @method POST
 * @desc   用户信息-添加编辑角色
 */
export function editUserInfoRoleApi(obj) {
  const { userWsid = "", item = [] } = obj
  return axios({
    method: "post",
    url: `/api/system/users/${userWsid}/roles`,
    data: item
  })
}

/**
 * @method GET
 * @desc   用户信息-获取用户关联角色
 */
export function getUserInfoRoleApi({ userWsid }) {
  return axios({
    method: "get",
    url: `/api/system/users/${userWsid}/roles`
  })
}

interface IManagerUpdateHolidayData {
  userWsid: string
  holidayStartTime: number | null
  holidayEndTime: number | null
}

/**
 * @method POST
 * @desc   安全中心--用户设置休假日期
 */
export function managerUpdateUserHolidayApi(data: IManagerUpdateHolidayData) {
  return axios({
    method: "post",
    url: "/api/system/users/manager-set-holiday",
    data
  })
}
