import axios from "@/interfaces/axios-instance"

// 获取病案签收列表
export const getSignForListApi = (data: IPaginationRequestParams) => {
  return axios({
    method: "get",
    url: `/api/document/bags/receipt-records`,
    params: data
  })
}

// 确认签收
export const confirmSignApi = (documentBagWsid: string) => {
  return axios({
    method: "post",
    url: `/api/document/bags/${documentBagWsid}/receipt`
  })
}

// 拒绝签收
export const rejectSignApi = (documentBagWsid: string, reason: string) => {
  return axios({
    method: "post",
    url: `/api/document/bags/${documentBagWsid}/reject-receipt`,
    data: { reason: reason }
  })
}
