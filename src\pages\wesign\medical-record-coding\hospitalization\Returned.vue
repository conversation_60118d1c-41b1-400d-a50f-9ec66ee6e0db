<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
        />

        <DepartmentFormItem v-model:model-value="searchFormState.outHospitalDeptWsid" label="出院科室" />

        <DaterangeFormItem v-model:model-value="searchFormState.outHospitalDatetime" label="出院时间" />

        <CommonInputFormItem v-model="searchFormState.attendingDoctor" label="主治医师"></CommonInputFormItem>

        <DaterangeFormItem v-model:model-value="searchFormState.returnTime" label="退回时间" />

        <PatientLabelFormItem v-model="searchFormState.patientLabel" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="returnedTableRef"
        table-id="returnedTableIdent"
        :table-columns="returnedControlColumns"
        :request-api="getReturnCatalogRecordApi"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <!-- 患者姓名 -->
        <template #patientName="{ row }">
          <div style="display: flex; align-items: center; flex-wrap: wrap">
            <span style="margin: 0 16px">{{ row.patientName }}</span>
            <PatientLabelTag :row="row" />
          </div>
        </template>
        <template #patientPrintCount="{ row }">
          {{ row.patientPrintCount > 0 ? "已打印" : "未打印" }}
        </template>
        <template #operation="{ row }">
          <TableButton @click="viewDetail(row)">详情</TableButton>
        </template>
      </CommonTable>
    </template>

    <!-- 详情 -->
    <DialogContainer v-model:visible="detailDialogVisible" title="详情" :width="550" :no-footer="true">
      <el-form label-position="left" label-width="120px">
        <el-form-item required label="限时修改时间：">
          <el-input disabled :value="selectedRow.limitedModifyTime"></el-input>
        </el-form-item>
        <el-form-item required label="退回原因：">
          <el-input disabled type="textarea" :value="selectedRow.reason"></el-input>
        </el-form-item>
      </el-form>
    </DialogContainer>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import {
  SearchContainer,
  PatientFilterFormItem,
  CommonInputFormItem,
  CommonTable,
  TableButton,
  DaterangeFormItem,
  DepartmentFormItem,
  PageContainer,
  PatientLabelTag,
  DialogContainer,
  PatientLabelFormItem
} from "@/base-components"
import { TabsRouter } from "@/page-components"
import { useTableSearch } from "@/hooks"
import { formatDatetime } from "@/utils"
import { getReturnCatalogRecordApi } from "../interface"
import { returnedControlColumns, tabsRouterList } from "./config"

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  patientFilterValue: "",
  patientFilterProp: "patientName",
  outHospitalDeptWsid: "",
  outHospitalDatetime: "",
  attendingDoctor: "",
  returnTime: "",
  patientLabel: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/* ======================== 表格相关方法 ======================== */

const dataCallback = (data: Array<Record<string, any>>) => {
  return data.map(item => ({
    ...item,
    inHospitalDatetime: formatDatetime(item.inHospitalDatetime),
    outHospitalDatetime: formatDatetime(item.outHospitalDatetime),
    returnTime: formatDatetime(item.returnTime),
    limitedModifyTime: formatDatetime(item.limitedModifyTime),
    patientTag: {
      death: item.death,
      daySurgery: item.daySurgery,
      nonMedicalLeave: item.nonMedicalLeave
    },
    hidePatientTag: [item.death, item.daySurgery, item.nonMedicalLeave].every(item => item === 0)
  }))
}

const returnedTableRef = ref()

const selectedRow = ref<Record<string, any>>({})
const detailDialogVisible = ref(false)
const viewDetail = row => {
  selectedRow.value = row
  detailDialogVisible.value = true
}
</script>
