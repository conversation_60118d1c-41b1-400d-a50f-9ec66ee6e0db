<template>
  <PageContainer>
    <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
      <CommonInputFormItem v-model="searchForm.menuName" label="菜单名称" />
    </SearchContainer>
    <div>
      <AddButton @click="addMenu(0)">新增</AddButton>
      <TooltipButton
        type="primary"
        tooltip="将当前已修改配置同步至数据库"
        :icon="h('i', { class: 'ri-upload-cloud-2-line' })"
        @click="handleResourceUpdate"
      >
        更新配置
      </TooltipButton>

      <el-button type="primary" plain @click="switchExpandAll(false)">全部折叠</el-button>

      <el-button type="primary" plain @click="switchExpandAll(true)">全部展开</el-button>

      <el-tooltip :disabled="selectedRows.length" content="请选择一条数据" placement="top">
        <el-button plain :disabled="!selectedRows.length" @click="batchDisableMenus(0)">禁用</el-button>
      </el-tooltip>

      <el-tooltip :disabled="selectedRows.length" content="请选择一条数据" placement="top">
        <el-button plain :disabled="!selectedRows.length" @click="batchDisableMenus(1)">启用</el-button>
      </el-tooltip>

      <!-- <TooltipButton
        type="primary"
        plain
        tooltip="将配置变更信息同步至数据库（适用于新功能上线）"
        :icon="h('i', { class: 'ri-arrow-up-down-line' })"
        @click="handleResourceSync"
      >
        同步配置
      </TooltipButton> -->

      <!-- <TooltipButton
        tooltip="将配置重置为初始状态"
        :icon="h('i', { class: 'ri-loop-right-line' })"
        @click="handleResourceReset"
      >
        重置配置
      </TooltipButton> -->
    </div>

    <TreeTable
      v-if="treeTableVIsible"
      ref="treeTable"
      :table-columns="menuConfigTableColumns"
      :default-expand-all="defaultExpandAll"
      :data="tableData"
      row-key="id"
    >
      <template #status="{ row }">
        <el-tag :type="row.status ? 'success' : 'danger'">
          {{ row.status ? "已启用" : "已禁用" }}
        </el-tag>
      </template>
      <template #level="{ row }">
        <span v-if="row.level === 0">模块</span>
        <span v-else>{{ row.level === 1 ? "一级菜单" : "二级菜单" }}</span>
      </template>
      <template #icon="{ row }">
        <i :class="row.icon"></i>
      </template>
      <template #operation="{ row }">
        <TableButton @click="editMenu(row)">编辑</TableButton>
        <!-- <TableButton v-if="row.level === 2" @click="editMenu(row)">删除</TableButton> -->
        <TableButton @click="switchMenuStatus(row)">{{ row.status ? "禁用" : "启用" }}</TableButton>
        <!-- <TableButton v-if="row.level === 1" @click="showIconDialog(row)">设置图标</TableButton> -->
        <!-- 上移下移操作 -->
        <template v-if="row.level === 0 && !searchForm.searchResult">
          <TableButton v-if="row.index !== 0" tooltip="当前菜单已是第一项" @click="moveMenu('up', row)">
            上移
          </TableButton>
          <TableButton
            v-if="(row.level === 1 || row.level === 0) && row.index !== menuConfigData.length - 1"
            tooltip="当前菜单已是最后一项"
            @click="moveMenu('down', row)"
          >
            下移
          </TableButton>
        </template>
        <template v-if="row.level !== 0 && !searchForm.searchResult">
          <TableButton v-if="row.index !== 0" tooltip="当前菜单已是第一项" @click="moveMenu('up', row)">
            上移
          </TableButton>
          <TableButton v-if="availableDown(row)" tooltip="当前菜单已是最后一项" @click="moveMenu('down', row)">
            下移
          </TableButton>
        </template>
        <TableButton v-if="row.level === 0" @click="addMenu(1, row)">添加菜单</TableButton>
        <TableButton v-if="row.level === 1" @click="addMenu(2, row)">添加子菜单</TableButton>
        <TableButton :disabled="!row.operations.length" tooltip="当前菜单无关联操作" @click="openOperationDialog(row)">
          设置操作权限
        </TableButton>
      </template>
    </TreeTable>

    <!-- 设置图标弹窗 -->
    <DialogContainer
      v-model:visible="iconDialogState.visible"
      title="设置图标"
      :confirm-callback="changeIcon"
      :width="510"
    >
      <div class="menu-list">
        <div
          v-for="item in menuIconsList"
          :key="item.class"
          class="menu-item"
          :class="iconDialogState.selectedIcon === item.class ? 'check-item' : ''"
          @click="chooseIcon(item)"
        >
          <i :class="item.class"></i>
          <i v-if="iconDialogState.selectedIcon === item.class" class="ri-check-line check-status"></i>
        </div>
      </div>
    </DialogContainer>

    <!-- 设置操作权限弹窗 -->
    <DialogContainer v-model:visible="operationDialogState.visible" title="设置操作权限" :width="780" :no-footer="true">
      <BaseTable class="operations-table" :columns="operationColumns" :data="operationDialogState.clickRow.operations">
        <template #status="{ row }">
          <el-tag :type="row.status ? 'success' : 'danger'">
            {{ row.status ? "已启用" : "已禁用" }}
          </el-tag>
        </template>
        <template #operation="{ row }">
          <TableButton @click="switchOperationsStatus(row)">{{ row.status ? "禁用" : "启用" }}</TableButton>
        </template>
      </BaseTable>
    </DialogContainer>

    <!-- 新增菜单弹窗 -->
    <DialogContainer
      v-model:visible="menuVisible"
      :title="isAddMenu ? '新增' + addMenuTitle[addMenuLevel] : '编辑' + addMenuTitle[addMenuLevel]"
      :width="700"
      :confirm-callback="menuConfirm"
      :confirm-loading="menuConfirmLoading"
    >
      <el-form
        ref="menuFormRef"
        :model="menuFormData"
        label-position="right"
        :label-width="120"
        label-suffix="："
        :rules="formRules"
      >
        <el-form-item label="菜单名称" prop="name">
          <el-input v-model.trim="menuFormData.name" placeholder="请输入菜单名称" />
        </el-form-item>

        <!-- <el-form-item v-if="addMenuLevel === 0" label="菜单路径" prop="code">
          <el-input v-model.trim="menuFormData.code" placeholder="请输入菜单路径" />
        </el-form-item> -->

        <el-form-item v-if="isAddMenu" label="菜单路径" prop="code">
          <el-input v-model.trim="menuFormData.code" placeholder="请输入菜单路径" />
        </el-form-item>

        <!-- 
        <el-form-item
          v-if="selectRow?.level === 2 || (selectRow?.level === 1 && !selectRow?.children.length)"
          label="文件路径"
          prop="filePath"
        >
          <el-input v-model.trim="menuFormData.filePath" placeholder="请输入文件路径" />
        </el-form-item> -->

        <!-- 一级菜单 -->
        <el-form-item v-if="addMenuLevel === 1" label="上级模块" prop="parentCode">
          <el-select v-model="menuFormData.parentCode" clearable placeholder="请选择上级模块">
            <el-option
              v-for="option in parentGroupIdOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <!-- 二级菜单 -->
        <el-form-item v-if="addMenuLevel === 2" label="上级模块" prop="parentGroupId">
          <el-select v-model="menuFormData.parentGroupId" clearable placeholder="请选择上级模块" @change="selectGroup">
            <el-option
              v-for="option in parentGroupIdOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="addMenuLevel === 2" label="上级菜单" prop="parentCode">
          <el-select v-model="menuFormData.parentCode" clearable placeholder="请选择上级模块">
            <el-option
              v-for="option in parentCodeList"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="addMenuLevel === 1" label="图标" prop="iconCode">
          <el-input placeholder="点击右侧按钮选择图标" :value="menuFormData.iconCode" class="input-with-select">
            <template #append>
              <el-button :icon="Tools" @click="setIcon" />
            </template>
          </el-input>
        </el-form-item>

        <!-- 是否隐藏菜单 -->
        <el-form-item v-if="addMenuLevel !== 0" label="隐藏菜单" prop="hideMenu">
          <el-radio-group v-model="menuFormData.hideMenu">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </DialogContainer>
  </PageContainer>
</template>

<script setup lang="ts">
import { ref, computed, reactive, h, toRaw, onMounted, nextTick } from "vue"
import { cloneDeep, set } from "lodash-es"
import { Tools } from "@element-plus/icons-vue"
import {
  PageContainer,
  TooltipButton,
  TreeTable,
  TableButton,
  DialogContainer,
  BaseTable,
  AddButton,
  CommonInputFormItem,
  SearchContainer
} from "@/base-components"
import {
  updateSystemResourceConfig,
  getSystemResourceConfig,
  addMenuApi,
  editMenuApi,
  updateMenuStatusApi
} from "@/interfaces"
import { initMenuData, allMenuData } from "@/pages/wesign/sub-routers"
import { useSystemStore } from "@/stores"
import { SystemAlert, SystemPrompt, toastError } from "@/utils"
import { menuConfigTableColumns, menuIconsList, operationColumns, formRules } from "./config"

const systemStore = useSystemStore()

const menuConfigData = ref(cloneDeep(systemStore.menus))

const menuCount = computed(() => menuConfigData.value.length)

const searchForm = reactive({
  menuName: "",
  searchResult: false
})
const tableData = ref<Array<Record<string, any>>>([])

const handleQuery = () => {
  if (!searchForm.menuName) return
  tableData.value = []
  menuConfigData.value.forEach(group => {
    if (group.name.includes(searchForm.menuName)) {
      tableData.value.push(group)
    }
    group?.children?.forEach(level1 => {
      if (level1.name.includes(searchForm.menuName)) {
        tableData.value.push(level1)
      }
      level1?.children?.forEach(level2 => {
        if (level2.name.includes(searchForm.menuName)) {
          tableData.value.push(level2)
        }
      })
    })
  })
  searchForm.searchResult = true
}

const handleReset = () => {
  searchForm.menuName = ""
  tableData.value = cloneDeep(menuConfigData.value)
  searchForm.searchResult = false
}

// 切换菜单状态
function switchMenuStatus(row) {
  const nextStatus = row.status ? 0 : 1
  const nextStatusText = row.status ? "禁用" : "启用"
  SystemPrompt(`是否确认${nextStatusText}菜单"${row.name}"，该菜单关联子菜单及功能将会同步${nextStatusText}?`).then(
    () => {
      row.status = nextStatus
      row.children.forEach(item => {
        item.status = nextStatus
        if (item.children.length) {
          item.children.forEach(child => {
            child.status = nextStatus
          })
        }
      })
      // 若二级菜单全部禁用，一级菜单也要随之禁用；若二级菜单有一项启用，一级菜单也要随之启用
      tableData.value.forEach(group => {
        const enableLevel1 = group.children.filter(item => item.status)
        if (group.children.length) {
          if (enableLevel1.length) group.status = 1
          else group.status = 0
        }
        group.children.forEach(level1 => {
          const enabledCLevel2 = level1.children.filter(item => item.status)
          if (level1.children.length) {
            if (enabledCLevel2.length) level1.status = 1
            else level1.status = 0
          }
        })
      })
    }
  )
}

// 一级、二级菜单是否允许下移
const availableDown = row => {
  if (row.level === 1) {
    const level0Node = menuConfigData.value.find(item => item.children.find(level1 => level1.id === row.id))
    return row.index !== level0Node?.children.length - 1 && level0Node?.children.length !== 1
  } else {
    let level1Node
    menuConfigData.value.forEach(item =>
      item.children.forEach(level1 => {
        if (level1?.children?.find(level2 => level2.id === row.id)) {
          level1Node = level1
        }
      })
    )
    return row.index !== level1Node?.children.length - 1 && level1Node?.children.length !== 1
  }
}

// 菜单顺序调整
function moveMenu(type: "up" | "down", row) {
  if (row.level === 0) {
    const currentIndex = menuConfigData.value.findIndex(item => item.id === row.id)
    const targetIndex = type === "up" ? currentIndex - 1 : currentIndex + 1
    menuConfigData.value.splice(currentIndex, 1)
    menuConfigData.value.splice(targetIndex, 0, row)
    menuConfigData.value.forEach((item, index) => {
      item.index = index
    })
  } else if (row.level === 1) {
    menuConfigData.value?.forEach(group => {
      const currentIndex = group.children.findIndex(item => item.id === row.id)
      const targetIndex = type === "up" ? currentIndex - 1 : currentIndex + 1
      if (group.id === row.parentCode) {
        group.children.splice(currentIndex, 1)
        group.children.splice(targetIndex, 0, row)
        group.children.forEach((item, index) => {
          item.index = index
        })
      }
    })
  } else {
    menuConfigData.value?.forEach(group => {
      group?.children?.forEach(level1 => {
        const currentIndex = level1.children.findIndex(item => item.id === row.id)
        const targetIndex = type === "up" ? currentIndex - 1 : currentIndex + 1
        if (level1.id === row.parentCode) {
          level1.children.splice(currentIndex, 1)
          level1.children.splice(targetIndex, 0, row)
          level1.children.forEach((item, index) => {
            item.index = index
          })
        }
      })
    })
  }
  console.log(`output->menuConfigData.value`, menuConfigData.value)
  tableData.value = cloneDeep(menuConfigData.value)
}

/*========================= 设置图标 =========================*/

const iconDialogState = reactive({
  visible: false,
  activeMenu: null as null | Record<string, any>,
  selectedIcon: ""
})

// 在弹窗内选择图标
function chooseIcon(icon) {
  iconDialogState.selectedIcon = icon.class
}

// 打开弹窗
function showIconDialog(row) {
  iconDialogState.visible = true
  iconDialogState.selectedIcon = row.icon
  iconDialogState.activeMenu = row
}

// 点击确认修改图标
function changeIcon() {
  if (iconDialogState.activeMenu) iconDialogState.activeMenu.icon = iconDialogState.selectedIcon
  if (menuVisible.value) menuFormData.iconCode = iconDialogState.selectedIcon
  iconDialogState.visible = false
}

/*========================= 设置操作权限弹窗 =========================*/
const operationDialogState = reactive({
  visible: false,
  clickRow: {} as Record<string, any>
})

// 打开操作权限弹窗
function openOperationDialog(row) {
  operationDialogState.clickRow = row
  operationDialogState.visible = true
}

// 修改操作权限
function switchOperationsStatus(row) {
  SystemPrompt(`是否确认${row.status ? "禁用" : "启用"}操作"${row.name}"`).then(() => {
    row.status = row.status ? 0 : 1
  })
}
/*========================= table 顶部操作 =========================*/

const defaultExpandAll = ref(false)
const treeTableVIsible = ref(true)
const treeTable = ref()

const lastSelectData = ref([] as any)

// 展开/收起所有
const switchExpandAll = val => {
  lastSelectData.value = cloneDeep(selectedRows.value)
  defaultExpandAll.value = val
  treeTableVIsible.value = false
  nextTick(() => {
    treeTableVIsible.value = true
    nextTick(() => {
      treeTable.value?.setCheckedKeys(lastSelectData?.value)
    })
  })
}

const selectedRows = computed(() => treeTable.value?.tableState?.selectedRows ?? [])

// 批量禁用/启用
const batchDisableMenus = async status => {
  SystemPrompt(`是否确认${status === 0 ? "禁用" : "启用"}已勾选菜单`).then(async () => {
    try {
      await updateMenuStatusApi({
        codes: selectedRows.value?.map(item => item.path) ?? [],
        status
      })
      SystemAlert("更新配置成功，需重新登录后生效", "success")
      await getMenuData()
    } catch (err: any) {
      toastError(err, "操作失败")
    }
  })
}

// 更新配置
function handleResourceUpdate() {
  updateSystemResourceConfig(JSON.stringify(tableData.value))
    .then(() => {
      SystemAlert("更新配置成功，需重新登录后生效", "success")
      systemStore.$patch({ menus: cloneDeep(toRaw(tableData.value)) })
    })
    .catch(err => {
      toastError(err, "更新配置失败")
    })
}

// 重置配置
function handleResourceReset() {
  updateSystemResourceConfig(JSON.stringify(allMenuData))
    .then(() => {
      SystemAlert("重置配置成功，需重新登录后生效", "success")
      systemStore.$patch({ menus: cloneDeep(allMenuData) })
      menuConfigData.value = cloneDeep(allMenuData)
    })
    .catch(err => {
      toastError(err, "重置配置失败")
    })
}

// 同步配置
function handleResourceSync() {
  updateSystemResourceConfig(JSON.stringify(initMenuData))
    .then(() => {
      SystemAlert("同步配置成功，需重新登录后生效", "success")
      systemStore.$patch({ menus: cloneDeep(initMenuData) })
      menuConfigData.value = cloneDeep(initMenuData)
    })
    .catch(err => {
      toastError(err, "同步配置失败")
    })
}

const getMenuData = async () => {
  const jsonData = (await getSystemResourceConfig())?.data?.data ?? ""
  if (jsonData) {
    const resourceConfig = JSON.parse(jsonData)

    systemStore.$patch({ menus: resourceConfig })
    menuConfigData.value = cloneDeep(resourceConfig)
    tableData.value = cloneDeep(resourceConfig)
  } else {
    updateSystemResourceConfig(JSON.stringify(allMenuData))
    systemStore.$patch({ menus: cloneDeep(allMenuData) })
    menuConfigData.value = cloneDeep(allMenuData)
  }
  tableData.value = cloneDeep(menuConfigData.value)
}

onMounted(async () => {
  await getMenuData()
})

/*========================= 新增菜单或分组 =========================*/
// 模块分组列表
const parentGroupIdOptions = computed(() => {
  return cloneDeep(menuConfigData.value)
    .filter(item => item.level === 0)
    .map(item => {
      return {
        label: item.name,
        value: item.id
      }
    })
})

const menuVisible = ref(false)

const addMenuLevel = ref(0) //当前选中节点的level
const isAddMenu = ref(true)
const addMenuTitle = ["模块", "菜单", "子菜单"]
const selectRow = ref<Record<string, any> | undefined>({})
const menuConfirmLoading = ref(false)

const menuFormData = reactive({
  name: "",
  code: "",
  iconCode: "",
  parentCode: "", // 上级菜单
  parentGroupId: "", // 上级模块
  level: 0,
  path: "",
  // children: [],
  // operations: [],
  // functions: [],
  status: 1,
  filePath: "",
  showIndex: 0,
  ext: {},
  hideMenu: 0
})

const addMenu = (level: number, row?: Record<string, any>) => {
  selectRow.value = row || {}
  isAddMenu.value = true
  menuVisible.value = true
  addMenuLevel.value = level

  menuFormData.name = ""
  menuFormData.code = ""
  menuFormData.path = ""
  menuFormData.filePath = ""
  menuFormData.iconCode = ""
  menuFormData.parentCode = ""
  menuFormData.parentGroupId = ""
  menuFormData.level = level
  menuFormData.status = 1

  if (level === 0) {
    menuFormData.showIndex = menuConfigData.value.length - 1
    menuFormData.ext = { type: "group" }
  } else {
    menuFormData.showIndex = row?.children?.length - 1 > 0 ? row?.children?.length - 1 : 0
  }
  // 回显上级模块和上级菜单
  if (row?.level === 0) menuFormData.parentCode = row?.id
  if (row?.level === 1) {
    menuFormData.parentGroupId = row?.parentCode
    selectGroup(menuFormData.parentGroupId)
    menuFormData.parentCode = row?.id
  }
}

const editMenu = row => {
  selectRow.value = row
  isAddMenu.value = false
  menuVisible.value = true
  addMenuLevel.value = row.level
  for (const key in menuFormData) {
    menuFormData[key] = row[key]
  }
  menuFormData.showIndex = row.index
  menuFormData.code = row.path
  menuFormData.iconCode = row.icon
  // 自动补全上级模块和上级菜单
  if (row.level === 2) {
    menuConfigData.value.forEach(group => {
      group?.children?.forEach(level1 => {
        level1?.children?.forEach(level2 => {
          if (level2.id === row.id) {
            menuFormData.parentGroupId = group.id
            selectGroup(group.id)
            menuFormData.parentCode = level1?.id
          }
        })
      })
    })
  }
  tableData.value = cloneDeep(menuConfigData.value)
}

const setIcon = () => {
  iconDialogState.visible = true
  iconDialogState.selectedIcon = selectRow.value?.icon
}

// 二级菜单编辑: 选择模块后 获取模块下的菜单
const parentCodeList = ref<Array<Record<string, any>>>([])
const selectGroup = val => {
  // 重置上级菜单
  menuFormData.parentCode = ""
  parentCodeList.value = menuConfigData.value
    .find(item => item.id === val)
    ?.children?.map(item => {
      return {
        label: item.name,
        value: item.id
      }
    })
}

const menuFormRef = ref()

// 确认添加菜单节点
const menuConfirm = async () => {
  menuFormData.path = menuFormData.code
  menuFormRef.value?.validate(async valid => {
    if (!valid) return
    // 移动的一级菜单
    if (menuFormData.level === 1) {
      menuFormData.showIndex = isAddMenu.value
        ? menuConfigData.value.find(group => group.id === menuFormData.parentCode)?.children?.length
        : menuFormData.showIndex
    } else if (menuFormData.level === 2) {
      // 移动的二级菜单
      menuConfigData.value.forEach(group => {
        group.children.forEach(level1 => {
          if (level1.id === menuFormData.parentCode) {
            menuFormData.showIndex = isAddMenu.value ? level1?.children?.length : menuFormData.showIndex
          }
        })
      })
    } else {
      menuFormData.showIndex = isAddMenu.value ? menuConfigData.value.length : menuFormData.showIndex
    }
    menuConfirmLoading.value = true
    console.log(`output->menuConfigData.value`, menuConfigData.value)
    console.log(`output->menuFormData`, menuFormData)
    const handle = isAddMenu.value ? addMenuApi : editMenuApi
    try {
      await handle(menuFormData)
      await getMenuData()
      menuVisible.value = false
      menuConfirmLoading.value = false
    } catch (error: any) {
      toastError(error, "保存失败，请重试")
      menuConfirmLoading.value = false
    }
  })
}
</script>

<style lang="less" scoped>
.common-page-container {
  :deep(.el-dialog__body) {
    padding: 10px 20px;
  }
  .menu-list {
    display: flex;
    flex-wrap: wrap;
    column-gap: 10px;
    row-gap: 10px;
    .menu-item {
      width: 56px;
      height: 56px;
      border: 1px solid #909399;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 6px;
      position: relative;
      cursor: pointer;
      i {
        font-size: 36px;
        color: #909399;
      }

      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }
    }

    .check-item {
      background: rgba(12, 127, 252, 0.04);
      border-color: #0c7ffc;
      .check-status {
        font-size: 18px;
        position: absolute;
        right: 0px;
        top: 0px;
        color: #0c7ffc;
      }
    }
  }

  .operations-table {
    height: 300px !important;
  }
}
</style>
