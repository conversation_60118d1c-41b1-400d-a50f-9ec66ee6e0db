<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <el-form-item>
          <el-input v-model="searchFormState.inputValue">
            <template #prepend>
              <el-select v-model="searchFormState.inputField" style="width: 100px" @change="changeSelectValue">
                <el-option label="数据项" value="collectItem" />
                <el-option label="字段名" value="collectFieldName" />
              </el-select>
            </template>
          </el-input>
        </el-form-item>
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="commonTableRef"
        :table-columns="homeMetaTableColumns"
        :request-api="getHomeMetaListApi"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <template #header>
          <div>
            <AddButton @click="handleAdd">新增字段</AddButton>
            <BatchOperationButton
              type="primary"
              :icon="FolderChecked"
              :disabled="!selectedRows.length"
              @click="handleBatchSwitchStatus('ENABLE')"
            >
              批量启用编目
            </BatchOperationButton>
            <BatchOperationButton
              :icon="FolderRemove"
              :disabled="!selectedRows.length"
              @click="handleBatchSwitchStatus('DISABLE')"
            >
              批量禁用编目
            </BatchOperationButton>
          </div>
        </template>
        <template #operation="{ row }">
          <TableButton @click="handleEdit(row)">编辑</TableButton>
          <TableButton @click="handleSwitchStatus(row)">
            {{ row.catalogStatus === "ENABLE" ? "禁用编目" : "启用编目" }}
          </TableButton>
          <TableButton :disabled="row.borrowStatus === 'CAN_NOT_BORROW'" @click="handleDelete(row)">删除</TableButton>
        </template>
        <template #collectValueFormat="{ row }">
          <TableButton :disabled="!row.collectValueFormat" tooltip="当前字段无取值范围" @click="showMetaRange(row)">
            {{ row.collectValueFormat || "--" }}
          </TableButton>
        </template>
      </CommonTable>
    </template>

    <DialogForm
      v-model:visible="homeMetaDialogVisible"
      :title="isEdit ? '修改字段' : '新增字段'"
      :confirm-callback="handleConfirm"
      :form-config="dialogFormConfig"
      :form-state="homeMetaDialogForm"
      :disabled-fields="isEdit ? ['collectItem', 'collectFieldName'] : []"
    />

    <DialogContainer
      v-model:visible="rangeDialogVisible"
      :title="`${activeRow.collectValueFormat}取值范围`"
      :width="550"
      :no-footer="true"
    >
      <CommonTable
        :table-columns="fieldRangeColumns"
        :request-api="searchAllMetaCodeTableFieldApi"
        :request-params="{
          filters: `code=${activeRow.collectValueFormat}`
        }"
        :border="false"
        :pagination="false"
      />
    </DialogContainer>
  </PageContainer>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from "vue"
import { cloneDeep } from "lodash-es"
import { FolderChecked, FolderRemove } from "@element-plus/icons-vue"
import {
  PageContainer,
  SearchContainer,
  CommonTable,
  DialogContainer,
  AddButton,
  TableButton,
  BatchOperationButton
} from "@/base-components"
import { TabsRouter, DialogForm } from "@/page-components"
import { searchAllMetaCodeTableFieldApi, getHomeMetaListApi } from "@/interfaces"
import { SystemPrompt, Message, toastError } from "@/utils/message-tool"

import {
  tabsRouterList,
  fieldRangeColumns,
  homeMetaTableColumns,
  transformValueType,
  homeMetaFormConfig
} from "./config"
import {
  searchStandardRangeClassifyApi,
  addHomeMetaField,
  editHomeMetaField,
  deleteHomeMetaField,
  batchSetStandardStatus
} from "./interface"
import type { BaseOptionItem } from "@/types"

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  inputField: "collectItem",
  inputValue: ""
})

const searchParams = reactive({
  filters: ""
})

// 切换过滤属性
function changeSelectValue(nextField: string) {
  searchFormState.inputField = nextField
  searchFormState.inputValue = ""
}

function handleReset() {
  searchFormState.inputField = "collectItem"
  searchFormState.inputValue = ""
  searchParams.filters = ""
}

function handleQuery() {
  if (searchFormState.inputValue) {
    searchParams.filters = `${searchFormState.inputField}=${searchFormState.inputValue}`
  }
}

/* ======================== 字段弹窗相关状态及方法 ======================== */

const homeMetaDialogVisible = ref(false)

const isEdit = ref(false)

const initFormData = {
  collectFieldName: "",
  collectItem: "",
  collectValueType: "",
  standardType: "FIRST_PAGE",
  collectValueFormat: "",
  collectLength: 0,
  required: "YES",
  remark: "",
  catalogStatus: "ENABLE"
}

const homeMetaDialogForm = reactive(cloneDeep(initFormData))

const fieldRangeOptions = ref<Array<BaseOptionItem>>([])

const dialogFormConfig = computed(() => {
  return homeMetaFormConfig.map(item => {
    if (item.prop === "collectValueFormat") return { ...item, options: fieldRangeOptions.value }
    else return { ...item }
  })
})

// 初始化字段取值范围选项
onMounted(() => {
  searchStandardRangeClassifyApi().then(res => {
    const resData = res.data?.data ?? []
    fieldRangeOptions.value = resData.map(item => ({
      label: `${item.name}(${item.code})`,
      value: item.code
    }))
  })
})

// 确认新增&编辑
function handleConfirm() {
  if (isEdit.value) {
    return editHomeMetaField(homeMetaDialogForm)
      .then(() => {
        homeMetaDialogVisible.value = false
        Message.success("操作成功！")
        commonTableRef.value?.refreshTableData()
      })
      .catch(err => toastError(err))
  } else {
    return addHomeMetaField(homeMetaDialogForm)
      .then(() => {
        homeMetaDialogVisible.value = false
        Message.success("操作成功！")
        commonTableRef.value?.refreshTableData()
      })
      .catch(err => toastError(err))
  }
}

/* ======================== 表格相关配置状态及方法 ======================== */

const commonTableRef = ref<InstanceType<typeof CommonTable>>()

// 被选中表格项
const selectedRows = computed(() => commonTableRef.value?.tableState?.selectedRows ?? [])

function dataCallback(data: Array<any>) {
  return data.map(item => ({
    ...item,
    status: item.catalogStatus,
    requiredDesc: item.required === "YES" ? "是" : "否",
    collectValueTypeDesc: transformValueType(item.collectValueType)
  }))
}

// 当前点击的表格行
const activeRow = ref<Record<string, any>>({})

// 新增字段
const handleAdd = () => {
  isEdit.value = false
  for (let key in homeMetaDialogForm) {
    homeMetaDialogForm[key] = initFormData[key]
  }
  homeMetaDialogVisible.value = true
}

// 编辑字段
const handleEdit = (row: Record<string, any>) => {
  isEdit.value = true
  for (let key in homeMetaDialogForm) {
    homeMetaDialogForm[key] = row[key]
  }
  homeMetaDialogVisible.value = true
}

// 删除字段
function handleDelete(row) {
  SystemPrompt(`您正在删除字段名为“${row.collectFieldName}”的数据，确定删除？`).then(() => {
    deleteHomeMetaField(row.id)
      .then(() => {
        Message.success("操作成功！")
        commonTableRef.value?.refreshTableData()
      })
      .catch(err => toastError(err))
  })
}

// 启用&禁用字段编目
function handleSwitchStatus(row) {
  const nextOperation = row.catalogStatus === "ENABLE" ? "禁用" : "启用"
  const nextStatus = row.catalogStatus === "ENABLE" ? "DISABLE" : "ENABLE"
  SystemPrompt(`您正在${nextOperation}字段名为“${row.collectFieldName}”的数据，确定${nextOperation}？`).then(() => {
    batchSetStandardStatus([{ collectFieldName: row.collectFieldName, catalogStatus: nextStatus }])
      .then(() => {
        Message.success("操作成功！")
        commonTableRef.value?.refreshTableData()
      })
      .catch(err => toastError(err))
  })
}

// 批量启用&禁用字段编目
function handleBatchSwitchStatus(type: "ENABLE" | "DISABLE") {
  const typeName = type === "ENABLE" ? "启用" : "禁用"
  SystemPrompt(`您正在${typeName}已勾选的数据，确定${typeName}？`).then(() => {
    const batchData = selectedRows.value?.map(item => {
      return {
        collectFieldName: item.collectFieldName,
        catalogStatus: type
      }
    })

    batchSetStandardStatus(batchData)
      .then(() => {
        Message.success("操作成功！")
        commonTableRef.value?.refreshTableData()
      })
      .catch(err => toastError(err))
  })
}

/* ======================== 字段取值范围弹窗展示 ======================== */

const rangeDialogVisible = ref(false)

const showMetaRange = row => {
  activeRow.value = row
  if (row.collectValueFormat) {
    rangeDialogVisible.value = true
  }
}
</script>
