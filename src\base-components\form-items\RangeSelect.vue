<!-- 范围搜索 -->
<template>
  <el-form-item :label="label">
    <el-select
      v-model="selectValue"
      :loading="loading"
      :placeholder="`请选择${label}`"
      :filterable="true"
      :filter-method="value => (searchValue = value)"
    >
      <el-option
        v-for="option in filterOptions"
        :key="option.key"
        :label="option.value"
        :value="option.key"
      ></el-option>
    </el-select>
  </el-form-item>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue"
import { searchAllMetaCodeTableFieldApi } from "@/interfaces"
import { BaseOptionItem } from "@/types"

const props = defineProps<{
  label: string
  modelValue: string
  rcCode: string
}>()

const emits = defineEmits(["update:modelValue"])

interface OptionType extends BaseOptionItem {
  key: string
  value: string
}

const loading = ref(false)
const selectOptions = ref<Array<OptionType>>([])

const searchValue = ref("")
const filterOptions = computed(() => {
  return selectOptions.value.filter(item => item.value.includes(searchValue.value))
})

// 初始渲染需要调用一次，否则打开前回填值label展示有问题
onMounted(() => {
  changeSelectVisible(true)
})

function changeSelectVisible(visible) {
  if (visible) {
    loading.value = true
    const params = { filters: `code=${props.rcCode}` }
    searchAllMetaCodeTableFieldApi(params)
      .then(res => {
        selectOptions.value = res as Array<OptionType>
      })
      .finally(() => {
        loading.value = false
      })
  }
}

const selectValue = computed({
  get: () => props.modelValue,
  set: val => {
    emits("update:modelValue", val)
  }
})
</script>
