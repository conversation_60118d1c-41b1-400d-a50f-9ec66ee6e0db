<template>
  <el-dialog v-model="visible" width="540" style="border-radius: 8px" @closed="handleClosed">
    <template #header>
      <div class="dialog-title">数据对比</div>
    </template>
    <div class="container">
      <div class="content">
        <p class="date-range">
          筛选时间范围:
          {{
            `${formatDatetime(props.searchFormState.consultationTime[0], "YYYY/MM/DD")}-${formatDatetime(
              props.searchFormState.consultationTime[1],
              "YYYY/MM/DD"
            )}`
          }}
        </p>
        <div class="count-wrap">
          <p>
            上报系统患者数：
            <span class="count">{{ hisCount }}人/次；</span>
          </p>
          <p>
            业务系统患者数：
            <span class="count">{{ businessCount }}人/次；</span>
          </p>
          <p>
            挂号缴费患者数:
            <span class="count">{{ compareSourceCount }}人/次；</span>
          </p>
          <p>
            挂号缴费占比门急诊病历：
            <span class="count">{{ pct }}%；</span>
          </p>
        </div>
      </div>
      <div class="conclusion">{{ conclusion }}</div>

      <div class="flex-end">
        <el-button plain @click="close">取消</el-button>
        <el-button plain @click="handleDownloadDiff">下载差异数据</el-button>
        <el-button type="primary" @click="handleSync">同步</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, ref } from "vue"
import { formatDatetime } from "@/utils"

const props = defineProps<{
  searchFormState: any
}>()

const emits = defineEmits(["sync", "downloadDiff"])

const businessCount = ref(0) // 业务系统患者数据量
const hisCount = ref(0) // 筛选范围上报系统患者数据量
const compareSourceCount = ref(0) // 挂号缴费系统统计
const pct = ref("")
const isDifferent = computed(() => hisCount.value !== businessCount.value) // 系统数据是否和his系统不一致
const conclusion = computed(() => {
  if (isDifferent.value) {
    return `上报系统与业务系统数据量不一致!点击同步即可从业务系统更新筛选范围数据!`
  } else {
    return `筛选范围上报系统患者数据量与业务系统患者数据量一致!`
  }
})

const handleSync = () => {
  emits("sync")
  close()
}

const handleDownloadDiff = () => {
  emits("downloadDiff")
}

const visible = ref(false)

/**
 * 显示弹窗
 * @param _hisCount （上报系统患者数）
 * @param _compareSourceCount 挂号缴费系统统计
 * @param _businessCount 业务系统患者数据量
 * @param _pct 百分比 挂号缴费系统统计/业务系统数据统计
 */
const show = (_hisCount: number, _compareSourceCount: number, _businessCount: number, _pct: string) => {
  businessCount.value = _businessCount
  compareSourceCount.value = _compareSourceCount
  hisCount.value = _hisCount
  pct.value = _pct
  visible.value = true
}

const close = () => {
  visible.value = false
}

const handleClosed = () => {
  hisCount.value = 0
  businessCount.value = 0
  compareSourceCount.value = 0
  pct.value = ""
}

defineExpose({
  show,
  close
})
</script>

<style lang="less" scoped>
.count {
  color: red;
}

.content {
  p {
    text-align: left;
  }
}

.date-range {
  font-weight: 500;
  font-size: 14px;
  color: #6c7385;
  margin-bottom: 12px;
}

.count-wrap {
  display: flex;
  flex-direction: column;
  background: #eff2f7;
  border-radius: 4px;
  padding: 20px;

  p {
    color: #0a1633;
    font-size: 14px;
    margin-bottom: 5px;
  }
}

.conclusion {
  margin-top: 12px;
  font-weight: 500;
  font-size: 14px;
  color: #6c7385;
  text-align: left;
  margin-bottom: 32px;
}
.dialog-title {
  font-family: PingFang SC, PingFang SC !important;
  font-weight: bold !important;
  font-size: 18px !important;
  color: #0a1633 !important;
}
</style>
