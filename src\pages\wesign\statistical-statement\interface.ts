import axios from "@/interfaces/axios-instance"

/**
 * @method GET
 * @desc   归档率-详细分页数据
 */

interface ArchiveItemParams {
  archiveDateStart: string | number
  archiveDateEnd: string | number
  deptWsid?: string
  itemType?: string
  offset?: string
  limit?: string
}
export const getArchivalStatisticsDetailApi = (params: ArchiveItemParams) => {
  return axios({
    method: "get",
    url: "/api/document/statistics/archive/details",
    params
  })
}

/**
 * @method POST
 * @desc   归档率-表格数据
 */

interface ArchivalPercentageType {
  archiveDateStart: string | number
  archiveDateEnd: string | number
  itemCode: Array<string>
  deptWsid?: string
}

export const getArchivalPercentageTableApi = (data: ArchivalPercentageType) => {
  return axios({
    method: "post",
    url: "/api/document/statistics/archive",
    data
  })
}

/**
 * @method GET
 * @desc   归档率-柱状图数据
 */
export const getArchivalStatisticsChartApi = (params: ArchiveItemParams) => {
  return axios({
    method: "get",
    url: "/api/document/statistics/archive/custom",
    params
  })
}

/**
 * @method GET
 * @desc   归档率指标-自定义配置-获取
 */
export const getCustomArchivalConfigApi = () => {
  return axios({
    method: "get",
    url: "/api/document/statistics/archive/config/custom"
  })
}

/**
 * @method PUT
 * @desc   归档率指标-自定义配置-更新
 */

interface CustomConfigType {
  archiveItemDays: string
  archiveItemOperators: string
}
export const updateCustomArchivalConfigApi = (data: Array<CustomConfigType>) => {
  return axios({
    method: "put",
    url: "/api/document/statistics/archive/config",
    data
  })
}

/**
 * @method GET
 * @desc   归档率指标-个人配置-获取
 */
export const getUserArchivalConfigApi = () => {
  return axios({
    method: "get",
    url: "/api/document/statistics/archive/user-config"
  })
}

/**
 * @method PUT
 * @desc   归档指标-个人配置-更新
 */

interface UserArchivalConfigType {
  archiveCountTitle: string
  archiveCountCode: string
  archiveCountCheck: string
  archivePercentageTitle: string
  archivePercentageCode: string
  archivePercentageCheck: string
}
export const updateUserArchivalConfigApi = (data: UserArchivalConfigType) => {
  return axios({
    method: "put",
    url: "/api/document/statistics/archive/user-config",
    data
  })
}

/**
 * @method GET
 * @desc   脱纸率-按科室(列表与柱状图)
 */
export const getDepartmentPaperStatisticsApi = (params: ArchiveItemParams) => {
  return axios({
    method: "get",
    url: "/api/document/statistics/paper/dept",
    params
  })
}

/**
 * @method GET
 * @desc   脱纸率-按文书分类
 */

interface WritPaperStatisticsParams {
  archiveDateStart: string | number
  archiveDateEnd: string | number
  deptWsid?: string
  patientName?: string
  patientId?: string
  mrNo?: string
}
export const getWritPaperStatisticsApi = (params: WritPaperStatisticsParams) => {
  return axios({
    method: "get",
    url: "/api/document/statistics/paper/mr-class",
    params: params
  })
}

/**
 * @method POST
 * @desc   归档率-导出
 */
export const exportArchiveStatisticsApi = (data: ArchivalPercentageType) => {
  return axios({
    method: "post",
    url: "/api/document/statistics/archive/export",
    responseType: "blob",
    data
  })
}

/**
 * @method GET
 * @desc   归档率-详细页面-导出
 */
export const exportArchiveStatisticsDetailApi = params => {
  return axios({
    method: "get",
    url: "/api/document/statistics/archive/details/export",
    responseType: "blob",
    params
  })
}

/**
 * @method GET
 * @desc   脱纸率-按科室-导出
 */
export const exportDepartmentStatisticsApi = params => {
  return axios({
    method: "get",
    url: "/api/document/statistics/paper/dept/export",
    responseType: "blob",
    params
  })
}

/**
 * @method GET
 * @desc   脱纸率-按文书分类-导出
 */
export const exportWritPaperStatisticsApi = params => {
  return axios({
    method: "get",
    url: "/api/document/statistics/paper/mr-class/export",
    responseType: "blob",
    params
  })
}

/**
 * @method GET
 * @desc 打印导出报表-列表
 */
export const getPrintExportReportListApi = (params: IPaginationRequestParams) => {
  return axios({
    method: "get",
    url: "/api/document/print/record",
    params: {
      ...params,
      sorts: "-createdDatetime"
    }
  })
}

/**
 * @method POST
 * @desc 打印导出报表-导出
 */

interface PrintExportReport {
  wsids: Array<string>
  filters?: string
}

export const exportPrintExportReportApi = (data: PrintExportReport) => {
  return axios({
    method: "post",
    url: "/api/document/print/record/export",
    responseType: "blob",
    data
  })
}

/*==============================病案质量统计==============================*/
// 获取指定时间段、去年同期的终末质控平均分以及不同等级病案的数量
interface MedicalQualityReportParams {
  startDate: number
  endDate: number
  outHospitalDeptWsid: string //出院科室WSID
}
export const getMedicalQualityReportLastYearApi = (data: MedicalQualityReportParams) => {
  return axios({
    method: "post",
    url: "/api/document/statistics/final-qc",
    data: data
  })
}

// 获取指定时间段按时间聚合分组后的平均分
export const getMedicalQualityReportGroupByDateApi = (data: MedicalQualityReportParams) => {
  return axios({
    method: "post",
    url: "/api/document/statistics/final-qc/by-date",
    data: data
  })
}

// 获取指定时间段按科室聚合分组后的平均分、各质控等级病案数等等
export const getMedicalQualityReportGroupByDeptApi = (data: MedicalQualityReportParams) => {
  return axios({
    method: "post",
    url: "/api/document/statistics/final-qc/by-dept",
    data: data
  })
}
