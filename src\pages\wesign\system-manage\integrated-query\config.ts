import { TableColumnItem } from "@/types"

export interface QueryTermItemType {
  label: string
  prop: string
  type: string
  [prop: string]: string
}

//病案table column
export const queryMedicalRecordColumns: Array<TableColumnItem> = [
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "patientSex", label: "性别", minWidth: 60 },
  { prop: "mrNo", label: "病案号", minWidth: 150, sortable: true, must: true },
  { prop: "visitId", label: "住院次数", minWidth: 90 },
  { prop: "inHospitalDatetime", label: "入院时间", minWidth: 170, sortable: true },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 100 },
  { prop: "outHospitalDatetime", label: "出院时间", minWidth: 170, sortable: true },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 100 },
  { prop: "outDischargeDiagnosisName", label: "出院诊断", minWidth: 100 },
  { prop: "doctorName", label: "主治医师", minWidth: 100 },
  { prop: "operation", label: "操作", width: 140, fixed: "right", must: true }
]
