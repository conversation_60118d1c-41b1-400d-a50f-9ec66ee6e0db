<template>
  <DialogContainer
    v-model:visible="authorizedDialogVisible"
    title="关联用户"
    :width="908"
    :confirm-callback="handleConfirm"
  >
    <div class="user-tab">
      <div class="tab-choice-box tab-user-choice-box">
        <div class="tab-choice-title">
          选择
          <el-tag type="info" size="small">{{ roleName }}</el-tag>
          角色的关联用户：
        </div>
        <div class="tab-choice tab-user-choice">
          <div>
            <el-input
              v-model="searchInput"
              style="width: 250px"
              :prefix-icon="Search"
              placeholder="姓名/工号"
              clearable
              @keydown.enter="searchUserForm.searchUserValue = searchInput"
              @clear="searchUserForm.searchUserValue = ''"
            />
            <div class="tree-data">
              <div
                v-for="dept in deptList"
                :key="dept.wsid"
                class="dept-item"
                :class="{ active: dept.wsid === searchUserForm.deptWsid }"
                @click="searchUserForm.deptWsid = dept.wsid"
              >
                {{ dept.deptName }}
              </div>
            </div>
          </div>

          <div v-loading="checkedUserState.searchUserLoading" class="choice-role">
            <div style="padding-left: 8px; margin-bottom: 16px">
              <el-checkbox :value="isCheckAll" :indeterminate="isIndeterminate" @change="handleCheckAll">
                {{ `全选(${userInDeptListCount})` }}
              </el-checkbox>
            </div>
            <el-checkbox-group
              v-model="checkedUserState.checkedUserList"
              v-infinite-scroll="scrollRenderUser"
              class="custom-checkbox"
              style="overflow-y: auto; height: 300px; padding-right: 10px"
            >
              <div v-for="item in checkedUserState.userInDeptList" :key="item.userWsid">
                <el-checkbox :label="item.userWsid" @click="e => clickUserInDept(item, e)">
                  <div style="display: flex; align-items: center">
                    <img src="~@/assets/png/avatar.png" class="custom-checkbox-avatar" alt="" />
                    <span class="custom-checkbox-name">{{ `${item.realName}(${item.jobId})` }}</span>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>
        </div>
      </div>

      <div class="tab-choice-box">
        <div class="tab-choice-title">已选择用户({{ authorizedState.userData.length }})</div>
        <div id="authorized-users" v-loading="authorizedState.userDataLoading" class="tab-choice tab-authorized-choice">
          <div v-for="item in authorizedState.userData" :key="item.userWsid" class="has-authorized-user">
            <div style="display: flex; flex: 1">
              <img src="~@/assets/png/avatar.png" class="custom-checkbox-avatar" alt="" />
              <span class="custom-checkbox-name">{{ `${item.realName}(${item.jobId})` }}</span>
            </div>
            <el-icon @click="deleteUser(item.userWsid)"><Close /></el-icon>
          </div>
        </div>
      </div>
    </div>
  </DialogContainer>
</template>

<script setup lang="ts">
import { reactive, ref, computed, onMounted, watch } from "vue"
import { Search, Close } from "@element-plus/icons-vue"
import { DialogContainer } from "@/base-components"
import { getHospitalDeptsApi, getFuzzyUsersInDeptsApi, getUserInRolesApi } from "@/interfaces"
import { SystemPrompt, Message, toastError } from "@/utils/message-tool"
import { editUsersInRoleApi } from "../interface"
import type { UserItem } from "@/types"

const props = defineProps({
  roleWsid: { type: String, default: "" },
  roleName: { type: String, default: "" },
  visible: { type: Boolean, required: true }
})

const emits = defineEmits(["update:visible"])

const authorizedDialogVisible = computed({
  get: () => props.visible,
  set: val => emits("update:visible", val)
})

/* ======================== 加载时获取科室选项 ======================== */

interface DeptOptionItem {
  wsid: string
  deptName: string
}

const deptList = ref<Array<DeptOptionItem>>([])

onMounted(() => {
  getHospitalDeptsApi().then(res => {
    deptList.value = res.data.data
    deptList.value.unshift({ wsid: "", deptName: "全部用户" })
  })
})
/* ========================  ======================== */

const searchInput = ref("")

// 已授权用户
const authorizedState = reactive({
  userData: [] as Array<UserItem>, // 显示的已授权的用户
  userDataLoading: false
})

// 科室用户搜索
const searchUserForm = reactive({
  searchUserValue: "",
  deptWsid: "" // 当前选中科室
})

// 科室用户
const checkedUserState = reactive({
  checkedUserList: [] as Array<string>, //已选科室用户
  allUserInDeptList: [] as Array<UserItem>, // 科室下的所有用户
  userInDeptList: [] as Array<UserItem>, // 显示的科室用户
  searchUserLoading: false // 弹窗-用户搜索loading
})

// 每次拖拽显示的数量
const scrollPageSize = 100

// 选择科室切换后刷新用户列表
watch(
  () => searchUserForm.deptWsid,
  () => {
    checkedUserState.searchUserLoading = true
    getFuzzyUsersInDeptsApi({
      deptWsid: searchUserForm.deptWsid,
      item: ""
    })
      .then(res => {
        checkedUserState.allUserInDeptList = res.data
        checkedUserState.searchUserLoading = false
      })
      .catch(err => {
        toastError(err, "获取用户列表失败")
        checkedUserState.searchUserLoading = false
      })
  },
  { immediate: true }
)

const filterUserInDeptList = computed(() => {
  return checkedUserState.allUserInDeptList.filter(
    item =>
      item.jobId.includes(searchUserForm.searchUserValue) || item.realName.includes(searchUserForm.searchUserValue)
  )
})

// 每次更新了筛选列表需要重新刷新一次显示用户列表
watch(
  () => filterUserInDeptList.value,
  val => {
    checkedUserState.userInDeptList = []
    scrollRenderUser()
  }
)

// 滚动渲染下一百条
function scrollRenderUser() {
  // 没有显示完，再显示后面的一百条
  const currentCount = checkedUserState.userInDeptList.length
  const totalCount = filterUserInDeptList.value.length
  if (currentCount < totalCount) {
    checkedUserState.userInDeptList.push(
      ...filterUserInDeptList.value.slice(currentCount, currentCount + scrollPageSize)
    )
  }
}

watch(
  [() => props.roleWsid, () => props.visible],
  () => {
    if (!props.roleWsid || !props.visible) return
    authorizedState.userDataLoading = true
    getUserInRolesApi({ roleWsid: props.roleWsid })
      .then(resData => {
        authorizedState.userData = resData
        const isSelectedUser: Array<string> = []
        resData.forEach(item => isSelectedUser.push(item.userWsid))
        checkedUserState.checkedUserList = isSelectedUser
        authorizedState.userDataLoading = false
      })
      .catch(err => {
        authorizedState.userDataLoading = false
        toastError(err, "获取已关联用户失败")
      })
  },
  {
    immediate: true
  }
)

// 弹窗显隐变化后重置搜索
watch(
  () => props.visible,
  () => {
    searchInput.value = ""
    searchUserForm.searchUserValue = ""
    searchUserForm.deptWsid = ""
    checkedUserState.checkedUserList = []
  }
)

// 全选是否为中间态
const isIndeterminate = computed(() => {
  const checkedUserCount = filterUserInDeptList.value.filter(item =>
    checkedUserState.checkedUserList.includes(item.userWsid)
  ).length
  if (checkedUserCount > 0 && checkedUserCount < filterUserInDeptList.value.length) return true
  else return false
})

// 是否全选状态
const isCheckAll = computed(() => {
  const checkedUserCount = filterUserInDeptList.value.filter(item =>
    checkedUserState.checkedUserList.includes(item.userWsid)
  ).length
  return checkedUserCount >= filterUserInDeptList.value.length && checkedUserCount !== 0
})

const userInDeptListCount = computed(() => {
  return filterUserInDeptList.value.length || 0
})

// 全选用户
const handleCheckAll = val => {
  // 推入所有筛选列表中的数据或者删除所有筛选列表中的数据
  filterUserInDeptList.value.forEach(item => {
    const isExist = authorizedState.userData.findIndex(checkedItem => checkedItem.userWsid === item.userWsid)
    const isChecked = checkedUserState.checkedUserList.findIndex(checkedItem => checkedItem === item.userWsid)
    // 已选择用户
    if (isExist === -1 && val) authorizedState.userData.push(item)
    else if (isExist !== -1 && !val) authorizedState.userData.splice(isExist, 1)

    // 未选择用户的选中状态
    if (isChecked === -1 && val) checkedUserState.checkedUserList.push(item.userWsid)
    else if (isChecked !== -1 && !val) checkedUserState.checkedUserList.splice(isChecked, 1)
  })
}

// 删除已授权用户
function deleteUser(userWsid) {
  SystemPrompt("确认移除该用户?").then(() => {
    checkedUserState.checkedUserList = checkedUserState.checkedUserList.filter(item => item !== userWsid)
    authorizedState.userData = authorizedState.userData.filter(user => user.userWsid !== userWsid)
  })
}

// 单选用户
function clickUserInDept(row, e) {
  const isExistIndex = authorizedState.userData.findIndex(item => item.userWsid === row.userWsid)
  //不存在则推入, 已经存在则删除
  if (isExistIndex === -1) {
    authorizedState.userData.push(row)
  } else {
    authorizedState.userData.splice(isExistIndex, 1)
  }
}

// 确认修改
function handleConfirm() {
  let wsidArr: Array<string> = []
  authorizedState.userData.forEach(item => {
    wsidArr.push(item.userWsid)
  })
  wsidArr = [...wsidArr]
  editUsersInRoleApi({ roleWsid: props.roleWsid, userWsids: wsidArr })
    .then(() => {
      Message.success("操作成功")
      emits("update:visible", false)
    })
    .catch(err => toastError(err))
}
</script>

<style lang="less" scoped>
.user-tab {
  display: flex;
  justify-content: space-between;
  .tab-user-choice-box {
    margin-right: 16px;
  }
  .tab-user-choice {
    display: flex;
    justify-content: space-between;
    width: 560px;
    height: 384px;
    .choice-role {
      width: 260px;
      height: 100%;
      margin-left: 16px;
      .custom-checkbox {
        .el-checkbox {
          flex-direction: row-reverse;
          width: 100%;
          height: 100%;
          margin-bottom: 12px;
          .el-checkbox__label {
            display: flex;
            justify-content: space-between;
            line-height: 28px;
            width: 100%;
          }
        }
      }
    }
  }
}
.custom-checkbox-avatar {
  display: inline-block;
  overflow: hidden;
  width: 28px;
  height: 28px;
  background-color: blanchedalmond;
  border-radius: 50%;
}
.custom-checkbox-name {
  overflow: hidden;
  width: 180px;
  margin-left: 5px;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex-grow: 1;
}

.tab-authorized-choice {
  overflow-y: auto;
  width: 284px;
  height: 384px;
  .has-authorized-user {
    display: flex;
    align-items: center;
    height: 28px;
    line-height: 28px;
    margin-bottom: 12px;
  }
}

.tab-choice-box {
  .tab-choice-title {
    display: flex;
    align-items: flex-end;
    column-gap: 5px;
    margin: 14px 0;
    height: 20px;
  }
  .tab-choice {
    padding: 16px;
    border: 1px solid rgb(220 223 230);
    border-radius: 2px;
    box-sizing: border-box;

    .tree-data {
      overflow-y: auto;
      width: 250px;
      height: 300px;
      margin-top: 16px;
      .custom-checkbox {
        .el-checkbox {
          flex-direction: row-reverse;
          width: 100%;
          height: 100%;
          margin-bottom: 12px;
          .el-checkbox__label {
            display: flex;
            justify-content: space-between;
            line-height: 28px;
            width: 100%;
          }
        }
      }
      .dept-item {
        height: 40px;
        padding-left: 16px;
        color: rgb(3 8 20 / 84.7%);
        border-radius: 4px;
        line-height: 40px;
        box-sizing: border-box;
        cursor: pointer;
        &:hover {
          background-color: rgb(245 245 246);
        }
        &.active {
          font-weight: 600;
          color: var(--el-color-primary);
          background-color: rgb(44 104 255 / 9.8%);
        }
      }
    }
  }
}
</style>
