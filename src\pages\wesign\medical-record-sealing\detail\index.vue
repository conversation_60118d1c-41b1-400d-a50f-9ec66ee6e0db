<template>
  <div id="medical-record-view">
    <div
      v-loading="state.loading"
      class="view-left"
      :style="{ width: recordTreeRef?.state.isCollapse ? '0px' : '280px' }"
    >
      <MedicalRecordTree
        ref="recordTreeRef"
        class="view-left-top"
        can-collapse
        :base-info="state.baseInfo"
        :tree-info="state.treeInfo"
        @click-pdf="handlePdfClick"
      />

      <div
        class="view-left-bottom"
        :style="{
          width: recordTreeRef?.state.isCollapse ? '0px' : '264px',
          overflow: recordTreeRef?.state.isCollapse ? 'hidden' : 'auto'
        }"
      >
        <div class="title">辅助资料</div>
        <div class="auxiliary-list">
          <div
            v-for="file in state.auxiliaryFiles"
            :key="file.id"
            class="auxiliary-item"
            :class="state.targetFileWsid === file.fileWsid ? 'click-auxiliary' : ''"
            @click="handlePdfClick({ fileWsid: file.fileWsid, contentFilePath: file.contentFilePath })"
          >
            <el-tooltip effect="dark" :content="file.fileName" placement="top">
              {{ file.fileName }}
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>
    <div class="view-middle common-box-shadow">
      <PdfPreviewComponent ref="pdfPreviewRef" :src="state.pdfSrc" />
    </div>
    <div v-loading="state.loading" class="view-right">
      <DocumentMeta
        can-collapse
        :document-wsid="state.documentWsid"
        :base-info-data="state.firstPageFields"
        :file-wsid="state.targetFileWsid"
        :mode="DocumentMetaTypeEnum.SignVerificationInfo"
        :disabled-download="true"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, ref } from "vue"
import { useRoute } from "vue-router"
import { PdfPreviewComponent } from "@/base-components"
import { MedicalRecordTree, DocumentMeta } from "@/page-components"
import { DocumentMetaTypeEnum } from "@/types"
import { toastError } from "@/utils"
import { getSealingDetailApi } from "../interface"

const recordTreeRef = ref()

const state = reactive({
  baseInfo: {},
  pdfSrc: "",
  loading: false,
  treeInfo: "" as any,
  targetFileWsid: "",
  firstPageFields: [] as Array<any>, // 首页字段
  auxiliaryFiles: [] as Array<Record<string, any>>,
  documentWsid: ""
})

const handlePdfClick = node => {
  state.targetFileWsid = node?.fileWsid
  state.documentWsid = node?.wsid
  state.pdfSrc = "/api" + node?.contentFilePath
}

onMounted(() => {
  const route = useRoute()
  const { id } = route.query
  if (!id) return
  state.loading = true
  getSealingDetailApi({ id })
    .then(res => {
      state.auxiliaryFiles = res.data.data.supportInfo
      state.baseInfo = res.data.data.baseInfo
      state.treeInfo = res.data.data.treeInfo
      state.loading = false
    })
    .catch(error => {
      toastError(error)
      state.loading = false
    })
})
</script>

<style lang="less" scoped>
#medical-record-view {
  display: flex;
  justify-content: space-between;
  height: 100%;
  .view-middle {
    overflow-x: auto;
    min-width: 400px;
    flex: 1;
    margin: 0 16px 0px 0px;
    box-sizing: border-box;
    .support-file-contain {
      height: 100%;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: unset;
        height: unset;
        max-width: 100%;
      }
    }
  }

  .view-left {
    display: flex;
    height: 100%;
    flex-direction: column;
    row-gap: 10px;
    transition: width 0.5s;
    .view-left-top {
      height: 65% !important;
    }
    .view-left-bottom {
      height: 35% !important;
      transition: width 0.5s;
      box-shadow: 0 2px 8px rgb(0 35 114 / 10%);
      min-height: 0px;
      background: #fff;
      position: relative;
      .title {
        font-size: 16px;
        color: rgba(3, 8, 20, 0.85);
        font-weight: 700;
        border-bottom: 1px solid #e1e2e6;
        padding: 20px 15px;
        white-space: nowrap;

        &:before {
          position: absolute;
          top: 22px;
          left: 0;
          width: 4px;
          height: 18px;
          background-color: #2c68ff;
          border: 2px;
          border-radius: 0 4px 4px 0;
          content: "";
        }
      }

      .auxiliary-list {
        width: 264px;
        display: flex;
        flex-direction: column;
        padding: 10px 10px;
        box-sizing: border-box;
        height: calc(100% - 62px);
        overflow-y: auto;
        .auxiliary-item {
          width: 100%;
          line-height: 26px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          cursor: pointer;
          padding: 0px 5px;
          box-sizing: border-box;
          flex-shrink: 0;
          &:hover {
            background-color: #f5f7fa;
          }
        }

        .click-auxiliary {
          background-color: #ecf5ff;
        }
      }
    }
    .view-left-bottom {
      height: calc(34% - 4px);
      margin-top: 16px;
      box-sizing: border-box;
    }
  }
}
</style>
