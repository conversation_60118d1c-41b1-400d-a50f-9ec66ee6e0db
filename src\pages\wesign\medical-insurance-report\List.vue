<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="medicalInsuranceTabsConfig" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <CommonInputFormItem
          v-model="searchFormState.mrNos"
          label="病案号"
          placeholder="多个病案号用逗号分隔"
          style="align-items: center"
        />
        <CommonInputFormItem
          v-model="searchFormState.names"
          label="患者姓名"
          placeholder="多个患者姓名用逗号分隔"
          style="align-items: center"
        />
        <DepartmentFormItem v-model="searchFormState.outHospitalDeptWsid" multiple collapse-tags label="出院科室" />
        <el-form-item label="出院时间">
          <el-date-picker
            v-model="searchFormState.outHospitalDatetime"
            type="daterange"
            unlink-panels
            :clearable="false"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :disabled-date="disabledDate"
            value-format="x"
            @calendar-change="handleCalendarChange"
          ></el-date-picker>
        </el-form-item>
        <CommonSelectFormItem v-model="searchFormState.reviewStatus" label="审核状态" :options="reviewStatusOptions" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="tableRef"
        table-id="medicalInsuranceReportTableIdent"
        :table-columns="medicalInsuranceDataTableColumns"
        :request-api="getMedicalInsuranceListApi"
        :request-params="actualParams"
        :data-callback="dataCallback"
      >
        <template #header>
          <div class="flex-start">
            <BatchOperationButton
              :disabled="!auditRows.length"
              type="primary"
              :plain="false"
              tooltip="请至少选择一条待审核的数据"
              @click="handleAudit"
            >
              审核
            </BatchOperationButton>
            <ManualExportButton
              ref="manualExportRef"
              button-text="导出csv"
              :selected-count="selectedRows.length"
              :total="tableRef?.tableState?.total"
              :max="10000"
              :tooltip-disabled="true"
              :error-message="exportErrorMessage"
              @export="handleExport('selected')"
              @export-all="handleExport('all')"
            />
          </div>
        </template>

        <template #syncDatetime="{ row }">
          {{ formatDatetime(row.syncDatetime) }}
        </template>
        <template #reviewStatus="{ row }">
          <el-tag :type="getReviewsStatusTagType(row.reviewStatus)">
            {{ reviewStatusOptions.find(item => item.value === row.reviewStatus)?.label }}
          </el-tag>
        </template>
        <template #operation="{ row }">
          <TableButton @click="toEdit(row)">详情</TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h, watch } from "vue"
import { useRouter } from "vue-router"
import { cloneDeep } from "lodash-es"
import {
  PageContainer,
  SearchContainer,
  CommonTable,
  CommonInputFormItem,
  CommonSelectFormItem,
  TableButton,
  BatchOperationButton,
  ManualExportButton,
  DepartmentFormItem
} from "@/base-components"
import { TabsRouter } from "@/page-components"
import {
  medicalInsuranceDataTableColumns,
  reviewStatusOptions,
  getReviewsStatusTagType,
  medicalInsuranceTabsConfig
} from "./config"
import {
  getMedicalInsuranceListApi,
  exportMedicalInsuranceToCvsApi,
  auditMedicalInsuranceApi,
  getMedicalInsuranceExportLimitApi
} from "./interface"
import type { DateModelType } from "element-plus"
import { useCommonOptions } from "@/hooks"
import useTableSearch from "@/hooks/useTableSearch_v2"
import { getHQMSDepartmentApi } from "@/interfaces"
import { useSystemStore } from "@/stores"
import { formatDatetime, Message, toastError, downloadFile, extractErrorMsg, SystemAlert } from "@/utils"

const router = useRouter()

const systemStore = useSystemStore()

/* ======================== 科室搜索 ======================== */

// const { options: outDeptOptions } = useCommonOptions({
//   getOptionsApi: getHQMSDepartmentApi,
//   labelAlias: "value",
//   valueAlias: "key"
// })

// const filteredDeptOptions = ref<Record<string, any>[]>([])

// // el-select自带筛选超过100条会报错，需要自定义filter-method
// const filterDeptOptions = query => {
//   if (!query) {
//     filteredDeptOptions.value = outDeptOptions.value
//     return
//   }
//   filteredDeptOptions.value = outDeptOptions.value.filter(option => option.label.includes(query))
// }

// // 初始化更新 filteredDeptOptions
// watch(
//   () => outDeptOptions.value,
//   newVal => {
//     if (newVal) {
//       filteredDeptOptions.value = cloneDeep(newVal)
//     }
//   }
// )

/* ======================== 搜索相关数据及方法 ======================== */

// 默认三个月前当日0点
const defaultTimeStart = new Date(new Date().setHours(0, 0, 0, 0) - 1 * 30 * 24 * 3600 * 1000).getTime()
const defaultTimeEnd = new Date(new Date().setHours(23, 59, 59, 999)).getTime()

const formInitialData = {
  mrNos: "", // 住院号
  names: "", // 姓名
  outHospitalDeptWsid: [] as string[], // 出院科室
  outHospitalDatetime: [defaultTimeStart, defaultTimeEnd] as [DateModelType, DateModelType], // 出院时间
  reviewStatus: "", // 审核状态
  exportType: "EXPORT_MISL"
}

const { searchFormState, searchParams, handleQuery, handleReset } = useTableSearch({
  filtersInitialData: formInitialData
})

const actualParams = ref() // 实际的请求参数

// 监听搜索参数变化，更新实际请求参数
watch(
  () => searchParams,
  val => {
    actualParams.value = {
      ...searchFormState,
      outHospitalDeptWsid: searchFormState.outHospitalDeptWsid?.length
        ? searchFormState.outHospitalDeptWsid.join(",")
        : "",
      outHospitalDatetime: searchFormState.outHospitalDatetime || "",
      outHospitalTimeStart: searchFormState.outHospitalDatetime?.[0] || "",
      outHospitalTimeEnd: new Date(searchFormState.outHospitalDatetime?.[1]).setHours(23, 59, 59, 999) || "",
      timestamp: val.timestamp
    }
  },
  { immediate: true, deep: true }
)

/* ======================== 表格数据 ======================== */

const tableRef = ref<InstanceType<typeof CommonTable>>()

// 当前选中表格项
const selectedRows = computed(() => tableRef.value?.tableState?.selectedRows ?? [])

// 可生成上报pdf的选中项
const auditRows = computed(() => selectedRows.value.filter(item => item.reviewStatus === "REVIEW_WAIT"))

const dataCallback = (data: any) => {
  data.forEach(row => {
    row.inHospitalDatetime = formatDatetime(row.inHospitalDatetime)
    row.outHospitalDatetime = formatDatetime(row.outHospitalDatetime)
  })
  return data
}

/* ======================== 导出 ======================== */

const manualExportRef = ref<InstanceType<typeof ManualExportButton>>()
const exportErrorMessage = ref("")
let exportLimit = 10000 // 导出数据的上限：如果超出上限则调用接口完成显示提示语，否则执行下载到本地

onMounted(async () => {
  exportLimit = (await getMedicalInsuranceExportLimitApi()).data.data
})

const handleExport = async (type: "all" | "selected") => {
  const totalExportCount = type === "all" ? tableRef.value?.tableState?.total || 0 : selectedRows.value.length // 当前导出总数
  const inpNos = type === "selected" ? selectedRows.value.map(row => row.inpNo) : undefined
  // 如果导出数量大于后台配置的导出上限，调用接口后就提示用户稍后查看（异步任务）
  if (totalExportCount > exportLimit) {
    try {
      systemStore.showLoading()
      await exportMedicalInsuranceToCvsApi({ inpNos: inpNos, ...actualParams.value })
      systemStore.hideLoading()
      SystemAlert("导出任务处理中，请您前往导出记录查看任务进度", "success")
    } catch (error: any) {
      systemStore.hideLoading()
      toastError(error, "导出失败")
    }
  }
  // 否则下载到本地
  else {
    try {
      manualExportRef.value?.handleExportStart()
      const res = await exportMedicalInsuranceToCvsApi({ inpNos: inpNos, ...actualParams.value })
      const fileName = res.headers["content-disposition"].split("filename")[1].split("=")[1].replaceAll(`"`, "")
      let fileType = fileName.split(".")[1].toLowerCase()
      if (fileType === "zip") fileType = "application/zip"
      downloadFile({ fileData: res.data, fileType: fileType, fileName })
      manualExportRef.value?.handleExportDone()
      exportErrorMessage.value = ""
    } catch (error: any) {
      let errorMessage = ""
      if (error?.response?.data instanceof Blob) {
        const blob = new Blob([error.response.data])
        const blobJson = JSON.parse(await blob.text())
        errorMessage = blobJson.userMessage
      } else {
        errorMessage = extractErrorMsg(error)
      }
      manualExportRef.value?.handleExportError()
      exportErrorMessage.value = errorMessage
      Message.error(errorMessage || "导出失败")
    }
  }
}

/* ======================== 时间筛选限制 ======================== */

const startDate = ref()
const days = 31 * 24 * 3600 * 1000

const handleCalendarChange = date => {
  const [minDate, maxDate] = date
  if (minDate && !maxDate) {
    startDate.value = minDate // 记录选中的首个日期
  } else {
    startDate.value = null
  }
}

const disabledDate = time => {
  // 如果已经选择了开始日期，则禁用所有超出30天范围的日期
  if (startDate.value) {
    return time.getTime() < startDate.value?.getTime() - days || time.getTime() > startDate.value?.getTime() + days
  } else {
    return false
  }
}

/* ======================== 表格操作 ======================== */

// 审核
const handleAudit = async () => {
  try {
    systemStore.showLoading("审核中")
    const wsids = auditRows.value.map(item => item.inpNo)
    await auditMedicalInsuranceApi(wsids)
    systemStore.hideLoading()
    Message.success("审核成功")
    tableRef.value?.refreshTableData()
  } catch (error: any) {
    console.log(error)
    systemStore.hideLoading()
    toastError(error, "操作失败")
  }
}

// 详情
const toEdit = row => {
  router.push({
    path: "/catalog-preview",
    query: {
      appScenario: "MISL_DETAIL",
      businessDataWsid: row.inpNo,
      returnUrl: "/medical-insurance-report/list",
      actionType: "edit"
    }
  })
}
</script>
