import type { TableColumnItem, BaseOptionItem } from "@/types"

export const menuId = "/statistical-statement/print-export-report"

export const printExportReportColumns: Array<TableColumnItem> = [
  { type: "selection", width: 50, fixed: "left" },
  { prop: "createdDatetime", label: "操作时间", minWidth: 180 },
  { prop: "operatorName", label: "操作人", minWidth: 120 },
  { prop: "totalPage", label: "总页数", minWidth: 120 },
  { prop: "typeEnum", label: "类型", minWidth: 120 },
  { prop: "sourceEnum", label: "来源", minWidth: 120 },
  { prop: "patientName", label: "姓名", minWidth: 120 },
  { prop: "mrNo", label: "病案号", minWidth: 150 },
  { prop: "patientId", label: "患者编号", minWidth: 150 },
  { prop: "inpNo", label: "住院号", minWidth: 150 },
  { prop: "outHospitalDatetime", label: "出院日期", minWidth: 180 },
  { prop: "operation", label: "操作", minWidth: 120 }
]

export enum DocumentPrintRecordTypeEnum {
  PRINT = "PRINT", // 打印
  EXPORT = "EXPORT" // 导出
}

export enum DocumentPrintRecordSourceEnum {
  BORROW = "BORROW", // 借阅
  SEAL = "SEAL", // 封存
  HQMS_REPORT = "HQMS_REPORT", // HQMS上报
  EMR_PRINT = "EMR_PRINT", // 病案打印
  ARCHIVE = "ARCHIVE", // 病案归档

  BORROW_PRINT = "BORROW_PRINT", //借阅打印
  BORROW_EXPORT = "BORROW_EXPORT", //借阅导出
  SEALED_PRINT = "SEALED_PRINT", //封存打印
  SEALED_EXPORT = "SEALED_EXPORT", //封存导出
  ARCHIVE_PRINT = "ARCHIVE_PRINT" //归档打印
}

export const DocumentPrintRecordTypeOptions: Array<BaseOptionItem> = [
  { label: "打印", value: DocumentPrintRecordTypeEnum.PRINT },
  { label: "导出", value: DocumentPrintRecordTypeEnum.EXPORT }
]

export const DocumentPrintRecordSourceOptions: Array<BaseOptionItem> = [
  { label: "借阅打印", value: DocumentPrintRecordSourceEnum.BORROW_PRINT },
  { label: "借阅导出", value: DocumentPrintRecordSourceEnum.BORROW_EXPORT },
  { label: "封存打印", value: DocumentPrintRecordSourceEnum.SEALED_PRINT },
  { label: "封存导出", value: DocumentPrintRecordSourceEnum.SEALED_EXPORT },
  { label: "HQMS上报", value: DocumentPrintRecordSourceEnum.HQMS_REPORT },
  { label: "归档打印", value: DocumentPrintRecordSourceEnum.ARCHIVE_PRINT }
]

export function getDocumentPrintRecordType(type: string) {
  switch (type) {
    case DocumentPrintRecordTypeEnum.PRINT:
      return "打印"
    case DocumentPrintRecordTypeEnum.EXPORT:
      return "导出"
    default:
      return "--"
  }
}

export function getDocumentPrintRecordSource(source: string) {
  switch (source) {
    case DocumentPrintRecordSourceEnum.BORROW:
      return "借阅"
    case DocumentPrintRecordSourceEnum.SEAL:
      return "封存"
    case DocumentPrintRecordSourceEnum.HQMS_REPORT:
      return "HQMS上报"
    case DocumentPrintRecordSourceEnum.EMR_PRINT:
      return "病案打印"
    case DocumentPrintRecordSourceEnum.BORROW_PRINT:
      return "借阅打印"
    case DocumentPrintRecordSourceEnum.BORROW_EXPORT:
      return "借阅导出"
    case DocumentPrintRecordSourceEnum.SEALED_PRINT:
      return "封存打印"
    case DocumentPrintRecordSourceEnum.SEALED_EXPORT:
      return "封存导出"
    case DocumentPrintRecordSourceEnum.ARCHIVE_PRINT:
      return "归档打印"
    default:
      return "--"
  }
}
