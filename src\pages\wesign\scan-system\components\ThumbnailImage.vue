<template>
  <div style="height: 100%" @click="currentImage = null">
    <div v-if="captureClassifyArray && captureClassifyArray?.length > 0" class="thumbnail-image__content">
      <div v-for="nodes in captureClassifyArray" :key="nodes.mrClassCode" class="thumbnail-image-item-classify">
        <div class="thumbnail-image-item-classify_title">{{ nodes?.mrClassName }}</div>

        <!-- <draggable
          item-key="fileName"
          group="people"
          :list="nodes.imageFiles"
          class="thumbnail-image-item-classify__box"
        >
          <template #item="{ element: item }">
            <div
              class="thumbnail-image-item__box draggable"
              :class="currentImage?.id === item?.id ? 'is-active' : ''"
              @click.stop="selectItem(item)"
            >
              <img :src="item.fileBase64 || item.filePath" class="handle" />
              <div class="thumbnail-image-item__label">
                <OverflowTooltip :content="item.fileName" />
                <i class="ri-edit-line" @click.stop="openEditImageNameDialog(item)"></i>
              </div>
            </div>
          </template>
        </draggable> -->
        <VueDraggable
          v-model="nodes.imageFiles"
          item-key="fileName"
          group="people"
          class="thumbnail-image-item-classify__box"
        >
          <template v-for="item in nodes.imageFiles" :key="item.fileName">
            <div
              class="thumbnail-image-item__box draggable"
              :class="currentImage?.id === item?.id ? 'is-active' : ''"
              @click.stop="selectItem(item)"
            >
              <img :src="item.fileBase64 || item.filePath" class="handle" />
              <div class="thumbnail-image-item__label">
                <OverflowTooltip :content="item.fileName" />
                <i class="ri-edit-line" @click.stop="openEditImageNameDialog(item)"></i>
              </div>
            </div>
          </template>
        </VueDraggable>
      </div>
    </div>
    <EmptyContent v-else desc="暂无数据" />
  </div>

  <el-dialog
    v-model="editImageNameDialog.visible"
    top="40vh"
    width="30%"
    title="编辑缩略图名称"
    :close-on-click-modal="false"
  >
    <el-input v-model="editImageNameDialog.editedName" placeholder="请输入缩略图名称" />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeEditImageNameDialog">取消</el-button>
        <el-button type="primary" @click="handleEditImageName">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, reactive } from "vue"
import { VueDraggable } from "vue-draggable-plus"
import { EmptyContent, OverflowTooltip } from "@/base-components"
import { SystemAlert } from "@/utils"
import type { CaptureImageItem } from "../config"
const props = defineProps<{
  captureClassify: any
  currentImage: CaptureImageItem | null
}>()

const emits = defineEmits(["update:captureClassify", "update:currentImage"])

const captureClassifyArray = computed({
  set: val => {
    emits("update:captureClassify", val)
  },
  get: () => {
    return props.captureClassify
  }
})

const currentImage = computed({
  set: val => {
    emits("update:currentImage", val)
  },
  get: () => {
    return props.currentImage
  }
})

// 选中图片
const selectItem = (item: CaptureImageItem) => {
  currentImage.value = item
}

/*==================编辑图片名称==================*/

const editImageNameDialog = reactive({
  visible: false,
  editedName: ""
})

// 关闭编辑图片名称弹窗
const closeEditImageNameDialog = () => {
  editImageNameDialog.visible = false
  editImageNameDialog.editedName = ""
}

// 打开编辑图片名称弹窗
const openEditImageNameDialog = item => {
  currentImage.value = item
  editImageNameDialog.visible = true
  editImageNameDialog.editedName = item?.fileName
}

// 编辑图片名称
const handleEditImageName = () => {
  if (!currentImage.value) return SystemAlert("请选择缩略图")
  if (!editImageNameDialog.editedName) {
    return SystemAlert("请输入缩略图名称")
  }
  currentImage.value.fileName = editImageNameDialog.editedName
  editImageNameDialog.visible = false
}
</script>

<style lang="scss" scoped>
.thumbnail-image__content {
  padding: 20px;

  .thumbnail-image-item-classify {
    &_title {
      padding-bottom: 15px;
      font-weight: bold;
    }

    .thumbnail-image-item-classify__box {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      padding: 20px 15px;
      height: auto;
      border: 1px solid #dbdfe3;
      border-radius: 4px;
      margin-bottom: 10px;
      .thumbnail-image-item__box {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        border: 1px solid #dbdfe3;
        border-radius: 4px;
        width: 150px;
        margin-bottom: 10px;
        margin-right: 20px;
        cursor: pointer;
        &:hover {
          border: 1px solid #409eff;
        }
        img {
          width: 100%;
        }
        .thumbnail-image-item__label {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 14px;
          color: #81878e;
          background-color: #eff2f5;
          padding: 6px;

          i {
            cursor: pointer;
          }
        }
      }
    }
    .is-active {
      border: 1px solid #409eff !important;
      color: #409eff !important;
    }
  }
}
</style>
