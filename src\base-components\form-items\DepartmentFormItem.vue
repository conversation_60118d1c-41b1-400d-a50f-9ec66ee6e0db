<template>
  <el-form-item :label="props.label">
    <el-select
      v-model="departmentOptionValue"
      :filterable="true"
      :filter-method="handleFilter"
      clearable
      :multiple="props.multiple"
      :collapse-tags="props.collapseTags"
      :collapse-tags-tooltip="props.collapseTagsTooltip"
    >
      <el-option v-for="item in filterOptions" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>
  </el-form-item>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from "vue"
import { useGlobalOptionsStore } from "@/stores"
import type { BaseOptionItem } from "@/types"

const globalOptionsStore = useGlobalOptionsStore()

const filterOptions = ref<Array<BaseOptionItem>>([])

onMounted(() => {
  filterOptions.value = globalOptionsStore.departmentOptions
})

interface DepartmentFormProps {
  label: string
  modelValue: string | Array<string>
  multiple?: boolean
  collapseTags?: boolean
  collapseTagsTooltip?: boolean
}

const props = withDefaults(defineProps<DepartmentFormProps>(), {
  multiple: false
})

// const props = defineProps({
//   label: { type: String, required: true },
//   modelValue: { type: String, required: true },
//   multiple:{ type: Boolean, required: true },
// })

const emits = defineEmits(["update:modelValue"])

const departmentOptionValue = computed({
  get: () => props.modelValue,
  set: val => emits("update:modelValue", val)
})

function handleFilter(filterText: string) {
  filterOptions.value = globalOptionsStore.departmentOptions.filter(item => item.label.includes(filterText))
}
</script>
