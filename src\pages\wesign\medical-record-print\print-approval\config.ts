import type { TableColumnItem, BaseOptionItem } from "@/types"

export const menuId = "/print/approval"

export const waitingApprovedColumns: Array<TableColumnItem> = [
  { prop: "orderCode", label: "订单单号", minWidth: 190, must: true },
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 120, must: true },
  { prop: "patientIdCard", label: "身份证号", minWidth: 200 },
  { prop: "patientOutHospitalDatetime", label: "出院日期", minWidth: 180 },
  { prop: "name", label: "申请人", minWidth: 100, must: true },
  { prop: "phone", label: "申请人手机", minWidth: 150 },
  { prop: "applySourceStr", label: "申请来源", minWidth: 120 },
  { prop: "relationshipStr", label: "与患者关系", minWidth: 120 },
  { prop: "createdDatetime", label: "申请时间", minWidth: 180, sortable: true },
  { prop: "printTypeName", label: "打印类型", minWidth: 120 },
  { prop: "duplicateNum", label: "复印份数", minWidth: 120, sortable: true },
  { prop: "pageCount", label: "总页数", minWidth: 100, sortable: true },
  { prop: "orderAmount", label: "订单金额", minWidth: 100, must: true },
  { prop: "receiveTypeStr", label: "领取方式", minWidth: 110 },
  { prop: "statusStr", label: "审批状态", minWidth: 110, must: true },
  { prop: "operation", label: "操作", width: 100, fixed: "right", must: true }
]

export const approvedColumns: Array<TableColumnItem> = [
  { type: "selection", width: 40 },
  { prop: "orderCode", label: "订单单号", minWidth: 190, must: true },
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 120, must: true },
  { prop: "patientIdCard", label: "身份证号", minWidth: 200 },
  { prop: "patientOutHospitalDatetime", label: "出院日期", minWidth: 180 },
  { prop: "name", label: "申请人", minWidth: 100, must: true },
  { prop: "phone", label: "申请人手机", minWidth: 150 },
  { prop: "applySourceStr", label: "申请来源", minWidth: 120 },
  { prop: "relationshipStr", label: "与患者关系", minWidth: 120 },
  { prop: "createdDatetime", label: "申请时间", minWidth: 180, sortable: true },
  { prop: "printTypeName", label: "打印类型", minWidth: 120 },
  { prop: "duplicateNum", label: "复印份数", minWidth: 120, sortable: true },
  { prop: "pageCount", label: "总页数", minWidth: 100, sortable: true },
  { prop: "orderAmount", label: "订单金额", minWidth: 100, must: true },
  { prop: "receiveTypeStr", label: "领取方式", minWidth: 110 },
  { prop: "statusStr", label: "审批状态", minWidth: 110, must: true },
  { prop: "approverName", label: "审批人", minWidth: 120 },
  { prop: "approverDatetime", label: "审批时间", minWidth: 180, sortable: true },
  { prop: "operation", label: "操作", width: 180, fixed: "right", must: true }
]

export const fileGenerationExceptionColumns: Array<TableColumnItem> = [
  { type: "selection", width: 45, fixed: "left" },
  { prop: "orderCode", label: "订单单号", minWidth: 170, sortable: true, must: true },
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 100, must: true },
  { prop: "patientIdCard", label: "身份证号", minWidth: 150 },
  { prop: "patientOutHospitalDatetime", label: "出院日期", minWidth: 180, sortable: true },
  { prop: "duplicateNum", label: "复印份数", minWidth: 120, sortable: true, must: true },
  { prop: "pageCount", label: "总页数", minWidth: 120, sortable: true },
  { prop: "createdDatetime", label: "申请时间", minWidth: 180, sortable: true, must: true },
  { prop: "operation", label: "操作", width: 75, fixed: "right", must: true }
]

export const tabsRouterList = [
  {
    label: "待审批",
    path: "/print/approval/waiting-approved",
    name: "print_approval_waitingApproved"
  },
  {
    label: "已审批",
    path: "/print/approval/approved",
    name: "print_approval_approved"
  }
]

export const applyStatusOptions: Array<BaseOptionItem> = [
  { label: "已驳回", value: "noPass" },
  { label: "已通过", value: "pass" }
]
