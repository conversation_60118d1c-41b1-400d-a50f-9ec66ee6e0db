<template>
  <div v-loading="loading" class="home-page-container common-search-container">
    <template v-if="!isEdit">
      <div class="common-search-header">
        <TabsRouter :tabs-config="homePageTabsRouterList" />
      </div>
      <div class="header">
        <el-button type="primary" :icon="Edit" @click="toEdit('')">编辑</el-button>

        <ExportButton
          :export-api="exportScoreStandard"
          :export-params="exportParams"
          :file-name="route.meta.title + '.xlsx'"
          file-type="application/vnd.ms-excel"
        >
          导出
        </ExportButton>

        <ImportButton @click="importDialogRef?.openUserImportDialog()">导入</ImportButton>
      </div>

      <!-- 表格数据 -->
      <div class="table-container">
        <el-table
          :data="tableData"
          :span-method="objectSpanMethod"
          style="width: 100%; margin-bottom: 20px"
          row-key="id"
          :border="true"
          default-expand-all
        >
          <el-table-column v-for="item in tableColumns" :key="item.prop" :prop="item.prop" :label="item.label" />

          <el-table-column width="100" prop="status" label="状态">
            <template #default="{ row }">
              <el-tag :type="row.status === 'ENABLE' ? 'success' : 'danger'">
                {{ row.status === "ENABLE" ? "已启用" : "已禁用" }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column width="100" label="操作">
            <template #default="{ row }">
              <TableButton @click="modifyStatus(row)">
                {{ row.status === "ENABLE" ? "禁用" : "启用" }}
              </TableButton>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </template>

    <RouterView v-else></RouterView>

    <BatchImport
      ref="importDialogRef"
      :download-template-api="downloadScoreStandardTemplate"
      :import-file-api="importScoreStandardTemplate"
      :import-params="{ scoreType: scoreTypeConfig[route.meta.title as string] }"
      :confirm-callback="toEdit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, reactive } from "vue"
import { useRouter, useRoute } from "vue-router"
import { Edit } from "@element-plus/icons-vue"
import { TableButton, ExportButton, ImportButton } from "@/base-components"
import { TabsRouter, BatchImport } from "@/page-components"
import { queryScoreStandard } from "@/interfaces"
import { SystemPrompt, Message, toastError } from "@/utils"
import {
  updateGivenScoreStandard,
  exportScoreStandard,
  downloadScoreStandardTemplate,
  importScoreStandardTemplate
} from "../interface"
import { homePageTabsRouterList, scoreTypeConfig, tableColumns } from "./config"
import type { TableColumnCtx } from "element-plus"

const importDialogRef = ref<InstanceType<typeof BatchImport>>()

/* ======================== 表格相关数据及方法 ======================== */

interface TableParam {
  PROBLEM_CATEGORIES: string
  PROBLEM_SUBCLASS: string
  SCORE_ITEM: string
  singleReject: "YES" | "NO"
  rejectLevel?: "LEVE_ONE" | "LEVE_TWO" | "LEVE_THREE" | ""
  score: number | string
  status: "ENABLE" | "DISABLE"
  wsid: string
}
const tableData = ref(Array<TableParam>(0))

interface SpanMethodProps {
  row: TableParam
  column: TableColumnCtx<TableParam>
  rowIndex: number
  columnIndex: number
}

const singleRejectData = {
  YES: "是",
  NO: "否"
}
const rejectLevelData = {
  LEVE_ONE: "甲",
  LEVE_TWO: "乙",
  LEVE_THREE: "丙"
}
const loading = ref(false)
const route = useRoute()
const router = useRouter()
const exportParams = reactive({
  documentType: "",
  status: "",
  scoreType: scoreTypeConfig[route.meta.title as string]
})
// 加载评分项
const getData = scoreType => {
  if (loading.value) return
  loading.value = true
  tableData.value = []
  queryScoreStandard({ documentType: "", scoreType: scoreType })
    .then(resData => {
      resData.forEach(item => {
        if (item.children) {
          item.children.forEach(data => {
            // level = 2 的评分项
            if (data.type === "SCORE_ITEM") {
              tableData.value.push({
                PROBLEM_CATEGORIES: `${item.label}(${item.totalScore || ""}分)`,
                PROBLEM_SUBCLASS: "",
                SCORE_ITEM: data.label,
                singleReject: singleRejectData[data.singleReject],
                rejectLevel: rejectLevelData[data.rejectLevel] || "",
                score: data.score,
                status: data.status,
                wsid: data.wsid
              })
            }
            // else {
            //   tableData.value.push({
            //     PROBLEM_CATEGORIES: item.label,
            //     PROBLEM_SUBCLASS: data.label,
            //     SCORE_ITEM: "",
            //     singleReject: singleRejectData[data.singleReject],
            //     rejectLevel: rejectLevelData[data.rejectLevel] || "",
            //     score: data.score,
            //     status: data.status,
            //     wsid: data.wsid
            //   })
            // }
            // 初始化table数据
            data.children.forEach(node => {
              tableData.value.push({
                PROBLEM_CATEGORIES: `${item.label}(${item.totalScore || ""}分)`,
                PROBLEM_SUBCLASS: data.label,
                SCORE_ITEM: node.label,
                singleReject: singleRejectData[node.singleReject],
                rejectLevel: rejectLevelData[node.rejectLevel] || "",
                score: node.score,
                status: node.status,
                wsid: node.wsid
              })
            })
          })
        }
      })
    })
    .finally(() => {
      loading.value = false
    })
}

// 页面初始化
onMounted(() => {
  getData(scoreTypeConfig[route.meta.title as string])
  isEdit.value = route.path.indexOf("content-setting") !== -1
})

// 合并值相同的row
const objectSpanMethod = ({ row, column, rowIndex, columnIndex }: SpanMethodProps) => {
  if (columnIndex === 0 || columnIndex === 1) {
    // 获取当前单元格的值
    const currentValue = row[column.property]
    // 获取上一行相同列的值
    const preRow = tableData.value[rowIndex - 1]
    const preValue = preRow ? preRow[column.property] : null
    // 如果当前值和上一行的值相同，则将当前单元格隐藏
    if (currentValue === preValue) {
      return { rowspan: 0, colspan: 0 }
    } else {
      // 否则计算当前单元格应该跨越多少行
      let rowspan = 1
      for (let i = rowIndex + 1; i < tableData.value.length; i++) {
        const nextRow = tableData.value[i]
        const nextValue = nextRow[column.property]
        if (nextValue === currentValue) {
          rowspan++
        } else {
          break
        }
      }
      return { rowspan, colspan: 1 }
    }
  }
}

// 更新指定评分标准的状态
const modifyStatus = row => {
  const nextStatus = row.status === "ENABLE" ? "DISABLE" : "ENABLE"
  SystemPrompt(`是否确定${row.status === "ENABLE" ? "禁用" : "启用"}评分项"${row.SCORE_ITEM}"？`).then(() => {
    updateGivenScoreStandard({ wsid: row.wsid, status: nextStatus })
      .then(() => {
        Message.success("修改状态成功")
        getData(scoreTypeConfig[route.meta.title as string])
      })
      .catch(err => toastError(err))
  })
}

const isEdit = ref(false)

watch(
  () => route,
  val => {
    if (val.meta.title !== "首页质控" && val.meta.title !== "科室质控" && val.meta.title !== "终末质控") return
    getData(scoreTypeConfig[val.meta.title])
    exportParams.scoreType = scoreTypeConfig[val.meta.title]
    // 判断是否停留在编辑界面
    isEdit.value = val.path.indexOf("content-setting") !== -1
    // 清空导入数据
    localStorage.removeItem("importData")
  },
  { immediate: true, deep: true }
)

// 编辑
const toEdit = (data: any) => {
  isEdit.value = true
  localStorage.setItem("importData", JSON.stringify(data || []))
  router.push({
    path: `/quality-control-config/scoring-standard/${route.meta.type}/content-setting`,
    query: {
      scoreType: scoreTypeConfig[route.meta.title as string]
    }
  })
}
</script>

<style lang="less" scoped>
.home-page-container {
  height: 100%;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 35, 114, 0.1);

  .header {
    height: 40px;
    padding: 10px 20px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    background-color: #fff;
  }
  .body {
    height: calc(100% - 60px);
    overflow: auto;
    padding: 0 20px;
  }
}

.level {
  display: flex;
  flex-wrap: wrap;
}

.content {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 10px;

  .form-item {
    margin-right: 15px;
  }
}

.level-1-content {
  background: #e1e2e6;
}

.operation-container {
  display: flex;
  align-items: center;
  float: right;
}

.children-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding-left: 30px;
}

.common-search-header {
  margin-bottom: 0;
  border-radius: 0;
}

:deep(.el-form) {
  display: flex;
}
:deep(.el-form-item) {
  margin-bottom: 0;
}

.table-container {
  padding: 10px 20px;
  background: #fff;
}
</style>
