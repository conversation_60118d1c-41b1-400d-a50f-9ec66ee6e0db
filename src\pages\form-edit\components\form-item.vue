<template>
  <!-- 文本 -->
  <el-input
    v-if="props.config.type === FormItemType.BaseInput"
    size="large"
    :value="props.config.default"
    :type="props.config.lineType === 'single' ? 'text' : 'textarea'"
    readonly
    :placeholder="props.config.placeholder ? props.config.placeholder : `请输入${props.config.name}`"
  />

  <el-input
    v-if="props.config.type === FormItemType.BloodDonationBarcode"
    size="large"
    :value="props.config.default"
    readonly
    :placeholder="props.config.placeholder ? props.config.placeholder : `请输入${props.config.name}`"
  />

  <!-- 数字输入框 -->
  <NumberInput
    v-if="props.config.type === FormItemType.BaseNumber"
    size="large"
    readonly
    :value="props.config.default"
    :placeholder="props.config.placeholder ? props.config.placeholder : `请输入${props.config.name}`"
    :unit="props.config.unit"
    :precision="props.config?.allowDecimal ? props.config.precision : undefined"
  >
    <!-- <span v-if="props.config.unit && props.config.unit?.length > 0">{{ props.config.unit }}</span> -->
  </NumberInput>

  <!-- 说明文本 -->
  <div v-if="props.config.type === FormItemType.DescText" class="output" v-html="props.config.content"></div>

  <!-- 日期选择 -->
  <el-date-picker
    v-else-if="props.config.type === FormItemType.BaseDate"
    :model-value="defaultDate"
    readonly
    size="large"
    :type="getDateType(props.config.dateFormat)"
    :format="props.config.dateFormat"
    value-format="x"
    :placeholder="props.config.placeholder ? props.config.placeholder : `请选择${props.config.name}`"
  />

  <!-- 单选框 -->
  <el-radio-group
    v-else-if="[FormItemType.BaseRadio].includes(props.config.type)"
    size="large"
    :model-value="props.config.default"
    :style="choiceFormItemStyle"
  >
    <el-radio v-for="option in props.config.options" :key="option.value" :label="option.value">
      {{ option.label }}
    </el-radio>
  </el-radio-group>

  <!-- 下拉选择 -->
  <div v-else-if="[FormItemType.BaseSelect].includes(props.config.type)" class="select-output">
    <div>{{ selectValue }}</div>
    &nbsp;
    <i class="ri-arrow-down-s-line"></i>
  </div>

  <!-- 多选框 -->
  <el-checkbox-group
    v-else-if="props.config.type === FormItemType.BaseCheckbox"
    size="large"
    :style="choiceFormItemStyle"
    :value="[]"
    :model-value="props.config.default"
  >
    <el-checkbox v-for="option in props.config.options" :key="option.value" :label="option.label"></el-checkbox>
  </el-checkbox-group>

  <!-- 用户签名 -->
  <div
    v-else-if="props.config.type === FormItemType.UserSign"
    :style="{
      width: '100%',
      height: '100%',
      backgroundColor: '#f8f9fc',
      color: '#606266',
      fontSize: '20px',
      padding: '20px 0px',
      textAlign: 'center'
    }"
  >
    {{ props.config.name || "献血者签名" }}
  </div>

  <!-- 知情同意书签名 -->
  <div
    v-else-if="props.config.type === FormItemType.InformedConsentFormSign"
    :style="{
      width: '100%',
      height: '100%',
      backgroundColor: '#f8f9fc',
      color: '#606266',
      fontSize: '20px',
      padding: '20px 0px',
      textAlign: 'center'
    }"
  >
    {{ props.config.name || "知情同意书签名" }}
  </div>

  <!-- 工作人员签名 -->
  <div
    v-else-if="props.config.type === FormItemType.StaffSign"
    :style="{
      width: '100%',
      height: '100%',
      backgroundColor: '#f8f9fc',
      color: '#606266',
      fontSize: '20px',
      padding: '20px 0px',
      textAlign: 'center'
    }"
  >
    工作人员签名
  </div>

  <!-- 地址 -->
  <div
    v-if="props.config.type === FormItemType.BaseAddress"
    :style="{
      width: '100%',
      height: '100%'
    }"
  >
    <div class="select-output">
      <div>{{ props.config.addressSelect?.join("/") }}</div>
      &nbsp;
      <i class="ri-arrow-down-s-line"></i>
    </div>

    <el-input
      :value="props.config.default"
      readonly
      size="large"
      type="textarea"
      :placeholder="props.config.placeholder ? props.config.placeholder : `请输入${props.config.name}`"
    />
  </div>

  <!-- 表格 -->
  <div v-else-if="props.config.type === FormItemType.Table" class="table-container">
    <VueDraggable
      ref="el"
      v-model="config.tableFormConfig"
      class="table-form-edit-area"
      :class="{ 'border-none': config.tableFormConfig.length > 0 }"
      :style="{ 'flex-wrap': config.tableArrangement === 'list' ? 'wrap' : '' }"
      ghost-class="ghost"
      group="people"
      filter=".el-empty"
      :animation="500"
      :delay="0"
    >
      <template v-if="props.config.tableFormConfig.length || true">
        <TableFormItem
          v-for="(item, index) in props.config.tableFormConfig"
          :key="item.prop"
          :prop="item.prop"
          :config="item"
          :index="index"
          :edit-card="editCard"
          :copy-card="copyCard"
          :delete-card="deleteCard"
          :active-table-config-index="props.activeTableConfigIndex"
          :active-table-config="activeTableConfig"
          :table-config="props.config"
          @click.stop="editCard(item)"
        />
      </template>
      <el-empty v-else style="width: 100%"></el-empty>
    </VueDraggable>
  </div>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from "vue"
import { regionData } from "element-china-area-data"
import { cloneDeep } from "lodash/fp"
import { v4 as uuidv4 } from "uuid"
import { VueDraggable } from "vue-draggable-plus"
import { NumberInput } from "@/base-components"
import { FormItemType, FormItemConfig } from "@/configs"
import { formatDatetime, getDateType, Message, SystemPrompt } from "@/utils"
import TableFormItem from "./table-form-item.vue"

const props = defineProps<{
  // prop: string
  config: FormItemConfig
  // index: number
  activeTableConfigIndex: number
  activeTableConfig: FormItemConfig
}>()

// 单选和多选框样式
const choiceFormItemStyle = computed(() => {
  const arrangement = props.config.arrangement
  if (arrangement === "vertical") {
    return {
      flexDirection: "column",
      alignItems: "flex-start",
      display: "flex"
    }
  }
  return {
    flexDirection: "row",
    alignItems: "center",
    display: "flex"
  }
})

const emits = defineEmits(["edit", "delete"])

// 日期的默认值
const defaultDate = computed(() => {
  let datetime = "" as string | Date | number
  if (props.config.default === "custom") {
    datetime = props.config.customValue
  } else {
    datetime = new Date().getTime()
  }
  // 将日期按照props.config.format格式化
  return formatDatetime(datetime as number, props.config.dateFormat)
})

// 下拉值
const selectValue = computed(() => {
  //如果default是数组，说明是多选,需要用,拼接
  if (Array.isArray(props.config.default)) {
    let str = ""
    props.config.default.forEach((item, index) => {
      str += props.config.options?.find(option => option.value === item)?.label
      if (index < props.config.default.length - 1) {
        str += ","
      }
    })
    return str
  }
  return props.config.options?.find(option => option.value === props.config.default)?.label
})

// 寻找当前的表单项
const findCard = (prop: string, callback?: any) => {
  const card = props.config?.tableFormConfig?.filter(item => item.prop === prop)?.[0]
  const res = {
    card,
    index: props.config?.tableFormConfig?.indexOf(card)
  }
  if (callback) {
    callback(res)
  }
  return res
}

const propsPanelVisible = ref(false)
const activeConfigIndex = ref(-1)

// 编辑表单项属性
function editCard(item: any) {
  // const formItem = props.config?.tableFormConfig?.find(item => item.prop === prop)
  // if (!formItem) return
  // if (formItem?.type === FormItemType.IdCardGroup) {
  //   return Message.warning("身份证组件不支持编辑")
  // }

  // if (formItem?.type === FormItemType.BloodDonationBarcode) {
  //   return Message.warning("献血条码组件不支持编辑")
  // }

  // const { index } = findCard(prop)
  // if (index <= -1) return
  // propsPanelVisible.value = true
  // activeConfigIndex.value = index
  emits("edit", item, props.config)
}

// 删除表单项
function deleteCard(prop: string) {
  SystemPrompt("是否确认删除该字段").then(() => {
    const { index } = findCard(prop)
    if (index > -1) {
      props.config?.tableFormConfig?.splice(index, 1)
      emits("delete")
    }
  })
}

// 复制表单项
function copyCard(config: FormItemConfig) {
  const newConfig = cloneDeep(config)
  newConfig.prop = `${uuidv4().slice(0, 8)}`
  // 找到被复制的表单项
  const { index } = findCard(config.prop)
  // 插入到当前表单项的下一个
  props.config?.tableFormConfig?.splice(index + 1, 0, newConfig)
}
</script>

<style lang="less" scoped>
.select-output {
  width: calc(100% - 16px);
  border: 1px solid var(--el-border-color);
  color: #606266;
  font-size: var(--custom-larger-font-size);
  border-radius: 4px;
  font-size: var(--custom-small-font-size);
  padding-left: 4px;
  display: flex;
  height: 38px;
  line-height: 38px;
  margin-bottom: 8px;
  padding-right: 12px;
  div {
    display: inline-block;
    width: calc(100% - 20px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.table-form-edit-area {
  display: flex;
  justify-content: space-between;
  min-height: 40px;
  align-items: center;
  border: 1px solid #ccc;
}
.table-container {
  width: 100%;
  height: 100%;
  padding: 1px;
}
.border-none {
  border: none;
}
:deep(.el-textarea__inner) {
  height: 40px !important;
}
</style>
