<template>
  <DialogContainer v-model:visible="state.visible" title="质控进度查询" :width="1150" :no-footer="true">
    <!-- 搜索 -->
    <SearchContainer style="align-items: flex-start" @query-btn-click="handleQuery" @reset-btn-click="handleReset">
      <CommonInputFormItem v-model="formData.qcOperator" label="质控员" placeholder="请输入质控员姓名" />

      <DaterangeFormItem v-model:model-value="formData.createDatetime" label="分配时间" />

      <CommonSelectFormItem v-model:model-value="formData.qcStatus" label="质控状态" :options="qcStatusOptions" />
    </SearchContainer>

    <!-- 进度列表 -->
    <CommonTable
      class="process-search-table"
      :request-api="getDistributeQcTaskApi"
      :table-columns="processSearchColumns"
      :request-params="{
        urlType: `${QualityControlTypeEnum.CROSS_QC_MEDICAL}-${QualityControlTypeEnum.CROSS_QC_NURSE}`,
        currentDept: 1,
        ...searchParams
      }"
    >
      <template #qcStatus="{ row }">
        <el-tag :type="getQcStatusTagType(row.qcStatus)">{{ getQcStatus(row.qcStatus) }}</el-tag>
      </template>
      <template #qcType="{ row }">
        {{ getQcTaskType(row.qcType) }}
      </template>
      <template #createDatetime="{ row }">
        {{ formatDatetime(row.createDatetime) }}
      </template>
      <template #qcDatetime="{ row }">
        {{ formatDatetime(row.qcDatetime) }}
      </template>
    </CommonTable>
  </DialogContainer>
</template>

<script setup lang="ts">
import { reactive } from "vue"
import {
  CommonSelectFormItem,
  CommonInputFormItem,
  CommonTable,
  DaterangeFormItem,
  DialogContainer,
  SearchContainer
} from "@/base-components"
import { qcStatusOptions, QualityControlTypeEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { getDistributeQcTaskApi } from "@/interfaces"

import { formatDatetime, getQcStatusTagType, getQcStatus, getQcTaskType } from "@/utils"
import { processSearchColumns } from "../../config"

const state = reactive({
  visible: false
})

/* ======================搜索======================*/

const formData = reactive({ qcOperator: "", createDatetime: "", qcStatus: "" })

const { searchParams, handleQuery, handleReset } = useTableSearch(formData)

defineExpose({
  openDialog: () => {
    state.visible = true
  }
})
</script>

<style lang="less" scoped>
.process-search-table {
  height: 450px;
}
</style>
