import type { TableColumnItem, BaseOptionItem } from "@/types"

export const tabsRouterList = [
  { path: "/coding/task-assignment/hospitalization", label: "住院" },
  { path: "/coding/task-assignment/outpatient-and-emergency", label: "门急诊" }
]

export const hospitalizationColumns: Array<TableColumnItem> = [
  { type: "selection", width: 50, fixed: "left" },
  { prop: "patientName", label: "姓名", minWidth: 200, must: true },
  { prop: "patientSex", label: "性别", minWidth: 80 },
  { prop: "patientAge", label: "年龄", minWidth: 100, sortable: true },
  // { prop: "mrNo", label: "住院号", minWidth: 150, must: true },
  { prop: "inpNo", label: "住院号", minWidth: 150, must: true },
  // { prop: "patientId", label: "患者编号", minWidth: 100, must: true },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 120 },
  { prop: "outHospitalDatetime", label: "出院时间", minWidth: 180, sortable: true },
  // { prop: "dischargeDiagnosisName", label: "出院主诊断", minWidth: 120, must: true },
  // { prop: "residentDoctor", label: "住院医师", minWidth: 120 },
  { prop: "attendingDoctor", label: "主治医师", minWidth: 120, must: true },
  { prop: "catalogerName", label: "编码员", minWidth: 120 },
  { prop: "catalogStatusEnum", label: "状态", minWidth: 100, must: true }
]

export const outpatientAndEmergencyColumns: Array<TableColumnItem> = [
  { type: "selection", width: 50, fixed: "left" },
  { prop: "patientName", label: "姓名", minWidth: 120, must: true },
  { prop: "patientSex", label: "性别", minWidth: 80 },
  { prop: "registerNo", label: "门诊号", minWidth: 150, must: true },
  { prop: "medicalNo", label: "就诊卡号", minWidth: 100, must: true },
  { prop: "mainDiagnostic", label: "主要诊断", minWidth: 120, must: true },
  { prop: "deptName", label: "就诊科室", minWidth: 120 },
  { prop: "consultationTime", label: "就诊时间", minWidth: 180, sortable: true },
  { prop: "doctorName", label: "就诊医师", minWidth: 120 },
  { prop: "catalogerName", label: "编码员", minWidth: 120 },
  { prop: "catalogStatus", label: "状态", minWidth: 100, must: true }
]

export const processColumns: Array<TableColumnItem> = [
  { prop: "patientId", label: "患者编号", minWidth: 120 },
  { prop: "patientName", label: "姓名", minWidth: 120 },
  { prop: "patientAge", label: "年龄", minWidth: 80, sortable: true },
  { prop: "medicalNo", label: "病案号", minWidth: 150 },
  { prop: "patientSex", label: "性别", minWidth: 100, sortable: true },
  { prop: "assignmentTime", label: "任务分配时间", minWidth: 180, sortable: true },
  { prop: "catalogerName", label: "编码员", minWidth: 100 },
  { prop: "catalogStatus", label: "状态", minWidth: 100 },
  { prop: "catalogTime", label: "编码时间", minWidth: 180, sortable: true }
]

export const outPatientProcessColumns: Array<TableColumnItem> = [
  { prop: "registerNo", label: "门诊号", minWidth: 150, must: true },
  { prop: "patientName", label: "姓名", minWidth: 120 },
  // { prop: "patientAge", label: "年龄", minWidth: 80, sortable: true },
  { prop: "medicalNo", label: "就诊卡号", minWidth: 100, must: true },
  { prop: "patientSex", label: "性别", minWidth: 100, sortable: true },
  { prop: "assignmentTime", label: "任务分配时间", minWidth: 180, sortable: true },
  { prop: "catalogerName", label: "编码员", minWidth: 100 },
  { prop: "catalogStatus", label: "状态", minWidth: 100 },
  { prop: "catalogTime", label: "编码时间", minWidth: 180, sortable: true }
]

export const catalogStatusOptions: Array<BaseOptionItem> = [
  { label: "未编码", value: "NOT_CATALOG" },
  { label: "已编码", value: "CATALOGED" }
]

export const manualColumns: Array<TableColumnItem> = [
  { type: "selection", width: 50, fixed: "left" },
  { prop: "jobId", label: "工号", minWidth: 150 },
  { prop: "catalogerName", label: "编码员", minWidth: 200 }
]

export const userDeptColumns: Array<TableColumnItem> = [
  { prop: "jobId", label: "工号", minWidth: 100 },
  { prop: "catalogerName", label: "编码员", minWidth: 200 },
  { prop: "deptWsids", label: "所属科室", minWidth: 600 }
]

export const catalogStatusEnum = {
  NOT_CATALOG: "未编目",
  CATALOGED: "已编目"
}

export const outpatientFilterFormOptions = [
  { label: "姓名", value: "patientName" },
  { label: "就诊卡号", value: "medicalNo" },
  { label: "门诊号", value: "registerNo" }
]
