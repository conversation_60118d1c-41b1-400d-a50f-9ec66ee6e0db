<template>
  <PageContainer separate>
    <!-- <template #search></template> -->

    <template #table>
      <CommonTable
        ref="commonTableRef"
        table-id="secrecySettingTable"
        :table-columns="secrecySettingColumns"
        :request-api="getSecreciesList"
        :request-params="searchParams"
      >
        <template #header>
          <div style="margin-bottom: 12px">
            <AddButton @click="handle('add', {})">新增</AddButton>
          </div>
        </template>
        <template #operation="{ row }">
          <TableButton @click="handle('edit', row)">编辑</TableButton>
          <TableButton @click="handleDelete(row)">删除</TableButton>
        </template>
      </CommonTable>
    </template>

    <DialogContainer
      v-model:visible="dialogVisible"
      :title="isEdit ? '编辑' : '新增'"
      :width="480"
      :confirm-callback="handleConfirm"
      :confirm-loading="confirmLoading"
    >
      <el-form
        ref="dialogFormRef"
        :model="dialogFormData"
        :rules="secrecyFormRules"
        label-width="145"
        label-suffix="："
      >
        <CommonInputFormItem v-model="dialogFormData.secrecyName" label="密级名称" prop="secrecyName" />
        <CommonInputFormItem
          v-model="dialogFormData.secrecyValue"
          label="密级等级"
          prop="secrecyValue"
          placeholder="请输入密级等级（数字越小，密级越高）"
        />
        <CommonInputFormItem v-model="dialogFormData.describe" label="描述" prop="describe" />
      </el-form>
    </DialogContainer>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import {
  CommonInputFormItem,
  PageContainer,
  CommonTable,
  DialogContainer,
  AddButton,
  TableButton
} from "@/base-components"
import { useTableSearch } from "@/hooks"

import { getSecreciesList } from "@/interfaces"
import { toastError, Message, SystemPrompt } from "@/utils"
import { secrecySettingColumns, secrecyFormRules } from "./config"
import { addSecrecies, updateSecrecies, deleteSecrecies } from "./interface"

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

const commonTableRef = ref<InstanceType<typeof CommonTable>>()

/* ======================== 增加、编辑、删除 ======================== */

const isEdit = ref(false)
const dialogVisible = ref(false)
const confirmLoading = ref(false)

const dialogFormData = reactive({
  secrecyValue: "",
  secrecyName: "",
  describe: ""
})
const activeSecrecyId = ref("")
const dialogFormRef = ref()

// 新增、编辑
const handle = async (type: string, row: Record<string, any>) => {
  dialogVisible.value = true
  dialogFormData.secrecyValue = row?.secrecyValue || ""
  dialogFormData.secrecyName = row?.secrecyName || ""
  dialogFormData.describe = row?.describe || ""
  activeSecrecyId.value = row.id
  isEdit.value = type === "edit"
}

const handleConfirm = () => {
  if (confirmLoading.value) return
  confirmLoading.value = true
  dialogFormRef.value?.validate(isValid => {
    if (!isValid) return
    const handle = isEdit.value ? updateSecrecies : addSecrecies
    handle({
      ...dialogFormData,
      secrecyId: isEdit.value ? activeSecrecyId.value : ""
    })
      .then(res => {
        Message.success("操作成功！")
        confirmLoading.value = false
        dialogVisible.value = false
        commonTableRef.value?.refreshTableData()
      })
      .catch(err => {
        confirmLoading.value = false
        toastError(err)
      })
  })
}

// 删除
const handleDelete = row => {
  SystemPrompt(`您确定要删除"${row.secrecyName}"？`, "error").then(() =>
    deleteSecrecies(row.id)
      .then(res => {
        Message.success("删除成功！")
        commonTableRef.value?.refreshTableData()
      })
      .catch(err => toastError(err))
  )
}
</script>
