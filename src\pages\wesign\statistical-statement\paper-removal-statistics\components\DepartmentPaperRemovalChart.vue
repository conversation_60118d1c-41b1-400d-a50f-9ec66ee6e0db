<template>
  <div class="chart-container">
    <div class="chart-header flex-end">
      <div class="table-chart-tool">
        <el-tooltip content="表格展示">
          <i v-if="isDisplayTable"><img src="@/assets/svg/statistical-statement/table-active.svg" alt="" /></i>
          <i v-else @click="() => emits('update:showChart', !isDisplayTable)">
            <img src="@/assets/svg/statistical-statement/table.svg" alt="" />
          </i>
        </el-tooltip>
        <el-tooltip content="图表展示">
          <i v-if="isDisplayTable" @click="() => emits('update:showChart', !isDisplayTable)">
            <img src="@/assets/svg/statistical-statement/chart.svg" alt="" />
          </i>
          <i v-else><img src="@/assets/svg/statistical-statement/chart-active.svg" alt="" /></i>
        </el-tooltip>
      </div>
    </div>
    <v-chart :option="options" autoresize style="min-height: 400px"></v-chart>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue"
import { BarChart } from "echarts/charts"
import { GridComponent, TooltipComponent } from "echarts/components"
import { use } from "echarts/core"
import { CanvasRenderer } from "echarts/renderers"
import VChart from "vue-echarts"
import { toastError } from "@/utils"
import { getDepartmentPaperStatisticsApi } from "../../interface"

interface PropsType {
  showChart: boolean
  requestParams: any
}

const emits = defineEmits(["update:showChart"])

const props = defineProps<PropsType>()

const isDisplayTable = computed({
  get: () => props.showChart,
  set: val => emits("update:showChart", val)
})

const xAxisData = ref([] as any)
const seriesData = ref([] as any)

function formatChatData(data) {
  const tempXAxisData = [] as any
  const tempSeriesData = [] as any
  data.forEach(item => {
    item.deptName ? tempXAxisData.push(item.deptName) : tempXAxisData.push("")
    tempSeriesData.push(item.totalElectronicPage)
  })
  xAxisData.value = tempXAxisData
  seriesData.value = tempSeriesData
}

function getChatData() {
  getDepartmentPaperStatisticsApi(props.requestParams)
    .then(res => {
      formatChatData(res.data.data.rows)
    })
    .catch(err => {
      toastError(err)
    })
}

watch(() => props.requestParams, getChatData, { immediate: true })

use([TooltipComponent, GridComponent, BarChart, CanvasRenderer])

const options = ref({
  grid: {
    left: "2%",
    right: "2%"
  },
  tooltip: {},
  xAxis: {
    type: "category",
    data: xAxisData
  },
  yAxis: {
    type: "value"
  },
  series: [
    {
      data: seriesData,
      type: "bar",
      barWidth: "10%",
      color: "#3860F4",
      itemStyle: {
        borderRadius: [20, 20, 0, 0]
      }
    }
  ]
})
</script>

<style lang="less" scoped>
.chart-container {
  height: calc(100% - 20px);

  i {
    cursor: pointer;
  }
}
</style>
