<template>
  <el-drawer v-model="visible" title="借阅审批" :size="720" destroy-on-close>
    <div v-loading="drawerLoading">
      <el-descriptions :column="2">
        <el-descriptions-item label="选择文件：" width="60%">
          {{ state.approvalInfo.fileCount }}
        </el-descriptions-item>
        <el-descriptions-item label="借阅时长：" width="40%">
          <!-- <el-select
            v-model="state.approvalInfo.timeLimitCh"
            :disabled="state.allowEdit === 'DISABLE'"
            style="width: 150px"
          >
            <el-option v-for="item in timeOptions" :key="item.label" :label="item.label" :value="item.label" />
          </el-select> -->
          <div class="time-limit">
            <el-input
              v-if="!state.approvalInfo.long"
              v-model="state.approvalInfo.timeLimitCh"
              type="number"
              :min="0"
              @input="getTimeLimitCh"
            ></el-input>
            <el-input v-else value="永久" disabled></el-input>
            <el-select v-model="state.approvalInfo.timeLimitUnit" :disabled="state.approvalInfo.long">
              <el-option v-for="item in timeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-checkbox v-model="state.approvalInfo.long" label="永久" size="large" />
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="借阅权限：" width="60%">
          <el-checkbox-group
            v-model="state.checkedPermissionList"
            :disabled="state.allowEdit === 'DISABLE'"
            style="display: inline-block"
          >
            <el-checkbox label="viewPermission" disabled>查看</el-checkbox>
            <el-checkbox label="printPermission">打印</el-checkbox>
            <el-checkbox label="exportPermission">导出</el-checkbox>
          </el-checkbox-group>
        </el-descriptions-item>
        <el-descriptions-item label="借阅类型：" width="40%">
          {{ state.approvalInfo.applyTypeName }}
        </el-descriptions-item>
        <el-descriptions-item label="借阅原因：" width="60%">
          {{ state.approvalInfo.applyReason || "--" }}
        </el-descriptions-item>
        <el-descriptions-item label="病人姓名：" width="40%">{{ state.patientName }}</el-descriptions-item>
        <el-descriptions-item label="审批类型：" width="40%">
          <el-radio-group v-model="state.approvalType" :disabled="state.allowEdit === 'DISABLE'">
            <el-radio :label="1">按文件</el-radio>
            <el-radio :label="2">按页数</el-radio>
          </el-radio-group>
        </el-descriptions-item>
      </el-descriptions>

      <div v-show="state.approvalType === 1" class="approval-tree">
        <div v-if="state.allowEdit === 'DISABLE'" class="approval-tree__mask"></div>
        <div class="approval-tree-header">
          <div class="approval-tree-title">病案文书</div>
          <div class="approval-check-buttons">
            <el-checkbox
              v-model="documentCheckAll"
              :indeterminate="isDocumentIndeterminate"
              @change="handleDocumentCheckAll"
            >
              <span style="position: absolute; right: 150%; top: 50%; transform: translateY(-50%)">全选</span>
            </el-checkbox>
          </div>
        </div>
        <div class="approval-tree-content">
          <CommonTree
            ref="documentTreeRef"
            style-type="approval"
            :data="state.documentTree"
            show-checkbox
            @check-node="handleCheckDocument"
          />
        </div>
      </div>

      <div v-show="state.approvalType === 2" class="approval-tree">
        <div v-if="state.allowEdit === 'DISABLE'" class="approval-tree__mask"></div>
        <div class="approval-tree-header">
          <div class="approval-tree-title">病案文件</div>
          <div class="approval-check-buttons">
            <el-checkbox v-model="pageCheckAll" :indeterminate="isPageIndeterminate" @change="handlePageCheckAll">
              <span style="position: absolute; right: 150%; top: 50%; transform: translateY(-50%)">全选</span>
            </el-checkbox>
          </div>
        </div>
        <div class="approval-tree-content">
          <CommonTree
            ref="pageTreeRef"
            style-type="approval"
            :data="state.pageTree"
            show-checkbox
            @check-node="handleCheckPage"
          />
        </div>
      </div>
    </div>

    <ApprovalDetail :business-key="state.approvalInfo.borrowWsid" />

    <template #footer>
      <el-form-item label="审批意见：">
        <el-input
          v-model="state.comment"
          type="textarea"
          maxlength="300"
          show-word-limit
          placeholder="请填写审批意见"
          :autosize="{ minRows: 2, maxRows: 4 }"
        ></el-input>
      </el-form-item>
      <div v-loading="state.confirmLoading">
        <el-popover v-if="showSendBackButton" trigger="click">
          <template #reference>
            <el-button plain>退回</el-button>
          </template>
          <PopoverButton v-for="node in processNodeList" :key="node.defKey" @click="handleSendBack(node)">
            {{ node.name }}
          </PopoverButton>
        </el-popover>

        <el-button type="danger" @click="handleReject">驳回</el-button>
        <el-button type="primary" @click="confirmApproval(true)">通过</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { reactive, ref, computed } from "vue"
import { cloneDeep } from "lodash-es"
import { CommonTree, PopoverButton } from "@/base-components"
import { ApprovalDetail } from "@/page-components"
import { getApprovalNodeApi } from "@/interfaces"
import { formatRecordTree, toastError, getFileNodeList, SystemPrompt, Message } from "@/utils"
import { timeOptions } from "../config"
import { getBorrowApprovalInfoApi, handleBorrowApprovalApi, sendBackApi } from "../interface"
import type { CheckboxValueType } from "element-plus"

const emits = defineEmits(["success"])

const drawerLoading = ref(false)

const state = reactive({
  patientName: "",
  allowEdit: "ENABLE", // 是否允许编辑审批内容 ENABLE | DISABLE"
  processTaskId: "", // 后台需要参数
  approvalInfo: {
    long: false,
    timeLimitUnit: ""
  } as Record<string, any>,
  approvalType: 1, // 1-按文件 2-按页数
  approvalTypeDisabled: false, // 是否禁止编辑审批类型
  documentTree: [] as Record<string, any>[], // 申请借阅的文件列表
  pageTree: [] as Record<string, any>[],
  checkedPermissionList: [] as string[],
  comment: "", // 审批意见
  confirmLoading: false // 提交时的loading
})

/* ================ 基础信息 =============== */

const getBorrowApprovalInfo = id => {
  drawerLoading.value = true
  getBorrowApprovalInfoApi({ paramsId: id })
    .then(res => {
      const __applyInfo = res.data.data
      // 格式化数据
      const checkedPermissionList: string[] = []
      if (__applyInfo.viewPermission === "ENABLE") {
        checkedPermissionList.push("viewPermission")
      }
      if (__applyInfo.printPermission === "ENABLE") {
        checkedPermissionList.push("printPermission")
      }
      if (__applyInfo.exportPermission === "ENABLE") {
        checkedPermissionList.push("exportPermission")
      }
      state.approvalInfo = Object.assign(state.approvalInfo, __applyInfo)
      state.approvalInfo.long = state.approvalInfo.timeLimitUnit === "FOREVER"
      // 如果是永久，展示默认的单位“天”
      if (state.approvalInfo.timeLimitUnit === "FOREVER") state.approvalInfo.timeLimitUnit = "DAY"
      state.checkedPermissionList = checkedPermissionList
      state.documentTree = formatRecordTree(__applyInfo.treeInfo)
      fileList = getFileNodeList(state.documentTree)
      state.pageTree = getPageTree()
      if (state.allowEdit !== "ENABLE") {
        setDocumentTreeDisabled()
        setPageTreeDisabled()
      }
      if (state.approvalInfo.approveMethod) {
        state.approvalType = state.approvalInfo.approveMethod === "DOCUMENT" ? 1 : 2
        state.approvalTypeDisabled = true
      }
      getPrecessNodes()
      drawerLoading.value = false
    })
    .catch(error => {
      drawerLoading.value = false
      toastError(error || "获取借阅信息失败")
    })
}

// 获取每个文件下的页数
const getPageTree = () => {
  const fileNodeList = cloneDeep(fileList)
  const __pageList: any[] = []
  for (let fileNode of fileNodeList) {
    fileNode.children = []
    for (let i = 0; i < fileNode.filePage; i++) {
      fileNode.children.push({
        wsid: `${fileNode.fileWsid}_${i + 1}`, // 需要设置给el-tree的nodeKey
        fileWsid: fileNode.fileWsid, // 文件wsid
        name: `第${i + 1}页`,
        value: `${i + 1}_${fileNode.fileWsid}`,
        page: i + 1, // 当前页数
        type: "PAGE"
      })
    }
    __pageList.push(...fileNode.children)
  }
  pageList = __pageList
  return fileNodeList
}

// 获取借阅时长
const getTimeLimitCh = val => {
  if (val < 1) state.approvalInfo.timeLimitCh = 1
  if (val % 1 !== 0) state.approvalInfo.timeLimitCh = Math.round(val)
}

/* ==================== 文档树 ===================== */

let fileList: Record<string, any>[] = [] // 只有根节点文档的树
const documentTreeRef = ref<InstanceType<typeof CommonTree>>()
const checkedDocumentList = ref<Record<string, any>[]>([]) // 选中的分类列表 mrClassCode[]
const documentCheckAll = ref(false) // 是否全选
const isDocumentIndeterminate = ref(false) // 全选框

// 切换全选
const handleDocumentCheckAll = (__checkAll: CheckboxValueType) => {
  checkedDocumentList.value = __checkAll ? fileList : []
  documentTreeRef.value?.checkAllNodes(__checkAll ? true : false)
  isDocumentIndeterminate.value = false
}

// 单选
const handleCheckDocument = (node, checkedNodes) => {
  checkedDocumentList.value = checkedNodes.filter(__node => __node.type === "FILE")
  documentCheckAll.value = checkedDocumentList.value.length === fileList.length
  isDocumentIndeterminate.value =
    checkedDocumentList.value.length > 0 && checkedDocumentList.value.length < fileList.length
}

// 设置节点value
const setDocumentTreeDisabled = () => {
  if (state.approvalInfo.approveMethod === "PAGE") return
  const getCheckedKeys = tree => {
    for (const treeNode of tree) {
      treeNode.disable = true
      if (treeNode.type === "FILE" && treeNode.statusEnum === "AUDIT_PASS") {
        needCheckedKeys.push(treeNode.wsid)
      }
      if (treeNode.children?.length) {
        getCheckedKeys(treeNode.children)
      }
    }
  }
  const needCheckedKeys: string[] = []
  getCheckedKeys(state.documentTree)
  documentTreeRef.value?.setCheckedKeys(needCheckedKeys)
}

/* ==================== 页码树 ===================== */

let pageList: Record<string, any>[] = [] // 只有根节点页码的树
const pageTreeRef = ref<InstanceType<typeof CommonTree>>()
const checkedPageList = ref<any[]>([])
const pageCheckAll = ref(false) // 是否全选
const isPageIndeterminate = ref(false) // 全选框

// 切换全选
const handlePageCheckAll = (__checkAll: CheckboxValueType) => {
  checkedPageList.value = __checkAll ? pageList : []
  pageTreeRef.value?.checkAllNodes(__checkAll ? true : false)
  isPageIndeterminate.value = false
}

// 单选
const handleCheckPage = (node, checkedNodes) => {
  checkedPageList.value = checkedNodes.filter(__node => __node.type === "PAGE")
  pageCheckAll.value = checkedPageList.value.length === pageList.length
  isPageIndeterminate.value = checkedPageList.value.length > 0 && checkedPageList.value.length < pageList.length
}

// 禁止勾选节点
const setPageTreeDisabled = () => {
  if (state.approvalInfo.approveMethod === "DOCUMENT") return
  const getCheckedKeys = tree => {
    for (const treeNode of tree) {
      treeNode.disable = true
      if (treeNode.type === "FILE" && treeNode.approveFilePage) {
        needCheckedKeys.push(treeNode.wsid)
        for (let pageNode of treeNode.children) {
          treeNode.approveFilePage.includes(pageNode.page) && needCheckedKeys.push(pageNode)
        }
        continue
      }
      if (treeNode.children?.length) {
        getCheckedKeys(treeNode.children)
      }
    }
  }
  const needCheckedKeys: string[] = []
  getCheckedKeys(state.pageTree)
  pageTreeRef.value?.setCheckedKeys(needCheckedKeys)
}

/* ======================== 审批 ======================== */

// 确认审批
const confirmApproval = (pass: boolean) => {
  let __checkedFileList = cloneDeep(state.approvalType === 1 ? checkedDocumentList.value : checkedPageList.value)
  // 如果是第二次及以后的审批节点，需要获取前面的人所有通过的节点
  if (state.approvalType === 1 && !__checkedFileList.length) {
    __checkedFileList = documentTreeRef.value?.getCheckedNodes().filter(node => node.type === "FILE")
  } else if (state.approvalType === 2 && !__checkedFileList.length) {
    __checkedFileList = pageTreeRef.value?.getCheckedNodes().filter(node => node.type === "PAGE")
  }
  if (pass && state.approvalType === 1 && !__checkedFileList.length) {
    Message.error("请勾选需要审核的文件！")
    return
  }
  if (pass && state.approvalType === 2 && !checkedPageList.value.length) {
    Message.error("请勾选需要审核的页数！")
    return
  }
  // 当选择的是按页数审核时，需要将选择的页数传入到文件节点的approveFilePage字段中
  if (state.approvalType === 2) {
    __checkedFileList = getCheckedPageFileList(__checkedFileList)
  }
  state.confirmLoading = true
  // 格式化文件
  __checkedFileList.forEach(file => {
    file.mrDocumentWsid = file.wsid
    file.statusEnumName = pass ? "AUDIT_PASS" : "AUDIT_REFUSE"
  })
  console.log(__checkedFileList)
  // 借阅权限
  const checkedPermission = {
    viewPermission: "DISABLE",
    exportPermission: "DISABLE",
    printPermission: "DISABLE"
  }
  state.checkedPermissionList.forEach(permission => {
    checkedPermission[permission] = "ENABLE"
  })
  // 请求参数
  const requestParams = {
    borrowId: state.approvalInfo.borrowId,
    documentBagWsid: state.approvalInfo.documentBagWsid,
    fileCount: state.approvalInfo.fileCount,
    timeLimitCh: state.approvalInfo.long ? 99999 : state.approvalInfo.timeLimitCh,
    timeLimitUnit: state.approvalInfo.long ? "FOREVER" : state.approvalInfo.timeLimitUnit,
    processTaskId: state.processTaskId,
    statusEnumName: pass ? "AUDIT_PASS" : "AUDIT_REFUSE",
    items: __checkedFileList,
    desc: state.comment,
    approveMethod: state.approvalType === 1 ? "DOCUMENT" : "PAGE", // 借阅文件审核方式-DOCUMENT(0,文书)/PAGE(1,页码)
    ...checkedPermission
  }
  handleBorrowApprovalApi({ ...requestParams })
    .then(() => {
      Message.success("操作成功")
      state.confirmLoading = false
      close()
      emits("success")
    })
    .catch(error => {
      state.confirmLoading = false
      toastError(error)
    })
}

// 根绝已经勾选的页数获取文件
const getCheckedPageFileList = pageList => {
  const formattedPageList: any[] = []
  for (let pageNode of pageList) {
    const existPageNode = formattedPageList.find(node => node.fileWsid === pageNode.fileWsid)
    if (!existPageNode) {
      formattedPageList.push({
        ...pageNode,
        approveFilePage: [pageNode.page]
      })
    } else {
      existPageNode.approveFilePage.push(pageNode.page)
    }
  }
  const checkedFileList: any[] = []
  for (let pageNode of formattedPageList) {
    const checkedFile = fileList.find(file => file.fileWsid === pageNode.fileWsid)
    checkedFileList.push({
      ...checkedFile,
      approveFilePage: pageNode.approveFilePage.join(",")
    })
  }
  return checkedFileList
}

/* =================== 驳回 =================== */

// 驳回
const handleReject = () => {
  if (!state.comment) {
    Message.warning("请输入审批意见")
    return
  }
  SystemPrompt("该操作将整体驳回，并且流程将结束，确定驳回吗？").then(() => {
    confirmApproval(false)
  })
}

/* =================== 退回 =================== */

const processNodeList = ref<{ name: string; defKey: string }[]>([{ name: "1", defKey: "1" }])
const showSendBackButton = computed(() => processNodeList.value.length) // 是否显示退回按钮

// 获取审批流程节点
const getPrecessNodes = async () => {
  processNodeList.value = await getApprovalNodeApi({
    businessKey: state.approvalInfo.borrowWsid,
    taskId: state.processTaskId
  })
}

// 退回
const handleSendBack = node => {
  if (!state.comment) {
    Message.warning("请输入审批意见")
    return
  }
  SystemPrompt("确定退回到该流程节点吗？").then(() => {
    state.confirmLoading = true
    sendBackApi(state.processTaskId, node.defKey, state.comment)
      .then(() => {
        Message.success("回退成功")
        close()
        // commonTableRef.value?.refreshTableData()
        emits("success")
      })
      .catch(error => {
        toastError(error)
      })
      .finally(() => {
        state.confirmLoading = false
      })
  })
}

/* ============= drawer ============== */

const visible = ref(false)

const show = row => {
  state.patientName = row.patientName
  state.allowEdit = row.formApproveStatus
  state.processTaskId = row.processTaskId
  getBorrowApprovalInfo(row.id)
  visible.value = true
}

const close = () => {
  visible.value = false
}

defineExpose({
  show,
  close
})
</script>

<style lang="less" scoped>
.approval-tree {
  position: relative;
  min-height: 200px;
  overflow-y: auto;
  border: 1px solid #e5e5e5;
  border-radius: 8px;

  &__mask {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: #fff;
    opacity: 0.5;
    z-index: 10;
    cursor: not-allowed;
  }

  &--disabled {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    cursor: not-allowed;
    z-index: 10;
    background: #e5e5e5;
    opacity: 0.2;
  }

  .approval-tree-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: calc(100% - 40px);
    padding: 10px 20px;
    background: #f6f7f9;
    border-radius: 4px;
    position: sticky;
    top: 0;
  }

  .approval-tree-content {
    padding: 10px 20px;
    max-height: 45vh;
    overflow-y: auto;
  }
}
.time-limit {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .el-input {
    width: 40%;
  }

  .el-select {
    width: 30% !important;
  }
}
</style>
