<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchForm
        :form-config="mrnoAuthorizationSearchFormConfig"
        :form-state="searchFormState"
        @query-btn-click="handleQuery"
        @reset-btn-click="handleReset"
      />
    </template>

    <template #table>
      <CommonTable
        ref="commonTableRef"
        table-id="mrnoAuthorizationTable"
        :table-columns="mrnoAuthorizationTableColumns"
        :data-callback="dataCallback"
        :request-params="searchParams"
        :request-api="getMrnoAuthorizationListApi"
      >
        <template #operationTime="{ row }">{{ formatDatetime(row.operationTime) }}</template>
        <template #operation="{ row }">
          <TableButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Authorize)"
            @click="showAuthorizedDialog(row)"
          >
            授权
          </TableButton>
          <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.View)" @click="showAuthorizedDetail(row)">
            详情
          </TableButton>
        </template>
      </CommonTable>
    </template>

    <AuthorizedDialog
      ref="dialogRef"
      authorize-type="mrno"
      :bag-wsid="authorizeState.bagWsid"
      :table-ref="commonTableRef"
    />

    <el-drawer v-model="detailState.showDrawer" title="详情" direction="rtl" :size="370" destroy-on-close>
      <CommonTable
        ref="authorizedUsersTableRef"
        style="height: auto"
        :table-columns="mrnoAuthorizationUsersTableColumns"
        :request-api="getBorrowMrnoAuthorizationUsersApi"
        :request-params="detailState.selectedRow"
        :pagination="false"
      >
        <template #header>
          <div class="drawer-item-wrapper">
            <div class="drawer-item-title">
              已授权用户({{ authorizedUsersTableRef?.tableState?.tableData.length || 0 }}):
            </div>
          </div>
        </template>
      </CommonTable>

      <CommonTable
        ref="authorizedDeptsTableRef"
        style="height: auto"
        :table-columns="mrnoAuthorizationDeptsTableColumns"
        :request-api="getBorrowMrnoAuthorizationDeptsApi"
        :request-params="detailState.selectedRow"
        :pagination="false"
      >
        <template #header>
          <div class="drawer-item-wrapper" style="margin-top: 20px">
            <div class="drawer-item-title">
              已授权科室({{ authorizedDeptsTableRef?.tableState?.tableData.length || 0 }}):
            </div>
          </div>
        </template>
      </CommonTable>
    </el-drawer>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import { CommonTable, TableButton, PageContainer } from "@/base-components"
import { TabsRouter, SearchForm } from "@/page-components"
import { secrecyLevelOptions, MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { useUserStore } from "@/stores"
import { formatDatetime } from "@/utils"

import AuthorizedDialog from "../components/AuthorizedDialog.vue"
import { tabsRouterList, menuId } from "../config"
import {
  getBorrowMrnoAuthorizationUsersApi,
  getBorrowMrnoAuthorizationDeptsApi,
  getMrnoAuthorizationListApi
} from "../interface"
import {
  mrnoAuthorizationSearchFormConfig,
  mrnoAuthorizationTableColumns,
  mrnoAuthorizationUsersTableColumns,
  mrnoAuthorizationDeptsTableColumns
} from "./config"

const { hasOperationPermission } = useUserStore()

const searchFormState = reactive({
  patientFilterProp: "",
  patientFilterValue: "",
  outHospitalDeptWsid: "",
  outHospitalDatetime: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

// tableRef
const authorizedUsersTableRef = ref()
const authorizedDeptsTableRef = ref()
const commonTableRef = ref()

const dataCallback = (data: Array<Record<string, any>>) => {
  data.forEach(row => {
    row.outHospitalDatetime = formatDatetime(row.outHospitalDatetime)
    row.secrecyGradeEnumName = getSecrecyLevelText(row.secrecyGradeEnumName)
  })
  return data
}

// 获取保密等级
const getSecrecyLevelText = secrecyEnum => {
  return secrecyLevelOptions.find(option => option.value === secrecyEnum)?.label || ""
}

/*==================== 授权相关 =====================*/
const dialogRef = ref()

const authorizeState = reactive({
  bagWsid: ""
})

/*==================== 详情业务 =====================*/
const detailState = reactive({
  showDrawer: false,
  selectedRow: {},
  authorizedUsersCount: 0, // 已授权用户数量
  authorizedDeptsCount: 0 // 已授权科室数量
})

const showAuthorizedDetail = row => {
  detailState.selectedRow = row
  detailState.showDrawer = true
}

const showAuthorizedDialog = row => {
  dialogRef.value.open()
  authorizeState.bagWsid = row.wsid
}
</script>

<style lang="less" scoped>
#authorized-mrno {
  width: 100%;
  height: 100%;
  :deep(.borrow-authorized-mrno-dialog) {
    color: rgba(0 0 0 85%);
  }

  :deep(.detailDrawer) {
    .drawer-item-wrapper {
      margin-bottom: 22px;
      .drawer-item-title {
        margin-bottom: 16px;
      }
    }
  }
}
</style>
