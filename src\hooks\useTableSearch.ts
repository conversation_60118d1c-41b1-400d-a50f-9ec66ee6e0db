import { reactive } from "vue"
import { cloneDeep } from "lodash-es"

import { useSystemStore } from "@/stores"

const useTableSearch = (
  searchFilterForm: Record<string, any>,
  requiredFilter?: string, // 必须携带的过滤条件
  initFormData?: null | Record<string, any>, // 初始表单值，若需要点击重置后让表单重置为指定初始状态，则需要传此项,
  queryParams?: Record<string, any>, //兼容后端精确查询，单独的query参数
  searchFormState?: Record<string, any> //患者信息、日期等相关查询条件
) => {
  const systemStore = useSystemStore()
  const pageParams = systemStore.pageParams.find(item => item.id === location.pathname)

  // 加载已保存的查询参数
  for (const key in pageParams?.searchFilterForm) {
    searchFilterForm[key] = pageParams?.searchFilterForm[key]
  }

  for (const key in pageParams?.queryParams) {
    if (queryParams) queryParams[key] = pageParams?.queryParams[key]
  }

  for (const key in pageParams?.searchFormState) {
    if (searchFormState) searchFormState[key] = pageParams?.searchFormState[key]
  }

  if (initFormData) {
    for (const key in initFormData) {
      if (initFormData[key]) {
        searchFilterForm[key] = initFormData[key]
      }
    }
  }

  const filters = pageParams?.searchParams?.filters
  const params = pageParams?.searchParams?.queryParams

  const searchParams = reactive({
    filters: filters || generateInitFilters(),
    queryParams: params || cloneDeep(queryParams) || ({} as Record<string, any> | undefined)
  })
  console.log(`output->searchParams`, searchParams)

  // 生成初始过滤参数
  function generateInitFilters() {
    const initFilterConditions: Array<string> = requiredFilter ? [requiredFilter] : []
    const complexFields = [
      "patientFilterProp",
      "patientFilterValue",
      "visitIdSymbol",
      "visitIdCount",
      "inHospitalDaysFrom",
      "inHospitalDaysTo"
    ]
    for (const key in searchFilterForm) {
      const value = searchFilterForm[key]
      // 复合字段跳过
      if (complexFields.includes(key)) {
        continue
      }
      // 兼容我的收藏页面，存在复现框查询表单类型
      if (
        Array.isArray(value) &&
        value.length !== 0 &&
        !key.includes("Datetime") &&
        !key.includes("consultationTime") &&
        !key.includes("catalogTime")
      ) {
        initFilterConditions.push(`${key}=${value}`)
        console.log(key)
      } else if (searchFilterForm[key] && !Array.isArray(value)) {
        initFilterConditions.push(`${key}=${value}`)
      } else if (
        Array.isArray(value) &&
        value.length === 2 &&
        (key.includes("Datetime") ||
          key.includes("consultationTime") ||
          key.includes("assignmentTime") ||
          key.includes("catalogTime")) || 
          key.includes("checkupDate")
      ) {
        // 日期筛选（查询条件默认带日期范围）
        initFilterConditions.push(`${key}>=${value[0]}`, `${key}<=${value[1] + (1000 * 60 * 60 * 24 - 1)}`)
      }
    }
    return initFilterConditions.join(",")
  }

  // 触发搜索
  function handleQuery() {
    console.log("handleQuery")
    const filterConditions: Array<string> = requiredFilter ? [requiredFilter] : []
    const complexFields = [
      "patientFilterProp",
      "patientFilterValue",
      "visitIdSymbol",
      "visitIdCount",
      "inHospitalDaysFrom",
      "inHospitalDaysTo"
    ]

    for (const key in searchFilterForm) {
      const value = searchFilterForm[key]
      // 复合字段跳过
      if (complexFields.includes(key)) {
        continue
      }
      // 日期范围字段 - 增加就诊时间 consultationTime
      else if (
        Array.isArray(value) &&
        value.length === 2 &&
        (key.includes("Datetime") ||
          key.includes("consultationTime") ||
          key.includes("assignmentTime") ||
          key.includes("catalogTime") ||
          key.includes("returnTime")) ||
          key.includes("checkupDate")
      ) {
        filterConditions.push(`${key}>=${value[0]}`, `${key}<=${value[1] + (1000 * 60 * 60 * 24 - 1)}`)
      }
      // 患者标签
      else if (key === "patientLabel") {
        if (!value?.length) continue
        if (typeof value === "string") {
          filterConditions.push(`${key}=${value}`)
        } else {
          value.length && filterConditions.push(`${key}=${value.join("|")}`)
        }
      }
      // 其他基础字段
      else if (value || value === 0) {
        if (Array.isArray(value)) {
          filterConditions.push(`${key}=${value.join("#")}`)
        } else {
          filterConditions.push(`${key}=${value}`)
        }
      }
    }
    // 患者信息复合字段
    const { patientFilterProp, patientFilterValue } = searchFilterForm
    if (patientFilterProp && patientFilterValue) filterConditions.push(`${patientFilterProp}=${patientFilterValue}`)

    // 住院次数复合字段
    const { visitIdSymbol, visitIdCount } = searchFilterForm
    if (visitIdSymbol && visitIdCount) filterConditions.push(`visitId${visitIdSymbol}${visitIdCount}`)

    // 住院天数范围字段
    const { inHospitalDaysFrom, inHospitalDaysTo } = searchFilterForm
    if (inHospitalDaysTo) filterConditions.push(`inHospitalDays<=${inHospitalDaysTo}`)
    if (inHospitalDaysFrom) filterConditions.push(`inHospitalDays>=${inHospitalDaysFrom}`)

    searchParams.filters = filterConditions.join(",")
    // searchParams.queryParams = cloneDeep(queryParams)
    searchParams.queryParams = Object.assign(cloneDeep(queryParams) || {}, { timestamp: new Date().getTime() })

    const systemStore = useSystemStore()
    systemStore.setPageParams({
      id: location.pathname,
      searchParams: searchParams,
      searchFilterForm: searchFilterForm,
      queryParams: queryParams,
      searchFormState: searchFormState
    })
  }

  // 重置搜索
  function handleReset() {
    if (initFormData) {
      for (const key in searchFilterForm) {
        searchFilterForm[key] = initFormData[key] ?? ""
      }

      // 初始化页面查询条件form
      for (const key in searchFormState) {
        searchFormState[key] = initFormData[key] ?? ""
      }

      // 初始化确认Params参数
      for (const key in queryParams) {
        queryParams[key] = initFormData[key] ?? ""
      }
    } else {
      for (const key in searchFilterForm) {
        searchFilterForm[key] = ""
      }
      for (const key in searchFormState) {
        searchFormState[key] = ""
      }
      for (const key in queryParams) {
        queryParams[key] = ""
      }
    }

    searchParams.filters = generateInitFilters()
    // searchParams.queryParams = cloneDeep(queryParams)
    // 每次请求带上时间戳
    searchParams.queryParams = Object.assign(cloneDeep(queryParams) || {}, { timestamp: new Date().getTime() })

    // if (initQueryParams) {
    //   searchParams.queryParams = cloneDeep(initQueryParams)
    // } else {
    //   searchParams.queryParams = cloneDeep(queryParams)
    // }

    const systemStore = useSystemStore()
    systemStore.setPageParams({
      id: location.pathname,
      searchParams: searchParams,
      searchFilterForm: searchFilterForm,
      queryParams: queryParams,
      searchFormState: searchFormState
    })
  }

  return {
    searchParams,
    handleQuery,
    handleReset
  }
}

export default useTableSearch
