import type { BaseOptionItem, TableColumnItem } from "@/types"

export const menuId = "/print/refund-manage"

export const refundColumns: Array<TableColumnItem> = [
  { type: "selection", width: 45, fixed: "left" },
  { prop: "orderCode", label: "订单号", minWidth: 200, must: true },
  { prop: "payDatetime", label: "支付时间", minWidth: 180, sortable: true, must: true },
  { prop: "refundApplyDatetime", label: "申请时间", minWidth: 180, sortable: true },
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 120, must: true },
  { prop: "patientIdCard", label: "身份证号", minWidth: 200 },
  { prop: "patientOutHospitalDatetime", label: "出院日期", minWidth: 180 },
  { prop: "duplicateNum", label: "复印份数", minWidth: 120, sortable: true },
  { prop: "pageCount", label: "总页数", minWidth: 120, sortable: true },
  { prop: "orderAmount", label: "订单金额", minWidth: 120, sortable: true, must: true },
  { prop: "refundApplyReason", label: "退款原因", minWidth: 150 },
  { prop: "refundStatusStr", label: "退款状态", minWidth: 100, must: true },
  { prop: "refundFailReason", label: "退款失败原因", minWidth: 150 },
  { prop: "operation", label: "操作", width: 180, fixed: "right", must: true }
]

export const refundStatusOptions: Array<BaseOptionItem> = [
  { label: "退款中", value: "REFUNDING" },
  { label: "退款成功", value: "REFUND_SUCCESS" }
]
