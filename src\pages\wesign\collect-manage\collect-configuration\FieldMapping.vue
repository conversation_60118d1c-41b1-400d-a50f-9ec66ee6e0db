<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <CommonSelectFormItem
          v-model:model-value="searchFormState.collectSystemWsid"
          label="厂商系统"
          :options="systemVendorOptions"
        />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="commonTableRef"
        :table-columns="fieldMappingTableColumns"
        :request-api="querySystemElementList"
        :request-params="searchParams"
      >
        <template #header>
          <AddButton @click="handleAdd">添加字段映射</AddButton>
          <ImportButton @click="userImportDialogRef?.openUserImportDialog()">导入</ImportButton>
        </template>
        <template #operation="{ row }">
          <TableButton @click="handleEdit(row)">编辑</TableButton>
          <TableButton @click="handleDelete(row)">删除</TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>

  <DialogContainer
    v-model:visible="formDialogVisible"
    :width="450"
    :title="isEdit ? '编辑字段' : '新增字段'"
    :confirm-callback="handleConfirm"
  >
    <el-form ref="fieldMappingFormRef" :model="dialogFormData" :rules="fieldMappingFormRules" :label-width="80">
      <CommonSelectFormItem
        v-model:model-value="dialogFormData.collectSystemWsid"
        label="厂商系统"
        prop="collectSystemWsid"
        :options="systemVendorOptions"
      />

      <CommonInputFormItem v-model="dialogFormData.systemElementCode" label="源端字段" prop="systemElementCode" />

      <el-form-item label="目标字段" prop="documentElementCode">
        <el-select v-model="dialogFormData.documentElementCode" placeholder="请选择目标字段" fit-input-width filterable>
          <el-option
            v-for="item in elementCodeList"
            :key="item.elementCode"
            :label="`${item.elementCode}(${item.elementName})`"
            :value="item.elementCode"
          >
            <div>
              <span>{{ item.elementCode }}</span>
              <span style="color: #aaa">{{ `(${item.elementName})` }}</span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </DialogContainer>

  <BatchImport
    ref="userImportDialogRef"
    :download-template-api="downloadSystemElementTemplate"
    :import-file-api="systemTemplateImport"
  />
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import {
  PageContainer,
  SearchContainer,
  DialogContainer,
  CommonTable,
  AddButton,
  ImportButton,
  TableButton,
  CommonSelectFormItem,
  CommonInputFormItem
} from "@/base-components"
import { TabsRouter, BatchImport } from "@/page-components"
import { useCommonOptions, useRequestData, useTableSearch } from "@/hooks"
import { SystemPrompt } from "@/utils/message-tool"
import { tabsRouterList, fieldMappingTableColumns, fieldMappingFormRules } from "./config"
import {
  getAllSystemVendor,
  systemTemplateImport,
  downloadSystemElementTemplate,
  querySystemElementList,
  getAllDocumentElementCode,
  addSystemElement,
  editSystemElement,
  deleteSystemElement
} from "./interface"
import type { FormInstance } from "element-plus"

// 获取所有系统厂商
const { options: systemVendorOptions } = useCommonOptions({
  getOptionsApi: getAllSystemVendor,
  labelAlias: "systemName",
  valueAlias: "wsid"
})

// 获取所有目标字段
const elementCodeList = useRequestData(getAllDocumentElementCode)

/* ======================== 搜索 ======================== */

const searchFormState = reactive({
  collectSystemWsid: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/* ======================== 子组件引用 ======================== */

const commonTableRef = ref<InstanceType<typeof CommonTable>>()
const userImportDialogRef = ref<InstanceType<typeof BatchImport>>()

/* ======================== 编辑弹窗 ======================== */
const fieldMappingFormRef = ref<FormInstance>()

const isEdit = ref(false)

const formDialogVisible = ref(false)

const dialogFormData = ref({
  collectSystemWsid: "",
  systemElementCode: "",
  documentElementCode: "",
  id: ""
})

// 添加字段映射
const handleAdd = () => {
  isEdit.value = false
  formDialogVisible.value = true
  dialogFormData.value = {
    collectSystemWsid: "",
    documentElementCode: "",
    systemElementCode: "",
    id: ""
  }
}

// 编辑字段映射
const handleEdit = row => {
  formDialogVisible.value = true
  isEdit.value = true
  dialogFormData.value = {
    collectSystemWsid: row.collectSystemWsid,
    documentElementCode: row.documentElementCode,
    systemElementCode: row.systemElementCode,
    id: row.id
  }
}

// 删除字段映射
const handleDelete = row => {
  SystemPrompt(`您确定要删除该字段吗？`).then(() => {
    deleteSystemElement(row).then(() => {
      commonTableRef.value?.refreshTableData()
    })
  })
}

// 确认新增&编辑
const handleConfirm = () => {
  fieldMappingFormRef.value?.validate(valid => {
    if (!valid) return
    if (isEdit.value) {
      editSystemElement(dialogFormData.value).then(() => {
        commonTableRef.value?.refreshTableData()
        formDialogVisible.value = false
      })
    } else {
      addSystemElement(dialogFormData.value).then(() => {
        commonTableRef.value?.refreshTableData()
        formDialogVisible.value = false
      })
    }
  })
}
</script>
