<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          :filter-prop-options="outpatientFilterFormOptions"
          label="患者信息"
        />

        <DepartmentFormItem v-model:model-value="searchFormState.deptCode" label="就诊科室" />

        <DaterangeFormItem v-model:model-value="searchFormState.consultationTime" label="就诊时间" />

        <CommonInputFormItem v-model="searchFormState.doctorName" label="就诊医师" />

        <!-- <el-form-item label="编码员">
          <el-select v-model="searchFormState.catalogerUserWsid" style="width: 100%">
            <el-option v-for="item in coders" :key="item.userWsid" :label="item.catalogerName" :value="item.userWsid" />
          </el-select>
        </el-form-item> -->

        <CommonSelectFormItem v-model="searchFormState.catalogerUserWsid" label="编码员" :options="coders" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="tableRef"
        table-id="outpatientAssignmentTableIdent"
        :table-columns="outpatientAndEmergencyColumns"
        :request-api="getOutpatientCatalogListApi"
        :request-params="requestParams"
        :data-callback="dataCallback"
      >
        <!-- 患者姓名 -->
        <template #patientName="{ row }">
          <div style="display: flex; align-items: center; flex-wrap: wrap">
            <span style="margin: 0 16px">{{ row.patientName }}</span>
            <PatientLabelTag :row="row" />
          </div>
        </template>
        <template #catalogStatus="{ row }">
          {{ catalogStatusOptions.find(item => item.value === row.catalogStatus)?.label || "--" }}
        </template>

        <template #header>
          <div style="margin-bottom: 12px">
            <el-button
              :type="assignmentType === 'AUTO_ASSIGNMENT' ? 'primary' : ''"
              @click="handleAssignment('AUTO_ASSIGNMENT')"
            >
              按数量分配
            </el-button>
            <el-button :type="assignmentType === 'USER_DEPT' ? 'primary' : ''" @click="handleAssignment('USER_DEPT')">
              按科室分配
            </el-button>
            <BatchOperationButton type="primary" :disabled="!selectedRows.length" @click="handleAssignment('MANUAL')">
              手动分配
            </BatchOperationButton>

            <el-button
              :icon="h('i', { class: 'ri-menu-search-line', style: { 'font-size': '18px' } })"
              @click="processDialogRef?.show()"
            >
              编码进度查询
            </el-button>
          </div>
        </template>
      </CommonTable>
    </template>

    <!-- 编码进度弹窗 -->
    <CodingProcessDialog ref="processDialogRef" range-type="OUTPATIENT_SERVICE" />

    <!-- 分配编目任务/重新分配 -->
    <AssignmentDialog
      ref="assignmentDialogRef"
      range-type="OUTPATIENT_SERVICE"
      :selected-rows="selectedRows"
      @success="handleAssignSuccess"
    />
  </PageContainer>
</template>

<script setup lang="ts">
import { h, reactive, ref, computed, onMounted } from "vue"
import {
  SearchContainer,
  CommonTable,
  PatientFilterFormItem,
  DepartmentFormItem,
  DaterangeFormItem,
  BatchOperationButton,
  PageContainer,
  CommonInputFormItem,
  CommonSelectFormItem,
  PatientLabelTag
} from "@/base-components"
import { TabsRouter } from "@/page-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { useUserStore } from "@/stores"
import { formatDatetime, formatDate } from "@/utils"
import { getCatalogConfigInfo, getOutpatientCatalogListApi } from "../interface"
import AssignmentDialog from "./components/AssignmentDialog.vue"
import CodingProcessDialog from "./components/CodingProcessDialog.vue"
import {
  outpatientAndEmergencyColumns,
  tabsRouterList,
  catalogStatusEnum,
  catalogStatusOptions,
  outpatientFilterFormOptions
} from "./config"

const menuId = "/catalog/catalog-assignment"

const { hasOperationPermission } = useUserStore()

const coders = ref<any[]>([]) // 编码员数据
const assignmentType = ref("") // 分配类型

onMounted(async () => {
  const res = (await getCatalogConfigInfo("OUTPATIENT_SERVICE")).data.data
  coders.value = res.cataloger.map(item => ({
    label: item.realName,
    value: item.userWsid
  }))
  assignmentType.value = res.assignmentType
})

/* ======================== 表格查询 ======================== */

const searchFormState = reactive({
  patientFilterProp: "patientName",
  patientFilterValue: "",
  deptCode: "",
  consultationTime: "",
  doctorName: "",
  catalogerUserWsid: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

const requestParams = computed(() => {
  return { ...searchParams, queryType: "catalog_assignment" }
})

/* ======================== 表格相关方法 ======================== */

const tableRef = ref<InstanceType<typeof CommonTable>>()

// 当前选中表格
const selectedRows = computed(() => tableRef.value?.tableState?.selectedRows ?? [])

const dataCallback = (data: Array<Record<string, any>>) => {
  data.forEach(item => {
    item.inHospitalDatetime = formatDatetime(item.inHospitalDatetime)
    item.outHospitalDatetime = formatDatetime(item.outHospitalDatetime)
    item.assignmentTime = formatDatetime(item.assignmentTime)
    item.catalogTime = formatDatetime(item.catalogTime)
    item.consultationTime = formatDate(item.consultationTime)
    item.catalogStatusEnum = catalogStatusEnum[item.catalogStatusEnum]
  })
  return data
}

/* ======================== 进度弹窗 ======================== */

const processDialogRef = ref()

/* ======================== 分配弹窗 ======================== */

const assignmentDialogRef = ref()

const handleAssignment = type => {
  assignmentDialogRef.value.show(type)
}

const handleAssignSuccess = type => {
  assignmentType.value = type
  tableRef.value?.refreshTableData()
}
</script>
