<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <CommonInputFormItem v-model="searchFormState.inpNo" label="患者编号" />

        <DaterangeFormItem v-model:model-value="searchFormState.createdDatetime" label="接收时间" />

        <CommonSelectFormItem
          v-model="searchFormState.mrClassCode"
          label="文档类型"
          :options="documentTypeOptions"
          filterable
        />

        <CommonSelectFormItem v-model="searchFormState.status" label="接收状态" :options="receiveStatusOptions" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="thirdPartyPushTableRef"
        table-id="thirdPartyPushTableIdent"
        :table-columns="thirdPartyPushColumns"
        :request-api="getThirdPartyPushList"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <template #status="{ row }">
          <el-tag :type="row.receiveTag">{{ row.receiveDesc }}</el-tag>
        </template>
        <template #operation="{ row }"></template>
      </CommonTable>
    </template>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref, computed } from "vue"
import { Operation } from "@element-plus/icons-vue"
import {
  PageContainer,
  SearchContainer,
  CommonTable,
  CommonInputFormItem,
  DaterangeFormItem,
  CommonSelectFormItem
} from "@/base-components"
import { TabsRouter } from "@/page-components"
import { useCommonOptions, useTableSearch } from "@/hooks"
import { getAllDocTypes } from "@/interfaces"
import { formatDatetime, Message, toastError } from "@/utils"
import { thirdPartyPushColumns, receiveStatusOptions, tabsRouterList, receiveStatusEnum } from "./config"
import { getThirdPartyPushList } from "./interface"

// 获取所有病案类型选项
const { options: documentTypeOptions } = useCommonOptions({
  getOptionsApi: getAllDocTypes,
  labelAlias: "mrClassName",
  valueAlias: "mrClassCode"
})

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  createdDatetime: "",
  mrClassCode: "",
  status: "",
  inpNo: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/* ======================== 表格相关方法 ======================== */

const thirdPartyPushTableRef = ref<InstanceType<typeof CommonTable>>()

const dataCallback = (data: Array<Record<string, any>>) => {
  const receiveMap = new Map([
    [receiveStatusEnum.ENABLE, { desc: "成功", tag: "success" }],
    [receiveStatusEnum.DISABLE, { desc: "失败", tag: "danger" }]
  ])
  return data.map(item => ({
    ...item,
    createdDatetime: formatDatetime(item.createdDatetime),
    receiveDesc: receiveMap.get(item.status)?.desc ?? "未知",
    receiveTag: receiveMap.get(item.status)?.tag ?? ""
  }))
}
</script>
