<template>
  <Spin :spinning="systemStore.pageLoading" :tip="loadingText">
    <div id="permission-manage">
      <el-container style="height: 100%">
        <el-aside style="width: auto; margin-right: 16px">
          <div class="organization-structure common-box-shadow">
            <el-tabs v-model="state.activeOrganizationStructure">
              <el-tab-pane label="角色" :name="PermissionClassifyEnum.ROLE"></el-tab-pane>
              <el-tab-pane label="科室" :name="PermissionClassifyEnum.DEPT"></el-tab-pane>
              <el-tab-pane label="职称" :name="PermissionClassifyEnum.JOB_TITLE"></el-tab-pane>
            </el-tabs>
            <div v-loading="state.roleLoading" class="organization-structure__content">
              <!-- 按角色 -->
              <div v-if="state.activeOrganizationStructure === PermissionClassifyEnum.ROLE">
                <div
                  v-for="item in roleList"
                  :key="item.wsid"
                  class="choice-classify-item"
                  :class="{ active: state.choiceOrganizationStructureWsid === item.wsid }"
                  @click="handleListClick(item.wsid)"
                >
                  {{ item.roleName }}
                </div>
              </div>

              <!-- 按科室 -->
              <div v-else-if="state.activeOrganizationStructure === PermissionClassifyEnum.DEPT">
                <div
                  v-for="item in globalOptionsStore.departmentOptions"
                  :key="item.value"
                  class="choice-classify-item"
                  :class="{ active: state.choiceOrganizationStructureWsid === item.value }"
                  @click="handleListClick(item.value)"
                >
                  {{ item.label }}
                </div>
              </div>

              <!-- 按职称 -->

              <div v-else-if="state.activeOrganizationStructure === PermissionClassifyEnum.JOB_TITLE">
                <div
                  v-for="item in jobTitlesSource"
                  :key="item.key"
                  :class="{ active: state.choiceOrganizationStructureWsid === item.key }"
                  class="choice-classify-item"
                  @click="handleListClick(item.key)"
                >
                  {{ item.value }}
                </div>
              </div>
            </div>
          </div>
        </el-aside>

        <el-container style="border-radius: 4px; overflow: hidden">
          <el-header style="padding: 0; height: auto">
            <div class="permission-save common-box-shadow">
              <!-- <div class="permission-tips">提示：选中复选框即可授予相应权限，取消选中则回收相应权限。</div> -->
              <el-tabs v-model="state.tabName">
                <el-tab-pane label="功能权限" :name="TabEnum.FUNCTION"></el-tab-pane>
                <el-tab-pane label="数据权限" :name="TabEnum.DATA"></el-tab-pane>
              </el-tabs>

              <div class="save-btn">
                <el-button type="primary" plain @click="toReturn">返回</el-button>

                <el-button
                  type="primary"
                  :disabled="!state.choiceOrganizationStructureWsid"
                  :icon="h('i', { class: 'ri-checkbox-circle-fill' })"
                  @click="savePermissions"
                >
                  保存
                </el-button>
              </div>
            </div>
          </el-header>
          <el-main style="padding: 0">
            <div v-if="state.tabName === TabEnum.FUNCTION" class="permission-wrapper">
              <div class="menu-permission">
                <!-- <CardContainer v-loading="state.menuLoading" title="菜单权限" sign>
                  <el-tree
                    v-if="state.choiceOrganizationStructureWsid"
                    ref="menuTreeRef"
                    :props="treeProps"
                    :data="systemStore.availableMenus"
                    node-key="id"
                    show-checkbox
                    default-expand-all
                    @node-click="handleMenuClick"
                    @check-change="checkChange"
                  />
                  <div v-else style="height: 100%">
                    <EmptyContent desc="请先选择角色" />
                  </div>
                </CardContainer> -->
                <div v-loading="state.menuLoading" class="group-container">
                  <div v-if="state.choiceOrganizationStructureWsid" class="group-list">
                    <div
                      v-for="item in availableMenus"
                      :key="item.id"
                      class="group-item"
                      :class="{ 'group-item--active': item.id === selectedGroup.id }"
                      @click="selectGroup(item)"
                    >
                      <div class="group-item__title">{{ item.name }}</div>
                      <el-icon v-if="item.id === selectedGroup.id"><ArrowRight /></el-icon>
                    </div>
                  </div>

                  <div v-else style="height: 100%">
                    <EmptyContent desc="请先选择角色" />
                  </div>
                </div>
              </div>

              <div class="operation-permission">
                <CardContainer v-loading="state.menuLoading" sign>
                  <template #header>
                    <div class="operation-permission-header">
                      <el-checkbox v-model="expandAll" label="展开/折叠" size="large" @change="expandAllChange" />
                      <el-checkbox v-model="allChecked" label="全选" size="large" @change="checkAllChange" />
                    </div>
                  </template>
                  <el-tree
                    v-if="state.choiceOrganizationStructureWsid && treeVIsible"
                    ref="menuTreeRef"
                    :props="treeProps"
                    :data="selectedGroup.children"
                    node-key="id"
                    show-checkbox
                    :default-expand-all="expandAll"
                    @node-click="handleMenuClick"
                    @check-change="checkChange"
                  />

                  <!-- <el-checkbox-group v-if="state.roleOperations.length" v-model="checkedOperations">
                    <el-checkbox v-for="operation in state.roleOperations" :key="operation.id" :label="operation.id">
                      {{ operation.name }}
                    </el-checkbox>
                  </el-checkbox-group>
                  <div v-else style="height: 100%">
                    <EmptyContent :desc="state.choiceMenuId ? '当前菜单下无关联操作' : '请先选择菜单'" />
                  </div> -->
                </CardContainer>
              </div>
            </div>
            <div v-else class="data-permission-wrapper">
              <div class="data-permission-wrapper__text">相关用户将对以下数据拥有查看、编辑、新增、删除的权限</div>
              <el-radio-group v-model="radio">
                <el-radio label="ALL_DEPT">全院数据</el-radio>
                <el-radio label="CURRENT_DEPT_SELF">本科室数据</el-radio>
                <el-radio label="CURRENT_USER_SELF">仅本人数据</el-radio>
                <el-radio label="DEPT_CUSTOM" class="custom-radio">
                  自定义数据
                  <DepartmentFormItem v-model:model-value="state.outHospitalDeptWsid" label="" :multiple="true" />
                </el-radio>
              </el-radio-group>
            </div>
          </el-main>
        </el-container>
      </el-container>
    </div>
  </Spin>
</template>

<script setup lang="ts">
import { reactive, onMounted, watch, ref, h, nextTick } from "vue"
import { useRouter } from "vue-router"
import { Spin } from "ant-design-vue"
import { cloneDeep } from "lodash-es"
import { CardContainer, EmptyContent, DepartmentFormItem } from "@/base-components"
import { UserTypeEnum } from "@/configs/enums"
import { getJobTitlesApi } from "@/interfaces"
import {
  getRoleListApi,
  getRolePermissions,
  updateRolePermissions,
  getJobTitlePermissionApi,
  updateJobTitlePermissionApi,
  updateDepartmentPermissionApi,
  getDepartmentPermissionApi,
  getDataPermissionApi,
  updateDataPermissionApi
} from "@/pages/wesign/organization-member-manage/interface"
import { useSystemStore, useGlobalOptionsStore } from "@/stores"
import { Message, toastError, flattenTree } from "@/utils"
import type { MenuConfigItem, MenuOperationItem } from "@/types"
import type { ElTree } from "element-plus"

const systemStore = useSystemStore()
const router = useRouter()

let loadingTimer

const loadingText = ref("")

watch(
  () => systemStore.pageLoading,
  val => {
    if (!val) {
      clearInterval(loadingTimer)
      return
    }
    loadingText.value = systemStore.loadingText
    loadingTimer = setInterval(() => {
      loadingText.value = loadingText.value.endsWith("...") ? loadingText.value.slice(0, -3) : loadingText.value + "."
    }, 500)
  }
)

const toReturn = () => {
  router.push("/home")
}

const globalOptionsStore = useGlobalOptionsStore()

enum PermissionClassifyEnum {
  ROLE = "ROLE",
  DEPT = "DEPT",
  JOB_TITLE = "JOB_TITLE"
}

enum TabEnum {
  FUNCTION = "FUNCTION",
  DATA = "DATA"
}

const treeProps = { label: "name", children: "children" }

const state = reactive({
  activeOrganizationStructure: PermissionClassifyEnum.ROLE, // 当前选中的权限分类
  choiceOrganizationStructureWsid: "", // 当前选中的职称/科室/角色
  choiceMenuId: "", // 当前选中菜单

  roleMenus: [] as Array<MenuConfigItem>,
  roleOperations: [] as Array<MenuOperationItem>,

  roleLoading: true,
  menuLoading: false,
  tabName: TabEnum.FUNCTION, //功能权限/数据权限
  outHospitalDeptWsid: [] as Array<string>
})

const menuTreeRef = ref<InstanceType<typeof ElTree>>()

// 操作权限列表选中的项
const checkedOperations = ref<Array<string>>([])

/* ======================== 科室权限 ======================== */

const handleDepartmentChange = (departmentWsid: string) => {
  state.choiceOrganizationStructureWsid = departmentWsid
}

/* ======================== 职称权限 ======================== */

const jobTitlesSource = ref<Array<any>>([])

const handleTitleChange = (titleWsid: string) => {
  state.choiceOrganizationStructureWsid = titleWsid
}

// 获取职称列表
const getJobTitleList = () => {
  getJobTitlesApi()
    .then(res => {
      jobTitlesSource.value = res.data?.data ?? []
    })
    .catch(err => {
      toastError(err, "获取职称列表失败")
      jobTitlesSource.value = []
    })
}

// 监听左侧列表 tab 变化，切换时默认选中列表第一个
watch(
  () => state.activeOrganizationStructure,
  val => {
    if (val === PermissionClassifyEnum.ROLE) {
      state.choiceOrganizationStructureWsid = roleList.value[0].wsid
    } else if (val === PermissionClassifyEnum.DEPT) {
      state.choiceOrganizationStructureWsid = globalOptionsStore.departmentOptions[0].value
    } else if (val === PermissionClassifyEnum.JOB_TITLE) {
      state.choiceOrganizationStructureWsid = jobTitlesSource.value[0].key
    }
    state.choiceMenuId = ""
    state.roleOperations = []
    // 重置菜单权限已勾选的权限
    menuTreeRef.value?.setCheckedKeys([], false)
  }
)

/* ======================== 角色列表相关 ======================== */

const roleList = ref<Array<any>>([])
const availableMenus = ref<Array<any>>([])

// 页面加载时获取角色列表和职称列表
onMounted(() => {
  getRoleList()
  getJobTitleList()
  availableMenus.value = cloneDeep(systemStore.availableMenus)
  availableMenus.value.forEach(group => {
    group.children.forEach(level1 => {
      level1.children.forEach(level2 => {
        level2.children = cloneDeep(level2.operations)
      })
    })
  })
})

// 获取角色列表
const getRoleList = () => {
  getRoleListApi()
    .then(res => {
      const roles = (res.data?.roles ?? []) as Array<Record<string, any>>
      roleList.value = roles.filter(item => ![UserTypeEnum.SUPER_ADMIN, UserTypeEnum.ADMIN].includes(item.wsid))
      if (roleList.value.length) state.choiceOrganizationStructureWsid = roleList.value[0].wsid // 默认选中第一个
    })
    .finally(() => {
      state.roleLoading = false
    })
}

// 切换点击角色
function handleListClick(roleWsid: string) {
  if (roleWsid === state.choiceOrganizationStructureWsid) return
  state.choiceOrganizationStructureWsid = roleWsid
  state.choiceMenuId = ""
  state.roleOperations = []
  // 重置菜单权限已勾选的权限
  menuTreeRef.value?.setCheckedKeys([], false)
}

/* ======================== 菜单树相关操作 ======================== */

// 从权限配置中提取有权限的菜单和权限列表
function extractCheckedMenus() {
  const checkedMenus: Array<string> = []
  // 已勾选节点（菜单、操作权限）
  state.roleMenus.forEach(group => {
    // if (group.id === selectedGroup.value.id) {
    // checkedMenus.push(group.id)
    group?.children.forEach(lever1 => {
      // if (lever1.ext?.type === "single")
      checkedMenus.push(lever1.id)
      lever1?.children.forEach(lever2 => {
        if (!lever2.operations) {
          checkedMenus.push(lever2.id)
        } else {
          lever2.operations.forEach(operation => {
            checkedMenus.push(operation.id)
          })
        }
      })
    })
    // }
  })

  nextTick(() => {
    menuTreeRef.value?.setCheckedKeys(checkedMenus, false)
  })

  initMenuData.value.forEach(group => {
    group.children.forEach(level1 => {
      checkedOperations.value.push(level1.id)
      level1?.children?.forEach(level2 => {
        if (level2.operations) {
          checkedOperations.value = [...new Set(checkedOperations.value.concat(level2.operations.map(item => item.id)))]
        } else {
          checkedOperations.value.push(level2.id)
        }
      })
    })
  })
}

// 从权限树勾选状态提取更新后的权限状态
function parseMenuStatus() {
  const checkedNodes = menuTreeRef.value?.getCheckedNodes(false, true) ?? []
  const checkedMenus = checkedNodes.map(item => item.id)

  console.log(`output->checkedOperations.value`, checkedOperations.value)
  state.roleMenus = cloneDeep(availableMenus.value)
  state.roleMenus.forEach(group => {
    // if (group.id === selectedGroup.value.id) {
    //  模块操作过滤
    group.operations = group?.operations.filter(operation => checkedOperations.value.includes(operation.id))
    // 一级菜单过滤
    group.children = group.children.filter(level1 => checkedOperations.value.includes(level1.id))
    group.children.forEach(level1 => {
      // 一级菜单操作过滤
      level1.operations = level1?.operations?.filter(operation => checkedOperations.value.includes(operation.id))
      // 二级菜单过滤
      level1.children = level1.children.filter(level2 => checkedOperations.value.includes(level2.id))
      // 二级菜单操作过滤
      level1.children.forEach(level2 => {
        level2.operations = level2?.operations.filter(operation => checkedOperations.value.includes(operation.id))
      })
    })
    // }
  })
  console.log(`output->state.roleMenus`, state.roleMenus)
}

// 切换点击菜单
function handleMenuClick(menu: MenuConfigItem) {
  // state.roleOperations = [...new Set(state.roleOperations.concat(menu.operations))]
  state.roleOperations = menu.operations
  state.choiceMenuId = menu.id
  // checkedOperations.value = menu.operations.map(item => item.id)
}

function checkChange(node, checked) {
  // 操作权限是否勾选
  if (checked) {
    // 操作权限列表
    state.roleOperations = cloneDeep(node.operations)
    // 勾选数据 使用接口返回的菜单数据
    // const stateNode = flattenTree(state.roleMenus)?.find(item => item.id === node.id) ?? cloneDeep(node)
    const stateNode = cloneDeep(node)
    // 菜单层级
    if (stateNode?.operations?.length) {
      checkedOperations.value = [
        ...new Set(checkedOperations.value.concat(stateNode.operations?.map(item => item?.id)))
      ]
    } else if (!stateNode?.level) {
      // 操作按钮层级
      checkedOperations.value = [...new Set(checkedOperations.value.concat([stateNode.id]))]
    } else {
      // level1的菜单
      checkedOperations.value = checkedOperations.value = [...new Set(checkedOperations.value.concat([stateNode.id]))]
    }
    // 一级菜单操作权限
  } else {
    if (node?.operation?.length) {
      node.operations.forEach(item => {
        checkedOperations.value = checkedOperations.value.filter(id => id !== item.id)
      })
    } else if (!node.level) {
      checkedOperations.value = checkedOperations.value.filter(id => id !== node.id)
    } else {
      checkedOperations.value = checkedOperations.value.filter(id => id !== node.id)
    }
  }
}

// 角色/职称/科室切换后重新获取权限树
watch([() => state.choiceOrganizationStructureWsid, () => state.tabName], reloadData, { immediate: true, deep: true })

const initMenuData = ref<Record<string, any>>({})
function reloadData() {
  if (state.choiceOrganizationStructureWsid) {
    state.menuLoading = true
    // 加载功能权限
    if (state.tabName === TabEnum.FUNCTION) {
      const searchPermissionApi =
        state.activeOrganizationStructure === PermissionClassifyEnum.ROLE
          ? getRolePermissions
          : state.activeOrganizationStructure === PermissionClassifyEnum.DEPT
          ? getDepartmentPermissionApi
          : getJobTitlePermissionApi

      searchPermissionApi(state.choiceOrganizationStructureWsid)
        .then(res => {
          const jsonData = res.data?.data ?? "[]"
          state.roleMenus = JSON.parse(jsonData)
          initMenuData.value = cloneDeep(JSON.parse(jsonData))
          extractCheckedMenus()
          state.menuLoading = false
        })
        .catch(err => {
          toastError(err, "获取菜单和按钮权限失败")
          state.menuLoading = false
          state.roleMenus = []
          extractCheckedMenus()
        })
    } else {
      // 加载数据权限
      getDataPermissionApi({
        objectType: state.activeOrganizationStructure,
        objectId: state.choiceOrganizationStructureWsid
      })
        .then(res => {
          radio.value = res.data.data?.permissionsType ?? ""
          state.outHospitalDeptWsid = res.data.data?.customScope?.[0] ? res.data.data?.customScope : []
          state.menuLoading = false
        })
        .catch(err => {
          toastError(err, "获取数据钮权限失败")
          state.menuLoading = false
        })
    }
  }
}

/* ======================== 操作权限列表相关 ======================== */

// 保存权限配置
function savePermissions() {
  // 保存功能权限
  if (state.tabName === TabEnum.FUNCTION) {
    parseMenuStatus()
    // return
    const updatedApi =
      state.activeOrganizationStructure === PermissionClassifyEnum.ROLE
        ? updateRolePermissions
        : state.activeOrganizationStructure === PermissionClassifyEnum.DEPT
        ? updateDepartmentPermissionApi
        : updateJobTitlePermissionApi
    updatedApi(state.choiceOrganizationStructureWsid, JSON.stringify(state.roleMenus))
      .then(() => {
        Message.success("更新权限成功")
        reloadData()
      })
      .catch(err => {
        toastError(err, "更新权限失败")
      })
  } else {
    updateDataPermissionApi({
      objectType: state.activeOrganizationStructure,
      objectId: state.choiceOrganizationStructureWsid,
      permissionsType: radio.value,
      customScope: radio.value === "DEPT_CUSTOM" ? state.outHospitalDeptWsid : []
    })
      .then(() => {
        Message.success("更新权限成功")
        reloadData()
      })
      .catch(err => {
        toastError(err, "更新权限失败")
      })
  }
}

/* ======================== 选择模块(group)相关操作 ======================== */
const selectedGroup = ref<Record<string, any>>({})
const allChecked = ref(false)
const expandAll = ref(true)
const treeVIsible = ref(true)

// 展开/折叠
const expandAllChange = val => {
  expandAll.value = val
  treeVIsible.value = false
  nextTick(() => {
    treeVIsible.value = true
    extractCheckedMenus()
  })
}

// 当前选中模块
const selectGroup = group => {
  selectedGroup.value = group
  nextTick(() => {
    extractCheckedMenus()
  })
}

// 全选操作
const checkAllChange = () => {
  if (allChecked.value) {
    menuTreeRef.value?.setCheckedKeys(
      selectedGroup.value.children.map(item => item.id),
      false
    )
  } else {
    menuTreeRef.value?.setCheckedKeys([], false)
  }
}

const radio = ref("")
</script>

<style lang="less" scoped>
#permission-manage {
  height: calc(100vh - 30px);
  padding: 15px;
  overflow: hidden;
  .permission-save {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // min-width: 1200px;
    // height: 56px;
    padding: 0 16px;
    // margin-bottom: 16px;
    border-bottom: 1px solid #eeeff3;
    border-radius: 0;
    .permission-tips {
      font-size: 14px;
      color: #333;
    }
    :deep(.el-tabs__header) {
      margin-bottom: 0 !important;
    }
  }
  .permission-wrapper {
    display: flex;
    justify-content: space-between;
    // height: calc(100% - 72px);
    height: 100%;
    overflow: hidden;
    .menu-permission {
      // width: 49%;
      min-width: 158px;
      // margin-left: 16px;
      .custom-tree-node {
        display: inline-flex;
        align-items: center;
        height: 20px;
        line-height: 20px;
        .file-icon {
          margin-right: 6px;
          font-size: 16px;
          color: rgb(171 172 176);
        }
      }
    }
    .operation-permission {
      // width: 49%;
      // min-width: 451px;
      flex: 1;
      // margin-left: 16px;
      .custom-tree-node {
        display: inline-flex;
        align-items: center;
        height: 20px;
        line-height: 20px;
        .file-icon {
          margin-right: 6px;
          font-size: 16px;
          color: rgb(171 172 176);
        }
      }

      :deep(.el-checkbox-group) {
        display: flex;
        flex-direction: column;
        row-gap: 8px;
      }

      &-header {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        width: 100%;
        margin-right: 8px;

        :deep(.el-checkbox) {
          display: flex;
          flex-direction: row-reverse;

          .el-checkbox__input {
            margin-left: 8px;
          }
        }
      }
    }
  }

  .data-permission-wrapper {
    padding: 24px;
    height: calc(100% - 48px);
    background: #fff;

    &__text {
      font-weight: 500;
      font-size: 14px;
      margin-bottom: 22px;
    }

    .el-radio-group {
      display: flex;
      .el-radio {
        width: 100%;
      }
      .custom-radio {
        :deep(.el-radio__label) {
          display: flex;
          align-items: center;
          width: 100%;
        }

        :deep(.el-form-item) {
          margin-bottom: 0;
          margin-left: 8px;
          width: 100%;
        }

        :deep(.el-select) {
          width: 100%;
        }
      }
    }
  }

  .organization-structure {
    min-width: 222px;
    box-sizing: border-box;
    height: 100%;
    :deep(.el-tabs__nav-wrap) {
      &::after {
        height: 1px;
      }
      padding: 0 16px;
    }
    :deep(.el-tabs__header) {
      margin-bottom: 1px;
    }
    &__content {
      height: calc(100% - 72px);
      // height: 100%;
      overflow-y: auto;
      padding: 16px;
    }
    .choice-classify-item {
      height: 40px;
      line-height: 40px;
      padding: 0 16px;
      box-sizing: border-box;
      cursor: pointer;
      &:hover {
        background-color: rgb(245 247 250);
      }
      &.active {
        color: var(--el-color-primary);
        background-color: rgb(50 128 252 / 9.8%);
      }
    }
  }
}

.custom-card {
  border-radius: 0 !important;
}

.group-container {
  height: calc(100% - 48px);
  background-color: #fff;
  padding: 26px 24px;

  .group-list {
    display: flex;
    flex-direction: column;

    .group-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: 500;
      font-size: 14px;
      padding: 10px 0;
      cursor: pointer;

      &:hover {
        color: #3860f4;
      }
      &--active {
        color: #3860f4;
      }
    }
  }
}

:deep(.el-tree) {
  .el-tree-node__content {
    position: relative;
  }

  .el-checkbox {
    position: absolute;
    right: 0;
  }
}
</style>
