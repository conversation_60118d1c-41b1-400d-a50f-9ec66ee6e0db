<!-- 捷宇高拍仪 -->

<template>
  <div style="height: 100%">
    <img v-if="cameraImage" :src="cameraImage" alt="Camera Feed" width="100%" />
    <EmptyContent v-else desc="暂无数据" style="height: 100%" />

    <!-- 高拍仪连接提示弹窗 -->
    <el-dialog
      v-model="wsHintVisible"
      title="高拍仪"
      align-center
      :close-on-click-modal="false"
      :show-close="false"
      :close-on-press-escape="false"
    >
      <el-result v-if="isConnectLoading" title="连接中" sub-title="高拍仪正在连接中，请稍候...">
        <template #icon>
          <el-icon class="is-loading" size="36" color="#409eff"><Loading /></el-icon>
        </template>
      </el-result>
      <el-result
        v-else
        :icon="isExpired || isError ? 'error' : 'success'"
        :title="isExpired ? '响应超时' : isError ? '连接失败' : '连接成功'"
        :sub-title="hintContext"
      >
        <template #extra>
          <el-button v-if="isError || isExpired" type="primary" @click="connectWebSocket">重新连接</el-button>
          <el-button v-else type="primary" @click="wsHintVisible = false">关闭弹窗</el-button>
        </template>
      </el-result>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from "vue"
import { debounce } from "lodash"
import { Loading } from "@element-plus/icons-vue"
import { EmptyContent } from "@/base-components"

const emits = defineEmits(["handleMessage", "updateBarCode"])

const cameraImage = ref("")
let webSocket

onMounted(() => {
  connectWebSocket()
})

function connectWebSocket() {
  if (!webSocket) {
    isConnectLoading.value = true
    wsHintVisible.value = true
    webSocket = new WebSocket("ws://localhost:1818")
  }
  webSocket.onopen = function (event) {
    console.log("open:", event)
    webSocket.send("bStartPlay")
  }
  webSocket.onerror = function (event) {
    console.log("error:", event)
    webSocket.close()

    // 异常后关闭ws并清空
  }
  webSocket.onclose = function (event) {
    console.log("onclose:", event)
    // 关闭后清空ws
    clearWebsocket()
  }

  webSocket.onmessage = function (event) {
    // console.log("message", event.data)
    handleWebSocketMessage(event)
  }
}

// 清除ws
function clearWebsocket(expired = false) {
  if (expired) isExpired.value = true
  else {
    isExpired.value = false
    isError.value = true
  }
  isConnectLoading.value = false
  // 关闭后清空ws
  webSocket = undefined
}

// ws信息处理
function handleWebSocketMessage(event) {
  // 开启主摄像头
  if (event.data.indexOf("BeginbStartPlay") >= 0) {
    const isSuccessConnect = event.data.indexOf("true") >= 0 ? true : false
    if (isSuccessConnect) {
      isError.value = false
      isConnectLoading.value = false
      isConnecting()
    } else {
      clearWebsocket()
    }

    if (!isError.value)
      setTimeout(() => {
        wsHintVisible.value = false
      }, 3000)
    return
  }

  isConnecting()
  // 处理Websocket接收到的请求响应，
  if (event.data.indexOf("BarCodeTransferBegin") >= 0) {
    alert(event.data.replace("BarCodeTransferBegin", "").replace("BarCodeTransferEnd", ""))
  }
  // 获取base64
  else if (event.data.indexOf("BeginsGetBase64") >= 0) {
    let imageBase64 = event.data.replace("BeginsGetBase64", "").replace("EndsGetBase64", "")
    if (isReShoot.value) {
      imageBase64 = "recover," + imageBase64
      isReShoot.value = false
    }
    emits("handleMessage", imageBase64)
  } else if (event.data.indexOf("BeginReadCard") >= 0) {
    alert(event.data.replace("BeginReadCard", "").replace("EndReadCard", ""))
  } else if (event.data.indexOf("BeginGetName") >= 0) {
    alert(event.data.replace("BeginGetName", "").replace("EndGetName", ""))
  } else if (event.data.indexOf("BeginGetSex") >= 0) {
    alert(event.data.replace("BeginGetSex", "").replace("EndGetSex", ""))
  } else if (event.data.indexOf("BeginGetFolk") >= 0) {
    alert(event.data.replace("BeginGetFolk", "").replace("EndGetFolk", ""))
  } else if (event.data.indexOf("BeginGetCode") >= 0) {
    alert(event.data.replace("BeginGetCode", "").replace("EndGetCode", ""))
  } else if (event.data.indexOf("BeginGetBirthYear") >= 0) {
    alert(event.data.replace("BeginGetBirthYear", "").replace("EndGetBirthYear", ""))
  } else if (event.data.indexOf("BeginGetBirthMonth") >= 0) {
    alert(event.data.replace("BeginGetBirthMonth", "").replace("EndGetBirthMonth", ""))
  } else if (event.data.indexOf("BeginGetBirthDay") >= 0) {
    alert(event.data.replace("BeginGetBirthDay", "").replace("EndGetBirthDay", ""))
  } else if (event.data.indexOf("BeginGetAddress") >= 0) {
    alert(event.data.replace("BeginGetAddress", "").replace("EndGetAddress", ""))
  } else if (event.data.indexOf("BeginGetAgency") >= 0) {
    alert(event.data.replace("BeginGetAgency", "").replace("EndGetAgency", ""))
  } else if (event.data.indexOf("BeginGetValidtotal") >= 0) {
    alert(event.data.replace("BeginGetValidtotal", "").replace("EndGetValidtotal", ""))
  } else if (event.data.indexOf("BeginGetValidBegin") >= 0) {
    alert(event.data.replace("BeginGetValidBegin", "").replace("EndGetValidBegin", ""))
  } else if (event.data.indexOf("BeginGetValidEnd") >= 0) {
    alert(event.data.replace("BeginGetValidEnd", "").replace("EndGetValidEnd", ""))
  } else if (event.data.indexOf("BeginReadNewAppMsg") >= 0) {
    alert(event.data.replace("BeginReadNewAppMsg", "").replace("EndReadNewAppMsg", ""))
  } else if (event.data.indexOf("BeginGetNewAddress") >= 0) {
    alert(event.data.replace("BeginGetNewAddress", "").replace("EndGetNewAddress", ""))
  } else if (event.data.indexOf("BeginGetPhotobuf") >= 0) {
    alert(event.data.replace("BeginGetPhotobuf", "").replace("EndGetPhotobuf", ""))
  } else if (event.data.indexOf("BeginGetJPGPhotobuf") >= 0) {
    alert(event.data.replace("BeginGetJPGPhotobuf", "").replace("EndGetJPGPhotobuf", ""))
  } else if (event.data.indexOf("BeginGetSAMID") >= 0) {
    alert(event.data.replace("BeginGetSAMID", "").replace("EndGetSAMID", ""))
  } else if (event.data.indexOf("BeginGetBirthDate") >= 0) {
    alert(event.data.replace("BeginGetBirthDate", "").replace("EndGetBirthDate", ""))
  } else if (event.data.indexOf("BeginGetFPData") >= 0) {
    alert(event.data.replace("BeginGetFPData", "").replace("EndGetFPData", ""))
  } else if (event.data.indexOf("BeginbLivingBodyDetect") >= 0) {
    alert(event.data.replace("BeginbLivingBodyDetect", "").replace("EndbLivingBodyDetect", ""))
  } else if (event.data.indexOf("BeginlImageMatch") >= 0) {
    alert(event.data.replace("BeginlImageMatch", "").replace("EndlImageMatch", ""))
  }
  // 条形码
  else if (event.data.indexOf("BeginsGetBarCodeOnline") >= 0) {
    const barCode = event.data.replace("BeginsGetBarCodeOnline", "").replace("EndsGetBarCodeOnline", "")
    emits("updateBarCode", barCode)
  }

  // 其他操作
  else if (event.data.indexOf("Begin") === -1) {
    cameraImage.value = "data:image/jpeg;base64," + event.data
  }
}

// 判断高拍仪设备是否断开（如果3s内没有数据传输，则视为断开连接）
const isConnecting = debounce(() => disconnect(), 3000)
function disconnect() {
  wsHintVisible.value = true
  clearWebsocket(true)
}

// 旋转
function rotateCamera(value) {
  console.log("Camera started" + value)
  webSocket.send(`bStartPlayRotate(${value})`)
}

// 拍摄图片
function shootImage() {
  console.log("shootImage")
  // 开启自动寻边：0—支持鼠标框选模式（默认模式） 1—定义固定大小拍照模式 2—定义固定大小身份证拍照模式 3—自动寻边 4—自动寻边身份证拍照模式
  webSocket.send("bSetMode(3)")
  webSocket.send("sGetBase64")
  webSocket.send(`sGetBarCodeOnline()`)
}

// 重拍
const isReShoot = ref(false)
function reShootImage() {
  isReShoot.value = true
  webSocket.send("bSetMode(3)")
  webSocket.send("sGetBase64")
  webSocket.send(`sGetBarCodeOnline()`)
}

/* ============================= websocket连接失败弹窗 ============================= */
const isConnectLoading = ref(false)
const wsHintVisible = ref(false)
const isError = ref(false)
const isExpired = ref(false)

const hintContext = computed(() => {
  return isExpired.value
    ? "高拍仪响应超时，请检查高拍仪并重新连接"
    : isError.value
    ? "高拍仪连接失败，请接入高拍仪设备，并启动高拍仪插件后再点击重新连接"
    : "高拍仪连接成功，弹窗将会在3秒后自动关闭或手动点击关闭弹窗"
})

defineExpose({ rotateCamera, shootImage, reShootImage })
</script>

<style lang="less" scoped>
/* 样式可以根据需要进行调整 */
img {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 5px;
  box-sizing: border-box;
}
</style>
