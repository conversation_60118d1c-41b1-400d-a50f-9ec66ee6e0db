import type { SearchFormConfigItem, TableColumnItem } from "@/types"

// 消息状态枚举
export enum NoticeStatusEnum {
  NOT_SEND = "NOT_SEND", //未发送
  HAS_BEEN_SEND = "HAS_BEEN_SEND", //已发送
  DELETE = "DELETE" //已删除
}

export enum SendTypeEnum {
  Immediate = "immediate", // 立即发送
  Assigned = "assigned" // 指定时间
}

// 系统公告管理搜索表单配置
export const systemNoticeSearchFormConfig: Array<SearchFormConfigItem> = [
  { type: "input", label: "公告标题", prop: "title" },
  {
    type: "select",
    label: "公告状态",
    prop: "status",
    options: [
      { label: "待发送", value: "0" },
      { label: "已发送", value: "1" },
      { label: "已删除", value: "9" }
    ]
  }
]

// 系统公告管理表格项
export const systemNoticeTableColumns: Array<TableColumnItem> = [
  { prop: "title", label: "标题", minWidth: 100 },
  { prop: "contentText", label: "内容", minWidth: 180 },
  { prop: "createDateTime", label: "创建时间", minWidth: 160 },
  { prop: "updateDateTime", label: "编辑时间", minWidth: 160 },
  { prop: "sendDateTime", label: "发送时间", minWidth: 160 },
  { prop: "statusEnum", label: "公告状态", minWidth: 100 },
  { prop: "operation", label: "操作", width: 150, fixed: "right" }
]

// 发送时间校验规则
const sendTimeValidator = (_rule: any, value: Date, callback: (err?: Error) => void) => {
  if (!value) return callback()
  const nowTime = new Date()
  if (value.getTime() <= nowTime.getTime()) return callback(new Error("发送指定时间必须晚于当前时间"))
  else return callback()
}

// 添加&编辑公告表单校验规则
export const systemNoticeFormRules = {
  title: [{ required: true, message: "请输入消息标题！", trigger: "blur" }],
  contentHtml: [{ required: true, message: "请输入消息的内容！", trigger: "blur" }],
  sendType: [{ required: true, message: "请选择发送时间！", trigger: "blur" }],
  sendDateTime: [
    { required: true, message: "请选择具体的发送时间！", trigger: "blur" },
    { validator: sendTimeValidator, trigger: "change" }
  ]
}
