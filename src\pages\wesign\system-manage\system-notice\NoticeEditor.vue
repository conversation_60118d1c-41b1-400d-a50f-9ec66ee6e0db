<template>
  <div class="quill-editor-root">
    <QuillEditor ref="quillEditorRef" theme="snow" toolbar="#toolbar" @blur="blur">
      <!-- 样式修改栏 -->
      <template #toolbar>
        <div id="toolbar">
          <!-- 撤销/取消撤销 -->
          <span class="ql-formats">
            <button id="ql-undo" class="ql-undo ri-arrow-go-back-line" @click="undoAction"></button>
            <button id="ql-redo" class="ql-redo ri-arrow-go-forward-line" @click="redoAction"></button>
          </span>

          <!-- 字号大小 -->
          <span class="ql-formats">
            <select class="ql-size">
              <option value="12px">12px</option>
              <option value="18px">18px</option>
              <option value="20px">20px</option>
              <option value="24px">24px</option>
              <option value="30px">30px</option>
              <option value="36px">36px</option>
              <option value="40px">40px</option>
              <option value="48px">48px</option>
            </select>
          </span>

          <!-- 加粗/下划线 -->
          <span class="ql-formats">
            <button class="ql-bold"></button>
            <button class="ql-italic"></button>
          </span>

          <!-- 文本对齐方式 -->
          <span class="ql-formats">
            <button class="ql-align" value="" @click="handleLeft"></button>
            <button class="ql-align" value="center" @click="handleOther"></button>
            <button class="ql-align" value="right" @click="handleOther"></button>
            <button class="ql-align" value="justify" @click="handleOther"></button>
          </span>

          <!-- 列表 -->
          <span class="ql-formats">
            <button class="ql-list" value="ordered"></button>
            <button class="ql-list" value="bullet"></button>
          </span>

          <!-- 缩进 -->
          <span class="ql-formats">
            <button class="ql-indent" value="-1"></button>
            <button class="ql-indent" value="+1"></button>
          </span>
        </div>
      </template>
    </QuillEditor>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from "vue"
import { QuillEditor, Quill } from "@vueup/vue-quill"

const quillEditorRef = ref()
/* ======================== 富文本和父组件的交互（参数接受/发送） ======================== */
// 传入
const props = defineProps({
  content: { type: String, default: "" },
  required: { type: Boolean, default: false }
})
// 传出
const emits = defineEmits(["getHtml"])

function blur() {
  const result = getHtml()
  emits("getHtml", result.html, result.validate)
}

// 获取html代码
function getHtml() {
  const barDom = document.querySelector(".ql-toolbar") as HTMLElement
  const contentDom = document.querySelector(".ql-container") as HTMLElement
  if (quillEditorRef.value.getText().trim() || !props.required) {
    barDom.style.borderColor = "#d1d5db"
    contentDom.style.borderColor = "#d1d5db"
    return { html: quillEditorRef.value.getHTML(), validate: true }
  }
  // 数据为空，校验失败
  barDom.style.borderColor = "red"
  contentDom.style.borderColor = "red"
  return { html: "", validate: false }
}

function returnHTML() {
  return getHtml().html
}
defineExpose({
  returnHTML
})

/* ======================== 富文本内部操作 ======================== */
// 注册font-size可用值的白名单列表，强制使用style去渲染，而不用class去渲染
const sizeStyle = Quill.import("attributors/style/size")
sizeStyle.whitelist = ["12px", "18px", "20px", "24px", "30px", "36px", "40px", "48px"]
Quill.register(sizeStyle, true)
let quill

onMounted(() => {
  if (quillEditorRef.value) {
    quill = quillEditorRef.value.getQuill()
    quillEditorRef.value.setHTML(props.content)
    quill.history.clear()
  }
})

/*===================文本对齐方式：手动更换高亮样式，避免闪烁=====================*/
// 点击文本居左
function handleLeft(e) {
  let target = e.target
  if (target.nodeName !== "BUTTON")
    target = target.nodeName === "line" ? target.parentElement?.parentElement : target.parentElement
  const alignDoms = new Array(...document.querySelectorAll(".ql-align"))
  const otherDom = alignDoms.filter(item => item.getAttribute("value")) as Array<HTMLElement>

  // 需要将其他文本对齐方式的高亮去除
  otherDom.forEach(item => {
    if (item.getAttribute("class")?.indexOf("ql-active") !== -1) {
      item.click()
    }
  })

  // 将文本居中高亮
  setTimeout(() => {
    target.classList.remove("active-background")
    target.classList.add("ql-active")
  })
}

// 点击除了文本居左以外的文本对齐方式
function handleOther(e) {
  const alignDoms = new Array(...document.querySelectorAll(".ql-align"))
  const leftDom = alignDoms.filter(item => !item.getAttribute("value"))[0]

  // 手动去除文本居左的样式
  setTimeout(() => {
    leftDom.classList.remove("ql-active")
    leftDom.classList.add("active-background")
  })
}

// 监听外部传入的文本
watch(
  () => props.content,
  async (newContent, oldContent) => {
    if (quillEditorRef.value) quillEditorRef.value.setHTML(newContent)
    if (quill) quill.history.clear()
  },
  { immediate: true }
)

// 取消撤销
function redoAction() {
  if (!quill) quill = quillEditorRef.value.getQuill()
  quill.history.redo()
}
// 撤销
function undoAction() {
  if (!quill) quill = quillEditorRef.value.getQuill()
  quill.history.undo()
}
</script>

<style lang="less" scoped>
.quill-editor-root {
  width: 100%;
  height: 170px;
}

.toolbar {
  border-color: #f56c6c;
}

:deep(.ql-editor) {
  padding: 10px;
}

:deep(.ql-toolbar) {
  height: 36px;
  border-radius: 4px 4px 0px 0px;
}

:deep(.ql-container) {
  height: 134px;
  border-radius: 0px 0px 4px 4px;
}

:deep(.ql-toolbar.ql-snow) {
  padding: 0px;

  .ql-formats {
    margin-right: 0px;
  }

  .ql-formats button {
    width: 24px;
  }
}

.active-background {
  background-color: transparent !important;
}
</style>
