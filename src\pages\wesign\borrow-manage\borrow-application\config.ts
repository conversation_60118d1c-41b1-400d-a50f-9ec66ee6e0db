import { getBorrowApplyInfoApi } from "@/interfaces"
import { SystemPrompt } from "@/utils"
import type { TableColumnItem, SearchFormConfigItem } from "@/types"

export const menuId = "/borrow/borrow-application"

export const borrowTab = [
  { label: "条件检索", path: "/borrow/borrow-application/list" },
  { label: "全文检索", path: "/borrow/borrow-application/full-text-search" }
]

// 借阅时长选项列表
export const borrowTimeOptions = [
  // { label: "2小时", value: "2小时" },
  // { label: "1天", value: "1天" },
  // { label: "2天", value: "2天" },
  // { label: "7天", value: "7天" },
  // { label: "永久", value: "永久" }
  { label: "小时", value: "HOUR" },
  { label: "天", value: "DAY" },
  { label: "月", value: "MONTH" },
  { label: "年", value: "YEAR" }
]

export const borrowApplicationFormConfig: Array<SearchFormConfigItem> = [
  { type: "patient", label: "患者信息" },
  { type: "department", label: "出院科室", prop: "outHospitalDeptWsid" },
  { type: "daterange", label: "出院日期", prop: "outHospitalDatetime" }
]

export const borrowApplicationColumns: Array<TableColumnItem> = [
  { type: "selection", width: 50, fixed: "left" },
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "mrNo", label: "病案号", minWidth: 150, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 100, must: true },
  { prop: "visitId", label: "住院次数", minWidth: 120, sortable: true },
  { prop: "inHospitalDatetime", label: "入院日期", minWidth: 180, sortable: true },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 100 },
  { prop: "outHospitalDatetime", label: "出院日期", minWidth: 180, sortable: true },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 100 },
  { prop: "borrowStatus", label: "借阅状态", minWidth: 100, must: true },
  // { prop: "applyTypeName", label: "借阅类型", minWidth: 100, must: true },
  { prop: "borrowingCount", label: "借阅次数", minWidth: 120, sortable: true, must: true },
  { prop: "operation", label: "操作", width: 160, fixed: "right", must: true }
]

// 借阅状态选项
export const borrowApplicationOptions = {
  BORROWED: { label: "已借阅", tagType: "success" },
  NO_BORROW: { label: "未借阅", tagType: "primary" },
  APPROVING: { label: "审核中", tagType: "info" },
  CAN_NOT_BORROW: { label: "无法借阅", tagType: "info" }
}

export type BorrowType = "single" | "multi"

export interface BorrowState {
  dialogVisible: boolean
  borrowTree: {
    name: string
    documents: any
    type: string
    filePage: any
  }[] // 树节点
  packetList: string[] // 文件袋信息列表
  borrowType: BorrowType
}

/**
 * 获取借阅信息
 * @param recordWsidList 所选借阅文档的wsid列表
 * @param borrowType 借阅类型 single - 单条； multi - 多条
 */
export const getBorrowInfo = (recordWsidList: string[], borrowType: BorrowType, setBorrowInfo) => {
  getBorrowApplyInfoApi(recordWsidList).then(res => {
    if (res.data.code !== "100100000") return
    const borrowInfo = res.data.data
    const borrowTree = borrowInfo.treeInfo // 文档树 []
    const packetList = borrowInfo.bagsInfo // 文件袋信息 []
    const alreadyBorrowed: boolean = borrowInfo.hasBorrowFile || false // 是否含有已借阅的病案袋 boolean

    // 无法借阅
    if (packetList.length === 0) {
      if (borrowType === "single") {
        SystemPrompt("该病案已申请借阅，或者还在借阅有效期内")
      } else {
        SystemPrompt("当前借阅所有病案已申请借阅或者还在借阅有效期内")
      }
      // 可以借阅 但 已经借阅了一部分
    } else if (alreadyBorrowed) {
      SystemPrompt(
        "您当前借阅包含已申请借阅或者在借阅有效期内的病案，本次借阅将自动取消已申请借阅或者在借阅有效期内的病案"
      ).then(() => setBorrowInfo(borrowTree, packetList))
      // 可以全部借阅
    } else {
      setBorrowInfo(borrowTree, packetList)
    }
  })
}
