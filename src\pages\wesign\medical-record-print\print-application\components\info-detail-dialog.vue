<!-- 查看详情弹窗 -->
<template>
  <DialogContainer v-model:visible="visible" title="打印详情" :width="1000" :no-footer="true">
    <!-- 打印详情预览 -->
    <div class="print-detail-root">
      <div class="preview-info">
        <div class="title">
          <el-radio-group v-model="previewType" class="print-preview-tab">
            <el-radio-button label="print">病案打印</el-radio-button>
            <el-radio-button label="gather">证件采集</el-radio-button>
          </el-radio-group>
        </div>
        <!-- 打印文件预览 -->
        <div v-if="previewType === 'print'" class="print-preview-wrapper">
          <div v-loading="detailState.loading" class="view-left">
            <div v-if="pdfState.fileList.length > 0" class="file-list">
              <OverflowTooltip
                v-for="file in pdfState.fileList"
                :key="file.fileWsid"
                class="file-item"
                :class="pdfState.fileWsid === file.fileWsid ? 'is-active-file' : ''"
                :content="file.title"
                @click="() => handlePdfClick(file)"
              />
            </div>
            <el-empty
              v-else
              :image="NoDataImage"
              description="请先查询待打印患者信息"
              :image-size="100"
              style="height: 100%"
            />
          </div>
          <div class="view-middle">
            <PdfPreviewComponent :src="pdfState.fileWsid ? '/api/files/' + pdfState.fileWsid : ''" />
          </div>
        </div>
        <!-- 认证信息预览 -->
        <GatherInfo
          v-else
          :relationship="detailState.detailInfo?.applyOrder?.relationship"
          :edit="false"
          :default-imgs="certificateInfo.files"
        />
      </div>

      <!-- 登记信息预览 -->
      <div class="detail-info">
        <div class="title">登记</div>
        <div class="patient-info-card">
          <div class="content-wrapper">
            <div class="patient-title">{{ detailState.detailInfo?.emr?.name || "--" }}</div>
            <div class="patient-title">{{ detailState.detailInfo?.emr?.patientId || "--" }}</div>
          </div>
          <div class="content-wrapper">
            <div class="content-label">住院号:</div>
            <div class="content-value">
              {{
                `${detailState.detailInfo?.emr?.inpNo || "--"} ${
                  detailState.detailInfo?.emr?.inHospitalDeptName || "--"
                }`
              }}
            </div>
          </div>
          <div class="content-wrapper">
            <div class="content-label">出院日期:</div>
            <div class="content-value">{{ formatDatetime(detailState.detailInfo?.emr?.outHospitalDatetime) }}</div>
          </div>
        </div>

        <div class="detail-wrapper">
          <FormGroupHeader title="患者信息" />
          <div class="content-wrapper">
            <div class="content-label">患者姓名:</div>
            <div class="content-value">{{ detailState.detailInfo?.applyOrder?.patientName || "--" }}</div>
          </div>
          <div class="content-wrapper">
            <div class="content-label">证件类型:</div>
            <div class="content-value">
              {{ patientCardType || "--" }}
            </div>
          </div>
          <div class="content-wrapper">
            <div class="content-label">患者证件号:</div>
            <div class="content-value">{{ detailState.detailInfo?.applyOrder?.patientCard || "--" }}</div>
          </div>

          <FormGroupHeader title="申请人信息" />
          <div class="content-wrapper">
            <div class="content-label">与患者关系:</div>
            <div class="content-value">{{ detailState.detailInfo?.applyOrder?.relationshipStr || "--" }}</div>
          </div>
          <div class="content-wrapper">
            <div class="content-label">申请人姓名:</div>
            <div class="content-value">{{ detailState.detailInfo?.applyOrder?.name || "--" }}</div>
          </div>
          <div class="content-wrapper">
            <div class="content-label">申请人证件号:</div>
            <div class="content-value">{{ detailState.detailInfo?.applyOrder?.agentIdCard || "--" }}</div>
          </div>
          <div class="content-wrapper">
            <div class="content-label">申请人手机:</div>
            <div class="content-value">{{ detailState.detailInfo?.applyOrder?.phone || "--" }}</div>
          </div>

          <FormGroupHeader title="打印信息" />
          <div class="content-wrapper">
            <div class="content-label">打印类型:</div>
            <div class="content-value">{{ detailState.detailInfo?.applyOrder?.printTypeName || "--" }}</div>
          </div>
          <div class="content-wrapper">
            <div class="content-label">打印用途:</div>
            <div class="content-value">{{ detailState.detailInfo?.applyOrder?.printPurposeName || "--" }}</div>
          </div>
          <div class="content-wrapper">
            <div class="content-label">打印份数:</div>
            <div class="content-value">{{ detailState.detailInfo?.applyOrder?.duplicateNum || "--" }}</div>
          </div>
          <div class="content-wrapper">
            <div class="content-label">总打印页数:</div>
            <div class="content-value">{{ detailState.detailInfo?.applyOrder?.pageCount || "--" }}</div>
          </div>

          <FormGroupHeader title="费用信息" />
          <div class="content-wrapper">
            <div class="content-label">支付金额:</div>
            <div class="content-value">{{ detailState.detailInfo?.applyOrder?.orderAmount || "--" }}</div>
          </div>

          <FormGroupHeader title="邮寄信息" />
          <div class="content-wrapper">
            <div class="content-label">是否邮寄:</div>
            <div class="content-value">{{ detailState.detailInfo?.applyOrder?.printMail ? "是" : "否" }}</div>
          </div>
          <template v-if="detailState.detailInfo?.applyOrder?.printMail">
            <div class="content-wrapper">
              <div class="content-label">收件人:</div>
              <div class="content-value">{{ detailState.detailInfo?.applyOrder?.printMail?.mailName || "--" }}</div>
            </div>
            <div class="content-wrapper">
              <div class="content-label">手机号:</div>
              <div class="content-value">{{ detailState.detailInfo?.applyOrder?.printMail?.mailPhone || "--" }}</div>
            </div>
            <div class="content-wrapper">
              <div class="content-label">收件地址:</div>
              <div class="content-value">{{ detailState.detailInfo?.applyOrder?.printMail?.mailAddress || "--" }}</div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </DialogContainer>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from "vue"
import { DialogContainer, FormGroupHeader, PdfPreviewComponent, OverflowTooltip } from "@/base-components"
import NoDataImage from "@/assets/png/nodata.png"
import { toastError, formatDatetime } from "@/utils"
import { getPrintDetailApi } from "../../interface"
import { typeOptions } from "../config"
import GatherInfo from "./gather-info.vue"
const visible = ref(false)
const previewType = ref("print")

const detailState = reactive({
  loading: false,
  detailInfo: null as null | Record<string, any>
})

const pdfState = reactive({
  fileList: [] as Array<Record<string, any>>,
  fileWsid: ""
})

const certificateInfo = reactive({
  files: {}
})

function openDialog(wsid, inpNo) {
  visible.value = true
  detailState.loading = true
  getPrintDetailApi(wsid, inpNo)
    .then(res => {
      const data = res.data.data || null
      if (!data) return
      detailState.detailInfo = data
      // pdf文件信息
      pdfState.fileList = data.files
      if (data.files.length > 0) pdfState.fileWsid = data.files[0].fileWsid
      // 证件信息
      certificateInfo.files = data.applyOrder.authimageFiles ? JSON.parse(data.applyOrder.authimageFiles) : {}
      detailState.loading = false
    })
    .catch(err => {
      detailState.loading = false
      toastError(err, "获取详情失败")
    })
}

// 加载pdf
const handlePdfClick = file => {
  pdfState.fileWsid = file?.fileWsid
}

const patientCardType = computed(() => {
  return typeOptions.find(item => item.value === detailState.detailInfo?.applyOrder?.patientCardType)?.label || "--"
})

defineExpose({ openDialog })
</script>

<style lang="less" scoped>
.print-detail-root {
  height: 560px;
  display: flex;
  border: 1px solid rgba(10, 22, 51, 0.1);
  .title {
    height: 48px;
    display: flex;
    align-items: center;
    padding: 0px 16px;
    box-sizing: border-box;
    border-bottom: 1px solid rgba(10, 22, 51, 0.1);
    font-weight: 600;
  }
  .preview-info {
    flex: 1;
    min-width: 0px;
    border-right: 1px solid rgba(10, 22, 51, 0.1);
    display: flex;
    flex-direction: column;
    .print-preview-wrapper {
      height: calc(100% - 48px);
      display: flex;
      .view-left {
        height: 100%;
        width: 200px;
        border-right: 1px solid rgba(10, 22, 51, 0.1);
        .file-list {
          height: 100%;
          overflow-y: auto;
          display: flex;
          flex-direction: column;
          padding: 5px 0px;
          box-sizing: border-box;
          .file-item {
            flex-shrink: 0;
            box-sizing: border-box;
            padding: 5px;
            cursor: pointer;
            &:hover {
              background: rgba(0, 0, 0, 0.02);
            }
          }
          .is-active-file {
            background: var(--el-color-primary-light-9);
          }
        }
      }
      .view-middle {
        flex: 1;
        min-width: 0px;
        :deep(.common-box-shadow) {
          box-shadow: none;
        }
      }
    }
  }

  .detail-info {
    width: 280px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    .patient-info-card {
      margin: 16px 16px 0px;
      padding: 16px;
      background: #eef1ff;
      border-radius: 4px;
      display: flex;
      flex-direction: column;
      row-gap: 12px;
    }

    .detail-wrapper {
      flex: 1;
      min-height: 0px;
      overflow-y: auto;
      margin: 16px;
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .content-wrapper {
      display: flex;
      column-gap: 12px;
      .patient-title {
        font-size: 16px;
        color: rgba(10, 22, 51, 0.7);
        font-weight: 600;
      }
      .content-label {
        color: rgba(10, 22, 51, 0.7);
      }
      .content-value {
        color: #0a1633;
      }
    }
  }
}
</style>
