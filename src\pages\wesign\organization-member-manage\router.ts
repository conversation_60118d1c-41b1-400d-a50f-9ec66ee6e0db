import type { RouteRecordRaw } from "vue-router"

const organizationMemberManageRouter: RouteRecordRaw = {
  path: "/organization-member-manage",
  redirect: "/organization-member-manage/user",
  name: "OrganizationMemberManage",
  meta: {
    title: "组织成员管理",
    icon: "ri-group-line",
    type: "multiple",
    showBreadcrumb: true
  },
  children: [
    {
      path: "/organization-member-manage/user",
      component: () => import("./user-manage/index.vue"),
      meta: { title: "用户管理" }
    },
    // {
    //   path: "/organization-member-manage/department",
    //   component: () => import("./department-manage/index.vue"),
    //   meta: { title: "科室管理" }
    // },
    {
      path: "/organization-member-manage/role",
      component: () => import("./role-manage/index.vue"),
      meta: { title: "角色管理" }
    },
    {
      path: "/organization-member-manage/permission",
      component: () => import("./permission-manage/index.vue"),
      meta: { title: "权限管理" }
    },
    {
      path: "/organization-member-manage/borrow-blacklist",
      component: () => import("./borrow-blacklist/index.vue"),
      meta: { title: "借阅黑名单" }
    }
  ]
}

export default organizationMemberManageRouter
