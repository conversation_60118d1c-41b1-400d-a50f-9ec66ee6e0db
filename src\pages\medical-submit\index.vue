<template>
  <Spin :spinning="systemStore.pageLoading" :tip="loadingText">
    <!-- 选择归档文件和预览 -->
    <StepArchive
      ref="stepArchiveRef"
      :inp-no="state.inpNo"
      :patient-name="state.patientName"
      :last-sync-time="state.lastSyncTime"
      :secret-key="state.secretKey"
      :allow-missing="state.allowMissing"
      @refresh="handleRefresh"
      @delete="handleDelete"
      @submit="handleSubmit"
    />

    <!-- 设置签名 -->
    <StepSign
      ref="stepSignRef"
      :inp-no="state.inpNo"
      :allow-missing="state.allowMissing"
      :patient-name="state.patientName"
      @refresh="handleRefreshDocument"
    />
  </Spin>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, watch, onBeforeUnmount } from "vue"
import { useRoute } from "vue-router"
import { Spin } from "ant-design-vue"
import StepArchive from "./components/StepArchive.vue"
import StepSign from "./components/StepSign.vue"
import { useSubmitStore } from "./useSubmitStore"
import type { AllowMissingType } from "./config"
import { useSystemStore } from "@/stores"
import { decryptStr } from "@/utils"

const submitStore = useSubmitStore()
const systemStore = useSystemStore()
const route = useRoute()

let loadingTimer

const loadingText = ref("")

watch(
  () => systemStore.pageLoading,
  val => {
    if (!val) {
      clearInterval(loadingTimer)
      return
    }
    loadingText.value = systemStore.loadingText
    loadingTimer = setInterval(() => {
      loadingText.value = loadingText.value.endsWith("...") ? loadingText.value.slice(0, -3) : loadingText.value + "."
    }, 500)
  }
)

onBeforeUnmount(() => clearInterval(loadingTimer))

const state = reactive({
  patientName: "", // 患者名称
  lastSyncTime: "", // 上次同步时间
  inpNo: "", // 病案号
  secretKey: "", // 封存密钥
  allowMissing: "NO" as AllowMissingType // 是否允许缺失
})

watch(
  () => route.query.inpNo,
  val => {
    if (val) {
      getData()
    }
  }
)

const getData = () => {
  state.inpNo = route.query?.inpNo as string
  state.secretKey = decryptStr((route.query?.secretKey as string) || "")
  state.patientName = (route.query?.patientName as string) || ""
  state.lastSyncTime = (route.query?.lastSyncTime as string) || ""
  state.allowMissing = (route.query?.allowMissing as AllowMissingType) || ""
}

onMounted(() => {
  getData()
  submitStore.activeStep = route.query?.needSign === "YES" ? 1 : 2
})

onUnmounted(() => {
  submitStore.activeStep = 1
  submitStore.checkedArchiveDocumentWsidList = []
})

/* =============== 签署 =============== */

const stepSignRef = ref()

// 同步成功后刷新签署文档
const handleRefresh = () => {
  stepSignRef.value.getNeedSignDocuments()
}

const stepArchiveRef = ref()
const handleRefreshDocument = () => {
  stepArchiveRef.value.getInactiveDocuments()
  stepArchiveRef.value.getSubmissionTree()
}

// 第一页删除文件
const handleDelete = (wsid: string) => {
  stepSignRef.value.deleteDocument(wsid)
}

const handleSubmit = () => {
  stepSignRef.value?.confirmSubmit()
}
</script>

<style lang="less" scoped>
:deep(.custom-card) {
  height: 100vh !important;
}

:deep(.custom-card-body) {
  background: #eff2f7;
}
</style>
