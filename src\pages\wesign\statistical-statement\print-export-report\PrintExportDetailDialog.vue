<template>
  <DialogContainer title="详情" v-model:visible="visible" :width="600" :no-footer="true">
    <div class="detail-container">
      <div class="title-wrapper">
        <div>具体内容如下:</div>
        <div>
          共计：
          <span class="page-num">{{ totalPage }}</span>
          页
        </div>
      </div>
      <div class="detail-list">
        <div v-if="props.document.length > 0" class="detail-item" v-for="(item, index) in props.document">
          <span class="detail-index">{{ index + 1 }}.</span>
          <span class="detail-title">{{ item.title }}</span>
          <span>({{ item.filePage }}页)</span>
        </div>
        <div v-else>暂无数据</div>
      </div>
    </div>
  </DialogContainer>
</template>

<script setup lang="ts">
import { ref, computed } from "vue"
import { DialogContainer } from "@/base-components"

interface PropsType {
  document: Array<Record<string, any>>
}

const props = defineProps<PropsType>()

const visible = ref(false)

const totalPage = computed(() => {
  return props.document.reduce((acc, cur) => {
    return acc + cur.filePage
  }, 0)
})

defineExpose({
  openDialog: () => {
    visible.value = true
  }
})
</script>

<style lang="less" scoped>
.detail-container {
  .title-wrapper {
    display: flex;
    justify-content: space-between;

    .page-num {
      color: #409eff;
      padding-right: 6px;
    }
  }

  .detail-list {
    margin-top: 20px;
    min-height: 50px;
    max-height: 500px;
    overflow: auto;
    font-size: 16px;
    background: #fafafa;
    padding: 10px;
    padding-right: 20px;
    color: #333;

    .detail-item {
      line-height: 1.5;
      margin-bottom: 12px;
      .detail-title {
        padding-right: 8px;
      }

      .detail-index {
        padding-right: 6px;
      }
    }
  }
}
</style>
