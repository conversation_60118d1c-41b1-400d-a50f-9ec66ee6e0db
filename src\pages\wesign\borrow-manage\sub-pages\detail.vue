<template>
  <div class="my-borrow-detail">
    <div v-loading="state.loading" class="view-left">
      <MedicalRecordTree
        can-collapse
        :base-info="state.baseInfo"
        :tree-info="state.treeInfo"
        @click-pdf="handlePdfClick"
      />
    </div>
    <div class="view-middle common-box-shadow">
      <PdfPreviewComponent ref="pdfPreviewRef" :src="state.pdfSrc" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted } from "vue"
import { useRoute, useRouter } from "vue-router"
import { PdfPreviewComponent } from "@/base-components"
import { MedicalRecordTree } from "@/page-components"
import { decryptStr, extractErrorMsg, SystemAlert } from "@/utils"
import { getMyBorrowViewApi, getBorrowMrnoViewApi, getBorrowDeptViewApi } from "./interface"

const route = useRoute()
const router = useRouter()
const { id, type = "", secretKey = "" } = route.query

const state = reactive({
  baseInfo: {},
  treeInfo: null as any,
  pdfSrc: "",
  loading: false
})

const handlePdfClick = node => {
  state.pdfSrc = "/api" + node?.contentFilePath
}

onMounted(() => {
  getBorrowDetail()
})

// 获取借阅文档详情
const getBorrowDetail = () => {
  let requestApi = getBorrowMrnoViewApi
  let requestParams: any = {}
  if (secretKey) {
    const sealKey = decryptStr(secretKey as string)
    requestParams["sealKey"] = sealKey
  }
  if (type === "mrno") {
    requestParams["documentBagWsid"] = id
  } else if (type === "dept") {
    requestParams["documentBagWsid"] = id
    requestApi = getBorrowDeptViewApi
  } else {
    requestParams["paramsId"] = id
    requestApi = getMyBorrowViewApi
  }
  state.loading = true
  requestApi(requestParams)
    .then(res => {
      state.baseInfo = res.data.data.baseInfo
      state.treeInfo = res.data.data.treeInfo
      state.loading = false
    })
    .catch(err => {
      const errMessage = extractErrorMsg(err)
      SystemAlert(errMessage).then(() => {
        router.back()
      })
      state.loading = false
    })
}
</script>

<style lang="less" scoped>
.my-borrow-detail {
  display: flex;
  justify-content: space-between;
  height: 100%;
  .view-middle {
    overflow-x: auto;
    min-width: 400px;
    flex: 1;
    box-sizing: border-box;
  }
}
</style>
