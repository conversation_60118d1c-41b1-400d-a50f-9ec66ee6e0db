import { uniq, flow, filter } from "lodash-es"

export const tabsRouterList = [
  { label: "页面配置", path: "/system-manage/system-arguments/page" },
  { label: "功能配置", path: "/system-manage/system-arguments/function" },
  { label: "登录配置", path: "/system-manage/system-arguments/login" },
  { label: "水印配置", path: "/system-manage/system-arguments/watermark" },
  { label: "IP黑/白名单", path: "/system-manage/system-arguments/IP" },
  { label: "全局配置", path: "/system-manage/system-arguments/global-setting" }
]

export enum IPListTypeEnum {
  BLACK = "BLACK",
  WHITE = "WHITE"
}

// ip 地址正则
const IPReg =
  /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\/(3[0-2]|[12]?[0-9]))?$/

// 校验IP地址
export function checkIP(value: string): any {
  const IPList = value
    ?.split("\n")
    ?.map(item => item?.trim())
    ?.filter(item => item?.length !== 0)

  // 校验ip 个数，Ip 个数不能超过4000个
  if (IPList.length > 4000) {
    return "IP地址数量不能超过4000个！"
  }

  // 校验 ip 地址是否重复
  const repeatIP = flow(
    filter((item: string) => item?.length > 0),
    uniq
  )(IPList)
  if (repeatIP.length !== IPList.length) {
    return "IP地址不能重复！"
  }

  // 校验 ip 地址是否合法
  const errorIP: any = []
  IPList.forEach((ip, index) => {
    if (ip?.length > 0 && !IPReg.test(ip.trim())) {
      errorIP.push({ index: index + 1, ip })
    }
  })
  const errorIPList = errorIP.map((item: any) => {
    return `第${item.index}行${item.ip}`
  })
  return errorIPList?.length > 0 ? `${errorIPList?.join(",")}地址不正确` : undefined
}

const validateIP = (_rule: any, value: string, callback: (err?: Error) => void) => {
  if (!value) return callback()
  const errorIP = checkIP(value)
  if (errorIP?.length) {
    return callback(errorIP)
  }
  return callback()
}
export const IPRules = {
  ipListType: [
    {
      required: true,
      message: "请选择IP名单类型",
      trigger: "change"
    }
  ],
  ipList: [
    {
      validator: validateIP,
      trigger: "change"
    }
  ]
}

export const QRcodePositionStyle = {
  LT: "left:10%;top:10%;",
  RT: "right:10%;top:10%;",
  LB: "left:10%;bottom:10%;",
  RB: "right:10%;bottom:10%;"
}

export const straddleStampLocationStyle = {
  "1": "right:0;top:0%;",
  "2": "right:0;top:40%",
  "3": "right:0;bottom:0%;"
}
