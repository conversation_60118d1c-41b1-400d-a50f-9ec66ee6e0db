import type { RouteRecordRaw } from "vue-router"

const systemManageRouter: RouteRecordRaw = {
  path: "/system-manage",
  name: "SystemManage",
  redirect: "/system-manage/plan-config",
  meta: {
    title: "系统配置",
    icon: "ri-settings-2-line",
    type: "multiple",
    showBreadcrumb: true
  },
  children: [
    {
      path: "/system-manage/plan-config",
      redirect: "/system-manage/plan-config/doc",
      meta: { title: "方案配置" },
      children: [
        {
          path: "/system-manage/plan-config/doc",
          component: () => import("./plan-config/DocType.vue"),
          meta: { title: "文书类型" }
        },
        {
          path: "/system-manage/plan-config/directory",
          component: () => import("./plan-config/DirectoryManage.vue"),
          meta: { title: "目录管理" }
        },
        {
          path: "/system-manage/plan-config/plan",
          component: () => import("./plan-config/PlanManage.vue"),
          meta: { title: "方案管理" }
        }
      ]
    },
    {
      path: "/system-manage/data-standard",
      component: () => import("./data-standard/MedicalMeta.vue"),
      meta: { title: "数据规范" }
    },
    {
      path: "/system-manage/system-arguments",
      redirect: "/system-manage/system-arguments/page",
      meta: { title: "系统参数" },
      children: [
        {
          path: "/system-manage/system-arguments/page",
          component: () => import("./system-arguments/PageConfig.vue"),
          meta: { title: "页面配置" }
        },
        {
          path: "/system-manage/system-arguments/function",
          component: () => import("./system-arguments/FunctionConfig.vue"),
          meta: { title: "功能配置" }
        },
        {
          path: "/system-manage/system-arguments/login",
          component: () => import("./system-arguments/LoginConfig.vue"),
          meta: { title: "登录配置" }
        },
        {
          path: "/system-manage/system-arguments/watermark",
          component: () => import("./system-arguments/WatermarkConfig.vue"),
          meta: { title: "水印配置" }
        },
        {
          path: "/system-manage/system-arguments/IP",
          component: () => import("./system-arguments/IPConfig.vue"),
          meta: { title: "IP黑/白名单" }
        },
        {
          path: "/system-manage/system-arguments/global-setting",
          component: () => import("./system-arguments/GlobalSetting.vue"),
          meta: { title: "全局配置" }
        }
      ]
    },
    {
      path: "/system-manage/integrated-query",
      meta: { title: "综合查询" },
      redirect: "/system-manage/integrated-query/list",

      children: [
        {
          path: "/system-manage/integrated-query/list",
          meta: { title: "查询列表" },
          component: () => import("./integrated-query/index.vue")
        },
        {
          path: "/system-manage/integrated-query/view",
          meta: { title: "详情" },
          component: () => import("@/pages/wesign/medical-record-manage/sub-pages/detail.vue")
        }
      ]
    },
    {
      path: "/system-manage/process-setting",
      meta: { title: "流程配置" },
      redirect: "/system-manage/process-setting/approval-process",
      children: [
        {
          path: "/system-manage/process-setting/approval-process",
          // redirect: "/system-manage/process-setting/approval-process/list",
          meta: { title: "审批流程" },
          component: () => import("./process-setting/approval-process/list.vue")
          // children: [
          //   {
          //     path: "/system-manage/process-setting/approval-process/list",
          //     component: () => import("./process-setting/approval-process/list.vue"),
          //     meta: { title: "列表" }
          //   },
          //   {
          //     path: "/system-manage/process-setting/approval-process/create",
          //     component: () => import("./process-setting/approval-process/detail.vue"),
          //     meta: { title: "新建" }
          //   },
          //   {
          //     path: "/system-manage/process-setting/approval-process/edit",
          //     component: () => import("./process-setting/approval-process/detail.vue"),
          //     meta: { title: "编辑" }
          //   }
          // ]
        },
        {
          path: "/system-manage/process-setting/create",
          meta: { title: "新建" },
          component: () => import("./process-setting/approval-process/detail.vue")
        },
        {
          path: "/system-manage/process-setting/edit",
          component: () => import("./process-setting/approval-process/detail.vue"),
          meta: { title: "编辑" }
        },
        {
          meta: { title: "业务流程", showBreadcrumb: true },
          path: "/system-manage/process-setting/business-process",
          component: () => import("./process-setting/BusinessProcess.vue")
        }
      ]
    },
    {
      path: "/system-manage/system-notice",
      component: () => import("./system-notice/index.vue"),
      meta: { title: "系统公告" }
    },
    {
      path: "/system-manage/system-log",
      redirect: "/system-manage/system-log/login",
      meta: { title: "系统日志" },
      children: [
        {
          path: "/system-manage/system-log/login",
          component: () => import("./system-log/LoginLog.vue"),
          meta: { title: "登录日志" }
        },
        {
          path: "/system-manage/system-log/operation",
          component: () => import("./system-log/OperationLog.vue"),
          meta: { title: "操作日志" }
        }
      ]
    },
    {
      path: "/system-manage/resource-config",
      component: () => import("./resource-config/index.vue"),
      meta: { title: "资源配置" }
    },
    {
      path: "/system-manage/message-push",
      component: () => import("./message-push/index.vue"),
      meta: { title: "消息推送" }
    },
    {
      path: "/system-manage/form-config",
      component: () => import("./form-config/index.vue"),
      meta: { title: "表单配置" }
    }
  ]
}

export default systemManageRouter
