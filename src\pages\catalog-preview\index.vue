<template>
  <Spin :spinning="systemStore.pageLoading" :tip="loadingText">
    <CatalogEdit></CatalogEdit>
  </Spin>
</template>

<script setup lang="ts">
import { ref, watch } from "vue"
import { useRoute } from "vue-router"
import { Spin } from "ant-design-vue"
import { CatalogEdit } from "@/page-components"
import { useSystemStore } from "@/stores"
const systemStore = useSystemStore()
const route = useRoute()

let loadingTimer

const loadingText = ref("")

watch(
  () => systemStore.pageLoading,
  val => {
    if (!val) {
      clearInterval(loadingTimer)
      return
    }
    loadingText.value = systemStore.loadingText
    loadingTimer = setInterval(() => {
      loadingText.value = loadingText.value.endsWith("...") ? loadingText.value.slice(0, -3) : loadingText.value + "."
    }, 500)
  }
)
</script>

<style></style>
