import type { TableColumnItem } from "@/types"
import type { FormRules } from "element-plus"

export const menuConfigTableColumns: Array<TableColumnItem> = [
  { type: "selection", width: 50, fixed: "left" },
  { prop: "name", label: "菜单名", minWidth: 100 },
  { prop: "id", label: "菜单ID", minWidth: 150 },
  { prop: "index", label: "序号", width: 100 },
  { prop: "level", label: "菜单等级", minWidth: 150 },
  { prop: "icon", label: "图标", minWidth: 120 },
  { prop: "status", label: "状态", minWidth: 120 },
  { prop: "operation", label: "操作", width: 400, fixed: "right" }
]

// 菜单可选icon配置
export const menuIconsList = [
  { class: "ri-home-4-line" },
  { class: "ri-draft-line" },
  { class: "ri-folder-open-line" },
  { class: "ri-book-read-line" },
  { class: "ri-folder-forbid-line" },
  { class: "ri-printer-cloud-line" },
  { class: "ri-inbox-archive-line" },
  { class: "ri-equalizer-line" },
  { class: "ri-folder-shield-2-line" },
  { class: "ri-file-add-line" },
  { class: "ri-lock-line" },
  { class: "ri-find-replace-line" },
  { class: "ri-line-chart-line" },
  { class: "ri-group-line" },
  { class: "ri-sound-module-line" },
  { class: "ri-tools-line" },
  { class: "ri-file-settings-line" },
  { class: "ri-equalizer-fill" },
  { class: "ri-tools-fill" },
  { class: "ri-layout-grid-line" },
  { class: "ri-folder-open-fill" },
  { class: "ri-building-2-line" },
  { class: "ri-community-line" },
  { class: "ri-pie-chart-2-line" },
  { class: "ri-archive-2-line" },
  { class: "ri-bar-chart-box-line" },
  { class: "ri-stack-line" },
  { class: "ri-layout-masonry-line" },
  { class: "ri-collage-line" },
  { class: "ri-terminal-box-line" },
  { class: "ri-folder-4-line" },
  { class: "ri-folders-line" },
  { class: "ri-money-dollar-box-line" },
  { class: "ri-map-2-line" },
  { class: "ri-navigation-line" },
  { class: "ri-checkbox-multiple-line" },
  { class: "ri-contacts-line" },
  { class: "ri-box-3-line" },
  { class: "ri-handbag-line" },
  { class: "ri-team-line" },
  { class: "ri-bar-chart-box-line" },
  { class: "ri-settings-4-line" },
  { class: "ri-file-info-line" },
  { class: "ri-chat-upload-line" }
]

export const operationColumns: Array<TableColumnItem> = [
  { prop: "index", label: "操作索引", minWidth: 80 },
  { prop: "id", label: "操作id", minWidth: 200 },
  { prop: "name", label: "操作名称", minWidth: 150 },
  { prop: "status", label: "状态", minWidth: 100 },
  { prop: "operation", label: "操作", width: 120 }
]

export const nameValidator = (_rule: any, value: string, callback: (err?: Error) => void) => {
  if (!value) callback()
  // else if (/^[\u3400-\u4dbf\u4e00-\u9fffA-Za-z0-9\-_.!@#$%^&*()，。！？、【】；《》：“”]{1,50}$/.test(value)) callback()
  else if (value.length <= 40) callback()
  else callback(new Error(" 限制40字符"))
}

export const formRules: FormRules = {
  name: [{ required: true, message: "请输入菜单名称", trigger: "blur" }, { validator: nameValidator }],
  code: [{ required: true, message: "请输入菜单路径", trigger: "blur" }],
  parentCode: [{ required: true, message: "请选择上级菜单", trigger: "blur" }],
  parentGroupId: [{ required: true, message: "请选择上级模块", trigger: "blur" }],
  iconCode: [{ required: true, message: "请选择图标", trigger: "blur" }]
}
