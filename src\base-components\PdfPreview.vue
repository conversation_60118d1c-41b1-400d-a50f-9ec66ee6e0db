<template>
  <div class="pdf-preview-container common-box-shadow">
    <div v-if="showControl" class="pdf-preview-header">
      <!-- 旋转操作 -->
      <div class="change-box">
        <!-- <el-tooltip content="向左旋转" placement="top">
          <i class="ri-arrow-go-back-line action" @click="rotatePdf('left')"></i>
        </el-tooltip>
        <el-tooltip content="向右旋转" placement="top">
          <i class="ri-arrow-go-forward-line action" @click="rotatePdf('right')"></i>
        </el-tooltip> -->
      </div>

      <!-- 缩放操作 -->
      <div class="change-box">
        <el-tooltip content="缩小" placement="top">
          <i class="ri-zoom-out-line action" @click="scalePdf('decrease')"></i>
        </el-tooltip>
        <el-select v-model="pdfState.scale" placeholder="请选择">
          <el-option
            v-for="scale in scaleOptions"
            :key="scale.value"
            :label="scale.label"
            :value="scale.value"
          ></el-option>
        </el-select>
        <el-tooltip content="放大" placement="top">
          <i class="ri-zoom-in-line action" @click="scalePdf('increase')"></i>
        </el-tooltip>
      </div>

      <!-- 翻页操作 -->
      <div class="change-box">
        <div v-if="pdfState.enablePaging" class="page-input-container">
          <el-tooltip content="上一页" placement="top">
            <i class="ri-arrow-left-s-line action" @click="turnPage('pre')"></i>
          </el-tooltip>
          <div class="page-input">
            <el-input-number
              v-model="pdfState.inputPage"
              class="input-container"
              size="small"
              :controls="false"
              step-strictly
              @keydown.enter="handleInputChange"
            ></el-input-number>
            <div class="page-hint number-slice">/</div>
            <div class="page-hint">{{ pdfState.total }}</div>
          </div>
          <el-tooltip content="下一页" placement="top">
            <i class="ri-arrow-right-s-line action" @click="turnPage('next')"></i>
          </el-tooltip>
        </div>
        <div v-else class="all-total">{{ `共 ${pdfState.total} 页` }}</div>
        <el-checkbox v-model="pdfState.enablePaging" label="分页模式" size="default" />
      </div>
    </div>

    <!-- 渲染区域 -->
    <div ref="pdfContentRef" v-loading="pdfState.status === 'loading'" class="pdf-preview-content">
      <div v-if="pdfState.status === 'error'" class="pdf-error">文件加载失败</div>
      <div v-else-if="!pdfState.source" class="pdf-error">暂无数据</div>
      <VuePdfEmbed
        v-else-if="pdfState.source"
        ref="pdfRef"
        :style="{ width: previewWidth + 'px' }"
        :width="previewWidth"
        :source="pdfSourceObject"
        :rotation="pdfState.rotation"
        :page="pdfState.enablePaging ? pdfState.current : undefined"
        disable-text-layer
        @rendered="extractPdfInfo"
      />
      <div class="sign-layer">
        <div
          :style="{
            position: 'relative',
            margin: 'auto',
            width: previewWidth + 'px',
            height: previewHeight + 'px'
          }"
        >
          <slot name="sign"></slot>
        </div>
      </div>

      <div v-if="props.showWatermark" class="watermark-layer">
        <div
          ref="watermarkRef"
          :style="{ margin: 'auto', width: previewWidth + 'px', height: previewHeight + 'px' }"
        ></div>
      </div>
    </div>

    <!-- 病案位置 -->
    <div v-if="props.medicalLocation" class="pdf-preview-location">病案位置：{{ props.medicalLocation }}</div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, watch, computed } from "vue"
import { toNumber } from "lodash-es"
import VuePdfEmbed from "vue-pdf-embed"
import { getWatermarkApi } from "@/interfaces"
import axios from "@/interfaces/axios-instance"
import { Message, toastError, defaultPdfRenderConfigs } from "@/utils"

const props = defineProps({
  enableCache: { type: Boolean, default: true }, // 是否启用缓存
  src: { type: String, required: true },
  defaultWidth: { type: Number, default: 1000 },
  showControl: { type: Boolean, default: true },
  showWatermark: { type: Boolean, default: true, require: false },
  medicalLocation: { type: String, default: "", require: false } // 病案位置
})

const scaleOptions = [
  { label: "50%", value: 0.5 },
  { label: "75%", value: 0.75 },
  { label: "100%", value: 1.0 },
  { label: "125%", value: 1.25 },
  { label: "150%", value: 1.5 },
  { label: "200%", value: 2.0 }
]

// empty为空，loading为加载中，success为加载成功，error为加载失败
type PdfStatusType = "empty" | "loading" | "success" | "error"

// vertical为常规竖屏，horizontal为横屏
type PdfFileType = "vertical" | "horizontal"

let flag = false // 用于标识是否已执行extractPdfInfo

const pdfRef = ref()
const pdfContentRef = ref()
const pdfState = reactive({
  type: "vertical" as PdfFileType, // pdf文件类型
  status: "empty" as PdfStatusType, // pdf文件加载状态
  source: "",
  rotation: 0,
  scale: 1.0, // 预览缩放比例
  inputPage: 1, // 页码输入框值
  current: 1, // 当前所在页
  total: 0, // 总页数
  enablePaging: false
})

// pdf原始尺寸
const rawSize = reactive({
  width: 0,
  height: 0
})

// 真实预览宽度
const previewWidth = computed(() => pdfState.scale * props.defaultWidth)

const previewHeight = computed(() => {
  if (ratio.value <= 0) return 0
  else if (pdfState.enablePaging) return Math.ceil(rawSize.height * ratio.value * 1)
  else return Math.ceil(rawSize.height * ratio.value * pdfState.total)
})

const pdfSourceObject = computed(() => ({
  url: pdfState.source,
  ...defaultPdfRenderConfigs
}))

// pdf预览尺寸与原始尺寸比
const ratio = computed(() => {
  if (pdfState.type === "horizontal") {
    return rawSize.height ? Number((previewWidth.value / rawSize.height).toFixed(3)) : 0
  } else {
    return rawSize.width ? Number((previewWidth.value / rawSize.width).toFixed(3)) : 0
  }
})

// 文件地址变化时发起新请求
watch(
  () => props.src,
  () => {
    if (!props.src || props.src.includes("undefined")) {
      pdfState.status = "empty"
      pdfState.source = ""
      pdfState.total = 0
      return
    }
    if (props.src.includes("data")) {
      pdfState.current = 1
      pdfState.inputPage = 1
      pdfState.rotation = 0
      pdfState.scale = 1.0
      pdfState.source = props.src
      pdfState.status = "success"
      return
    }
    pdfState.status = "loading"
    flag = false
    const requestUrl = props.enableCache ? props.src : props.src + `?t=${new Date().getTime()}`
    axios({
      method: "get",
      url: requestUrl,
      responseType: "blob"
    })
      .then(res => {
        pdfState.current = 1
        pdfState.inputPage = 1
        pdfState.rotation = 0
        pdfState.scale = 1.0
        pdfState.source = URL.createObjectURL(res.data)
        pdfState.status = "success"
      })
      .catch(error => {
        toastError(error, "文件获取失败")
        pdfState.status = "error"
        pdfState.source = ""
        pdfState.total = 0
      })
  },
  { immediate: true }
)

// 旋转操作
function rotatePdf(direction: "left" | "right") {
  if (direction === "left") {
    if (pdfState.rotation === 0) pdfState.rotation = 270
    else pdfState.rotation -= 90
  } else {
    if (pdfState.rotation === 270) pdfState.rotation = 0
    else pdfState.rotation += 90
  }
}

// 缩放操作
function scalePdf(type: "increase" | "decrease") {
  const currentScaleIndex = scaleOptions.findIndex(item => item.value === pdfState.scale)
  const nextScaleIndex = type === "increase" ? currentScaleIndex + 1 : currentScaleIndex - 1
  if (scaleOptions[nextScaleIndex]) pdfState.scale = scaleOptions[nextScaleIndex].value
}

// 翻页操作
function turnPage(type: "pre" | "next") {
  const nextPage = type === "pre" ? pdfState.current - 1 : pdfState.current + 1
  if (nextPage > 0 && nextPage < pdfState.total + 1) {
    pdfState.inputPage = nextPage
    pdfState.current = nextPage
  }
}

// 跳转到指定页
function handleInputChange() {
  const validPages = new Array(pdfState.total).fill(0).map((_item, index) => index + 1)
  if (validPages.includes(pdfState.inputPage)) pdfState.current = pdfState.inputPage
  else Message.warning("请输入有效的页码")
}

// pdf渲染后获取页码和旋转
function extractPdfInfo() {
  if (!pdfRef.value || flag) return
  pdfState.total = pdfRef.value.pageCount
  pdfRef.value.document.getPage(1).then(pageData => {
    pdfState.rotation = 0
    const view = pageData?.view ?? []
    if (view.length > 0) {
      // 横板pdf
      if (view[2] > view[3]) {
        rawSize.width = view[3]
        rawSize.height = view[2]
        pdfState.type = "horizontal"
        // 标准竖版pdf
      } else {
        rawSize.width = view[2]
        rawSize.height = view[3]
        pdfState.type = "vertical"
      }
    }

    flag = true
  })
}

/* ======================== 水印绘制 ======================== */

watch([() => previewWidth.value, () => previewHeight.value], val => {
  if (props.showWatermark) getWatermark()
})

const watermarkRef = ref()
const watermarkState = reactive({
  waterMark: "",
  width: 0,
  height: 0
})

function getTextActualSize(text) {
  const canvas = document.createElement("canvas")
  canvas.width = 400
  canvas.height = 400
  const ctx = canvas.getContext("2d") as CanvasRenderingContext2D
  const { actualBoundingBoxAscent, actualBoundingBoxDescent, actualBoundingBoxLeft, actualBoundingBoxRight } =
    ctx.measureText(text)
  return {
    width: actualBoundingBoxLeft + actualBoundingBoxRight,
    height: actualBoundingBoxAscent + actualBoundingBoxDescent
  }
}

function getWatermark() {
  getWatermarkApi()
    .then(res => {
      const resData = res.data.data
      // 获取绘制文本的尺寸
      const spacing = resData.rowSize
      const textSize = getTextActualSize(resData.waterMark)
      const fontSize = resData.fontSize * ratio.value
      const rotatePI = (-toNumber(resData.radians) / 180) * Math.PI
      const canvas = document.createElement("canvas")
      canvas.width = (textSize.width * 4 + 20 + spacing) / 2
      canvas.height = (textSize.width * 4 + 20 + spacing) / 2
      const ctx = canvas.getContext("2d")
      if (!ctx) return
      // 绘制之前清空画布
      ctx.clearRect(0, 0, watermarkState.width, watermarkState.height)
      // 设置画笔属性
      ctx.font = `${fontSize}px serif`
      ctx.fillStyle = `rgba(${resData.colorRgb})`
      ctx.globalAlpha = resData.transparency
      // 平移画布后，左上角不再是0，0, 而是（-canvas.width / 2， -canvas.height / 2）
      ctx.translate(canvas.width / 2, canvas.height / 2)
      // 旋转水印方向
      ctx.rotate(rotatePI)
      // 文字宽度
      const stringWidth = ctx.measureText(resData.waterMark).width

      ctx.fillText(resData.waterMark, -stringWidth / 2, fontSize / 4)
      if (watermarkRef.value) {
        watermarkRef.value.style.backgroundImage = `url(${canvas.toDataURL()})`
        watermarkRef.value.style.backgroundRepeat = "repeat"
        watermarkRef.value.style.backgroundPosition = `top left`
      }
    })
    .catch(err => {
      toastError(err, "获取水印失败")
    })
}

defineExpose({
  ratio,
  rawSize,
  pdfContentRef
})
</script>

<style lang="less" scoped>
.pdf-preview-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  .pdf-preview-header {
    height: 40px;
    background-color: #999;
    display: flex;
    color: #fff;
    font-size: 14px;
    line-height: 24px;
    padding: 0px 10px;
    i {
      font-size: 18px;
    }

    .change-box {
      flex: 1;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      column-gap: 10px;
      &:first-child {
        justify-content: flex-start;
      }
      &:last-child {
        justify-content: flex-end;
      }

      .action {
        cursor: pointer;
      }
      .all-total {
        text-align: right;
        padding-right: 12px;
      }
      .page-input-container {
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
      .page-input {
        display: flex;
        align-items: center;
        white-space: nowrap;
        :deep(.el-input-number) {
          width: 45px;
          border: none;
          .el-input__wrapper {
            padding: 0px 5px;
            .el-input__inner {
              font-size: 14px;
              color: #666;
              text-align: center;
              line-height: 18px;
              height: 20px;
            }
          }
        }
      }
      :deep(.el-select) {
        width: 100px;
        line-height: 20px;
        .el-input {
          --el-input-height: 24px;
          line-height: 20px;
          font-size: 14px;
        }
      }
      .page-hint {
        cursor: default;
      }
      .number-slice {
        padding: 0px 10px;
      }
      :deep(.el-checkbox) {
        color: #fff;
        --el-checkbox-checked-icon-color: #333;
        .el-checkbox__label {
          color: #fff;
        }
        .el-checkbox__inner {
          border-color: transparent;
          background: aliceblue;
        }
      }
    }
  }

  .pdf-preview-content {
    flex: 1;
    overflow: auto;
    position: relative;
    :deep(.vue-pdf-embed) {
      width: 100%;
      margin: auto;
      display: flex;
      flex-direction: column;
      align-items: center;
      div:not(:last-child) {
        .annotationLayer {
          height: 1px;
          width: 100%;
          background: #aaa;
        }
      }
    }

    .sign-layer,
    .watermark-layer {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
    }

    .pdf-error {
      color: #999999;
      font-size: 16px;
      letter-spacing: 8px;
      text-align: center;
      margin-top: 50px;
    }
  }

  .pdf-preview-location {
    height: 30px;
    line-height: 30px;
    padding: 10px;
  }
}
</style>
