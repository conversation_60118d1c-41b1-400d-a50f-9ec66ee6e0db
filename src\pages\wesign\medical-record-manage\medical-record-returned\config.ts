import type { TableColumnItem, BaseOptionItem } from "@/types"

// 已签收列表
export const returnedColumns: Array<TableColumnItem> = [
  // { prop: "statusEnumName", label: "病案状态", minWidth: 120 },
  { prop: "returnStatus", label: "退回状态", minWidth: 180 },
  { prop: "patientName", label: "姓名", minWidth: 260, must: true, showOverflowTooltip: false },
  { prop: "mrNo", label: "病案号", minWidth: 150, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 100, must: true },
  { prop: "visitId", label: "住院次数", minWidth: 90 },
  { prop: "patientSex", label: "性别", minWidth: 60 },
  { prop: "idCard", label: "身份证号", minWidth: 180 },
  { prop: "inHospitalDatetime", label: "入院日期", minWidth: 180, sortable: true },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 100 },
  { prop: "outHospitalDatetime", label: "出院日期", minWidth: 180, sortable: true },
  {
    prop: "outHospitalDeptName",
    label: "出院科室",
    tooltipContent: "在院患者入院科室与出院科室默认一致",
    minWidth: 100
  },
  { prop: "isSpecial", label: "特殊病案", minWidth: 90 },
  { prop: "deletionCount", label: "缺失记录", minWidth: 90 },

  { prop: "operation", label: "操作", width: 100, fixed: "right", must: true }
]

export const returnedStatusOptions: Array<BaseOptionItem> = [
  { label: "未退", value: 0 },
  { label: "医生质控退回", value: 1 },
  { label: "护士质控退回", value: 2 },
  { label: "医护质控退回", value: 3 },
  { label: "签收退回", value: 4 },
  { label: "编目退回", value: 5 },
  { label: "终末质控退回", value: 6 },
  { label: "归档退回", value: 7 },
  { label: "召回", value: 8 },
  { label: "撤销", value: 9 },
  { label: "提交退回", value: 10 }
]
