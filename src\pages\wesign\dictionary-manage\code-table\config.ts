import type { TableColumnItem, DialogFormConfigItem } from "@/types"
import type { TabsPaneContext, FormInstance, FormRules } from "element-plus"
import { variableCodeValidator } from "@/utils"

export const tabsRouterList = [
  { label: "首页元数据", path: "/system-manage/data-standard/home-meta" },
  { label: "医保结算元数据", path: "/system-manage/data-standard/medical-insurance-meta" },
  { label: "数据元值域代码表", path: "/system-manage/data-standard/code-table" }
]

/* ======================== 数据元值域代码表 ======================== */

export const codeTableColumns: Array<TableColumnItem> = [
  { prop: "code", label: "值域代码", minWidth: 100 },
  { prop: "key", label: "值", minWidth: 100 },
  { prop: "value", minWidth: 150, label: "名称" },
  { prop: "enableDefaultDesc", minWidth: 150, label: "默认值" },
  // { prop: "remark", minWidth: 150, label: "说明" },
  { prop: "hospitalKey", minWidth: 150, label: "对照值" },
  { prop: "hospitalValue", minWidth: 150, label: "对照名称" },
  { prop: "operation", label: "操作", width: 160, fixed: "right" }
]

export const keyValidator = (_rule: any, value: string, callback: (err?: Error | string) => void) => {
  const keyReg = /^[^\u4e00-\u9fa5]+$/
  if (!value) callback()
  else if (keyReg.test(value)) callback()
  else callback(new Error("不支持中文格式"))
}

// 值映射表单配置
export const metaCodeDialogFormConfig: Array<DialogFormConfigItem> = [
  {
    type: "input",
    label: "值",
    prop: "key",
    rules: [
      { required: true, message: "请输入值", trigger: "blur" },
      { validator: keyValidator, trigger: "blur" }
    ]
  },
  {
    type: "input",
    label: "名称",
    prop: "value",
    rules: [{ required: true, message: "请输入名称", trigger: "blur" }]
  },
  // {
  //   type: "input",
  //   label: "说明",
  //   prop: "remark"
  // },
  {
    type: "input",
    label: "对照值",
    prop: "hospitalKey"
  },
  {
    type: "input",
    label: "对照名称",
    prop: "hospitalValue"
  }
]
export const rules: FormRules = {
  key: [
    { required: true, message: "请输入值", trigger: "blur" },
    { validator: keyValidator, trigger: "blur" }
  ],
  value: [{ required: true, message: "请输入名称", trigger: "blur" }]
}

// 值域代码表单配置
export const classifyDialogConfig: Array<DialogFormConfigItem> = [
  {
    type: "input",
    label: "值域代码",
    prop: "code",
    rules: [{ required: true, validator: variableCodeValidator, trigger: "blur" }]
  },
  {
    type: "input",
    label: "值域名称",
    prop: "name",
    rules: [{ required: true, message: "请输入值域名称", trigger: "blur" }]
  }
]

/* ======================== 首页元数据 ======================== */

export const homeMetaTableColumns: Array<TableColumnItem> = [
  { prop: "checked", type: "selection", width: 50, must: true },
  { prop: "collectFieldName", label: "字段名", minWidth: 100, must: true },
  { prop: "collectItem", minWidth: 120, label: "数据项" },
  { prop: "collectValueTypeDesc", minWidth: 100, label: "数据类型" },
  { prop: "collectLength", minWidth: 100, label: "长度" },
  { prop: "requiredDesc", label: "是否必填", minWidth: 120, sortable: true },
  { prop: "remark", label: "备注", minWidth: 180, sortable: true },
  { prop: "collectValueFormat", label: "取值范围", minWidth: 120, sortable: true },
  { prop: "status", label: "是否启用", minWidth: 100 },
  { prop: "operation", label: "操作", width: 200, fixed: "right" }
]

export const fieldRangeColumns: Array<TableColumnItem> = [
  { prop: "key", label: "值", minWidth: 100, sortable: false },
  { prop: "value", minWidth: 200, label: "值含义", width: 200 },
  { prop: "description", minWidth: 200, label: "说明" }
]

export const transformValueType = (valueType: string) => {
  switch (valueType) {
    case "STRING":
      return "字符"
    case "NUMBER":
      return "数字"
    case "DATA":
    case "DATE":
      return "日期"
    case "DATA_TIME":
    case "DATE_TIME":
      return "日期时间"
    case "COLLECTIONS":
      return "集合"
    default:
      return "--"
  }
}

export const homeMetaFormConfig: Array<DialogFormConfigItem> = [
  {
    type: "input",
    label: "字段名",
    prop: "collectFieldName",
    rules: [{ required: true, message: "请输入字段名", trigger: "blur" }]
  },
  {
    type: "input",
    label: "数据项",
    prop: "collectItem",
    rules: [{ required: true, message: "请输入数据项", trigger: "blur" }]
  },
  {
    type: "select",
    label: "数据类型",
    prop: "collectValueType",
    options: [
      { value: "STRING", label: "字符" },
      { value: "NUMBER", label: "数字" },
      { value: "DATE", label: "日期" },
      { value: "DATE_TIME", label: "日期时间" },
      { value: "COLLECTIONS", label: "集合" }
    ],
    rules: [{ required: true, message: "请选择数据类型", trigger: "blur" }]
  },
  {
    type: "select",
    label: "字段类型",
    prop: "standardType",
    options: [
      { value: "FIRST_PAGE", label: "首页字段" },
      { value: "HQMS", label: "HQMS字段" }
    ],
    rules: [{ required: true, message: "请选择字段类型", trigger: "blur" }]
  },
  {
    type: "number",
    label: "长度",
    prop: "collectLength",
    rules: [{ type: "number", max: 99, min: 1, message: "字段长度应在1到99之间" }]
  },
  {
    type: "radio",
    label: "是否必填",
    prop: "required",
    options: [
      { value: "YES", label: "是" },
      { value: "NO", label: "否" }
    ]
  },
  {
    type: "select",
    label: "取值范围",
    prop: "collectValueFormat",
    options: []
  },
  {
    type: "input",
    label: "备注",
    prop: "remark"
  }
]

/* ======================== 医保结算元数据 ======================== */
export const MedicalInsuranceMetaColumns: Array<TableColumnItem> = [
  { prop: "checked", type: "selection", width: 50, must: true },
  { prop: "name", label: "字段名", minWidth: 100, must: true },
  { prop: "title", minWidth: 120, label: "数据项" },
  { prop: "valueTypeDesc", minWidth: 100, label: "数据类型" },
  { prop: "length", minWidth: 100, label: "长度" },
  { prop: "requiredDesc", label: "是否必填", minWidth: 120, sortable: true },
  { prop: "remark", label: "备注", minWidth: 180, sortable: true },
  { prop: "valueRangeKey", label: "取值范围", minWidth: 120, sortable: true },
  { prop: "status", label: "是否启用", minWidth: 100 },
  { prop: "operation", label: "操作", width: 200, fixed: "right" }
]

export const medicalMetaFormConfig: Array<Record<string, any>> = [
  {
    type: "input",
    label: "字段名",
    prop: "name",
    rules: [{ required: true, message: "请输入字段名", trigger: "blur" }]
  },
  {
    type: "input",
    label: "数据项",
    prop: "title",
    rules: [{ required: true, message: "请输入数据项", trigger: "blur" }]
  },
  {
    type: "select",
    label: "数据类型",
    prop: "valueType",
    options: [
      { value: "STRING", label: "字符" },
      { value: "NUMBER", label: "数字" },
      { value: "DATE", label: "日期" },
      { value: "DATE_TIME", label: "日期时间" },
      { value: "COLLECTIONS", label: "集合" }
    ],
    rules: [{ required: true, message: "请选择数据类型", trigger: "blur" }]
  },
  {
    type: "number",
    label: "长度",
    prop: "length",
    rules: [{ type: "number", max: 99, min: 1, message: "字段长度应在1到99之间" }]
  },
  {
    type: "radio",
    label: "是否必填",
    prop: "required",
    options: [
      { value: true, label: "是" },
      { value: false, label: "否" }
    ]
  },
  {
    type: "select-api",
    label: "字段对照",
    prop: "refFirstPageName",
    options: [],
    rules: [{ required: false, message: "请选择", trigger: "blur" }]
  },
  {
    type: "select",
    label: "元数据分类",
    prop: "groupType",
    options: [
      { value: "BASE_INFO", label: "基本信息" },
      { value: "INPATIENT_INFO", label: "住院信息" },
      { value: "DIAGNOSIS_INFO", label: "出院诊断信息" },
      { value: "PROCEDURE_INFO", label: "操作信息" },
      { value: "FEE_INFO", label: "费用信息" },
      { value: "OCST_INFO", label: "门诊慢特病诊疗信息" },
      { value: "ICU_INFO", label: "重症监护信息" },
      { value: "BTI_INFO", label: "重症监护信息" }
    ],
    rules: [{ required: true, message: "请选择分组名称", trigger: "blur" }]
  },

  {
    type: "select",
    label: "取值范围",
    prop: "valueRangeKey",
    options: []
  },
  {
    type: "input",
    label: "备注",
    prop: "remark"
  }
]
