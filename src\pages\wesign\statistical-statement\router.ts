import { MenuOperationEnum } from "@/configs"
import type { RouteRecordRaw } from "vue-router"

const statisticalStatementRouter: RouteRecordRaw = {
  path: "/statistical-statement",
  redirect: "/statistical-statement/archival-statistics",
  name: "statisticalStatement",
  meta: {
    title: "统计报表",
    icon: "ri-line-chart-line",
    type: "multiple",
    showBreadcrumb: true
  },
  children: [
    {
      path: "/statistical-statement/archival-statistics",
      meta: {
        title: "归档率统计"
      },
      component: () => import("./archival-statistics/ArchivalStatistics.vue")
    },
    {
      path: "/statistical-statement/archival-statistics-detail",
      meta: {
        title: "归档明细",
        hideMenu: true
      },
      component: () => import("./archival-statistics/ArchivalDetail.vue")
    },
    {
      path: "/statistical-statement/paper-removal-statistics",
      redirect: "/statistical-statement/paper-removal-statistics/department",
      meta: {
        title: "脱纸率统计"
      },
      children: [
        {
          path: "/statistical-statement/paper-removal-statistics/department",
          meta: {
            title: "科室脱纸率"
          },
          component: () => import("./paper-removal-statistics/DepartmentPaperRemoval.vue")
        },
        {
          path: "/statistical-statement/paper-removal-statistics/writ",
          meta: {
            title: "文书脱纸率"
          },
          component: () => import("./paper-removal-statistics/WritPaperRemoval.vue")
        }
      ]
    },
    {
      path: "/statistical-statement/page-count-report",
      meta: {
        title: "病案页数报表",
        operations: [{ id: MenuOperationEnum.Export, name: "导出" }]
      },
      component: () => import("./page-count-report/index.vue")
    },
    {
      path: "/statistical-statement/print-export-report",
      meta: {
        title: "打印导出报表",
        operations: [{ id: MenuOperationEnum.Export, name: "导出" }]
      },
      component: () => import("./print-export-report/index.vue")
    },
    {
      path: "/statistical-statement/medical-quality-report",
      meta: {
        title: "病案质量报表"
      },
      component: () => import("./medical-quality-report/index.vue")
    }
  ]
}

export default statisticalStatementRouter
