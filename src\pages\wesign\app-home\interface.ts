import axios from "@/interfaces/axios-instance"

/**
 * @method GET
 * @desc   首页-获取代办统计数据
 */
export function getTodoStatisticsApi() {
  return axios({
    method: "get",
    url: `/api/document/statistics/todo`
  })
}

/**
 * @method GET
 * @desc   首页-获取科室统计数据（右侧列表数据）
 */
export function getDepartmentStatisticsApi(startDate: number, endDate: number) {
  return axios({
    method: "get",
    url: `/api/document/statistics/dept`,
    params: {
      startDate: startDate,
      endDate: endDate
    }
  })
}

interface IStatisticsParams {
  startDate: number
  endDate: number
  limit?: number | null
  orderType?: number
}

/**
 * @method GET
 * @desc   首页-获取所有病案统计数据（树状图数据）
 */
export function getAllStatisticsApi(params: IStatisticsParams) {
  return axios({
    method: "get",
    url: `/api/document/statistics/oper`,
    params: params
  })
}
