<template>
  <div class="quality-control-score">
    <div class="score-header">
      <span>病案缺陷列表</span>
      <AddButton v-if="props.isControl && editAble" @click="openAddDefect">添加缺陷</AddButton>
    </div>
    <!-- 病案缺陷表格 -->
    <el-table
      v-loading="scoreInfoState.loading"
      class="table-container"
      border
      :expand-row-keys="scoreInfoState.expandRemarkRows"
      :data="scoreInfoState.medicalScoreData"
      :header-cell-style="{ background: 'rgb(248,249,252)', color: '#030814' }"
      :row-key="row => row.wsid"
    >
      <el-table-column type="expand" :width="1">
        <template #default="{ row }">
          <div class="remark-container">
            <div class="remark-label" :style="{ lineHeight: props.isControl && !row.isSave ? '32px' : '19px' }">
              缺陷备注：
            </div>
            <el-input
              v-if="props.isControl && !row.isSave"
              v-model="row.remark"
              type="textarea"
              placeholder="请输入缺陷备注"
              resize="none"
              maxlength="250"
              show-word-limit
              :rows="2"
            />
            <div v-else class="remark-value">{{ row.remark }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="评分项" prop="name" width="fit-content" :show-overflow-tooltip="false">
        <template #default="{ row }">
          <i v-if="props.isControl && !row.isSave" class="ri-close-circle-line" @click="deleteDefect(row)"></i>
          <span>{{ row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="评分级别" prop="rejectLevel" :width="85" :show-overflow-tooltip="true">
        <template #default="{ row }">{{ getGradeLevel(row.rejectLevel) }}</template>
      </el-table-column>
      <el-table-column label="实际扣分" prop="factScore" :width="85" :show-overflow-tooltip="true">
        <template #default="{ row }">
          <el-input
            v-if="props.isControl && !row.isSave"
            v-model="row.factScore"
            type="number"
            :disabled="!row.isEdit"
            @input="row.factScore = row.factScore.match(/\d+\.?\d{0,1}/)?.[0]"
            @focus="oldValue = cloneDeep(row.factScore)"
            @change="changeDeduction(row)"
          />
          <div v-else>{{ row.factScore }}</div>
        </template>
      </el-table-column>

      <!-- 无数据样式 -->
      <template #empty>
        <EmptyContent />
      </template>
    </el-table>

    <!-- 评分/评级 -->
    <el-descriptions v-loading="calcLoading" class="score-footer">
      <el-descriptions-item v-if="scoreInfoState.actualScore" label="当前评分：">
        {{ scoreInfoState.actualScore }}
      </el-descriptions-item>
      <el-descriptions-item label="评级：">{{ scoreInfoState.gradeLevel }}</el-descriptions-item>
    </el-descriptions>

    <!-- 操作 -->
    <el-button
      v-if="props.isControl && editAble"
      :loading="saveLoading"
      class="save-btn"
      type="primary"
      @click="saveDefectScore"
    >
      保存评分
    </el-button>
  </div>

  <!-- 添加缺陷弹窗 -->
  <DialogContainer
    v-if="props.isControl"
    v-model:visible="addDefectState.visible"
    title="病案评分"
    :width="700"
    :confirm-callback="addTempDefect"
  >
    <div class="filter-action">
      <div class="filter-select">
        <div style="white-space: nowrap">病案评分类别：</div>
        <el-select v-model="addDefectState.search.ratingType" :clearable="true">
          <el-option v-for="item in filterDefectOptions" :key="item.wsid" :label="item.name" :value="item.wsid" />
        </el-select>
      </div>

      <el-input
        v-model="addDefectState.search.ratingSearch"
        placeholder="请输入搜索内容"
        :clearable="true"
        :prefix-icon="Search"
      />
    </div>
    <CommonTable
      ref="addDefectTableRef"
      class="filter-table"
      :table-columns="medicalRecordRatingColumns"
      :request-api="getAddDefectTableData"
      :request-params="addDefectTableParams"
      :pagination="false"
      :border="false"
      :selectable="addDefectRowSelectable"
    >
      <template #rejectLevel="{ row }">{{ getGradeLevel(row.rejectLevel) }}</template>
    </CommonTable>
  </DialogContainer>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, computed } from "vue"
import { useRoute } from "vue-router"
import { cloneDeep } from "lodash-es"
import { Search } from "@element-plus/icons-vue"
import { AddButton, CommonTable, DialogContainer, EmptyContent } from "@/base-components"
import { QualityControlTypeEnum } from "@/configs"
import {
  queryScoreStandard,
  getMedicalScoreApi,
  calculateDepartmentScoreApi,
  saveDepartmentScoreApi
} from "@/interfaces"
import { Message, toastError, flattenTree } from "@/utils"
import { medicalRecordRatingColumns, RejectLevelEnum } from "../../config"

const route = useRoute()

const props = defineProps({
  isControl: { type: Boolean, default: false },
  isFinalQc: { type: Boolean, default: false }
})

const scoreInfoState = reactive({
  medicalScoreData: [] as Array<Record<string, any>>,
  info: null as null | Record<string, any>,
  actualScore: 0,
  gradeLevel: "",
  loading: false,
  expandRemarkRows: [] as Array<string>
})

// 允许添加缺陷和保存评分
const editAble = computed(() => {
  return route.query.urlType === "FINAL_QC" ? props.isFinalQc : true
})

onMounted(() => {
  getMedicalRecordDefectData()
})
/* =================== 评分列表 =====================*/
const saveLoading = ref(false)
// 获取评分列表
function getMedicalRecordDefectData() {
  scoreInfoState.loading = true
  getMedicalScoreApi({
    inpNo: route.query.inpNo,
    urlType: !props.isFinalQc && route.query.urlType === "FINAL_QC" ? "DEPT_QC_MEDICAL" : route.query.urlType
  })
    .then(res => {
      const data = res.data.data

      scoreInfoState.medicalScoreData = data.gradeDeducts || []
      scoreInfoState.info = data
      scoreInfoState.actualScore = data?.actualScore
      scoreInfoState.gradeLevel = data?.gradeLevel

      let tempExpand = [] as Array<string>
      scoreInfoState.medicalScoreData.forEach(item => {
        if (item.remark) tempExpand.push(item.wsid)
        item.isSave = true
        item.isEdit = false
      })

      scoreInfoState.expandRemarkRows = tempExpand
    })
    .catch(err => {
      toastError(err, "获取评分列表失败")
    })
    .finally(() => {
      scoreInfoState.loading = false
    })
}

// 获取评分等级
function getGradeLevel(level) {
  return level === RejectLevelEnum.LEVEL_ONE
    ? "甲"
    : level === RejectLevelEnum.LEVEL_TWO
    ? "乙"
    : level === RejectLevelEnum.LEVEL_THREE
    ? "丙"
    : "--"
}

const oldValue = ref()
const calcLoading = ref(false)

// 获取实际可扣分数
function getCanAppend(row) {
  const flattenData = flattenTree(addDefectTableData.value)

  const parentNode = flattenData.find(item => item.wsid === row.parentWsid)
  const childrenNodes = scoreInfoState.medicalScoreData.filter(item => item.parentWsid === row.parentWsid)

  let canAddScore = parentNode ? Number(parentNode.score) : Number(row.score)
  childrenNodes.forEach(item => {
    if (item.wsid !== row.wsid) canAddScore -= Number(item.factScore)
  })
  return canAddScore
}

// 校验实际扣分
function validateDeduction(row) {
  if (row.factScore === undefined || row.factScore === null || row.factScore === "") {
    row.factScore = oldValue.value
    Message.warning("实际扣分不得为空")
    return false
  } else if (row.factScore < 0) {
    row.factScore = oldValue.value
    Message.warning("实际扣分不能小于0")
    return false
  } else if (row.factScore > row.totalScore) {
    row.factScore = oldValue.value
    Message.warning(`实际扣分不能超过最高扣分数：${row.totalScore}`)
    return false
  } else {
    let canAddScore = getCanAppend(row)
    if (row.factScore > canAddScore) {
      row.factScore = oldValue.value
      Message.warning(`实际扣分不能超过可扣分总数：${canAddScore}`)
      return false
    } else return true
  }
}

// 修改评分
function changeDeduction(row) {
  if (validateDeduction(row)) {
    calculateDepartmentScore(
      () => {
        oldValue.value = row.factScore
      },
      () => {
        row.factScore = oldValue.value
      }
    )
  }
}

// 保存评分
function saveDefectScore() {
  const data = [] as Array<Record<string, any>>
  scoreInfoState.medicalScoreData.forEach(item => {
    if (!item.isSave) data.push({ wsid: item.wsid, factScore: Number(item.factScore), remark: item.remark || "" })
  })
  saveLoading.value = true
  saveDepartmentScoreApi({ inpNo: route.query.inpNo, data: data, urlType: route.query.urlType })
    .then(res => {
      let notExpand = [] as Array<string>
      scoreInfoState.medicalScoreData.forEach(item => {
        if (!item.remark) notExpand.push(item.wsid)
        item.isSave = true
        item.isEdit = false
      })

      scoreInfoState.expandRemarkRows = scoreInfoState.expandRemarkRows.filter(item => !notExpand.includes(item))
      saveLoading.value = false
      Message.success("保存成功")
    })
    .catch(err => {
      saveLoading.value = false
      toastError(err, "保存失败")
    })
}

// 删除评分项
function deleteDefect(row) {
  const index = scoreInfoState.medicalScoreData.findIndex(item => item.wsid === row.wsid)
  if (index !== -1) scoreInfoState.medicalScoreData.splice(index, 1)
  scoreInfoState.expandRemarkRows = scoreInfoState.expandRemarkRows.filter(item => item !== row.wsid)

  // 重新计算分数等级
  if (scoreInfoState.medicalScoreData.length > 0) calculateDepartmentScore()
  else {
    scoreInfoState.actualScore = 0
    scoreInfoState.gradeLevel = ""
  }
}

function calculateDepartmentScore(callback = () => {}, errorBack = () => {}) {
  const data = [] as Array<Record<string, any>>
  scoreInfoState.medicalScoreData.forEach(item => {
    if (!item.isSave) data.push({ wsid: item.wsid, factScore: Number(item.factScore) })
  })

  calcLoading.value = true
  calculateDepartmentScoreApi({ inpNo: route.query.inpNo, urlType: route.query.urlType, data: data })
    .then(res => {
      scoreInfoState.actualScore = res.data.data.actualScore
      scoreInfoState.gradeLevel = res.data.data.gradeLevel
      callback()
    })
    .catch(err => {
      errorBack()
      toastError(err, "分数计算失败")
    })
    .finally(() => {
      calcLoading.value = false
    })
}

/* =================== 缺陷列表 =====================*/
const addDefectTableRef = ref()
const filterDefectOptions = ref<Array<Record<string, any>>>([])
const addDefectTableData = ref<Array<Record<string, any>>>([])

const addDefectTableParams = computed(() => {
  return {
    parentWsid: addDefectState.search.ratingType,
    name: addDefectState.search.ratingSearch
  }
})

// 获取添加缺陷的列表（需要处理外部缺陷列表中的选项）
function getAddDefectTableData(params) {
  return new Promise((resolve, reject) => {
    const scoreType = route.query.urlType !== QualityControlTypeEnum.FINAL_QC ? "DEPT_SCORE" : "FINAL_SCORE"
    const obj = { status: "ENABLE", scoreType: scoreType }
    queryScoreStandard(obj).then(res => {
      let data
      if (!res) return
      filterDefectOptions.value = res.filter(item => item.level === 1)
      addDefectTableData.value = res
      if (params.parentWsid) {
        data = formatAddDefectData(res, params.parentWsid, params.name, [])
      } else data = formatAddDefectData(res, "", params.name, [])

      // 设置初始化已选
      setTimeout(() => setInitSelected(data))
      resolve(data)
    })
  })
}

// 设置已选
function setInitSelected(data) {
  data.forEach(item => {
    const selectIndex = scoreInfoState.medicalScoreData.findIndex(
      (select: Record<string, any>) => select.wsid === item.wsid
    )
    if (selectIndex !== -1) addDefectTableRef.value.toggleRowSelection(item, true)
  })
}

// 添加缺陷表格数据格式化(最里面的子项)
function formatAddDefectData(data, parentWsid, name, result) {
  data.forEach(item => {
    if (item.children && item.children.length > 0) {
      // level === 1 判断筛选评分类别
      if ((item.level === 1 && item.wsid === parentWsid) || item.level > 1 || !parentWsid)
        formatAddDefectData(item.children, parentWsid, name, result)
    } else {
      // 判断筛选名称
      if (item.name.includes(name)) {
        result.push(item)
      }
    }
  })
  return result
}

// 外部缺陷列表中的选项默认不可选
function addDefectRowSelectable(row, index) {
  const isExit = scoreInfoState.medicalScoreData.findIndex((item: Record<string, any>) => item.wsid === row.wsid)
  if (isExit !== -1) row.disabledAdd = true
  return isExit === -1
}

/* =================== 添加缺陷弹窗 =====================*/
const addDefectState = reactive({
  visible: false,
  search: {
    ratingType: "",
    ratingSearch: ""
  }
})

// 打开添加缺陷弹窗
function openAddDefect() {
  addDefectState.search.ratingSearch = ""
  addDefectState.search.ratingType = ""
  addDefectState.visible = true
}

// 添加缺陷
function addTempDefect() {
  // 传输新添加的和没有保存过评分的
  const data = [] as Array<Record<string, any>>
  if (addDefectTableRef.value) {
    const selectedRows = addDefectTableRef.value.tableState.selectedRows
    // 本次弹窗新添加的
    selectedRows.forEach(item => {
      if (!item.disabledAdd) {
        let canAddScore = getCanAppend(item)
        if (item.score <= canAddScore) item.factScore = item.score
        else item.factScore = 0
        data.push({ wsid: item.wsid, factScore: item.factScore })
        scoreInfoState.expandRemarkRows.push(item.wsid)
      }
    })

    // 以前弹窗添加但是没保存评分的
    scoreInfoState.medicalScoreData.forEach(item => {
      if (!item.isSave) data.push({ wsid: item.wsid, factScore: item.factScore })
    })

    calcLoading.value = true
    calculateDepartmentScoreApi({ inpNo: route.query.inpNo, urlType: route.query.urlType, data: data })
      .then(res => {
        // 计算成功后把本次新添加的推入缺陷列表并置为未保存状态
        selectedRows.forEach(item => {
          if (!item.disabledAdd)
            scoreInfoState.medicalScoreData.push({
              ...item,
              factScore: item.factScore,
              isEdit: item.singleReject !== "YES",
              isSave: false
            })
        })

        scoreInfoState.actualScore = res.data.data.actualScore
        scoreInfoState.gradeLevel = res.data.data.gradeLevel
        Message.success("添加成功")
      })
      .catch(err => {
        selectedRows.forEach(item => {
          if (!item.disabledAdd)
            scoreInfoState.medicalScoreData.push({
              ...item,
              factScore: 0,
              isEdit: item.singleReject !== "YES",
              isSave: false
            })
        })
      })
      .finally(() => {
        addDefectState.visible = false
        calcLoading.value = false
      })
  }
}
</script>

<style lang="less" scoped>
.quality-control-score {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  row-gap: 16px;
  padding: 20px;
  box-sizing: border-box;
  .score-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
      font-size: 14px;
      font-weight: 600;
    }
  }
  .table-container {
    flex: 1;
    min-height: 0px;
    :deep(.el-input) {
      input[type="number"]::-webkit-inner-spin-button {
        -webkit-appearance: none !important;
      }
    }
    :deep(.el-scrollbar__view) {
      height: 100%;
    }
    :deep(.el-table__expand-column) {
      .cell {
        display: none;
      }
    }
    :deep(.el-table__expanded-cell) {
      &:hover {
        background: #f5f7fa !important;
      }
    }

    .ri-close-circle-line {
      cursor: pointer;
      color: #f56c6c;
      margin-right: 5px;
    }

    .remark-container {
      padding: 0px 12px;
      display: flex;
      .remark-label {
        white-space: nowrap;
      }
      .remark-value {
        word-break: break-all;
      }
    }
  }

  .score-footer {
    :deep(.el-descriptions__cell) {
      padding: 0px;
      .el-descriptions__label {
        margin-right: 0px;
        font-weight: 600;
      }
      .el-descriptions__content {
        font-size: 24px;
      }
    }
  }

  .save-btn {
    width: fit-content;
    align-self: flex-end;
  }
}

.filter-action {
  display: flex;
  column-gap: 10px;
  .filter-select {
    display: flex;
    align-items: center;
    .el-select {
      width: 120px !important;
    }
  }
}
.filter-table {
  margin-top: 12px;
  height: 400px;
}
</style>
