<template>
  <el-form-item :label="props.label" :prop="props.prop">
    <el-radio-group v-model="commonRadioValue" :disabled="props.disabled" @change="val => $emit('change', val)">
      <el-radio v-for="item in props.options" :key="String(item.value)" :label="item.value">
        {{ item.label }}
      </el-radio>
    </el-radio-group>
  </el-form-item>
</template>

<script setup lang="ts">
import { computed } from "vue"
import type { BaseOptionItem } from "@/types"

const props = withDefaults(
  defineProps<{
    label: string
    options: Array<BaseOptionItem>
    modelValue: string
    prop?: string
    disabled?: boolean
  }>(),
  { prop: "", disabled: false }
)

const emits = defineEmits(["update:modelValue", "change"])

const commonRadioValue = computed({
  get: () => props.modelValue,
  set: val => emits("update:modelValue", val)
})
</script>
