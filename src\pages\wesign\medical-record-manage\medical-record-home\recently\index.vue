<template>
  <PageContainer separate>
    <template #search>
      <Tabs />

      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
        />

        <DepartmentFormItem v-model:model-value="searchFormState.outHospitalDeptWsid" label="出院科室" />

        <CommonSelectFormItem
          v-model="searchFormState.status"
          label="病案状态"
          :options="globalOptionsStore.medicalRecordStatusOptions"
        />

        <DaterangeFormItem v-model:model-value="searchFormState.inHospitalDatetime" label="入院日期" />

        <DaterangeFormItem v-model:model-value="searchFormState.outHospitalDatetime" label="出院日期" />

        <CommonSelectFormItem
          v-model="searchFormState.isOutHospital"
          label="是否出院"
          :options="inHospitalOptions"
          @change="handleChangeIsInHospital"
        />

        <PatientLabelFormItem v-model="searchFormState.patientLabel" multiple />

        <el-form-item label="病案标记">
          <el-select v-model="medicalSymbolValue" clearable filterable multiple @change="handleSelectSymbol">
            <el-option v-for="item in symbolOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>

        <template #after-action>
          <el-badge style="margin-left: auto" :is-dot="advancedOptions ? true : false">
            <el-button
              style="margin-left: auto"
              :type="advancedOptions ? 'primary' : 'default'"
              @click="ComplexSearchRef?.openDrawer()"
            >
              高级搜索
            </el-button>
          </el-badge>
        </template>
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        v-if="displayMode === 'table'"
        ref="recordTableRef"
        table-id="recordTable"
        :table-columns="recordTableColumns"
        :request-api="getMedicalRecordListApi"
        :request-params="searchParams"
      >
        <template #header>
          <TooltipButton
            tooltip="切换为卡片展示模式"
            :icon="h('i', { class: 'ri-function-line', style: { 'font-size': '18px' } })"
            @click="toggleDisplayMode"
          ></TooltipButton>
          <BatchOperationButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Favorite)"
            type="warning"
            :disabled="!selectedRows.length"
            :icon="StarFilled"
            @click="collectDialogRef?.open()"
          >
            批量收藏
          </BatchOperationButton>

          <el-popover
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Export)"
            placement="bottom"
            width="210"
            trigger="hover"
          >
            <template #reference>
              <el-button :icon="Download">导出</el-button>
            </template>

            <PopoverButton
              :tip="`(当前选中${recordTableRef?.tableState.selectedRows.length}条数据)`"
              :center="false"
              @click="handleExportSelected"
            >
              导出选中
            </PopoverButton>
            <el-tooltip effect="dark" content="单次最大数据导出量10000条" placement="bottom">
              <PopoverButton
                :center="false"
                :tip="`(共${recordTableRef?.tableState.total}条数据)`"
                @click="handleExportAll"
              >
                导出全部
              </PopoverButton>
            </el-tooltip>
          </el-popover>
        </template>

        <!-- 业务详情 -->
        <template #businessDetails="{ row }">
          <div>
            <div class="business-tip" @click.stop="handleMedical(row)">
              <div class="business-tip-placeholder"></div>
              <img
                class="business-tip-icon"
                :style="{ filter: `drop-shadow(500px 0 ${bulbColor(row)})` }"
                src="@/assets/home/<USER>"
              />
            </div>
          </div>
        </template>

        <!-- 患者姓名 -->
        <template #patientName="{ row }">
          <div style="display: flex; align-items: center; flex-wrap: wrap">
            <el-button
              type="primary"
              link
              @click="handleRowClick(row, '/medical-record/detail', MenuOperationEnum.View)"
            >
              {{ row.patientName }}
            </el-button>

            <PatientLabelTag :row="row" />
          </div>
        </template>

        <!-- 病案状态 -->
        <template #statusEnumName="{ row }">
          <div @click.stop="handleMedical(row)">
            <MedicalRecordStatusTag
              style="cursor: pointer; border: none"
              :status="row.statusEnumName"
              :submit-status="row.submitStatusEnum"
            />
          </div>
        </template>

        <!-- 封存 -->
        <template #sealingStatus="{ row }">
          {{ sealingStatusOptions.find(option => option.value === row.sealingStatus)?.label }}
        </template>

        <!-- 特殊病案 -->
        <template #isSpecial="{ row }">
          <el-button
            v-if="row.isSpecialEnum === 'YES'"
            type="primary"
            link
            @click.stop="showSpecialDialog(row, 'check')"
          >
            是
          </el-button>
          <el-button v-else link @click.stop>否</el-button>
        </template>

        <!-- 缺失记录数量 -->
        <template #deletionCount="{ row }">
          <el-button v-if="row.deletionCount >= 1" type="primary" link @click.stop="showMissingDetail(row)">
            {{ row.deletionCount }}
          </el-button>
          <el-button v-else link @click.stop>{{ row.deletionCount }}</el-button>
        </template>

        <!-- 住院时间 -->
        <template #inHospitalDatetime="{ row }">
          {{ formatDatetime(row.inHospitalDatetime) }}
        </template>

        <!-- 出院时间 -->
        <template #outHospitalDatetime="{ row }">
          {{ formatDatetime(row.outHospitalDatetime) }}
        </template>

        <!-- 是否死亡 -->
        <template #death="{ row }">{{ row.death === 1 ? "是" : "否" }}</template>

        <template #operation="{ row }">
          <div style="display: flex">
            <div v-for="btn in operationBtnList.slice(0, 3)" :key="btn.key" @click.stop>
              <template v-if="btn.key === MenuOperationEnum.Submit">
                <TableButton
                  v-if="
                    hasOperationPermission(menuId, MenuOperationEnum.Submit) &&
                    row.submitStatusEnum === 'NOT_SUBMIT' &&
                    row.canSubmit
                  "
                  :disabled="isOperationDisabled(btn, row)"
                  :tooltip="getButtonTooltip(btn.key, row)"
                  @click="handleRowClick(row, btn.path, btn.key)"
                >
                  <span>提交</span>
                </TableButton>
              </template>
              <template v-else-if="btn.key === MenuOperationEnum.View">
                <TableButton
                  v-if="
                    hasOperationPermission(menuId, MenuOperationEnum.View) && row.submitStatusEnum === 'SUBMIT_SUCCESS'
                  "
                  :disabled="isOperationDisabled(btn, row)"
                  :tooltip="getButtonTooltip(btn.key, row)"
                  @click="handleRowClick(row, btn.path, btn.key)"
                >
                  <span>查看</span>
                </TableButton>
              </template>
              <template v-else>
                <TableButton
                  v-if="hasOperationPermission(menuId, btn.key)"
                  :disabled="isOperationDisabled(btn, row)"
                  :tooltip="getButtonTooltip(btn.key, row)"
                  @click="handleRowClick(row, btn.path, btn.key)"
                >
                  <span v-if="btn.key === MenuOperationEnum.Favorite">{{ row.isFavorite ? "取消收藏" : "收藏" }}</span>
                  <span v-else>{{ btn.label }}</span>
                </TableButton>
              </template>
            </div>

            <el-popover
              v-if="operationBtnList.length > 3"
              placement="bottom"
              :popper-style="{ minWidth: '80px', padding: '4px' }"
              :width="100"
              trigger="click"
            >
              <template #reference>
                <span @click.stop>
                  <TableButton>
                    更多
                    <el-icon style="font-size: 10px; margin-left: 2px"><ArrowDownBold /></el-icon>
                  </TableButton>
                </span>
              </template>

              <div v-for="btn in operationBtnList.slice(3, operationBtnList.length)" :key="btn.key">
                <PopoverButton
                  v-if="hasOperationPermission(menuId, btn.key) && showOperationButton(btn.key, row)"
                  :disabled="isOperationDisabled(btn, row)"
                  @click="handleRowClick(row, btn.path, btn.key)"
                >
                  <span v-if="btn.key === MenuOperationEnum.Favorite">{{ row.isFavorite ? "取消收藏" : "收藏" }}</span>
                  <span v-else>{{ btn.label }}</span>
                </PopoverButton>
              </div>
            </el-popover>
          </div>
        </template>
      </CommonTable>

      <PatientCardList
        v-else
        ref="patientCardListRef"
        :request-params="searchParams"
        :request-api="getMedicalRecordListApi"
        @to-detail="handleRowClick"
        @handle-sync="handleSync"
      >
        <template #header>
          <TooltipButton
            tooltip="切换为表格展示模式"
            :icon="h('i', { class: 'ri-list-check', style: { 'font-size': '18px' } })"
            @click="toggleDisplayMode"
          ></TooltipButton>
        </template>

        <template #statusEnumName="{ row }">
          <MedicalRecordStatusTag
            style="cursor: pointer; border: none; background-color: unset"
            :status="row.statusEnumName"
            :submit-status="row.submitStatusEnum"
            @click="handleMedical(row)"
          />
        </template>

        <!-- 卡片列表操作按钮 -->
        <template #operation="{ row }">
          <div style="display: flex">
            <div v-for="btn in operationBtnList.slice(0, 3)" :key="btn.key">
              <template v-if="btn.key === MenuOperationEnum.Submit">
                <TableButton
                  v-if="
                    hasOperationPermission(menuId, MenuOperationEnum.Submit) &&
                    row.submitStatusEnum === 'NOT_SUBMIT' &&
                    row.canSubmit
                  "
                  :disabled="isOperationDisabled(btn, row)"
                  :tooltip="getButtonTooltip(btn.key, row)"
                  @click="handleRowClick(row, btn.path, btn.key)"
                >
                  <span>提交</span>
                </TableButton>
              </template>
              <template v-else-if="btn.key === MenuOperationEnum.View">
                <TableButton
                  v-if="
                    hasOperationPermission(menuId, MenuOperationEnum.View) && row.submitStatusEnum === 'SUBMIT_SUCCESS'
                  "
                  :disabled="isOperationDisabled(btn, row)"
                  :tooltip="getButtonTooltip(btn.key, row)"
                  @click="handleRowClick(row, btn.path, btn.key)"
                >
                  <span>查看</span>
                </TableButton>
              </template>
              <template v-else>
                <TableButton
                  v-if="hasOperationPermission(menuId, btn.key)"
                  :disabled="isOperationDisabled(btn, row)"
                  :tooltip="getButtonTooltip(btn.key, row)"
                  @click="handleRowClick(row, btn.path, btn.key)"
                >
                  <span v-if="btn.key === MenuOperationEnum.Favorite">{{ row.isFavorite ? "取消收藏" : "收藏" }}</span>
                  <span v-else>{{ btn.label }}</span>
                </TableButton>
              </template>
            </div>

            <el-popover
              v-if="operationBtnList.length > 3"
              placement="bottom"
              :popper-style="{ minWidth: '80px', padding: '4px' }"
              :width="100"
              trigger="click"
            >
              <template #reference>
                <span @click.stop>
                  <TableButton>
                    更多
                    <el-icon style="font-size: 10px; margin-left: 2px"><ArrowDownBold /></el-icon>
                  </TableButton>
                </span>
              </template>

              <div v-for="btn in operationBtnList.slice(3, operationBtnList.length)" :key="btn.key">
                <PopoverButton
                  v-if="hasOperationPermission(menuId, btn.key) && showOperationButton(btn.key, row)"
                  :disabled="isOperationDisabled(btn, row)"
                  @click="handleRowClick(row, btn.path, btn.key)"
                >
                  <span v-if="btn.key === MenuOperationEnum.Favorite">{{ row.isFavorite ? "取消收藏" : "收藏" }}</span>
                  <span v-else>{{ btn.label }}</span>
                </PopoverButton>
              </div>
            </el-popover>
          </div>
        </template>
      </PatientCardList>
    </template>

    <!-- 导出弹窗 -->
    <ExportDialog
      ref="exportDialogRef"
      :total="recordTableRef?.tableState.total || 0"
      :selected-rows="selectedRows"
      :filters="searchParams.filters"
      :export-all="exportAll"
    />

    <!-- 收藏弹窗 -->
    <CollectDialog
      ref="collectDialogRef"
      :table-ref="recordTableRef"
      :selected-rows="selectedRows"
      :selected-row="operationState.selectedRow"
    />

    <!-- 封存检查弹窗 -->
    <SealDialog ref="sealDialogRef" :selected-row="operationState.selectedRow" :confirm-callback="confirmOperation" />

    <!-- 特殊病案弹窗 -->
    <SpecialDialog
      ref="specialDialogRef"
      :selected-row="operationState.selectedRow"
      @success="recordTableRef?.refreshTableData"
    />

    <!-- 缺失登记弹窗 -->
    <MissingReportDialog
      ref="missingReportDialogRef"
      :get-record-type="getRecordType"
      :selected-row="operationState.selectedRow"
      @success="recordTableRef?.refreshTableData"
    />

    <!-- 缺失详情弹窗 -->
    <MissingDetailDialog
      ref="missingDetailDialogRef"
      :get-record-type="getRecordType"
      :selected-row="operationState.selectedRow"
      @success="recordTableRef?.refreshTableData"
    />

    <!-- 提交进度 -->
    <SubmitProgressDialog ref="progressDialogRef" :inp-no="operationState.selectedRow?.inpNo" />

    <!-- 同步弹窗 -->
    <SyncDialog
      ref="syncDialogRef"
      :inp-no="operationState.selectedRow?.inpNo"
      @success="recordTableRef?.refreshTableData"
    />

    <!-- 示踪抽屉 -->
    <TraceDrawer
      ref="traceDrawerRef"
      :inp-no="operationState.selectedRow?.inpNo"
      @check-submit="showSubmitHistoryDialog"
    />

    <!-- 高级搜索 -->
    <ComplexSearch
      ref="ComplexSearchRef"
      filter-type="EMR_QEURY"
      :disabled-options-prop="disabledOptions"
      :default-active-template-id="defaultActiveTemplateID"
      :default-advanced-options="defaultAdvancedOptions"
      @search="handleQuery"
      @reset="handleReset"
    />

    <!-- 提交历史记录弹窗 -->
    <SubmitHistoryDialog ref="submitHistoryDialogRef" />

    <!-- 业务详情的抽屉 -->
    <BusinessDetailDrawer ref="businessDetailDrawerRef" />
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref, computed, nextTick, onBeforeMount, h } from "vue"
import { useRouter } from "vue-router"
import { cloneDeep } from "lodash-es"
import { StarFilled, ArrowDownBold, Download } from "@element-plus/icons-vue"
import {
  SearchContainer,
  CommonTable,
  PatientFilterFormItem,
  DepartmentFormItem,
  DaterangeFormItem,
  BatchOperationButton,
  TableButton,
  MedicalRecordStatusTag,
  PopoverButton,
  PageContainer,
  CommonSelectFormItem,
  TooltipButton,
  PatientLabelTag,
  PatientLabelFormItem
} from "@/base-components"
import { SealDialog, SyncDialog, ComplexSearch } from "@/page-components"
import SubmitProgressDialog from "../../components/SubmitProgressDialog.vue"
import { cancelCollectApi, getSubmitProgressApi } from "../../interface"
import {
  CollectDialog,
  ExportDialog,
  SpecialDialog,
  MissingReportDialog,
  MissingDetailDialog,
  TraceDrawer,
  SubmitHistoryDialog,
  PatientCardList,
  BusinessDetailDrawer,
  Tabs
} from "../components"
import {
  recordTableColumns,
  operationBtnConfig,
  sealingStatusOptions,
  inHospitalOptions,
  symbolOptions,
  menuId
} from "../config"
import { getSubmitConfigApi, getMedicalRecordListApi } from "../interface"
import { MedicalRecordStatusEnum, MenuOperationEnum } from "@/configs"
import { useCommonOptions } from "@/hooks"
import { permissionCheckApi, getAllDocTypes } from "@/interfaces"
import { useGlobalOptionsStore, useSystemStore, useUserStore } from "@/stores"
import { encryptStr, Message, SystemPrompt, formatDatetime, toastError, SystemAlert } from "@/utils"

const router = useRouter()
const systemStore = useSystemStore()
const { hasOperationPermission } = useUserStore()
const globalOptionsStore = useGlobalOptionsStore()

// 病案类型数据
const { getOptionLabel: getRecordType } = useCommonOptions({
  getOptionsApi: getAllDocTypes,
  labelAlias: "mrClassName",
  valueAlias: "mrClassCode"
})

/* ================ 高级筛选 ============== */
const ComplexSearchRef = ref()
const disabledOptions = ["a11", "a48", "b15", "b16c"]

/* ================ 操作按钮 ============== */

const operationBtnList = computed(() => {
  return operationBtnConfig.filter(item => hasOperationPermission(menuId, item.key))
})

// 检查操作按钮是否禁用
const isOperationDisabled = (config, row) => {
  // 同步
  if (
    config.key === MenuOperationEnum.Sync &&
    row.statusEnumName !== MedicalRecordStatusEnum.COLLECTING &&
    row.statusEnumName !== MedicalRecordStatusEnum.REPAIR
  ) {
    return true
  }
  // 提交
  if (
    config.key === MenuOperationEnum.Submit &&
    ((row.submitStatusEnum !== "NOT_SUBMIT" && row.submitStatusEnum !== "SUBMIT_FAIL") || !row.outHospitalDatetime)
  ) {
    return true
  }
  return false
}

// 是否展示操作按钮
const showOperationButton = (key, row) => {
  if (key === MenuOperationEnum.MissingReport && row.deletion === "NO") return false
  return true
}

// 操作按钮提示
const getButtonTooltip = (key, row) => {
  // 同步
  if (
    key === MenuOperationEnum.Sync &&
    row.statusEnumName !== MedicalRecordStatusEnum.COLLECTING &&
    row.statusEnumName !== MedicalRecordStatusEnum.REPAIR
  ) {
    return "当前状态不允许同步"
  }
  // 提交
  if (
    key === MenuOperationEnum.Submit &&
    ((row.submitStatusEnum !== "NOT_SUBMIT" && row.submitStatusEnum !== "SUBMIT_FAIL") || !row.outHospitalDatetime)
  ) {
    if (row.submitStatusEnum === "SUBMIT_SUCCESS") return "该病案已提交成功"
    if (row.submitStatusEnum === "SUBMIT_ING") return "该病案正在提交中"
    return "该病案无法提交"
  }
  return ""
}

/*===================== 表格展示方式 =====================*/

const displayMode = ref<"table" | "card">("table")

// 切换数据展示方式
const toggleDisplayMode = () => {
  displayMode.value = displayMode.value === "table" ? "card" : "table"
}

/*===================== 查询 =====================*/

const recordTableRef = ref<InstanceType<typeof CommonTable>>()
const selectedRows = computed(() => recordTableRef.value?.tableState?.selectedRows ?? [])
const archiveDateEnd = new Date().setHours(23, 59, 59, 999)
// const archiveDateStart = new Date(new Date().setMonth(new Date().getMonth() - 6)).setHours(0, 0, 0, 0)
const archiveDateStart = new Date().setHours(0, 0, 0, 0) - 3600 * 1000 * 24 * 7

const initFormValue = {
  patientFilterValue: "",
  patientFilterProp: "patientName",
  outHospitalDeptWsid: "", // 出院科室
  status: 0 as "" | number, // 病案状态
  inHospitalDatetime: "", // 入院日期
  outHospitalDatetime: [archiveDateStart, archiveDateEnd] as [number, number] | "", // 出院日期
  sealingStatus: "", // 封存状态
  isLateFiling: false, // 迟归标记
  isSealing: false, // 封存标记
  isSpecial: false, //特殊标记
  isRepair: false, // 返修标记 YES | NO
  isInHospital: false, //在院患者
  isOutHospital: "", //出院患者
  patientLabel: [] //患者标签
}
const searchFormState = reactive(cloneDeep(initFormValue))

const searchParams = reactive({
  filters: `outHospitalDatetime>=${archiveDateStart},outHospitalDatetime<=${archiveDateEnd}`,
  orFieldName: "",
  timestamp: 0
})

const defaultActiveTemplateID = ref("")
const defaultAdvancedOptions = ref([])
// 选择是否出院
const handleChangeIsInHospital = val => {
  if (typeof val === "boolean") searchFormState.isInHospital = !val
  if (searchFormState.isInHospital) searchFormState.outHospitalDatetime = ""
}

// 选择病案标记
const medicalSymbolValue = ref<Array<string>>([])
const handleSelectSymbol = val => {
  searchFormState.isLateFiling = false
  searchFormState.isRepair = false
  searchFormState.isSealing = false
  searchFormState.isSpecial = false
  val.forEach(item => {
    searchFormState[item] = true
  })
}

onBeforeMount(() => {
  const status = (router.currentRoute.value.query?.status as string) || ""
  if (status) {
    searchFormState.status = Number(status)
    searchFormState.outHospitalDatetime = ""
    searchParams.filters = `status=${status}`
    saveParams()
    router.replace("/medical-record/list")
  } else {
    const pageParams = systemStore.pageParams.find(item => item.id === location.pathname)
    //病案标记列表
    const medicalSymbolValueList = symbolOptions.map(item => item.value)
    for (const key in pageParams?.searchFilterForm) {
      searchFormState[key] = pageParams?.searchFilterForm[key]
      // 回显已勾选的病案标记
      if (medicalSymbolValueList.includes(key) && pageParams?.searchFilterForm[key]) {
        medicalSymbolValue.value.push(key)
      }
    }
    for (const key in pageParams?.searchParams) {
      searchParams[key] = pageParams?.searchParams[key]
    }
    defaultActiveTemplateID.value = pageParams?.activeTemplateID || ""
    defaultAdvancedOptions.value = pageParams?.advancedOptions || []
  }
  isInit.value = true
})

const isInit = ref(true)
const advancedOptions = computed(() => {
  let haveFilter = false
  if (isInit.value && (defaultActiveTemplateID.value || defaultAdvancedOptions.value.length > 0)) {
    haveFilter = true
  }
  if (ComplexSearchRef.value?.advancedFilters.filters) {
    haveFilter = true
  }
  return haveFilter
})

// 搜索
function handleQuery() {
  const filters: Array<string> = []
  if (searchFormState.patientFilterProp && searchFormState.patientFilterValue) {
    filters.push(`${searchFormState.patientFilterProp}=${searchFormState.patientFilterValue}`)
  }
  console.log(`output->searchFormState`, searchFormState)
  for (const key in searchFormState) {
    const value = searchFormState[key]
    // 复合字段跳过
    if (key === "patientFilterProp" || key === "patientFilterValue") {
      continue
    }
    // 日期范围字段
    else if (Array.isArray(value) && value.length === 2 && key.includes("Datetime")) {
      filters.push(`${key}>=${value[0]}`, `${key}<=${value[1] + (1000 * 60 * 60 * 24 - 1)}`)
    } else if (typeof value === "boolean" && value) {
      filters.push(`${key}=YES`)
    }
    // 患者标签
    else if (key === "patientLabel") {
      value.length && filters.push(`${key}=${value.join("|")}`)
    }
    // 其他基础字段
    else if (value || value === 0) {
      if (Array.isArray(value)) {
        if (value.length) filters.push(`${key}=${value.join("#")}`)
      } else {
        filters.push(`${key}=${value}`)
      }
    }
  }

  // 获取复杂查询的筛选值
  ComplexSearchRef.value?.getAdvancedFilters()
  if (ComplexSearchRef.value?.advancedFilters.filters)
    filters.push(`${ComplexSearchRef.value?.advancedFilters.filters}`)
  searchParams.filters = filters.join(",")
  searchParams.orFieldName = ComplexSearchRef.value?.advancedFilters.orFieldName || ""
  searchParams.timestamp = new Date().getTime()
  saveParams()
}

// 重置
function handleReset() {
  for (let key in searchFormState) {
    if (key === "patientFilterProp") searchFormState[key] = "patientName"
    else searchFormState[key] = initFormValue[key]
  }
  searchParams.filters = `outHospitalDatetime>=${archiveDateStart},outHospitalDatetime<=${archiveDateEnd},status=0`
  searchParams.orFieldName = ""
  searchParams.timestamp = new Date().getTime()
  // 清除复杂查询的筛选值
  ComplexSearchRef.value?.resetValue()
  medicalSymbolValue.value = []
  saveParams()
}

function saveParams() {
  isInit.value = false

  systemStore.setPageParams({
    id: location.pathname,
    searchParams: searchParams,
    searchFilterForm: searchFormState,
    activeTemplateID: ComplexSearchRef.value?.activeTemplateID || "",
    advancedOptions: ComplexSearchRef.value?.advancedOptions || []
  })
}

// 灯泡颜色
const bulbColor = row => {
  return globalOptionsStore.medicalRecordStatusOptions.find(item => item.statusEnumName === row.statusEnumName)?.color
}

/*===================== 行操作相关 =====================*/

const operationState = reactive({
  selectedRow: {} as Record<string, any>,
  routerPath: "",
  key: ""
})

const sealDialogRef = ref() // SealDialog组件ref

// 点击行的操作
const handleRowClick = (row: Record<string, any>, routerPath: string, operationKey: string) => {
  operationState.selectedRow = row
  operationState.routerPath = routerPath
  operationState.key = operationKey
  // 收藏/取消收藏
  if (operationKey === MenuOperationEnum.Favorite) {
    handleCollectSingle()
  }
  // 提交
  else if (operationKey === MenuOperationEnum.Submit) {
    handleSubmit()
  }
  // 同步
  else if (operationKey === MenuOperationEnum.Sync) {
    handleSync()
  }
  // 特殊病案
  else if (operationKey === MenuOperationEnum.Special) {
    showSpecialDialog(row, "edit")
  }
  // 示踪
  else if (operationKey === MenuOperationEnum.Trace) {
    nextTick(() => traceDrawerRef.value?.show())
  } else if (operationKey === MenuOperationEnum.Capture) {
    nextTick(() => {
      localStorage.setItem("PATIENT_INFO", JSON.stringify(row))

      window.open(`/scan-system`)
    })
  } else if (operationKey === MenuOperationEnum.MissingReport) {
    nextTick(() => missingReportDialogRef.value?.show())
  } else {
    permissionCheckApi(row?.inpNo).then(res => {
      if (res.data.data) {
        sealDialogRef.value.checkSealed()
      } else {
        Message.warning("无当前操作权限")
      }
    })
  }
}

// 检查封存成功之后的回调操作
const confirmOperation = () => {
  const query = { inpNo: operationState.selectedRow?.inpNo }
  if (sealDialogRef.value.secretKey) {
    query["secretKey"] = encryptStr(sealDialogRef.value.secretKey)
  }
  router.push({
    path: operationState.routerPath,
    query: query
  })
}

/*===================== 收藏 =====================*/

const collectDialogRef = ref()

// 收藏单条
const handleCollectSingle = () => {
  // 已收藏 - 取消收藏
  if (operationState.selectedRow?.isFavorite) {
    handleCancelCollect()
  }
  // 未收藏 - 收藏
  else {
    collectDialogRef.value.open()
  }
}

// 取消收藏
const handleCancelCollect = () => {
  SystemPrompt("是否取消收藏？").then(() => {
    cancelCollectApi([operationState.selectedRow?.wsid])
      .then(() => {
        recordTableRef.value?.refreshTableData()
        Message.success("操作成功")
      })
      .catch(err => toastError(err))
  })
}

/*===================== 导出 =====================*/

const exportDialogRef = ref()
const exportAll = ref(false)

// 导出选中
const handleExportSelected = () => {
  if (!selectedRows.value.length) return Message.warning("请至少勾选一条数据")
  exportAll.value = false
  exportDialogRef.value.open()
}

// 导出所有
const handleExportAll = () => {
  if (!recordTableRef.value?.tableState.total) {
    return Message.warning("没有数据可以导出")
  }
  exportAll.value = true
  exportDialogRef.value.open()
}

/*===================== 同步 =====================*/

const syncDialogRef = ref<InstanceType<typeof SyncDialog>>()

// 同步病案
const handleSync = () => {
  if (operationState.selectedRow.submitStatusEnum === "SUBMIT_SUCCESS") {
    return Message.warning("该病案已提交成功，无法同步")
  }
  if (operationState.selectedRow.submitStatusEnum === "SUBMIT_ING") {
    return Message.warning("该病案正在提交中，无法同步")
  }
  systemStore.showLoading()
  permissionCheckApi(operationState.selectedRow?.inpNo)
    .then(res => {
      systemStore.hideLoading()
      if (res.data.data) {
        nextTick(() => syncDialogRef.value?.show())
      } else {
        Message.warning("无当前操作权限")
      }
    })
    .catch(error => {
      systemStore.hideLoading()
      toastError(error)
    })
}

/* ======================== 提交 ======================== */

const progressDialogRef = ref<InstanceType<typeof SubmitProgressDialog>>()

let query = "" // 跳转到提交详情的参数

// 生成跳转参数
const generateQuery = (deletionSubmit: "YES" | "NO") => {
  const _query = `inpNo=${operationState.selectedRow?.inpNo}&patientName=${operationState.selectedRow?.patientName}&lastSyncTime=${operationState.selectedRow?.syncDatetime}&allowMissing=${deletionSubmit}&mrNo=${operationState.selectedRow?.mrNo}&outHospitalDeptName=${operationState.selectedRow?.outHospitalDeptName}&inHospitalDatetime=${operationState.selectedRow?.inHospitalDatetime}&outHospitalDatetime=${operationState.selectedRow?.outHospitalDatetime}`
  if (sealDialogRef.value?.secretKey) {
    query + `&secretKey=${encryptStr(sealDialogRef.value.secretKey)}`
  }
  query = _query
}

// 检查病案是否提交
const handleSubmit = async () => {
  try {
    systemStore.showLoading()
    const { needSign, deletionSubmit } = (await getSubmitConfigApi(operationState.selectedRow?.wsid)).data.data
    generateQuery(deletionSubmit)
    systemStore.hideLoading()
    // 根据配置跳转不同的页面 - 是否需要签名
    if (needSign === "YES") {
      checkSubmissionProgress()
    } else {
      query += "&needSign=NO"
      router.push(`/medical-submit?${query}&returnUrl=/medical-record/list`)
    }
  } catch (error: any) {
    toastError(error, "初始化配置失败")
    systemStore.hideLoading()
  }
}

// 检查病案是否提交
const checkSubmissionProgress = () => {
  getSubmitProgressApi(operationState.selectedRow?.inpNo)
    .then(res => {
      // completeFlag 1未提交；2提交中；3提交完成
      const { completeFlag } = res.data.data
      // 提交成功
      if (completeFlag === 2) {
        SystemAlert("该病案已提交成功，无需再次提交")
      }
      // 已经提交但在提交中
      else if (completeFlag === 1) {
        progressDialogRef.value?.init(res.data.data.submitCount, res.data.data.total)
      }
      // 正常提交流程
      else {
        query += "&needSign=YES"
        // router.push(`/medical-record/submit-sign?${query}`)
        router.push(`/medical-submit?${query}&returnUrl=/medical-record/list`)
      }
    })
    .catch(error => {
      toastError(error)
    })
}

/*===================== 特殊病案 =====================*/

const specialDialogRef = ref<InstanceType<typeof SpecialDialog>>()

const showSpecialDialog = (row, type) => {
  operationState.selectedRow = row
  nextTick(() => specialDialogRef.value?.show(type))
}

/*===================== 缺失登记 =====================*/

const missingReportDialogRef = ref<InstanceType<typeof MissingReportDialog>>()

/*===================== 缺失详情 =====================*/

const missingDetailDialogRef = ref<InstanceType<typeof MissingDetailDialog>>()

const showMissingDetail = row => {
  operationState.selectedRow = row
  nextTick(() => missingDetailDialogRef.value?.show())
}

/*===================== 示踪 =====================*/

const traceDrawerRef = ref<InstanceType<typeof TraceDrawer>>()

/*===================== 提交版本记录 =====================*/

const submitHistoryDialogRef = ref<InstanceType<typeof SubmitHistoryDialog>>()

const showSubmitHistoryDialog = action => {
  traceDrawerRef.value?.close()
  submitHistoryDialogRef.value?.show(action)
}

/*============================ 业务详情 ============================*/

const businessDetailDrawerRef = ref<InstanceType<typeof BusinessDetailDrawer>>()

const handleMedical = row => {
  businessDetailDrawerRef.value?.show(row)
}
</script>

<style lang="less" scoped>
.business-tip {
  position: relative;

  .business-tip-placeholder {
    width: 20px;
    height: 20px;
    cursor: pointer;
  }

  .business-tip-icon {
    transform: translateX(-500px) translateY(-50%);
    position: absolute;
    left: 0;
    top: 50%;
  }
}
</style>
