import type { SearchFormConfigItem } from "@/types"

export interface RecallFilesProps {
  documentWsid: string // 病案文档id
  mrClassCode: string // 文件分类code
  fileWsid: string // 文件id
  title: string // 文件title
  homePageFlag: string // 首页标识
}

export const recallCommonSearchFormConfig: Array<SearchFormConfigItem> = [
  { type: "patient", label: "患者信息" },
  { type: "daterange", label: "入院时间", prop: "inHospitalDatetime" },
  { type: "daterange", label: "出院时间", prop: "outHospitalDatetime" }
]
