/* ======================== 公共页面布局样式 ======================== */

.common-page-container {
  min-width: 980px;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgb(0 35 114 / 10%);
  box-sizing: border-box;
}

.common-search-container {
  min-width: 980px;
  height: 100%;
  display: flex;
  flex-direction: column;

  .common-search-header {
    padding: 10px 20px;
    margin-bottom: 20px;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgb(0 35 114 / 10%);
  }

  .common-search-content {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgb(0 35 114 / 10%);
  }
}

/* ======================== 公共元素样式 ======================== */

// 容器边框阴影
.common-box-shadow {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgb(0 35 114 / 10%);
  box-sizing: border-box;
}

// 单行文字省略展示
.common-text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* ======================== element plus组件自定义样式类名 ======================== */

// 不显示此刻的date-time-picker popper-class="not-show-now"
.not-show-now {
  .el-button.is-text {
    display: none;
  }
}

/* ======================== 全局覆盖element plus样式 ======================== */

.el-drawer__header {
  margin-bottom: 24px !important;
}

.el-drawer__body {
  border-top: 1px solid #ddd;
}

.el-button.is-plain {
  background-color: #fff;
}

.el-button.is-text {
  &:hover,
  &:focus {
    background-color: #fff !important;
  }
}

/* ======================== 全局覆盖ant design vue样式 ======================== */

.ant-spin-spinning {
  max-height: 2000px !important;
}

.ant-spin-text {
  text-shadow: none !important;
}

/* ======================== flex ======================== */

.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.flex-around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
