<template>
  <div>
    <el-drawer v-model="visible" :size="505" :teleported="false" title="高级筛选">
      <div id="advanced-search" class="advanced-search">
        <el-radio-group v-model="activeTab">
          <el-radio-button label="save" value="save">新的筛选</el-radio-button>
          <el-radio-button label="saved" value="saved">已保存</el-radio-button>
        </el-radio-group>

        <div v-show="activeTab === 'save'" class="complex-search-container">
          <SearchList
            ref="SearchListRef"
            v-model:options="advancedOptions"
            :disabled-options-prop="disabledOptionsProp"
            :filter-type="props.filterType"
          >
            <template #actions>
              <el-button type="primary" @click="() => search()">查询</el-button>
              <el-button @click="openCreateDialog">存为模板</el-button>
              <el-button @click="resetFilter">重置</el-button>
            </template>
          </SearchList>
        </div>
        <div v-show="activeTab === 'saved'" class="complex-search-container">
          <FilterTemplate
            ref="FilterTemplateRef"
            :filter-type="props.filterType"
            :active-template-i-d="activeTemplateID"
            @search="search"
            @reset="emits('reset')"
          />
        </div>
      </div>
    </el-drawer>

    <DialogContainer
      v-model:visible="createVisible"
      title="存为筛选模板"
      :confirm-loading="createLoading"
      :confirm-callback="createTemplate"
    >
      <el-form ref="createFormRef" :model="{ templateName }">
        <el-form-item prop="templateName" :rules="createRule" label="模板名称">
          <el-input v-model.trim="templateName" placeholder="请输入模板名称" />
        </el-form-item>
      </el-form>
    </DialogContainer>
  </div>
</template>

<script setup lang="ts">
import { nextTick, onMounted, ref, watch } from "vue"
import { DialogContainer } from "@/base-components"
import { QueryTermTypeEnum } from "@/configs"
import { createFilterTemplateApi } from "@/interfaces"
import { FilterType } from "@/types"
import { Message, toastError } from "@/utils"
import FilterTemplate from "./FilterTemplate.vue"
import SearchList from "./SearchList.vue"

const activeTab = ref("save")

interface PropsType {
  filterType: FilterType
  disabledOptionsProp?: Array<string>
  defaultActiveTemplateId?: string
  defaultAdvancedOptions?: Array<Record<string, any>>
}
const props = withDefaults(defineProps<PropsType>(), {
  disabledOptionsProp: () => {
    return []
  },
  defaultActiveTemplateId: "",
  defaultAdvancedOptions: () => {
    return []
  }
})

const FilterTemplateRef = ref()
const SearchListRef = ref()

const visible = ref(false)

function openDrawer() {
  if (activeTemplateID.value) activeTab.value = "saved"
  else activeTab.value = "save"
  visible.value = true
}

watch(
  () => activeTab.value,
  val => {
    if (val === "saved") {
      nextTick(() => {
        FilterTemplateRef.value?.refreshData()
      })
    }
  },
  { immediate: true }
)

const emits = defineEmits(["search", "reset"])

onMounted(() => {
  if (props.defaultActiveTemplateId) activeTemplateID.value = props.defaultActiveTemplateId
  if (props.defaultAdvancedOptions) advancedOptions.value = props.defaultAdvancedOptions
})

/* ======================== 高级筛选条件 ======================== */
const advancedOptions = ref<Array<Record<string, any>>>([])

/* ======================== 模板操作 ======================== */
function checkEmptyItem(item) {
  if (item.value === null || item.value === undefined || item.value === "") return true
  else if (item.value && (JSON.stringify(item.value) === "{}" || JSON.stringify(item.value) === "[]")) return true
}
function checkEmptyValue() {
  const isEmpty = advancedOptions.value.find(item => {
    if (checkEmptyItem(item)) return item
  })

  if (isEmpty) return true
  else return false
}
/* ================ 存储模板 ================ */
const createLoading = ref(false)
const createVisible = ref(false)
const templateName = ref("")
const createFormRef = ref()
const createRule = [
  { required: true, message: "请输入模板名称", trigger: "blur" },
  { max: 20, message: "模板名称不可超过20字符", trigger: "blur" }
]
// 打开存储弹窗
function openCreateDialog() {
  if (advancedOptions.value.length <= 0) return Message.warning("请添加检索项")
  if (checkEmptyValue()) return Message.warning("检索项的值不可为空")
  if (!validateTemplate()) return Message.warning("检索项校验不通过，请检查输入的值")
  createVisible.value = true
  templateName.value = ""
  createFormRef.value?.resetField()
}
// 存储模板
function createTemplate() {
  createFormRef.value?.validate(valid => {
    if (!valid) return
    createLoading.value = true
    const data = {
      name: templateName.value,
      module: props.filterType,
      filters: JSON.stringify(advancedOptions.value)
    }

    createFilterTemplateApi(data)
      .then(res => {
        createLoading.value = false
        Message.success("模板存储成功")
        createVisible.value = false
        FilterTemplateRef.value?.refreshData()
      })
      .catch(err => {
        createLoading.value = false
        toastError(err, "模板存储失败")
      })
  })
}

/* ================ 重置筛选条件 ================ */
function resetFilter() {
  advancedOptions.value = []
  emits("reset")
}

/* ================ 筛选查询 ================ */
const activeTemplateID = ref("")
const activeTemplate = ref([])
const advancedFilters = ref({
  filters: "",
  orFieldName: ""
})

function validateTemplate() {
  const options = !activeTemplateID.value ? advancedOptions.value : activeTemplate.value
  for (let i = 0; i < options.length; i++) {
    if (options[i].error) return false
  }
  return true
}

function search(options = [], id = "") {
  activeTemplateID.value = id
  activeTemplate.value = options
  if (id) {
    advancedOptions.value = []
  }
  if (!validateTemplate()) return
  emits("search")
}

// 获取筛选值
function getAdvancedFilters() {
  let filters: Array<string> = []
  let orFieldName: Array<string> = []
  const options = !activeTemplateID.value ? advancedOptions.value : activeTemplate.value
  options.forEach((item, index) => {
    if (!checkEmptyItem(item)) {
      if (item.type === QueryTermTypeEnum.Daterange)
        filters.push(
          `${item.prop}>=${new Date(item.value[0]).setHours(0, 0, 0, 0)},${item.prop}<=${new Date(
            item.value[1]
          ).setHours(23, 59, 59, 999)}`
        )
      else if (item.type === QueryTermTypeEnum.CountRange) {
        if (item.value.from) filters.push(`${item.prop}>=${item.value.from}`)
        if (item.value.to) {
          filters.push(`${item.prop}<=${item.value.to}`)
        }
      } else filters.push(`${item.prop}=${item.value}`)
      if (item.logic === "OR") {
        orFieldName.push(options[index].prop)
      }
    }
  })

  advancedFilters.value.filters = filters.join(",")
  advancedFilters.value.orFieldName = orFieldName.join(",")
}

function resetValue() {
  if (activeTemplateID.value) activeTemplateID.value = ""
  else advancedOptions.value = []
  advancedFilters.value.filters = ""
  advancedFilters.value.orFieldName = ""
}

defineExpose({ openDrawer, advancedFilters, resetValue, getAdvancedFilters, advancedOptions, activeTemplateID })
</script>

<style lang="less" scoped>
:deep(.el-drawer) {
  .advanced-search {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    .complex-search-container {
      width: 100%;
      margin-top: 20px;
      flex: 1;
      min-height: 0px;

      .save-action-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .add-options-popover {
          padding: 0px !important;
          height: 400px !important;
          display: flex;
          flex-direction: column;
          .option-search-input {
            width: calc(100% - 50px);
            .el-input__wrapper {
              padding: 0px 0px 8px;
              box-shadow: none;
              height: 24px;
              line-height: 24px;
              .el-input__icon {
                margin-right: 12px;
              }
              .el-input__inner {
                height: 24px;
                line-height: 24px;
              }
            }
            border-bottom: 1px solid #dadfe7;
            margin: 15px 25px 5px;
          }
          .options-list {
            width: 100%;
            height: 100%;
            overflow: auto;
            margin-bottom: 25px;
            .option-item {
              padding: 6px 25px;
              box-sizing: border-box;
              cursor: pointer;
              color: #0a1633;
              display: flex;
              justify-content: space-between;
              align-items: center;

              div {
                flex: 1;
                min-width: 0px;
              }

              .ri-check-line {
                margin-left: 10px;
              }

              &:hover {
                background: #f0f4fb;
              }
            }

            .is-active {
              background: #f0f4fb;
              .ri-check-line {
                color: #409eff;
              }
            }
          }
        }
      }
    }
  }
}
</style>
