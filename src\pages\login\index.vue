<template>
  <Spin :spinning="isLogging" :tip="loggingText">
    <div class="login-wrapper">
      <div class="external-main-bg"></div>
      <div class="external-main-wrapper">
        <img class="login-logo" src="@/assets/png/logo.png" />
        <div class="login-sub-title">病案易管家</div>
        <div class="login-title-desc">安全可信智慧的电子病案管家</div>
        <div class="external-main">
          <el-radio-group v-model="activeTab">
            <el-radio-button label="account">账号登录</el-radio-button>
            <el-radio-button label="qrCode">二维码登录</el-radio-button>
          </el-radio-group>
          <!-- 账号密码登录 -->
          <el-form
            v-if="activeTab === 'account'"
            ref="ruleFormRef"
            class="external-content"
            :model="loginForm"
            :rules="loginFormRules"
          >
            <el-form-item prop="account" :error="errors.account">
              <el-input
                v-model="loginForm.account"
                size="large"
                class="external-content-input"
                placeholder="请输入用户名"
              >
                <template #prefix><i class="ri-account-box-line" style="font-size: 18px"></i></template>
              </el-input>
            </el-form-item>
            <el-form-item prop="password" :error="errors.password" class="form-password">
              <el-input
                v-model="loginForm.password"
                size="large"
                class="external-content-input"
                type="password"
                show-password
                placeholder="请输入登录密码"
                @keydown.enter="handleLogin"
              >
                <template #prefix><i class="ri-lock-line" style="font-size: 18px"></i></template>
              </el-input>
            </el-form-item>
            <el-form-item style="padding-left: 2px">
              <el-checkbox v-model="loginForm.rememberAccount" label="记住账号"></el-checkbox>
            </el-form-item>
            <el-form-item>
              <div class="login-btn" @click="handleLogin">登录</div>
            </el-form-item>
          </el-form>

          <!-- 二维码登录 -->
          <div v-if="activeTab === 'qrCode'" class="external-content">
            <div class="qr-code-container">
              <img v-if="!qrCodeError" :src="qrCode" class="login-qr-code" @click="getQrCode" />
              <div v-else class="login-qr-code login-qr-code-error" @click="getQrCode">
                {{ `${qrCodeError}\n(点击此处刷新二维码)` }}
              </div>
            </div>
            <div class="qr-code-hint">点击二维码刷新</div>
            <div class="qr-code-hint">请使用易云盾扫码登录</div>
          </div>
        </div>
      </div>
      <div class="external-footer">{{ copyrightData }}</div>
    </div>
  </Spin>
</template>

<script setup lang="ts">
import { reactive, h, ref, onMounted, watch, onBeforeUnmount } from "vue"
import { useRouter } from "vue-router"
import { ElNotification } from "element-plus"

import { Spin } from "ant-design-vue"
import { toDataURL } from "qrcode"
import { loginFormRules } from "./config"
import { loginApi, getLoginQrCode, checkLoginQrCode } from "./interface"
import type { FormInstance } from "element-plus"
import {
  querySystemCopyright,
  querySystemIcon,
  getUserPermissionsApi,
  getUserHospitalInfo,
  getHeaderMessageCountApi,
  getOperLogEnumApi
} from "@/interfaces"
import { useSystemStore, useUserStore, useResourceStore } from "@/stores"
import { Message, extractErrorMsg, toastError, getUrlBase64, SystemAlert } from "@/utils"

const resourceStore = useResourceStore()

const ruleFormRef = ref<FormInstance>()
const loginForm = reactive({
  account: "",
  password: "",
  rememberAccount: true
})
const isLogging = ref(false)
const loggingText = ref("正在登录...")
const errors = reactive({ account: "", password: "" })

const router = useRouter()
const userStore = useUserStore()
const systemStore = useSystemStore()

const copyrightData = ref("")

onMounted(() => {
  if (!localStorage.getItem("SessionWsid")) return
  getHeaderMessageCountApi().then(res => {
    return router.push({ path: userStore.userMenus[0]?.path ?? "/user-setting" }) // 自动登录
  })

  if (userStore.rememberAccount) loginForm.account = userStore.account

  // 获取版权信息
  querySystemCopyright().then(res => {
    copyrightData.value = res.data.data || "版本号： v2.3.0.0 四川易企签科技版权所有"
  })
})

// account:账号登录  qrCode:二维码登录
const activeTab = ref<"account" | "qrCode">("account")
let timerId

watch(
  () => activeTab.value,
  val => {
    if (val === "qrCode") {
      getQrCode()
    } else {
      // 使用账号密码登录取消轮询检查
      clearInterval(timerId)
      timerId = ""
    }
  },
  { immediate: true }
)

onBeforeUnmount(() => {
  clearInterval(timerId)
})

/* =================== 二维码登录 =================== */
const qrCode = ref("")
const loginId = ref("")
const qrCodeError = ref("")

// 获取二维码
function getQrCode() {
  qrCodeError.value = ""
  getLoginQrCode()
    .then(res => {
      loginId.value = res.data.data.loginId
      const data = JSON.parse(res.data.data.qrRawData)
      data.loginId = res.data.data.loginId
      toDataURL(JSON.stringify(data)).then(url => {
        qrCode.value = url
      })
      if (!timerId) timerId = setInterval(checkQrcodeLogin, 1000)
    })
    .catch(err => {
      qrCodeError.value = "获取二维码失败"
      toastError(err, "获取二维码失败")
    })
}

// 检查二维码是否登录
function checkQrcodeLogin() {
  checkLoginQrCode(loginId.value)
    .then(res => {
      const resData = res.data.data

      // 未扫码不返回resData
      if (!resData) return

      // 扫码登录成功取消轮询
      clearInterval(timerId)
      timerId = ""
      // 获取用户信息
      isLogging.value = true
      getUserInfo(resData)
    })
    .catch(err => {
      qrCode.value = ""
      qrCodeError.value = extractErrorMsg(err)
      clearInterval(timerId)
      timerId = ""
      toastError(err, "二维码登录失败")
    })
}

/* =================== 账号登录 =================== */
// 登录
async function handleLogin() {
  // 验证登录表单
  try {
    await ruleFormRef.value?.validate()
  } catch (error) {
    return Message.error("请输入账户密码")
  }

  // 调用登录接口
  try {
    errors.account = ""
    errors.password = ""
    isLogging.value = true
    loggingText.value = "正在登录..."
    const resData = await loginApi({ userName: loginForm.account, password: loginForm.password })
    getUserInfo(resData)
  } catch (err: any) {
    const errMsg = extractErrorMsg(err, "登录失败")
    errors.account = errMsg
    errors.password = errMsg
    isLogging.value = false
  }
}
const availablePath = ref("")

async function getUserInfo(resData) {
  // 获取用户配置信息
  try {
    const { sessionId, passwordExpire } = resData
    localStorage.setItem("SessionWsid", sessionId)
    userStore.setUserInfo({ ...resData, ...loginForm })
    userStore.$patch({ passwordExpire })

    loggingText.value = "正在加载用户配置信息..."

    // 获取菜单权限树
    const res = await getUserPermissionsApi()
    const jsonData = res.data?.data ?? "[]"
    const userResourceConfig = JSON.parse(jsonData)
    userStore.$patch({ userMenus: userResourceConfig })
    if (!userResourceConfig.length) {
      localStorage.removeItem("SessionWsid")
      return SystemAlert("当前账号尚未配置权限，请联系管理员配置权限")
    }

    // 获取hospital信息
    const hospitalInfo = await getUserHospitalInfo()
    userStore.setUserHospitalInfo(hospitalInfo)

    // 每次登录获取最新配置的logo和favicon
    getIconSrc("favicon")
    getIconSrc("logo")

    // 跳转至首个有权限的菜单
    ElNotification({
      type: "success",
      title: userStore.userHospitalInfo.realName + "，您好",
      message: h("div", {}, [
        h("span", {}, "欢迎登录无纸化病案系统"),
        h("i", { class: "ri-sparkling-line", style: { fontSize: "18px", marginLeft: "4px", color: "#ffba08" } })
      ]),
      offset: 50
    })
    const operLogEnum = (await getOperLogEnumApi())?.data?.data
    systemStore.setOperLogEnum(operLogEnum)
    console.log(`output->userResourceConfig`, userResourceConfig)
    const availableGroup = userResourceConfig.find(item => item.level === 0 && item.children.length)
    console.log(`output->availableGroup`, availableGroup)
    const availablePath = availableGroup?.children?.[0]?.children?.[0]?.path || availableGroup?.children[0]?.path
    console.log(`output->availablePath`, availablePath)
    router.push({ path: availablePath ?? "/user-setting" })
  } catch (err: any) {
    toastError(err, "获取用户配置信息失败")
  } finally {
    isLogging.value = false
  }
}

// 获取远程icon
const getIconSrc = (type: "favicon" | "logo") => {
  querySystemIcon(type)
    .then(async res => {
      if (type === "favicon") {
        const faviconBase = await getUrlBase64(res)
        resourceStore.setFavicon(faviconBase)
      } else {
        const faviconBase = await getUrlBase64(res)
        resourceStore.setLogo(faviconBase)
      }
    })
    .catch(async err => {
      if (type === "favicon") {
        const faviconBase = ""
        resourceStore.setFavicon(faviconBase)
      } else {
        const faviconBase = ""
        resourceStore.setLogo(faviconBase)
      }
    })
}
</script>

<style lang="less" scoped>
.login-wrapper {
  position: relative;
  width: 100%;
  min-width: 980px;
  height: 100vh;
  min-height: 600px;
  background: url("@/assets/png/bg.png") no-repeat;
  background-size: auto 100%;
  background-color: rgb(56 96 244);
  font-weight: 500;
  text-align: center;

  .external-main-bg {
    position: absolute;
    top: 50%;
    transform: translate(0, -50%);
    right: 10%;
    z-index: 99;
    width: 450px;
    height: 550px;
    background: linear-gradient(164deg, #99cfff 0%, #acd8fe 23%, #eef5fb 48%, #fff 100%);
    border-radius: 4px;
    opacity: 0.8;
    box-shadow: 0 4px 13px 0 rgb(0 0 0 / 8%);
  }
  .external-main-wrapper {
    position: absolute;
    top: 50%;
    transform: translate(0, -50%);
    right: 10%;
    z-index: 99;
    width: 450px;
    height: 550px;
    display: flex;
    flex-direction: column;
    .login-logo {
      height: 68px;
      margin-top: 48px;
      object-fit: contain;
      flex-shrink: 0;
    }

    .external-main {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      row-gap: 16px;
      flex: 1;
      min-height: 0px;
      :deep(.el-radio-group) {
        padding: 5px;
      }
    }

    .login-sub-title {
      margin-top: 16px;
      font-size: 32px;
      color: #3860f4;
    }

    .login-title-desc {
      margin-top: 16px;
      font-size: 20px;
      color: rgb(56 96 244 / 56%);
    }
    .external-content {
      width: 380px;
      margin-top: 0px;
      height: 250px;
      :deep(.external-content-input) {
        .el-input__inner {
          height: 50px;
          line-height: 50px;
          font-size: 16px !important;
        }
      }
      .qr-code-container {
        width: 100%;
        display: flex;
        justify-content: center;
        .login-qr-code {
          width: 180px;
          height: 180px;
          background: rgba(0, 0, 0, 0.1);
          cursor: pointer;
          border: 1px solid #bbb;
        }
        .login-qr-code-error {
          display: flex;
          align-items: center;
          justify-content: center;
          color: #888;
          white-space: pre;
        }
      }

      .qr-code-hint {
        margin-top: 5px;
        color: #666;
      }
    }
    .login-btn {
      width: 100%;
      height: 50px;
      line-height: 50px;
      font-size: 24px;
      color: white;
      background-color: var(--el-color-primary);
      border-radius: 4px;
      cursor: pointer;
      &:hover {
        background: var(--el-color-primary-light-3);
      }
    }
  }

  .external-footer {
    position: absolute;
    bottom: 24px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 16px;
    color: rgb(255 255 255 / 53%);
  }
}

:deep(.form-password) {
  margin-bottom: 10px;
}

.el-input-group__append {
  color: #030814 !important;
  background: #fff !important;
}
</style>
