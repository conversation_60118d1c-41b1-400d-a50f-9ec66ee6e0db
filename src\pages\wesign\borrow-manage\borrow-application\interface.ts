import axios from "@/interfaces/axios-instance"

/**
 * @method GET
 * @desc   我的借阅-分页列表
 */
export function getBorrowApplicationListApi(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/document/complex-query/borrow`,
    params
  })
}

/**
 * @method GET
 * @desc   我的借阅-全文检索
 */
export function searchBorrowApplicationListApi(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/document/full-text/borrow`,
    params
  })
}

interface IBorrowApplyData {
  documentWsids: Array<string>
  timeLimit: number
  applyType: string
  applyReason: string
  viewPermission: string
  exportPermission: string
  printPermission: string
}

/**
 * @method POST
 * @desc   发起借阅申请
 */
export function handleBorrowApplyApi(data: IBorrowApplyData) {
  return axios({
    method: "post",
    url: `/api/document/borrow/apply`,
    data
  })
}
