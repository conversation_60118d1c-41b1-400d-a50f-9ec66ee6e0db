<template>
  <PageContainer separate class="archive-Finish-wrapper">
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchForm
        :form-config="archiveFinishedSearchFormConfig"
        :form-state="searchFormState"
        @query-btn-click="handleQuery"
        @reset-btn-click="handleReset"
      />
    </template>

    <template #table>
      <CommonTable
        ref="archivedTableRef"
        table-id="archivedTableIdent"
        :table-columns="archiveFinishedTableColumns"
        :request-api="getArchivedList"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <template #operation="{ row }">
          <TableButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.View)"
            @click="handleSupplementArchiving(row)"
          >
            补归
          </TableButton>

          <TableButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.View)"
            @click="handleRowClick(row, 'detail')"
          >
            查看
          </TableButton>
          <TableButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Print)"
            @click="handleRowClick(row, 'print')"
          >
            打印
          </TableButton>
        </template>
      </CommonTable>
    </template>

    <SealDialog ref="sealDialogRef" :selected-row="medicalRecord" :confirm-callback="confirmOperation"></SealDialog>

    <!-- 补归弹窗 -->
    <DialogContainer
      title="补充归档"
      :visible="supplementArchivingState.visible"
      :close-callback="closeSupplementArchiving"
    >
      <el-form
        ref="supplementArchivingRef"
        label-width="80px"
        :rules="archivePendingValidateRules"
        :model="supplementArchivingState.supplementArchivingForm"
      >
        <el-form-item label="报告类型" prop="reportType">
          <el-select v-model="supplementArchivingState.supplementArchivingForm.reportType">
            <el-option
              v-for="(type, index) in supplementArchivingState.reportTypeOptions"
              :key="index"
              :label="type.className"
              :value="type.classCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="报告名称" prop="reportName">
          <el-select v-model="supplementArchivingState.supplementArchivingForm.reportName">
            <el-option
              v-for="(item, index) in supplementArchivingState.reportNameOptions"
              :key="index"
              :label="item.name"
              :value="item.wsid"
            >
              {{ item.name }}
            </el-option>
          </el-select>
        </el-form-item>
        <el-upload
          v-model:file-list="supplementArchivingState.supplementArchivingForm.reportDocument"
          action=""
          accept=".jpg,.jpeg,.png"
          list-type="picture-card"
          :multiple="true"
          :auto-upload="false"
        >
          <el-icon><Plus /></el-icon>
          <template #file="{ file }">
            <div class="upload-file">
              <div class="file-type">
                <img class="thumb_image" :src="file.url" alt="" />
              </div>

              <div class="upload-hover">
                <el-icon @click="previewFile(file)"><ZoomIn /></el-icon>
                <el-icon @click="deleteFile(file)"><Delete /></el-icon>
              </div>
            </div>
          </template>
        </el-upload>
      </el-form>

      <template #footer>
        <div style="display: flex">
          <el-button
            type="primary"
            :loading="CameraDialogRef?.cameraLoading"
            @click="() => CameraDialogRef?.openDialog()"
          >
            拍摄
          </el-button>
          <div style="flex: 1; min-width: 0px">
            <el-button @click="closeSupplementArchiving">取消</el-button>
            <el-button type="primary" @click="submitSupplementArchiving">确定</el-button>
          </div>
        </div>
      </template>
    </DialogContainer>

    <!-- 拍摄弹窗 -->
    <CameraDialog ref="CameraDialogRef" @submit="takePhoto" />

    <!-- 上传图片预览 -->
    <div v-if="previewedFileUrl" class="previewed-file-container">
      <el-icon class="close-previewed-file" @click="previewedFileUrl = ''"><Close /></el-icon>
      <img :src="previewedFileUrl" />
    </div>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref, watch } from "vue"
import { useRouter } from "vue-router"
import printJS from "print-js"
import { PageContainer, CommonTable, TableButton, DialogContainer } from "@/base-components"
import { SearchForm, TabsRouter, SealDialog, CameraDialog } from "@/page-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { downloadFileApi } from "@/interfaces"
import { useUserStore, useSystemStore } from "@/stores"
import { formatDatetime, Message, toastError } from "@/utils"
import {
  archiveFinishedTableColumns,
  tabsRouterList,
  menuId,
  archiveFinishedSearchFormConfig,
  archivePendingValidateRules
} from "./config"
import { getArchivedList, getSupplementArchiveDataApi, addSupplementArchiveApi } from "./interface"
import type { FormInstance } from "element-plus"
const systemStore = useSystemStore()

const router = useRouter()

const { hasOperationPermission } = useUserStore()

const CameraDialogRef = ref()

/* ======================== 搜索相关 ======================== */

const searchFormState = reactive({
  patientFilterProp: "",
  patientFilterValue: "",
  outHospitalDeptWsid: "",
  outHospitalDatetime: "",
  visitIdSymbol: "",
  visitIdCount: "",
  archiveDatetime: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/* ======================== 表格相关 ======================== */

const archivedTableRef = ref<InstanceType<typeof CommonTable>>()

function dataCallback(data: Array<Record<string, any>>) {
  return data.map(item => ({
    ...item,
    inHospitalDatetime: formatDatetime(item.inHospitalDatetime),
    outHospitalDatetime: formatDatetime(item.outHospitalDatetime),
    archiveDatetime: formatDatetime(item.archiveDatetime),
    ifOverdue: item.ifOverdue === "ENABLE" ? "是" : "否"
  }))
}

/* ======================== 检查封存 ======================== */
type OperationType = "print" | "detail" | "export" | ""

// 输入封存代码之后进行的操作类型 - 打印/去详情
const operationType = ref<OperationType>("")
const medicalRecord = ref<Record<string, any> | null>(null) // 用户点击提交的病案信息
const sealDialogRef = ref() // SealDialog组件ref

// 检查是否封存
const handleRowClick = (row: any, type) => {
  medicalRecord.value = row
  operationType.value = type
  sealDialogRef.value.checkSealed()
}

// 查看详情
const toDetail = () => {
  router.push({ path: "/archive/local/version", query: { inpNo: medicalRecord.value?.inpNo } })
}

const confirmOperation = () => {
  if (operationType.value === "print") {
    const requestParams = {
      inpNo: medicalRecord.value?.inpNo,
      type: "COMPOSE",
      source: "ARCHIVE_PRINT",
      sealKey: sealDialogRef.value?.secretKey || "" // 有sealKey则带入
    }
    systemStore.showLoading("加载中")
    downloadFileApi(requestParams)
      .then(res => {
        systemStore.hideLoading()
        const data = URL.createObjectURL(new Blob([res.data], { type: "application/pdf" }))
        printJS(data)
      })
      .catch(err => {
        const decoder = new TextDecoder("utf-8")
        err.response.data = JSON.parse(decoder.decode(err.response.data))
        console.log(`output->err`, err)
        systemStore.hideLoading()
        toastError(err, "操作失败")
      })
  } else if (operationType.value === "detail") {
    toDetail()
  }
}

/* ==========================================补充归档=============================*/

const supplementArchivingRef = ref<FormInstance>()

interface ReportTypeProps {
  //分类编码
  classCode: string
  //分类名称
  className: string
  // 文件名称
  fileName: string[]
  // 登记编号
  registerNumber: string
  [key: string]: any
}

const supplementArchivingState = reactive({
  visible: false,
  supplementArchivingForm: {
    reportType: "",
    reportName: "",
    reportDocument: [] as Array<any>
  },
  inpNo: "",
  reportTypeOptions: [] as Array<ReportTypeProps>,
  // 报告名称数据源（依赖于报告类型）
  reportNameOptions: [] as Array<any>
})

// 报告名称数据源（依赖于报告类型）
watch(
  () => supplementArchivingState.supplementArchivingForm.reportType,
  () => {
    supplementArchivingState.supplementArchivingForm.reportName = ""
    supplementArchivingState.reportNameOptions = supplementArchivingState.reportTypeOptions.filter(
      item => item.classCode === supplementArchivingState.supplementArchivingForm.reportType
    )[0]?.registerNumberItem
  },
  {
    immediate: true
  }
)

//获取补充归档分类数据源
const getSupplementArchiveData = inpNo => {
  if (!inpNo) return
  getSupplementArchiveDataApi(inpNo)
    .then(res => {
      supplementArchivingState.reportTypeOptions = res.data.data
      if (res.data.data.length === 0) {
        Message.warning("该病案未进行登记，无法进行补充归档")
        return
      }
      supplementArchivingState.visible = true
      supplementArchivingState.inpNo = inpNo
    })
    .catch(err => {
      toastError(err, "获取数据失败")
      supplementArchivingState.reportTypeOptions = []
    })
}

//打开补充归档提交弹窗
const handleSupplementArchiving = (row: Record<string, any>) => {
  getSupplementArchiveData(row.inpNo)
}

//关闭补充归档提交弹窗
const closeSupplementArchiving = () => {
  supplementArchivingState.visible = false
  supplementArchivingState.supplementArchivingForm.reportType = ""
  supplementArchivingState.supplementArchivingForm.reportName = ""
  supplementArchivingState.supplementArchivingForm.reportDocument = []
  supplementArchivingRef.value?.resetFields()
}

function takePhoto(files) {
  supplementArchivingState.supplementArchivingForm.reportDocument =
    supplementArchivingState.supplementArchivingForm.reportDocument.concat(files)
  CameraDialogRef.value?.closeDialog()
}

//补充归档提交
const submitSupplementArchiving = async () => {
  if (!supplementArchivingRef.value) return
  await supplementArchivingRef.value?.validate(valid => {
    if (!valid) return
    const params = {
      inpNo: supplementArchivingState.inpNo,
      fileName: supplementArchivingState.supplementArchivingForm.reportType,
      registerNumber: supplementArchivingState.supplementArchivingForm.reportName,
      file: supplementArchivingState.supplementArchivingForm.reportDocument
    }
    addSupplementArchiveApi(params)
      .then(() => {
        Message.success("补充归档成功")
        closeSupplementArchiving()
      })
      .catch(err => {
        toastError(err, "补充归档失败")
      })
  })
}

/*======================上传的文件==========================*/

// 预览的文件url
const previewedFileUrl = ref("")

// 预览文件
const previewFile = (file: any) => {
  previewedFileUrl.value = file.url
}

// 删除文件
const deleteFile = (file: any) => {
  const index = supplementArchivingState.supplementArchivingForm.reportDocument.findIndex(
    (item: any) => item.uid === file.uid
  )
  supplementArchivingState.supplementArchivingForm.reportDocument.splice(index, 1)
}
</script>

<style scoped lang="less">
.archive-Finish-wrapper {
  :deep(.el-upload-list__item) {
    width: 100px;
    height: 100px;
  }
  :deep(.el-upload--picture-card) {
    width: 100px;
    height: 100px;
  }
  .upload-file {
    position: relative;
    width: 100%;
    .file-type {
      padding: 5px;
      height: 100%;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: 100%;
        object-fit: contain;
      }
    }

    .upload-hover {
      position: absolute;
      width: 100px;
      height: 100px;
      top: 0px;
      left: 0px;
      opacity: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      column-gap: 5px;
      color: #fff;
      font-size: 24px;
      &:hover {
        opacity: 1;
        background: rgba(0, 0, 0, 0.2);
      }
      .el-icon {
        cursor: pointer;
      }
    }

    .upload-status {
      position: absolute;
      top: 5px;
      right: 5px;
      font-size: 14px;
    }
  }
  .previewed-file-container {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.2);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    .close-previewed-file {
      position: absolute;
      right: 20px;
      top: 20px;
      font-size: 28px;
      color: #fff;
      cursor: pointer;
    }
    img {
      width: 65%;
      height: 65%;
      object-fit: contain;
    }
  }
}
</style>
