<template>
  <DialogContainer
    v-model:visible="visible"
    :title="'审批详情'"
    :width="900"
    :no-footer="approvalInfo.status !== 'APPLICATION'"
    :cancel-callback="reset"
    :confirm-callback="confirmApproval"
  >
    <div class="patient-info">
      <div class="patient-info-title">患者信息</div>

      <el-descriptions :column="1">
        <el-descriptions-item label="姓名：">{{ approvalInfo?.patientName }}</el-descriptions-item>
        <el-descriptions-item label="身份证号：">{{ approvalInfo?.patientIdCard }}</el-descriptions-item>
      </el-descriptions>

      <div class="images-list">
        <div v-for="(key, index) in approvalInfo?.patientImageKeys" :key="index" class="image">
          <template v-if="approvalInfo?.authImageFiles[key]">
            <div class="image-wrapper">
              <el-image
                :src="patientSrcList[index]"
                :zoom-rate="1.2"
                :preview-src-list="patientSrcList"
                :initial-index="index"
                fit="fill"
                :preview-teleported="true"
              />
            </div>
            <div class="image-title">{{ getImageTypeText(key) }}</div>
          </template>
        </div>
      </div>
    </div>

    <div v-if="['AGENT', 'GUARDIAN'].includes(approvalInfo?.relationship)" class="agent-info">
      <div class="agent-info-title">{{ approvalInfo?.relationshipStr }}信息</div>

      <el-descriptions :column="1">
        <el-descriptions-item :label="`${approvalInfo?.relationshipStr}姓名：`">
          {{ approvalInfo?.agentName }}
        </el-descriptions-item>
        <el-descriptions-item :label="`${approvalInfo?.relationshipStr}身份证号：`">
          {{ approvalInfo?.agentIdCard }}
        </el-descriptions-item>
      </el-descriptions>

      <div class="images-list">
        <div v-for="(key, index) in approvalInfo?.agentImageKeys" :key="index" class="image">
          <template v-if="approvalInfo?.authImageFiles[key]">
            <div class="image-wrapper">
              <el-image
                :src="agentSrcList[index]"
                :zoom-rate="1.2"
                :preview-src-list="agentSrcList"
                :initial-index="index"
                fit="fill"
                :preview-teleported="true"
              />
            </div>
            <div class="image-title">{{ getImageTypeText(key) }}</div>
          </template>
        </div>
      </div>
    </div>

    <el-form
      v-if="approvalInfo.status === 'APPLICATION'"
      :label-width="100"
      label-position="right"
      style="margin-top: 20px"
    >
      <el-form-item label="审批结果：">
        <el-radio-group v-model="approvalInfo.approvalStatus">
          <el-radio :label="true">通过</el-radio>
          <el-radio :label="false">不通过</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="!approvalInfo.approvalStatus" label="不通过原因：">
        <el-radio-group v-model="approvalInfo.reason">
          <el-radio v-for="(option, index) in auditReasonOptions" :key="index" :label="option.value">
            <span style="max-width: 100%; word-wrap: break-word; white-space: wrap">
              {{ option.value }}
            </span>
          </el-radio>
          <el-radio key="other" label="其他"></el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="approvalInfo.reason === '其他'" label=" " required>
        <el-input
          v-model="customReason"
          type="textarea"
          placeholder="请输入不通过原因"
          style="max-width: 400px"
        ></el-input>
      </el-form-item>
    </el-form>

    <el-descriptions v-else :column="1" style="margin-top: 20px">
      <el-descriptions-item label="审批结果：">
        {{ approvalInfo.status === "APPROVE_REFUSE" ? "已驳回" : "已通过" }}
      </el-descriptions-item>
      <el-descriptions-item v-if="approvalInfo?.status === 'APPROVE_REFUSE'" label="驳回原因：">
        {{ approvalInfo?.approverReason }}
      </el-descriptions-item>
      <el-descriptions-item label="审批时间：">
        {{ formatDatetime(approvalInfo?.approverDatetime) }}
      </el-descriptions-item>
      <el-descriptions-item label="审批人：">
        {{ approvalInfo?.approverName || userStore?.userHospitalInfo?.realName }}
      </el-descriptions-item>
    </el-descriptions>
  </DialogContainer>
</template>

<script setup lang="ts">
import { nextTick, onMounted, ref } from "vue"
import { DialogContainer } from "@/base-components"
import { approvePrintApplyApi, getPrintApplyDetailApi } from "../../interface"
import { getImage, getApproveReasonApi } from "@/interfaces"
import { useSystemStore, useUserStore } from "@/stores"
import { Message, formatDatetime, toastError } from "@/utils"
const userStore = useUserStore()

const systemStore = useSystemStore()

const emits = defineEmits(["success"])

/* ============= 基础信息 ============= */

const auditReasonOptions = ref<any[]>([])

onMounted(() => {
  getApprovalReasonOptions()
})

// 获取审批不通过的原因
const getApprovalReasonOptions = () => {
  getApproveReasonApi({ groupKey: "PRINT_AUDIT_REFUSE" }).then(res => {
    auditReasonOptions.value = res
  })
}

/* ============= 审批信息获取 ============= */

const patientSrcList = ref<any[]>([])
const agentSrcList = ref<any[]>([])
const customReason = ref("")

const approvalInfo = ref<Record<string, any>>({
  approvalStatus: true
})

// 获取审批信息
const getApprovalInfo = (wsid: string) => {
  systemStore.showLoading()
  show()
  getPrintApplyDetailApi(wsid)
    .then(res => {
      approvalInfo.value = res.data.data
      approvalInfo.value.approvalStatus = true
      if (res.data.data.authimageFiles) {
        // 认证图片
        const authImageFiles = JSON.parse(res.data.data.authimageFiles)
        approvalInfo.value.authImageFiles = authImageFiles
        console.log(approvalInfo.value.authImageFiles)
        // 患者
        const patientImageKeys = ["patientIdCardFront", "patientIdCardReverse", "patientHandIdCard"]
        approvalInfo.value.patientImageKeys = patientImageKeys
        console.log(approvalInfo.value.patientImageKeys)
        // 代理人
        approvalInfo.value.agentImageKeys = Object.keys(authImageFiles).filter(key => !patientImageKeys.includes(key))

        // 图片src推入列表
        Object.keys(authImageFiles).forEach(key => {
          getImage(authImageFiles[key]).then(res => {
            if (patientImageKeys.includes(key)) {
              patientSrcList.value.push(res)
            } else {
              agentSrcList.value.push(res)
            }
          })
        })
      }
      systemStore.hideLoading()
      nextTick(() => show())
    })
    .catch(error => {
      systemStore.hideLoading()
      console.log(error)
      toastError(error)
    })
}

/* ================ 审批 =============== */

// 确认审批
const confirmApproval = () => {
  if (approvalInfo.value?.approvalStatus === false) {
    if (!approvalInfo.value?.reason) {
      Message.warning("请选择不通过原因")
      return
    }
  }
  if (approvalInfo.value?.reason === "其他" && !customReason.value?.length) {
    Message.warning("请输入审核不通过原因")
    return
  }
  const rejectReason = approvalInfo.value?.reason === "其他" ? customReason.value : approvalInfo.value?.reason
  approvePrintApplyApi({
    wsid: approvalInfo.value?.wsid,
    status: approvalInfo.value?.approvalStatus,
    reason: rejectReason || ""
  })
    .then(() => {
      Message.success("操作成功")
      reset()
      emits("success")
    })
    .catch(error => {
      toastError(error)
    })
}

/* ============== 其他 ============== */

// 获取图片类型
const getImageTypeText = (key: string) => {
  switch (key) {
    case "patientIdCardFront":
      return "患者身份证人像面"
    case "patientIdCardReverse":
      return "患者身份证反面"
    case "patientHandIdCard":
      return "患者手持身份证"
    case "agentIdCardFront":
      return "代理人身份证人像面"
    case "agentIdCardReverse":
      return "代理人身份证反面"
    case "patientAgentPowerOfAttorney":
      return "患者及代理人手持身份证和委托书正面照"
    case "powerOfAttorney":
      return "患者和代理人签名的委托书"
    case "birthProof":
      return "患者出生证明"
    case "householdRegister":
      return "户口本患者页"
    case "guardianIdCardFront":
      return "监护人身份证人像面"
    case "guardianIdCardReverse":
      return "监护人身份证反面"
    case "dischargeCertificate":
      return "患者出院证明或者出院结账发票"
    case "guardianHandBirthProof":
      return "监护人手持身份证正面及患者出生证明"
  }
}

/* ============== dialog ============== */

const visible = ref(false)

// 重置审批数据
const reset = () => {
  close()
  approvalInfo.value = {}
  customReason.value = ""
}

const show = () => {
  visible.value = true
}

const close = () => {
  visible.value = false
}

defineExpose({ show, close, getApprovalInfo })
</script>

<style lang="less" scoped>
.patient-info-title,
.agent-info-title {
  padding-bottom: 20px;
  display: flex;
  align-items: center;
  line-height: 1;

  &::before {
    content: "";
    display: block;
    width: 4px;
    height: 16px;
    background: #0c7ffc;
    border-radius: 0 4px 4px 0;
    margin-right: 10px;
  }
}

.agent-info {
  margin-top: 10px;
}

.images-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  column-gap: 20px;
  row-gap: 10px;

  .image-wrapper {
    position: relative;
    height: 140px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    border: 1px solid #e3e3e3;
    border-radius: 10px;
  }

  .image-title {
    text-align: center;
    margin-top: 5px;
  }
}

:deep(.el-radio) {
  height: auto;
  line-height: normal;
  margin-bottom: 10px;
  .el-radio__label {
    display: block !important;
    white-space: normal !important;
  }
}
</style>
