import { ref, watch } from "vue"
import type { Ref } from "vue"

const useRequestData = (requestApi: () => Promise<CommonObjectList>, dependencies: Array<Ref<any>> = []) => {
  const data = ref<CommonObjectList>([])

  watch(
    dependencies,
    () => {
      requestApi().then(resData => {
        data.value = resData
      })
    },
    {
      immediate: true,
      deep: true
    }
  )

  return data
}

export default useRequestData
