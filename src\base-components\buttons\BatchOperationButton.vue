<template>
  <el-tooltip :disabled="!$props.disabled" :content="$props.tooltip" placement="top">
    <el-button
      :type="$props.type"
      :plain="$props.plain"
      :disabled="$props.disabled"
      :icon="$props.icon"
      @click="$emit('click')"
    >
      <slot></slot>
    </el-button>
  </el-tooltip>
</template>

<script setup lang="ts">
import type { Component } from "vue"

withDefaults(
  defineProps<{
    tooltip?: string
    disabled?: boolean
    icon?: string | Component
    type?: "" | "primary" | "success" | "danger" | "warning" | "info"
    plain?: boolean
  }>(),
  {
    tooltip: "请至少选择一条数据",
    disabled: false,
    icon: "",
    type: "",
    plain: true
  }
)

defineEmits(["click"])
</script>
