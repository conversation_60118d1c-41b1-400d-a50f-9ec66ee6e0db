<template>
  <SearchContainer
    class="search-container"
    @query-btn-click="$emit('queryBtnClick')"
    @reset-btn-click="$emit('resetBtnClick')"
  >
    <div
      v-for="(item, index) in formConfig"
      :key="item.label"
      :class="collapse && index > 1 ? 'hidden-form-item' : 'show-form-item'"
    >
      <!-- 患者信息 -->
      <PatientFilterFormItem
        v-if="item.type === 'patient'"
        v-model:filter-prop="$props.formState.patientFilterProp"
        v-model:filter-value="$props.formState.patientFilterValue"
        :label="item.label"
      />

      <!-- 住院次数 -->
      <VisitIdFormItem
        v-else-if="item.type === 'visit'"
        v-model:input-value="$props.formState.visitIdCount"
        v-model:select-value="$props.formState.visitIdSymbol"
        :label="item.label"
      />

      <!-- 出院科室 -->
      <DepartmentFormItem
        v-else-if="item.type === 'department'"
        v-model:model-value="$props.formState[item.prop]"
        :label="item.label"
      />

      <!-- 时间范围 -->
      <DaterangeFormItem
        v-else-if="item.type === 'daterange'"
        v-model:model-value="$props.formState[item.prop]"
        :label="item.label"
      />

      <el-form-item v-else :label="item.label">
        <!-- 输入框 -->
        <el-input
          v-if="item.type === 'input'"
          v-model.trim="$props.formState[item.prop]"
          :placeholder="`请输入${item.label}`"
          style="width: 240px"
          clearable
        />

        <!-- 下拉选择 -->
        <el-select
          v-else-if="item.type === 'select'"
          v-model="$props.formState[item.prop]"
          :placeholder="`请选择${item.label}`"
          style="width: 240px"
          clearable
          filterable
        >
          <el-option v-for="option in item.options" :key="option.value" :label="option.label" :value="option.value" />
        </el-select>
      </el-form-item>
    </div>
    <template #action>
      <el-button v-if="props.formConfig?.length > 2" type="primary" link @click="collapse = !collapse">
        {{ collapse ? "展开" : "折叠" }}
        <i :class="collapse ? 'ri-arrow-down-s-line' : 'ri-arrow-up-s-line'"></i>
      </el-button>
    </template>

    <template #after-action>
      <slot name="after-action"></slot>
    </template>
  </SearchContainer>
</template>

<script setup lang="ts">
import { ref } from "vue"
import {
  SearchContainer,
  PatientFilterFormItem,
  DaterangeFormItem,
  VisitIdFormItem,
  DepartmentFormItem
} from "@/base-components"
import type { SearchFormConfigItem } from "@/types"

const props = defineProps<{
  formConfig: Array<SearchFormConfigItem>
  formState: Record<string, any>
}>()

defineEmits(["resetBtnClick", "queryBtnClick"])

// 折叠
const collapse = ref(false)
</script>

<style lang="less" scoped>
.page-search-form {
  display: flex;
  column-gap: 20px;
  padding-top: 20px;
  flex-wrap: wrap;

  :deep(.el-date-editor) {
    width: 240px;
  }
}

.search-container {
  .hidden-form-item {
    display: none;
  }

  .show-form-item {
    width: 19vw;
    max-width: 20vw;
    display: block;

    :deep(.el-form-item) {
      width: auto !important;
      min-width: 280px;
    }
  }
}
</style>
