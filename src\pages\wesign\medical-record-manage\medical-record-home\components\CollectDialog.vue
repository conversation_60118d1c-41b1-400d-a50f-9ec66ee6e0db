<template>
  <DialogContainer
    title="收藏"
    :width="490"
    :visible="visible"
    :close-on-click-modal="false"
    :cancel-callback="clear"
    :close-callback="clear"
    :confirm-callback="confirmCollect"
  >
    <div class="dialog-body">
      <div>选择添加标签：</div>
      <div class="collection-tags">
        <!-- <el-button
          v-for="(tag, tagIndex) in state.tagList"
          :key="tagIndex"
          plain
          size="small"
          class="collection-tag"
          :class="{ 'collection-tag--active': state.checkedTagList.includes(tag) }"
          @click="handleTagClick(tag)"
        >
          {{ tag }}
        </el-button> -->

        <el-tag
          v-for="tag in state.tagList"
          :key="tag"
          style="cursor: pointer"
          :type="state.checkedTagList.includes(tag) ? 'primary' : 'info'"
          :disable-transitions="false"
          @click="handleTagClick(tag)"
        >
          {{ tag }}
        </el-tag>
      </div>
      <!-- <el-input
        v-if="inputVisible"
        ref="InputRef"
        v-model="state.newTag"
        class="w-20"
        size="small"
        @keyup.enter="handleAddTag"
        @blur="inputVisible = false"
      >
        <template #append><el-button :icon="Plus" @click="handleAddTag" /></template>
      </el-input>
      <el-button v-else class="button-new-tag" size="small" @click="showInput">+ New Tag</el-button> -->

      <div style="margin-bottom: 10px">新建标签：</div>
      <el-input v-model.trim="state.newTag" placeholder="请输入标签名">
        <template #append><el-button :icon="Plus" @click="handleAddTag" /></template>
      </el-input>
    </div>
  </DialogContainer>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from "vue"
import { Plus } from "@element-plus/icons-vue"
import { DialogContainer } from "@/base-components"
import { getCollectionTagListApi, addCollectionTagApi, collectMedicalRecordApi } from "../../interface"
import { Message, SystemPrompt } from "@/utils/message-tool"

interface Props {
  selectedRow: any // 所选的单条数据
  selectedRows: any[] // 所选的多条数据
  tableRef: any
}

const props = defineProps<Props>()

interface State {
  tagList: string[]
  newTag: string
  checkedTagList: string[]
}

const visible = ref(false)

const state = reactive<State>({
  tagList: [],
  newTag: "",
  checkedTagList: []
})

onMounted(() => {
  // 获取已有的标签列表
  getCollectionTagList()
})

// 获取收藏标签
const getCollectionTagList = () => {
  getCollectionTagListApi().then(res => {
    state.tagList = res.data.data
  })
}

// 添加标签
const handleAddTag = () => {
  if (!state.newTag) {
    Message.error("标签不能为空")
    return
  }
  addCollectionTagApi({ labelName: state.newTag })
    .then(() => {
      Message.success("添加标签成功")
      state.newTag = ""
      getCollectionTagList()
    })
    .catch(error => {
      Message.error(error.response.errMessage || "添加失败")
    })
}

// 点击tag，勾选/取消勾选
const handleTagClick = tag => {
  if (state.checkedTagList.includes(tag)) {
    state.checkedTagList = state.checkedTagList.filter(__tag => {
      return __tag !== tag
    })
  } else {
    state.checkedTagList.push(tag)
  }
}

// 确认收藏
const confirmCollect = () => {
  if (!state.checkedTagList.length) {
    Message.error("未选择收藏标签！")
    return
  }
  let documentBagWsids: string[] = []
  if (props.selectedRows.length) {
    documentBagWsids = props.selectedRows.map(row => row.wsid)
  } else {
    documentBagWsids = [props.selectedRow.wsid]
  }
  return collectRecord(documentBagWsids)
}

// 收藏调用接口
const collectRecord = (documentBagWsids: string[]) => {
  collectMedicalRecordApi({ documentBagWsids: documentBagWsids, labels: state.checkedTagList }).then(res => {
    const existFavorite: string[] = res.data.data.existFavorite
    const notExistFavorite: string[] = res.data.data.notExistFavorite
    if (existFavorite.length === 0) {
      Message.success("收藏成功")
      props.tableRef.refreshTableData()
      close()
      return
    }
    if (documentBagWsids.length === existFavorite.length) {
      SystemPrompt("当前选中的所有病案已在收藏中，请勿重复收藏")
      return
    }
    if (existFavorite.length && notExistFavorite.length) {
      // 需要单独请求一次，否则会导致循环
      SystemPrompt("您当前收藏包含有已收藏的病案，本次收藏只会收藏未收藏的病案").then(() => {
        collectMedicalRecordApi({
          documentBagWsids: notExistFavorite,
          labels: state.checkedTagList
        }).then(res => {
          const { existFavorite } = res.data.data
          if (existFavorite && existFavorite.length === 0) {
            Message.success("收藏成功")
            close()
            props.tableRef?.refreshTableData()
          } else {
            Message.success("收藏失败")
            close()
            props.tableRef?.refreshTableData()
          }
        })
      })
    }
  })
}

const clear = () => {
  state.checkedTagList = []
  state.newTag = ""
  close()
}

const open = () => {
  visible.value = true
}

const close = () => {
  visible.value = false
}

const InputRef = ref()

const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    InputRef.value!.input!.focus()
  })
}

const inputVisible = ref(false)

defineExpose({ open, close })
</script>

<style scoped lang="less">
.collection-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin: 10px 0;
}

.collection-tag {
  color: rgb(44 104 255);
  border: 1px solid rgb(44 104 255);

  &--active {
    color: white;
    background-color: rgb(44 104 255);
  }
}

.collect-dialog {
  .collect-tags {
    overflow-y: auto;
    max-height: 100px;
    margin-top: 12px;

    gap: 12px;
    .collect-tag-item {
      justify-content: space-between;
      padding: 4px 8px;
      font-size: 12px;
      color: rgb(44 104 255);
      border: 1px solid rgb(44 104 255);
      cursor: pointer;
      &:hover {
        color: white;
        background-color: rgb(44 104 255);
      }
      &.tag-active {
        color: white;
        background-color: rgb(44 104 255);
      }
    }
  }
}
</style>
