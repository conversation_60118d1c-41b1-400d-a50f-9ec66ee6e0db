<template>
  <AntdConfigProvider :locale="antdZhCn">
    <el-config-provider :locale="zhCn">
      <router-view v-if="$route.meta.hideLayout"></router-view>
      <Layout v-else></Layout>
    </el-config-provider>
  </AntdConfigProvider>
</template>

<script setup lang="ts">
import { ConfigProvider as AntdConfigProvider } from "ant-design-vue"
import antdZhCn from "ant-design-vue/es/locale/zh_CN"
import zhCn from "element-plus/dist/locale/zh-cn.mjs"
import Layout from "@/layout/index.vue"
</script>
