import { createApp } from "vue"
import ElementPlus from "element-plus"
import { createVersionPolling } from "version-polling"
import print from "vue3-print-nb"
import * as ElementPlusIconsVue from "@element-plus/icons-vue"
import App from "./App.vue"
import router from "@/router"
import store from "@/stores"

import "@/styles/common-style.less"
import "remixicon/fonts/remixicon.css"
import "element-plus/dist/index.css"
import "@signit/vue3-components/dist/style.css"
// 全局初始注入样式文件（否则要先触发quill编辑器样式才正常）
import "@vueup/vue-quill/dist/vue-quill.core.css"
import "@vueup/vue-quill/dist/vue-quill.snow.css"
import "@vueup/vue-quill/dist/vue-quill.bubble.css"

// createVersionPolling({
//   appETagKey: "__APP_ETAG__",
//   pollingInterval: 5 * 1000, //
//   silent: process.env.NODE_ENV === "development", // 开发环境下不检测
//   onUpdate: self => {
//     const result = confirm("页面有更新，点击确定刷新页面！")
//     if (result) {
//       self.onRefresh()
//     } else {
//       self.onCancel()
//     }
//     self.onRefresh()
//   }
// })

const app = createApp(App)

// 全局导入element-plus
app.use(ElementPlus)
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
app.use(store)
app.use(router)
app.use(print)

app.mount("#app")
