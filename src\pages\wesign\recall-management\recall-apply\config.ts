import type { TableColumnItem, SearchFormConfigItem } from "@/types"

// 召回申请表格配置
export const recallApplyTableColumns: Array<TableColumnItem> = [
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 120, must: true, sortable: true },
  { prop: "mrNo", label: "病案号", minWidth: 100, must: true, sortable: true },
  { prop: "visitId", label: "住院次数", sortable: true, minWidth: 120, must: true },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 120, must: true },
  { prop: "inHospitalDatetime", label: "入院时间", minWidth: 180, sortable: true },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 120, must: true },
  { prop: "outHospitalDatetime", label: "出院时间", minWidth: 180, sortable: true },
  { prop: "outDischargeDiagnosisName", label: "出院主诊断", minWidth: 200, must: true },
  { prop: "recallCount", label: "修改次数", minWidth: 120, sortable: true },
  { prop: "patientPrintCount", label: "患者打印", minWidth: 100, must: true },
  { prop: "operation", label: "操作", width: 75, fixed: "right", must: true }
]

// 召回申请搜索表单配置
export const recallApplySearchFormConfig: Array<SearchFormConfigItem> = [
  { type: "patient", label: "患者信息" },
  { type: "daterange", label: "入院时间", prop: "inHospitalDatetime" },
  { type: "daterange", label: "出院时间", prop: "outHospitalDatetime" }
]
