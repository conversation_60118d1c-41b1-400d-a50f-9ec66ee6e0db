import type { TableColumnItem } from "@/types"

export const menuId = "/medical-record/sign-for"

export const tabsConfig = [
  { label: "待签收", path: "/medical-record/sign-for/waiting" },
  { label: "已签收", path: "/medical-record/sign-for/signed" },
  { label: "已退回", path: "/medical-record/sign-for/returned" }
]

// 待签收列表
export const waitingColumns: Array<TableColumnItem> = [
  { prop: "patientName", label: "姓名", minWidth: 200, must: true },
  { prop: "mrNo", label: "病案号", minWidth: 150, must: true },
  { prop: "inpNo", label: "住院号", minWidth: 120 },
  { prop: "patientId", label: "患者编号", minWidth: 100 },
  { prop: "visitId", label: "住院次数", minWidth: 110, sortable: true },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 100 },
  { prop: "outHospitalDatetime", label: "出院时间", minWidth: 180, sortable: true, must: true },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 100 },
  { prop: "doctorInCharge", label: "主治医师", minWidth: 100 },
  { prop: "operation", label: "操作", width: 100, fixed: "right", must: true }
]

// 已签收列表
export const signedColumns: Array<TableColumnItem> = [
  { prop: "patientName", label: "姓名", minWidth: 200, must: true },
  { prop: "mrNo", label: "病案号", minWidth: 150, must: true },
  { prop: "inpNo", label: "住院号", minWidth: 120 },
  { prop: "patientId", label: "患者编号", minWidth: 100 },
  { prop: "visitId", label: "住院次数", minWidth: 110, sortable: true },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 100 },
  { prop: "outHospitalDatetime", label: "出院时间", minWidth: 180, sortable: true, must: true },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 100 },
  { prop: "doctorInCharge", label: "主治医师", minWidth: 100 },
  { prop: "receiptOperator", label: "签收人", minWidth: 100 },
  { prop: "receiptDatetime", label: "签收时间", minWidth: 180 },
  { prop: "operation", label: "操作", width: 100, fixed: "right", must: true }
]

// 已签收列表
export const returnedColumns: Array<TableColumnItem> = [
  { prop: "patientName", label: "姓名", minWidth: 200, must: true },
  { prop: "mrNo", label: "病案号", minWidth: 150, must: true },
  { prop: "inpNo", label: "住院号", minWidth: 120 },
  { prop: "patientId", label: "患者编号", minWidth: 100 },
  { prop: "visitId", label: "住院次数", minWidth: 110, sortable: true },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 100 },
  { prop: "outHospitalDatetime", label: "出院时间", minWidth: 180, sortable: true, must: true },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 100 },
  { prop: "doctorInCharge", label: "主治医师", minWidth: 100 },
  { prop: "receiptOperator", label: "签收人", minWidth: 100 },
  { prop: "receiptDatetime", label: "签收时间", minWidth: 180 },
  { prop: "receiptReason", label: "原因", minWidth: 100 },
  { prop: "operation", label: "操作", width: 100, fixed: "right", must: true }
]

export const filterPropOptions = [
  { label: "姓名", value: "patientName" },
  { label: "住院号", value: "inpNo" },
  { label: "病案号", value: "mrNo" }
]
