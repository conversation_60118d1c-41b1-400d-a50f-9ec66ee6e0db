import { defineStore } from "pinia"
import type { BaseOptionItem } from "@/types"

/* ======================== 常用选项 ======================== */

const useGlobalOptionsStore = defineStore("COMMON_OPTIONS", {
  state: () => {
    return {
      departmentOptions: [] as Array<BaseOptionItem>,
      borrowTypeOptions: [] as Array<BaseOptionItem>,
      medicalRecordStatusOptions: [] as Array<Record<string, any>>, // 病案状态列表
      patientLabelOptions: [] as Array<BaseOptionItem> // 患者标签列表
    }
  }
})

export default useGlobalOptionsStore
