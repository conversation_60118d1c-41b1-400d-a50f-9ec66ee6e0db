<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
          :filter-prop-options="filterPropOptions"
        />

        <CommonSelectFormItem
          v-model:model-value="searchFormState.approverStatus"
          label="审批状态"
          :options="applyStatusOptions"
        />

        <CommonSelectFormItem
          v-model:model-value="searchFormState.receiveType"
          label="领取方式"
          :options="receiveTypeOptions"
        />

        <DaterangeFormItem v-model:model-value="searchFormState.createdDatetime" label="申请时间" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="tableRef"
        table-id="approvalTableIdent"
        :table-columns="approvedColumns"
        :request-api="getPrintApplyList"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <template #header>
          <BatchOperationButton :icon="Connection" type="primary" @click="handleBatchRegenerate">
            批量重新生成
          </BatchOperationButton>
        </template>

        <template #approverDatetime="{ row }">
          {{ formatDatetime(row.approverDatetime) }}
        </template>
        <template #operation="{ row }">
          <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.View)" @click="showApprovalDialog(row)">
            详情
          </TableButton>
          <TableButton
            v-if="row.packageStatus === 'HANDLER_FAIL' && hasOperationPermission(menuId, MenuOperationEnum.Regenerate)"
            @click="handleRegenerate(row)"
          >
            重新生成
          </TableButton>
        </template>
      </CommonTable>
    </template>

    <ApprovalDialog ref="approvalDialogRef" @success="tableRef?.refreshTableData()" />
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import { Connection } from "@element-plus/icons-vue"
import {
  SearchContainer,
  CommonTable,
  TableButton,
  PatientFilterFormItem,
  DaterangeFormItem,
  CommonSelectFormItem,
  PageContainer,
  BatchOperationButton
} from "@/base-components"
import { TabsRouter } from "@/page-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { useUserStore } from "@/stores"
import { Message, SystemPrompt, formatDatetime, toastError } from "@/utils"
import { getPrintApplyList, reGenerateFileApi } from "../interface"
import { receiveTypeOptions, filterPropOptions } from "../print-config"
import ApprovalDialog from "./components/ApprovalDialog.vue"
import { applyStatusOptions, approvedColumns, tabsRouterList, menuId } from "./config"

const { hasOperationPermission } = useUserStore()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  patientFilterValue: "",
  patientFilterProp: "patientName",
  receiveType: "",
  approverStatus: "",
  createdDatetime: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/* ======================== 表格相关方法 ======================== */

const tableRef = ref<InstanceType<typeof CommonTable>>()

function dataCallback(data: Array<Record<string, any>>) {
  data.forEach(item => {
    item.createdDatetime = formatDatetime(item.createdDatetime)
    item.statusStr = item.status === "APPROVE_REFUSE" ? "已驳回" : "已通过"
  })
  return data
}

/* ================ 审批弹窗 ================== */

const approvalDialogRef = ref<InstanceType<typeof ApprovalDialog>>()

const showApprovalDialog = row => {
  approvalDialogRef.value?.getApprovalInfo(row.wsid)
}

/* =================== 异常重新生成 ==================== */

// 单个重新生成
const handleRegenerate = row => {
  confirmReGenerate([row.wsid])
}

// 批量重新生成
const handleBatchRegenerate = () => {
  if (!tableRef.value?.tableState.selectedRows?.length) {
    Message.error("请选择需要重新生成的文件")
    return
  }
  const canReGeneratedRows = tableRef.value?.tableState.selectedRows.filter(row => row.packageStatus === "HANDLER_FAIL")
  if (!canReGeneratedRows?.length) {
    Message.error("所选文件已全部生成成功，无需重新生成")
    return
  }
  if (canReGeneratedRows.length !== tableRef.value?.tableState.selectedRows.length) {
    SystemPrompt("仅重新生成异常的文件", "info").then(() => {
      confirmReGenerate(canReGeneratedRows.map(row => row.wsid))
    })
  } else {
    confirmReGenerate(canReGeneratedRows.map(row => row.wsid))
  }
}

// 确认重新生成
const confirmReGenerate = (wsidList: string[]) => {
  reGenerateFileApi(wsidList)
    .then(() => {
      tableRef.value?.refreshTableData()
      Message.success("操作成功")
    })
    .catch(error => {
      toastError(error)
    })
}
</script>
