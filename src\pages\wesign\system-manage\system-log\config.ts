import type { SearchFormConfigItem, TableColumnItem } from "@/types"

/* ======================== tabs路由配置 ======================== */
export const tabsRouterList = [
  {
    label: "登录日志",
    path: "/system-manage/system-log/login",
    name: "login-log"
  },
  {
    label: "操作日志",
    path: "/system-manage/system-log/operation",
    name: "operation-log"
  }
]
/* ======================== 登录日志配置 ======================== */

export const loginLogTableColumns: Array<TableColumnItem> = [
  { prop: "loginName", label: "操作账号" },
  { prop: "realName", label: "姓名" },
  { prop: "path", label: "权限节点", minWidth: 200 },
  { prop: "operationTypeEnum", label: "操作类型", minWidth: 100 },
  { prop: "describe", label: "操作描述", minWidth: 150 },
  { prop: "ip", label: "IP地址", minWidth: 120 },
  { prop: "operationDateTime", label: "操作时间", minWidth: 150 }
]

export enum OperationTypeEnum {
  LOGIN = "LOGIN", // 登录
  LOGIN_OUT = "LOGIN_OUT" // 登出
}

// 登录日志搜索表单配置
export const loginLogSearchFormConfig: Array<SearchFormConfigItem> = [
  { type: "input", prop: "loginName", label: "操作账号" },
  {
    type: "select",
    prop: "operationType",
    label: "操作类型",
    options: [
      { label: "登录", value: OperationTypeEnum.LOGIN },
      { label: "登出", value: OperationTypeEnum.LOGIN_OUT }
    ]
  },
  { type: "daterange", prop: "operationDateTime", label: "操作时间" }
]

/* ======================== 操作日志配置 ======================== */

export const operationLogTableColumns: Array<TableColumnItem> = [
  { prop: "loginName", label: "操作账号" },
  { prop: "realName", label: "姓名" },
  { prop: "path", label: "权限节点", minWidth: 200 },
  { prop: "operationTypeEnum", label: "操作类型", minWidth: 100 },
  { prop: "describe", label: "操作描述", minWidth: 150 },
  { prop: "ip", label: "IP地址", minWidth: 120 },
  { prop: "operationDateTime", label: "操作时间", minWidth: 150 }
]

// 操作日志搜索表单配置
export const operationLogSearchFormConfig: Array<SearchFormConfigItem> = [
  { type: "input", prop: "loginName", label: "操作账号" },
  { type: "select", prop: "operationType", label: "操作类型", options: [] },
  { type: "input", prop: "describe", label: "操作描述" },
  { type: "daterange", prop: "operationDateTime", label: "操作时间" }
]
