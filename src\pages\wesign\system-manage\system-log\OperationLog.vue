<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchForm
        :form-config="searchFormConfig"
        :form-state="searchForm"
        @query-btn-click="handleQuery"
        @reset-btn-click="handleReset"
      />
    </template>

    <template #table>
      <CommonTable
        :table-columns="operationLogTableColumns"
        :request-api="getOperationLogApi"
        :request-params="searchParams"
      >
        <template #operationDateTime="{ row }">
          {{ formatDatetime(row.operationDateTime) }}
        </template>
        <template #operationTypeEnum="{ row }">
          <el-tag>{{ getOperationTypeDesc(row.operationTypeEnum) }}</el-tag>
        </template>
      </CommonTable>
    </template>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, computed } from "vue"
import { cloneDeep } from "lodash-es"
import { PageContainer, CommonTable } from "@/base-components"
import { TabsRouter, SearchForm } from "@/page-components"
import { useCommonOptions, useTableSearch } from "@/hooks"
import { formatDatetime } from "@/utils"
import { getOperationLogApi, getOperationTypeApi } from "../interface"
import { tabsRouterList, operationLogTableColumns, operationLogSearchFormConfig } from "./config"

/* ======================== 操作类型选项相关 ======================== */

const { options: operationTypeOptions, getOptionLabel: getOperationTypeDesc } = useCommonOptions({
  getOptionsApi: getOperationTypeApi,
  labelAlias: "value",
  valueAlias: "key"
})

const searchFormConfig = computed(() => {
  const temp = cloneDeep(operationLogSearchFormConfig)
  temp.forEach(item => {
    if (item.type === "select" && item.label === "操作类型") item.options = operationTypeOptions.value
  })
  return temp
})

/* ======================== 搜索相关 ======================== */

const searchForm = reactive({
  loginName: "",
  operationType: "",
  describe: "",
  operationDateTime: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchForm)
</script>
