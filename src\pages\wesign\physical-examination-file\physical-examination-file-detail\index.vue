<template>
  <div class="container">
    <div class="header flex-between">
      <el-form-item label="体检历史编号" style="margin-bottom: 0">
        <el-select v-model="selectedVersion" @change="handleVersionChange">
          <el-option
            v-for="version in historyVersionList"
            :key="version.checkupFileWsid"
            :value="version.checkupFileWsid"
            :label="version.checkupId"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-button type="primary" @click="router.back">返回</el-button>
    </div>

    <PdfObjectPreview :src="fileSrc" :show-watermark="false" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue"
import { useRoute, useRouter } from "vue-router"
import { PdfObjectPreview } from "@/base-components"
import { Message } from "@/utils"
import { getPhysicalExaminationFileHistoryApi } from "../interface"

const router = useRouter()
const route = useRoute()

const fileWsid = route.query?.fileWsid as string
const checkupId = route.query?.checkupId as string

const fileSrc = ref("") // 当前文件src
const historyVersionList = ref<Record<string, string>[]>([]) // 历史版本列表
const selectedVersion = ref("") // 当前选中的版本

onMounted(() => {
  if (!fileWsid || !checkupId) {
    Message.error("参数错误")
    return
  }
  fileSrc.value = `/api/checkup/documents/${checkupId}/file/${fileWsid}`
  getPhysicalExaminationFileHistoryApi(checkupId).then(res => {
    historyVersionList.value = res.data.data
    selectedVersion.value = fileWsid
  })
})

// 切换版本
const handleVersionChange = (fileWsid: string) => {
  if (fileWsid === selectedVersion.value) return
  fileSrc.value = `/api/checkup/documents/${route.query?.checkupId}/file/${fileWsid}`
}
</script>

<style lang="less" scoped>
.container {
  background: #fff;
  flex: 1;
  display: flex;
  flex-direction: column;
  border-radius: 8px;

  .header {
    padding: 10px 20px;
  }
}
</style>
