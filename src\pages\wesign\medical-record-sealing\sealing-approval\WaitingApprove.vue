<template>
  <PageContainer>
    <!-- 头部搜索 -->
    <TabsRouter :tabs-config="tabsRouterList" />
    <SearchForm
      :form-config="sealingApprovalSearchFormConfig"
      :form-state="searchFilterForm"
      @query-btn-click="handleQuery"
      @reset-btn-click="handleReset"
    />

    <!-- 审批表格 -->
    <CommonTable
      ref="sealingWaitingTable"
      table-id="sealingWaitingApproveTable"
      :table-columns="sealingWaitingApproveColumns"
      :request-params="searchParams"
      :request-api="getSealingApprovalListApi"
    >
      <template #applyReason="{ row }">
        <span>{{ row.type === "解封" ? row.unlockReason : row.lockReason }}</span>
      </template>
      <template #createdDatetime="{ row }">
        <span>{{ formatDatetime(row.createdDatetime) }}</span>
      </template>
      <template #outHospitalDatetime="{ row }">
        <span>{{ formatDatetime(row.outHospitalDatetime) }}</span>
      </template>
      <template #operation="{ row }">
        <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.Approve)" @click="openApprovalDialog(row)">
          审批
        </TableButton>
      </template>
    </CommonTable>

    <!-- 封存弹窗 -->
    <el-drawer
      v-model="sealingState.isVisibleSealingDialog"
      title="封存审批"
      :size="600"
      :before-close="resetSealingDialog"
      :destroy-on-close="true"
    >
      <div v-loading="sealingState.infoLoading">
        <div class="info">
          <span class="label">申请人：</span>
          <span>{{ sealingState.sealingDialogInfo.applicant }}</span>
        </div>
        <div class="info">
          <span class="label">申请人证件号：</span>
          <span>{{ sealingState.sealingDialogInfo.applicantCertificatesNo }}</span>
        </div>
        <div class="info">
          <span class="label">联系方式：</span>
          <span>{{ sealingState.sealingDialogInfo.applicantContent }}</span>
        </div>
        <div class="info">
          <span class="label">与患者关系：</span>
          <span>{{ sealingState.sealingDialogInfo.relationship }}</span>
        </div>
        <div class="info">
          <span class="label">封存时间：</span>
          <span>{{ unlockTypeToStr(sealingState.sealingDialogInfo.unlockType) }}</span>
        </div>
        <div v-if="sealingState.sealingDialogInfo.unlockType !== 'UNLIMITED_TIME'" class="info">
          <span class="label">封存起止时间：</span>
          <span>
            {{ formatDatetime(sealingState.sealingDialogInfo.createdDatetime) }}
            <span v-if="sealingState.sealingDialogInfo.unlockDatetime">
              至 {{ formatDatetime(sealingState.sealingDialogInfo.unlockDatetime) }}
            </span>
          </span>
        </div>
        <div class="info">
          <span class="label">是否允许追加病历：</span>
          <span>{{ additionalCasesToStr(sealingState.sealingDialogInfo.additionalCases) }}</span>
        </div>
        <div class="info">
          <span class="label">封存原因：</span>
          <span>{{ sealingState.sealingDialogInfo.lockReason }}</span>
        </div>
        <div class="tree-doc" style="height: 200px">
          <div class="label">封存文档：</div>
          <div class="tree">
            <CommonTree ref="treeRef" :data="sealingState.documentTree" />
          </div>
        </div>
        <div v-if="sealingState.sealingDialogInfo.needSecretKey === 'YES'" class="tree-doc">
          <div class="label">封存密钥：</div>
          <div class="input">
            <el-form ref="sealingFormRef" :rules="rules" :model="sealingState">
              <el-form-item prop="secretKey">
                <el-input v-model="sealingState.secretKey" clearable @focus="sealingFormRef.clearValidate()" />
              </el-form-item>
            </el-form>
          </div>
        </div>
        <div class="info">
          <span class="label">辅助资料：</span>
          <div class="auxiliary-file-list">
            <div v-for="item in sealingState.sealingDialogInfo.supportInfo" :key="item.fileWsid" class="auxiliary-file">
              <VuePdfEmbed
                class="auxiliary-file"
                :style="{ width: 88 + 'px' }"
                :width="88"
                :source="{
                  ...defaultPdfRenderConfigs,
                  url: item.source
                }"
                :page="1"
                disable-text-layer
              />
              <div class="upload-hover">
                <el-icon @click="previewFile(item)"><ZoomIn /></el-icon>
              </div>
            </div>
          </div>
        </div>

        <!-- 审批详情 -->
        <ApprovalDetail :business-key="sealingState.sealingDialogInfo.wsid" class="approval-detail" />
      </div>
      <template #footer>
        <div class="approval-reason-label">
          <span class="label">审批意见：</span>
          <el-input
            v-model="approvalOpinionState.reason"
            type="textarea"
            placeholder="填写审批意见"
            maxlength="300"
            show-word-limit
          />
        </div>
        <el-dropdown v-if="sealingState.approvalNodes.length > 0">
          <el-button style="margin-right: 12px">退回</el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-for="item in sealingState.approvalNodes" :key="item.defKey" @click="lockBack(item)">
                {{ item.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button
          :loading="sealingState.actionLoading === 'reject'"
          :disabled="sealingState.actionLoading ? true : false"
          type="danger"
          @click="clickSealReject()"
        >
          驳回
        </el-button>
        <el-button
          :loading="sealingState.actionLoading === 'pass'"
          :disabled="sealingState.actionLoading ? true : false"
          type="primary"
          @click="sealingDialogConfirm()"
        >
          通过
        </el-button>
      </template>
    </el-drawer>

    <!-- 解封弹窗 -->
    <el-drawer
      v-model="sealingState.isVisibleUnblockDialog"
      title="解封审批"
      :size="600"
      :before-close="resetUnblockDialog"
      :destroy-on-close="true"
    >
      <div v-loading="sealingState.infoLoading">
        <div class="info">
          <span class="label">申请人：</span>
          <span>{{ sealingState.unblockDialogInfo.applicant }}</span>
        </div>
        <div class="info">
          <span class="label">申请人证件号：</span>
          <span>{{ sealingState.unblockDialogInfo.applicantCertificatesNo }}</span>
        </div>
        <div class="info">
          <span class="label">联系方式：</span>
          <span>{{ sealingState.unblockDialogInfo.applicantContent }}</span>
        </div>
        <div class="info">
          <span class="label">与患者关系：</span>
          <span>{{ sealingState.unblockDialogInfo.relationship }}</span>
        </div>
        <div class="info">
          <span class="label">解封原因：</span>
          <span>{{ sealingState.unblockDialogInfo.unlockReason }}</span>
        </div>
        <div v-if="sealingState.unblockDialogInfo.needSecretKey === 'YES'" class="tree-doc">
          <div class="label">解封密钥：</div>
          <div class="input">
            <el-form ref="formRef" :rules="rules" :model="sealingState">
              <el-form-item prop="unBlockSecretKey">
                <el-input v-model="sealingState.unBlockSecretKey" clearable @focus="formRef.clearValidate()" />
              </el-form-item>
            </el-form>
          </div>
        </div>
        <div class="info">
          <span class="label">辅助资料：</span>
          <div class="auxiliary-file-list">
            <div v-for="item in sealingState.unblockDialogInfo.supportInfo" :key="item.fileWsid" class="auxiliary-file">
              <VuePdfEmbed
                :style="{ width: 88 + 'px' }"
                :width="88"
                :source="{
                  ...defaultPdfRenderConfigs,
                  url: item.source
                }"
                :page="1"
                disable-text-layer
              />
              <div class="upload-hover">
                <el-icon @click="previewFile(item)"><ZoomIn /></el-icon>
              </div>
            </div>
          </div>
        </div>

        <!-- 审批详情 -->
        <ApprovalDetail :business-key="sealingState.unblockDialogInfo.wsid" class="approval-detail" />
      </div>
      <template #footer>
        <div class="approval-reason-label">
          <span class="label">审批意见：</span>
          <el-input
            v-model="approvalOpinionState.reason"
            type="textarea"
            placeholder="填写审批意见"
            maxlength="300"
            show-word-limit
          />
        </div>
        <el-dropdown v-if="sealingState.approvalNodes.length > 0">
          <el-button style="margin-right: 12px">退回</el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-for="item in sealingState.approvalNodes" :key="item.defKey" @click="unlockBack(item)">
                {{ item.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button
          :disabled="sealingState.actionLoading ? true : false"
          :loading="sealingState.actionLoading === 'reject'"
          type="danger"
          @click="clickUnlockReject()"
        >
          驳回
        </el-button>
        <el-button
          :loading="sealingState.actionLoading === 'pass'"
          :disabled="sealingState.actionLoading ? true : false"
          type="primary"
          @click="unblockSealingDialogConfirm"
        >
          通过
        </el-button>
      </template>
    </el-drawer>

    <!-- pdf 预览 -->
    <el-dialog
      v-model="pdfPreviewState.visible"
      class="preview-document-dialog-wrapper"
      :show-close="false"
      :fullscreen="true"
    >
      <div v-loading="pdfPreviewState.loading" style="height: 100vh">
        <span class="return-back" @click="closePdfPreview">
          <i class="ri-arrow-left-line"></i>
          退出
        </span>
        <div ref="pdfPreviewRef" class="pdf-preview-content"></div>
      </div>
    </el-dialog>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, nextTick, ref } from "vue"
import PdfObject from "pdfobject"
import VuePdfEmbed from "vue-pdf-embed"
import { CommonTable, TableButton, CommonTree, PdfPreview, PageContainer } from "@/base-components"
import { TabsRouter, SearchForm, ApprovalDetail } from "@/page-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { getApprovalNodeApi } from "@/interfaces"
import { useUserStore } from "@/stores"
import { toastError, formatDatetime, Message, SystemPrompt, formatRecordTree, defaultPdfRenderConfigs } from "@/utils"
import {
  getSealingApprovalListApi,
  getSealingDetailApi,
  handleApprovalSealingApi,
  backApprovalNodeApi,
  getSealSupportFileApi
} from "../interface"
import {
  unlockTypeToStr,
  additionalCasesToStr,
  sealingWaitingApproveColumns,
  tabsRouterList,
  menuId,
  sealingApprovalSearchFormConfig
} from "./config"

const { hasOperationPermission } = useUserStore()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFilterForm = reactive({
  patientFilterProp: "patientName",
  patientFilterValue: "",
  applicant: "",
  type: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFilterForm)

/* ======================== 大图预览 ======================== */
// pdf 预览
const pdfPreviewState = reactive({
  visible: false,
  loading: false
})
const pdfPreviewRef = ref<InstanceType<typeof PdfPreview>>()
// 预览文件
function previewFile(file) {
  pdfPreviewState.visible = true
  nextTick(() => {
    PdfObject.embed(file.source, pdfPreviewRef.value)
  })
}

// 关闭pdf预览
const closePdfPreview = () => {
  pdfPreviewState.visible = false
}

/* ======================== 审批意见 ======================== */
// 驳回/回退审批意见
const approvalOpinionState = reactive({
  reason: ""
})

/* ======================== 封存/解封弹窗 ======================== */

const sealingState = reactive({
  isVisibleSealingDialog: false, //封存弹窗
  isVisibleUnblockDialog: false, //解封弹窗
  sealingDialogInfo: {} as Record<string, any>,
  unblockDialogInfo: {} as Record<string, any>,
  secretKey: "", //封存密钥
  unBlockSecretKey: "", //解封密钥
  actionLoading: "",
  documentTree: [] as Array<Record<string, any>>,
  approvalNodes: [] as Array<Record<string, any>>,
  infoLoading: false
})

const formRef = ref()
const sealingFormRef = ref()
const rules = reactive({
  unBlockSecretKey: [{ required: true, message: "请输入封存密钥", trigger: "change" }],
  secretKey: [{ required: true, message: "请输入封存密钥", trigger: "change" }]
})

const sealingWaitingTable = ref()

// 打开审批弹窗
function openApprovalDialog(row) {
  sealingState.infoLoading = true
  if (row?.type === "封存") {
    backNodeState.type = "lock"
    sealingState.isVisibleSealingDialog = true
    getSealingDetailApi({ id: row.id })
      .then(async res => {
        const { code = "", data = {} } = res.data
        if (code === "100100000") {
          data.mrNo = row.mrNo
          data.processTaskId = row.processTaskId
          data.needSecretKey = row.needSecretKey
          sealingState.sealingDialogInfo = data
          sealingState.documentTree = formatRecordTree(res.data.data.treeInfo)
          sealingWaitingTable.value?.refreshTableData()
          await Promise.all(
            sealingState.sealingDialogInfo.supportInfo?.map((item: Record<string, any>) => {
              return getSealSupportFileApi({
                "file-wsid": item.fileWsid
              }).then(res => {
                const source = URL.createObjectURL(new Blob([res.data], { type: "application/pdf" }))
                item.source = source
              })
            })
          )
          sealingState.approvalNodes = await getApprovalNodeApi({ businessKey: data.wsid, taskId: data.processTaskId })
          sealingState.infoLoading = false
        } else {
          sealingState.isVisibleSealingDialog = false
          sealingState.infoLoading = false
          Message.error("操作失败")
        }
      })
      .catch(err => {
        sealingState.infoLoading = false
        toastError(err)
      })
  } else {
    sealingState.isVisibleUnblockDialog = true
    backNodeState.type = "unlock"
    getSealingDetailApi({ id: row.id })
      .then(async res => {
        const { code = "", data = {} } = res.data
        if (code === "100100000") {
          data.mrNo = row.mrNo
          data.processTaskId = row.processTaskId
          data.needSecretKey = row.needSecretKey
          sealingState.unblockDialogInfo = data

          await Promise.all(
            sealingState.unblockDialogInfo.supportInfo?.map((item: Record<string, any>) => {
              return getSealSupportFileApi({
                "file-wsid": item.fileWsid
              }).then(res => {
                const source = URL.createObjectURL(new Blob([res.data], { type: "application/pdf" }))
                item.source = source
              })
            })
          )

          sealingState.approvalNodes = await getApprovalNodeApi({ businessKey: data.wsid, taskId: data.processTaskId })
          sealingState.infoLoading = false
        } else {
          sealingState.isVisibleUnblockDialog = false
          sealingState.infoLoading = false
          Message.error("操作失败")
        }
      })
      .catch(err => {
        sealingState.infoLoading = false
        toastError(err)
      })
  }
}

// 修改审批状态
function handleApprovalSealing(params) {
  handleApprovalSealingApi(params)
    .then(() => {
      sealingState.isVisibleSealingDialog = false
      sealingState.isVisibleUnblockDialog = false
      Message.success("操作成功")
      sealingWaitingTable.value?.refreshTableData()
      resetSealingDialog()
      resetUnblockDialog()
    })
    .catch(err => {
      sealingState.isVisibleSealingDialog = false
      sealingState.isVisibleUnblockDialog = false
      toastError(err)
    })
    .finally(() => {
      sealingState.actionLoading = ""
    })
}

/* ======================== 解封审批 =========================== */
//打开退回弹窗
function unlockBack(backNode) {
  backNodeState.backNode = backNode
  if (!approvalOpinionState.reason) {
    Message.warning("请输入审批意见")
    return
  }
  SystemPrompt("您确定要退回吗？").then(() => {
    backNodeApproval()
  })
}

// 驳回解封申请
function clickUnlockReject() {
  if (!approvalOpinionState.reason) {
    Message.warning("请输入审批意见")
    return
  }
  const row = sealingState.unblockDialogInfo

  if (!row.processTaskId) return Message.error("未获取到流程任务id")

  formRef.value?.clearValidate()
  SystemPrompt(`您确定要驳回病案号${row.mrNo}的解封申请！`).then(() => {
    const params = {
      id: row.id,
      status: "REJECT",
      processTaskId: row.processTaskId,
      desc: approvalOpinionState.reason
    }
    sealingState.actionLoading = "reject"
    handleApprovalSealing(params)
  })
}

// 关闭解封审批
function resetUnblockDialog() {
  sealingState.isVisibleUnblockDialog = false
  sealingState.unblockDialogInfo = {}
  sealingState.unBlockSecretKey = ""
  resetApprovalOpinionForm()
}

/* ======================== 封存审批 =========================== */
// 关闭封存审批
function resetSealingDialog() {
  sealingState.isVisibleSealingDialog = false
  sealingState.sealingDialogInfo = {}
  sealingState.documentTree = []
  sealingState.secretKey = ""
  resetApprovalOpinionForm()
}

// 封存回退
function lockBack(backNode) {
  backNodeState.backNode = backNode
  if (!approvalOpinionState.reason) {
    Message.warning("请输入审批意见")
    return
  }
  SystemPrompt("您确定要退回吗？").then(() => {
    backNodeApproval()
  })
}

// 驳回封存申请
function clickSealReject() {
  if (!approvalOpinionState.reason) {
    Message.warning("请输入审批意见")
    return
  }
  const row = sealingState.sealingDialogInfo
  sealingFormRef.value?.clearValidate()

  if (!row.processTaskId) return Message.error("未获取到流程任务id")

  SystemPrompt(`您确定要驳回病案号${row.mrNo}的封存申请！`).then(() => {
    const params = {
      id: row.id,
      status: "REJECT",
      processTaskId: row.processTaskId,
      desc: approvalOpinionState.reason
    }
    sealingState.actionLoading = "reject"
    handleApprovalSealing(params)
  })
}

// 通过封存申请
async function sealingDialogConfirm() {
  if (sealingState.sealingDialogInfo.needSecretKey === "YES" && !sealingState.secretKey)
    return sealingFormRef.value.validate()
  const { id, processTaskId } = sealingState.sealingDialogInfo
  if (!id || !processTaskId) return Message.error("未获取到流程任务id")
  const params = {
    id,
    status: "APPROVAL_PASS",
    secretKey: sealingState.secretKey,
    processTaskId: processTaskId,
    desc: approvalOpinionState.reason
  }
  SystemPrompt("您确定要通过封存申请吗？").then(() => {
    sealingState.actionLoading = "pass"
    handleApprovalSealing(params)
  })
}

// 通过解封
function unblockSealingDialogConfirm() {
  const { id, processTaskId } = sealingState.unblockDialogInfo
  if (sealingState.unblockDialogInfo.needSecretKey === "YES" && !sealingState.unBlockSecretKey)
    return formRef.value.validate()
  const params = {
    id,
    status: "APPROVAL_PASS",
    secretKey: sealingState.unBlockSecretKey,
    processTaskId: processTaskId,
    desc: approvalOpinionState.reason
  }

  SystemPrompt("您确定要通过解封申请吗？").then(() => {
    sealingState.actionLoading = "pass"
    handleApprovalSealing(params)
  })
}

/* ======================== 审批节点退回弹窗 ======================== */
const backNodeState = reactive({
  loading: false,
  backNode: null as null | Record<string, any>,
  type: "lock" //封存/解封
})

// 提交解封/封存审批节点退回
function backNodeApproval() {
  if (!backNodeState.backNode) return
  const approval = backNodeState.type === "lock" ? sealingState.sealingDialogInfo : sealingState.unblockDialogInfo
  const data = {
    processTaskId: approval.processTaskId,
    processTarActId: backNodeState?.backNode?.defKey,
    desc: approvalOpinionState.reason
  }

  backNodeState.loading = true
  backApprovalNodeApi(data)
    .then(res => {
      Message.success("退回成功")
      if (backNodeState.type === "lock") sealingState.isVisibleSealingDialog = false
      else sealingState.isVisibleUnblockDialog = false

      resetApprovalOpinionForm()

      sealingWaitingTable.value?.refreshTableData()
      backNodeState.loading = false
    })
    .catch(err => {
      backNodeState.loading = false
      toastError(err, "退回失败")
    })
}

// 重置审批意见配置
const resetApprovalOpinionForm = () => {
  approvalOpinionState.reason = ""
}
</script>

<style lang="less" scoped>
.info {
  margin-bottom: 20px;
  display: flex;
  .label {
    text-align: right;
    width: 130px;
    color: rgba(10, 22, 51, 0.6);
  }
  .auxiliary-file-list {
    display: flex;
    column-gap: 5px;
    row-gap: 5px;
    flex: 1;
    min-width: 0px;
    flex-wrap: wrap;
    .auxiliary-image {
      width: 88px;
      height: 88px;
      cursor: pointer;
      object-fit: contain;
    }
    .auxiliary-file {
      position: relative;
      width: 88px;

      .upload-hover {
        position: absolute;
        width: 88px;
        height: 100%;
        top: 0px;
        left: 0px;
        opacity: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        column-gap: 5px;
        color: #fff;
        font-size: 24px;
        &:hover {
          opacity: 1;
          background: rgba(0, 0, 0, 0.2);
        }
        .el-icon {
          cursor: pointer;
        }
      }
    }
  }
}

.approval-reason-label {
  margin-top: 20px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  .label {
    color: rgba(10, 22, 51, 0.6);
    text-align: right;
    width: 160px;
    // &::after {
    //   content: "";
    //   width: 100%;
    //   display: inline-block;
    //   overflow: hidden;
    //   height: 0;
    // }
  }
}

.approval-detail {
  margin-bottom: 20px;
  // max-height: 300px;
  // overflow: auto;
}
.tree-doc {
  display: flex;
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
  .label {
    width: 130px;
    text-align: right;
    color: rgba(10, 22, 51, 0.6);
  }
  .tree {
    width: calc(100% - 135px);
    padding: 16px;
    background-color: rgb(248 249 252);
    border: 1px solid rgb(225 226 230);
    border-radius: 4px;
    box-sizing: border-box;
  }
  .input {
    width: calc(100% - 135px);
  }
}

.bigger-image-container {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  .close-bigger-image {
    position: absolute;
    right: 20px;
    top: 20px;
    font-size: 28px;
    color: #fff;
    cursor: pointer;
  }
  img {
    width: 65%;
    height: 65%;
    object-fit: contain;
  }
  .pdf-preview-header {
    justify-content: space-between !important;
  }
  .return-back {
    cursor: pointer;
    font-size: 14px;
    display: inline-block;
    padding-left: 4px;
    height: 32px;
    line-height: 32px;
  }
  .pdf-preview-content {
    height: calc(100% - 36px);
  }
}
</style>

<style lang="less">
.preview-document-dialog-wrapper {
  .el-dialog__header {
    padding: 0;
  }
  .el-dialog__body {
    padding: 0;
  }
  .pdf-preview-header {
    justify-content: space-between !important;
  }
  .return-back {
    cursor: pointer;
    font-size: 14px;
    display: inline-block;
    padding-left: 4px;
    height: 32px;
    line-height: 32px;
  }
  .pdf-preview-content {
    height: calc(100% - 36px);
  }
}
</style>
