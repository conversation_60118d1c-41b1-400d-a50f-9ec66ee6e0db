import axios from "@/interfaces/axios-instance"

// 用户授权列表
export const getAuthorizedByMrnoListApi = (params: IPaginationRequestParams) => {
  return axios({
    method: "get",
    url: `/api/document/authorization/my/to-user`,
    params: {
      ...params,
      authorizationType: "AUTHORIZATION_TO_USER"
    }
  })
}

// 科室授权列表
export const getAuthorizedByDeptListApi = (params: IPaginationRequestParams) => {
  return axios({
    method: "get",
    url: `/api/document/authorization/my/to-user`,
    params: {
      ...params,
      authorizationType: "AUTHORIZATION_TO_DEPT"
    }
  })
}
