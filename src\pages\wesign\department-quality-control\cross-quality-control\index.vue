<template>
  <PageContainer separate>
    <!-- 顶部搜索 -->
    <template #search>
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
        />

        <DepartmentFormItem v-model:model-value="searchFormState.outHospitalDeptWsid" label="出院科室" />

        <VisitIdFormItem
          v-model:input-value="searchFormState.visitIdCount"
          v-model:select-value="searchFormState.visitIdSymbol"
          label="住院次数"
        />

        <CountRangeFormItem
          v-model:from="searchFormState.inHospitalDaysFrom"
          v-model:to="searchFormState.inHospitalDaysTo"
          :min="0"
          label="住院天数"
          unit="天"
        />

        <DaterangeFormItem v-model="searchFormState.outHospitalDatetime" label="出院时间" />

        <PatientLabelFormItem v-model="searchFormState.patientLabel" />
      </SearchContainer>
    </template>

    <!-- 交叉质控任务分配列表 -->
    <template #table>
      <CommonTable
        ref="qualityControlTableRef"
        :table-columns="crossQualityControlColumns"
        :request-api="getDistributeQcCrossList"
        :request-params="searchParams"
      >
        <!-- 表格头部操作 -->
        <template #header>
          <el-button
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Distribute)"
            type="primary"
            :icon="Plus"
            :disabled="selectedRows.length === 0"
            @click="openDistributeDialog"
          >
            分配
          </el-button>
          <el-button
            v-if="hasOperationPermission(menuId, MenuOperationEnum.ProcessSearch)"
            :icon="Search"
            @click="processSearchRef?.openDialog()"
          >
            质控进度查询
          </el-button>
        </template>
        <template #inHospitalDatetime="{ row }">
          {{ formatDatetime(row.inHospitalDatetime) }}
        </template>
        <template #outHospitalDatetime="{ row }">
          {{ formatDatetime(row.outHospitalDatetime) }}
        </template>
        <template #patientName="{ row }">
          <div style="display: flex; align-items: center; flex-wrap: wrap">
            <span style="margin: 0 16px">{{ row.patientName }}</span>
            <PatientLabelTag :row="row" />
          </div>
        </template>
      </CommonTable>
    </template>
  </PageContainer>

  <!-- 分配质控员弹窗 -->
  <DialogContainer
    v-model:visible="distributeDialogState.visible"
    title="分配质控任务"
    :width="700"
    :confirm-loading="distributeDialogState.submitLoading"
    :confirm-callback="confirmDistribute"
  >
    <div class="distribute-search-container">
      <el-select
        v-model="distributeDialogState.qcType"
        placeholder="请选择质控角色"
        style="width: 200px"
        @change="getDistributeTable"
      >
        <el-option label="科室医疗质控" :value="QualityControlTypeEnum.CROSS_QC_MEDICAL" />
        <el-option label="科室护理质控" :value="QualityControlTypeEnum.CROSS_QC_NURSE" />
      </el-select>
      <el-input
        v-model="distributeDialogState.searchValue"
        :prefix-icon="Search"
        placeholder="请输入需要搜索的质控员的工号或者姓名"
        clearable
        @change="getDistributeTable"
      />
    </div>
    <BaseTable
      ref="distributeTableRef"
      class="distribute-table"
      :border="true"
      :columns="distributeColumns"
      :data="distributeDialogState.data"
      :loading="distributeDialogState.loading"
    />
  </DialogContainer>

  <!-- 进度查询弹窗 -->
  <ProcessSearch ref="processSearchRef" />
</template>

<script setup lang="ts">
import { reactive, computed, ref } from "vue"

import { cloneDeep } from "lodash-es"
import { Search, Plus } from "@element-plus/icons-vue"
import {
  CommonTable,
  BaseTable,
  CountRangeFormItem,
  DaterangeFormItem,
  DepartmentFormItem,
  DialogContainer,
  PageContainer,
  PatientFilterFormItem,
  SearchContainer,
  VisitIdFormItem,
  PatientLabelTag,
  PatientLabelFormItem
} from "@/base-components"
import { MenuOperationEnum, QualityControlTypeEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { getQualityControlUserApi, distributeQcTaskApi } from "@/interfaces"
import { useUserStore } from "@/stores"
import { Message, toastError, formatDatetime } from "@/utils"
import { crossQualityControlColumns, distributeColumns } from "../config"
import { getDistributeQcCrossList } from "../interface"
import ProcessSearch from "./component/ProcessSearch.vue"

const { hasOperationPermission } = useUserStore()
const menuId = "/department-quality-control/cross-distribute"

const initFormData = {
  patientFilterProp: "patientName",
  patientFilterValue: "",
  outHospitalDeptWsid: "",
  visitIdCount: "",
  visitIdSymbol: "",
  inHospitalDaysFrom: "",
  inHospitalDaysTo: "",
  outHospitalDatetime: "",
  death: "",
  patientLabel: ""
}

const searchFormState = reactive(cloneDeep(initFormData))

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/* =================== 未质控列表 ==================== */
const qualityControlTableRef = ref()

const selectedRows = computed(() => {
  return qualityControlTableRef.value?.tableState?.selectedRows || []
})
/* =================== 分配质控员弹窗 ==================== */
const distributeTableRef = ref()
const distributeDialogState = reactive({
  visible: false,
  loading: false,
  data: [] as Array<Record<string, any>>,
  qcType: QualityControlTypeEnum.CROSS_QC_MEDICAL,
  searchValue: "",
  submitLoading: false
})

// 打开分配质控员弹窗
function openDistributeDialog() {
  getDistributeTable()
  distributeDialogState.searchValue = ""
  distributeDialogState.visible = true
}

function getDistributeTable() {
  distributeDialogState.loading = true
  getQualityControlUserApi(distributeDialogState.qcType, distributeDialogState.searchValue)
    .then(res => {
      distributeDialogState.data = res.data.data || []
    })
    .catch(err => {
      toastError(err, "获取质控员列表失败")
    })
    .finally(() => {
      distributeDialogState.loading = false
    })
}

// 确认分配质控任务
function confirmDistribute() {
  const selectedDistribute = distributeTableRef.value?.tableState?.selectedRows || []
  if (selectedDistribute.length === 0) Message.warning("请选择要分配的质控员")
  else {
    distributeDialogState.submitLoading = true
    //病案
    const tasks: Array<Record<string, any>> = []
    selectedRows.value.forEach(item => {
      tasks.push({ inpNo: item.inpNo })
    })
    //质控员
    const operators: Array<string> = []
    selectedDistribute.forEach(item => {
      operators.push(item.wsid)
    })

    distributeQcTaskApi({ tasks, operators, qcType: distributeDialogState.qcType })
      .then(res => {
        qualityControlTableRef.value?.refreshTableData()
        Message.success("分配成功")
        distributeDialogState.visible = false
      })
      .catch(err => {
        toastError(err, "分配失败")
      })
      .finally(() => {
        distributeDialogState.submitLoading = false
      })
  }
}

/* =================== 进度查询弹窗 ==================== */
const processSearchRef = ref()
</script>

<style lang="less" scoped>
.distribute-table {
  margin-top: 10px;
  height: 320px;
}

.distribute-search-container {
  display: flex;
}
</style>
