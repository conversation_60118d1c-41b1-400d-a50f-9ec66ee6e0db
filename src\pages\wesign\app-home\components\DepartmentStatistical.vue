<template>
  <div class="department-table-container">
    <BaseTable :columns="departmentStatisticalColumns" :data="props.data" border></BaseTable>
  </div>
</template>

<script setup lang="ts">
import { BaseTable } from "@/base-components"
import { departmentStatisticalColumns } from "../config"

const props = defineProps<{ data: Array<Record<string, any>> }>()
</script>

<style lang="less" scoped>
.department-table-container {
  height: 100%;
  .el-table__body-wrapper {
    max-height: 400px;
  }
}
</style>
