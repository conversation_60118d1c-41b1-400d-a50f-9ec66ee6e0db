<template>
  <div class="data-dictionary-container">
    <!-- 左侧字典列表 -->
    <div v-loading="dictionaryData.loading" class="dictionary-list">
      <PageSideList
        ref="pageSideListRef"
        title="字典名称"
        :list-data="dictionaryData.list"
        :loading="dictionaryData.loading"
        :hide-add="true"
        :hide-edit="true"
        @click-add="handleAddDictionaryGroup"
        @click-edit="handleEditDictionaryGroup"
        @click-delete="handleDeleteDictionaryGroup"
        @change-active="setDictionary"
      />
    </div>

    <!-- 右侧字典明细 -->
    <div v-loading="dictionaryDetailLoading" class="dictionary-table-container">
      <div class="table-header">{{ dictionaryData.activeDictionary.name }}</div>
      <div class="table-content">
        <CommonTable
          ref="dictionaryTableRef"
          :table-columns="tableColumns"
          :request-api="queryDictionaryDetailByGroup"
          :request-params="params"
          :pagination="false"
        >
          <template #header>
            <div style="margin-bottom: 12px">
              <AddButton @click="handle('add')">新增</AddButton>
            </div>
          </template>

          <template #createdDatetime="{ row }">
            {{ formatDatetime(row.createdDatetime) }}
          </template>
          <template #operation="{ row }">
            <TableButton @click="handle('edit', row)">编辑</TableButton>
            <TableButton @click="modifyStatus(row)">{{ row.status === "ENABLE" ? "禁用" : "启用" }}</TableButton>
            <TableButton @click="handleDelete(row)">删除</TableButton>
          </template>
        </CommonTable>
      </div>
    </div>

    <!-- 字典编辑/新增弹窗 -->
    <DialogContainer
      v-model:visible="visible"
      :title="dictionaryData.type === 'add' ? '新增' : '编辑'"
      :width="400"
      :close-on-click-modal="false"
      :confirm-callback="confirmAddGroup"
    >
      <el-form :label-position="'right'" label-width="100px" :model="dictionaryData" style="max-width: 460px">
        <el-form-item label="字典编码：" required>
          <el-input v-model="dictionaryData.key" />
        </el-form-item>
        <el-form-item label="字典名称：" required>
          <el-input v-model="dictionaryData.name" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="dictionaryData.desc" type="textarea" />
        </el-form-item>
      </el-form>
    </DialogContainer>

    <!-- 字典明细编辑/新增弹窗 -->
    <DialogContainer
      v-model:visible="dictionaryDetailVisible"
      :title="dictionaryDetailType === 'add' ? '新增' : '编辑'"
      :width="500"
      :close-on-click-modal="false"
      :confirm-callback="confirmAddDictionaryDetail"
    >
      <el-form
        ref="detailFormRef"
        :label-position="'right'"
        label-width="110px"
        :model="dictionaryDetailData"
        style="max-width: 460px"
        :rules="detailFormRules"
      >
        <el-form-item label="字典键：" prop="key">
          <el-input v-model="dictionaryDetailData.key" />
        </el-form-item>
        <el-form-item label="字典明细值：" prop="value">
          <el-input v-model="dictionaryDetailData.value" />
        </el-form-item>
        <el-form-item label="字典项描述：" prop="describe">
          <el-input v-model="dictionaryDetailData.describe" type="textarea" />
        </el-form-item>
        <el-form-item
          v-if="dictionaryData.activeDictionary.key === 'PRIVACY_KEYWORDS'"
          label="关键字长度："
          prop="length"
        >
          <el-input v-model.number="dictionaryDetailData.length" />
        </el-form-item>
      </el-form>
    </DialogContainer>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, computed } from "vue"
import { ElForm } from "element-plus"
import { DialogContainer, CommonTable, AddButton, TableButton } from "@/base-components"
import { PageSideList } from "@/page-components"
import { queryDictionaryDetailByGroup } from "@/interfaces"
import { CollectTypeItem } from "@/types"
import { SystemPrompt, Message, extractErrorMsg, toastError, formatDatetime } from "@/utils"
import { dataDictionaryColumns, dataDictionaryColumnsWithKeywords, detailFormRules } from "./config"
import {
  queryDictionaryGroup,
  addDictionaryGroup,
  editDictionaryGroup,
  deleteDictionaryGroup,
  addDictionaryDetail,
  editDictionaryDetail,
  setDictionaryDetailStatus
} from "./interface"

const tableColumns = computed(() => {
  if (dictionaryData.activeDictionary.key === "PRIVACY_KEYWORDS") {
    return dataDictionaryColumnsWithKeywords
  }
  return dataDictionaryColumns
})

/* ======================== 侧边栏相关数据及方法 ======================== */

interface Group extends CollectTypeItem {
  desc: string
  key: string
}

interface Dictionary {
  list: Array<Group>
  loading: boolean
  activeDictionary: Record<string, any>
  name: string
  key: string
  desc: string
  id: string
  type: "add" | "edit" | "delete"
}

const dictionaryData = reactive<Dictionary>({
  list: [],
  loading: false,
  activeDictionary: {},
  name: "",
  key: "",
  desc: "",
  id: "",
  type: "add"
})

const params = ref()

onMounted(() => {
  getDictionaryGroup()
})

// 获取字典分类
const getDictionaryGroup = () => {
  queryDictionaryGroup().then(res => {
    dictionaryData.list = res.data.data
    // 默认展示第一个

    if (dictionaryData.list.length) {
      params.value = { groupKey: dictionaryData.list[0]?.key }
      setDictionary(dictionaryData.list[0].id)
    }
    dictionaryData.loading = false
  })
}

// 设置当前选中字典
const setDictionary = data => {
  dictionaryData.activeDictionary = dictionaryData.list.find(item => item.id === data) || {}
  params.value = { groupKey: dictionaryData.list.find(item => item.id === data)?.key }
}

const visible = ref(false)
// 记录上一次的操作 点"新增"是否保存上一次的信息
const lastType = ref("")

const handleAddDictionaryGroup = () => {
  visible.value = true
  // if (lastType.value === "edit") {
  dictionaryData.name = ""
  dictionaryData.key = ""
  dictionaryData.desc = ""
  dictionaryData.id = ""
  // }
  dictionaryData.type = "add"
  lastType.value = "add"
}

const handleEditDictionaryGroup = data => {
  visible.value = true
  dictionaryData.name = data.name
  dictionaryData.key = data.key
  dictionaryData.desc = data.desc
  dictionaryData.id = data.id
  dictionaryData.type = "edit"
  lastType.value = "edit"
}

const handleDeleteDictionaryGroup = data => {
  if (dictionaryData.loading) return
  dictionaryData.loading = true
  deleteDictionaryGroup(data.key)
    .then(() => {
      Message.success("操作成功")
      getDictionaryGroup()
    })
    .catch(err => Message.error(extractErrorMsg(err, "操作失败")))
}

// 确认添加/编辑字典分类
const confirmAddGroup = () => {
  dictionaryData.desc = dictionaryData.desc ?? ""
  if (dictionaryData.key === "" || dictionaryData.name === "") {
    Message.warning("请填写必填项后再提交")
    return
  }

  if (dictionaryData.loading) return
  dictionaryData.loading = true

  const handler =
    dictionaryData.type === "add"
      ? addDictionaryGroup({
          name: dictionaryData.name,
          key: dictionaryData.key,
          desc: dictionaryData.desc,
          id: dictionaryData.id
        })
      : editDictionaryGroup({
          name: dictionaryData.name,
          key: dictionaryData.key,
          desc: dictionaryData.desc,
          id: dictionaryData.id
        })

  handler
    .then(() => {
      Message.success("操作成功")

      getDictionaryGroup()
    })
    .catch(err => Message.error(extractErrorMsg(err, "操作失败")))
    .finally(() => {
      visible.value = false
      dictionaryData.loading = false
    })
}

/* ======================== 表格相关数据及方法 ======================== */

const detailFormRef = ref<InstanceType<typeof ElForm>>() // form ref
const dictionaryDetailData = reactive({
  wsid: "",
  key: "",
  value: "",
  length: 1, // 隐私关键字字典特有字段：关键字长度
  describe: ""
})
const dictionaryDetailVisible = ref(false)
const dictionaryDetailType = ref("")

// 记录上一次的操作 点"新增"是否保存上一次的信息
const lastDictionaryDetailType = ref("")

const handle = (type, row = {}) => {
  dictionaryDetailType.value = type
  Object.keys(dictionaryDetailData).forEach(key => {
    dictionaryDetailData[key] = type === "add" ? "" : row[key]
    if (lastDictionaryDetailType.value === "edit" && type === "add") {
      dictionaryDetailData[key] = ""
    }
  })
  dictionaryDetailVisible.value = true
  lastDictionaryDetailType.value = type
}

const dictionaryDetailLoading = ref(false)

// 确认添加字典明细
const confirmAddDictionaryDetail = () => {
  dictionaryDetailData.describe = dictionaryDetailData.describe ?? ""
  if (dictionaryDetailData.key === "" || dictionaryDetailData.value === "") {
    Message.warning("请填写必填项后再提交")
    return
  }
  detailFormRef.value?.validate(valid => {
    console.log("valid", valid)
    if (!valid) return
    if (dictionaryDetailLoading.value) return
    dictionaryDetailLoading.value = true
    const handler =
      dictionaryDetailType.value === "add"
        ? addDictionaryDetail({
            groupKey: dictionaryData.activeDictionary.key,
            key: dictionaryDetailData.key,
            value: dictionaryDetailData.value,
            describe: dictionaryDetailData.describe ?? "",
            length: dictionaryDetailData.length || undefined
          })
        : editDictionaryDetail({ ...dictionaryDetailData, groupKey: dictionaryData.activeDictionary.key })

    handler
      .then(() => {
        Message.success("操作成功")
        dictionaryDetailVisible.value = false
        params.value = { groupKey: dictionaryData.activeDictionary.key }
      })
      .catch(err => Message.error(extractErrorMsg(err, "操作失败")))
      .finally(() => {
        dictionaryDetailLoading.value = false
      })
  })
}

const dictionaryTableRef = ref()
// 切换启用&禁用状态
const modifyStatus = row => {
  const nextStatus = row.status === "ENABLE" ? "DISABLE" : "ENABLE"
  SystemPrompt(`是否确定${row.status === "ENABLE" ? "禁用" : "启用"}${row.value} `).then(() => {
    modifyDataSourcesInfo(row.wsid, nextStatus)
  })
}

// 删除
const handleDelete = row => {
  SystemPrompt(`您确定要删除字典明细${row.value} `).then(() => modifyDataSourcesInfo(row.wsid, "DEL"))
}

const modifyDataSourcesInfo = (wsid: string, nextStatus: "ENABLE" | "DISABLE" | "DEL") => {
  setDictionaryDetailStatus(wsid, nextStatus, dictionaryData.activeDictionary.key)
    .then(() => {
      Message.success(nextStatus === "DEL" ? "删除数据成功" : "修改状态成功")
      dictionaryTableRef.value?.refreshTableData()
    })
    .catch(err => toastError(err))
}
</script>

<style lang="less" scoped>
.data-dictionary-container {
  width: 100%;
  min-width: 780px;
  height: 100%;
  padding: 16px;
  //   background-color: white;
  box-sizing: border-box;
  display: flex;
  //   flex-direction: column;
  justify-content: space-between;

  .dictionary-list {
    background: #fff;
    width: 300px;
  }

  .dictionary-table-container {
    background: #fff;
    width: 80%;
    box-shadow: 0 2px 8px rgba(0, 35, 114, 0.1);
    .table-header {
      height: 56px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #e1e2e6;
      font-weight: bold;
      font-size: 14px;
      padding-left: 20px;
    }

    .table-content {
      padding: 20px;
      height: calc(100% - 100px);
    }
  }
}
</style>
