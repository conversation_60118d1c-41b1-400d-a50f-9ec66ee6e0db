<template>
  <div class="detail">
    <PdfPreviewComponent :src="pdfSrc"></PdfPreviewComponent>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue"
import { useRoute } from "vue-router"
import { PdfPreviewComponent } from "@/base-components"

const route = useRoute()

const pdfSrc = ref("")

onMounted(async () => {
  pdfSrc.value = `/api/catalog/outpatient-hqms/${route.query?.registerNo}/first-page-export?t=${new Date().getTime()}`
})
</script>

<style lang="scss" scoped>
.detail {
  height: 100%;
}
</style>
