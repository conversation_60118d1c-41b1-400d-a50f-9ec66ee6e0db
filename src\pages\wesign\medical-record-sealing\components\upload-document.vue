<template>
  <div class="document-upload-wrapper">
    <div class="document-content">
      <div v-for="file in files" :key="file.source" class="identification-materials__upload">
        <div v-loading="file?.status === 'uploading'" class="identification-materials__upload-type">
          <VuePdfEmbed
            v-if="file.source"
            :style="{ width: 88 + 'px' }"
            :width="88"
            :source="file.source"
            :page="1"
            disable-text-layer
          />
        </div>

        <i v-if="file?.status !== 'uploading'" class="ri-close-fill delete-icon" @click="deleteFile(file)"></i>

        <i v-if="file?.status !== 'uploading'" class="ri-eye-line preview-icon" @click="previewFile(file)"></i>
      </div>
      <div class="upload-box">
        <el-upload
          class="upload-container"
          drag
          accept=".jpg,.jpeg,.png,.pdf"
          action=""
          :auto-upload="false"
          :show-file-list="false"
          list-type="picture"
          :on-change="handleUploadFile"
        >
          <el-icon><Plus /></el-icon>
        </el-upload>
      </div>
    </div>

    <!-- pdf 预览 -->
    <el-dialog
      v-model="pdfPreviewState.visible"
      class="preview-upload-document-dialog-wrapper"
      :show-close="false"
      :fullscreen="true"
    >
      <div v-loading="pdfPreviewState.loading" style="height: 100vh">
        <span class="return-back" @click="closePdfPreview">关闭</span>
        <div ref="pdfPreviewRef" class="pdf-preview-content"></div>
      </div>
    </el-dialog>
  </div>

  <CameraDialog ref="CameraDialogRef" :submit-loading="uploadingLoading" @submit="takePhoto" />
</template>

<script setup lang="ts">
import { ref, computed, reactive, nextTick } from "vue"
import { ElMessage } from "element-plus"
import { debounce } from "lodash-es"
import PdfObject from "pdfobject"
import VuePdfEmbed from "vue-pdf-embed"
import { PdfPreview } from "@/base-components"
import { CameraDialog } from "@/page-components"
import { useVirtualProgress } from "@/hooks"
import { toastError, Message, defaultPdfRenderConfigs, extractErrorMsg } from "@/utils"
import { uploadSealingFile, getSealSupportFileApi } from "../interface"
import type { UploadUserFile } from "element-plus"

interface UploadFileProps {
  files: any
}

const props = defineProps<UploadFileProps>()

const emits = defineEmits(["update:files"])
const CameraDialogRef = ref()

defineExpose({
  takePhoto: () => {
    CameraDialogRef.value?.openDialog()
  },
  CameraDialogRef
})

const files = computed({
  get: () => {
    console.log("sealForm", props.files)
    return props.files
  },
  set: val => emits("update:files", val)
})

interface FileType extends UploadUserFile {
  fileWsid?: string
  fileName?: string
  wsid?: string
  filePage?: number
  source?: Record<string, any>
}

//缓存了所有上传的文件名称
const { progress, startProgress, endProgress } = useVirtualProgress()
const FILE_MAX_SIZE = 1024 * 1024 * 5 //5M

function takePhoto(files) {
  files.map(file => {
    handleUploadFile(file)
  })
}

const uploadingLoading = ref(false)
const handleUploadFile = async file => {
  if (file?.size > FILE_MAX_SIZE) {
    Message.error("上传文件大小不能超过 5MB!")
    return false
  }

  const fileType = file.raw.type
  const tempDocumentWsid = Math.random().toString(36).slice(-8)
  console.log("tempDocumentWsid", file)
  const data = {
    fileHref: "",
    percentage: progress,
    status: "uploading",
    fileType,
    documentWsid: tempDocumentWsid
  }

  files.value.push(data)

  startProgress()

  const fileData = file.raw
  uploadingLoading.value = true
  uploadSealingFile({ file: fileData, flag: true })
    .then(async res => {
      const fileInfo = res.data.data.fileInfo
      let uploadFileSource = ""
      await getSealSupportFileApi({
        "file-wsid": res.data.data.fileInfo.fileWsid
      })
        .then(res => {
          uploadFileSource = URL.createObjectURL(new Blob([res.data], { type: "application/pdf" }))
          uploadingLoading.value = false
        })
        .catch(err => {
          ElMessage({
            message: extractErrorMsg(err, "文件预览失败"),
            grouping: true,
            type: "error"
          })
          uploadingLoading.value = false
        })
      endProgress()
      const index = files.value.findIndex(item => item.documentWsid === tempDocumentWsid)
      const uploadSuccessFile = {
        ...files.value[index],
        name: fileInfo.name,
        fileType: "application/pdf",
        status: "success",
        fileWsid: fileInfo.fileWsid,
        source: {
          ...defaultPdfRenderConfigs,
          url: uploadFileSource
        },
        filePage: fileInfo.filePage,
        wsid: fileInfo?.fileWsid
      }

      files.value[index] = uploadSuccessFile
      CameraDialogRef.value?.closeDialog()

      ElMessage({
        message: "文件上传成功",
        grouping: true,
        type: "success"
      })
    })
    .catch(err => {
      const index = files.value.findIndex(item => item.documentWsid === tempDocumentWsid)

      // 删除上传失败的文件
      files.value.splice(index, 1)
      ElMessage({
        message: extractErrorMsg(err, "文件上传失败"),
        grouping: true,
        type: "error"
      })
      uploadingLoading.value = false
    })
}

//删除文件
const deleteFile = (file: FileType) => {
  const tempFiles = files.value
  const index = tempFiles.findIndex(__file => __file.fileWsid === file.fileWsid)
  tempFiles.splice(index, 1)
  files.value = tempFiles
}

// pdf 预览
const pdfPreviewState = reactive({
  visible: false,
  loading: false
})

const pdfPreviewRef = ref<InstanceType<typeof PdfPreview>>()

// 关闭pdf预览
const closePdfPreview = () => {
  pdfPreviewState.visible = false
}

// 预览文件
const previewFile = (file: FileType) => {
  pdfPreviewState.visible = true
  if (!file?.source?.url) return
  nextTick(() => {
    PdfObject.embed(file.source?.url, pdfPreviewRef.value)
  })
}
</script>

<style lang="less" scoped>
.document-upload-wrapper {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  .identification-materials {
    display: flex;
    flex-direction: column;
    font-size: 14px;
    color: #f56c6c;
    line-height: 1.5;
    width: 100%;

    &__upload {
      width: 88px;
      height: 124px;
      position: relative;

      &-type {
        height: 100%;
        border-radius: 4px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #ebeef5;
      }
      .preview-icon {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: #fff;
        font-weight: 400;
        line-height: 24px;
        cursor: pointer;
        opacity: 0;
        transition: all 0.3s;
        background: rgba(23, 25, 28, 0.5);

        z-index: 2;
        &:hover {
          opacity: 1;
        }
      }
      .delete-icon {
        position: absolute;
        width: 20px;
        height: 20px;
        line-height: 20px;
        top: -8px;
        right: -8px;
        opacity: 1;
        color: #fff;
        font-size: 20px;
        background-color: red;
        border-radius: 50%;
        cursor: pointer;
        z-index: 10;
      }
    }
  }
  .document-content {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    width: 100%;
  }
  .upload-container {
    height: 124px;
    :deep(.el-upload) {
      width: 100%;
      height: inherit;
      .el-upload-dragger {
        width: 100%;
        height: inherit;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        background: #fff;
        .el-icon {
          color: #409eff;
        }
      }
    }
  }
  .upload-box {
    background: #fff;
    border-radius: 4px;
    width: 88px;
    height: 124px;
    &:hover {
      box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
    }
  }
}
</style>

<style lang="less" scoped>
:deep(.preview-upload-document-dialog-wrapper) {
  color: red;
  :deep(.el-dialog__header) {
    padding: 0;
  }
  .el-dialog__header {
    padding: 0;
  }
  .el-dialog__body {
    padding: 0;
  }
  .pdf-preview-header {
    justify-content: space-between !important;
  }
  .return-back {
    cursor: pointer;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 8px;
    height: 32px;
    line-height: 32px;
    &:hover {
      color: #409eff;
    }
  }
  .pdf-preview-content {
    height: calc(100% - 36px);
  }
}
</style>
