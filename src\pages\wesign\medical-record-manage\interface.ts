import axios from "@/interfaces/axios-instance"
import { toastError } from "@/utils"

interface IBorrowApplyData {
  documentWsids: Array<string>
  timeLimit: string
  applyType: string
  applyReason: string
  viewPermission: string
  exportPermission: string
  printPermission: string
}

/**
 * @method POST
 * @desc   发起借阅申请
 */
export function handleBorrowApplyApi(data: IBorrowApplyData) {
  return axios({
    method: "post",
    url: `/api/document/borrow/apply`,
    data
  })
}

// 获取病案详情
export const getRecordDetailApi = params => {
  return axios({
    method: "get",
    url: `/api/document/document-bags/view/${params.inpNo}`,
    params: {
      sealKey: params.sealKey
    }
  })
}

// 获取病案历史
export const getRecordHistoryApi = params => {
  return axios({
    method: "get",
    url: `/api/document/document-bags/versions/${params.inpNo}`
  })
}

/**
 * @method POST
 * @desc   回收病案
 * @param  recordNumberList 病案号列表
 */
export const recycleRecordApi = (recordNumberList: string[]) => {
  return axios({
    method: "post",
    url: `/api/document/bags/reclaim`,
    data: {
      inpNos: recordNumberList
    }
  })
}

// 导出病案
export const exportRecordApi = (params: {
  recordWsidList: string[]
  fields: string
  sorts?: string
  filters: string
}) => {
  return axios({
    method: "post",
    url: `/api/document/bag/export`,
    responseType: "arraybuffer",
    data: {
      wsids: params.recordWsidList,
      fields: params.fields,
      filters: params.filters,
      sorts: params.sorts || ""
    }
  })
}

/* ===================== 示踪 ====================== */

// 获取示踪信息
export const getRecordTraceInfoApi = params => {
  return axios({
    method: "get",
    url: `/api/document/document-bags/tracer/${params.inpNo}`,
    params: {
      sealKey: params.sealKey
    }
  })
}

/**
 * @method GET
 * @desc   病案管理-获取示踪操作类型
 */
export function getBagOpers() {
  return axios({
    method: "get",
    url: `/api/document/bag/opers`
  })
}

/* ===================== 打印 ====================== */

// 申请打印
export const applyPrintApi = (params: {
  applicatName: string
  applicatIdCrad: string
  patientWsid: string
  patientName: string
  inpNo: string
  patientRelationship: string
  items: any[]
}) => {
  return axios({
    method: "post",
    url: `/api/document/print/application`,
    data: {
      applicatName: params.applicatName,
      applicatIdCrad: params.applicatIdCrad,
      patientWsid: params.patientWsid,
      patientName: params.patientName,
      patientRelationship: params.patientRelationship,
      inpNo: params.inpNo,
      items: params.items
    }
  })
}

// 获取打印套餐
export const getPrintListApi = () => {
  return axios({
    method: "get",
    url: `/api/document/schemes/print-list`
  })
}

/* ===================== 患者查询 ====================== */

// 获取患者列表
export const getPatientListApi = (params: IPaginationRequestParams) => {
  return axios({
    method: "get",
    url: `/api/hospital/patsInHospitals`,
    params
  })
}

/* ===================== 病案收藏 ====================== */

/**
 * @method GET
 * @desc   病案文档-收藏分页列表
 */
export function getCollectListApi(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/document/favorite`,
    params
  })
}

// 取消收藏
export const cancelCollectApi = (recordWsidList: string[]) => {
  return axios({
    method: "post",
    url: `/api/document/favorite/cancel`,
    data: {
      documentBagWsids: recordWsidList
    }
  })
}

// 获取收藏列表
export const getCollectionTagListApi = () => {
  return axios({
    method: "get",
    url: `/api/document/favorite/user-label`
  })
}

// 增加收藏标签
export const addCollectionTagApi = params => {
  return axios({
    method: "post",
    url: `/api/document/favorite/user-label/add`,
    data: {
      labelName: params.labelName
    }
  })
}

// 收藏病案
export function collectMedicalRecordApi(params) {
  return axios({
    method: "post",
    url: `/api/document/favorite/do`,
    data: {
      documentBagWsids: params.documentBagWsids,
      labels: params.labels
    }
  })
}

/* ===================== 病案提交 ====================== */

// 获取文件的提交进度
export const getSubmitProgressApi = (inpNo: string) => {
  return axios({
    method: "get",
    url: `/api/document/bags/${inpNo}/submit-count`
  })
}

// 获取病案同步的分类
export const getClassifyApi = (inpNo: string) => {
  return axios({
    method: "get",
    url: `/api/document/bags/${inpNo}/mrclass`
  })
}

// 同步指定分类的病案
export const syncClassifyApi = (inpNo: string, mrClassCode: string) => {
  return axios({
    method: "get",
    url: `/api/collect/document-sync/mrClass-code`,
    params: {
      inpNo: inpNo,
      mrClassCode: mrClassCode
    },
    timeout: 60000
  })
}

// 检查签名完整性
export const checkSignIntegralityApi = (inpNo: string) => {
  return axios({
    method: "get",
    url: `/api/document/bags/${inpNo}/submit-check/sign`
  })
    .then(res => {
      return res.data.data || {}
    })
    .catch(error => {
      throw error
    })
}

// 检查签名完整性
export const getQCInfoApi = (inpNo: string) => {
  return axios({
    method: "get",
    url: `/api/document/bags/${inpNo}/submit/qc`
  })
    .then(res => {
      return res.data.data
    })
    .catch(error => {
      throw error
    })
}

// 发送整改通知
export const sendModifyMessageApi = (inpNo: string) => {
  return axios({
    method: "post",
    url: `/api/document/bags/${inpNo}/submit/sendRectifyNotice`
  })
}

export interface SubmitMedicalRecordParams {
  inpNo: string // 病案号
  confirmDocumentWsids: string[]
  signInfos?: SignInfo[]
  submitRemark?: string
}

export interface SignInfo {
  documentWsid: string
  templateFormName: string // 表单名称
  jobIds: string[] // 工号组
}

// 提交病案
export const submitMedicalRecordApi = (params: SubmitMedicalRecordParams) => {
  return axios({
    method: "post",
    url: `/api/document/bags/${params.inpNo}/sign-reclaim`,
    data: {
      confirmDocumentWsids: params.confirmDocumentWsids,
      signInfos: params.signInfos || [],
      submitRemark: params.submitRemark
    }
  })
}

interface IUnassignedDocParams {
  inpNo?: string
  beforeDays?: string
}
// 获取待归档的文档列表
export const getInactiveDocumentsApi = (params: IUnassignedDocParams) => {
  return axios({
    method: "get",
    // url: `/api/document/bags/${inpNo}/inactive`
    url: `/api/document/unassigned/docs`,
    params
  })
}

// 获取病案文档树形列表
export const getSubmissionTreeApi = (inpNo: string, sealKey: string) => {
  return axios({
    method: "get",
    url: `/api/document/document-bags/view/${inpNo}`,
    params: { sealKey: sealKey }
  })
}

// 获取需要签名的文件列表
export const getNeedSignDocumentsApi = (inpNo: string) => {
  return axios({
    method: "get",
    url: `/api/document/bags/${inpNo}/need-sign`
  })
}

// 模糊查询当前科室成员
export const getDepartmentMemberApi = (keyword?: string) => {
  return axios({
    method: "get",
    url: `/api/hospital/users/fuzzy-query-current`,
    params: {
      item: keyword || ""
    }
  })
}

// 获取需要签署的表单
export const getSignFormsApi = (documentWsid: string) => {
  return axios({
    method: "get",
    url: `/api/document/platform-sign/${documentWsid}/position`
  })
    .then(res => {
      return res.data.data
    })
    .catch(error => {
      toastError(error)
    })
}

// 删除文档节点
export const deleteFileApi = (documentWsid: string) => {
  return axios({
    method: "post",
    url: "/api/document/bag/file-delete",
    data: { wsids: [documentWsid] }
  })
}

export interface GetSignPositionParams {
  signPositions: {
    formName: string
    keywordData: Record<string, any>[] // form的位置属性
    joIds: string[]
  }[]
}

// 获取每个签名的基于签名框的位置
export const getEverySignPositionApi = (params: GetSignPositionParams) => {
  return axios({
    method: "post",
    url: "/api/document/user-sign-position",
    data: {
      signPositions: params.signPositions
    }
  })
    .then(res => {
      return res.data.data
    })
    .catch(error => {
      toastError(error)
    })
}

// 获取签署者信息 图片/静默签是否开启
export const getSignerInfoApi = (jobIdList: string[]) => {
  return axios({
    method: "get",
    url: "/api/document/user/sign-pic",
    params: { jobIds: jobIdList.join(",") }
  })
    .then(res => {
      return res.data.data
    })
    .catch(error => {
      toastError(error)
    })
}

// 获取病案提交的历史快照
export const getSubmitHistoryApi = (bagWsid: string) => {
  return axios({
    method: "get",
    url: "/api/document/document-bags-snapshot/versions",
    params: { bagWsid: bagWsid, type: "SUBMIT" }
  })
}

// 获取提交快照详情
export const getSubmitSnapshotApi = (wsid: string) => {
  return axios({
    method: "get",
    url: `/api/document/document-bags-snapshot/view/${wsid}`
  })
}

export const getReturnedListApi = (data: IPaginationRequestParams) => {
  return axios({
    method: "get",
    url: `/api/document/complex-query/return`,
    params: data
  })
}
