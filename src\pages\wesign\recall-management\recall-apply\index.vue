<template>
  <PageContainer separate>
    <!-- 搜索区域 -->
    <template #search>
      <SearchForm
        :form-config="recallApplySearchFormConfig"
        :form-state="searchFormState"
        @query-btn-click="handleQuery"
        @reset-btn-click="handleReset"
      />
    </template>

    <!-- 表格展示 -->
    <template #table>
      <CommonTable
        ref="recallApplyTableRef"
        table-id="recallApplyTableIdent"
        :request-params="searchParams"
        :table-columns="recallApplyTableColumns"
        :request-api="getRecallApplyListApi"
      >
        <template #inHospitalDatetime="{ row }">
          {{ formatDatetime(row.inHospitalDatetime) }}
        </template>
        <template #patientPrintCount="{ row }">
          {{ row.patientPrintCount > 0 ? "是" : "否" }}
        </template>
        <template #outHospitalDatetime="{ row }">
          {{ formatDatetime(row.outHospitalDatetime) }}
        </template>
        <template #archiveDatetime="{ row }">
          {{ formatDatetime(row.archiveDatetime) }}
        </template>
        <template #operation="{ row }">
          <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.Recall)" @click="showApplyDialog(row)">
            召回
          </TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>

  <!-- 召回申请弹窗 -->
  <DialogContainer
    v-if="recallApplyDialogVisible"
    v-model:visible="recallApplyDialogVisible"
    title="召回申请"
    :width="400"
    :confirm-callback="handleConfirm"
    :cancel-callback="clearApplyState"
    :close-callback="clearApplyState"
    :confirm-loading="recallApplyState.loading"
  >
    <el-form :model="recallApplyState">
      <el-form-item label="召回原因：">
        <el-select v-model="recallApplyState.recallReason" placeholder="请选择召回原因">
          <el-option
            v-for="item in recallReasonOptions"
            :key="item.value"
            :label="item.value"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="召回时长：">
        <el-select v-model="recallApplyState.recallTimes" placeholder="请选择召回时长">
          <el-option
            v-for="item in recallTimeOptions"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <div v-loading="isLoading" class="tree">
        <div class="tree-title">
          <span>选择文件</span>
          <el-checkbox v-model="treeState.checkAll" size="large" @change="checkAllNodes">全选</el-checkbox>
        </div>
        <div class="tree-content">
          <CommonTree
            ref="applyTreeRef"
            node-key="wsid"
            :show-checkbox="true"
            :data="recallDocuments"
            @check-node="getCheckedNodes"
          />
        </div>
      </div>
    </el-form>
  </DialogContainer>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue"
import { CommonTable, TableButton, CommonTree, DialogContainer, PageContainer } from "@/base-components"
import { SearchForm } from "@/page-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { getRecordViewData, getApproveReasonApi, checkSealedApi, querySystemConfig } from "@/interfaces"
import { useUserStore } from "@/stores"
import { formatDatetime, Message, formatRecordTree, setSpecifiedPropFileNodes, toastError } from "@/utils"
import { RecallFilesProps } from "../config"
import { getRecallApplyListApi, recallApplyApi } from "../interface"
import { recallApplyTableColumns, recallApplySearchFormConfig } from "./config"

const { hasOperationPermission } = useUserStore()

const menuId = "/recall/apply"

const recallApplyTableRef = ref()

const isLoading = ref(false)

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  patientFilterProp: "patientName",
  patientFilterValue: "",
  inHospitalDatetime: "",
  outHospitalDatetime: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/*========================= 召回申请请求 ======================= */

interface RecallApplyProps {
  recallFiles: Array<RecallFilesProps>
  recallReason: string
  recallTimes: string
  inpNo: string
  loading: boolean
}
const recallApplyState = reactive<RecallApplyProps>({
  inpNo: "",
  recallTimes: "",
  recallReason: "",
  recallFiles: [],
  loading: false
})

interface RecallSearchOptionsProps {
  value: string
  key: string
  [propName: string]: any
}
// 召回原因数据源
const recallReasonOptions = ref<Array<RecallSearchOptionsProps>>([])

// 召回时长数据源
const recallTimeOptions = ref<Array<RecallSearchOptionsProps>>([])

// 获取召回原因数据源
async function getQueryDictionaryReasonDetailByGroup() {
  await getApproveReasonApi({ groupKey: "RECALL_REASON" }).then(resData => {
    recallReasonOptions.value = resData
    recallApplyState.recallReason = resData[0].value
  })
}

// 获取召回时长数据源
async function getQueryDictionaryTimeDetailByGroup() {
  await getApproveReasonApi({ groupKey: "RECALL_TIMES" }).then(resData => {
    recallTimeOptions.value = resData
    recallApplyState.recallTimes = resData[0].key
  })
}

// 召回申请需要的文件列表源
const recallDocuments = ref<any>([])

// 获取召回申请需要的文件列表源
const showApplyDialog = (row: any) => {
  if (row.patientPrintCount > 0 && printRecall.value === "NO") {
    return Message.error("当前病案患者已打印，不允许召回")
  }
  if (!row?.inpNo) {
    return Message.error("病案号为空，无法发起召回申请")
  }
  isLoading.value = true
  // 如果病案被封存，不能发起召回申请，需要先在其他地方解除封存
  checkSealedApi(row.inpNo)
    .then(isSealed => {
      isLoading.value = false
      if (isSealed) return Message.error("病案已封存，无法发起召回申请，请先解除封存")

      recallApplyDialogVisible.value = true
      recallApplyState.inpNo = row.inpNo
      if (row?.inpNo) {
        getRecordViewData({ inpNo: row.inpNo })
          .then(res => {
            const data = formatRecordTree(res.data.data.treeInfo)
            const baseData = setSpecifiedPropFileNodes(data, "disableCheckbox", true)
            recallDocuments.value = baseData
          })
          .catch(error => {
            toastError(error, "召回申请失败")
          })
      }
    })
    .catch(error => {
      isLoading.value = false
      toastError(error, "召回申请失败")
    })
}

const printRecall = ref("")

onMounted(async () => {
  isLoading.value = true
  await getQueryDictionaryReasonDetailByGroup()
  await getQueryDictionaryTimeDetailByGroup()
  printRecall.value = (await querySystemConfig({ type: "FUNCTION" }))?.data?.data?.find(
    item => item.key === "printRecall"
  )?.value
  console.log(`output->printRecall.value`, printRecall.value)
  isLoading.value = false
})

/*========================= 召回弹窗 ======================= */

const recallApplyDialogVisible = ref(false)

// 清空表单数据
const clearApplyState = () => {
  recallApplyDialogVisible.value = false
  recallApplyState.inpNo = ""
  recallApplyState.recallTimes = recallTimeOptions.value[0]?.key
  recallApplyState.recallReason = recallReasonOptions.value[0]?.value
  recallApplyState.recallFiles = []
  treeState.checkAll = false
}

// 发起召回请求
const handleConfirm = () => {
  const recallFiles = recallApplyState.recallFiles.map(item => {
    return {
      documentWsid: item.documentWsid,
      mrClassCode: item.mrClassCode,
      fileWsid: item.fileWsid,
      title: item.title,
      homePageFlag: item.homePageFlag
    }
  })

  if (!recallFiles || recallFiles.length === 0) {
    return Message.warning("要召回的文件不能为空,请选择召回文件")
  }
  // 如果病案被封存，不能发起召回申请，需要先在其他地方解除封存
  checkSealedApi(recallApplyState.inpNo).then(isSealed => {
    if (isSealed) return Message.error("病案已封存，无法发起召回申请，请先解除封存")

    const params = {
      inpNo: recallApplyState.inpNo,
      recallTimes: recallApplyState.recallTimes,
      recallReason: recallApplyState.recallReason,
      recallFiles: recallFiles,
      // 召回时长描述
      recallTimesDescribe: recallTimeOptions.value.find(item => item.key === recallApplyState.recallTimes)?.value
    }
    recallApplyState.loading = true

    recallApplyApi(params)
      .then(() => {
        Message.success("召回申请成功")
        clearApplyState()
        recallApplyTableRef.value?.refreshTableData()
      })
      .catch(error => {
        toastError(error, "召回申请失败")
      })
      .finally(() => {
        recallApplyState.loading = false
      })
  })
}

/*================ tree相关 ==================*/

const applyTreeRef = ref()

const treeState = reactive({
  checkAll: false
})

// 全选所有文件
const checkAllNodes = checkAll => {
  if (!checkAll) {
    recallApplyState.recallFiles = []
  }
  applyTreeRef.value.checkAllNodes(checkAll)
}

// 获取用户勾选的nodes，取出其中的file
const getCheckedNodes = (_, checkedNodes) => {
  const checkedFileList = checkedNodes
    .filter(node => {
      return node.type === "FILE"
    })
    .map(v => {
      return {
        documentWsid: v.wsid,
        mrClassCode: v.mrClassCode,
        fileWsid: v.fileWsid,
        title: v.title,
        homePageFlag: v.homePageFlag
      }
    })
  recallApplyState.recallFiles = checkedFileList
}
</script>

<style lang="less" scoped>
.tree {
  margin-top: 10px;
  border: 1px solid #e5e5e5;
}

.tree-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #333;
  background: #e5e5e5;
  padding: 0 10px;
}

.tree-content {
  padding: 0 10px;
  min-height: 20vh;
  max-height: 25vh;
  overflow-y: scroll;
}
</style>
