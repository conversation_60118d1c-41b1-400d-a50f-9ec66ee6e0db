<template>
  <div class="integrated-query-container">
    <CardContainer class="query-left-container" title="检索项">
      <div class="left-card-body">
        <el-input v-model="searchQueryTerm" placeholder="请输入检索项名称" :clearable="true" :prefix-icon="Search" />
        <div v-loading="queryTermListLoading" class="query-term-list-container">
          <div v-if="filterQueryTermList.length > 0" class="query-term-list">
            <div
              v-for="item in filterQueryTermList"
              :key="item.prop"
              class="query-term-item"
              :class="selectedTermProp.includes(item.prop) ? 'active-term' : ''"
              @click="addQueryTermItem(item)"
            >
              {{ item.label }}
            </div>
          </div>
          <el-empty v-else description="暂无数据"></el-empty>
        </div>
      </div>
    </CardContainer>
    <div class="query-right-container">
      <QuerySearch
        v-model:query-filters="requestFilters"
        :query-term="selectedQueryTerm"
        :delete-term="deleteQueryTerm"
      />

      <CommonTable
        class="medical-record-table"
        table-id="recordTable"
        :table-columns="queryMedicalRecordColumns"
        :request-api="getIntegratedQueryListApi"
        :request-params="{ filters: requestFilters }"
      >
        <template #operation="{ row }">
          <TableButton @click="handleRowClick(row)">查看</TableButton>
        </template>
      </CommonTable>
    </div>

    <SealDialog ref="sealDialogRef" :selected-row="tableState.row" :confirm-callback="confirmOperation" />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref } from "vue"
import { useRouter } from "vue-router"
import { Search } from "@element-plus/icons-vue"
import { CardContainer, CommonTable, TableButton } from "@/base-components"
import { SealDialog } from "@/page-components"
import { getIntegratedQueryTermsApi } from "@/interfaces"
import { Message, encryptStr, toastError } from "@/utils"
import { getIntegratedQueryListApi } from "../interface"
import QuerySearch from "./component/query-search/index.vue"
import { queryMedicalRecordColumns, QueryTermItemType } from "./config"

const router = useRouter()

/* =========================左侧检索项=========================*/
const queryTermListLoading = ref(false)
const queryTermList = ref<Array<QueryTermItemType>>([])
const selectedQueryTerm = ref<Array<Record<string, any>>>([])
const searchQueryTerm = ref("")

const filterQueryTermList = computed(() => {
  return queryTermList.value.filter(item => item.label.includes(searchQueryTerm.value))
})

onMounted(() => {
  queryTermListLoading.value = true
  getIntegratedQueryTermsApi()
    .then(res => {
      queryTermList.value = res.data.data || []
    })
    .catch(err => {
      toastError(err, "获取检索项失败")
    })
    .finally(() => {
      queryTermListLoading.value = false
    })
})

const selectedTermProp = computed(() => {
  return selectedQueryTerm.value.reduce((res, item) => {
    return res.concat(item.prop)
  }, [])
})

// 添加检索项
function addQueryTermItem(row) {
  if (!selectedTermProp.value.includes(row.prop)) {
    selectedQueryTerm.value.push(row)
  } else Message.warning("该检索项已添加")
}

// 删除检索项
function deleteQueryTerm(prop) {
  const termIndex = selectedQueryTerm.value.findIndex(item => item.prop === prop)
  if (termIndex !== -1) selectedQueryTerm.value.splice(termIndex, 1)
}

/* =========================表格操作=========================*/
const requestFilters = ref("")
const sealDialogRef = ref()
const tableState = reactive({
  row: null as null | Record<string, any>
})

function handleRowClick(row) {
  //
  tableState.row = row
  sealDialogRef.value?.checkSealed()
}

function confirmOperation() {
  const query = { inpNo: tableState.row?.inpNo, wsid: tableState.row?.wsid }
  if (sealDialogRef.value.secretKey) {
    query["secretKey"] = encryptStr(sealDialogRef.value.secretKey)
  }
  router.push({
    path: "/system-manage/integrated-query/view",
    query: query
  })
}
</script>

<style lang="less" scoped>
.integrated-query-container {
  width: 100%;
  height: 100%;
  display: flex;

  .query-left-container {
    width: 265px;
    height: 100%;
    margin-right: 16px;
    .left-card-body {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      row-gap: 10px;

      .query-term-list-container {
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 1;
        min-height: 0px;
        overflow-y: auto;
      }

      .query-term-list {
        height: 100%;
        width: 100%;
        display: flex;
        flex-direction: column;
        .query-term-item {
          height: 40px;
          padding-left: 16px;
          padding-right: 5px;
          display: flex;
          align-items: center;
          cursor: pointer;
          flex-shrink: 0;
          &:hover {
            background-color: #f5f5f6;
            font-weight: 600;
          }
        }

        .active-term {
          background-color: #f5f5f6;
          font-weight: 600;
        }
      }
    }
  }

  .query-right-container {
    flex: 1;
    min-width: 0px;
    height: 100%;
    background: #fff;
    padding: 24px 16px;
    box-sizing: border-box;
    box-shadow: 0 2px 8px rgba(0, 35, 114, 0.1);
    display: flex;
    flex-direction: column;

    .medical-record-table {
      flex: 1;
      min-height: 0px;
    }
  }
}
</style>
