<template>
  <DialogContainer
    v-model:visible="visible"
    title="特殊病案"
    :no-footer="type === 'check'"
    :width="500"
    :close-callback="close"
    :confirm-callback="setSpecialInfo"
  >
    <el-form ref="formRef" v-loading="loading" :model="specialInfo" label-width="110px">
      <el-form-item label="特殊病案属性" prop="properties">
        <el-select v-model="specialInfo.properties" multiple placeholder="请选择" :disabled="type === 'check'">
          <el-option
            v-for="option in specialStatusOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
            :disabled="option.disabled"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="其他说明" prop="desc">
        <el-input
          v-model="specialInfo.desc"
          :rows="5"
          type="textarea"
          placeholder="请输入其他说明"
          :disabled="type === 'check'"
        />
      </el-form-item>

      <!-- 查看单独字段 -->
      <template v-if="type === 'check'">
        <el-form-item label="登记时间">
          {{ specialInfo.modifiedDatetime }}
        </el-form-item>
        <el-form-item label="登记人">
          {{ specialInfo.operatorName }}
        </el-form-item>
      </template>
    </el-form>
  </DialogContainer>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue"
import { DialogContainer } from "@/base-components"
import { getApproveReasonApi } from "@/interfaces"
import { formatDatetime, Message, toastError } from "@/utils"
import { getSpecialInfoApi, setSpecialInfoApi } from "../interface"

const props = defineProps<{ selectedRow: Record<string, any> }>()
const emits = defineEmits(["success"])

onMounted(() => {
  getSpecialStatusOptions()
})

const type = ref<"edit" | "check">("edit")
const loading = ref(false)
const specialInfo = ref<Record<string, any>>({
  properties: [],
  desc: "",
  operatorName: "",
  modifiedDatetime: ""
})

const getSpecialInfo = () => {
  if (props.selectedRow.isSpecialEnum !== "YES") return
  loading.value = true
  getSpecialInfoApi(props.selectedRow.inpNo)
    .then(res => {
      loading.value = false
      if (res?.data?.data) {
        specialInfo.value.desc = res.data.data.desc
        specialInfo.value.operatorName = res.data.data.operatorName
        specialInfo.value.modifiedDatetime = formatDatetime(res.data.data.modifiedDatetime)
        const properties = res.data.data.properties.filter(prop => prop.key)
        specialInfo.value.properties = properties.map(prop => prop.key)
        properties.forEach(prop => {
          if (prop.key && !specialStatusOptions.value.find(option => option.value === prop.key)) {
            specialStatusOptions.value.push({
              label: prop.desc,
              value: prop.key,
              disabled: true
            })
          }
        })
      }
    })
    .catch(error => {
      loading.value = false
      toastError(error)
    })
}

/* ============== 特殊病案属性获取 ============= */

const specialStatusOptions = ref<Array<{ label: string; value: string; disabled?: boolean }>>([])

const getSpecialStatusOptions = () => {
  getApproveReasonApi({ groupKey: "BAG_SPECIAL_PROPERTY" }).then(res => {
    specialStatusOptions.value = res.map(item => {
      return {
        label: item.value,
        value: item.key
      }
    })
  })
}

/* ============== 设置特殊病案属性 ============= */

const setSpecialInfo = () => {
  const properties = specialInfo.value.properties.map(key => ({
    key: key,
    desc: specialStatusOptions.value.find(option => option.value === key)?.label
  }))
  console.log(properties)
  setSpecialInfoApi(props.selectedRow.inpNo, properties, specialInfo.value.desc).then(() => {
    Message.success("设置成功！")
    close()
    emits("success")
  })
}

/* ============== form ============= */

const formRef = ref()

/* ============== dialog ============= */

const visible = ref(false)

const show = _type => {
  type.value = _type
  visible.value = true
  getSpecialInfo()
}

const close = () => {
  visible.value = false
  specialInfo.value = { properties: [], desc: "" }
}

defineExpose({
  show,
  close
})
</script>
