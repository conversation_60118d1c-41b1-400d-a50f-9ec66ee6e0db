import { EditorView } from "@codemirror/view"
import type { TableColumnItem, BaseOptionItem } from "@/types"
import type { FormRules } from "element-plus"

export const menuId = "/collect/way"

export const tabsRouterList = [
  { path: "/collect/way/database", label: "数据库" },
  { path: "/collect/way/ftp", label: "FTP" },
  { path: "/collect/way/interface", label: "接口配置" }
]

/* ======================== 选项配置 ======================== */

// 数据类型选项
export const dataTypeOptions = [
  { label: "病案内容", value: "MR_CONTENT" },
  { label: "科室部门信息", value: "DEPARTMENT" },
  { label: "医护人员信息", value: "HOSPITAL_USER" },
  { label: "在院患者信息", value: "PATIENT_IN_HOSPITAL" },
  { label: "患者基础信息", value: "MR_BASE_INFO" },
  { label: "出院诊断信息", value: "MR_DISCHARGE_DIAGNOSIS" },
  { label: "手术信息", value: "MR_OPERATION" },
  { label: "费用信息", value: "MR_FEE_INFO" }
]

export const filterDataTypeOptions = [
  { label: "病案内容", value: "MR_CONTENT" },
  { label: "在院患者信息", value: "PATIENT_IN_HOSPITAL" }
]

export const charsetOptions = [
  { value: "GBK", label: "GBK" },
  { value: "GB2312", label: "GB2312" },
  { value: "UTF-8", label: "UTF-8" }
]

export const methodTypeOptions = [
  { label: "GET", value: "GET" },
  { label: "POST", value: "POST" }
]

export const resultTypeOptions = [
  { label: "JSON", value: 1, status: "JSON" },
  { label: "XML", value: 2, status: "XML" },
  { label: "TEXT_XML", value: 3, status: "TEXT_XML" }
]

// 触发方式
export const triggerTypeOptions = [
  { label: "定时触发", value: "TIME" },
  { label: "业务触发", value: "BUSINESS" }
]

// 业务类型
export const sceneOptions = [
  { label: "住院", value: "IN_HOSPITAL" },
  { label: "门诊", value: "OUT_HOSPITAL" },
  // { label: "急诊", value: "URGENT_CARE" },
  { label: "体检", value: "PHYSICAL_EXAMINATION" },
  { label: "历史住院", value: "IN_HOSPITAL_HISTORY" } // TODO 历史住院枚举
]

// 自增固定时间范围选项
export const fixedTimeRangeOptions = [
  { label: "1天", value: "ONE_DAY" },
  { label: "2天", value: "TWO_DAY" },
  { label: "1小时", value: "ONE_HOUR" },
  { label: "2小时", value: "TWO_HOUR" },
  { label: "6小时", value: "FIVE_HOUR" },
  { label: "12小时", value: "TWELVE_HOUR" }
]

// 自增类型
export const incrementTypeOptions = [
  { label: "时间", value: "TIME" },
  { label: "数字", value: "NUMBER" }
]

export function getDataTypeDesc(val: string) {
  return dataTypeOptions.find(option => option.value === val)?.label || "--"
}

export const getTriggerTypeDesc = (val: string) => {
  return triggerTypeOptions.find(option => option.value === val)?.label || "--"
}

export const getSceneDesc = (val: string) => {
  return sceneOptions.find(option => option.value === val)?.label || "--"
}

// 采集模式
export const extractTypeOptions = [
  { label: "全量", value: "ALL" },
  { label: "增量", value: "INCREMENT" },
  { label: "固定时间范围", value: "FIXED_TIME_RANGE" }
]

// 返回数据类型
export const resultDataTypeOptions = [
  { label: "文件", value: "FILE" },
  { label: "数据", value: "DATA_KEY_VALUE" }
]

export const getResultDataTypeDesc = (val: string) => {
  return resultDataTypeOptions.find(option => option.value === val)?.label || "--"
}

/* ======================== 数据库 ======================== */

export const databaseTableColumns: Array<TableColumnItem> = [
  { prop: "name", label: "名称", minWidth: 200 },
  { prop: "triggerType", label: "触发方式", minWidth: 120 },
  { prop: "scene", label: "业务类型", minWidth: 120 },
  { prop: "systemName", label: "厂商系统", minWidth: 120 },
  { prop: "dataSourceName", label: "数据源名称", minWidth: 150 },
  { prop: "dataType", label: "数据类型", minWidth: 120 },
  { prop: "mrClassName", label: "文书分类", minWidth: 150 },
  { prop: "querySql", label: "SQL语句配置", minWidth: 200, showOverflowTooltip: false },
  { prop: "cron", label: "采集频率(cron)", minWidth: 180 },
  { prop: "status", label: "状态", minWidth: 100 },
  { prop: "operation", label: "操作", width: 280, fixed: "right" }
]

export const databaseFormRules: FormRules = {
  name: [{ required: true, message: "请输入数据库名称", trigger: "blur" }],
  triggerType: [{ required: true, message: "请选择触发方式", trigger: "blur" }],
  scene: [{ required: true, message: "请选择业务类型", trigger: "blur" }],
  systemWsid: [{ required: true, message: "请选择厂商系统", trigger: "blur" }],
  dataSourceWsid: [{ required: true, message: "请选择数据源名称", trigger: "blur" }],
  dataType: [{ required: true, message: "请选择数据类型", trigger: "blur" }],
  mrClassCode: [{ required: true, message: "请选择文书分类", trigger: "blur" }],
  querySql: [{ required: true, message: "请输入SQL语句配置", trigger: "blur" }],
  resultDataType: [{ required: true, message: "请选择返回数据类型", trigger: "blur" }],
  templateWsid: [{ required: true, message: "请选择关联模板", trigger: "blur" }],
  charset: [{ required: true, message: "请选择编码格式", trigger: "blur" }],
  extractType: [{ required: true, message: "请选择采集模式", trigger: "blur" }],
  incrementFieldKey: [{ required: true, message: "请输入自增key", trigger: "blur" }],
  fixedTimeFieldKey: [{ required: true, message: "请输入固定时间自增key", trigger: "blur" }],
  incrementFieldType: [{ required: true, message: "请选择自增key类型", trigger: "blur" }],
  incrementLastValue: [{ required: true, message: "请输入/选择最近自增值", trigger: "blur" }],
  incrementDelta: [{ required: true, message: "请输入增幅", trigger: "blur" }],
  fixedTimeRange: [{ required: true, message: "请选择时间范围", trigger: "blur" }],
  cron: [{ required: true, message: "请输入采集频率", trigger: "blur" }]
}

/* ======================== 接口配置 ======================== */

export const interfaceTableColumns: Array<TableColumnItem> = [
  { prop: "name", label: "接口名称", minWidth: 200 },
  { prop: "triggerType", label: "触发方式", minWidth: 120 },
  { prop: "scene", label: "业务类型", minWidth: 120 },
  { prop: "systemName", label: "厂商系统", minWidth: 120 },
  { prop: "dataType", label: "数据类型", minWidth: 120 },
  { prop: "mrClassName", label: "文书分类", minWidth: 150 },
  { prop: "url", label: "URL", minWidth: 120 },
  { prop: "method", label: "请求方式", minWidth: 120 },
  { prop: "authorization", label: "认证信息", minWidth: 120 },
  { prop: "queryParams", label: "请求参数", minWidth: 120 },
  { prop: "bodyParams", label: "请求体", minWidth: 120 },
  { prop: "resultType", label: "返回体", minWidth: 120 },
  { prop: "resultField", label: "返回数据取值", minWidth: 120 },
  { prop: "timeout", label: "超时时间(秒)", minWidth: 120 },
  { prop: "cron", label: "采集频率(cron)", minWidth: 180 },
  { prop: "status", label: "状态", minWidth: 90 },
  { prop: "operation", label: "操作", width: 280, fixed: "right" }
]

export const interfaceFormRules: FormRules = {
  name: [{ required: true, message: "请输入接口名称", trigger: "blur" }],
  triggerType: [{ required: true, message: "请选择触发方式", trigger: "blur" }],
  scene: [{ required: true, message: "请选择业务类型", trigger: "blur" }],
  systemWsid: [{ required: true, message: "请选择厂商系统", trigger: "blur" }],
  dataType: [{ required: true, message: "请选择数据类型", trigger: "blur" }],
  mrClassCode: [{ required: true, message: "请选择标准文书类型", trigger: "blur" }],
  url: [{ required: true, message: "请输入URL", trigger: "blur" }],
  method: [{ required: true, message: "请选择请求方式", trigger: "blur" }],
  // queryParams: [{ required: true, message: "输入请求参数", trigger: "blur" }],
  // bodyParams: [{ required: true, message: "输入请求体", trigger: "blur" }],
  contentType: [{ required: true, message: "输选择content-type", trigger: "blur" }],
  resultType: [{ required: true, message: "请选择响应体类型", trigger: "blur" }],
  resultField: [{ required: true, message: "请输入返回数据取值", trigger: "blur" }],
  resultDataType: [{ required: true, message: "请选择返回数据类型", trigger: "blur" }],
  templateWsid: [{ required: true, message: "请选择关联模板", trigger: "blur" }],
  charset: [{ required: true, message: "请选择编码格式", trigger: "blur" }],
  extractType: [{ required: true, message: "请选择采集模式", trigger: "blur" }],
  incrementFieldKey: [{ required: true, message: "请输入自增key", trigger: "blur" }],
  fixedTimeFieldKey: [{ required: true, message: "请输入固定时间自增key", trigger: "blur" }],
  incrementFieldType: [{ required: true, message: "请选择自增key类型", trigger: "blur" }],
  incrementLastValue: [{ required: true, message: "请输入/选择最近自增值", trigger: "blur" }],
  incrementDelta: [{ required: true, message: "请输入增幅", trigger: "blur" }],
  fixedTimeRange: [{ required: true, message: "请选择时间范围", trigger: "blur" }],
  cron: [{ required: true, message: "请输入cron", trigger: "blur" }]
}

/* ======================== FTP ======================== */

export const ftpTableColumns: Array<TableColumnItem> = [
  { prop: "name", label: "名称", minWidth: 120 },
  { prop: "triggerType", label: "触发方式", minWidth: 120 },
  { prop: "scene", label: "业务类型", minWidth: 120 },
  { prop: "systemName", label: "厂商系统", minWidth: 120 },
  { prop: "ftpDataSourceName", label: "数据源名称", minWidth: 120 },
  { prop: "dataType", label: "数据类型", minWidth: 120 },
  { prop: "mrClassName", label: "文书分类", minWidth: 120 },
  { prop: "parseEl", label: "解析表达式", minWidth: 120 },
  { prop: "cron", label: "采集频率(cron)", minWidth: 180 },
  { prop: "timeout", label: "超时时间（秒）", minWidth: 140 },
  { prop: "status", label: "状态", width: 90 },
  { prop: "operation", label: "操作", width: 280, fixed: "right" }
]

export const ftpFormRules: FormRules = {
  name: [{ required: true, message: "请输入FTP名称", trigger: "blur" }],
  triggerType: [{ required: true, message: "请选择触发方式", trigger: "blur" }],
  scene: [{ required: true, message: "请选择业务类型", trigger: "blur" }],
  systemWsid: [{ required: true, message: "请选择厂商系统", trigger: "blur" }],
  ftpSourceWsid: [{ required: true, message: "请输入数据源名称", trigger: "blur" }],
  dataType: [{ required: true, message: "请选择数据类型", trigger: "blur" }],
  mrClassCode: [{ required: true, message: "请选择标准文书类型", trigger: "blur" }],
  parseEl: [{ required: true, message: "请输入解析表达式", trigger: "blur" }],
  cron: [{ required: true, message: "请输入采集频率", trigger: "blur" }]
}

/* ============== 记录 ================ */

export const recordTableColumns: Array<TableColumnItem> = [
  { prop: "executionTime", label: "采集时间", minWidth: 200 },
  { prop: "countFile", label: "采集数量", minWidth: 120 },
  { prop: "executor", label: "操作人", minWidth: 120 },
  { prop: "errormSG", label: "异常情况", minWidth: 120 }
]

export const myTheme = EditorView.theme(
  {
    // 输入的字体颜色
    "&": {
      color: "#0052D9",
      backgroundColor: "#FFFFFF"
    },
    ".cm-content": {
      caretColor: "#0052D9"
    },
    // 激活背景色
    ".cm-activeLine": {
      backgroundColor: "#FFFFDD"
    },
    // 激活序列的背景色
    ".cm-activeLineGutter": {
      backgroundColor: "#FAFAFA"
    },
    //光标的颜色
    "&.cm-focused .cm-cursor": {
      borderLeftColor: "#0052D9"
    },
    // 选中的状态
    "&.cm-focused .cm-selectionBackground, ::selection": {
      backgroundColor: "#0052D9",
      color: "#dcdfe6"
    },
    // 左侧侧边栏的颜色
    ".cm-gutters": {
      backgroundColor: "#FFFFFF",
      color: "#ddd", //侧边栏文字颜色
      border: "none"
    }
  },
  { dark: true }
)
