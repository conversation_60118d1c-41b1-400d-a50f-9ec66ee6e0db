<template>
  <el-dialog v-model="visible" :width="500" class="verify-dialog" title="" :close-callback="closeSignDialog" no-footer>
    <div v-if="signData" class="envelope-verifier-detail">
      <div class="verify-title">
        <div v-if="signData?.signInfo.modified">
          <span class="color-error">签名已被篡改</span>
        </div>
        <div v-else-if="signData?.certInfo.certInvalid">
          <span class="color-warning">签名存在风险</span>
        </div>
        <div v-else-if="!signData?.certInfo.certAgainstRootValid">
          <span class="color-warning">签名超出平台信任范围</span>
        </div>
        <div v-else>
          <span class="color-success">签名有效且可信</span>
        </div>
      </div>
      <div class="verify-title-info">
        自签名以来,
        <template v-if="signData?.signInfo.modified">
          <strong>文档已被修改</strong>
        </template>
        <template v-else>文档未被修改</template>
        ,
        <template v-if="signData?.certInfo.certInvalid">
          <strong>用户证书无效</strong>
        </template>
        <template v-else>用户证书有效</template>
        ,
        <template v-if="signData?.certInfo.tsaResult.verifyTimeStamp">时间戳有效</template>
        <template v-else>
          <strong>时间戳无效</strong>
        </template>
      </div>
      <div class="verify-info-block">
        <el-descriptions title="签名详情" :column="1">
          <el-descriptions-item label="签者姓名:">
            {{ getCNName(signData?.signInfo?.signUser) }}
          </el-descriptions-item>
          <el-descriptions-item v-if="signData?.signInfo?.signDatetime" label="签名时间">
            {{ formatDatetime(signData?.signInfo?.signDatetime) }}
          </el-descriptions-item>
          <el-descriptions-item v-if="signData?.signInfo?.signReason" label="签名原因:">
            {{ signData?.signInfo?.signReason }}
          </el-descriptions-item>
          <el-descriptions-item v-if="signData?.signInfo?.signLocation" label="签者地点:">
            {{ signData?.signInfo?.signLocation }}
          </el-descriptions-item>
          <el-descriptions-item label="签名值:">
            <el-popover placement="top-start" width="200" trigger="hover" :content="signData?.signInfo?.signValue">
              <template #reference>
                <span class="small-text">
                  {{ signData?.signInfo?.signValue }}
                </span>
              </template>
            </el-popover>
          </el-descriptions-item>
        </el-descriptions>
        <div v-if="signData?.signInfo?.page" class="page-length">该签名位于第{{ signData?.signInfo?.page }}页</div>
        <el-descriptions title="证书详情" :column="1">
          <el-descriptions-item label="证书持有者:">
            {{ getSubjectCNName(signData?.certInfo?.certSubject) }}
          </el-descriptions-item>
          <el-descriptions-item label="证书序列号:">
            {{ signData?.certInfo?.certSerialNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="证书发行者:">
            <el-popover
              placement="top-start"
              width="200"
              trigger="hover"
              :content="getSubjectCNName(signData?.certInfo?.certIssuer)"
            >
              <template #reference>
                <span class="small-text">
                  {{ getSubjectCNName(signData?.certInfo.certIssuer) }}
                </span>
              </template>
            </el-popover>
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions title="时间戳详情" :column="1">
          <el-descriptions-item label="是否嵌入时间戳:">
            <span v-if="signData?.certInfo.tsaResult.timeStampEmbedded">已嵌入时间戳</span>
            <span v-else>未嵌入时间戳</span>
          </el-descriptions-item>
          <el-descriptions-item label="签名时间戳时间:">
            {{ formatDatetime(signData?.certInfo.tsaResult.timeStampDate) }}
          </el-descriptions-item>
          <el-descriptions-item label="时间戳验证是否有效:">
            <span v-if="signData?.certInfo?.tsaResult.timeStampEmbedded">有效验证</span>
            <span v-else>无效验证</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from "vue"
import { formatDatetime } from "@/utils"

const props = defineProps<{
  visible: boolean
  signData: Record<string, any>
}>()
const signData = computed(() => props.signData)

const emits = defineEmits(["update:visible"])
const closeSignDialog = () => {
  emits("update:visible", false)
}

const visible = computed({
  get: () => props.visible,
  set: val => emits("update:visible", val)
})

// 获取签名者姓名
function getCNName(cn) {
  if (!cn) return cn
  const RegEx = cn.match(/([^@]*)(@[^@]*@([^@]*)@[^@]*)?/)
  return RegEx[1] + (RegEx[3] ? "(" + RegEx[3] + ")" : "")
}

// 获取证书名称
function getSubjectCNName(subject) {
  return getCNName(subject.match(/CN=(.*)?,?/)[1])
}
</script>

<style lang="less" scoped>
.verify-dialog {
  :deep(.el-dialog__header) {
    padding: 10px;
  }
  :deep(.el-dialog__body) {
    padding: 10px 20px;
  }

  .small-text {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
  }
  .verify-title {
    text-align: center;
    padding-bottom: 10px;
    span {
      display: inline-block;
      color: #fff;
      padding: 0 47px;
      font-size: 14px;
      border-radius: 12px;
      height: 24px;
      line-height: 24px;
    }
    .color-success {
      background-color: #51c78a;
    }
    .color-error {
      background-color: #fe4f3d;
    }
    .color-warning {
      background-color: #e6a23c;
    }
  }
  .verify-title-info {
    padding-bottom: 20px;
    text-align: center;
  }
  .verify-info-block {
    :deep(.el-descriptions__header) {
      margin-bottom: 12px;
      margin-top: 10px;
    }
    :deep(.el-descriptions__title) {
      font-size: 14px;
      color: #333;
    }
    :deep(.el-descriptions-item) {
      padding-bottom: 2px;
    }
    :deep(.el-descriptions__label) {
      width: fit-content;
      white-space: nowrap;
      color: #81878e;
    }
    :deep(.el-descriptions__content) {
      color: #999;
      flex: 1;
      min-width: 0px;
      word-break: break-all;
    }
    :deep(.el-descriptions__cell) {
      display: flex;
      padding-bottom: 6px;
    }
  }
}
</style>
