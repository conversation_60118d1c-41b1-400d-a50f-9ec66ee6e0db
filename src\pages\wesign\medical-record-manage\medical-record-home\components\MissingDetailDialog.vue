<template>
  <DialogContainer v-model:visible="visible" title="登记详情" :width="1000" no-footer :confirm-callback="close">
    <!-- 缺失登记表格 -->
    <BaseTable :loading="loading" :data="tableData" :columns="missingReportRecordTableColumns" border>
      <template #status="{ row }">
        <el-tag v-if="row.status === MissingReportStatusEnum.WAIT_SUBMIT" type="warning">待补交</el-tag>
        <el-tag v-else-if="row.status === MissingReportStatusEnum.COMPLETE_SUBMIT" type="success">已补交</el-tag>
      </template>

      <template #predictDatetime="{ row }">
        {{ formatDate(row.predictDatetime) }}
      </template>
      <template #mrClassCode="{ row }">
        {{ $props.getRecordType(row.mrClassCode) }}
      </template>
      <template #createdDatetime="{ row }">
        {{ formatDate(row.createdDatetime) }}
      </template>
      <template #type="{ row }">
        <span v-if="row.type === MissingReportTypeEnum.ELECTRON">电子</span>
        <span v-else-if="row.type === MissingReportTypeEnum.PAPER">纸质</span>
      </template>
    </BaseTable>
  </DialogContainer>
</template>

<script setup lang="ts">
import { ref } from "vue"
import { DialogContainer, BaseTable } from "@/base-components"
import { MissingReportStatusEnum, MissingReportTypeEnum } from "@/configs"
import { formatDate, toastError } from "@/utils"
import { getMissingReportDetailApi } from "../interface"
import type { TableColumnItem } from "@/types"

const props = defineProps<{ selectedRow: any; getRecordType: any }>()

const loading = ref(false)

// 打开登记详情
const getMissingDetail = () => {
  loading.value = true
  // 获取已经提交的缺失登记
  getMissingReportDetailApi(props.selectedRow.wsid)
    .then(res => {
      tableData.value = res.data.data ?? []
      loading.value = false
    })
    .catch(error => {
      toastError(error)
      loading.value = false
    })
}

/* ============== 表格 ============= */

const tableData = ref<any[]>([])

// 缺失登记记录详情表格
const missingReportRecordTableColumns: Array<TableColumnItem> = [
  { prop: "status", label: "状态", width: 90 },
  { prop: "predictDatetime", label: "预计补交日期", minWidth: 110 },
  { prop: "mrClassCode", label: "文书类型", minWidth: 90 },
  { prop: "type", label: "报告类型", width: 90 },
  { prop: "name", label: "报告名称", width: 110 },
  { prop: "reason", label: "缺失原因", width: 110 },
  { prop: "createdDatetime", label: "登记时间", width: 120 },
  { prop: "creator", label: "登记人", width: 110 }
]

/* ============== dialog ============= */

const visible = ref(false)

const show = () => {
  visible.value = true
  getMissingDetail()
}

const close = () => {
  visible.value = false
}

defineExpose({
  show,
  close
})
</script>
