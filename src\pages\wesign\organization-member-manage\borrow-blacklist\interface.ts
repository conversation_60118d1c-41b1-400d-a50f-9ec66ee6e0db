import axios from "@/interfaces/axios-instance"

/**
 * @method GET
 * @desc   获取借阅黑名单列表
 */
export const getBorrowBlacklistApi = (params: IPaginationRequestParams) => {
  return axios({
    method: "get",
    url: "/api/document/borrow/black",
    params
  })
}

/**
 * @method POST
 * @desc   添加借阅黑名单
 */
export const addBorrowBlacklistApi = params => {
  const { userWsid, realName, jobId, reason } = params
  return axios({
    method: "post",
    url: "/api/document/borrow/black",
    data: {
      userWsid,
      realName,
      jobId,
      reason
    }
  })
}

/**
 * @method DELETE
 * @desc   删除指定黑名单
 */
export const deleteBorrowBlacklistApi = id => {
  return axios({
    method: "delete",
    url: `/api/document/borrow/black/${id}`
  })
}
