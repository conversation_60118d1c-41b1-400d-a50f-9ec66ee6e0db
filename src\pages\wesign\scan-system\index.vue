<template>
  <el-container class="scan-system-page">
    <el-header>
      <div class="header-logo">
        <div class="header-logo-icon">
          <img :src="LogoIcon" alt="" />
          <span>病案数字化翻拍系统</span>
        </div>
      </div>

      <!-- 顶部右侧消息和个人信息 -->
      <div class="header-right-wrapper">
        <el-popover
          placement="bottom"
          popper-class="avatar-popover"
          :width="120"
          trigger="click"
          :teleported="false"
          :hide-after="0"
        >
          <template #reference>
            <div class="avatar-wrapper">
              <div class="avatar-img">
                <BlobImage
                  style="border-radius: 50%"
                  :file-wsid="userStore.userHospitalInfo.avatar"
                  :default="defaultAvatar"
                />
              </div>
              <div class="avatar-text">
                {{ userStore.userHospitalInfo.realName || "超级管理员" }}
              </div>
              <img :src="CaretIcon" alt="" />
            </div>
          </template>
          <div class="avatar-btn" @click="handleLogout">退出登录</div>
        </el-popover>
      </div>
    </el-header>
    <el-main>
      <router-view></router-view>
    </el-main>
  </el-container>
</template>

<script setup lang="ts">
import { onMounted, watchEffect } from "vue"
import { useRouter } from "vue-router"
import { useNetwork } from "@vueuse/core"
import { BlobImage } from "@/base-components"
import defaultAvatar from "@/assets/png/avatar.png"
import LogoIcon from "@/assets/png/scan-logo.png"
import CaretIcon from "@/assets/svg/other/caret-bottom.svg"
import { logoutApi } from "@/interfaces"
import { useUserStore } from "@/stores"
import { Message } from "@/utils/message-tool"

const router = useRouter()
const userStore = useUserStore()
const { isOnline } = useNetwork()

// 主动退出登录
const handleLogout = () => {
  logoutApi().finally(() => {
    userStore.clearUserStore()
    router.push({ path: "/login" })
  })
}

// 网络状态监测
watchEffect(() => {
  if (!isOnline.value) {
    Message.warning("您的网络存在异常，请检查网络是否正常连接")
  }
})
</script>

<style lang="less" scoped>
.scan-system-page {
  width: 100vw;
  height: 100vh;
  .el-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
    height: 60px;
    border: 1px solid #eff2f5;
    background-color: #fff;
  }
  .el-main {
    height: calc(100% - 60px);
  }
  .header-logo {
    display: flex;
    align-items: center;
    .header-logo-icon {
      display: flex;
      align-items: center;
    }
    span {
      font-size: 20px;
      font-weight: bold;
      color: #262626;
      padding-left: 8px;
    }
    :deep(.el-menu-item) {
      padding: 0 16px;
    }

    :deep(.el-menu) {
      border: none;
    }
  }
  .header-right-wrapper {
    .avatar-wrapper {
      display: flex;
      align-items: center;
      cursor: pointer;
      .avatar-img {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        margin-right: 8px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .avatar-text {
        padding-right: 12px;
      }
    }

    .avatar-btn {
      cursor: pointer;
    }
  }
  .header-menu-wrapper {
    background-color: transparent;
    padding-left: 76px;
  }
}
</style>
