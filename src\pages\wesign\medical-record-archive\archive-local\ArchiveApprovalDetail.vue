<template>
  <div class="quality-control-view">
    <div v-loading="state.loading" class="view-left">
      <MedicalRecordTree
        ref="recordTreeRef"
        can-collapse
        :base-info="state.baseInfo"
        :tree-info="state.treeInfo"
        @click-pdf="handlePdfClick"
      />
    </div>
    <div class="view-right">
      <!-- 顶部信息及按钮 -->
      <view class="tool-bar">
        <view class="document-name">{{ state.documentName }}</view>

        <view class="tool-bar-buttons">
          <el-button @click="toPrev">上一份文件</el-button>
          <el-button @click="toNext">下一份文件</el-button>

          <el-button type="danger" @click="handleArchiveRefuse">归档不通过</el-button>
          <el-button type="success" @click="handleArchiveAgree">病案归档</el-button>
        </view>
      </view>

      <!-- pdf -->
      <div class="view-middle common-box-shadow">
        <PdfPreviewComponent
          ref="pdfPreviewRef"
          :src="state.pdfSrc"
          :medical-location="state.baseInfo?.documentStorageLocation"
        />
      </div>
    </div>

    <!-- 归档不通过原因 -->
    <DialogContainer
      v-model:visible="RefuseDialogVisible"
      title="归档审批"
      :confirm-callback="confirmRefuse"
      :confirm-loading="archiveLoading"
    >
      <div class="export-content">
        <div style="margin-bottom: 10px">不通过原因：</div>
        <el-input v-model="reason" type="textarea" :rows="5" placeholder="请输入原因" />
      </div>
    </DialogContainer>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, ref, watch, computed } from "vue"
import { useRoute, useRouter } from "vue-router"
import { debounce } from "lodash-es"
import { PdfPreviewComponent, DialogContainer } from "@/base-components"
import { MedicalRecordTree } from "@/page-components"
import { getRecordViewData } from "@/interfaces"
import QualityInfoCard from "@/pages/wesign/department-quality-control/quality-control-view/quality-info-card/index.vue"
import { decryptStr, toastError, Message } from "@/utils"
import { archiveBag, archivePassApi, archiveRefuseApi } from "./interface"

const route = useRoute()

const router = useRouter()

interface ViewState {
  loading: boolean
  baseInfo: Record<string, any>
  firstPageFields: []
  pdfSrc: string
  targetNode: Record<string, any>
  qualityControlledData: []
  treeInfo: any
  documentName: string
  documentWsid: string
}

const state = reactive<ViewState>({
  loading: false,
  baseInfo: {},
  firstPageFields: [],
  pdfSrc: "",
  targetNode: {},
  qualityControlledData: [],
  treeInfo: {},
  documentName: "",
  documentWsid: "" as string // 当前点击的文档wsid
})

const handlePdfClick = node => {
  state.pdfSrc = "/api" + node?.contentFilePath
  state.targetNode = node
  state.documentName = node?.title
  state.documentWsid = node?.wsid
}

watch(
  () => route.query.inpNo,
  val => {
    if (val) {
      getData()
    }
  }
)

const getData = () => {
  const inpNo = route.query.inpNo as string
  const secretKey = route.query.secretKey || ""
  const params = { inpNo: inpNo }
  if (secretKey) {
    const sealKey = decryptStr(secretKey as string)
    params["sealKey"] = sealKey
  }
  state.loading = true
  getRecordViewData(params).then(res => {
    const { code, data } = res.data
    if (code === "100100000") {
      const { baseInfo = {}, firstPageFields = [] } = data
      state.baseInfo = baseInfo
      state.firstPageFields = firstPageFields
      state.treeInfo = res.data.data.treeInfo
      state.loading = false
    }
  })
}

onMounted(() => {
  getData()
  registerKeyboardEvent()
})

/*======================== 归档相关方法 ========================*/
const archiveLoading = ref(false)
const RefuseDialogVisible = ref(false)
const reason = ref("")
// 归档拒绝
const handleArchiveRefuse = () => {
  if (!route.query?.wsid) return Message.error("未获取到病案信息")
  RefuseDialogVisible.value = true
}
const confirmRefuse = async () => {
  archiveLoading.value = true
  await archiveRefuseApi({
    wsid: route.query.wsid as string,
    reason: reason.value
  })
    .then(() => {
      Message.success("归档拒绝")
      router.push({ path: "/archive/local/pending" })
    })
    .catch(err => toastError(err))
    .finally(() => {
      archiveLoading.value = false
    })
}

// 归档同意
const handleArchiveAgree = () => {
  if (!route.query?.wsid) return Message.error("未获取到病案信息")
  archiveLoading.value = true
  archivePassApi(route.query.wsid as string)
    .then(() => {
      Message.success("归档同意")
      router.push({ path: "/archive/local/pending" })
    })
    .catch(err => toastError(err))
    .finally(() => {
      archiveLoading.value = false
    })
}

const recordTreeRef = ref<InstanceType<typeof MedicalRecordTree>>()
const documentNodeList = computed(() => recordTreeRef.value?.state.documentNodeList)

const registerKeyboardEvent = () => {
  document.onkeyup = e => {
    const keyCode = e.code
    // 左/上键
    if (keyCode === "ArrowLeft" || keyCode === "ArrowUp") {
      toPrev()
    }
    // 右/下键
    else if (keyCode === "ArrowRight" || keyCode === "ArrowDown") {
      toNext()
    }
  }
}

const toPrev = debounce(() => {
  if (!documentNodeList.value) return
  const index = documentNodeList.value.findIndex(item => item.wsid === state.documentWsid) || 0
  if (index <= 0) return
  const targetNode = documentNodeList.value[index - 1]
  handlePdfClick(targetNode)
  recordTreeRef.value?.setCurrentKey(targetNode.wsid)
}, 300)

const toNext = debounce(() => {
  if (!documentNodeList.value) return
  const index = documentNodeList.value.findIndex(item => item.wsid === state.documentWsid)
  if (index >= documentNodeList.value.length - 1) return
  const targetNode = documentNodeList.value[index + 1]
  handlePdfClick(targetNode)
  recordTreeRef.value?.setCurrentKey(targetNode.wsid)
}, 300)
</script>

<style lang="less" scoped>
.quality-control-view {
  display: flex;
  justify-content: space-between;
  height: 100%;
  .view-middle {
    overflow-y: auto;
    height: calc(100% - 60px);
    min-width: 400px;
    flex: 1;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
  }

  // .view-right {
  //   width: 400px;
  //   background-color: #fff;
  //   padding-top: 10px;
  //   .view-right-action {
  //     text-align: right;
  //     padding-right: 10px;
  //     padding-bottom: 10px;
  //   }
  //   .quality-info-card {
  //     height: calc(100% - 42px);
  //   }
  // }

  .tool-bar {
    background: #fff;
    border-radius: 8px;
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 40px;

    .document-name {
      font-size: 16px;
      font-weight: 600;
    }
  }

  .remark {
    color: #f59a23;
    background-color: #fdf6ec;
    padding: 10px;
    cursor: pointer;
    div {
      max-width: 800px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .view-right {
    display: flex;
    flex-wrap: wrap;
    flex: 1;
  }
}
</style>
