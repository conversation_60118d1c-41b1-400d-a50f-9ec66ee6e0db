import { MenuOperationEnum } from "@/configs"
import type { RouteRecordRaw } from "vue-router"

const departmentQualityControlRouter: RouteRecordRaw = {
  path: "/department-quality-control",
  name: "DepartmentQualityControl",
  redirect: "/department-quality-control/manage",
  meta: {
    title: "科室病案管理",
    icon: "ri-folder-shield-2-line",
    type: "multiple",
    showBreadcrumb: true
  },
  children: [
    {
      path: "/department-quality-control/medical-qc",
      redirect: "/department-quality-control/medical-qc/waiting-control",
      meta: {
        title: "科室质控",
        operations: [
          { id: MenuOperationEnum.Track, name: "质控轨迹" },
          { id: MenuOperationEnum.QC, name: "质控" },
          { id: MenuOperationEnum.View, name: "查看" }
        ]
      },
      children: [
        {
          path: "/department-quality-control/medical-qc/waiting-control",
          meta: { title: "待质控" },
          component: () => import("./medical-quality-control/WaitingQualityControl.vue")
        },
        {
          path: "/department-quality-control/medical-qc/controlled",
          meta: { title: "已质控" },
          component: () => import("./medical-quality-control/QualityControlled.vue")
        }
      ]
    },
    {
      path: "/department-quality-control/medical-qc/view",
      meta: { title: "质控详情", hideMenu: true },
      component: () => import("./quality-control-view/index.vue")
    }
    // {
    //   path: "/department-quality-control/nurse-qc",
    //   redirect: "/department-quality-control/nurse-qc/waiting-control",
    //   meta: {
    //     title: "质控护士签收",
    //     operations: [
    //       { id: MenuOperationEnum.Track, name: "质控轨迹" },
    //       { id: MenuOperationEnum.QC, name: "质控" },
    //       { id: MenuOperationEnum.View, name: "查看" }
    //     ]
    //   },
    //   children: [
    //     {
    //       path: "/department-quality-control/nurse-qc/waiting-control",
    //       meta: { title: "待质控" },
    //       component: () => import("./nurse-quality-control/WaitingQualityControl.vue")
    //     },
    //     {
    //       path: "/department-quality-control/nurse-qc/controlled",
    //       meta: { title: "已质控" },
    //       component: () => import("./nurse-quality-control/QualityControlled.vue")
    //     }
    //   ]
    // },
    // {
    //   path: "/department-quality-control/nurse-qc/view",
    //   meta: { title: "质控详情", hideMenu: true },
    //   component: () => import("./quality-control-view/index.vue")
    // },
    // {
    //   path: "/department-quality-control/medical-task",
    //   meta: {
    //     title: "医疗质控任务",
    //     operations: [
    //       { id: MenuOperationEnum.QC, name: "质控" },
    //       { id: MenuOperationEnum.View, name: "查看" },
    //       { id: MenuOperationEnum.Track, name: "质控轨迹" }
    //     ]
    //   },
    //   redirect: "/department-quality-control/medical-task/waiting-control",
    //   children: [
    //     {
    //       path: "/department-quality-control/medical-task/waiting-control",
    //       meta: { title: "待质控", hideMenu: true },
    //       component: () => import("./medical-quality-control-task/WaitingQualityControlTask.vue")
    //     },
    //     {
    //       path: "/department-quality-control/medical-task/controlled",
    //       meta: { title: "已质控", hideMenu: true },
    //       component: () => import("./medical-quality-control-task/QualityControlledTask.vue")
    //     }
    //   ]
    // },
    // {
    //   path: "/department-quality-control/medical-task/view",
    //   meta: { title: "质控详情", hideMenu: true },
    //   component: () => import("./quality-control-view/index.vue")
    // },
    // {
    //   path: "/department-quality-control/nurse-task",
    //   meta: {
    //     title: "护理质控任务",
    //     operations: [
    //       { id: MenuOperationEnum.QC, name: "质控" },
    //       { id: MenuOperationEnum.View, name: "查看" },
    //       { id: MenuOperationEnum.Track, name: "质控轨迹" }
    //     ]
    //   },
    //   redirect: "/department-quality-control/nurse-task/waiting-control",
    //   children: [
    //     {
    //       path: "/department-quality-control/nurse-task/waiting-control",
    //       meta: { title: "待质控", hideMenu: true },
    //       component: () => import("./nurse-quality-control-task/WaitingQualityControlTask.vue")
    //     },
    //     {
    //       path: "/department-quality-control/nurse-task/controlled",
    //       meta: { title: "已质控", hideMenu: true },
    //       component: () => import("./nurse-quality-control-task/QualityControlledTask.vue")
    //     }
    //   ]
    // },
    // {
    //   path: "/department-quality-control/nurse-task/view",
    //   meta: { title: "质控详情", hideMenu: true },
    //   component: () => import("./quality-control-view/index.vue")
    // },
    // {
    //   path: "/department-quality-control/cross-distribute",
    //   meta: {
    //     title: "质控任务分配",
    //     operations: [
    //       { id: MenuOperationEnum.Distribute, name: "分配" },
    //       { id: MenuOperationEnum.ProcessSearch, name: "进度查询" }
    //     ]
    //   },
    //   component: () => import("./cross-quality-control/index.vue")
    // }
  ]
}

export default departmentQualityControlRouter
