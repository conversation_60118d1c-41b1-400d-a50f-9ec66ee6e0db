import type { TableColumnItem } from "@/types"

export const menuId = "/borrow/approval"

export const borrowApprovalWaitColumns: Array<TableColumnItem> = [
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "borrowDoctorName", label: "申请人", minWidth: 100 },
  { prop: "fileCount", label: "文件数量", minWidth: 90 },
  { prop: "applyTypeName", label: "借阅类型", minWidth: 90 },
  { prop: "applyReason", label: "借阅原因", minWidth: 150 },
  { prop: "patientId", label: "患者编号", minWidth: 150, sortable: true, must: true },
  { prop: "mrNo", label: "病案号", minWidth: 150, sortable: true, must: true },
  { prop: "statusEnumName", label: "审批状态", minWidth: 100 },
  { prop: "applyDatetime", label: "申请时间", minWidth: 180, sortable: true },
  { prop: "approveLatestTime", label: "审批时限", minWidth: 180, sortable: true },
  { prop: "timeLimit", label: "借阅时长", minWidth: 90 },
  { prop: "operation", label: "操作", width: 75, fixed: "right", must: true }
]

export const borrowApprovalFinishedColumns: Array<TableColumnItem> = [
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "borrowDoctorName", label: "申请人", minWidth: 100 },
  { prop: "fileCount", label: "文件数量", minWidth: 90 },
  { prop: "applyTypeName", label: "借阅类型", minWidth: 90 },
  { prop: "applyReason", label: "借阅原因", minWidth: 150 },
  { prop: "patientId", label: "患者编号", minWidth: 150, sortable: true, must: true },
  { prop: "mrNo", label: "病案号", minWidth: 150, sortable: true, must: true },
  { prop: "approverName", label: "审批人", minWidth: 100 },
  { prop: "statusEnumName", label: "审批状态", minWidth: 100 },
  { prop: "applyDatetime", label: "申请时间", minWidth: 180, sortable: true },
  { prop: "approveLatestTime", label: "审批时限", minWidth: 180, sortable: true },
  { prop: "approverDatetime", label: "审批时间", sortable: true, minWidth: 180 },
  { prop: "timeLimit", label: "借阅时长", minWidth: 90 },
  { prop: "operation", label: "操作", width: 75, fixed: "right", must: true }
]

export const timeOptions = [
  // { label: "2小时", value: "1" },
  // { label: "1天", value: "2" },
  // { label: "2天", value: "3" },
  // { label: "7天", value: "4" },
  // { label: "永久", value: "5" }
  { label: "小时", value: "HOUR" },
  { label: "天", value: "DAY" },
  { label: "月", value: "MONTH" },
  { label: "年", value: "YEAR" }
]

// 申请状态
export const APPROVAL_STATUS_MAP = new Map([
  ["APPLICATION", "待审批"],
  ["AUDIT_PASS", "审批通过"],
  ["TIME_OUT_AUDIT", "审批超时"],
  ["AUDIT_REFUSE", "申请驳回"],
  ["OVER_DUE", "已过期"]
])

// 申请状态的el-tag 类型
export const APPROVAL_TAG_MAP = new Map<string, "success" | "warning" | "info" | "danger">([
  ["APPLICATION", "success"],
  ["AUDIT_PASS", "success"],
  ["TIME_OUT_AUDIT", "danger"],
  ["AUDIT_REFUSE", "danger"],
  ["OVER_DUE", "danger"]
])

// tab
export const approvalRouterList = [
  { label: "待审批", path: "/borrow/approval/wait" },
  { label: "已审批", path: "/borrow/approval/finished" }
]

export const unitOptions = {
  HOUR: "小时",
  DAY: "天",
  MONTH: "月",
  YEAR: "年",
  FOREVER: "",
  UNKNOWN: "未知"
}
