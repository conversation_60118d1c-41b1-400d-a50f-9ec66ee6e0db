import { MenuOperationEnum } from "@/configs"
import type { RouteRecordRaw } from "vue-router"

const archiveRouter: RouteRecordRaw = {
  path: "/archive",
  name: "MedicalRecordArchive",
  redirect: "/archive/local",
  meta: {
    title: "病案归档",
    icon: "ri-inbox-archive-line",
    showBreadcrumb: true,
    type: "multiple",
    operations: [
      { id: MenuOperationEnum.Archive, name: "归档" },
      { id: MenuOperationEnum.View, name: "查看" },
      { id: MenuOperationEnum.Print, name: "打印" }
    ]
  },
  children: [
    {
      path: "/archive/local",
      redirect: "/archive/local/pending",
      meta: { title: "病案归档" },
      children: [
        {
          path: "/archive/local/pending",
          meta: { title: "待归档" },
          component: () => import("./archive-local/ArchivePending.vue")
        },
        {
          path: "/archive/local/finished",
          meta: { title: "已归档" },
          component: () => import("./archive-local/ArchiveFinished.vue")
        },
        //未归档归档审批
        {
          path: "/archive/local/approval-view",
          meta: { title: "归档审批", hideMenu: true },
          component: () => import("./archive-local/ArchiveApprovalDetail.vue")
        },
        //未归档查看
        {
          path: "/archive/local/view",
          meta: { title: "查看", hideMenu: true },
          component: () => import("@/pages/wesign/medical-record-manage/sub-pages/detail.vue")
        },
        //已归档查看
        {
          path: "/archive/local/version",
          meta: { title: "查看", hideMenu: true },
          component: () => import("./archive-local/ArchiveVersion.vue")
        }
      ]
    },
    {
      path: "/archive/third-party",
      meta: { title: "第三方归档" },
      redirect: "/archive/third-party/list",
      children: [
        {
          path: "/archive/third-party/list",
          meta: { title: "第三方归档" },
          component: () => import("./archive-third-party/index.vue")
        },
        {
          path: "/archive/third-party/detail",
          meta: { title: "详情", hideMenu: true },
          component: () => import("./archive-third-party/Detail.vue")
        }
      ]
    }
  ]
}

export default archiveRouter
