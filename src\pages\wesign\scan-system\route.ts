import ImageEditor from "./ImageEditor.vue"
import Capture from "./capture.vue"
import ScanPage from "./index.vue"
import type { RouteRecordRaw } from "vue-router"

const ScanSystemRoute: RouteRecordRaw = {
  path: "/scan-system",
  name: "ScanSystem",
  redirect: "/scan-system/capture",
  component: ScanPage,
  meta: {
    title: "翻拍系统",
    hideLayout: true
  },
  children: [
    {
      path: "/scan-system/capture",
      name: "Capture",
      component: Capture,
      meta: {
        title: "翻拍系统",
        hideLayout: true
      }
    },
    {
      path: "/scan-system/image-editor",
      name: "ImageEditor",
      component: ImageEditor,
      meta: {
        title: "图片编辑",
        hideLayout: true
      }
    }
  ]
}

export default ScanSystemRoute
