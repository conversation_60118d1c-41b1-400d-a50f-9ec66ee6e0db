<template>
  <div class="quality-control-view">
    <div v-loading="state.loading" class="view-left">
      <MedicalRecordTree
        v-if="route.query.urlType !== QualityControlTypeEnum.DEPT_QC_NURSE"
        ref="recordTreeRef"
        can-collapse
        :base-info="state.baseInfo"
        :tree-info="state.treeInfo"
        @click-pdf="handlePdfClick"
      />

      <!-- 左侧文件操作栏 -->
      <div v-else class="left-container">
        <div class="aside-container" :style="{ width: leftCollapse ? '0px' : '350px' }">
          <!-- 病案文件列表 -->
          <div class="record-tree">
            <div class="no-inpNo-document" :style="{ display: leftCollapse ? 'none' : 'flex' }">
              <!-- 无住院号且在本次住院期间产生的文书 -->
              <el-checkbox v-model="autoAssignmentStatus" size="large">
                <div style="display: flex; align-items: center">
                  无住院号
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    content="无住院号且在本次住院期间产生的文书"
                    placement="top"
                  >
                    <el-icon>
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </div>
              </el-checkbox>
              <!-- 翻拍 -->
              <el-checkbox v-model="isSupplement" label="翻拍" size="large" />
            </div>
            <CommonTree
              ref="submissionTreeRef"
              node-key="wsid"
              :data="state.documentTree"
              :filter-tree-node="filterTreeNode"
              :filter-keys="[autoAssignmentStatus, isSupplement]"
              show-delete
              @click-node="handlePdfClick"
            >
              <template #title="{ node, data }">
                <!-- 文件名 -->

                <span v-if="data.type === 'FILE'" style="margin-right: 4px">
                  <i v-if="!data.isSupplement" class="ri-file-text-line electric-icon"></i>
                  <i v-else class="ri-camera-line paper-icon"></i>
                </span>
                <!-- 文本 -->
                <OverflowTooltip
                  v-if="!data.renamable"
                  :content="getNodeTooltipContent(node, data, false)"
                  max-width="65%"
                ></OverflowTooltip>
                <div
                  v-if="data.autoAssignmentStatus && !data.renamable && data.type === 'FILE'"
                  class="auto-assignment"
                ></div>

                <el-input
                  v-if="data.type === 'FILE' && data.renamable"
                  v-model="data.title"
                  style="max-width: 75%"
                  size="small"
                  placeholder="请输入文件名称"
                  @click.stop
                  @blur="saveRename(data)"
                >
                  <template #append>
                    <el-button size="small" :icon="Select" @click="saveRename(data)" />
                  </template>
                </el-input>

                <!--  操作按钮-->
                <div class="operation-wrapper">
                  <!-- 编辑文件名称 -->
                  <i
                    v-if="data.type === 'FILE' && !data.renamable"
                    style="color: #3860f4"
                    class="ri-edit-box-line"
                    @click.stop="handleRename(data)"
                  ></i>
                  <!-- 删除 -->
                  <i
                    v-if="data.type === 'FILE' && data.homePageFlag !== 1"
                    class="ri-delete-bin-line"
                    @click.stop="handleDeleteNode(node)"
                  ></i>
                </div>
              </template>
            </CommonTree>
          </div>
          <!-- 需要归档的文件列表 -->
          <div class="archive-list">
            <div class="archive-list__header">
              <el-checkbox
                v-model="checkedAllArchive"
                label="全选"
                size="large"
                :disabled="state.inactiveDocuments.length === 0"
                @change="handleSelectAllArchive"
              />
              <el-popover
                placement="top"
                trigger="hover"
                width="300"
                :popper-style="{ background: 'rgba(0, 0, 0, 0.5)' }"
              >
                <div class="tips-content">无法确定文书为患者本次住院文书，用户选择是否归档</div>
                <template #reference>
                  <el-icon style="position: absolute; left: 70px">
                    <QuestionFilled />
                  </el-icon>
                </template>
              </el-popover>
              <div>选择归档文件</div>

              <el-select
                v-model="beforeDays"
                placeholder="请选择"
                style="width: 100%; margin-top: 16px"
                @change="selectBeforeDays"
              >
                <el-option
                  v-for="(item, index) in beforeDaysOptions"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>

            <div class="archive-list__content">
              <empty-content v-if="state.inactiveDocuments.length === 0" desc="暂无数据" />
              <el-checkbox-group v-else v-model="state.checkedArchiveDocumentWsidList">
                <el-checkbox
                  v-for="archiveDocument in state.inactiveDocuments"
                  :key="archiveDocument.wsid"
                  :label="archiveDocument.wsid"
                  class="custom-checkbox"
                  @change="handlePdfClick(archiveDocument)"
                >
                  <div :title="archiveDocument.title + '（' + archiveDocument.filePage + '页）'" class="file-label">
                    <div>
                      <span style="margin-right: 4px">
                        <i v-if="!archiveDocument.isSupplement" class="ri-file-text-line electric-icon"></i>
                        <i v-else class="ri-camera-line paper-icon"></i>
                      </span>
                      <span v-if="!archiveDocument.renamable">
                        {{ archiveDocument.title }}（{{ archiveDocument.filePage }}页）
                      </span>
                      <el-input
                        v-else
                        v-model="archiveDocument.title"
                        style="max-width: 75%"
                        size="small"
                        placeholder="请输入文件名称"
                        @click.stop
                        @blur="saveRename(archiveDocument)"
                      >
                        <template #append>
                          <el-button color="#3860f4" size="small" :icon="Select" @click="saveRename(archiveDocument)" />
                        </template>
                      </el-input>
                    </div>
                    <!-- 编辑文件名称 -->
                    <i
                      v-if="!archiveDocument.renamable"
                      style="color: #3860f4"
                      class="ri-edit-box-line"
                      @click.stop="handleRename(archiveDocument)"
                    ></i>
                  </div>
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </div>
        <!-- 展开/收缩 -->
        <div
          class="collapse-icon"
          :style="{
            boxShadow: leftCollapse ? '0px 0px 3px 1px rgba(0, 35, 114, 0.1)' : '3px 0px 3px 1px rgba(0, 35, 114, 0.1)',
            borderRadius: leftCollapse ? '5px' : '0px 5px 5px 0px'
          }"
          @click="() => (leftCollapse = !leftCollapse)"
        >
          <el-icon v-if="!leftCollapse"><ArrowLeft /></el-icon>
          <el-icon v-if="leftCollapse"><ArrowRight /></el-icon>
        </div>
      </div>
    </div>
    <div class="view-right">
      <!-- 顶部信息及按钮 -->
      <view class="tool-bar">
        <view class="document-name">{{ state.documentName }}</view>

        <view class="tool-bar-buttons">
          <el-button @click="toPrev">上一份文件</el-button>
          <el-button @click="toNext">下一份文件</el-button>
          <el-button
            v-if="route.query.isControl === 'true'"
            :disabled="noPassLoading"
            :loading="passLoading"
            type="success"
            @click="passQualityControl"
          >
            {{ operationOptions[route.query.urlType as string] }}通过
          </el-button>
          <el-button
            v-if="route.query.isControl === 'true'"
            :disabled="passLoading"
            :loading="noPassLoading"
            type="danger"
            @click="noPassQualityControl"
          >
            {{ operationOptions[route.query.urlType as string] }}不通过
          </el-button>
        </view>
      </view>

      <div class="view-middle common-box-shadow">
        <!-- 病案备注 -->
        <div
          v-if="state.baseInfo.submitRemark && state.baseInfo.submitStatusEnum === 'SUBMIT_SUCCESS'"
          class="remark"
          @click="showSubmitRemark"
        >
          <div>备注：{{ state.baseInfo.submitRemark }}</div>
        </div>
        <!-- pdf -->
        <PdfPreviewComponent
          :src="state.pdfSrc"
          :default-width="750"
          :class="{ 'remark-existed': state.baseInfo.submitRemark }"
        />
      </div>

      <div v-loading="state.loading" class="right-container" :style="{ width: rightCollapse ? '0px' : '432px' }">
        <QualityInfoCard
          ref="qualityInfoCard"
          :document-bag-wsid="state.baseInfo.wsid"
          @refresh="getQualityControlRecord"
        />

        <!-- 展开/收缩 -->
        <div
          class="right-collapse-icon"
          :style="{
            boxShadow: rightCollapse
              ? '3px 0px 3px 1px rgba(0, 35, 114, 0.1)'
              : '0px 0px 3px 1px rgba(0, 35, 114, 0.1)',
            borderRadius: rightCollapse ? '5px' : '5px 0px 0px 5px'
          }"
          @click="() => (rightCollapse = !rightCollapse)"
        >
          <el-icon v-if="rightCollapse"><ArrowLeft /></el-icon>
          <el-icon v-if="!rightCollapse"><ArrowRight /></el-icon>
        </div>
      </div>
    </div>

    <!-- 备注 -->
    <DialogContainer v-model:visible="remarkDialogVisible" title="提交备注" :width="550">
      <el-input disabled :value="state.baseInfo.submitRemark" type="textarea" :rows="10" />
      <template #footer>
        <div>
          <el-button type="primary" @click="remarkDialogVisible = false">知道了</el-button>
        </div>
      </template>
    </DialogContainer>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, watch, ref, computed, nextTick } from "vue"
import { useRoute, useRouter } from "vue-router"
import { debounce } from "lodash-es"
import { Select, ArrowLeft, ArrowRight } from "@element-plus/icons-vue"
import { PdfPreviewComponent, DialogContainer, CommonTree, OverflowTooltip, EmptyContent } from "@/base-components"
import { MedicalRecordTree } from "@/page-components"
import { beforeDaysOptions } from "../config"
import QualityInfoCard from "./quality-info-card/index.vue"
import { QcStatusEnum, QualityControlTypeEnum } from "@/configs"
import {
  getRecordViewData,
  changeQualityControlStatusApi,
  getQualityOptionRecordApi,
  fileRenameApi,
  deleteFileApi,
  getInactiveDocumentsApi
} from "@/interfaces"
import {
  decryptStr,
  Message,
  SystemPrompt,
  toastError,
  getQcStatus,
  getQcStatusTagType,
  formatDatetime,
  formatRecordTree,
  getNodeTooltipContent,
  getFileNodeList
} from "@/utils"
const route = useRoute()
const router = useRouter()

interface ViewState {
  loading: boolean
  baseInfo: Record<string, any>
  firstPageFields: []
  pdfSrc: string
  targetNode: Record<string, any>
  qualityControlledData: []
  treeInfo: any
  documentName: string
  documentWsid: string
  documentTree: Record<string, any>[] // 病案树形结构
  inactiveDocuments: Record<string, any>[] // 可以归档的文件
  checkedArchiveDocumentWsidList: string[] // 用户所勾选需要归档的文件
}

const state = reactive<ViewState>({
  loading: false,
  baseInfo: {},
  firstPageFields: [],
  pdfSrc: "",
  targetNode: {},
  qualityControlledData: [],
  treeInfo: {},
  documentName: "",
  documentWsid: "",
  documentTree: [],
  inactiveDocuments: [],
  checkedArchiveDocumentWsidList: []
})

const handlePdfClick = node => {
  state.pdfSrc = "/api" + node?.contentFilePath
  state.targetNode = node
  state.documentName = node?.title
  state.documentWsid = node?.wsid
}

watch(
  () => route.query.inpNo,
  val => {
    if (val) {
      getData()
    }
  }
)

const getData = async () => {
  const inpNo = route.query.inpNo as string
  const secretKey = route.query.secretKey || ""
  const params = { inpNo: inpNo }
  if (secretKey) {
    const sealKey = decryptStr(secretKey as string)
    params["sealKey"] = sealKey
  }
  state.loading = true
  getRecordViewData(params).then(res => {
    const { code, data } = res.data
    if (code === "100100000") {
      const { baseInfo = {}, firstPageFields = [] } = data
      state.baseInfo = baseInfo
      state.firstPageFields = firstPageFields
      state.treeInfo = res.data.data.treeInfo
      state.documentTree = formatRecordTree(res.data.data.treeInfo)
      const pdfNodeList = getFileNodeList(state.documentTree)
      handlePdfClick(pdfNodeList[0])
      nextTick(() => submissionTreeRef.value?.setCurrentKey(pdfNodeList?.[0]?.wsid || ""))
      state.loading = false
    }
  })
}

onMounted(async () => {
  await getData()
  getInactiveDocuments()
  registerKeyboardEvent()
})

const operationOptions = {
  [QualityControlTypeEnum.DEPT_QC_MEDICAL]: "医疗质控",
  [QualityControlTypeEnum.DEPT_QC_NURSE]: "护理质控",
  [QualityControlTypeEnum.DEPT_QC]: "医护质控"
}

/* =============== toolbar =============== */

const recordTreeRef = ref<InstanceType<typeof MedicalRecordTree>>()
const documentNodeList = computed(() => recordTreeRef.value?.state.documentNodeList)

const registerKeyboardEvent = () => {
  document.onkeyup = e => {
    const keyCode = e.code
    // 左/上键
    if (keyCode === "ArrowLeft" || keyCode === "ArrowUp") {
      toPrev()
    }
    // 右/下键
    else if (keyCode === "ArrowRight" || keyCode === "ArrowDown") {
      toNext()
    }
  }
}

const toPrev = debounce(() => {
  if (!documentNodeList.value) return
  const index = documentNodeList.value.findIndex(item => item.wsid === state.documentWsid) || 0
  if (index <= 0) return
  const targetNode = documentNodeList.value[index - 1]
  handlePdfClick(targetNode)
  recordTreeRef.value?.setCurrentKey(targetNode.wsid)
}, 300)

const toNext = debounce(() => {
  if (!documentNodeList.value) return
  const index = documentNodeList.value.findIndex(item => item.wsid === state.documentWsid)
  if (index >= documentNodeList.value.length - 1) return
  const targetNode = documentNodeList.value[index + 1]
  handlePdfClick(targetNode)
  recordTreeRef.value?.setCurrentKey(targetNode.wsid)
}, 300)

// 展示备注
const remarkDialogVisible = ref(false)
const showSubmitRemark = () => {
  remarkDialogVisible.value = true
}

/* =============== 质控通过、不通过 =============== */

const passStatus = ref<QcStatusEnum>(QcStatusEnum.WAIT)
const passLoading = ref(false)
const noPassLoading = ref(false)
const suggestList = ref<Array<Record<string, any>>>([])
onMounted(() => {
  getQualityControlRecord()
})

function getQualityControlRecord() {
  getQualityOptionRecordApi({ inpNo: route.query.inpNo, urlType: route.query.urlType })
    .then(res => {
      if (route.query.isControl === "true") {
        suggestList.value = res.data.data.currentComments || []
      } else {
        passStatus.value =
          res.data.data.hisComments.find(item => item.qcType === route.query.urlType)?.qcStatus || QcStatusEnum.WAIT
      }
    })
    .catch(err => {
      toastError(err, "获取质控意见列表失败")
    })
    .finally(() => {
      // suggestListLoading.value = false
    })
}

// 通过质控
function passQualityControl() {
  SystemPrompt("是否通过质控？确认后不能修改。").then(() => {
    changeQualityControlStatus(QcStatusEnum.PASS)
  })
}

const qualityInfoCard = ref()
// 不通过质控
function noPassQualityControl() {
  if (suggestList.value.length === 0) {
    qualityInfoCard.value?.updateActiveTab("quality-control-record")
    setTimeout(() => {
      qualityInfoCard.value?.openAddSuggest()
    }, 500)
    return Message.error("请在质控记录添加质控意见！")
  }
  SystemPrompt("是否不通过质控？确认后不能修改。").then(() => {
    changeQualityControlStatus(QcStatusEnum.NOT_PASS)
  })
}

// 修改质控状态
function changeQualityControlStatus(status) {
  if (status === QcStatusEnum.PASS) passLoading.value = true
  else noPassLoading.value = true
  changeQualityControlStatusApi({
    inpNo: route.query.inpNo as string,
    qcType: route.query.urlType as string,
    status: status,
    confirmDocumentWsids: state.checkedArchiveDocumentWsidList
  })
    .then(res => {
      Message.success(`${status === QcStatusEnum.PASS ? "通过" : "不通过"}成功`)
      // 通过后isControl为false不可再编辑
      setTimeout(() => {
        if (status === QcStatusEnum.PASS) passLoading.value = false
        else noPassLoading.value = false
        if (route.query.returnUrl) {
          router.push({
            path: route.query.returnUrl as string
          })
        } else {
          router.replace({ query: { ...route.query, isControl: "false" } })
          setTimeout(() => {
            window.location.reload()
          })
        }
      }, 1000)
    })
    .catch(err => {
      if (status === QcStatusEnum.PASS) passLoading.value = false
      else noPassLoading.value = false
      toastError(err, `${status === QcStatusEnum.PASS ? "通过" : "不通过"}失败`)
    })
}

const rightCollapse = ref(false)
const leftCollapse = ref(false)

/* =============== 左侧文件操作栏 =============== */
const submissionTreeRef = ref()

const handleRename = data => {
  data.renamable = true
}

// 文件重命名
const saveRename = async data => {
  try {
    data.renamable = false
    await fileRenameApi(data.wsid, data.title)
  } catch (error: any) {
    toastError(error, "文件重命名失败！")
  }
}

const handleDeleteNode = node => {
  SystemPrompt("确认删除该文件吗？").then(() => {
    deleteFileApi(node.data.wsid)
      .then(res => {
        if (!res.data.data) {
          Message.error("删除失败")
        } else {
          Message.success("删除成功")
          getData()
        }
      })
      .catch(error => {
        toastError(error)
      })
  })
}

// 全选归档文件
watch(
  () => state.checkedArchiveDocumentWsidList,
  val => {
    checkedAllArchive.value = val.length === state.inactiveDocuments.length
    // submitStore.checkedArchiveDocumentWsidList = val
  }
)

const checkedAllArchive = ref(false)
const handleSelectAllArchive = val => {
  state.checkedArchiveDocumentWsidList = val ? state.inactiveDocuments.map(item => item.wsid) : []
}

const beforeDays = ref("14")

const getInactiveDocuments = () => {
  getInactiveDocumentsApi({ inpNo: route.query.inpNo as string, beforeDays: beforeDays.value }).then(res => {
    const __inactiveDocuments = res.data?.data ?? []
    state.inactiveDocuments = __inactiveDocuments.map(doc => {
      doc.type = "FILE"
      doc.renamable = false
      return doc
    })
  })
}

const selectBeforeDays = val => {
  getInactiveDocuments()
}

/* ============== 筛选文件 =============== */
const isSupplement = ref(false)
const autoAssignmentStatus = ref(false)

const filterTreeNode = (value, data, node) => {
  if (value.every(item => item === false)) {
    return true
  } else if (value.some(item => item === true)) {
    if (data.type === "FILE") {
      return value[0]
        ? data.autoAssignmentStatus
        : !data.autoAssignmentStatus && value[1]
        ? data.isSupplement === 1
        : data.isSupplement === 0
    }
  }
}
</script>

<style lang="less" scoped>
.quality-control-view {
  display: flex;
  justify-content: space-between;
  height: 100%;
  .view-middle {
    overflow-x: auto;
    height: calc(100% - 60px);
    min-width: 400px;
    flex: 1;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .remark {
      color: #f59a23;
      background-color: #fdf6ec;
      padding: 10px;
      cursor: pointer;
      div {
        max-width: 800px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .tool-bar {
    background: #fff;
    border-radius: 8px;
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 40px;

    .document-name {
      font-size: 16px;
      font-weight: 600;
    }
  }

  .view-right {
    display: flex;
    flex-wrap: wrap;
    flex: 1;
  }
}
.remark-existed {
  height: calc(100% - 40px) !important;
}
@media screen and (max-width: 1440px) {
  .view-middle {
    min-width: 0 !important;
  }
}

.right-container {
  position: relative;
  height: calc(100% - 60px);
  transition: width 0.5s;
}

.right-collapse-icon {
  width: 24px;
  height: 48px;
  background: #fff;
  left: -24px;
  padding: 16px 4px;
  box-sizing: border-box;
  position: absolute;
  top: 0px;
  bottom: 0px;
  margin: auto;
  z-index: 999;
  cursor: pointer;
  &:hover {
    color: #999;
  }
}

.left-container {
  height: 100%;
  position: relative;
  background-color: #fff;
}

.aside-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: column;
  height: 100%;
  position: relative;
  transition: width 0.5s;
  overflow: hidden;
}

.integrality-container {
  height: 100%;
  position: relative;
  transition: width 0.5s;
}

.record-tree {
  width: calc(100% - 20px);
  height: calc(50% - 30px);
  // border: 1px solid #e5e5e5;
  // border-radius: 4px;
  padding: 10px;
}

.archive-list {
  height: 50%;
  width: 100%;
  // border: 1px solid #e5e5e5;
  // border-radius: 4px;

  &__header {
    position: sticky;
    color: #0a1633;
    font-size: 14px;
    height: 40px;
    line-height: 40px;

    background: #f0f4fb;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    padding: 0 20px;
  }

  &__content {
    padding: 0 20px;
    height: calc(100% - 100px);
    overflow-y: auto;
    margin-top: 60px;
  }
}

.no-inpNo-document {
  font-size: 12px;
  display: flex;
  align-items: center;
}

.auto-assignment {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #f59a23;
  margin-right: 5px;
}

.operation-wrapper {
  display: flex;
  flex: 1;
  justify-content: flex-end;
  align-items: center;
}
.tips-content {
  color: #fff;
  font-size: 12px;
}
.no-inpNo-document {
  font-size: 12px;
  display: flex;
  align-items: center;
}
.patient-hospitalTime {
  font-size: 12px;
  color: #999999;
  display: flex;
  align-items: center;
  position: absolute;
  bottom: 5px;
  line-height: 1;
}

.electric-icon {
  color: #04c3a1;
  font-size: 16px;
}
.paper-icon {
  color: #409eff;
  font-size: 16px;
}

.collapse-icon {
  width: 24px;
  height: 48px;
  background: #fff;
  right: -24px;
  padding: 16px 4px;
  box-sizing: border-box;
  position: absolute;
  top: 0px;
  bottom: 0px;
  margin: auto;
  z-index: 999;
  cursor: pointer;
  &:hover {
    color: #999;
  }
}

.ri-delete-bin-line {
  margin-left: 10px;
  font-size: 16px;
  color: var(--el-color-danger);
}

.custom-checkbox {
  align-items: center;
  // flex-direction: row-reverse;
  width: 100%;
  justify-content: flex-end;
  margin-right: 0;
  margin-bottom: 6px;
}

:deep(.el-checkbox__label) {
  width: 90% !important;
}

.file-label {
  overflow: hidden;
  justify-content: space-between;
  align-items: center;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 100%;
  display: flex;
}
</style>
