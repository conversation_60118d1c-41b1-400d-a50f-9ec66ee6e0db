<template>
  <div v-if="props.showTable" class="average-score-table-container">
    <div class="header flex-between">
      <div class="header-title">平均得分</div>

      <div class="table-chart-tool">
        <el-tooltip content="表格展示">
          <i v-if="props.showTable"><img src="@/assets/svg/statistical-statement/table-active.svg" alt="" /></i>
          <i v-else>
            <img src="@/assets/svg/statistical-statement/table.svg" alt="" />
          </i>
        </el-tooltip>
        <el-tooltip content="图表展示">
          <i v-if="props.showTable" @click="emits('changeTab', false)">
            <img src="@/assets/svg/statistical-statement/chart.svg" alt="" />
          </i>
          <i v-else><img src="@/assets/svg/statistical-statement/chart-active.svg" alt="" /></i>
        </el-tooltip>
        <i class="ri-download-2-line" @click="emits('export')"></i>
      </div>
    </div>

    <div class="average-table">
      <BaseTable :columns="columns" border :data="dataSources" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue"

import { concat, flow, map } from "lodash/fp"
import { BaseTable } from "@/base-components"
import type { TableColumnItem } from "@/types"

const props = defineProps({
  showTable: { type: Boolean, default: true },
  timeList: { type: Array, default: () => [] },
  series: { type: Array, default: () => [] }
})

const emits = defineEmits(["changeTab", "export"])
const mapIndexed = map.convert({ cap: false })

const columns = computed(() => {
  let defaultColumns = [
    {
      label: "日期",
      prop: "date",
      width: 80,
      fixed: "left"
    }
  ] as TableColumnItem
  return flow(
    mapIndexed((item: string, index: number) => ({
      label: item,
      prop: item,
      minWidth: 80,
      ...(index === props.timeList.length - 1 ? { fixed: "right" } : {})
    })),
    concat(defaultColumns)
  )(props.timeList) as Array<TableColumnItem>
})

const dataSources = computed(() => {
  const obj = {
    date: "得分"
  }
  //将series和timeList合并成一个对象
  props.timeList.forEach((item: string, index: number) => {
    obj[item] = props.series[index]
  })
  return [obj]
})
</script>

<style lang="less" scoped>
.average-score-table-container {
  border: 1px solid #d4d4d4;
  border-radius: 4px;
  padding: 20px;
  box-sizing: border-box;
  width: calc(100% - 450px);
  margin-left: 20px;
  .header {
    .header-title {
      font-size: 14px;
      font-weight: bold;
      color: #333;
    }
    .table-chart-tool {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      gap: 10px;
      .table-setting-btn {
        margin-right: 8px;
        padding: 4px 8px;
        cursor: pointer;
      }

      i {
        cursor: pointer;
        img {
          vertical-align: bottom;
        }
      }
    }
  }

  .average-table {
    :deep(.el-table) {
      height: auto !important;
    }
  }
}
</style>
