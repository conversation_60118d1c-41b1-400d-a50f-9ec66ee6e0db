<template>
  <div class="record-compare-container">
    <el-container>
      <!-- 头部操作 -->
      <el-header>
        <div
          class="back-action"
          @click="$router.push(`/recall/history/detail?wsid=${route.query.wsid}&inpNo=${route.query.inpNo}`)"
        >
          <el-icon><ArrowLeft /></el-icon>
          对比查看
        </div>
        <el-checkbox v-model="moveTogether">同步滚动</el-checkbox>
      </el-header>
      <el-main class="compare-container">
        <!-- 召回版本 -->
        <div class="version-root">
          <div class="version-choose">
            召回版本：
            <el-select v-model="recallState.activeRecallVersion" @change="getRecallDetail">
              <el-option
                v-for="option in recallState.data"
                :key="option.versionName"
                :label="option.versionName"
                :value="option.wsid"
              />
            </el-select>
          </div>
          <div class="version-container">
            <div v-loading="recallState.detailLoading || recallState.versionLoading" class="view-left">
              <MedicalRecordTree
                :can-collapse="true"
                :base-info="recallState.baseInfo"
                :tree-info="recallState.treeInfo"
                @click-pdf="node => handlePdfClick(node, 'recall')"
              />
            </div>
            <div class="view-middle common-box-shadow">
              <PdfPreviewComponent ref="recallPdfRef" :src="recallState.pdfSrc" />
            </div>
          </div>
        </div>
        <!-- 归档版本 -->
        <div class="version-root">
          <div class="version-choose">
            归档版本：
            <el-select v-model="archiveState.activeRecallVersion" @change="getArchiveDetail">
              <el-option
                v-for="option in archiveState.data"
                :key="option.versionName"
                :label="option.versionName"
                :value="option.code"
              />
            </el-select>
          </div>
          <div class="version-container">
            <div v-loading="archiveState.detailLoading || archiveState.versionLoading" class="view-left">
              <MedicalRecordTree
                :can-collapse="true"
                :base-info="archiveState.baseInfo"
                :tree-info="archiveState.treeInfo"
                @click-pdf="node => handlePdfClick(node, 'archive')"
              />
            </div>
            <div class="view-middle common-box-shadow">
              <PdfPreviewComponent ref="archivistPdfRef" :src="archiveState.pdfSrc" />
            </div>
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, ref, watch } from "vue"
import { useRoute } from "vue-router"
import { ArrowLeft } from "@element-plus/icons-vue"
import { PdfPreviewComponent } from "@/base-components"
import { MedicalRecordTree } from "@/page-components"
import { getRecallApprovedDetailApi, getRecallVersionApi, getArchiveVersionApi } from "../../interface"
import { getArchiveVersionDetailApi } from "@/interfaces"
import { decryptStr, toastError } from "@/utils"

const route = useRoute()

const archivistPdfRef = ref()
const recallPdfRef = ref()

const moveTogether = ref(false)

// 同步滚动开启/关闭
watch(
  () => moveTogether.value,
  val => {
    const archivistContent = archivistPdfRef.value.pdfAreaRef
    const recallContent = recallPdfRef.value.pdfAreaRef
    archivistContent.onscroll = val ? e => scrollTogether(archivistContent, recallContent) : () => {}
    recallContent.onscroll = val ? e => scrollTogether(recallContent, archivistContent) : () => {}
  }
)

// 同步滚动
function scrollTogether(source, target) {
  const sourceScrollWidth = source.scrollWidth
  const targetScrollWidth = target.scrollWidth
  //   横向滚动：可能有一方的目录收缩隐藏了，因此需要乘上比例
  const scrollScale = targetScrollWidth / sourceScrollWidth
  target.scrollLeft = source.scrollLeft * scrollScale
  target.scrollTop = source.scrollTop
}

// 点击目录
const handlePdfClick = (node, type) => {
  if (type === "recall") {
    recallState.pdfSrc = "/api" + node?.contentFilePath
    recallState.targetNode = node
  } else if (type === "archive") {
    archiveState.pdfSrc = "/api" + node?.contentFilePath
    archiveState.targetNode = node
  }
}

onMounted(() => {
  getRecallData()
  getArchiveData()
})

/* ========================= 召回版本 ========================= */
const recallState = reactive({
  // 召回版本
  versionLoading: false,
  data: [] as Array<Record<string, any>>,
  activeRecallVersion: "",

  // 召回详情
  detailLoading: false,
  baseInfo: {} as any, // 患者基础信息
  pdfSrc: "",
  targetNode: {} as any, // 右侧元数据需要的信息
  firstPageFields: [] as any[], // 首页字段
  treeInfo: {} as any // 左侧tree需要的原始数据
})

// 获取召回版本
function getRecallData() {
  recallState.versionLoading = true
  getRecallVersionApi(route.query.inpNo)
    .then(res => {
      const data = res.data.data || []
      recallState.data = data
      if (data.length > 0) {
        recallState.activeRecallVersion = data[0].wsid
        getRecallDetail()
      }
      recallState.versionLoading = false
    })
    .catch(err => {
      toastError(err, "获取召回版本失败")
      recallState.versionLoading = false
    })
}

// 获取当前版本详情
function getRecallDetail() {
  recallState.detailLoading = true
  getRecallApprovedDetailApi(recallState.activeRecallVersion)
    .then(res => {
      if (res.data.code !== "100100000") return
      const recordDetail = res.data.data
      recallState.baseInfo = recordDetail.baseInfo || {}
      recallState.firstPageFields = recordDetail.firstPageFields || []
      recallState.treeInfo = recordDetail.treeInfo
      recallState.detailLoading = false
    })
    .catch(error => {
      recallState.detailLoading = false
      toastError(error, "获取召回数据失败")
    })
}

/* ========================= 归档版本 ========================= */
const archiveState = reactive({
  // 召回版本
  versionLoading: false,
  data: [] as Array<Record<string, any>>,
  activeRecallVersion: "",

  // 召回详情
  detailLoading: false,
  baseInfo: {} as any, // 患者基础信息
  pdfSrc: "",
  targetNode: {} as any, // 右侧元数据需要的信息
  firstPageFields: [] as any[], // 首页字段
  treeInfo: {} as any // 左侧tree需要的原始数据
})

// 获取召回版本
function getArchiveData() {
  archiveState.versionLoading = true
  getArchiveVersionApi(route.query.inpNo)
    .then(res => {
      const data = res.data.data || []
      archiveState.data = data
      if (data.length > 0) {
        archiveState.activeRecallVersion = data[0].code
        getArchiveDetail()
      }
      archiveState.versionLoading = false
    })
    .catch(err => {
      toastError(err, "获取归档版本失败")
      archiveState.versionLoading = false
    })
}

function getArchiveDetail() {
  archiveState.detailLoading = true
  const secretKetString = route.query.sealKey as string
  const sealKey = secretKetString ? decryptStr(secretKetString) : ""
  getArchiveVersionDetailApi({
    inpNo: route.query.inpNo as string,
    versionCode: archiveState.activeRecallVersion,
    coverView: false,
    sealKey
  })
    .then(res => {
      if (res.data.code !== "100100000") return
      const recordDetail = res.data.data
      archiveState.baseInfo = recordDetail.baseInfo || {}
      archiveState.firstPageFields = recordDetail.firstPageFields || []
      archiveState.treeInfo = recordDetail.treeInfo
      archiveState.detailLoading = false
    })
    .catch(error => {
      archiveState.detailLoading = false
      toastError(error, "获取召回数据失败")
    })
}
</script>

<style lang="less" scoped>
.record-compare-container {
  width: 100vw;
  height: 100vh;
  .el-container {
    height: 100%;
  }
  .el-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
    background-color: white;
    border-bottom: solid 1px rgb(225 226 230);
    padding-left: 0px;
    .back-action {
      display: flex;
      align-items: center;
      height: 100%;
      cursor: pointer;
      padding: 0px 20px;
      .el-icon {
        margin-right: 8px;
      }
      &:hover {
        color: #73767a;
      }
    }
  }
  .compare-container {
    display: flex;
    height: calc(100% - 60px);
    column-gap: 20px;
    .version-root {
      flex: 1;
      min-width: 0px;
      display: flex;
      flex-direction: column;
      .version-choose {
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        .el-select {
          width: 183px;
        }
      }
    }
    .version-container {
      display: flex;
      height: calc(100% - 48px);
      .view-middle {
        flex: 1;
        min-width: 0px;
      }
    }
  }
}
</style>
