<template>
  <DialogContainer
    v-model:visible="visible"
    title="自动导出配置"
    :width="550"
    :confirm-callback="handleConfirm"
    :confirm-loading="confirmLoading"
  >
    <el-form
      ref="ruleFormRef"
      :model="exportForm"
      label-position="right"
      :label-width="130"
      label-suffix="："
      :rules="exportFormRules"
    >
      <el-form-item label="导出配置" prop="inpatientHqmsExportAutoSwitch">
        <el-switch v-model="exportForm.inpatientHqmsExportAutoSwitch" />
      </el-form-item>
      <el-form-item label="IP地址" prop="inpatientHqmsExportIp">
        <el-input v-model="exportForm.inpatientHqmsExportIp" />
      </el-form-item>
      <el-form-item label="保存地址" prop="inpatientHqmsExportDirectory">
        <el-input v-model="exportForm.inpatientHqmsExportDirectory" />
      </el-form-item>
      <el-form-item label="开始时间" prop="inpatientHqmsExportStartDate">
        <el-date-picker v-model="exportForm.inpatientHqmsExportStartDate" type="date" clearable value-format="x" />
      </el-form-item>
      <el-form-item prop="inpatientHqmsExportDay">
        <template #label>
          数据量（天）
          <el-tooltip
            class="item"
            effect="dark"
            content="导出N天的数据量，默认导出1天，最大支持导出31天的数据"
            placement="top"
          >
            <i class="ri-question-fill" style="font-size: 18px"></i>
          </el-tooltip>
          ：
        </template>
        <el-input v-model="exportForm.inpatientHqmsExportDay" type="number" min="1" max="31">
          <template #append>天</template>
        </el-input>
      </el-form-item>
    </el-form>
  </DialogContainer>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from "vue"
import { DialogContainer } from "@/base-components"
import { querySystemConfig, updateSystemConfig } from "@/interfaces"
import { Message, toastError } from "@/utils"
import type { FormRules } from "element-plus"

const visible = ref(false)
const confirmLoading = ref(false)

const exportForm = reactive({
  inpatientHqmsExportAutoSwitch: "" as any,
  inpatientHqmsExportIp: "",
  inpatientHqmsExportDirectory: "",
  inpatientHqmsExportStartDate: 0, // 开始日期
  inpatientHqmsExportDay: 1 // 数据量(天)
})

onMounted(async () => {
  await querySystemConfig({ type: "OUTPATIENT_HQMS" }).then(res => {
    res.data.data?.forEach((item: Record<string, any>) => {
      if (item.key === "inpatientHqmsExportStartDate") {
        exportForm.inpatientHqmsExportStartDate = Number(item.value)
      } else {
        exportForm[item.key] = item.value
      }
    })
  })
  exportForm.inpatientHqmsExportAutoSwitch = exportForm.inpatientHqmsExportAutoSwitch === "1"
})
const ruleFormRef = ref()

const handleConfirm = async () => {
  ruleFormRef.value?.validate(async valid => {
    if (!valid) return
    try {
      confirmLoading.value = true
      await updateSystemConfig([
        { key: "inpatientHqmsExportAutoSwitch", value: exportForm.inpatientHqmsExportAutoSwitch ? "1" : "0" },
        { key: "inpatientHqmsExportIp", value: exportForm.inpatientHqmsExportIp },
        { key: "inpatientHqmsExportDirectory", value: exportForm.inpatientHqmsExportDirectory },
        {
          key: "inpatientHqmsExportStartDate",
          value: new Date(exportForm.inpatientHqmsExportStartDate).setHours(0, 0, 0, 0)
        },
        { key: "inpatientHqmsExportDay", value: exportForm.inpatientHqmsExportDay }
      ])
      confirmLoading.value = false
      Message.success("保存成功")
      visible.value = false
    } catch (error: any) {
      confirmLoading.value = false
      toastError(error, "保存失败")
    }
  })
}

const show = () => {
  visible.value = true
}

defineExpose({
  show
})

const exportFormRules: FormRules = {
  inpatientHqmsExportAutoSwitch: [{ required: true, message: "请设置", trigger: "blur" }],
  inpatientHqmsExportIp: [
    { required: true, message: "请输入IP地址", trigger: "blur" }
    // { validator: ipValidator, trigger: "blur" }
  ],
  inpatientHqmsExportDirectory: [
    { required: true, message: "请输入保存地址", trigger: "blur" }
    // { validator: directoryValidator, trigger: "blur" }
  ]
}
</script>
