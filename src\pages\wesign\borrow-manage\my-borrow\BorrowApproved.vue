<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchForm
        :form-config="borrowApprovedSearchFormConfig"
        :form-state="searchFormState"
        @query-btn-click="handleQuery"
        @reset-btn-click="handleReset"
      />
    </template>

    <template #table>
      <CommonTable
        table-id="borrowApprovedTable"
        :table-columns="borrowApprovedColumns"
        :request-api="getBorrowListApi"
        :request-params="searchParams"
      >
        <template #applyDatetime="{ row }">
          {{ formatDatetime(row.applyDatetime) }}
        </template>
        <template #outHospitalDatetime="{ row }">
          {{ formatDatetime(row.outHospitalDatetime) }}
        </template>
        <template #approverDatetime="{ row }">
          {{ formatDatetime(row.approverDatetime) }}
        </template>
        <template #timeLimitCh="{ row }">
          {{
            row.approveTimeLimitUnitEnumName === "FOREVER"
              ? "永久"
              : row.approveTimeLimitCh + unitOptions[row.approveTimeLimitUnitEnumName]
          }}
        </template>
        <template #operation="{ row }">
          <TableButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.View)"
            :disabled="row.viewPermissionEnumName === 'DISABLE'"
            @click="handleRowClick(row, 'detail')"
          >
            查看
          </TableButton>
          <TableButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Print)"
            :disabled="row.printPermissionEnumName === 'DISABLE'"
            @click="handleRowClick(row, 'print')"
          >
            打印
          </TableButton>
          <TableButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Export)"
            :disabled="row.exportPermissionEnumName === 'DISABLE'"
            @click="handleRowClick(row, 'export')"
          >
            导出PDF
          </TableButton>
        </template>
      </CommonTable>
    </template>

    <DialogContainer v-model:visible="printState.dialogVisible" title="病案打印" :width="660" :no-footer="true">
      <div v-loading="printState.loading" class="print">
        <div class="print__content">
          <div class="print-desc">打印此次借阅病历资料，具体打印内容如下：</div>
          <div class="print-catalogue-list">
            <div v-for="(item, index) in printState.fileList" :key="item.mrClassCode" class="print-catalogue">
              {{ `${index + 1}、${item.mrClassName}` }}
            </div>
          </div>
        </div>
        <div class="print__footer">
          <div>共计{{ printState.fileList.length }}份文件</div>
          <div>
            <el-button @click="clearPrintState">取消</el-button>
            <el-button type="primary" @click="confirmPrint">确定</el-button>
          </div>
        </div>
      </div>
    </DialogContainer>

    <SealDialog ref="sealDialogRef" :selected-row="medicalRecord" :confirm-callback="confirmOperation"></SealDialog>

    <!-- 打印密码校验弹窗 -->
    <PrintPasswordDialog ref="printPasswordDialogRef" @confirm="confirm" />

    <!-- 导出pdf下载格式选择 -->
    <DialogContainer
      v-model:visible="exportTypeDialogVisible"
      title="设置"
      :confirm-callback="confirmExport"
      :confirm-loading="printState.loading"
    >
      <div class="export-content">
        <div>导出样式</div>
        <el-select v-model="exportType" style="width: 285px" placeholder="请选择下载样式">
          <el-option
            v-for="item in downloadPdfTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div>
    </DialogContainer>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import { useRouter } from "vue-router"
import JSZip from "jszip"
import printJS from "print-js"
import { CommonTable, DialogContainer, TableButton, PageContainer } from "@/base-components"
import { TabsRouter, SealDialog, SearchForm, PrintPasswordDialog } from "@/page-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { getPrintPasswordStatus, downloadFileApi } from "@/interfaces"
import { useSystemStore, useUserStore } from "@/stores"
import { encryptStr, Message, toastError, formatDatetime } from "@/utils"
import {
  borrowApprovedColumns,
  tabsRouterList,
  menuId,
  borrowApprovedSearchFormConfig,
  unitOptions,
  downloadPdfTypeOptions
} from "./config"
import { exportBorrowFilesApi, getBorrowPrintInfoApi, printBorrowFilesApi, getBorrowListApi } from "./interface"

const { hasOperationPermission } = useUserStore()
const systemStore = useSystemStore()

const router = useRouter()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  patientFilterProp: "",
  patientFilterValue: "",
  outHospitalDeptWsid: "",
  applyDatetime: "",
  outHospitalDatetime: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState, "status=1")

/* ======================== 封存弹窗相关 ======================== */

type OperationType = "print" | "detail" | "export" | ""

// 输入封存代码之后进行的操作类型 - 打印/去详情
const operationType = ref<OperationType>("")
const medicalRecord = ref<Record<string, any> | null>(null) // 用户点击的病案
const sealDialogRef = ref() // SealDialog组件ref

const handleRowClick = (row, type) => {
  medicalRecord.value = row
  operationType.value = type
  sealDialogRef.value.checkSealed()
}

const printPasswordDialogRef = ref()

const confirm = data => {
  if (data) {
    printPasswordDialogRef.value?.close()
    showPrintDialog()
  } else {
    Message.error("密码错误，请重试")
  }
}

// 检查封存之后的回调
const confirmOperation = async () => {
  if (operationType.value === "print") {
    // 打印
    systemStore.showLoading("请稍候")
    try {
      const status = (await getPrintPasswordStatus())?.data?.data?.status
      systemStore.hideLoading()
      if (status === "ENABLE") {
        printPasswordDialogRef.value?.openDialog()
      } else {
        showPrintDialog()
      }
    } catch (err: any) {
      toastError(err)
    }
  } else if (operationType.value === "export") {
    // 导出
    // confirmExport()
    exportTypeDialogVisible.value = true
  } else if (operationType.value === "detail") {
    // 查看详情
    const query = { id: medicalRecord.value?.id, type: "my" }
    if (sealDialogRef.value.secretKey) {
      query["secretKey"] = encryptStr(sealDialogRef.value.secretKey)
    }
    router.push({
      path: "/borrow/detail",
      query: query
    })
  }
}

/* ======================== 打印 ======================== */

interface PrintState {
  dialogVisible: boolean
  fileList: any[] // 打印文件的列表
  loading: boolean
  printId: string
}

const printState = reactive<PrintState>({
  dialogVisible: false,
  fileList: [],
  loading: false,
  printId: ""
})

// 打开打印弹窗
const showPrintDialog = () => {
  printState.dialogVisible = true
  printState.loading = true
  getBorrowPrintInfoApi(medicalRecord.value?.id)
    .then(res => {
      printState.fileList = res.data.data || []
      printState.loading = false
      printState.printId = medicalRecord.value?.id
    })
    .catch(error => {
      toastError(error)
    })
}

// 点击确认打印
const confirmPrint = () => {
  printState.dialogVisible = false
  const requestParams = {
    // paramsId: printState.printId,
    sealKey: sealDialogRef.value?.secretKey || "", // 有sealKey则带入
    // type: "ZIP",
    type: "COMPOSE",
    source: "BORROW_PRINT",
    ext: medicalRecord.value?.id,
    inpNo: medicalRecord.value?.inpNo
  }
  systemStore.showLoading("请稍候")
  downloadFileApi(requestParams)
    .then(res => {
      const data = URL.createObjectURL(new Blob([res.data], { type: "application/pdf" }))
      printJS(data)
      systemStore.hideLoading()
    })
    .catch(err => {
      const decoder = new TextDecoder("utf-8")
      err.response.data = JSON.parse(decoder.decode(err.response.data))
      systemStore.hideLoading()
      toastError(err, "操作失败")
    })
}

// 创建iframe进行打印
const createIframe = (content, fileName?: string) => {
  const pdfUrl = window.URL.createObjectURL(new Blob([content], { type: "application/pdf" }))
  const curDate = new Date().getTime()
  const iframeId = "printPdf" + curDate
  const iframe = document.createElement("iframe")
  iframe.style.display = "none"
  iframe.style.pageBreakBefore = "always"
  iframe.setAttribute("id", iframeId)
  iframe.setAttribute("name", "printPdf" + (fileName || curDate))
  iframe.src = pdfUrl
  document.body.appendChild(iframe)
  // @ts-ignore
  const iframeWindow = document.getElementById(iframeId)?.contentWindow
  setTimeout(() => {
    iframeWindow.print()
  }, 100)
  window.URL.revokeObjectURL(iframe.src)
}

// 清除打印数据
const clearPrintState = () => {
  printState.loading = false
  printState.fileList = []
  printState.printId = ""
  printState.dialogVisible = false
}

/* ======================== 导出 ======================== */

const exportType = ref("COMPOSE")
const exportTypeDialogVisible = ref(false)
// 确认导出
const confirmExport = () => {
  const requestParams = {
    sealKey: sealDialogRef.value.secretKey || "",
    // paramsId: medicalRecord.value?.id,
    type: exportType.value,
    source: "BORROW_EXPORT",
    ext: medicalRecord.value?.id,
    inpNo: medicalRecord.value?.inpNo
  }
  systemStore.showLoading("正在导出，请稍候")
  downloadFileApi(requestParams)
    // exportBorrowFilesApi(requestParams)
    .then(res => {
      systemStore.hideLoading()
      Message.success("导出文件成功")
      const fileData = res.data
      const blob = new Blob([fileData], {
        type: exportType.value === "COMPOSE" ? "application/pdf" : "application/zip"
      })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement("a")
      link.style.display = "none"
      link.href = url
      // link.setAttribute("download", "导出文件" + `${exportType.value === "COMPOSE" ? ".pdf" : ".zip"}`)
      link.setAttribute(
        "download",
        `${decodeURIComponent(res.headers["content-disposition"].split("filename=")[1].replaceAll('"', ""))}`
      )
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    })
    .catch(error => {
      systemStore.hideLoading()
      toastError(error, "导出文件失败")
    })
}
</script>

<style lang="less" scoped>
.print {
  color: #0a1633;
}

.print-catalogue-list {
  height: 300px;
  font-weight: bold;
  padding: 12px 20px;
  background-color: rgb(250 250 250);
  overflow-y: auto;
  margin-top: 20px;
}

.print-catalogue {
  height: 32px;
  line-height: 32px;
}

.print__footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
}
.export-content {
  display: flex;
  flex-wrap: nowrap;
  gap: 10px;
  align-items: center;
}
</style>
