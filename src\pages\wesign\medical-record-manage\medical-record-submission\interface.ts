import axios from "@/interfaces/axios-instance"

// 获取病案列表
export const getSubmissionListApi = (params: IPaginationRequestParams) => {
  return axios({
    method: "get",
    url: "/api/document/bags/nurse-station",
    params
  })
}

// 获取病案提交配置 - 是否需要签名
export const getSubmitConfigApi = (documentBagWsid: string) => {
  return axios({
    method: "get",
    url: `/api/document/bags/${documentBagWsid}/submit-config`
  })
}
