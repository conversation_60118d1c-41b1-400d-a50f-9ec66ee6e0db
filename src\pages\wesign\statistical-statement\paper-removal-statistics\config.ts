import type { TableColumnItem } from "@/types"

export const departmentPaperRemovalColumns: Array<TableColumnItem> = [
  { prop: "deptName", label: "科室", minWidth: 150 },
  { prop: "totalElectronicPage", label: "脱纸页数", minWidth: 150, sortable: true },
  { prop: "totalPage", label: "总页数", minWidth: 150, sortable: true },
  { prop: "percentage", label: "脱纸率", minWidth: 150, sortable: true }
]

export const writPaperRemovalColumns: Array<TableColumnItem> = [
  { prop: "mrClassCode", label: "分类代码", minWidth: 150 },
  { prop: "mrClassName", label: "分类名称", minWidth: 150 },
  { prop: "totalPage", label: "总页数", minWidth: 150 },
  { prop: "totalElectronicPage", label: "采集", minWidth: 150 },
  { prop: "totalPaperPage", label: "翻拍", minWidth: 150 },
  { prop: "percentage", label: "脱纸率", minWidth: 150 }
]

export const tabsRouterList = [
  {
    label: "科室脱纸率",
    path: "/statistical-statement/paper-removal-statistics/department"
  },
  {
    label: "文书脱纸率",
    path: "/statistical-statement/paper-removal-statistics/writ"
  }
]

export function handleYear(val) {
  const startOfYear = new Date(val, 0, 1).getTime() // 该年第一个月的第一天
  const endOfYear = new Date(val, 11, 31).getTime() // 该年最后一个月的最后一天
  return [startOfYear, endOfYear]
}

export function handleMonth(val) {
  const [year, month] = val.split("-")
  const startOfMonth = new Date(year, month - 1, 1).getTime()
  const endOfMonth = new Date(year, month, 0).getTime()
  return [startOfMonth, endOfMonth]
}

export function getQuartersTimestamps(year) {
  const quarters = {
    Q1: { start: null, end: null },
    Q2: { start: null, end: null },
    Q3: { start: null, end: null },
    Q4: { start: null, end: null }
  }

  // 计算每个季度的开始和结束日期
  for (let q = 1; q <= 4; q++) {
    const firstDay = new Date(year, (q - 1) * 3, 1)
    const lastDay = new Date(year, q * 3, 0) // 获取季度最后一天的日期对象
    quarters[`Q${q}`] = {
      start: firstDay.getTime(), // 转换为时间戳
      end: lastDay.getTime() // 转换为时间戳
    }
  }

  return quarters
}
