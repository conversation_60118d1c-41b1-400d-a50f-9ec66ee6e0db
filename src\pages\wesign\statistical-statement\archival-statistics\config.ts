import type { TableColumnItem } from "@/types"

export const archivalStatisticsColumn: Array<TableColumnItem> = [
  { prop: "deptName", label: "出院科室", minWidth: 150 },
  { prop: "totalOutCount", label: "出院人数", minWidth: 120 },
  { prop: "totalArchiveCount", label: "归档数", minWidth: 120 }
]

export const archivalStatisticsDetailColumn: Array<TableColumnItem> = [
  { prop: "patientName", label: "姓名", minWidth: 100 },
  { prop: "mrNo", label: "病案号", minWidth: 150 },
  { prop: "inpNo", label: "患者编号", minWidth: 120 },
  { prop: "visitId", label: "住院次数", minWidth: 120 },
  { prop: "outHospitalDatetime", label: "出院时间", minWidth: 180 },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 120 },
  { prop: "archiveTime", label: "归档时间", minWidth: 120 },
  { prop: "archiveDays", label: "归档天数", minWidth: 120 },
  { prop: "archiverName", label: "归档人", minWidth: 120 }
]

// 归档天数枚举
export enum ArchiveDayItemEnum {
  ARCHIVE_ITEM_LET_2 = "ARCHIVE_ITEM_LET_2", // 2日
  ARCHIVE_ITEM_LET_3 = "ARCHIVE_ITEM_LET_3", // 3日
  ARCHIVE_ITEM_LET_5 = "ARCHIVE_ITEM_LET_5", // 5日
  ARCHIVE_ITEM_LET_7 = "ARCHIVE_ITEM_LET_7", // 7日
  ARCHIVE_ITEM_GT_7 = "ARCHIVE_ITEM_GT_7", // 超7日
  ARCHIVE_ITEM_AVERAGE = "ARCHIVE_ITEM_AVERAGE" // 平均天数
}

// 归档天数类型枚举
export enum ArchiveItemOperatorsEnum {
  LET = "LET", // 小于等于
  GT = "GT" // 大于
}

export const archivalDaysOptions = [
  { label: "2日", value: ArchiveDayItemEnum.ARCHIVE_ITEM_LET_2 },
  { label: "3日", value: ArchiveDayItemEnum.ARCHIVE_ITEM_LET_3 },
  { label: "5日", value: ArchiveDayItemEnum.ARCHIVE_ITEM_LET_5 },
  { label: "7日", value: ArchiveDayItemEnum.ARCHIVE_ITEM_LET_7 },
  { label: "超7日", value: ArchiveDayItemEnum.ARCHIVE_ITEM_GT_7 }
]

export const archiveItemOperatorsOptions = [
  { label: "小于等于", value: ArchiveItemOperatorsEnum.LET },
  { label: "大于", value: ArchiveItemOperatorsEnum.GT }
]

// 对动态生成的列进行排序
export function sortDynamicColumn(columnsData) {
  // 排序函数
  columnsData.sort((a: any, b: any) => {
    // 提取数字的正则表达式
    const regex = /(\d+)/

    // // 提取归档数和归档率的标识
    const isRateA = a.label.includes("率")
    const isRateB = b.label.includes("率")
    const isOverdueA = a.label.includes("超出")
    const isOverdueB = b.label.includes("超出")
    const isAverageA = a.label.includes("平均")
    const isAverageB = b.label.includes("平均")

    // 如果a是"平均归档数"，则放在后面
    if (isAverageA && !isAverageB) {
      return 1
    }

    // 如果b是"平均归档数"，则放在后面
    if (!isAverageA && isAverageB) {
      return -1
    }

    // 如果a同时包含"超出"和"归档数"或"归档率"，则放在后面
    if (isOverdueA && (isRateA || !isRateA) && !isOverdueB) {
      return 1
    }

    // 如果b同时包含"超出"和"归档数"或"归档率"，则放在后面
    if (!isOverdueA && (isRateB || !isRateB) && isOverdueB) {
      return -1
    }

    // // 提取数字部分
    const numA = parseInt(a.label.match(regex) && a.label.match(regex)[0])
    const numB = parseInt(b.label.match(regex) && b.label.match(regex)[0])
    // // 数字小的排在前面
    if (numA !== numB) {
      return numA - numB
    }

    // 任何带有"归档数"字符的项排在带有"归档率"字符的项前面
    if (a.label.includes("归档数") && b.label.includes("归档率")) {
      return -1
    }
    if (a.label.includes("归档率") && b.label.includes("归档数")) {
      return 1
    }

    // 归档数在归档率前面
    if (isRateA !== isRateB) {
      return isRateA ? 1 : -1
    }

    // 带有超出归档数字符的排在没有超出字符的后面
    if (isOverdueA !== isOverdueB) {
      return isOverdueA ? 1 : -1
    }

    // 如果以上都不满足，则保持原来的顺序
    return 0
  })
}

// 解析动态配置的那部分字段
export function parseColumn(archivalData) {
  const labelArray = [] as any
  const fields: Array<string> = []
  for (const key in archivalData[0]) {
    fields.push(key)
  }
  const patternCountLet = /^let(\d|\d{2,})Count$/
  const patternPercentageLet = /^let(\d|\d{2,})Percentage$/
  const patternCountGt = /^gt(\d|\d{2,})Count$/
  const patternPercentageGt = /^gt(\d|\d{2,})Percentage$/
  fields.forEach(field => {
    if (patternCountLet.exec(field)) {
      const match = patternCountLet.exec(field) as RegExpExecArray
      labelArray.push({
        prop: field,
        label: `${match[1]}日归档数`,
        minWidth: 100
      })
    } else if (patternPercentageLet.exec(field)) {
      const match = patternPercentageLet.exec(field) as RegExpExecArray
      labelArray.push({
        prop: field,
        label: `${match[1]}日归档率`,
        minWidth: 100
      })
    } else if (patternCountGt.exec(field)) {
      const match = patternCountGt.exec(field) as RegExpExecArray
      labelArray.push({
        prop: field,
        label: `超出${match[1]}日归档数`,
        minWidth: 150
      })
    } else if (patternPercentageGt.exec(field)) {
      const match = patternPercentageGt.exec(field) as RegExpExecArray
      labelArray.push({
        prop: field,
        label: `超出${match[1]}日归档率`,
        minWidth: 150
      })
    } else if (field === "avgArchiveDays") {
      labelArray.push({
        prop: field,
        label: `平均归档天数`,
        minWidth: 120
      })
    }
  })
  sortDynamicColumn(labelArray)
  return labelArray
}

// 获取所有归档数的prop
export const getArchivalCountProp = (archivalData) => {
  const propsArray = [] as Array<string>
  const fields: Array<string> = []
  for (const key in archivalData[0]) {
    fields.push(key)
  }
  const patternCountLet = /^let(\d|\d{2,})Count$/ // N日归档数的正则
  const patternCountGt = /^gt(\d|\d{2,})Count$/ // 超出N日归档数的正则
  fields.forEach(field => {
    if (patternCountLet.exec(field)) {
      propsArray.push(field)
    } else if(patternCountGt.exec(field)) {
      propsArray.push(field)
    }
  })
  return propsArray
}

// 获取归档天数(自定义配置)
export const getCustomConfig = (configData) => {
  const resultCode: Array<Record<string, any>> = []
  configData.forEach(item => {
    if (item.archiveCountTitle !== "平均归档天数") {
      if (item.archiveCountCheck) {
        const codeObj = {
          label: item.archiveCountTitle,
          value: item.archiveCountCode
        }
        resultCode.push(codeObj)
      }
    }
  })
  sortDynamicColumn(resultCode)
  return resultCode
}
