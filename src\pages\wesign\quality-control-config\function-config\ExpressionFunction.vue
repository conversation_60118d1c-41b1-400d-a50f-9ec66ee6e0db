<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <CommonInputFormItem v-model="formData.funName" label="函数名" />
        <CommonInputFormItem v-model="formData.code" label="函数代码" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="expressFunctionTableRef"
        :table-columns="expressionFunctionColumns"
        :request-api="getQcFunctionList"
        :request-params="searchParams"
      >
        <template #header>
          <AddButton @click="handleOperation('add')">新增</AddButton>
        </template>
        <template #operation="{ row }">
          <TableButton @click="handleOperation('edit', row)">编辑</TableButton>
          <TableButton @click="modifyStatus(row)">
            {{ row.status === "ENABLE" ? "禁用" : "启用" }}
          </TableButton>
          <TableButton v-if="!row.isDefault" @click="handleDelete(row)">删除</TableButton>
        </template>
      </CommonTable>
    </template>

    <DialogContainer
      v-model:visible="state.dialogVisible"
      class="code-container-dialog"
      :title="state.updateRuleType === 'edit' ? '修改表达式函数' : '添加表达式函数'"
      :width="1200"
      :confirm-callback="handleConfirm"
    >
      <el-form
        ref="ruleFormRef"
        label-position="right"
        label-width="130px"
        label-suffix="："
        :model="ruleData"
        :rules="ruleConfigFormRules"
      >
        <CommonInputFormItem v-model="ruleData.funName" label="函数名" prop="funName" />
        <CommonInputFormItem v-model="ruleData.code" label="函数代码" prop="code" />
        <el-form-item label="代码块" prop="funSource">
          <!-- <el-input v-model="ruleData.funSource" type="textarea" placeholder="请输入" :rows="10" /> -->
          <Codemirror
            v-model="ruleData.funSource"
            placeholder="请输入代码块"
            :tab-size="10"
            :extensions="extensions"
            :style="{ height: '100%', width: '100%' }"
          />
        </el-form-item>
        <CommonInputFormItem v-model="ruleData.funMethodName" label="执行方法名" prop="funMethodName" />

        <el-form-item :error="validationMessage">
          <template #label>{{ ` ` }}</template>
          <el-input v-model="validationInput" placeholder="请输入测试值(按方法参数顺序逗号隔开)">
            <template #append>
              <el-button
                type="primary"
                style="background-color: var(--el-color-primary); color: #fff; border-radius: 0"
                @click="handleExpressionCheck"
              >
                点击验证
              </el-button>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
    </DialogContainer>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, computed } from "vue"
import { java } from "@codemirror/lang-java"
import { Codemirror } from "vue-codemirror"
import {
  PageContainer,
  SearchContainer,
  CommonTable,
  AddButton,
  TableButton,
  CommonInputFormItem,
  DialogContainer
} from "@/base-components"
import { TabsRouter } from "@/page-components"
import { useTableSearch } from "@/hooks"
import { SystemPrompt, Message, toastError } from "@/utils"
import {
  getQcFunctionList,
  addQcFunction,
  editQcFunction,
  deleteQcFunction,
  modifyQcFunctionStatus,
  getQcFunctionDetail,
  checkQcFunctionStatus
} from "../interface"
import { tabsRouterList, expressionFunctionColumns, ruleConfigFormRules, myTheme } from "./config"

/* ======================== 搜索相关数据及方法 ======================== */
const expressFunctionTableRef = ref()

const formData = reactive({
  funName: "",
  code: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(formData)

/* ======================== 弹窗相关数据及方法 ======================== */

const state = reactive({
  dialogVisible: false,
  updateRuleType: "add",
  activeRow: {} as any
})

const ruleData = reactive({
  funName: "",
  funSource: "",
  code: "",
  funMethodName: ""
})

// 新增、编辑
const handleOperation = async (type: string, row?: Record<string, any>) => {
  try {
    state.updateRuleType = type
    if (type === "edit") {
      const result = (await getQcFunctionDetail(row?.code))?.data?.data
      for (const key in ruleData) {
        ruleData[key] = result[key]
      }
    } else {
      for (const key in ruleData) {
        ruleData[key] = ""
      }
      ruleData.funSource = "//请输入代码块\n\n\n\n\n\n\n\n\n\n"
    }

    state.dialogVisible = true
    if (row) state.activeRow = row
  } catch (err: any) {
    toastError(err)
  }
}

const ruleFormRef = ref()

const handleConfirm = () => {
  ruleFormRef.value?.validate(valid => {
    if (!valid) return

    const handle = state.updateRuleType === "add" ? addQcFunction : editQcFunction

    handle(ruleData)
      .then(res => {
        Message.success(state.updateRuleType === "add" ? "添加成功" : "编辑成功")
        state.dialogVisible = false
        expressFunctionTableRef.value?.refreshTableData()
      })
      .catch(err => toastError(err))
  })
}

const modifyDataSourcesInfo = (code: string, nextStatus: "ENABLE" | "DISABLE") => {
  modifyQcFunctionStatus({ code: code, status: nextStatus })
    .then(() => {
      Message.success("修改状态成功")
      expressFunctionTableRef.value?.refreshTableData()
    })
    .catch(err => toastError(err))
}

// 切换启用&禁用状态
const modifyStatus = row => {
  const nextStatus = row.status === "ENABLE" ? "DISABLE" : "ENABLE"
  SystemPrompt(`是否确定${row.status === "ENABLE" ? "禁用" : "启用"}函数${row.funName} `).then(() => {
    modifyDataSourcesInfo(row.code, nextStatus)
  })
}

// 删除
function handleDelete(row) {
  SystemPrompt(`您确定要删除函数${row.funName} ?`).then(() => {
    deleteQcFunction(row.code)
      .then(() => {
        Message.success("删除数据成功")
        expressFunctionTableRef.value?.refreshTableData()
      })
      .catch(err => toastError(err))
  })
}

const validationInput = ref("") // 规则表达式校验值输入框
const validationMessage = ref("") // 规则表达式校验错误信息

// 校验表达式规则
function handleExpressionCheck() {
  checkQcFunctionStatus({
    params: validationInput.value,
    funSource: ruleData.funSource,
    funMethodName: ruleData.funMethodName
  })
    .then(resData => {
      // 校验成功
      if (resData.data.data) {
        validationMessage.value = ""
        Message.success("校验成功")
      } else {
        Message.error("校验失败")
        validationMessage.value = "校验失败"
      }
    })
    .catch(err => toastError(err, "校验失败"))
}

const extensions = [java(), myTheme]
</script>

<style lang="less" scoped>
:deep(.cm-editor) {
  border: 1px solid #dcdfe6;
}

:deep(.cm-focused) {
  outline: none !important;
}

:deep(.cm-lineNumbers) {
  border-right: 1px solid #dcdfe6;
}
.code-container-dialog {
  :deep(.el-dialog__body) {
    max-height: 75vh;
    overflow-y: auto;
  }
}
</style>
