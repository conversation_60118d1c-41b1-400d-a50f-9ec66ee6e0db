<template>
  <!-- <el-drawer v-model="panelVisible" title="编辑表单控件属性" :size="280" :before-close="beforeClose"> -->
  <!-- 字段属性 -->
  <div ref="widgetDrawerListRef" class="widget-drawer-list">
    <el-tabs v-model="activeTab" class="form-edit-tabs">
      <el-tab-pane label="字段属性" :name="TabEnum.Field"></el-tab-pane>
      <el-tab-pane label="表单属性" :name="TabEnum.Form"></el-tab-pane>
    </el-tabs>

    <template v-if="activeTab === TabEnum.Field">
      <!-- 字段类型 -->
      <div class="config-type">
        {{ translateTypeToName(drawerConfig?.type) }}
      </div>

      <!-- 分组标题 -->
      <div v-if="drawerConfig?.type === FormItemType.GroupHeadline" class="widget-props-panel">
        <el-form size="default" label-position="top" :model="drawerConfig">
          <el-form-item>
            <template #label>
              字段名称
              <el-tooltip placement="top" content="用于标识字段展示名称">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </template>
            <el-input v-model="drawerConfig.name" maxlength="50" show-word-limit clearable></el-input>
          </el-form-item>

          <el-form-item label="标题大小">
            <el-radio-group v-model="drawerConfig.size">
              <el-radio-button v-for="size in fontSizeOptions" :key="size.value" :label="size.value">
                {{ size.label }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="对齐方式">
            <el-radio-group v-model="drawerConfig.align">
              <el-radio-button v-for="align in textAlignContentTypes" :key="align.value" :label="align.value">
                <el-tooltip :content="align.label" placement="top">
                  <i :class="align.icon"></i>
                </el-tooltip>
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="字体颜色">
            <el-color-picker v-model="drawerConfig.fontColor"></el-color-picker>
          </el-form-item>

          <el-form-item label="字段占比">
            <el-radio-group v-model="drawerConfig.width" size="small">
              <el-radio-button label="25%">1/4</el-radio-button>
              <el-radio-button label="33%">1/3</el-radio-button>
              <el-radio-button label="50%">1/2</el-radio-button>
              <el-radio-button label="66%">2/3</el-radio-button>
              <el-radio-button label="75%">3/4</el-radio-button>
              <el-radio-button label="100%">1</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>

      <!-- 字段控件编辑 -->
      <div v-else-if="drawerConfig" class="widget-props-panel">
        <el-form ref="formRef" size="default" label-position="top" :model="drawerConfig">
          <el-form-item prop="name" :rules="nameRequired">
            <template #label>
              字段名称
              <el-tooltip placement="top" content="用于标识字段展示名称">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </template>
            <el-input v-model="drawerConfig.name" maxlength="50" show-word-limit clearable></el-input>
          </el-form-item>

          <el-form-item label="字段标识" prop="prop" :rules="propRule">
            <template #label>
              字段标识
              <el-tooltip placement="top" content="属性标识仅支持英文、数字和下划线">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </template>
            <el-input v-model="drawerConfig.prop" maxlength="50" show-word-limit clearable />
          </el-form-item>

          <!-- 诊断关联 -->
          <el-form-item v-if="drawerConfig.type === FormItemType.Table" label="诊断关联">
            <el-select
              v-model="drawerConfig.association"
              clearable
              filterable
              :style="{
                width: '100%',
                minWidth: '200px'
              }"
            >
              <el-option
                v-for="option in diagnosticAssociationOptions"
                :key="option.value"
                :value="option.value"
                :label="option.label"
              />
            </el-select>
          </el-form-item>

          <el-form-item v-if="drawerConfig.type !== FormItemType.DescText">
            <template #label>
              描述
              <el-tooltip placement="top" content="用于提示用户填写的字段具体描述">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </template>
            <el-input v-model="drawerConfig.desc" maxlength="50" show-word-limit clearable></el-input>
          </el-form-item>

          <el-form-item v-if="drawerConfig.type === FormItemType.BaseAddress" label="地址精度">
            <el-select v-model="drawerConfig.addressAccuracy" style="width: 100%" @change="refreshConfigRule">
              <el-option
                v-for="item in drawerConfig.addressAccuracyOption"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
          </el-form-item>

          <el-form-item v-if="hasDefault" label="默认值" :error="drawerConfig.error">
            <!-- 单天日期 -->

            <div v-if="drawerConfig.type === FormItemType.BaseDate">
              <el-select
                v-if="drawerConfig.type === FormItemType.BaseDate"
                v-model="drawerConfig.default"
                :style="{
                  width: '100%',
                  minWidth: '200px'
                }"
              >
                <el-option label="填写当时" value="today">填写当时</el-option>
                <el-option label="自定义" value="custom">自定义</el-option>
              </el-select>
              <el-date-picker
                v-if="drawerConfig.default === 'custom'"
                v-model="drawerConfig.customValue"
                :style="{
                  marginTop: '10px',
                  width: '100%'
                }"
                :type="getDateType(drawerConfig.dateFormat)"
                :format="drawerConfig.dateFormat"
                value-format="x"
                clearable
              />
            </div>

            <!-- 多选 -->
            <el-select
              v-else-if="showOptionConfig && drawerConfig.multiple"
              v-model="drawerConfig.default"
              style="width: 100%"
              filterable
              :multiple="true"
              clearable
            >
              <el-option
                v-for="option in drawerConfig.options"
                :key="option.value"
                :value="option.value"
                :label="option.label"
              />
            </el-select>
            <!-- 单选 -->
            <el-select
              v-else-if="showOptionConfig && !drawerConfig.multiple"
              v-model="drawerConfig.default"
              style="width: 100%"
              filterable
              clearable
            >
              <el-option
                v-for="option in drawerConfig.options"
                :key="option.value"
                :value="option.value"
                :label="option.label"
              />
            </el-select>
            <!-- 数字 -->
            <el-input
              v-else-if="drawerConfig.type === FormItemType.BaseNumber"
              v-model="drawerConfig.default"
              type="number"
              clearable
            />
            <!-- 普通文本 -->
            <el-input
              v-else-if="editLengthRange"
              v-model="drawerConfig.default"
              type="text"
              clearable
              :maxlength="drawerConfig.maxLength || 999"
              show-word-limit
            />
            <!-- 地址 -->
            <div
              v-else-if="drawerConfig.type === FormItemType.BaseAddress"
              style="width: 100%; display: flex; flex-direction: column"
            >
              <el-cascader
                v-model="drawerConfig.addressSelect"
                style="width: 100%; margin-bottom: 8px"
                placeholder="请选择"
                :options="regionData"
                :props="{ value: 'label' }"
                @change="changeValue"
              />
              <el-input
                v-model="drawerConfig.default"
                type="text"
                clearable
                :maxlength="drawerConfig.maxLength || 999"
                show-word-limit
              />
            </div>

            <el-input v-else v-model="drawerConfig.default" type="text" clearable />
          </el-form-item>

          <!-- 表单是否可编辑 -->
          <el-form-item label="字段属性">
            <el-radio-group v-model="drawerConfig.fieldEditable">
              <el-radio-button label="edit">编辑</el-radio-button>
              <el-radio-button label="readOnly">只读</el-radio-button>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="drawerConfig.type === FormItemType.BaseNumber" label="单位">
            <el-input v-model="drawerConfig.unit" clearable />
          </el-form-item>

          <el-form-item
            v-if="drawerConfig.type === FormItemType.BaseNumber || drawerConfig.type === FormItemType.Table"
            :label="drawerConfig.type === FormItemType.BaseNumber ? '取值范围' : '表格行数'"
            :error="numberRangeError"
          >
            <div style="display: flex; column-gap: 5px">
              <el-input-number
                v-model="drawerConfig.min"
                placeholder="最小值"
                type="number"
                :min="numberMin"
                :max="numberMax"
                :value-on-clear="numberMin"
                :controls="false"
                clearable
                @change="refreshConfigRule"
              />
              -
              <el-input-number
                v-model="drawerConfig.max"
                placeholder="最大值"
                type="number"
                :min="numberMin"
                :max="numberMax"
                :value-on-clear="numberMax"
                :controls="false"
                clearable
                @change="refreshConfigRule"
              />
            </div>
          </el-form-item>

          <el-form-item v-if="drawerConfig.type === FormItemType.BaseNumber">
            <template #label>
              <el-checkbox v-model="drawerConfig.allowDecimal"></el-checkbox>
              允许小数
            </template>

            <span
              class="flex-start"
              :style="{
                gap: '5px',
                color: '#606266'
              }"
            >
              限制
              <el-input-number
                v-model="drawerConfig.precision"
                controls-position="right"
                placeholder="请输入数字的精度"
                :style="{
                  width: '40%'
                }"
                :min="0"
                :precision="0"
                :value-on-clear="0"
                @change="refreshConfigRule"
              />
              位
            </span>
          </el-form-item>

          <!-- 下拉选项的数据源 -->
          <div v-if="drawerConfig.type === FormItemType.BaseSelect">
            <el-form-item label="数据源" prop="collectSourceType">
              <el-select
                v-model="drawerConfig.collectSourceType"
                clearable
                style="width: 100%"
                placeholder="请选择取值来源"
                @change="selectCollectSourceType"
              >
                <el-option
                  v-for="option in collectSourceTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              v-if="drawerConfig.collectSourceType === 'INTERFACE'"
              style="width: 100%"
              label="接口地址"
              prop="collectValueSource"
            >
              <!-- <el-input v-model.trim="drawerConfig.collectValueSource" placeholder="请输入接口地址" /> -->
              <el-select
                v-model="drawerConfig.collectValueSource"
                style="width: 100%"
                filterable
                clearable
                placeholder="请选择接口地址"
              >
                <el-option v-for="option in apiList" :key="option.value" :label="option.label" :value="option.value" />
              </el-select>
            </el-form-item>
            <el-form-item v-if="drawerConfig.collectSourceType === 'METADATA'" label="取值范围" prop="valueRangeKey">
              <el-select
                v-model="drawerConfig.valueRangeKey"
                style="width: 100%"
                filterable
                clearable
                placeholder="请选择取值范围"
              >
                <el-option
                  v-for="(option, index) in fieldRangeOptions"
                  :key="index"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>

          <el-form-item v-if="showOptionConfig">
            <template #label>
              <div class="options-label">
                <span>
                  选项配置
                  <el-tooltip placement="top" content="配置控件选项">
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </span>
              </div>
            </template>
            <div class="option-group">
              <VueDraggable v-model="drawerConfig.options" :animation="15" handle=".handle" class="option-group">
                <div
                  v-for="(item, index) in drawerConfig.options?.filter(item => !item?.other)"
                  :key="item?.id"
                  class="option"
                >
                  <div>
                    <span>文本：</span>
                    <el-input
                      v-model="item.label"
                      clearable
                      style="width: 75px"
                      @blur="validateOption(item, 'label')"
                      @change="changeOptions(item)"
                    />
                  </div>
                  <div>
                    <span>值：</span>
                    <el-input
                      v-model="item.value"
                      clearable
                      style="width: 75px"
                      @blur="validateOption(item, 'value')"
                      @input="changeOptions(item)"
                    />
                  </div>
                  <i class="ri-delete-bin-line icon-action" @click="deleteOption(index)"></i>
                  <i v-if="!item?.other" class="ri-draggable handle drag-option"></i>
                </div>
              </VueDraggable>
              <div
                v-for="(item, index) in drawerConfig.options?.filter(item => item?.other)"
                :key="item.value"
                class="option"
              >
                <span :style="{ width: '160px', color: '#606266' }">{{ item.label }}</span>

                <i class="ri-delete-bin-line icon-action" @click="deleteOption(drawerConfig?.options?.length - 1)"></i>
              </div>
              <el-button type="primary" @click="addOptionItem">添加新选项</el-button>
              <div class="flex-between">
                <el-button :disabled="drawerConfig?.options?.some(item => item?.other)" text @click="addOtherOption">
                  添加其他
                </el-button>
                <el-button text @click="bulkEditOptionLabel">批量编辑</el-button>
              </div>
            </div>
          </el-form-item>

          <el-form-item v-if="showOptionConfig" label="逻辑表单">
            <el-button style="width: 100%" icon="Plus" @click="toConfigLogicForm">点击配置</el-button>
          </el-form-item>

          <el-form-item
            v-if="drawerConfig.type === FormItemType.BaseRadio || drawerConfig.type === FormItemType.BaseCheckbox"
            label="选择方式"
          >
            <el-radio-group v-model="drawerConfig.arrangement">
              <el-radio-button label="horizontal">横向排列</el-radio-button>
              <el-radio-button label="vertical">竖向排列</el-radio-button>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="drawerConfig.type === FormItemType.BaseRadio">
            <el-checkbox v-model="drawerConfig.titleAndOptionsInRow">标题与选项一行排列</el-checkbox>
          </el-form-item>

          <el-form-item v-if="drawerConfig.type === FormItemType.BaseRadio">
            <el-checkbox v-model="drawerConfig.showDivider">是否显示分割线</el-checkbox>
          </el-form-item>

          <el-form-item v-if="drawerConfig.type === FormItemType.BaseSelect" label="取值字段">
            <el-radio-group v-model="drawerConfig.optionKey">
              <el-radio-button label="label">文本</el-radio-button>
              <el-radio-button label="value">值</el-radio-button>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="drawerConfig.type === FormItemType.BaseSelect" label="选择方式">
            <el-radio-group v-model="drawerConfig.multiple">
              <el-radio-button :label="false">下拉单选</el-radio-button>
              <el-radio-button :label="true">下拉多选</el-radio-button>
            </el-radio-group>
          </el-form-item>

          <!-- 单行、多行 -->
          <el-form-item v-if="drawerConfig.type === FormItemType.BaseInput" label="文本类型">
            <el-radio-group v-model="drawerConfig.lineType">
              <el-radio-button label="single" border>单行</el-radio-button>
              <el-radio-button label="multiple" border>多行</el-radio-button>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="editPlaceholder" label="输入提示">
            <el-input v-model="drawerConfig.placeholder" clearable maxlength="20" show-word-limit></el-input>
          </el-form-item>

          <!-- 编码类型 -->
          <el-form-item v-if="drawerConfig.type === FormItemType.Table" label="编码类型">
            <el-select
              v-model="drawerConfig.icdType"
              filterable
              clearable
              :style="{
                width: '100%',
                minWidth: '200px'
              }"
            >
              <el-option
                v-for="option in icdTypeOptions"
                :key="option.value"
                :value="option.value"
                :label="option.label"
              />
            </el-select>
          </el-form-item>

          <!-- 关联字段 -->
          <el-form-item label="关联字段">
            <el-select
              v-model="drawerConfig.fieldLinkage"
              filterable
              clearable
              :style="{
                width: '100%',
                minWidth: '200px'
              }"
            >
              <el-option
                v-for="option in fieldLinkageOptions"
                :key="option.value"
                :value="option.value"
                :label="option.label"
              />
            </el-select>
          </el-form-item>

          <!-- 表格关联字段 -->
          <el-form-item v-if="isTableColumn" label="表格关联字段">
            <el-select
              v-model="drawerConfig.tableFieldLinkage"
              filterable
              clearable
              :style="{
                width: '100%',
                minWidth: '200px'
              }"
            >
              <el-option
                v-for="option in tableFieldLinkageOptions"
                :key="option.value"
                :value="option.value"
                :label="option.label"
              />
            </el-select>
          </el-form-item>

          <!-- 映射字段 （同步到医保使用） -->
          <el-form-item v-if="isTableColumn" label="映射字段">
            <el-select
              v-model="drawerConfig.mapField"
              filterable
              clearable
              :style="{
                width: '100%',
                minWidth: '200px'
              }"
            >
              <el-option
                v-for="option in tableFieldLinkageOptions"
                :key="option.value"
                :value="option.value"
                :label="option.label"
              />
            </el-select>
          </el-form-item>

          <!-- 表格计数字段 -->
          <el-form-item v-if="drawerConfig.type === FormItemType.Table" label="表格计数字段">
            <el-select
              v-model="drawerConfig.tableDataCountField"
              filterable
              clearable
              :style="{
                width: '100%',
                minWidth: '200px'
              }"
            >
              <el-option
                v-for="option in fieldLinkageOptions"
                :key="option.value"
                :value="option.value"
                :label="option.label"
              />
            </el-select>
          </el-form-item>

          <el-form-item v-if="editDateFormat" label="显示格式">
            <el-select v-model="drawerConfig.dateFormat" @change="refreshConfigRule">
              <el-option v-for="item in dateFormatOptions" :key="item.value" :value="item.value" :label="item.label" />
            </el-select>
          </el-form-item>

          <el-form-item v-if="drawerConfig.type === FormItemType.DescText" label="内容">
            <TextEditor
              ref="editorRef"
              v-model="drawerConfig.content"
              :content="drawerConfig.content"
              required
              @get-html="getHtml"
            />
          </el-form-item>

          <el-form-item v-if="!drawerConfig.tableFormItem" label="字段占比">
            <el-radio-group v-model="drawerConfig.width" size="small">
              <el-radio-button label="25%">1/4</el-radio-button>
              <el-radio-button label="33%">1/3</el-radio-button>
              <el-radio-button label="50%">1/2</el-radio-button>
              <el-radio-button label="66%">2/3</el-radio-button>
              <el-radio-button label="75%">3/4</el-radio-button>
              <el-radio-button label="100%">1</el-radio-button>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="drawerConfig.type === FormItemType.Table" label="排列方式">
            <el-radio-group v-model="drawerConfig.tableArrangement" @change="changeTableArrangement">
              <el-radio-button label="table">表格方式</el-radio-button>
              <el-radio-button label="list">平铺方式</el-radio-button>
            </el-radio-group>
          </el-form-item>

          <el-form-item
            v-if="drawerConfig.type === FormItemType.Table && drawerConfig.tableArrangement === 'list'"
            label="表格一行展示数量"
          >
            <el-input-number
              v-model="drawerConfig.column"
              controls-position="right"
              placeholder="请输入展示数量"
              :style="{
                width: '40%'
              }"
              :min="1"
              :precision="0"
              :value-on-clear="0"
            />
          </el-form-item>

          <el-form-item v-if="drawerConfig.type === FormItemType.Table">
            <el-checkbox v-model="drawerConfig.tableIndex">显示表格序号</el-checkbox>
          </el-form-item>

          <!-- 子表单全局配置 -->
          <el-form-item v-if="drawerConfig.type === FormItemType.Table" label="子表单全局配置">
            <div class="operation-config">
              <div class="operation-config-content">
                <div v-for="item in drawerConfig.tableGlobalConfiguration" :key="item.key" class="attribute-item">
                  <el-checkbox v-model="item.checked" :label="item.checkboxLabel" style="flex: 1" />
                  <el-input v-model="item.label" style="width: 100px; margin-right: 5px" />
                  <el-button
                    :style="{ color: item.key === 'add' ? '#fff' : '#409EFF' }"
                    type="text"
                    icon="Setting"
                    @click="openSyncConfig(item)"
                  ></el-button>
                </div>
              </div>
            </div>
          </el-form-item>

          <el-form-item v-if="drawerConfig.type === FormItemType.Table">
            <el-checkbox v-model="drawerConfig.operate">显示表格操作项</el-checkbox>
          </el-form-item>
          <!-- 操作项配置 -->
          <el-form-item v-if="drawerConfig.type === FormItemType.Table && drawerConfig.operate" label="">
            <div class="operation-config">
              <div class="icon-control">
                <div>操作项配置</div>
                <el-checkbox v-model="drawerConfig.tableOperationIcon" label="显示图标" />
              </div>
              <div class="operation-config-content">
                <div v-for="item in drawerConfig.tableOperationConfig" :key="item.key" class="attribute-item">
                  <el-checkbox v-model="item.checked" :label="item.checkboxLabel" />
                  <el-input v-model="item.label" style="width: 142px" />
                  <el-button type="text" :icon="item.icon"></el-button>
                </div>
              </div>
            </div>
          </el-form-item>
          <!-- 
          <el-form-item>
            <el-checkbox v-model="drawerConfig.canShowTitle">显示标题</el-checkbox>
          </el-form-item> -->

          <el-form-item>
            <el-checkbox v-model="drawerConfig.fixed">固定到顶部</el-checkbox>
          </el-form-item>

          <el-form-item v-if="isRequired">
            <el-checkbox v-model="drawerConfig.required">必填项</el-checkbox>
          </el-form-item>

          <!-- 逻辑展示表单 -->
          <!-- <el-form-item>
            <el-checkbox v-model="drawerConfig.isLogicForm">是否为逻辑表单</el-checkbox>
          </el-form-item> -->

          <el-form-item v-if="editLengthRange" label="字符长度" :error="lengthRangeError">
            <div style="display: flex; column-gap: 5px">
              <el-input-number
                v-model="drawerConfig.minLength"
                placeholder="最少字符"
                :min="0"
                :max="999"
                :value-on-clear="0"
                clearable
                :style="{
                  width: '40%'
                }"
                controls-position="right"
                @change="refreshConfigRule"
              />
              -
              <el-input-number
                v-model="drawerConfig.maxLength"
                placeholder="最多字符"
                :min="0"
                :max="999"
                :precision="0"
                :style="{
                  width: '40%'
                }"
                :value-on-clear="999"
                clearable
                controls-position="right"
                @change="refreshConfigRule"
              />
            </div>
          </el-form-item>

          <!-- 限定输入格式 -->
          <el-form-item v-if="drawerConfig.type === FormItemType.BaseInput">
            <div
              class="flex-between"
              :style="{
                width: '100%'
              }"
            >
              <el-checkbox v-model="drawerConfig.canFilterRegex" @change="openFilterRegexDialog">
                限定输入格式
              </el-checkbox>

              &nbsp;
              <el-button
                v-if="drawerConfig.filterRegex && drawerConfig.filterRegex.length > 0"
                size="small"
                text
                type="primary"
                :icon="Plus"
                @click="addFilterRegex"
              ></el-button>
            </div>
            <div
              class="flex-column"
              :style="{
                width: '100%'
              }"
            >
              <div v-for="rule in drawerConfig.filterRegex" :key="rule.id" class="flex-between filter-regex-item">
                <span
                  :style="{
                    flex: '1'
                  }"
                  class="flex-between"
                  @click="editFilterRegex(rule)"
                >
                  {{ rule.title }}
                  <i class="ri-edit-line"></i>
                </span>
                <i
                  :style="{
                    paddingLeft: '30px'
                  }"
                  class="ri-close-line"
                  @click="deleteFilterRegex(rule)"
                ></i>
              </div>
            </div>
          </el-form-item>

          <!-- <el-form-item v-if="canShowColumn">
          <el-checkbox v-model="drawerConfig.canShowColumn">在列表显示</el-checkbox>
        </el-form-item>

        <el-form-item v-if="canShowStatistic">
          <el-checkbox v-model="drawerConfig.canShowStatistic">在统计显示</el-checkbox>
        </el-form-item>

        <el-form-item v-if="canSearch">
          <el-checkbox v-model="drawerConfig.canSearch">作为列表查询项</el-checkbox>
        </el-form-item> -->
        </el-form>
      </div>

      <!-- 空白 -->
      <div v-else class="blank">请在左侧画布选中节点</div>
    </template>
    <template v-if="activeTab === TabEnum.Form">
      <div class="form-attribute">
        <div class="form-attribute-title">操作项配置</div>
        <div class="form-attribute-content">
          <div v-for="item in props.formOperationConfig" :key="item.value" class="attribute-item">
            <el-checkbox v-model="item.checked" :disabled="item.must" :label="item.checkboxLabel" />
            <el-input v-model="item.label" style="width: 142px" />
          </div>
        </div>
      </div>
    </template>
  </div>
  <!-- </el-drawer> -->

  <!-- 批量编辑选项 -->
  <el-dialog v-model="bulkEditOptionState.visible">
    <el-input
      v-model="bulkEditOptionState.label"
      type="textarea"
      autosize
      placeholder="请输入选项内容，以换行分隔"
    ></el-input>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="bulkEditOptionState.visible = false">取消</el-button>
        <el-button type="primary" @click="confirmBulkEditOptions">确定</el-button>
      </div>
    </template>
  </el-dialog>

  <el-dialog v-model="bulkEditOptionState.visible">
    <el-input
      v-model="bulkEditOptionState.label"
      type="textarea"
      autosize
      placeholder="请输入选项内容，以换行分隔"
    ></el-input>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="bulkEditOptionState.visible = false">取消</el-button>
        <el-button type="primary" @click="confirmBulkEditOptions">确定</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 医保同步、病理诊断同步配置 -->
  <el-dialog v-model="syncVIsible" :title="syncKey === 'sync-medicalInsurance' ? '同步至医保配置' : '同步至病理配置'">
    <!-- 医保 -->
    <template v-if="syncKey === 'sync-medicalInsurance'">
      <el-form label-width="140px" label-position="left">
        <!-- 医保对照关联 -->
        <el-form-item v-if="drawerConfig?.type === FormItemType.Table" label="关联医保表格">
          <el-select
            v-model="drawerConfig.medicalInsuranceTable"
            filterable
            clearable
            :style="{
              width: '100%',
              minWidth: '200px'
            }"
          >
            <el-option
              v-for="option in medicalInsuranceTableOptions"
              :key="option.value"
              :value="option.value"
              :label="option.label"
            />
          </el-select>
        </el-form-item>

        <!-- 医保编码字段 -->
        <el-form-item v-if="drawerConfig?.type === FormItemType.Table" label="医保编码字段">
          <el-select
            v-model="drawerConfig.medicalInsuranceCode"
            filterable
            clearable
            :style="{
              width: '100%',
              minWidth: '200px'
            }"
          >
            <el-option
              v-for="option in tableFieldLinkageOptions"
              :key="option.value"
              :value="option.value"
              :label="option.label"
            />
          </el-select>
        </el-form-item>

        <!-- 医保编码名称字段 -->
        <el-form-item v-if="drawerConfig?.type === FormItemType.Table" label="医保编码名称字段">
          <el-select
            v-model="drawerConfig.medicalInsuranceName"
            filterable
            clearable
            :style="{
              width: '100%',
              minWidth: '200px'
            }"
          >
            <el-option
              v-for="option in tableFieldLinkageOptions"
              :key="option.value"
              :value="option.value"
              :label="option.label"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </template>

    <!-- 病理 -->
    <template v-if="syncKey === 'sync-pathology'">
      <el-form label-width="140px" label-position="left">
        <el-form-item v-if="drawerConfig?.type === FormItemType.Table" label="关联病理表格">
          <el-select
            v-model="drawerConfig.pathologicalDiagnosisTable"
            filterable
            clearable
            :style="{
              width: '100%',
              minWidth: '200px'
            }"
          >
            <el-option
              v-for="option in medicalInsuranceTableOptions"
              :key="option.value"
              :value="option.value"
              :label="option.label"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </template>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="syncVIsible = false">取消</el-button>
        <el-button type="primary" @click="syncVIsible = false">确定</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 逻辑表单配置 -->
  <el-dialog v-model="logicFormRuleVIsible" title="添加逻辑表单规则">
    <div class="logic-form-rule">
      <div class="logic-form-rule__title">选择选项后，才会显示所设置的其他字段</div>
      <div class="logic-form-rule__item" style="color: #606266">
        <div class="option">选项内容</div>
        <div class="filed">显示字段</div>
      </div>
      <div class="logic-form-rule__content">
        <div
          v-for="(item, index) in drawerConfig.options?.filter(item => !item?.other)"
          :key="index"
          class="logic-form-rule__item"
        >
          <div class="option">{{ item.label }}</div>
          <div class="filed">
            <el-select
              v-model="drawerConfig.logicFormConfig[item.value]"
              filterable
              clearable
              multiple
              :style="{
                width: '100%',
                minWidth: '200px'
              }"
              @change="changeLogicForm"
            >
              <el-option
                v-for="option in fieldLinkageOptions"
                :key="option.value"
                :value="option.value"
                :label="option.label"
              />
            </el-select>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>

  <!-- 限定输入格式弹窗 -->
  <FilterRegexDialog ref="filterRegexRef" @change-regex="confirmFilterRegex" />
</template>

<script setup lang="ts">
import { on } from "events"
import { ref, watch, computed, nextTick, reactive, onMounted } from "vue"
import { regionData } from "element-china-area-data"
import { flow, prop, map, filter, join, split, compact } from "lodash/fp"
import { cloneDeep } from "lodash-es"
import { v4 as uuidv4 } from "uuid"
import { VueDraggable } from "vue-draggable-plus"
import { QuestionFilled, Plus } from "@element-plus/icons-vue"
import FilterRegexDialog from "./components/filter-regex-dialog.vue"
import TextEditor from "./components/text-editor.vue"
import {
  dateFormatOptions,
  getFormFiledShowPermission,
  nameRequired,
  collectSourceTypeOptions,
  FormItemTypeTitle,
  tableOperationConfig,
  icdTypeOptions
} from "./config"
import type { BaseOptionItem } from "@/types"
import { FormItemType, FormItemConfig } from "@/configs"
import { searchStandardRangeClassifyApi, queryDictionaryDetailByGroup } from "@/interfaces"
import {
  getDateType,
  textAlignContentTypes,
  getCustomRule,
  Message,
  checkRuleWithDefaultValue,
  fontSizeOptions
} from "@/utils"

const props = defineProps<{
  visible: boolean
  config: FormItemConfig | null
  formConfigs: Array<FormItemConfig>
  formOperationConfig: any
  classifyDetail: any
}>()

const emits = defineEmits(["update:visible", "update:config", "update:classifyDetail"])

const panelVisible = ref(false)

const drawerConfig = ref<FormItemConfig | null>(null)

const widgetDrawerListRef = ref()

// 外部值同步到组件内部
watch(
  () => props.visible,
  () => {
    panelVisible.value = props.visible
    nextTick(() => {
      if (widgetDrawerListRef.value && props.visible) {
        widgetDrawerListRef.value.scrollTop = 0
      }
    })
  },
  { immediate: true }
)

watch(
  () => props.config,
  val => {
    drawerConfig.value = val
  }
)

const isTableColumn = computed(() => {
  const allTableFormConfig = props.formConfigs
    ?.filter(item => item.type === FormItemType.Table)
    ?.reduce((pre, next) => {
      return pre.concat(next?.tableFormConfig || [])
    }, [])

  return allTableFormConfig.some((item: Record<string, any>) => item.prop === drawerConfig.value?.prop)
})

// 默认值校验
watch(
  () => drawerConfig.value?.default,
  val => {
    if (
      !val || //值不存在
      lengthRangeError.value || //长度校验失败
      numberRangeError.value ||
      !drawerConfig.value?.rule || //校验规则不存在
      [FormItemType.BaseDate].includes(drawerConfig.value?.type) // 日期格式不用检验
    ) {
      if (drawerConfig.value?.error) drawerConfig.value.error = ""
      return
    }

    refreshConfigRule()
    drawerConfig.value.error = checkRuleWithDefaultValue(val, drawerConfig.value)
  }
)

// 组件内部值同步到外部
watch(panelVisible, () => {
  emits("update:visible", panelVisible.value)
})

/*========================================字段属性、表单属强切换========================================*/
enum TabEnum {
  Field = "Field",
  Form = "Form"
}

const activeTab = ref(TabEnum.Field)

/*========================================富文本编辑========================================*/

const editorRef = ref()

// 获取消息内容回填富文本
function getHtml(html, isSuccess) {
  if (!drawerConfig.value) return
  // backspace会产生空格，影响判断，所以校验失败需要去除空格
  if (isSuccess) {
    drawerConfig.value.content = html
  } else drawerConfig.value.content = ""
}

/* ============================ 特殊配置判断是否显示 ============================== */
const {
  editPlaceholder,
  editLengthRange,
  editDateFormat,
  isRequired,
  canShowColumn,
  canShowStatistic,
  canSearch,
  hasDefault,
  showOptionConfig
} = getFormFiledShowPermission(drawerConfig)

/* ========================================= 校验规则 =========================================== */
const formRef = ref()
// 属性标识校验规则
const propRule = [
  { required: true, message: "请输入该值", trigger: "blur" },
  { validator: propValidate, trigger: "change" }
]

function propValidate(_rule: any, value: string, callback: (err?: Error) => void) {
  const propReg = /^[a-zA-Z0-9\_]+$/
  if (!value) callback()
  // 产品：相同属性标识可存在多个
  //需要检验属性标识是否存在于表单中
  // const isExist = props.formConfigs?.filter(item => item.prop === value).length > 1
  // if (isExist) callback(new Error("属性标识已存在"))
  else if (propReg.test(value)) callback()
  else callback(new Error("属性标识仅支持英文、数字和下划线"))
}

// 输入长度限制
const lengthRangeError = computed(() => {
  if (!drawerConfig.value) return ""
  const { minLength, maxLength } = drawerConfig.value
  if (minLength && maxLength && maxLength < minLength) return "最大长度不能小于最小长度"
  return ""
})

//取值范围限制
const numberRangeError = computed(() => {
  if (!drawerConfig.value) return ""
  const { min, max } = drawerConfig.value
  if (min && max && max < min) return "最大值不能小于最小值"
  return ""
})

// 最小取值
const numberMin = computed(() => {
  return drawerConfig.value?.precision ? Number(`-999999999.${"9".repeat(drawerConfig.value?.precision)}`) : -999999999
})

// 最大取值
const numberMax = computed(() => {
  return drawerConfig.value?.precision ? Number(`999999999.${"9".repeat(drawerConfig.value?.precision)}`) : 999999999
})

// 更新校验正则(日期使用时间戳传输，前端无需校验)
function refreshConfigRule() {
  if (!drawerConfig.value || numberRangeError.value || lengthRangeError.value) {
    if (drawerConfig.value?.error) drawerConfig.value.error = ""
    return
  }
  // 写入校验规则
  const rule = getCustomRule(drawerConfig.value)
  drawerConfig.value.rule = rule.rule
  drawerConfig.value.ruleMessage = rule.ruleMessage
  // 如果已经填写了默认值，要进行校验
  if (drawerConfig.value.default) {
    drawerConfig.value.error = checkRuleWithDefaultValue(drawerConfig.value.default, drawerConfig.value)
  }
}

// 是否关闭抽屉
function beforeClose(done: () => void) {
  if (drawerConfig.value?.type === FormItemType.GroupHeadline) {
    done()
    return
  }
  //校验属性标识是否存在，是否符合规范
  formRef.value?.validate((valid: boolean) => {
    if (!valid) return Message.error("表单中有值不符合规范，请检查表单设置")
    // 表单中有校验失败的则不允许关闭
    if (lengthRangeError.value || drawerConfig.value?.error || numberRangeError.value)
      return Message.error("表单中有值不符合规范，请检查表单设置")
    done()
  })
}

/*===============================单行文本自定义校验规则===============================*/
const filterRegexRef = ref<InstanceType<typeof FilterRegexDialog>>()
// 文本控件自定义输入格式
const openFilterRegexDialog = val => {
  if (!drawerConfig.value) return
  if (val) {
    filterRegexRef.value?.openDialog()
  }
  drawerConfig.value.canFilterRegex = false
  drawerConfig.value.filterRegex = []
}

const editFilterRegex = (regex: Record<string, any>) => {
  if (!drawerConfig.value) return
  filterRegexRef.value?.openDialog(regex)
}

const addFilterRegex = () => {
  if (drawerConfig.value?.filterRegex && drawerConfig.value?.filterRegex?.length >= 5) {
    return Message.warning("最多添加5个限定规则")
  }
  filterRegexRef.value?.openDialog()
}

const deleteFilterRegex = (rule: Record<string, any>) => {
  if (!drawerConfig.value || !drawerConfig.value.filterRegex) return
  drawerConfig.value.filterRegex = drawerConfig.value.filterRegex.filter(item => item.name !== rule.name)
  if (drawerConfig.value.filterRegex?.length === 0) {
    drawerConfig.value.canFilterRegex = false
  }
}

//提交规则
const confirmFilterRegex = (regex: Record<string, string>) => {
  if (!drawerConfig.value || !drawerConfig.value?.filterRegex) return
  if (drawerConfig.value.filterRegex.find(item => item.id === regex.id)) {
    //替换
    drawerConfig.value.filterRegex = drawerConfig.value.filterRegex.map(item => {
      if (item.id === regex.id) return regex
      return item
    })
    return
  }
  drawerConfig.value?.filterRegex.push(regex)
  if (!drawerConfig.value.canFilterRegex) {
    drawerConfig.value.canFilterRegex = true
  }
}

/* ========================================= 选项编辑 =========================================== */

// 添加新的选项
function addOptionItem() {
  let configTemp = drawerConfig.value
  const random = Date.now()
  const newOption = {
    label: `选项${random}`,
    value: `选项${random}`,
    id: uuidv4()
  }
  if (configTemp && Array.isArray(configTemp.options)) {
    const index = configTemp.options.findIndex(item => item.other)
    if (index !== -1) {
      // 如果有其他选项，则添加到其他选项前面
      configTemp.options.splice(index, 0, newOption)
    } else {
      configTemp.options.push(newOption)
    }
    refreshConfigRule()
  }
}

//添加其他选项
function addOtherOption() {
  let configTemp = drawerConfig.value
  if (configTemp && Array.isArray(configTemp.options)) {
    configTemp.options.push({
      label: `其他`,
      value: `其他`,
      other: true,
      id: uuidv4()
    })
    refreshConfigRule()
  }
}

// 批量编辑选项的label
const bulkEditOptionState = reactive({
  visible: false,
  label: "" as string
})

// 打开批量编辑选项
const bulkEditOptionLabel = () => {
  bulkEditOptionState.visible = true
  bulkEditOptionState.label = flow(
    prop("options"),
    filter((item: IBaseOptionItem) => !item?.other),
    map("label"),
    join("\n")
  )(drawerConfig.value)
}

const confirmBulkEditOptions = () => {
  if (!bulkEditOptionState.label || !drawerConfig.value?.options) return
  const otherOption = drawerConfig.value?.options?.find(item => item?.other)
  const bulkEditOptions = flow(
    split("\n"),
    compact,
    map(item => ({
      label: item,
      value: item,
      id: uuidv4()
    }))
  )(bulkEditOptionState.label)
  drawerConfig.value.options = compact([
    ...bulkEditOptions,
    otherOption ? { ...otherOption } : null
  ]) as Array<IBaseOptionItem>

  bulkEditOptionState.visible = false

  refreshConfigRule()
}

// 修改选项配置
function changeOptions(item: Record<string, string>) {
  // 如果已经设置了默认值，则需要判断当前设置的新值是否为修改值
  // 如果默认值不在选项值optionsValue中，则说明当前修改的值就是已经选择的默认值
  if (drawerConfig.value && drawerConfig.value.default) {
    // 选项值
    const optionsValue = drawerConfig.value.options?.map(option => option.value)
    // 多选：多选的值是数组，则替换当前值
    if (drawerConfig.value?.multiple || [FormItemType.BaseCheckbox].includes(drawerConfig.value?.type)) {
      const changeIndex = drawerConfig.value.default.findIndex(tempItem => {
        return !optionsValue?.includes(tempItem)
      })
      if (changeIndex !== -1) {
        drawerConfig.value.default.splice(changeIndex, 1, item.value)
      }
    }
    // 单选：直接赋值默认值
    else {
      if (!optionsValue?.includes(drawerConfig.value.default)) drawerConfig.value.default = item.value
    }
  }

  refreshConfigRule()
}

// 校验选项配置
const validateOption = (item, valueType) => {
  if (!item?.[valueType]) {
    item[valueType] = `选项${new Date().getTime()}`
  }
  //如果item?.[valueType]里有,，需要提示
  if (item?.[valueType].includes(",")) {
    Message.warning("选项值不允许包含英文逗号，将自动为您替换成_")
    item[valueType] = item[valueType].replace(/,/g, "_")
  }
}

// 删除选项
function deleteOption(index) {
  let configTemp = drawerConfig.value
  configTemp?.options?.splice(index, 1)
  refreshConfigRule()
}
const changeValue = value => {
  console.log(`output->value`, value)
  // drawerConfig.value.address = value.join("")
}
const fieldLinkageOptions = computed(() => {
  const normalForm = props.formConfigs
    .filter(item => item.type !== FormItemType.Table)
    .map(item => {
      return {
        label: `${item.name}--${item.prop}`,
        value: item.prop
      }
    })
  const tableForms = props.formConfigs
    .filter(item => item.type === FormItemType.Table)
    .reduce((result, pre) => {
      return result.concat(pre.tableFormConfig || [])
    }, [])
    .map((item: Record<string, any>) => {
      return {
        label: `${item.name}--${item.prop}`,
        value: item.prop
      }
    })
  console.log(`output->tableForms`, tableForms)
  return normalForm.concat(tableForms).filter(item => item.value !== props.config.prop)
})

const tableFieldLinkageOptions = computed(() => {
  const tableForms = props.formConfigs
    .filter(item => item.type === FormItemType.Table)
    .reduce((result, pre) => {
      return result.concat(pre.tableFormConfig || [])
    }, [])
    .map((item: Record<string, any>) => {
      return {
        label: `${item.name}--${item.prop}`,
        value: item.prop
      }
    })

  return tableForms.filter(item => item.value !== props.config.prop)
})

const medicalInsuranceTableOptions = computed(() => {
  const tableForms = props.formConfigs
    .filter(item => item.type === FormItemType.Table)
    .map((item: Record<string, any>) => {
      return {
        label: `${item.name}--${item.prop}`,
        value: item.prop
      }
    })

  return tableForms.filter(item => item.value !== props.config.prop)
})

// 取值范围
const fieldRangeOptions = ref<Array<BaseOptionItem>>([])
// 初始化字段取值范围选项
onMounted(() => {
  searchStandardRangeClassifyApi().then(res => {
    const resData = res.data?.data ?? []
    fieldRangeOptions.value = resData.map(item => ({
      label: `${item.name}(${item.code})`,
      value: item.code
    }))
  })
})

const diagnosticAssociationOptions = computed(() => {
  return props.formConfigs
    .filter(item => item.type === FormItemType.Table && item.prop !== props.config.prop)
    .map(item => {
      return {
        label: item.name,
        value: item.prop
      }
    })
})

// 接口地址列表

const apiList = ref<Array<Record<string, any>>>([])

onMounted(async () => {
  apiList.value = (await queryDictionaryDetailByGroup({ groupKey: "VALUE_SOURCE" }))?.map(item => {
    return {
      label: item.key,
      value: item.value
    }
  })
})

// 切换数据源时，清空上一次选的值
const selectCollectSourceType = val => {
  if (val === "METADATA") {
    drawerConfig.value.collectValueSource = ""
  } else if (val === "INTERFACE") {
    drawerConfig.value.valueRangeKey = ""
  }
}

// 转化表单类型名称
const translateTypeToName = type => {
  return FormItemTypeTitle[type]
}

// 切换table排列方式时，修改默认宽度
const changeTableArrangement = val => {
  drawerConfig.value.width = val === "list" ? "50%" : "100%"
}

/* ========================================= 医保、诊断配置 =========================================== */

const syncKey = ref("")
const syncVIsible = ref(false)

const openSyncConfig = item => {
  console.log(`output->item`, item)
  syncKey.value = item.key
  syncVIsible.value = true
}

/* ========================================= 逻辑表单配置 =========================================== */
const logicFormRuleVIsible = ref(false)

const toConfigLogicForm = () => {
  logicFormRuleVIsible.value = true
}

const changeLogicForm = value => {
  // 所有逻辑表单
  let allLogicFormConfig = cloneDeep(props.formConfigs).reduce((pre, next) => {
    return Object.assign(pre, next?.logicFormConfig)
  }, {})
  // 更新当前选中的逻辑表单
  props.formConfigs.forEach(item => {
    item.isLogicForm = JSON.stringify(allLogicFormConfig || "{}").includes(item.prop)
    if (value.includes(item.prop)) item.isLogicForm = true
  })
  console.log(`output->props.formConfigs`, props.formConfigs)
}
</script>

<style lang="less" scoped>
:deep(.el-overlay) {
  background-color: transparent;
}
:deep(.el-drawer__body) {
  min-height: 0px;
  padding: 10px 10px;
}
.widget-drawer-list {
  width: 280px;
  height: calc(100% - 55px);
  overflow: hidden;
  margin-top: 1px;
}

.config-type {
  padding: 11px 20px;
  border-bottom: 1px solid #f3f3f3;
  color: #0a1633;
}

.widget-props-panel {
  padding: 24px 20px;
  overflow-y: auto;
  height: calc(100% - 90px);

  .columns-group {
    display: flex;
    flex-direction: column;
    row-gap: 10px;

    .columns {
      display: flex;
      border: 1px solid #dcdfe6;
      border-radius: 5px;
      height: 22px;
      align-items: center;
      .columns-name {
        margin-left: 5px;
        font-size: 12px;
        width: 160px;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #606266;
      }
    }
  }
  .option-group {
    display: flex;
    flex-direction: column;
    row-gap: 10px;
    width: 100%;
    .option {
      display: flex;
      align-items: center;
      column-gap: 5px;
      border: 1px solid #dcdfe6;
      padding: 4px 12px;
      border-radius: 4px;
      justify-content: space-between;
    }
    .drag-option {
      cursor: move;
      font-size: var(--custom-default-font-size);
    }
  }
  :deep(.el-form-item--small) {
    margin-bottom: 24px;
  }
  :deep(.el-input) {
    .el-input-group__prepend {
      padding: 0px 5px;
    }
  }

  :deep(.el-input-number) {
    width: 100%;
  }

  .icon-action {
    font-size: var(--custom-default-font-size);
    line-height: 1;
    border-radius: 5px;
    padding: 5px;
    color: #888;
    cursor: pointer;
    &:hover {
      background: rgba(0, 0, 0, 0.04);
    }
  }

  .filter-regex-item {
    cursor: pointer;
    margin: 5px auto 0 0;
    padding: 4px 10px;
    border-radius: 4px;
    background-color: #f5f7fa;
    color: #606266;
    font-size: 12px;
    width: 100%;
    box-sizing: border-box;
  }
}

.blank {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-form-item__label) {
  font-size: 14px !important;
}

:deep(.el-radio-group) {
  width: 100%;
  display: flex !important;
}

:deep(.el-radio-button) {
  flex: 1 !important;
}

:deep(.el-radio-button__inner) {
  width: 100%;
}

.form-edit-tabs {
  background: #f8f8fa;
}
:deep(.el-tabs__nav) {
  width: 100% !important;
  justify-content: space-evenly !important;
  margin-bottom: 3px;
}

:deep(.el-tabs__item.is-active) {
  color: #0a1633;
  font-weight: bold;
}
:deep(.el-tabs__active-bar) {
  // width: 24px !important;
}
:deep(.el-tabs__nav-wrap::after) {
  height: 0px !important;
}

:deep(.el-tabs__header) {
  margin-bottom: 0;
}

.form-attribute {
  padding: 25px 21px;

  .form-attribute-title {
    font-weight: bold;
    font-size: 14px;
    color: #0a1633;
    margin-bottom: 8px;
  }
}

.attribute-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}
.operation-config {
  width: 100%;

  .icon-control {
    // text-align: right;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #606266;
  }
}

.logic-form-rule {
  &__title {
    background-color: #f4f6f9;
    padding: 10px;
  }

  &__content {
    height: 50vh;
    overflow: auto;
  }

  &__item {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #f4f6f9;
    padding: 10px;
    color: #000;

    .option,
    .filed {
      flex: 1;
    }
  }
}
</style>
