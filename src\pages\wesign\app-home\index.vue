<template>
  <el-container style="height: 100%">
    <!-- 顶部待办列表 -->
    <el-header ref="todoListRef" class="todo-list">
      <el-skeleton :loading="todoLoading" :rows="1" :animated="true">
        <CardContainer title="待处理事项" sign>
          <template #default>
            <el-space wrap>
              <div
                v-for="todo in todoStatistics"
                :key="todo.name"
                class="todo-list__item"
                @click="checkTodo(todo.name)"
              >
                <el-badge :hidden="todo.num > 0 ? false : true" :value="todo.num">
                  <img class="todo-list__item-icon" :src="getTodoIcon(todo.name)" />
                </el-badge>
                <div class="todo-list__item-name">{{ todo.name }}</div>
              </div>
              <!-- <el-badge
                v-for="todo in todoStatistics"
                :key="todo.name"
                :hidden="todo.num > 0 ? false : true"
                :value="todo.num"
              >
                <div class="todo-list__item" @click="checkTodo(todo.name)">
                  <img class="todo-list__item-icon" :src="getTodoIcon(todo.name)" />
                  <div class="todo-list__item-name">{{ todo.name }}</div>
                </div>
              </el-badge> -->
            </el-space>
          </template>
        </CardContainer>
      </el-skeleton>
    </el-header>

    <el-main style="overflow-y: scroll; height: 75%">
      <!-- 数据统计 -->
      <el-container style="height: 458px; margin-bottom: 20px">
        <el-main ref="allStatisticsRef" class="all-statistics">
          <CardContainer v-loading="allStatisticsLoading" title="数据统计" sign>
            <template #header>
              <div class="all-statistics-header">
                <div class="date-select">
                  <!-- 日期选择 -->
                  <el-button
                    v-for="dateOption in dateOptions"
                    :key="dateOption.value"
                    class="date-option"
                    :type="selectedDateOption === dateOption.value ? 'primary' : 'default'"
                    size="small"
                    @click="handleSelectDateOption(dateOption.value)"
                  >
                    {{ dateOption.label }}
                  </el-button>
                </div>

                <div class="dept-select">
                  <el-select ref="deptSelect" v-model="customDeptData.type" class="department-select">
                    <el-option
                      v-for="item in deptOptions"
                      :key="item.value"
                      :value="item.value"
                      :label="item.label"
                    ></el-option>
                  </el-select>
                  <div class="select-form">
                    <!-- 科室排名 -->
                    <el-input
                      v-if="customDeptData.type === 'SOME'"
                      v-model="customDeptData.deptNum"
                      style="width: 60px"
                    />
                    <!-- 自定义 -->
                    <DepartmentFormItem
                      v-if="customDeptData.type === 'CUSTOM'"
                      v-model:model-value="outHospitalDepts"
                      label="科室："
                      :multiple="true"
                      :collapse-tags="true"
                      :collapse-tags-tooltip="true"
                      class="dept"
                    />
                  </div>
                </div>
              </div>
            </template>
            <!-- 查询类型分组 -->
            <div class="statistic-type-radio-group">
              <div
                v-for="statistic in allStatistics"
                :key="statistic[0].type"
                class="statistic-type-radio"
                :class="[selectedStatisticType === statistic[0].type ? 'statistic-type-radio--active' : '']"
                @click="handleStatisticTypeChange(statistic[0].type)"
              >
                {{ `${statistic[0].name} / ${statistic[1].name}（${statistic[0].count} / ${statistic[1].count}）` }}
              </div>
            </div>

            <div v-if="allStatistics.length > 0" class="statistic-container">
              <div class="charts-container">
                <HomeChart v-if="chartOption" ref="chartDom" :options="chartOption" :chart-width="chartWidth" />
              </div>
            </div>
            <el-empty v-else description="暂无数据" :image-size="60" />
          </CardContainer>
        </el-main>
      </el-container>

      <el-container class="side-container">
        <!-- 科室统计 -->
        <el-aside ref="departmentStatisticsRef" class="department-statistics">
          <CardContainer v-loading="departmentStatisticsLoading" title="科室统计" sign>
            <template #header>
              <div class="department-statistics-header">
                <el-button
                  v-for="dateOption in dateOptions"
                  :key="dateOption.value"
                  class="date-option"
                  :type="selectedDeptDateOption === dateOption.value ? 'primary' : 'default'"
                  size="small"
                  @click="handleSelectDeptDateOption(dateOption.value)"
                >
                  {{ dateOption.label }}
                </el-button>
              </div>
            </template>
            <DepartmentStatistical
              v-if="departmentStatistics.length > 0"
              :data="departmentStatistics"
            ></DepartmentStatistical>
            <el-empty v-else description="暂无数据" :image-size="60" />
          </CardContainer>
        </el-aside>
        <!-- 消息通知 -->
        <el-aside ref="messageRef" class="message">
          <CardContainer v-loading="messageData.loading" title="消息通知" sign>
            <template #header>
              <div class="message-header">
                <el-button
                  v-for="menu in messageConfig"
                  :key="menu.key"
                  class="date-option"
                  :type="selectedMessage === menu.key ? 'primary' : 'default'"
                  size="small"
                  @click="handleSelectMessageOption(menu.key)"
                >
                  {{ menu.title }}
                </el-button>
                <!-- <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
                  <el-tab-pane
                    v-for="menu in menuConfig"
                    :key="menu.key"
                    :label="menu.title"
                    :name="menu.key"
                  ></el-tab-pane>
                </el-tabs> -->

                <el-button size="small" class="date-option" @click="router.push('/message-center')">
                  更多
                  <el-icon class="el-icon--right"><DArrowRight /></el-icon>
                </el-button>
              </div>
            </template>
            <div class="message-wrapper">
              <div class="message-list">
                <!-- 消息 -->
                <div v-for="item in messageData.rows" :key="item.id" class="message-item">
                  <div class="row-box">
                    <div class="message-type">{{ item.title }}</div>
                    <div class="message-content">{{ item.createDateTimeCh }}</div>
                  </div>
                  <div class="row-box">
                    <div class="message-content">{{ item.content }}</div>
                    <div class="message-action" @click="openDialog(item)">
                      {{ selectedMessage !== MessageConfigEnum.WAIT_DISPOSE ? "查看" : "去处理" }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContainer>
        </el-aside>
      </el-container>
    </el-main>

    <!-- 修改密码弹窗（只出现一次） -->
    <PasswordDialog ref="passwordDialogRef" />

    <Tour v-model:current="current" :open="showTour" :steps="steps" @close="closeTour"></Tour>

    <!-- 系统公告弹窗 -->

    <DialogContainer
      v-if="dialogData.clickItem"
      v-model:visible="dialogData.visible"
      :title="dialogData.clickItem.title"
      :width="450"
      :close-callback="() => readOne(dialogData.clickItem)"
    >
      <div
        v-if="dialogData.clickItem.dialogType === 'HTML'"
        class="detail-content ql-editor"
        v-html="dialogData.clickItem.contentHtml"
      ></div>
      <div v-else v-text="dialogData.clickItem.content"></div>
      <div class="detail-time">{{ dialogData.clickItem.createDateTime }}</div>
      <template #footer>
        <el-button type="primary" @click="dialogData.visible = false">知道了</el-button>
      </template>
    </DialogContainer>
  </el-container>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from "vue"
import { useRouter } from "vue-router"
import { Tour } from "ant-design-vue"
import { EChartsOption } from "echarts"
import { DArrowRight } from "@element-plus/icons-vue"
import { CardContainer, DialogContainer, DepartmentFormItem } from "@/base-components"
import { PasswordDialog } from "@/page-components"
import { shortcuts, StatisticTypeEnum } from "@/configs"
import { useUserStore, useSystemStore } from "@/stores"
import { Message, toastError, formatDatetime } from "@/utils"

import { checkBrowserVersion } from "@/utils/browser-util"
import {
  getAnnouncementsApi,
  getMessageOrDisposeApi,
  readAnnouncementApi,
  readMessageOrDisposeApi
} from "../../message-center/interface"
import HomeChart from "./HomeChart.vue"
import DepartmentStatistical from "./components/DepartmentStatistical.vue"
import {
  getTodoIcon,
  todoRoutePathMap,
  dateOptions,
  getRangeDateByType,
  messageConfig,
  MessageConfigEnum,
  MessageItemType,
  deptOptions,
  formatDepartmentStatisticalData
} from "./config"
import { getTodoStatisticsApi, getDepartmentStatisticsApi, getAllStatisticsApi } from "./interface"

const systemStore = useSystemStore()

/*========================= 漫游式引导操作 ======================= */

const TourGuideKey = "TOUR_GUIDE"

const todoListRef = ref() // 代办列表
const allStatisticsRef = ref() // 数据统计
const departmentStatisticsRef = ref() // 科室统计
const messageRef = ref() //消息通知

const showTour = ref(false)
const current = ref(0)

const outHospitalDepts = ref<Array<string>>([])

const steps = [
  {
    title: "待办列表",
    description: "点击待办列表项可快捷进入对应功能模块",
    target: () => todoListRef.value && todoListRef.value.$el
  },
  {
    title: "数据统计",
    description: "可视化分类展示所有病案的状态统计详情",
    target: () => allStatisticsRef.value && allStatisticsRef.value.$el
  },
  {
    title: "科室统计",
    description: "按科室分类展示病案统计数据",
    target: () => departmentStatisticsRef.value && departmentStatisticsRef.value.$el
  },
  {
    title: "消息通知",
    description: "包含待处理消息、系统通知和系统公告",
    target: () => messageRef.value && messageRef.value.$el
  }
]

function closeTour() {
  showTour.value = false
  localStorage.setItem(TourGuideKey, new Date().getTime().toString())
}

function getTourStatus() {
  if (!localStorage.getItem(TourGuideKey)) {
    showTour.value = true
  }
}

/*========================= 基础 ======================= */

const router = useRouter()
const userStore = useUserStore()

const passwordDialogRef = ref()

onMounted(() => {
  getTodoStatistics()
  getAllStatistics()
  getDepartmentStatistics()
  const browserVersion = checkBrowserVersion()
  if (browserVersion === "HIGH") getTourStatus()
  if (userStore.passwordExpire) {
    passwordDialogRef.value?.openDialog()
    userStore.$patch({ passwordExpire: false })
  }
})

/*========================= 待办 ======================= */

// 代办统计数据
interface TodoStatistic {
  name: string
  num: number
}

const todoLoading = ref(false)
const todoStatistics = ref<TodoStatistic[]>([])

// 获取待办列表信息
const getTodoStatistics = () => {
  todoLoading.value = true
  getTodoStatisticsApi()
    .then(res => {
      todoLoading.value = false
      todoStatistics.value = res.data.data
    })
    .catch(() => {
      todoLoading.value = false
      Message.error("获取待办列表失败！")
    })
}

// 点击todo跳转
const checkTodo = (todoName: string) => {
  const TodoRoutePath = todoRoutePathMap.get(todoName)
  if (TodoRoutePath) router.push(TodoRoutePath)
}

/*========================= 所有统计数据 ======================= */

interface Statistic {
  type: number // 操作类型
  name: string // 操作类型 名称
  count: number
  deptCount: {
    deptWsid: string
    deptName: string // 科室名称
    count: number
  }[]
}

const allStatisticsLoading = ref(true)
// 所有统计数据
const allStatistics = ref<Array<Array<Statistic>>>([])
// 数据统计所选时间选项
const selectedDateOption = ref(dateOptions[0].value)
// 统计数据时间范围
const selectedDateRange = ref<[number, number]>(getRangeDateByType(dateOptions[0].value as StatisticTypeEnum) as any)
// 所选统计类型
const selectedStatisticType = ref(94)
// 表格数据
const chartOption = ref<EChartsOption>()
// 查询数量
const limit = ref(0)

// 自定义科室数据
const customDeptData = reactive({
  type: "ALL",
  deptNum: 0,
  outHospitalDepts: [] as Array<Record<string, any>>
})

const deptSelect = ref()

const handleSelectChange = visible => {
  if (!visible && deptSelect.value) {
    deptSelect.value.focus() // 立即重新获得焦点，保持下拉框展开
  }
}

// 统计数据类型切换
const handleStatisticTypeChange = (statisticType: number) => {
  selectedStatisticType.value = statisticType
  getAllStatistics()
}

const disabledDate = computed(() => {
  return (time: Date) => {
    return time.getTime() > Date.now()
  }
})

// 选择时间范围选项
const handleSelectDateOption = (value: StatisticTypeEnum) => {
  const startTime = getRangeDateByType(value)
  selectedDateOption.value = value
  selectedDateRange.value = startTime as any
  getAllStatistics()
}

// 改变picker数据日期
const handleDateRangeChange = (dateRange: [number, number]) => {
  selectedDateOption.value = ""
  selectedDateRange.value = dateRange
  getAllStatistics()
}

// 获取统计数据
const getAllStatistics = () => {
  allStatisticsLoading.value = true
  getAllStatisticsApi({
    startDate: selectedDateRange.value[0],
    endDate: selectedDateRange.value[1],
    limit: customDeptData.deptNum ? customDeptData.deptNum : null,
    orderType: selectedStatisticType.value
  })
    .then(res => {
      allStatisticsLoading.value = false
      // allStatistics.value = res.data.data
      allStatistics.value = [
        [res.data.data.find(item => item.name === "未采集"), res.data.data.find(item => item.name === "已采集")],
        [res.data.data.find(item => item.name === "已出院"), res.data.data.find(item => item.name === "已提交")],
        [res.data.data.find(item => item.name === "未归档"), res.data.data.find(item => item.name === "已归档")]
      ]

      setChartOption()
    })
    .catch(err => {
      toastError(err, "获取统计数据失败，请重试")
      allStatisticsLoading.value = false
      allStatistics.value = []
      setChartOption()
    })
}

const chartWidth = ref(948)

const curStatisticList = ref<Array<Statistic>>([])

// 设置图标option
const setChartOption = () => {
  // 当前统计类型数据列表
  allStatistics.value.forEach(statistic => {
    if (statistic.some(item => item.type === selectedStatisticType.value)) {
      curStatisticList.value = statistic
    }
  })

  // 当前统计类型名称
  // const selectedStatisticName =
  //   allStatistics.value.find(statistic => statistic.type === selectedStatisticType.value) || []

  // 取数组计算，屏幕宽度和948兜底的最大值
  chartWidth.value = Math.max(curStatisticList?.value[0]?.deptCount?.length * 80 + 200, window.innerWidth * 0.66, 948)

  console.log(`output->chartWidth.value`, chartWidth.value)

  // 筛选自定义科室
  const xAxisData = outHospitalDepts.value.length
    ? curStatisticList.value[0]?.deptCount
        .filter(item => outHospitalDepts.value.includes(item.deptWsid))
        .map(statistic => statistic.deptName)
    : curStatisticList.value[0]?.deptCount.map(statistic => statistic.deptName)

  chartOption.value = {
    renderer: "svg",
    // width: chartWidth.value - 100,
    title: { text: "" },
    tooltip: {},
    legend: {
      data: [curStatisticList.value[0]?.name, curStatisticList.value[1]?.name]
    },
    xAxis: {
      axisLabel: {
        //x轴文字的配置
        // width: 60,
        width: Math.floor(chartWidth.value / curStatisticList?.value[0]?.deptCount?.length),
        overflow: "breakAll",
        show: true,
        interval: 0, //使x轴文字显示全
        rotate: 45
      },
      data: xAxisData
      // splitLine: {
      //   show: true
      // }
    },
    yAxis: {},
    grid: {
      left: 50,
      right: 50
    },

    series: [
      {
        name: curStatisticList.value[0]?.name,
        type: "bar",
        data: curStatisticList.value[0]?.deptCount.map(statistic => statistic.count),
        itemStyle: {
          color: "rgb(56,159,255)"
        }
      },
      {
        name: curStatisticList.value[1]?.name,
        type: "bar",
        data: curStatisticList.value[1]?.deptCount.map(statistic => statistic.count),
        itemStyle: {
          color: "rgb(78,203,115)"
        }
      }
    ],
    // 缩放设置
    dataZoom: [
      {
        type: "slider",
        show: true,
        // xAxisIndex: [0 ],
        startValue: 0,
        endValue: 14,
        zoomLock: true,
        brushSelect: false
      }
    ]
  }
}

// 自定义科室更新
watch(
  () => outHospitalDepts.value,
  val => {
    setChartOption()
  },
  {
    immediate: true,
    deep: true
  }
)

// 科室排名更新
watch(
  () => customDeptData.deptNum,
  val => {
    if (val) getAllStatistics()
  },
  {
    immediate: true,
    deep: true
  }
)

// 切换查询条件
watch(
  () => customDeptData.type,
  val => {
    if (val !== "CUSTOM") {
      outHospitalDepts.value = []
    }
    customDeptData.deptNum = val === "SOME" ? 15 : 0
    if (val !== "SOME") getAllStatistics()
  }
)

/*========================= 科室统计相关 ======================= */

// 科室统计数据
interface DepartmentStatistic {
  deptWsid: string
  deptName: string // 科室名称
  opers: {
    type: number // 操作类型值
    name: string // 操作类型名称
    count: number
  }[]
}

const departmentStatisticsLoading = ref(true)

const departmentStatistics = ref<DepartmentStatistic[]>([])
// 科室统计时间选项
const selectedDeptDateOption = ref(dateOptions[0].value)

// 获取科室统计数据
const getDepartmentStatistics = () => {
  departmentStatisticsLoading.value = true
  const range = getRangeDateByType(selectedDeptDateOption.value as StatisticTypeEnum)
  getDepartmentStatisticsApi(range[0], range[1])
    .then(res => {
      departmentStatisticsLoading.value = false
      departmentStatistics.value = formatDepartmentStatisticalData(res.data.data)
    })
    .catch(err => {
      departmentStatisticsLoading.value = false
      toastError(err, "获取科室统计数据失败，请重试")
      departmentStatistics.value = []
    })
}

// 选择日期选项
const handleSelectDeptDateOption = dateOptionValue => {
  selectedDeptDateOption.value = dateOptionValue
  getDepartmentStatistics()
}

/*========================= 消息通知相关 ======================= */

const selectedMessage = ref(MessageConfigEnum.WAIT_DISPOSE as string)
const messageData = reactive({
  loading: false,
  rows: [] as Array<Record<string, any>>
})
const getMessageList = () => {
  if (messageData.loading) return
  messageData.loading = true
  if (selectedMessage.value === MessageConfigEnum.ANNOUNCEMENT) {
    getAnnouncementsApi({
      limit: 5,
      offset: 0,
      filters: `isRead=NOT_READ`,
      sorts: "-createDateTime"
    })
      .then(res => {
        const resData = res.data?.data
        const rows: Array<Record<string, any>> = []
        resData.rows.forEach(item => {
          rows.push({
            ...item,
            content: item.contentText,
            dialogType: "HTML",
            createDateTime: formatDatetime(item.createDateTime)
          })
        })
        messageData.rows = rows
        messageData.loading = false
      })
      .catch(err => {
        messageData.loading = false
        toastError(err, "获取列表失败！")
      })
  } else {
    getMessageOrDisposeApi({
      limit: 5,
      offset: 0,
      filters: `isRead=NOT_READ`,
      typeName: selectedMessage.value,
      sorts: "-createDateTime"
    })
      .then(res => {
        const resData = res.data?.data
        const rows: Array<Record<string, any>> = []
        resData.rows.forEach(item => {
          rows.push({
            ...item,
            dialogType: "TEXT",
            createDateTime: formatDatetime(item.createDateTime)
          })
        })
        messageData.rows = rows
        messageData.loading = false
      })
      .catch(err => {
        messageData.loading = false
        toastError(err, "获取列表失败！")
      })
  }
}
onMounted(() => {
  getMessageList()
})

const handleSelectMessageOption = async val => {
  selectedMessage.value = val
  getMessageList()
}

// 单击已读
function readOne(item) {
  if (item.isRead) {
    //单击已读消息
    if (selectedMessage.value === MessageConfigEnum.WAIT_DISPOSE) router.push(item.redirectPath)
    else dialogData.visible = false
  } else {
    //单击未读消息
    if (selectedMessage.value === MessageConfigEnum.ANNOUNCEMENT) {
      // 单击未读公告
      readAnnouncementApi(item.id)
        .then(() => {
          systemStore.$patch({ refreshMessage: true })
          getMessageList()
        })
        .catch(err => toastError(err, "查看失败！"))
        .finally(() => {
          dialogData.visible = false
        })
    } else {
      // 单击未读通知/待处理
      readMessageOrDisposeApi(item.id)
        .then(() => {
          systemStore.$patch({ refreshMessage: true })
          if (selectedMessage.value === MessageConfigEnum.WAIT_DISPOSE) router.push(item.redirectPath)
          else getMessageList()
        })
        .catch(err => toastError(err, "查看失败！"))
        .finally(() => {
          dialogData.visible = false
        })
    }
  }
}

const dialogData = reactive({
  visible: false,
  clickItem: null as MessageItemType | null //当前点击的消息
})

// 打开消息弹窗
function openDialog(item) {
  if (selectedMessage.value === MessageConfigEnum.WAIT_DISPOSE) return readOne(item)
  dialogData.visible = true
  dialogData.clickItem = item
}
</script>

<style lang="less" scoped>
// 顶部待办列表
.todo-list {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  height: auto;
  min-height: 60px;
  :deep(.el-skeleton) {
    min-width: 980px;
    .el-skeleton__p.is-first {
      width: 100%;
    }
  }

  &__item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 20%;
    min-width: 110px;
    max-width: 120px;
    padding: 15px 10px;
    font-size: 16px;
    background: white;
    border-radius: 4px;
    // box-shadow: 0px 0px 6px rgb(10 38 102 / 10%);
    cursor: pointer;

    &-icon {
      width: 60px;
      height: 60px;
    }

    &-name {
      // padding-left: 15px;
      font-size: 14px;
    }
  }
}

// 所有统计数据
.all-statistics {
  padding: 0 0 0 0;
  width: 66.6%;
  min-width: 800px;
  max-height: 100%;
}

.all-statistics-header {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 100%;
  flex: 1;
  margin-left: 100px;
  // width: 60%;
}

.statistic-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: calc(100% - 50px);
  :deep(.el-empty) {
    height: 60px;
  }
}

.statistic-type-radio-group {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  height: 32px;
  border-radius: 4px;
  width: 100%;
}

.charts-container {
  width: 100%;
  height: 95%;
  // display: flex;
  // justify-content: center;
  // overflow: auto;
}

.statistic-type-radio {
  font-size: 12px;
  cursor: pointer;
  background: #c0c1c4;
  color: #fff;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 16px;
  border-radius: 4px;
  justify-content: center;
  margin-right: 10px;

  &:hover {
    border-color: #babdc4;
    background: #548aff;
  }

  &--active {
    color: white !important;
    background: var(--el-color-primary) !important;
    border: none !important;
  }
}

.side-container {
  height: calc(100% - 478px);
  min-height: 50%;
  overflow: hidden;
  justify-content: space-between;
}

// 科室统计数据
.department-statistics {
  width: 50%;
  min-width: 626px;
  // height: 100%;
  // max-width: 626px;
}

.department-statistics-header {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

@media screen and (max-width: 1920px) {
  .department-statistics-wrapper {
    // max-height: 70vh;
    // overflow-y: scroll;
  }
}

@media screen and (min-width: 1921px) and (max-width: 2560px) {
  .department-statistics-wrapper {
    // max-height: 72vh;
    // overflow-y: scroll;
  }
}

.departments-statistic-card {
  // border-left: 2px solid;
  // padding: 0 10px 20px 20px;
  // margin-bottom: 20px;
  // border-left: 6px solid #2c68ff7f;
  border-radius: 4px;
  // box-shadow: 0px 0px 6px rgb(10 38 102 / 10%);
  // margin-right: 10px;
  padding: 0 16px;
  border-bottom: 1px solid rgba(0, 35, 114, 0.1);
}

.department-statistic-title {
  margin: 10px 0;
  font-weight: bold;
  color: #030814d9;
  font-size: 14px;
}

.department-statistic-content {
  // display: grid;
  // grid-template-columns: repeat(2, auto);
  // row-gap: 6px;
  display: flex;
  justify-content: space-between;
  color: #030814a7;
  font-size: 14px;
  margin: 10px 0;
}

.message {
  width: 49%;
  // min-width: 556px;

  .message-list {
    overflow-y: auto;
    font-size: 14px;
    height: calc(100% - 52px);

    .message-item {
      padding: 0px 16px;
      border-bottom: 1px solid rgba(0, 35, 114, 0.1);

      .row-box {
        display: flex;
        justify-content: space-between;
        margin: 10px 0px;

        .message-type {
          color: #030814d8;
          font-weight: bold;
        }

        .message-content {
          color: #030814a5;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .message-action {
          color: var(--el-color-primary);
          white-space: nowrap;
          margin-left: 10px;
          cursor: pointer;
        }
      }
    }
  }
}

// 公共
.date-option {
  // width: 50px;
  height: 32px;
  font-size: 14px;
  text-align: center;
}

:deep(.el-empty) {
  width: 100%;
  height: 100%;
}

:deep(.el-header) {
  --el-header-padding: 0px;
}

:deep(.el-main) {
  --el-main-padding: 20px 0px 0px 0px;
}

@media screen and (max-width: 934px) {
  .container-right {
    width: 100% !important;
    max-width: 800px !important;
    margin-right: 20px;
  }
}

.dept {
  :deep(.el-form-item__content) {
    width: 100%;
  }

  // :deep(.el-select) {
  //   width: 50%;
  // }
}

.dept-select {
  // width: 60%;
  display: flex;
  align-items: center;

  .select-form {
    // min-width: 260px;
  }

  :deep(.el-form-item) {
    margin-bottom: 0;
  }
}

.department-select {
  margin-right: 20px;
}

.date-select {
  margin-right: 40px;
}

@media screen and (max-width: 1280px) {
  .date-select {
    min-width: 50%;
  }
}
// @media screen and (min-width: 1920px) {
//   .side-container {
//     min-height: 23%;
//   }
// }

// @media screen and (max-width: 1536px) {
//   .side-container {
//     min-height: 23%;
//   }
// }
</style>
