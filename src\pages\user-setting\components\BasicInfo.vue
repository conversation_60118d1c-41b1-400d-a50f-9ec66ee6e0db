<template>
  <div class="basic-box-root">
    <div class="basic-box-title">基本信息</div>
    <el-descriptions class="basic-box-content" title="" :column="1">
      <el-descriptions-item label="头像：">
        <div class="user-avatar-box">
          <BlobImage class="avatar-img" :file-wsid="userStore.userHospitalInfo.avatar" :default="defaultAvatar" />
          <el-button color="#409fee" plain class="change-btn" @click="changeAvatars">更换</el-button>
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="姓名：">{{ userStore.userHospitalInfo.realName }}</el-descriptions-item>
      <el-descriptions-item label="手机：">{{ userStore.userHospitalInfo.phone }}</el-descriptions-item>
      <el-descriptions-item label="工号：">{{ userStore.userHospitalInfo.jobId }}</el-descriptions-item>
      <el-descriptions-item label="职称：">{{ userStore.userHospitalInfo.jobTitleName }}</el-descriptions-item>
      <el-descriptions-item label="科室：">{{ userStore.userHospitalInfo.deptName }}</el-descriptions-item>
    </el-descriptions>

    <CropperDialog ref="cropperDialogRef" title="上传头像" :img-src="avatarBase" @submit-cropper="submitCropper" />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue"
import { BlobImage } from "@/base-components"
import { CropperDialog } from "@/page-components"
import defaultAvatar from "@/assets/png/avatar2.png"
import { useUserStore } from "@/stores"
import { Message, selectFile, toastError, blobToBase64 } from "@/utils"

import { updateUserAvatar } from "../interface"

// 用户信息store
const userStore = useUserStore()

/* ====================== 更换头像 ======================== */
const cropperDialogRef = ref()
const avatarBase = ref("")

// 打开裁剪弹窗
const changeAvatars = async () => {
  if (!cropperDialogRef.value) return

  selectFile(".jpg,.jpeg,.png", 1024 * 1024 * 30).then(async file => {
    avatarBase.value = (await blobToBase64(file as Blob)) as string
    cropperDialogRef.value.openDialog()
  })
}

// 提交裁剪后的头像
function submitCropper(file) {
  updateUserAvatar({ file: file })
    .then(async res => {
      const fileWsid = res.data.data
      Message.success("更换头像成功")
      userStore.userHospitalInfo.avatar = fileWsid
    })
    .catch(err => toastError(err, "更换头像失败"))
    .finally(() => {
      if (cropperDialogRef.value) cropperDialogRef.value.closeDialog()
    })
}
</script>

<style lang="less" scoped>
.basic-box-root {
  width: 660px;
  background-color: #fff;
  box-shadow: 0px 2px 10px rgba(0, 35, 114, 0.1);

  .basic-box-title {
    font-size: 16px;
    font-weight: 650;
    padding: 15px;
    border-bottom: 1px solid #ebedf0;
  }

  .basic-box-content {
    padding: 15px;

    :deep .el-descriptions__cell {
      display: flex;
      align-items: center;
      padding-bottom: 15px;
    }

    :deep .el-descriptions__label {
      width: 60px;
    }

    :deep .el-descriptions__content {
      width: calc(100% - 60px);
    }

    .user-avatar-box {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .avatar-img {
        width: 50px;
        height: 50px;
        border-radius: 50%;
      }
    }
  }
}
</style>
