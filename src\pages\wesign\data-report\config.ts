import type { TableColumnItem, BaseOptionItem } from "@/types"
import type { FormRules } from "element-plus"
import { ipValidator, directoryValidator } from "@/utils"

export const errHintColumns = [
  { prop: "fieldName", label: "错误项", minWidth: 120 },
  { prop: "promptInfo", label: "错误信息", minWidth: 120 },
  { prop: "operation", label: "操作", minWidth: 80 }
]

export const dataDictionaryColumns = [
  { prop: "value", label: "字典明细" },
  { prop: "key", label: "字典明细值" }
]

export enum ColumnValueType {
  STRING = "STRING",
  NUMBER = "NUMBER",
  DATE = "DATE",
  DATE_TIME = "DATE_TIME",
  SELECT = "SELECT",
  ICD_CODE = "ICD_CODE",
  ICD_NAME = "ICD_NAME",
  ICD_CM_CODE = "ICD_CM_CODE",
  ICD_CM_NAME = "ICD_CM_NAME",
  ICD_O_CODE = "ICD_O_CODE",
  ICD_O_NAME = "ICD_O_NAME"
}

export enum ValidateResultEnum {
  Success = "Success",
  Fail = "Fail"
}

export const outHospitalWayOptions: Array<BaseOptionItem> = [
  { label: "非医嘱离院", value: 4 },
  { label: "死亡", value: 5 }
]

/* ================= 住院上报 =================== */

export const hospitalizationTabsConfig = [
  { label: "住院上报", path: "/data-report/hospitalization/list" },
  { label: "导出记录", path: "/data-report/hospitalization/export-record" }
]

export const hospitalizationDataReportTableColumns: Array<TableColumnItem> = [
  { type: "selection", width: 50, fixed: "left" },
  { prop: "mrNo", label: "病案号", minWidth: 180 },
  { prop: "patientName", label: "姓名", minWidth: 100 },
  { prop: "patientSex", label: "性别", minWidth: 100 },
  { prop: "visitId", label: "住院次数", minWidth: 90 },
  { prop: "inHospitalDatetime", label: "入院时间", minWidth: 180, sortable: true },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 100 },
  { prop: "outHospitalDatetime", label: "出院时间", minWidth: 180, sortable: true },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 100 },
  { prop: "reviewStatus", label: "审核状态", minWidth: 120 },
  { prop: "reviewInvalidCount", label: "审核问题数", minWidth: 100 },
  { prop: "operation", label: "操作", width: 180, fixed: "right", must: true }
]

// 门急诊导出记录列表
export const hospitalizationExportRecordTableColumns: Array<TableColumnItem> = [
  { type: "selection", width: 50 },
  { prop: "createdDatetime", label: "任务日期", minWidth: 300 },
  { prop: "fileNum", label: "文件数", minWidth: 100 },
  { prop: "recordNum", label: "总记录数", minWidth: 100 },
  { prop: "startDatetime", label: "任务开始时间", minWidth: 180, sortable: true },
  { prop: "endDatetime", label: "任务结束时间", minWidth: 180, sortable: true },
  { prop: "status", label: "任务状态", minWidth: 100 },
  { prop: "operation", label: "操作", width: 160, fixed: "right", must: true }
]

/* ================= 上报文书 =================== */

export const submitDocumentsTableColumns: Array<TableColumnItem> = [
  { type: "selection", width: 50, fixed: "left" },
  { prop: "mrNo", label: "病案号", minWidth: 100 },
  { prop: "patientName", label: "姓名", minWidth: 100 },
  { prop: "outHospitalWay", label: "离院方式", minWidth: 100 },
  { prop: "patientSex", label: "性别", minWidth: 100 },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 120 },
  { prop: "inHospitalDatetime", label: "入院时间", minWidth: 200 },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 120 },
  { prop: "outHospitalDatetime", label: "出院时间", minWidth: 200 },
  { prop: "exportStatus", label: "是否已生成PDF", minWidth: 100 },
  { prop: "operation", label: "操作", width: 180, fixed: "right", must: true }
]

/* ================= 门急诊上报 =================== */

export const outpatientTabsConfig = [
  { label: "门急诊上报", path: "/data-report/outpatient-and-emergency/list" },
  { label: "导出记录", path: "/data-report/outpatient-and-emergency/export-record" }
]

// 门急诊上报列表
export const outpatientAndEmergencyTableColumns: Array<TableColumnItem> = [
  { type: "selection", width: 50, fixed: "left" },
  { prop: "patientName", label: "姓名", minWidth: 100 },
  { prop: "patientSex", label: "性别", minWidth: 100 },
  { prop: "medicalNo", label: "就诊卡号或病案号", minWidth: 180 },
  { prop: "deptName", label: "就诊科室", minWidth: 160 },
  { prop: "mainDiagnostic", label: "主要诊断", minWidth: 200 },
  { prop: "consultationTime", label: "就诊时间", minWidth: 180 },
  { prop: "doctorName", label: "就诊医师", minWidth: 120 },
  { prop: "reviewStatus", label: "审核状态", minWidth: 100 },
  { prop: "reviewInvalidCount", label: "审核问题数", minWidth: 100 },
  { prop: "syncDatetime", label: "同步时间", minWidth: 180 },
  { prop: "operation", label: "操作", width: 180, fixed: "right", must: true }
]

// 门急诊导出记录列表
export const outpatientExportRecordTableColumns: Array<TableColumnItem> = [
  // { type: "expand", width: 50 },
  { type: "selection", width: 50 },
  { prop: "createdDatetime", label: "任务日期", minWidth: 300 },
  { prop: "fileNum", label: "文件数", minWidth: 100 },
  { prop: "recordNum", label: "总记录数", minWidth: 100 },
  { prop: "startDatetime", label: "任务开始时间", minWidth: 180, sortable: true },
  { prop: "endDatetime", label: "任务结束时间", minWidth: 180, sortable: true },
  { prop: "status", label: "任务状态", minWidth: 100 },
  { prop: "operation", label: "操作", width: 160, fixed: "right", must: true }
]

/* ================= 部分公共 =================== */

// 审核状态
export const reviewStatusOptions: Array<BaseOptionItem> = [
  { label: "待审核", value: "REVIEW_WAIT" },
  { label: "审核通过", value: "REVIEW_PASS" },
  { label: "审核未通过", value: "REVIEW_FAIL" }
]

export const getReviewsStatusTagType = (status: string) => {
  switch (status) {
    case "REVIEW_WAIT":
      return "warning"
    case "REVIEW_PASS":
      return "success"
    case "REVIEW_FAIL":
      return "danger"
    default:
      return "info"
  }
}

// 门急诊导出状态
export const exportStatusOptions = [
  { label: "进行中", value: "ONGOING", tagType: "warning" },
  { label: "失败", value: "FAIL", tagType: "danger" },
  { label: "成功", value: "SUCCESS", tagType: "success" }
]
