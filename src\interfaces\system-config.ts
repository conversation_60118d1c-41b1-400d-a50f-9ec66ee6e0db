import axios from "@/interfaces/axios-instance"

/**
 * @method GET
 * @desc   获取当前账户的指定table的列配置
 */
export function getTableConfigApi(obj) {
  const { mark = "" } = obj
  return axios({
    method: "get",
    url: `/api/system/column-cofig/${mark}`
  })
}

/**
 * @method POST
 * @desc   设置当前账户的指定table的列配置
 */
export function setTableConfigApi(obj) {
  const { mark = "", columnConfig } = obj
  return axios({
    method: "post",
    url: `/api/system/column-cofig/${mark}`,
    data: {
      columnConfig
    }
  })
}

/**
 * @method GET
 * @desc   查询首页标准字段列表
 */
export function getHomeMetaListApi(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: "/api/catalog/standard",
    params
  })
}

/**
 * @method GET
 * @desc   获取所有文档类型列表
 */
export function getAllDocTypes() {
  return axios
    .request<IPaginationResponseData>({
      method: "get",
      url: "/api/document/mr-classs",
      params: {
        limit: 1000,
        offset: 0
      }
    })
    .then(res => res.data?.data?.rows ?? [])
}

/**
 * @method GET
 * @desc   查询指定分类下的数据字典明细
 */
export function queryDictionaryDetailByGroup(params: { groupKey: string }) {
  return axios
    .request<IResponseData>({
      method: "get",
      url: `/api/system/dictionary/by-group-key?group-key=${params.groupKey}`
    })
    .then(res => res.data?.data ?? [])
}

/**
 * @method GET
 * @desc   查询系统配置
 */
export function querySystemConfig(obj) {
  const { type } = obj
  return axios({
    method: "get",
    url: `/api/system/system-config?type=${type}`
  })
}

/**
 * @method GET
 * @desc   查询favicon图标信息（无session）
 */
export function querySystemIcon(mark) {
  return axios({
    method: "get",
    url: `/api/system/system-config/icon?mark=${mark}`,
    responseType: "blob",
    headers: {
      "Cache-Control": "no-cache"
    }
  }).then(res => {
    return res.data.size === 0 ? "" : URL.createObjectURL(res.data)
  })
}

/**
 * @method GET
 * @desc   查询favicon图标信息（无session）
 */
export function querySystemCopyright() {
  return axios({
    method: "get",
    url: `/api/system/system-config/copyright`
  })
}

/**
 * @method POST
 * @desc   更新系统菜单及操作资源配置
 */
export function updateSystemResourceConfig(jsonData: string) {
  return axios({
    method: "post",
    url: "/api/system/menu-button",
    data: { jsonData }
  })
}

/**
 * @method GET
 * @desc   获取系统菜单及操作资源配置
 */
export function getSystemResourceConfig() {
  return axios({
    method: "get",
    url: "/api/system/menu-button"
  })
}

/**
 * @method POST
 * @desc   系统参数-上传系统图标
 */
export function uploadSystemFile(obj) {
  const { file } = obj
  const formData = new FormData()
  formData.append("file", file)
  return axios({
    method: "post",
    url: `/api/system/upload-file`,
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  })
}

/**
 * @method GET
 * @desc   用户管理-当前账户的权限树(只返回已启用的)
 */
export function getUserPermissionsApi() {
  return axios({
    method: "get",
    url: "/api/system/users/query-current-user-permissions"
  })
}

/**
 * @method GET
 * @desc   查询当前登录用户详情(及医护信息)
 */
export function getUserHospitalInfo() {
  return axios({
    method: "get",
    url: "/api/system/users/hosp/get",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    }
  }).then(res => {
    return res.data.data
  })
}

/**
 * @method GET
 * @desc   用户信息-打印密码开启状态
 */
export function getPrintPasswordStatus() {
  return axios({
    method: "get",
    url: "/api/system/user-config/print"
  })
}

/**
 * @method POST
 * @desc   用户信息-校验打印密码
 */
export function checkPrintPassword(password: string) {
  return axios({
    method: "post",
    url: "/api/system/user-config/print/password/check",
    data: { password: password }
  })
}

/**
 * @method GET
 * @desc   编目字段分类
 */
export function getCatalogStandardList(params?: any) {
  return axios({
    method: "get",
    url: "/api/catalog/standard/file-list",
    params
  })
}

interface ICatalogStandardData {
  fileName: string
  fileType: string
  scenarioType: string
  id?: string
  catalogType?: "CATALOG"
  parentId?: string
}

/**
 * @method POST
 * @desc   新增分类
 */
export function addCatalogStandardApi(data: ICatalogStandardData) {
  return axios({
    method: "post",
    url: "/api/catalog/standard/file",
    data: data
  })
}

/**
 * @method PUT
 * @desc   修改编码列表（分类）
 */
export function editCatalogStandardApi(data: ICatalogStandardData) {
  return axios({
    method: "put",
    url: "/api/catalog/standard/file",
    data: data
  })
}

/**
 * @method DELETE
 * @desc   删除编码目录（分类）
 */
export function deleteCatalogStandardApi(data: { id: string; fileType: string }) {
  return axios({
    method: "delete",
    url: `/api/catalog/standard/file/${data.id}?fileType=${data.fileType}`
  })
}

interface IStandardData {
  scenarioType: string
  name: string
  title: string
  valueType: string
  length: number
  remark: string
  parentId: string
  catalogType?: string
  refFirstPageName: string
  valueRangeKey: string
  collectValueSource?: string
  collectSourceType: "METADATA" | "INTERFACE" | string
  widgetType?: string
}

/**
 * @method POST
 * @desc   系统上报字段-新增
 */
export function addStandardApi(data: IStandardData) {
  return axios({
    method: "post",
    url: "/api/catalog/scenario/standard-info",
    data: data
  })
}

/**
 * @method PUT
 * @desc   修改编码列表（分类）
 */
export function editStandardApi(data: IStandardData) {
  return axios({
    method: "put",
    url: "/api/catalog/scenario/standard-info",
    data: data
  })
}

interface IEditFormConfigsData {
  catalogFormConfigWsid: string
  name: string
  formContent: string
}

// 编辑表单配置
export function editFormConfigsApi(data: IEditFormConfigsData) {
  return axios({
    method: "put",
    url: "/api/catalog/form-configs",
    data
  })
}

// 查询表单配置详情
export function getFormConfigsDetailApi(wsid: string) {
  return axios({
    method: "get",
    url: `/api/catalog/form-configs/${wsid}`
  })
}

/**
 * @method GET
 * @desc   获取所有值域列表
 */
export function searchStandardRangeClassifyApi() {
  return axios({
    method: "get",
    url: "/api/catalog/standard/options/classify"
  })
}

/**
 * @method PUT
 * @desc   更新系统配置
 */
export function updateSystemConfig(obj) {
  return axios({
    method: "put",
    url: `/api/system/system-config`,
    data: obj
  })
}

/**
 * @method GET
 * @desc   标准字段-分页查询
 */
export function getStandardsFieldApi(params) {
  const { offset, limit, filters, queryParams } = params
  console.log(`output->queryParams`, queryParams)
  // debugger
  return axios({
    method: "get",
    url: `/api/catalog/scenario/standards/${queryParams.scenarioType}`,
    params: {
      offset: offset,
      limit: limit,
      filters: filters,
      ...queryParams
    }
  })
}

interface IMenuData {
  name: string
  code: string
  path: string
  parentCode?: string
  iconCode: string
  level: number
  status: number
  showIndex: number
  ext?: any
  filePath?: string
}

/**
 * @method POST
 * @desc   新增单个菜单
 */
export function addMenuApi(data: IMenuData) {
  return axios({
    method: "post",
    url: "/api/system/menu-add",
    data
  })
}

/**
 * @method POST
 * @desc   编辑单个菜单
 */
export function editMenuApi(data: IMenuData) {
  return axios({
    method: "post",
    url: "/api/system/menu-update",
    data
  })
}

interface IUpdateMenuStatusData {
  codes: Array<string>
  status: 0 | 1
}

/**
 * @method POST
 * @desc   修改菜单状态
 */
export function updateMenuStatusApi(data: IUpdateMenuStatusData) {
  return axios({
    method: "put",
    url: "/api/system/menu-update",
    data
  })
}
