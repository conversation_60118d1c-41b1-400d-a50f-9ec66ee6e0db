<template>
  <PageContainer separate>
    <!-- 头部搜索 -->
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList"></TabsRouter>
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
        />

        <VisitIdFormItem
          v-model:input-value="searchFormState.visitIdCount"
          v-model:select-value="searchFormState.visitIdSymbol"
          label="住院次数"
        />

        <DepartmentFormItem v-model:model-value="searchFormState.outHospitalDeptWsid" label="出院科室" />

        <CountRangeFormItem
          v-model:from="searchFormState.inHospitalDaysFrom"
          v-model:to="searchFormState.inHospitalDaysTo"
          :min="0"
          label="住院天数"
          unit="天"
        />

        <DaterangeFormItem v-model:model-value="searchFormState.outHospitalDatetime" label="出院时间" />

        <!-- <PatientLabelFormItem v-model="searchFormState.patientLabel" /> -->
      </SearchContainer>
    </template>

    <!-- 待质控表格 -->
    <template #table>
      <CommonTable
        table-id="finalQcTable"
        :table-columns="finalWaitingQualityControlColumns"
        :request-api="getDistributeQcTaskListApi"
        :request-params="{ qcType: QualityControlTypeEnum.FINAL_QC, ...searchParams }"
        :data-callback="dataCallback"
      >
        <!-- 患者姓名 -->
        <template #patientName="{ row }">
          <div style="display: flex; align-items: center; flex-wrap: wrap">
            <span style="margin: 0 16px">{{ row.patientName }}</span>
            <PatientLabelTag :row="row" />
          </div>
        </template>
        <template #patientPrintCount="{ row }">
          {{ row.patientPrintCount > 0 ? "已打印" : "未打印" }}
        </template>
        <template #operation="{ row }">
          <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.QC)" @click="qualityControlItem(row)">
            质控
          </TableButton>
          <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.View)" @click="viewItem(row)">
            查看
          </TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>

  <!-- 封存病案密钥 -->
  <SealDialog ref="sealDialogRef" :selected-row="medicalRecord" :confirm-callback="confirmOperation" />
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue"
import { useRouter } from "vue-router"
import {
  SearchContainer,
  CommonTable,
  TableButton,
  PatientFilterFormItem,
  DepartmentFormItem,
  DaterangeFormItem,
  VisitIdFormItem,
  PageContainer,
  CountRangeFormItem,
  PatientLabelTag,
  PatientLabelFormItem
} from "@/base-components"
import { TabsRouter, SealDialog } from "@/page-components"
import { MenuOperationEnum, QualityControlTypeEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { getDistributeQcTaskListApi } from "@/interfaces"
import { useUserStore } from "@/stores"
import { formatDatetime, encryptStr, toastError } from "@/utils"
import { getThirdConfigApi } from "../interface"
import { finalWaitingQualityControlColumns, tabsRouterList } from "./config"

const menuId = "/final-quality-control/manage"
const { hasOperationPermission } = useUserStore()

const router = useRouter()
const userStore = useUserStore()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  patientFilterValue: "",
  patientFilterProp: "patientName",
  outHospitalDeptWsid: "",
  outHospitalDatetime: "",
  visitIdSymbol: "",
  visitIdCount: "",
  inHospitalDaysFrom: "",
  inHospitalDaysTo: "",
  death: "",
  patientLabel: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState, `qcStatus=WAIT`)

/* ======================== 表格相关方法 ======================== */
const medicalRecord = ref<Record<string, any> | null>(null) // 用户点击的病案
const sealDialogRef = ref() // SealDialog组件ref

function dataCallback(data: Array<Record<string, any>>) {
  data.forEach(item => {
    item.outHospitalDatetime = formatDatetime(item.outHospitalDatetime)
    item.inHospitalDatetime = formatDatetime(item.inHospitalDatetime)
    item.outClinicalDeptDatetime = formatDatetime(item.outClinicalDeptDatetime)
  })
  return data
}

// 质控
const qualityControlItem = row => {
  medicalRecord.value = row
  if (thirdQcConfig.needThirdQc) {
    redirect(row)
  } else {
    if (sealDialogRef.value) sealDialogRef.value.checkSealed()
  }
}

// 检查封存之后的回调
const confirmOperation = () => {
  const query = {
    inpNo: medicalRecord.value?.inpNo,
    wsid: medicalRecord.value?.wsid,
    isControl: "true",
    urlType: QualityControlTypeEnum.FINAL_QC,
    returnUrl: "/final-quality-control/manage/waiting-control"
  }
  if (sealDialogRef.value.secretKey) {
    query["secretKey"] = encryptStr(sealDialogRef.value.secretKey)
  }
  router.push({
    path: "/final-quality-control/manage/view",
    query: query
  })
}

/* ======================== 第三方质控系统 ======================== */

interface ThirdQc {
  needThirdQc: boolean
  thirdQcUrl: string
  thirdQcName: string
}

const thirdQcConfig = reactive<ThirdQc>({
  needThirdQc: false,
  thirdQcUrl: "",
  thirdQcName: ""
})

onMounted(async () => {
  try {
    const result = (await getThirdConfigApi()).data.data
    for (const key in thirdQcConfig) {
      thirdQcConfig[key] = result[key]
    }
  } catch (err: any) {
    toastError(err)
  }
})

const redirect = row => {
  // 替换配置的url内的参数
  for (const key in row) {
    if (thirdQcConfig.thirdQcUrl.includes(key)) {
      thirdQcConfig.thirdQcUrl = thirdQcConfig.thirdQcUrl.replace("${" + key + "}", row[key])
    }
  }
  thirdQcConfig.thirdQcUrl = thirdQcConfig.thirdQcUrl.replace("${jobId}", userStore.userHospitalInfo.jobId)
  window.open(`${thirdQcConfig.thirdQcUrl}`, "_blank")
}

const viewItem = row => {
  router.push({
    path: "/medical-record/detail",
    query: {
      inpNo: row.inpNo
    }
  })
}
</script>
