import moment from "moment"
import type { TableColumnItem } from "@/types"

export const workloadColumns: Array<TableColumnItem> = [
  { prop: "dateTime", label: "统计日期", minWidth: 150, sortable: true },
  { prop: "orderNum", label: "新增订单数", minWidth: 120, sortable: true },
  { prop: "duplicateNum", label: "复印总份数", minWidth: 120, sortable: true },
  { prop: "pageCount", label: "总页数", minWidth: 120, sortable: true },
  { prop: "orderAmount", label: "订单总金额", minWidth: 120, sortable: true }
]

// 格式化返回数据中的dateTime
const formatResDataTime = (type: "DAY" | "WEEK" | "MONTH" | "YEAR" | "OVERMONTH", time) => {
  switch (type) {
    case "DAY":
      return moment(time).format("HH:mm")
    case "WEEK":
    case "MONTH":
      return moment(time).format("MM-DD")
    case "OVERMONTH":
      return moment(time).format("YYYY-MM")
    case "YEAR":
      return moment(time).format("YYYY")
  }
}

// 获取实例数据
export const getSeriesData = (type, key: string, timeList: Array<any>, resData) => {
  // 将后台返回的时间转换为和前端计算的x轴一样的格式
  const baseData = resData.map((item: any) => {
    return {
      ...item,
      dateTime: formatResDataTime(type, new Date(item.dateTime).getTime())
    }
  })

  //获取resDate对应的字段数据
  const data = new Array(timeList.length).fill(0)
  timeList.map((time, index) => {
    baseData.map(item => {
      data[index] = time === item.dateTime ? item[key] : data[index]
    })
  })
  return data
}

export const extraInvokingOptions = {
  legend: {
    show: true,
    icon: "rect"
  }
}

export const getOrderSeries = (dateFormatter: string, timeList: string[], data: Array<any>) => [
  {
    name: "邮寄",
    type: "line",
    color: "#3860F4",
    symbol: "circle",
    data: getSeriesData(dateFormatter, "mail", timeList, data)
  },
  {
    name: "自取",
    type: "line",
    color: "#61B32A",
    symbol: "circle",
    data: getSeriesData(dateFormatter, "ownTake", timeList, data)
  },
  {
    name: "电子分发",
    type: "line",
    color: "#F52F3E",
    symbol: "circle",
    data: getSeriesData(dateFormatter, "electronicDistribution", timeList, data)
  }
]

export const getInvoicingSeries = (dateFormatter: string, timeList: string[], data: Array<any>) => [
  {
    name: "开票",
    type: "line",
    color: "#3860F4",
    symbol: "circle",
    data: getSeriesData(dateFormatter, "invoiceNum", timeList, data)
  },
  {
    name: "不开票",
    type: "line",
    color: "#FF9D03",
    symbol: "circle",
    data: getSeriesData(dateFormatter, "noInvoiceNum", timeList, data)
  }
]

export const getPrintTypeSeries = (dateFormatter: string, timeList: string[], data: Array<any>) => [
  {
    name: "数量",
    type: "bar",
    itemStyle: {
      color: "#3860F4"
    },
    barWidth: "30%",
    data: getSeriesData(dateFormatter, "orderNum", timeList, data)
  }
]
