import axios from "@/interfaces/axios-instance"

/**
 * @method GET
 * @desc   查询指定科室质控分类校验
 */
export function getQualityControlledSortCheckApi(obj) {
  const { inpNo, urlType } = obj
  return axios({
    method: "get",
    url: `/api/qc/${urlType}/${inpNo}/mr-checks`
  })
}

/**
 * @method GET
 * @desc   查询指定科室质控缺失未提交报告列表
 */
export function getQualityControlledMissingReportApi(documentBagWsid) {
  return axios({
    method: "get",
    url: `/api/document/deletion/${documentBagWsid}`
  })
}

/**
 * @method GET
 * @desc   查询指定病案的评分
 */
export function getMedicalScoreApi(obj) {
  const { inpNo, urlType } = obj
  return axios({
    method: "get",
    url: `/api/qc/${urlType}/${inpNo}/grade`
  })
}

/**
 * @method POST
 * @desc   科室评分计算
 */
export function calculateDepartmentScoreApi(obj) {
  const { inpNo, data, urlType } = obj
  return axios({
    method: "post",
    url: `/api/qc/${urlType}/${inpNo}/grade-calc`,
    data: data
  })
}

/**
 * @method POST
 * @desc   保存科室评分
 */
export function saveDepartmentScoreApi(obj) {
  const { inpNo, data, urlType } = obj
  return axios({
    method: "post",
    url: `/api/qc/${urlType}/${inpNo}/grade-deducts`,
    data: data
  })
}
/**
 * @method GET
 * @desc   查询指定病案的质控意见列表
 */
export function getQualityOptionRecordApi(obj) {
  const { inpNo, urlType } = obj
  return axios({
    method: "get",
    url: `/api/qc/${urlType}/${inpNo}/comments`
  })
}

/**
 * @method POST
 * @desc   添加指定病案的质控意见
 */
export function addQualityControlSuggestApi(obj) {
  const { inpNo, urlType, subject = "", questionCategory = "", questionType = "", description = "" } = obj
  return axios({
    method: "post",
    url: `/api/qc/${urlType}/${inpNo}/comments`,
    data: {
      subject,
      questionCategory,
      questionType,
      description
    }
  })
}

/**
 * @method POST
 * @desc   编辑指定病案的质控意见
 */
export function editQualityControlSuggestApi(obj) {
  const { inpNo, urlType, id, subject = "", questionCategory = "", questionType = "", description = "" } = obj
  return axios({
    method: "post",
    url: `/api/qc/${urlType}/${inpNo}/comments/${id}`,
    data: {
      subject,
      questionCategory,
      questionType,
      description
    }
  })
}

/**
 * @method DELETE
 * @desc   删除指定病案的质控意见
 */
export function deleteRecordApi(obj) {
  const { inpNo, urlType, id } = obj
  return axios({
    method: "delete",
    url: `/api/qc/${urlType}/${inpNo}/comments/${id}`
  })
}

interface IQCStatusData {
  inpNo: string
  qcType: string
  status: string
  confirmDocumentWsids?: Array<string>
}

/**
 * @method PUT
 * @desc   更新指定病案的质控状态
 */
export function changeQualityControlStatusApi(data: IQCStatusData) {
  return axios({
    method: "put",
    url: `/api/qc/${data.qcType}/${data.inpNo}/update-status`,
    data: {
      status: data.status,
      confirmDocumentWsids: data.confirmDocumentWsids
    }
  })
}

/**
 * @method GET
 * @desc   查询指定病案的质控状态
 */
export function getQualityControlStatusApi(obj) {
  const { inpNo, urlType } = obj
  return axios({
    method: "get",
    url: `/api/qc/${urlType}/qc-record/${inpNo}`
  })
}

/**
 * @method POST
 * @desc   分配质控任务
 */
export function distributeQcTaskApi(data) {
  const { tasks, operators, qcType } = data
  return axios({
    method: "post",
    url: `/api/qc/${qcType}/qc-tasks`,
    data: {
      tasks,
      operators
    }
  })
}

/**
 * @method get
 * @desc   质控进度查询
 */
export function getDistributeQcTaskApi(params) {
  const { urlType, filters, offset, limit, currentDept } = params
  return axios({
    method: "get",
    url: `/api/qc/${urlType}/qc-tasks`,
    params: {
      filters,
      offset,
      limit,
      currentDept
    }
  })
}

/**
 * @method get
 * @desc   查询质控任务（科室质控任务-待质控，交叉质控任务，终末质控）
 */
export function getDistributeQcTaskListApi(params) {
  const { qcType, filters, offset, limit } = params
  return axios({
    method: "get",
    url: `/api/qc/${qcType}/qc-tasks/qc-current-tasks`,
    params: {
      filters,
      offset,
      limit
    }
  })
}

/**
 * @method POST
 * @desc   自动分配质控任务
 */
export function autoDistributeQcTaskApi(params) {
  const { urlType } = params
  return axios({
    method: "post",
    url: `/api/qc/${urlType}/qc-tasks-random`
  })
}

/**
 * @method GET
 * @desc   质控轨迹查询
 */
export function getQualityControlTraceApi(inpNo) {
  return axios({
    method: "get",
    url: `/api/qc/qc-logs`,
    params: { inpNo }
  })
}

/**
 * @method GET
 * @desc   有质控权限用户
 */
export function getQualityControlUserApi(qcType, likeJobIdOrRealName) {
  return axios({
    method: "get",
    url: `/api/qc/${qcType}/qc-permissions-user`,
    params: { likeJobIdOrRealName }
  })
}

// 检查签名完整性
export const getQCInfoApi = (inpNo: string) => {
  return axios({
    method: "get",
    url: `/api/document/bags/${inpNo}/submit/qc`
  })
    .then(res => {
      return res.data.data
    })
    .catch(error => {
      throw error
    })
}
