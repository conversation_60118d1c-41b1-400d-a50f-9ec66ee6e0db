import type { BaseOptionItem, RelationshipType } from "@/types"
import type { FormRules } from "element-plus"
import { RelationshipTypeEnum } from "@/types"
import { phoneNumberValidator, IdNumberValidator } from "@/utils"

export const typeOptions: Array<BaseOptionItem> = [
  { value: "ID_CARD", label: "身份证" },
  { value: "MEDICAL_CARD", label: "就诊卡" },
  { value: "HEALTH_CARD", label: "居民健康卡" },
  { value: "PHONE", label: "手机号" },
  { value: "FINGERPRINT", label: "指纹" }
]

export const relationshipStrOptions: Array<BaseOptionItem> = [
  { value: RelationshipTypeEnum.OWN, label: "本人" },
  { value: RelationshipTypeEnum.AGENT, label: "代理人" },
  { value: RelationshipTypeEnum.GUARDIAN, label: "监护人" },
  { value: RelationshipTypeEnum.HEIR, label: "继承人" },
  { value: RelationshipTypeEnum.HEIR_AGENT, label: "代理继承人" }
]

export const printFormRules: FormRules = {
  patientName: [{ required: true, message: "请输入患者姓名", trigger: "blur" }],
  papersType: [{ required: true, message: "请选择证件类型", trigger: "blur" }],
  relationship: [{ required: true, message: "请选择", trigger: "blur" }],
  agentName: [{ required: true, message: "请输入申请人姓名", trigger: "blur" }],
  agentIdCard: [
    { required: true, message: "请刷卡或手动输入", trigger: "blur" },
    { validator: IdNumberValidator, trigger: "blur" }
  ],
  agentPhone: [
    { required: false, message: "请输入申请人电话", trigger: "blur" },
    { validator: phoneNumberValidator, trigger: "blur" }
  ],
  printTypeCode: [{ required: true, message: "请选择打印类型", trigger: "blur" }],
  printPurposeCode: [{ required: true, message: "请选择打印用途", trigger: "blur" }],
  paymentMethod: [{ required: true, message: "请选择支付方式", trigger: "blur" }]
}

export const mailFormRules: FormRules = {
  mailName: [{ required: false, message: "请输入收件人姓名", trigger: "blur" }],
  mailPhone: [
    { required: false, message: "请输入收件人电话", trigger: "blur" },
    { validator: phoneNumberValidator, trigger: "blur" }
  ],
  mailAddress: [{ required: false, message: "请输入收件地址", trigger: "blur" }]
}

export enum CertificateTypeEnum {
  Portrait = "portrait", //身份证人像图
  NationalEmblem = "nationalEmblem", //身份证国徽图
  Authorization = "authorization", //授权委托书，
  BirthCertificate = "birthCertificate", //出生证明
  ResidenceBooklet = "residenceBooklet", //户口本
  DeathCertificate = "deathCertificate", //死亡证明
  RelationshipCertificate = "relationshipCertificate" // 关系证明
}

// 患者本人
export const ownInfo = [
  {
    label: "患者证件",
    relationship: RelationshipTypeEnum.OWN,
    children: [
      { label: "身份证人像图", type: CertificateTypeEnum.Portrait, prop: "patientIdCardFront" },
      { label: "身份证国徽图", type: CertificateTypeEnum.NationalEmblem, prop: "patientIdCardReverse" }
    ]
  }
]

// 代理人
export const agentInfo = [
  ...ownInfo,
  {
    label: "申请人证件",
    relationship: RelationshipTypeEnum.AGENT,
    children: [
      { label: "身份证人像图", type: CertificateTypeEnum.Portrait, prop: "agentIdCardFront" },
      { label: "身份证国徽图", type: CertificateTypeEnum.NationalEmblem, prop: "agentIdCardReverse" },
      { label: "授权委托书", type: CertificateTypeEnum.Authorization, prop: "powerOfAttorney" }
    ]
  }
]

// 监护人
export const guardianInfo = [
  {
    label: "患者证件",
    relationship: RelationshipTypeEnum.OWN,
    children: [
      { label: "患者出生证明", type: CertificateTypeEnum.BirthCertificate, prop: "birthProof" },
      { label: "户口本患者页", type: CertificateTypeEnum.ResidenceBooklet, prop: "householdRegister" }
    ]
  },
  {
    label: "申请人信息",
    relationship: RelationshipTypeEnum.GUARDIAN,
    children: [
      { label: "身份证人像图", type: CertificateTypeEnum.Portrait, prop: "guardianIdCardFront" },
      { label: "身份证国徽图", type: CertificateTypeEnum.NationalEmblem, prop: "guardianIdCardReverse" }
    ]
  }
]

// 继承人
export const inheritorInfo = [
  {
    label: "患者证件",
    relationship: RelationshipTypeEnum.OWN,
    children: [{ label: "死亡证明", type: CertificateTypeEnum.DeathCertificate, prop: "deathProof" }]
  },
  {
    label: "继承人证件",
    relationship: RelationshipTypeEnum.HEIR,
    children: [
      { label: "身份证人像图", type: CertificateTypeEnum.Portrait, prop: "heirIdCardFront" },
      { label: "身份证国徽图", type: CertificateTypeEnum.NationalEmblem, prop: "heirIdCardReverse" },
      { label: "关系证明", type: CertificateTypeEnum.RelationshipCertificate, prop: "heirProof" }
    ]
  }
]

// 代理继承人
export const agentInheritorInfo = [
  ...inheritorInfo,
  {
    label: "代理人继承人证件",
    relationship: RelationshipTypeEnum.HEIR_AGENT,
    children: [
      { label: "身份证人像图", type: CertificateTypeEnum.Portrait, prop: "heirAgentIdCardFront" },
      { label: "身份证国徽图", type: CertificateTypeEnum.NationalEmblem, prop: "heirAgentIdCardReverse" },
      { label: "授权委托书", type: CertificateTypeEnum.Authorization, prop: "heirAgentAttorney" }
    ]
  }
]

// 登记信息
export const registrationInfo = [
  {
    label: "患者信息",
    children: [
      { label: "患者姓名", prop: "patientName" },
      { label: "证件类型", prop: "papersType" },
      { label: "患者身份证号", prop: "idNumber" }
    ]
  },
  {
    label: "申请人信息",
    children: [
      { label: "与患者关系", prop: "patientName" },
      { label: "申请人姓名", prop: "papersType" },
      { label: "申请人证件号", prop: "idNumber" },
      { label: "申请人手机", prop: "idNumber" }
    ]
  },
  {
    label: "打印信息",
    children: [
      { label: "打印类型", prop: "patientName" },
      { label: "打印用途", prop: "papersType" },
      { label: "打印份数", prop: "idNumber" },
      { label: "总打印页数", prop: "idNumber" }
    ]
  },
  {
    label: "费用信息",
    children: [
      { label: "支付金额", prop: "patientName" },
      { label: "支付方式", prop: "papersType" }
    ]
  },
  {
    label: "邮寄信息",
    children: [
      { label: "支付方式", prop: "patientName" },
      { label: "收件人", prop: "papersType" },
      { label: "手机号", prop: "idNumber" },
      { label: "收件地址", prop: "idNumber" }
    ]
  }
]
