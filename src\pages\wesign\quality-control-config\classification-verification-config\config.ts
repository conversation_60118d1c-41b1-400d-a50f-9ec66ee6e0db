import type { TableColumnItem } from "@/types"
import type { FormRules } from "element-plus"

export const menuId = "/quality-control-config/classification-verification-config"

export const classificationConfigColumns: Array<TableColumnItem> = [
  { prop: "documentMrClassName", label: "文档类型", minWidth: 200 },
  { prop: "applyRoom", label: "应用科室", minWidth: 300 },
  { prop: "status", label: "状态", minWidth: 100 },
  { prop: "operation", label: "操作", width: 200, fixed: "right" }
]
export const classificationConfigFormRules: FormRules = {
  documentMrClassName: [{ required: true, message: "请选择文档类型", trigger: "blur" }]
}
