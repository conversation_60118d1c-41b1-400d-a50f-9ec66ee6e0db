<template>
  <CardContainer class="create-process-wrapper" sign title="流程配置">
    <template #header>
      <div>
        <el-button v-if="activeStep === 1" @click="goToPrevStep">返回</el-button>
        <el-button type="primary" @click="saveApprovalProcess">发布</el-button>
      </div>
    </template>

    <!-- 内容区域 -->
    <div class="create-process-container">
      <ApprovalStep v-model:active-step="activeStep" :status="basicInfoVerificationResults" />
      <!--  -->
      <div class="create-process-content">
        <!-- 基本信息 -->
        <el-form
          v-if="activeStep === 0"
          ref="basicInfoRef"
          :rules="processBasicInfoRules"
          label-width="100px"
          :model="basicInfoState"
          class="create-process-basic-info"
        >
          <el-form-item label="流程名称" prop="flowName">
            <el-input
              v-model="basicInfoState.flowName"
              show-word-limit
              placeholder="请输入流程名称"
              :maxlength="50"
              clearable
            />
          </el-form-item>

          <CommonRadioFormItem
            v-model="basicInfoState.flowType"
            :disabled="processId?.length > 0"
            label="业务类型"
            prop="flowType"
            :options="createProcessBusinessTypeOptions"
          />

          <CommonSelectFormItem
            v-model="basicInfoState.applyDepartment"
            :disabled="processId?.length > 0"
            label="应用科室"
            prop="applyDepartment"
            :options="createProcessApplyDepartmentOptions"
          />

          <el-form-item label="流程负责人" prop="adminAssignees">
            <UserSelect ref="userSelectRef" v-model:members="basicInfoState.adminAssignees" :multiple="false" />
          </el-form-item>

          <el-form-item label="">
            <el-button type="primary" @click="goToNextStep">提交</el-button>
          </el-form-item>
        </el-form>

        <!-- 审批节点配置 -->
        <CreateProcessNode v-else ref="approvalProcessRef" v-model:data="flowDefineJSON" />
      </div>
    </div>
  </CardContainer>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue"
import { useRoute, useRouter } from "vue-router"
import { FormInstance } from "element-plus"
import { CardContainer, CommonSelectFormItem, CommonRadioFormItem } from "@/base-components"
import { getApprovalProcessDetailApi } from "@/interfaces"
import { toastError, Message } from "@/utils"
import { updateApprovalProcessApi } from "../../interface"
import ApprovalStep from "./components/ApprovalStep.vue"
import CreateProcessNode from "./components/CreateProcessNode.vue"
import UserSelect from "./components/UserSelect.vue"
import {
  createProcessBusinessTypeOptions,
  processBasicInfoRules,
  createProcessApplyDepartmentOptions,
  FlowTypeEnum,
  ApproveSettingEnum
} from "./config"

const route = useRoute()
const router = useRouter()
const processId = route.query?.processId as string
const activeStep = ref(0)

const approvalProcessRef = ref<any>()

/*============================基本信息=========================*/
const basicInfoRef = ref<FormInstance>()

const basicInfoState = reactive({
  flowName: "",
  flowType: FlowTypeEnum.BORROW,
  applyDepartment: "ALL",
  adminAssignees: "",
  adminAssigneesName: ""
})

//选择流程负责人
const userSelectRef = ref<any>()

//基本信息校验结果
const basicInfoVerificationResults = ref(true)

//监听basicInfoState
watch(
  () => basicInfoState,
  () => {
    if (basicInfoRef.value) {
      validateBasicInfo()
    }
    if (basicInfoState.adminAssignees) {
      basicInfoState.adminAssigneesName = userSelectRef.value?.userState?.usersDataSource?.find(
        item => item.wsid === basicInfoState.adminAssignees
      )?.realName
    }
  },
  { deep: true, immediate: true }
)

// 校验基本信息
const validateBasicInfo = (): any => {
  basicInfoRef.value?.validate((valid: boolean) => {
    basicInfoVerificationResults.value = valid
  })
}

// 到第二步审批节点
const goToNextStep = () => {
  validateBasicInfo()
  activeStep.value = 1
}

// 到第一步基本信息
const goToPrevStep = () => {
  activeStep.value = 0
}

/*======================流程配置==========================*/

const flowDefineJSON = ref()

// 监听activeStep，如果最新的activeStep是第一步，就获取审批节点的数据，因为在第一步基本信息页面，获取不到审批配置的ref
watch(
  () => activeStep.value,
  val => {
    if (val === 0) {
      flowDefineJSON.value = approvalProcessRef.value?.getGraphData()
    }
  }
)

// 获取流程详情
const getApprovalProcessDetail = () => {
  if (!route.query?.flowDefineId) return
  getApprovalProcessDetailApi(route.query?.flowDefineId as string)
    .then(res => {
      flowDefineJSON.value = res.data.data
      basicInfoState.adminAssignees = res.data.data?.baseConfig?.adminAssignees?.[0]?.wsid
      basicInfoState.adminAssigneesName = res.data.data?.baseConfig?.adminAssignees?.[0]?.name
    })
    .catch(err => {
      toastError(err, "获取流程详情失败")
      flowDefineJSON.value = {}
    })
}
onMounted(() => {
  if (processId) {
    basicInfoState.flowName = route.query?.flowName as string
    basicInfoState.flowType = route.query?.flowType as FlowTypeEnum
    basicInfoState.applyDepartment = route.query?.applyDeptCode as string
    getApprovalProcessDetail()
  }
})

// 保存流程
const saveApprovalProcess = async () => {
  if (!basicInfoState.flowName) {
    Message.error("请输入流程名称")
    return
  }

  if (!basicInfoState.adminAssignees) {
    Message.error("请选择流程负责人")
    return
  }
  let flowDefine: any = {}
  if (activeStep.value === 0) {
    flowDefine = flowDefineJSON.value
  } else {
    flowDefine = approvalProcessRef.value?.getGraphData()
  }

  // 遍历flowDefine的tasks,看assigneeConfig里面的assignee是否有值，如果没有值就提示输入数据
  const tasks = flowDefine?.tasks
  if (tasks) {
    for (let i = 0; i < tasks.length; i++) {
      const task = tasks[i]
      if (task?.assigneeConfig?.type !== ApproveSettingEnum.DEPT_MANAGER) {
        if (!task.assigneeConfig?.approver || task.assigneeConfig?.approver?.length === 0) {
          Message.error(`第${i + 1}个节点的审批人为空，请完善数据!`)
          return
        }
        //遍历assigneeConfig里面的approver的每一个字段,如果有一个为空就提示输入数据
        for (let j = 0; j < task.assigneeConfig?.approver?.length; j++) {
          const approver = task.assigneeConfig?.approver[j]
          if (!approver?.wsid || !approver?.name) {
            Message.error(`第${i + 1}个节点的审批人为空，请完善数据!`)
            return
          }
        }
      }
      if (task?.assigneeConfig?.type === ApproveSettingEnum.DEPT_MANAGER) {
        const deptManager = task.assigneeConfig?.deptManager
        //遍历assigneeConfig里面的deptManager的每一个字段,如果有一个为空就提示输入数据
        if (!deptManager?.filterCode || !deptManager?.filterName || !deptManager?.roleWsid || !deptManager?.roleName) {
          Message.error(`第${i + 1}个节点的审批人为空，请完善数据!`)
          return
        }
      }
    }
  }
  const params = {
    flowName: basicInfoState.flowName,
    flowType: basicInfoState.flowType,
    applyDeptCode: basicInfoState.applyDepartment,
    adminAssignees: [
      {
        wsid: basicInfoState.adminAssignees,
        name: basicInfoState.adminAssigneesName
      }
    ],
    applyDeptName:
      createProcessApplyDepartmentOptions.find(item => item.value === basicInfoState.applyDepartment)?.label || "",
    flowDefine
  }
  await updateApprovalProcessApi(processId ? { ...params, id: processId } : params)
    .then(() => {
      if (processId) {
        Message.success("编辑成功")
      } else {
        Message.success("创建成功")
      }
      router.push("/system-manage/process-setting/approval-process/list")
    })
    .catch(err => {
      toastError(err, "保存失败")
    })
}
</script>

<style lang="less" scoped>
.create-process-wrapper {
  .create-process-container {
    height: 100%;

    .create-process-content {
      height: calc(100% - 50px);
      overflow: auto;
      .create-process-basic-info {
        padding-top: 100px;
        width: 500px;
        margin: auto;
      }
    }
  }
}
</style>
