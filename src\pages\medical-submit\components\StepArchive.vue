<template>
  <CardContainer
    v-show="submitStore.activeStep === 2"
    :allowed-return="true"
    :return-url="route.query?.returnUrl as string"
    :title="`${props.patientName} （ ${props.inpNo}）`"
    style="height: 100vh"
  >
    <template #title>
      <div class="patient-hospitalTime">
        {{ formatDatetime(Number(route.query.inHospitalDatetime), "YYYY/MM/DD") }} -
        {{ formatDatetime(Number(route.query.outHospitalDatetime), "YYYY/MM/DD") }}
      </div>
    </template>
    <template #header>
      <div class="header-right">
        <div class="header-right-title"></div>
        <div>
          <el-button @click="toPrev">上一份文件</el-button>
          <el-button @click="toNext">下一份文件</el-button>
          <el-button style="color: #3860f4" color="#D7DFFD" :disabled="state.loading" @click="back">上一步</el-button>
          <el-tooltip class="box-item" effect="dark" content="同步对系统主动采集文书/数据生效" placement="top">
            <el-button style="color: #3860f4" color="#D7DFFD" :disabled="state.loading" @click="openSyncDialog">
              同步
            </el-button>
          </el-tooltip>

          <el-button style="color: #3860f4" color="#D7DFFD" :disabled="state.loading" @click="toMedicalAdviceCheck">
            医嘱核对
          </el-button>
          <el-button
            v-if="!route.query?.returnUrl?.includes('/returned')"
            style="color: #3860f4"
            color="#D7DFFD"
            :disabled="state.loading && !integralityListLength"
            @click="sendNotify"
          >
            发送整改通知
          </el-button>
          <template v-if="integralityListLength">
            <el-button
              :disabled="props.allowMissing === 'NO'"
              style="color: #fff"
              color="#3860F4"
              @click="confirmSubmit"
            >
              病案提交
            </el-button>
          </template>
          <template v-else>
            <el-button style="color: #fff" color="#3860F4" @click="confirmSubmit">病案提交</el-button>
          </template>
        </div>
      </div>
    </template>

    <div v-loading="state.loading" class="document-container">
      <!-- 左侧文件操作栏 -->
      <div class="left-container">
        <div class="aside-container" :style="{ width: leftCollapse ? '0px' : '350px' }">
          <!-- 病案文件列表 -->
          <div class="record-tree">
            <div class="no-inpNo-document" :style="{ display: leftCollapse ? 'none' : 'flex' }">
              <!-- 无住院号且在本次住院期间产生的文书 -->
              <el-checkbox v-model="autoAssignmentStatus" size="large">
                <div style="display: flex; align-items: center">
                  无住院号
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    content="无住院号且在本次住院期间产生的文书"
                    placement="top"
                  >
                    <el-icon>
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </div>
              </el-checkbox>
              <!-- 翻拍 -->
              <el-checkbox v-model="isSupplement" label="翻拍" size="large" />
            </div>
            <CommonTree
              ref="submissionTreeRef"
              node-key="wsid"
              :data="state.documentTree"
              :filter-tree-node="filterTreeNode"
              :filter-keys="[autoAssignmentStatus, isSupplement]"
              show-delete
              @click-node="setPdfSrc"
            >
              <template #title="{ node, data }">
                <!-- 文件名 -->

                <span v-if="data.type === 'FILE'" style="margin-right: 4px">
                  <i v-if="!data.isSupplement" class="ri-file-text-line electric-icon"></i>
                  <i v-else class="ri-camera-line paper-icon"></i>
                </span>
                <!-- 文本 -->
                <OverflowTooltip
                  v-if="!data.renamable"
                  :content="getNodeTooltipContent(node, data, false)"
                  max-width="65%"
                ></OverflowTooltip>
                <div
                  v-if="data.autoAssignmentStatus && !data.renamable && data.type === 'FILE'"
                  class="auto-assignment"
                ></div>

                <el-input
                  v-if="data.type === 'FILE' && data.renamable"
                  v-model="data.title"
                  style="max-width: 75%"
                  size="small"
                  placeholder="请输入文件名称"
                  @click.stop
                  @blur="saveRename(data)"
                >
                  <template #append>
                    <el-button size="small" :icon="Select" @click="saveRename(data)" />
                  </template>
                </el-input>

                <!--  操作按钮-->
                <div class="operation-wrapper">
                  <!-- 编辑文件名称 -->
                  <i
                    v-if="data.type === 'FILE' && !data.renamable"
                    style="color: #3860f4"
                    class="ri-edit-box-line"
                    @click.stop="handleRename(data)"
                  ></i>
                  <!-- 删除 -->
                  <i
                    v-if="data.type === 'FILE' && data.homePageFlag !== 1"
                    class="ri-delete-bin-line"
                    @click.stop="handleDeleteNode(node)"
                  ></i>
                </div>
              </template>
            </CommonTree>
          </div>
          <!-- 需要归档的文件列表 -->
          <div class="archive-list">
            <div class="archive-list__header">
              <el-checkbox
                v-model="checkedAllArchive"
                size="large"
                :disabled="state.inactiveDocuments.length === 0"
                @change="handleSelectAllArchive"
              >
                <div style="display: flex; align-items: center">
                  全选
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    content="无法确定文书为患者本次住院文书，用户选择是否归档"
                    placement="top"
                  >
                    <el-icon>
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </div>
              </el-checkbox>
              <div>选择归档文件</div>

              <el-select
                v-model="beforeDays"
                placeholder="请选择"
                style="width: 100%; margin-top: 16px"
                @change="selectBeforeDays"
              >
                <el-option
                  v-for="(item, index) in beforeDaysOptions"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>

            <div class="archive-list__content">
              <empty-content v-if="state.inactiveDocuments.length === 0" desc="暂无数据" />
              <el-checkbox-group v-else v-model="state.checkedArchiveDocumentWsidList">
                <el-checkbox
                  v-for="archiveDocument in state.inactiveDocuments"
                  :key="archiveDocument.wsid"
                  :label="archiveDocument.wsid"
                  class="custom-checkbox"
                  @change="setPdfSrc(archiveDocument)"
                >
                  <div :title="archiveDocument.title + '（' + archiveDocument.filePage + '页）'" class="file-label">
                    <div>
                      <span style="margin-right: 4px">
                        <i v-if="!archiveDocument.isSupplement" class="ri-file-text-line electric-icon"></i>
                        <i v-else class="ri-camera-line paper-icon"></i>
                      </span>
                      <span v-if="!archiveDocument.renamable">
                        {{ archiveDocument.title }}（{{ archiveDocument.filePage }}页）
                      </span>
                      <el-input
                        v-else
                        v-model="archiveDocument.title"
                        style="max-width: 75%"
                        size="small"
                        placeholder="请输入文件名称"
                        @click.stop
                        @blur="saveRename(archiveDocument)"
                      >
                        <template #append>
                          <el-button color="#3860f4" size="small" :icon="Select" @click="saveRename(archiveDocument)" />
                        </template>
                      </el-input>
                    </div>
                    <!-- 编辑文件名称 -->
                    <i
                      v-if="!archiveDocument.renamable"
                      style="color: #3860f4"
                      class="ri-edit-box-line"
                      @click.stop="handleRename(archiveDocument)"
                    ></i>
                  </div>
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </div>
        <!-- 展开/收缩 -->
        <div
          class="collapse-icon"
          :style="{
            boxShadow: leftCollapse ? '0px 0px 3px 1px rgba(0, 35, 114, 0.1)' : '3px 0px 3px 1px rgba(0, 35, 114, 0.1)',
            borderRadius: leftCollapse ? '5px' : '0px 5px 5px 0px'
          }"
          @click="() => (leftCollapse = !leftCollapse)"
        >
          <el-icon v-if="!leftCollapse"><ArrowLeft /></el-icon>
          <el-icon v-if="leftCollapse"><ArrowRight /></el-icon>
        </div>
      </div>

      <!-- pdf预览 -->
      <div class="pdf-container">
        <PdfPreviewComponent
          v-if="state.currentPdfSrc"
          ref="pdfPreviewRef"
          :src="state.currentPdfSrc"
        ></PdfPreviewComponent>
      </div>

      <div class="right-container">
        <div
          v-if="!route.query?.returnUrl?.includes('/returned')"
          class="integrality-container"
          :style="{ width: rightCollapse ? '0px' : '408px' }"
        >
          <!-- 完整性弹窗 -->
          <IntegralityDrawer
            ref="integralityDrawerRef"
            v-model:submit-remark="state.submitRemark"
            :inp-no="props.inpNo"
            :allow-missing="props.allowMissing"
            :wsid="state.wsid"
            :out-hospital-dept-wsid="state.outHospitalDeptWsid"
            @sync="handleSync"
          />
        </div>

        <div
          v-else
          class="process-container"
          :style="{ width: rightCollapse ? '0px' : '388px', padding: rightCollapse ? '0px' : '0 10px' }"
        >
          <el-tabs v-if="!rightCollapse" v-model="currentTab">
            <!-- <el-tab-pane label="流程图" name="processGraph">
              <FlowGraph v-if="businessInfo?.nodes" :data="businessInfo?.nodes" />
            </el-tab-pane> -->
            <el-tab-pane label="运行日志" name="runningLogs">
              <RunningLogs v-if="businessInfo?.logs" :data="businessInfo?.logs" />
            </el-tab-pane>
          </el-tabs>
        </div>

        <!-- 展开/收缩 -->
        <div
          class="right-collapse-icon"
          :style="{
            boxShadow: rightCollapse
              ? '3px 0px 3px 1px rgba(0, 35, 114, 0.1)'
              : '0px 0px 3px 1px rgba(0, 35, 114, 0.1)',
            borderRadius: rightCollapse ? '5px' : '5px 0px 0px 5px'
          }"
          @click="() => (rightCollapse = !rightCollapse)"
        >
          <el-icon v-if="rightCollapse"><ArrowLeft /></el-icon>
          <el-icon v-if="!rightCollapse"><ArrowRight /></el-icon>
        </div>
      </div>
    </div>
  </CardContainer>

  <SyncDialog
    ref="syncDialogRef"
    :inp-no="props.inpNo"
    :checked-mr-class-code="checkedMrClassCode"
    @success="handleSyncSuccess"
  />
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick, onMounted, computed } from "vue"
import { useRouter, useRoute } from "vue-router"
import { ElMessageBox } from "element-plus"
import { debounce } from "lodash-es"
import { Select, ArrowLeft, ArrowRight } from "@element-plus/icons-vue"
import {
  CommonTree,
  EmptyContent,
  PdfObjectPreview,
  CardContainer,
  OverflowTooltip,
  PdfPreviewComponent
} from "@/base-components"
import { SyncDialog } from "@/page-components"
import { beforeDaysOptions } from "../config"
import { useSubmitStore } from "../useSubmitStore"
import IntegralityDrawer from "./IntegralityDrawer.vue"
import type { AllowMissingType } from "../config"
import {
  deleteFileApi,
  getSubmissionTreeApi,
  getInactiveDocumentsApi,
  sendModifyMessageApi,
  fileRenameApi,
  getMedicalRecordProcessApi
} from "@/interfaces"
import { FlowGraph, RunningLogs } from "@/pages/wesign/medical-record-manage/medical-record-home/components"
import { useSystemStore } from "@/stores"
import {
  Message,
  getFileNodeList,
  SystemPrompt,
  toastError,
  formatRecordTree,
  formatDatetime,
  getNodeTooltipContent,
  SystemAlert
} from "@/utils"

const submitStore = useSubmitStore()
const systemStore = useSystemStore()
const router = useRouter()
const route = useRoute()

const pdfRef = ref()

interface SubmitProps {
  inpNo: string
  secretKey: string
  patientName: string
  lastSyncTime: string
  allowMissing: AllowMissingType // 是否允许缺失
}
const props = defineProps<SubmitProps>()
const emits = defineEmits(["refresh", "delete", "submit"])

interface ArchiveState {
  loading: boolean
  currentPdfSrc: string // 用户当前所选的pdf文件url
  inactiveDocuments: Record<string, any>[] // 可以归档的文件
  checkedArchiveDocumentWsidList: string[] // 用户所勾选需要归档的文件
  documentTree: Record<string, any>[] // 病案树形结构
  submitRemark: string
  documentNodeList: Record<string, any>[] // 病案文件列表
  documentWsid: string // 当前点击的文档wsid
  outHospitalDeptWsid: string //出院科室id
  wsid: string //出院科室id
}

const state = reactive<ArchiveState>({
  loading: false,
  currentPdfSrc: "",
  inactiveDocuments: [],
  checkedArchiveDocumentWsidList: [],
  documentTree: [],
  submitRemark: "",
  documentNodeList: [],
  documentWsid: "" as string, // 当前点击的文档wsid
  outHospitalDeptWsid: "",
  wsid: ""
})

// 获取病案树结构
const getSubmissionTree = () => {
  state.loading = true
  getSubmissionTreeApi(props.inpNo, props.secretKey)
    .then(res => {
      state.documentTree = formatRecordTree(res.data.data.treeInfo)
      state.loading = false
      submitStore.status = res.data.data.baseInfo.statusEnumName
      state.outHospitalDeptWsid = res.data.data.baseInfo.outHospitalDeptWsid
      state.wsid = res.data.data.baseInfo.wsid
    })
    .catch(() => {
      state.loading = false
    })
}

// 获取未归档的文件
const getInactiveDocuments = () => {
  getInactiveDocumentsApi({ inpNo: props.inpNo, beforeDays: beforeDays.value }).then(res => {
    const __inactiveDocuments = res.data?.data ?? []
    state.inactiveDocuments = __inactiveDocuments.map(doc => {
      doc.type = "FILE"
      doc.renamable = false
      return doc
    })
  })
}

watch(
  () => props.inpNo,
  val => {
    if (!val) return
    nextTick(() => {
      getSubmissionTree()
      getInactiveDocuments()
      if (submitStore.status === "REPAIR") {
        ElMessageBox.confirm("当前病案数据已返修，请确认您已同步过病案", "注意", {
          type: "warning",
          confirmButtonText: "已同步"
        }).then(() => {
          integralityDrawerRef.value?.checkIntegrality()
        })
      } else {
        integralityDrawerRef.value?.checkIntegrality()
      }
      getBusinessInfo(val)
    })
  },
  { immediate: true }
)

const integralityDrawerRef = ref<InstanceType<typeof IntegralityDrawer>>()

/* ============== tree =============== */

const submissionTreeRef = ref() // tree ref

const activeNode = ref({})
// 点击node节点，设置当前需要预览的pdf
const setPdfSrc = node => {
  activeNode.value = node
  if (!node?.contentFilePath) return
  const newPdfSrc = "/api" + node.contentFilePath
  if (state.currentPdfSrc === newPdfSrc) return
  if (node.type === "FILE") {
    state.currentPdfSrc = newPdfSrc
  }
  state.documentWsid = node?.wsid
}

// 删除文档
const handleDeleteNode = node => {
  SystemPrompt("确认删除该文件吗？").then(() => {
    deleteFileApi(node.data.wsid)
      .then(res => {
        if (!res.data.data) {
          Message.error("删除失败")
        } else {
          Message.success("删除成功")
          emits("delete", node.data.fileWsid)
          getSubmissionTree()
        }
      })
      .catch(error => {
        toastError(error)
      })
  })
}

/* ============== 同步 =============== */

const syncDialogRef = ref<InstanceType<typeof SyncDialog>>()

const openSyncDialog = () => {
  syncDialogRef.value?.show()
}

// 同步指定文书完整性
const checkedMrClassCode = ref<Array<string>>([])
const handleSync = mrClassCode => {
  checkedMrClassCode.value = [mrClassCode]
  nextTick(() => {
    syncDialogRef.value?.show()
  })
}

// 同步病案
const handleSyncSuccess = () => {
  getSubmissionTree()
  getInactiveDocuments()
  emits("refresh")
  // 同步后重新校验病案完整性
  integralityDrawerRef.value?.checkIntegrality()
}

/* ============== 其他 =============== */

// 点击上一步
const back = () => {
  submitStore.activeStep = 1
  submitStore.checkedArchiveDocumentWsidList = state.checkedArchiveDocumentWsidList
  // 保存备注
  submitStore.submitRemark = state.submitRemark
  emits("refresh")
}

// 发送整改通知
const sendNotify = () => {
  systemStore.pageLoading = true
  sendModifyMessageApi(props.inpNo)
    .then(() => {
      systemStore.pageLoading = false
      SystemAlert("整改通知发送成功，将返回列表页面", "success").then(() => {
        router.replace((route.query?.returnUrl as string) || "/medical-record/list")
      })
    })
    .catch(error => {
      systemStore.pageLoading = false
      toastError(error, "发送通知失败！")
    })
}

// 监听 tree 改变，设置初始的 pdf 预览文件
watch(
  () => state.documentTree,
  tree => {
    const fileList = getFileNodeList(tree)
    state.documentNodeList = fileList
    // 判断首页文件是否存在
    const homePageFile = fileList.find(file => file.homePageFlag === 1)
    submitStore.homePageFlag = homePageFile ? true : false
    // 没有文档则设置pdf预览为空
    if (!fileList.length) {
      state.currentPdfSrc = ""
      return
    }

    setPdfSrc(Object.keys(activeNode.value).length > 0 ? activeNode.value : fileList[0])
    nextTick(() => submissionTreeRef.value?.setCurrentKey(activeNode.value?.wsid || fileList[0]?.wsid))
  },
  { immediate: true, deep: true }
)

// 待归档文件选择
const beforeDays = ref("14")

const selectBeforeDays = val => {
  getInactiveDocuments()
}
// 全选归档文件
watch(
  () => state.checkedArchiveDocumentWsidList,
  val => {
    checkedAllArchive.value = val.length === state.inactiveDocuments.length
    submitStore.checkedArchiveDocumentWsidList = val
  }
)

const checkedAllArchive = ref(false)
const handleSelectAllArchive = val => {
  state.checkedArchiveDocumentWsidList = val ? state.inactiveDocuments.map(item => item.wsid) : []
}

// pdf切换
const toPrev = debounce(() => {
  if (!state.documentNodeList) return
  const index = state.documentNodeList.findIndex(item => item.wsid === state.documentWsid) || 0
  if (index <= 0) return
  const targetNode = state.documentNodeList[index - 1]
  setPdfSrc(targetNode)
  submissionTreeRef.value?.setCurrentKey(targetNode.wsid)
}, 300)

const toNext = debounce(() => {
  if (!state.documentNodeList) return
  const index = state.documentNodeList.findIndex(item => item.wsid === state.documentWsid)
  if (index >= state.documentNodeList.length - 1) return
  const targetNode = state.documentNodeList[index + 1]
  setPdfSrc(targetNode)
  submissionTreeRef.value?.setCurrentKey(targetNode.wsid)
}, 300)

// 病案提交
const confirmSubmit = () => {
  // 保存备注
  submitStore.submitRemark = state.submitRemark
  emits("submit")
}

const handleRename = data => {
  data.renamable = true
}

// 文件重命名
const saveRename = async data => {
  try {
    data.renamable = false
    await fileRenameApi(data.wsid, data.title)
  } catch (error: any) {
    toastError(error, "文件重命名失败！")
  }
}

// 判断展示提交、发送消息通知按钮
const integralityListLength = computed(() => {
  return (
    integralityDrawerRef.value?.documentIntegralityList?.filter(item => item.qcResult === "NO_GATHER").length ||
    0 + (integralityDrawerRef.value?.homeIntegralityList?.length || 0)
  )
})

// 侧边栏隐藏
const leftCollapse = ref(false)
const rightCollapse = ref(false)

// 右侧运行日志
type TabValue = "processGraph" | "runningLogs"

const currentTab = ref<TabValue>("runningLogs")

const businessInfo = ref<Record<string, any>>({})
const getBusinessInfo = async (inpNo: string) => {
  // currentTab.value = "processGraph"
  businessInfo.value = (await getMedicalRecordProcessApi(inpNo)).data.data
}

const toMedicalAdviceCheck = () => {
  window.open(`/medical-advice-check?${route.fullPath.split("?")[1]}`)
}

/* ============== 筛选文件 =============== */
const isSupplement = ref(false)
const autoAssignmentStatus = ref(false)

const filterTreeNode = (value, data, node) => {
  if (value.every(item => item === false)) {
    return true
  } else if (value.some(item => item === true)) {
    if (data.type === "FILE") {
      return (
        (value[0] ? data.autoAssignmentStatus : !data.autoAssignmentStatus) &&
        (value[1] ? data.isSupplement === 1 : data.isSupplement === 0)
      )
    }
  }
}

defineExpose({ getSubmissionTree, getInactiveDocuments })
</script>

<style lang="less" scoped>
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .last-sync-time {
    color: #999;
  }
}

.document-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  // margin-top: 20px;
  // height: 95%;
  height: calc(100% - 32px);
  margin: 16px;
  background: #fff;
}

// @media screen and (max-height: 900px) {
//   .document-container {
//     height: 80%;
//   }
// }

// @media screen and (min-height: 901px) and (max-height: 1080px) {
//   .document-container {
//     height: 95%;
//   }
// }

// @media screen and (min-height: 1081px) and (max-height: 1440px) {
//   .document-container {
//     height: 90%;
//   }
// }

.left-container,
.right-container {
  height: 100%;
  position: relative;
}

.aside-container {
  // min-width: 300px;
  // width: 350px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: column;
  height: 100%;
  position: relative;
  transition: width 0.5s;
}

.integrality-container {
  height: 100%;
  position: relative;
  transition: width 0.5s;
  background: #fff;
}

.process-container {
  height: 100%;
  padding: 0 10px;
  position: relative;
  transition: width 0.5s;
  background: #fff;

  :deep(.el-tabs--top) {
    height: 100%;
  }

  :deep(.el-tabs__content) {
    height: calc(100% - 55px);
    overflow-y: auto;
  }
}

.record-tree {
  width: calc(100% - 20px);
  height: calc(50% - 30px);
  // border: 1px solid #e5e5e5;
  // border-radius: 4px;
  padding: 10px;
}

.archive-list {
  height: 50%;
  width: 100%;
  // border: 1px solid #e5e5e5;
  // border-radius: 4px;

  &__header {
    position: sticky;
    color: #0a1633;
    font-size: 14px;
    height: 40px;
    line-height: 40px;
    padding-left: 10px;
    background: #f0f4fb;
  }

  &__content {
    padding: 0 20px;
    height: calc(100% - 100px);
    overflow-y: auto;
    margin-top: 60px;
  }
}

.pdf-container {
  border: 1px solid #e5e5e5;
  height: 100%;
  flex: 1;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 20px;
}

.custom-checkbox {
  align-items: center;
  // flex-direction: row-reverse;
  width: 100%;
  justify-content: flex-end;
  margin-right: 0;
  margin-bottom: 6px;
}
.ri-delete-bin-line {
  margin-left: 10px;
  font-size: 16px;
  color: var(--el-color-danger);
}

.archive-list__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 0 20px;
}
.header-right {
  flex: 1;
  display: flex;
  justify-content: space-between;
  // border-left: 1px solid #e5e5e5;
  padding-left: 16px;
  // margin-left: 190px;

  &-title {
    font-weight: 600;
    font-size: 14px;
    color: #0a1633;
  }
}

.remark {
  height: calc(100% - 14px);
  padding: 7px 12px;
}

:deep(.custom-card) {
  height: 100vh !important;
}

:deep(.custom-card-title) {
  width: 334px !important;
}

:deep(.custom-card-body) {
  padding: 0 !important;
  // overflow-y: hidden !important;
}
.electric-icon {
  color: #04c3a1;
  font-size: 16px;
}
.paper-icon {
  color: #409eff;
  font-size: 16px;
}

:deep(.el-checkbox__label) {
  width: 90% !important;
}
.file-label {
  overflow: hidden;
  justify-content: space-between;
  align-items: center;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 100%;
  display: flex;
}
.auto-assignment {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #f59a23;
  margin-right: 5px;
}
.operation-wrapper {
  display: flex;
  flex: 1;
  justify-content: flex-end;
  align-items: center;
}
.tips-content {
  color: #fff;
  font-size: 12px;
}
.no-inpNo-document {
  font-size: 12px;
  display: flex;
  align-items: center;
}
.patient-hospitalTime {
  font-size: 12px;
  color: #999999;
  display: flex;
  align-items: center;
  position: absolute;
  bottom: 5px;
  line-height: 1;
}

.collapse-icon {
  width: 24px;
  height: 48px;
  background: #fff;
  right: -24px;
  padding: 16px 4px;
  box-sizing: border-box;
  position: absolute;
  top: 0px;
  bottom: 0px;
  margin: auto;
  z-index: 999;
  cursor: pointer;
  &:hover {
    color: #999;
  }
}
.right-collapse-icon {
  width: 24px;
  height: 48px;
  background: #fff;
  left: -24px;
  padding: 16px 4px;
  box-sizing: border-box;
  position: absolute;
  top: 0px;
  bottom: 0px;
  margin: auto;
  z-index: 999;
  cursor: pointer;
  &:hover {
    color: #999;
  }
}
</style>
