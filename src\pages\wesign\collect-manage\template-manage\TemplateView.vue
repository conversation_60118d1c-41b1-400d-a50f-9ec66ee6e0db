<template>
  <div class="pdf-preview-container">
    <PdfPreviewComponent :src="templateSrc" :show-watermark="false" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue"
import { useRoute } from "vue-router"
import { PdfPreviewComponent } from "@/base-components"

const route = useRoute()

const templateSrc = ref("")

onMounted(() => {
  const wsid = route.query?.wsid ?? ""
  templateSrc.value = "/api/collect/templates/" + wsid + "/view"
})
</script>

<style lang="less" scoped>
.pdf-preview-container {
  height: 100%;
}
</style>
