import { createPinia } from "pinia"
import piniaPluginPersistedstate from "pinia-plugin-persistedstate"
import useGlobalOptionsStore from "./modules/global-options"
import useResourceStore from "./modules/resource"
import useSystemStore from "./modules/system"
import useUserStore from "./modules/user"

const store = createPinia()
store.use(piniaPluginPersistedstate)

export default store
export { useUserStore, useSystemStore, useGlobalOptionsStore, useResourceStore }
