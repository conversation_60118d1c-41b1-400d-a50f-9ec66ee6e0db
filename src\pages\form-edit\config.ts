import { computed } from "vue"
import { map } from "lodash/fp"
import { v4 as uuidv4 } from "uuid"
import type { BaseOptionItem } from "@/types"
import {
  FormItemConfig,
  nationOptions,
  MethodCodeEnum,
  dateRange,
  dateRangeType,
  FormItemType,
  DateFormatEnum
} from "@/configs"
import { getCustomRule, Message } from "@/utils"

const genderOptions: Array<IBaseOptionItem> = [
  { label: "男", value: "男" },
  { label: "女", value: "女" }
]
export const icdTypeOptions: Array<IBaseOptionItem> = [
  { label: "病种对码", value: "ICD_BZ_DM" },
  { label: "手术对码", value: "ICD_SS_DM" }
]

const defaultOptions: Array<IBaseOptionItem> = [
  { label: "选项一", value: "选项一", id: uuidv4() },
  { label: "选项二", value: "选项二", id: uuidv4() }
]

export function getDefaultWidth(formItemType: FormItemType) {
  if ([FormItemType.GroupHeadline, FormItemType.IdCardGroup].includes(formItemType)) return "100%"
  return "25%"
}

export function getDefaultValue(formItemType: FormItemType) {
  if ([FormItemType.BaseCheckbox].includes(formItemType)) return []
  else return ""
}

// 日期选择
export const dateFormatOptions = [
  { label: "年", value: DateFormatEnum.Year },
  { label: "年-月", value: DateFormatEnum.Month },
  { label: "年-月-日", value: DateFormatEnum.Date },
  { label: "年-月-日 时:分", value: DateFormatEnum.Minutes },
  { label: "年-月-日 时:分:秒", value: DateFormatEnum.Seconds }
]

//生成的身份证控件,其中包含姓名，性别，民族，出生日期，住址，身份证号码
const setIdCardGroup = (): Array<FormItemConfig> => {
  const formItemTypes = [
    {
      type: FormItemType.BaseInput,
      name: "姓名",
      width: "25%",
      prop: "name"
    },
    {
      type: FormItemType.BaseRadio,
      name: "性别",
      width: "25%",
      prop: "sex"
    },
    {
      type: FormItemType.BaseSelect,
      name: "民族",
      width: "25%",
      prop: "nation"
    },
    {
      type: FormItemType.BaseDate,
      name: "出生日期",
      width: "25%",
      prop: "birthday"
    },
    {
      type: FormItemType.BaseInput,
      name: "地址",
      width: "50%",
      prop: "address"
    },
    {
      type: FormItemType.BaseInput,
      name: "证件号码",
      width: "50%",
      prop: "idNumber"
    }
  ]
  const idCardGroups = [] as Array<FormItemConfig>
  formItemTypes.forEach((item, index) => {
    const formConfig = createFormItem(item.type, item.name, item.width)
    formConfig.prop = item.prop as string
    formConfig.required = true

    if (item.name === "出生日期") {
      formConfig.rule = `\\d{4}-\\d{2}-\\d{2}`
      formConfig.ruleMessage = "日期格式为YYYY-MM-DD"
      formConfig.prop = "birthday"
    }
    if (item.name === "性别") {
      formConfig.options = genderOptions
      const genderOptionsValue = map("value")(genderOptions)
      formConfig.rule = `(${genderOptionsValue.join("|")})(,(${genderOptionsValue.join("|")}))*`
      formConfig.ruleMessage = "该值不在规定的值域中"
    }
    if (item.name === "民族") {
      formConfig.options = nationOptions
      const tempOptionsValue = map("value")(nationOptions)
      formConfig.rule = `(${tempOptionsValue.join("|")})(,(${tempOptionsValue.join("|")}))*`
      formConfig.ruleMessage = "该值不在规定的值域中"
      formConfig.default = "汉族"
    }
    // if (item.name === "证件号码") {
    //   formConfig.rule =
    //     "([1-6][1-9]|50)\\d{4}(18|19|20)\\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]|([1-6][1-9]|50)\\d{4}\\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\\d{3}"
    //   formConfig.ruleMessage = "不满足15/18位身份证格式"
    // }
    idCardGroups.push(formConfig)
  })
  return idCardGroups
}

export const addressAccuracyOption: Array<BaseOptionItem> = [
  { label: "省/地区、市、区/县、详细地址", value: "1" },
  { label: "省/地区、市、区/县", value: "2" },
  { label: "省/地区、市", value: "3" },
  { label: "省/地区", value: "4" }
]

export function createFormItem(
  formType: FormItemType,
  name: string,
  width?: string,
  node?: Record<string, any>
): FormItemConfig {
  const newFormItem: FormItemConfig = {
    type: formType,
    prop: `${uuidv4().slice(0, 8)}`,
    name: name,
    placeholder: "",
    desc: "",
    width: getDefaultWidth(formType) || width,
    default: getDefaultValue(formType),
    required: false,
    canSearch: false,
    filterRegex: [], // 正则表达式
    canFilterRegex: false, // 是否可以设置正则
    canShowColumn: false,
    canShowStatistic: false,
    canShowTitle: true,
    tableFormConfig: [],
    fieldLinkage: "",
    tableFieldLinkage: "",
    collectSourceType: "",
    collectValueSource: "",
    valueRangeKey: "",
    association: "",
    lineType: "",
    fixed: false,
    optionKey: "value",
    fieldEditable: "edit",
    icdType: "",
    isLogicForm: false,
    logicFormConfig: {}
  }
  // 设置数据规范字段的prop
  newFormItem.prop = node?.prop || node?.code || node?.collectFieldName || node?.name || newFormItem.prop

  newFormItem.collectSourceType = node?.collectSourceType
  newFormItem.collectValueSource = node?.collectValueSource
  newFormItem.valueRangeKey = node?.valueRangeKey

  // 不同type特有属性
  if ([FormItemType.BaseSelect, FormItemType.BaseCheckbox, FormItemType.BaseRadio].includes(formType)) {
    newFormItem.multiple = FormItemType.BaseCheckbox === formType ? true : false
    newFormItem.options = JSON.parse(JSON.stringify(defaultOptions))
  }

  // 不同type特有属性
  if ([FormItemType.BaseRadio].includes(formType)) {
    newFormItem.titleAndOptionsInRow = false
    newFormItem.showDivider = true
  }

  if ([FormItemType.BaseNumber].includes(formType)) {
    newFormItem.default = ""
    newFormItem.precision = 0
    newFormItem.min = -999999999
    newFormItem.max = 999999999
    newFormItem.allowDecimal = true
  }

  if ([FormItemType.BaseCheckbox, FormItemType.BaseRadio].includes(formType)) {
    newFormItem.arrangement = "horizontal"
  }

  if ([FormItemType.GroupHeadline].includes(formType)) {
    newFormItem.size = "default"
    newFormItem.align = "ALIGN_CENTER"
    newFormItem.fontColor = "#000000"
  }

  // 锚点标题默认100%宽度
  if ([FormItemType.AnchorPoint].includes(formType)) {
    newFormItem.width = "100%"
  }

  if ([FormItemType.DescText].includes(formType)) {
    newFormItem.content = ""
  }

  if ([FormItemType.BloodDonationBarcode].includes(formType)) {
    newFormItem.prop = "barcode"
    newFormItem.required = true
    newFormItem.width = "50%"
  }

  if ([FormItemType.BaseDate].includes(formType)) {
    newFormItem.dateFormat = DateFormatEnum.Date
    newFormItem.default = "custom"
    newFormItem.customValue = ""
  }

  if ([FormItemType.BaseInput, FormItemType.BaseTextarea].includes(formType)) {
    newFormItem.minLength = 0
    newFormItem.maxLength = 999
    newFormItem.lineType = "single"
  }

  if ([FormItemType.IdCardGroup].includes(formType)) {
    newFormItem.tableColumns = setIdCardGroup() as Array<FormItemConfig>
    //添加一条空数据用于预览
    newFormItem.tableData = [{ key: new Date().getTime() }]
  }

  // table默认宽度100%
  if ([FormItemType.Table].includes(formType)) {
    newFormItem.width = "100%"
    newFormItem.operate = true
    newFormItem.tableArrangement = "table"
    newFormItem.column = 1
    newFormItem.tableOperationIcon = false
    newFormItem.tableOperationConfig = tableOperationConfig
    newFormItem.tableGlobalConfiguration = tableGlobalConfiguration
    newFormItem.tableIndex = false
    newFormItem.min = 1
    newFormItem.max = 100
  }

  if ([FormItemType.BaseAddress].includes(formType)) {
    newFormItem.default = ""
    newFormItem.addressSelect = []
    newFormItem.addressAccuracy = ""
    newFormItem.addressAccuracyOption = addressAccuracyOption
  }

  // 初始化校验
  const rule = getCustomRule(newFormItem)
  newFormItem.rule = rule.rule
  newFormItem.ruleMessage = rule.ruleMessage
  return newFormItem
}

// 表单属性，操作按钮
export const formOperationButtons = [
  { defaultName: "提交", rename: "提交", pc: false, mobile: false, isEdit: false },
  { defaultName: "保存", rename: "保存", pc: true, mobile: true, isEdit: false },
  { defaultName: "保存，下一步", rename: "保存，下一步", pc: true, mobile: true, isEdit: false }
]

// 编目操作
export const formOperationConfig = [
  { must: false, checked: false, checkboxLabel: "查看", key: "view", label: "查看" },
  { must: false, checked: false, checkboxLabel: "退回", key: "return", label: "退回" },
  { must: false, checked: false, checkboxLabel: "取消编码", key: "cancel", label: "取消编码" },
  { must: false, checked: false, checkboxLabel: "暂存", key: "save", label: "暂存" },
  { must: false, checked: false, checkboxLabel: "同步", key: "sync", label: "同步" },
  { must: true, checked: true, checkboxLabel: "校验", key: "check", label: "校验" },
  { must: true, checked: true, checkboxLabel: "提交", key: "confirm", label: "提交" }
]

// table 操作项配置
export const tableOperationConfig = [
  { checked: false, checkboxLabel: "新增", key: "add", label: "新增", icon: "Plus" },
  // { checked: false, checkboxLabel: "复制", key: "copy", label: "复制" },
  { checked: false, checkboxLabel: "上移", key: "up", label: "上移", icon: "Top" },
  { checked: false, checkboxLabel: "下移", key: "down", label: "下移", icon: "Bottom" },
  { checked: false, checkboxLabel: "置顶", key: "top", label: "置顶", icon: "Upload" },
  { checked: false, checkboxLabel: "删除", key: "delete", label: "删除", icon: "Delete" }
  // { checked: false, checkboxLabel: "同步", key: "sync", label: "同步至医保首页", icon: "Refresh" },
  // { checked: false, checkboxLabel: "同步", key: "sync-pathology", label: "同步至病理诊断", icon: "RefreshRight" }
]

// 子表单全局配置
export const tableGlobalConfiguration = [
  { checked: false, checkboxLabel: "新增", key: "add", label: "新增", icon: "Plus" },
  { checked: false, checkboxLabel: "同步至医保", key: "sync-medicalInsurance", label: "同步至医保", icon: "Refresh" },
  { checked: false, checkboxLabel: "同步至病理", key: "sync-pathology", label: "同步至病理", icon: "RefreshRight" }
]

// 获取合并的控件列表，主要是针对有身份证控件组
export const getAllWidgets = formConfig => {
  const widgets = [] as Array<Record<string, any>>

  if (formConfig.find(item => item.type === FormItemType.IdCardGroup)) {
    const idCardGroupWidgets = formConfig.find(item => item.type === FormItemType.IdCardGroup)?.tableColumns
    idCardGroupWidgets?.map((item: any) => {
      widgets.push(item)
    })
  }
  formConfig?.map((item: any) => {
    widgets.push(item)
  })
  return widgets
}

// 保存前校验json
export const validateForm = async (classifyDetail: any, formList: Array<Record<string, any>>) => {
  if (classifyDetail?.formConfig?.length === 0) {
    Message.error("请添加控件")
    return false
  }

  //同时满足显示规则和提交规则都不存在时，返回true
  if (classifyDetail?.showRule?.length === 0 && classifyDetail?.submitRule?.length === 0) {
    return true
  }

  //校验显示规则
  const showRuleValidRes = classifyDetail.showRule?.every(item => {
    const { name, rules, actions, conditionCode } = item
    if (!name) {
      Message.error("请填写显示规则名称")
      return false
    }

    if (!conditionCode) {
      Message.error(`请选择${name}的条件关系`)
      return false
    }
    if (!rules || rules.length === 0) {
      Message.error(`请设置完整的${name}显示规则`)
      return false
    }

    if (!actions || actions.length === 0) {
      Message.error(`请设置完整的${name}执行动作`)
      return false
    }

    const widgets = getAllWidgets(classifyDetail.formConfig)

    // 遍历rules里的rule，判断field是否存在于formConfig里
    const ruleRes = rules.every(rule => {
      if (!rule.field || !rule.method || !rule.value) {
        Message.error(`请填写${name}的显示规则条件`)
        return false
      }
      const fieldConfig = widgets?.find(form => form.prop === rule.field)
      if (!fieldConfig) {
        Message.error(`请检查${name}显示规则条件中的控件${rule.field}是否存在`)
        return false
      }
      const selectWidgets = [FormItemType.BaseSelect, FormItemType.BaseRadio, FormItemType.BaseCheckbox]
      if (selectWidgets.includes(fieldConfig?.type)) {
        //判断rule的value是否在options里，value可能是一个数组，也可能是一个字符串
        const options = fieldConfig?.options
        if (Array.isArray(rule.value)) {
          const valueRes = rule.value.every(value => options.some(option => option.value === value))
          if (!valueRes) {
            Message.error(`请检查${name}显示规则条件中的控件${rule.field}的值是否在选项中`)
            return false
          }
        }
        if (typeof rule.value === "string") {
          const valueRes = options.some(option => option.value === rule.value)
          if (!valueRes) {
            Message.error(`请检查${name}显示规则条件中的控件${rule.field}的值是否在选项中`)
            return false
          }
        }
      }

      //如果rule的method是RANGE，需要校验value的格式
      if (rule.method === MethodCodeEnum.RANGE) {
        const { GT, minValue, maxValue, LT } = rule.value
        if (GT === undefined || minValue === undefined || maxValue === undefined || LT === undefined) {
          Message.error(`请设置完整的${name}显示规则`)
          return false
        }
      }
      return true
    })
    if (!ruleRes) {
      return false
    }

    //遍历actions里的action，判断field是否存在于formConfig里
    const actionRes = actions.every(action => {
      if (!action.field || !action.action) {
        Message.error(`请填写${name}的执行动作`)
        return false
      }
      const fieldExist = widgets?.find(form => form.prop === action.field)
      if (!fieldExist) {
        Message.error(`请检查${name}显示规则执行动作中的控件${action.field}是否存在`)
        return false
      }
      return true
    })
    if (!actionRes) {
      return false
    }
    return true
  })

  // 校验提交规则
  const submitRuleValidRes = classifyDetail.submitRule?.every(item => {
    const { name, rules, conditionCode, errorMsg } = item
    if (!name) {
      Message.error("请填写提交规则名称")
      return false
    }

    if (!errorMsg) {
      Message.error(`请填写${name}的错误提示`)
      return false
    }

    if (!conditionCode) {
      Message.error(`请选择${name}的条件关系`)
      return false
    }

    if (!rules || rules.length === 0) {
      Message.error(`请设置完整的${name}提交规则`)
      return false
    }

    // 如果rules里有rule的methodCode是CUSTOM_DATE，需要校验value的格式
    const customDateRule = rules.find(rule => rule.methodCode === MethodCodeEnum.CUSTOM_DATE)
    if (customDateRule) {
      const { minValues, maxValues } = customDateRule.value
      if (minValues?.length === 0 || maxValues?.length === 0) {
        Message.error(`请设置完整的${name}提交规则`)
        return false
      }
    }
    // 如果rules里有rule的methodCode是RANGE，需要校验value的格式
    const rangeRule = rules.find(rule => rule.methodCode === MethodCodeEnum.RANGE)
    if (rangeRule) {
      const { GT, minValue, maxValue, LT } = rangeRule.value
      if (GT === undefined || minValue === undefined || maxValue === undefined || LT === undefined) {
        Message.error(`请设置完整的${name}提交规则`)
        return false
      }
    }

    // 遍历rules,判断field是否存在于formList里
    const rulesRes = rules.every(rule => {
      if (!(rule.field && rule.methodCode && rule.fieldType && rule.value)) {
        Message.error(`请设置完整的${name}提交规则条件`)
        return false
      }

      //校验控件
      const formWsid = rule?.field.split(",")?.[0]
      const fieldExist = formList.some(form => form.wsid === formWsid)
      if (!fieldExist) {
        Message.error(`请检查${name}提交规则中的表单是否存在`)
        return false
      }
      const form = formList.find(form => form.wsid === formWsid)
      if (!form?.fieldJson || form?.fieldJson.length === 0) {
        Message.error(`请检查${name}提交规则中的表单是否存在`)
        return false
      }
      const fieldJson = JSON.parse(form?.fieldJson)
      const formConfig = fieldJson?.formConfig
      const widgets = getAllWidgets(formConfig)

      const widget = widgets?.find(form => form.prop === rule.prop)
      if (!widget) {
        Message.error(`请检查${name}提交规则中的控件${rule.field}是否存在`)
        return false
      }

      //校验值
      if (rule.methodCode === MethodCodeEnum.IN || rule.methodCode === MethodCodeEnum.NO_IN) {
        if (rule.value.length === 0) {
          Message.error(`请设置完整的${name}提交规则`)
          return false
        }
        //判断rule的value是否在options里，value可能是一个数组，也可能是一个字符串
        const options = widget.options
        if (Array.isArray(rule.value)) {
          const valueRes = rule.value.every(value => options.some(option => option.value === value))
          if (!valueRes) {
            Message.error(`请检查${name}提交规则中的控件${rule.field}的值是否在选项中`)
            return false
          }
        }
        if (typeof rule.value === "string") {
          const valueRes = options.some(option => option.value === rule.value)
          if (!valueRes) {
            Message.error(`请检查${name}提交规则中的控件${rule.field}的值是否在选项中`)
            return false
          }
        }
      }

      //如果rule的methodCode是RANGE，需要校验value的格式
      if (rule.methodCode === MethodCodeEnum.RANGE) {
        const { GT, minValue, maxValue, LT } = rule.value
        if (GT === undefined || minValue === undefined || maxValue === undefined || LT === undefined) {
          Message.error(`请设置完整的${name}提交规则`)
          return false
        }
      }

      // 校验日期范围
      if (rule.type === FormItemType.BaseDate) {
        const minValues = rule.value?.minValues
        const maxValues = rule.value?.maxValues
        //如果rule.methodCode是CUSTOM_DATE，需要校验minValues和maxValues的格式
        if (rule.methodCode === MethodCodeEnum.CUSTOM_DATE) {
          if (minValues?.length === 0 || maxValues?.length === 0) {
            Message.error(`请设置完整的${name}提交规则`)
            return false
          }
        }
        if (minValues?.length === 0) {
          Message.error(`请设置完整的${name}提交规则`)
          return false
        }
      }

      return true
    })

    if (!rulesRes) {
      return false
    }

    return true
  })

  return showRuleValidRes && submitRuleValidRes
}

// 将前端传给后端的自定义时间范围格式化
export const formatCustomDate = (rules: Record<string, any>, methodCode) => {
  const minRangeTypeKey = dateRangeType.find(item => item.value === rules?.minValues?.[0])?.key
  const maxRangeTypeKey = dateRangeType.find(item => item.value === rules?.maxValues?.[0])?.key
  const minRange = dateRange.find(item => item.value === rules?.minValues?.[2])?.key
  const maxRange = dateRange.find(item => item.value === rules?.maxValues?.[2])?.key
  const minStr = `${minRangeTypeKey}${rules?.minValues?.[1]}${minRange}_GT`
  const maxStr = `${maxRangeTypeKey}${rules?.maxValues?.[1]}${maxRange}_LT`
  return methodCode === MethodCodeEnum.CUSTOM_DATE ? `${minStr},${maxStr}` : minStr
}

function splitStringByNumber(inputString) {
  // 使用正则表达式匹配数字，并将其作为分隔符
  const parts = inputString.split(/(\d+)/)
  // 过滤掉空字符串
  return parts.filter(Boolean)
}

// 将后端传给前端的校验规则格式化
export const formatSubmitRuleToFront = (rule: Record<string, any>, fieldType: FormItemType) => {
  if (fieldType && fieldType === FormItemType.BaseDate) {
    rule.value = {}
    const minValuesStr = rule?.ruleValue?.split(",")?.[0]?.split("_")?.[0]
    const minValues = splitStringByNumber(minValuesStr)
    if (minValues?.length === 3) {
      const minRangeType = minValues?.[0] ? dateRangeType.find(item => item.key === minValues?.[0])?.value : ""
      rule.value.minValues = [
        minRangeType,
        Number(minValues?.[1]),
        dateRange.find(item => item.key === minValues?.[2])?.value
      ]
    } else if (minValues?.length === 2) {
      rule.value.minValues = ["NOW", Number(minValues?.[0]), dateRange.find(item => item.key === minValues?.[1])?.value]
    }
    if (rule.methodCode === MethodCodeEnum.CUSTOM_DATE) {
      const maxValuesStr = rule?.ruleValue?.split(",")?.[1]?.split("_")?.[0]
      const maxValues = splitStringByNumber(maxValuesStr)

      if (maxValues?.length === 3) {
        const maxRangeType = maxValues?.[0] ? dateRangeType.find(item => item.key === maxValues?.[0])?.value : ""
        rule.value.maxValues = [
          maxRangeType,
          Number(maxValues?.[1]),
          dateRange.find(item => item.key === maxValues?.[2])?.value
        ]
      } else if (maxValues?.length === 2) {
        rule.value.maxValues = [
          "NOW",
          Number(maxValues?.[0]),
          dateRange.find(item => item.key === maxValues?.[1])?.value
        ]
      }
    }
  } else if (rule.methodCode === MethodCodeEnum.RANGE) {
    const minValues = rule?.ruleValue?.split(",")?.[0]
    const maxValues = rule?.ruleValue?.split(",")?.[1]
    rule.value = {
      GT: Number(minValues?.split("_")?.[0]),
      minValue: minValues?.split("_")?.[1],
      maxValue: maxValues?.split("_")?.[1],
      LT: Number(maxValues?.split("_")?.[0])
    }
  } else if (rule.methodCode === MethodCodeEnum.IN || rule.methodCode === MethodCodeEnum.NO_IN) {
    rule.value = rule.ruleValue.split(",")
  } else {
    rule.value = rule.ruleValue
  }
  return rule
}

// 将后端传给前端的显示规则格式化
export const formatShowRuleToFront = (rule: Record<string, any>) => {
  if (rule.method === MethodCodeEnum.RANGE) {
    const minValues = rule?.ruleValue?.split(",")?.[0]
    const maxValues = rule?.ruleValue?.split(",")?.[1]
    rule.value = {
      GT: Number(minValues?.split("_")?.[0]),
      minValue: minValues?.split("_")?.[1],
      maxValue: maxValues?.split("_")?.[1],
      LT: Number(maxValues?.split("_")?.[0])
    }
  } else if (rule.methodCode === MethodCodeEnum.IN || rule.methodCode === MethodCodeEnum.NO_IN) {
    rule.value = rule.ruleValue.split(",")
  } else {
    rule.value = rule.ruleValue
  }
  return rule
}

//格式化校验规则
export const formatSubmitRules = (rules: Array<any>) => {
  rules.forEach(item => {
    item.rules.forEach(rule => {
      rule.ruleValue = rule.value
      rule.field = rule.prop
      if (rule.methodCode === MethodCodeEnum.RANGE) {
        rule.ruleValue =
          rule?.value?.GT + "_" + rule?.value?.minValue + "," + rule?.value?.LT + "_" + rule?.value?.maxValue
      } else if (rule.type === FormItemType.BaseDate) {
        rule.ruleValue = formatCustomDate(rule.value, rule.methodCode)
      } else if (rule.value instanceof Array) {
        rule.ruleValue = rule.value.join(",")
      } else if (Array.isArray(rule.value)) {
        rule.ruleValue = rule.value.join(",")
      }
    })
  })
  return rules
}

export interface RuleItemProps {
  prop: string
  formWsid: string
  field: string
  fieldName: string
  method: string
  methodCode: string
  fieldType: string
  value: string | Record<string, any>
  ruleValue: string
}

export const formFieldshowPermission = {
  editPlaceholder: [
    FormItemType.BaseDate,
    FormItemType.BaseInput,
    FormItemType.BaseTextarea,
    FormItemType.BaseNumber,
    FormItemType.BloodDonationBarcode
  ],
  editLengthRange: [FormItemType.BaseInput, FormItemType.BaseTextarea, FormItemType.BloodDonationBarcode],
  editDateFormat: [FormItemType.BaseDate],
  isRequired: [
    FormItemType.BaseInput,
    FormItemType.BaseTextarea,
    FormItemType.BaseSelect,
    FormItemType.BaseRadio,
    FormItemType.BaseCheckbox,
    FormItemType.BaseNumber,
    FormItemType.UserSign,
    FormItemType.StaffSign,
    FormItemType.BaseDate,
    FormItemType.BloodDonationBarcode,
    FormItemType.InformedConsentFormSign,
    FormItemType.BaseAddress
  ],
  canShowColumn: [
    FormItemType.BaseInput,
    FormItemType.BaseTextarea,
    FormItemType.BaseSelect,
    FormItemType.BaseRadio,
    FormItemType.BaseCheckbox,
    FormItemType.BaseNumber,
    FormItemType.BaseDate,
    FormItemType.BloodDonationBarcode
  ],
  canShowStatistic: [FormItemType.BaseSelect, FormItemType.BaseRadio],
  canSearch: [
    FormItemType.BaseInput,
    FormItemType.BaseTextarea,
    FormItemType.BaseSelect,
    FormItemType.BaseRadio,
    FormItemType.BaseCheckbox,
    FormItemType.BaseNumber,
    FormItemType.BaseDate,
    FormItemType.BloodDonationBarcode
  ],
  hasDefault: [
    FormItemType.BaseInput,
    FormItemType.BaseTextarea,
    FormItemType.BaseSelect,
    FormItemType.BaseRadio,
    FormItemType.BaseCheckbox,
    FormItemType.BaseNumber,
    FormItemType.BaseDate,
    FormItemType.BloodDonationBarcode,
    FormItemType.BaseAddress
  ],
  showOptionConfig: [FormItemType.BaseSelect, FormItemType.BaseCheckbox, FormItemType.BaseRadio]
}

export const getFormFiledShowPermission = drawerConfig => {
  /* ==================== 特殊属性判断 ====================== */
  // 是否显示输入提示
  const editPlaceholder = computed(() => {
    if (!drawerConfig.value) return false
    return formFieldshowPermission.editPlaceholder.includes(drawerConfig.value.type)
  })

  // 是否显示文本限制长度
  const editLengthRange = computed(() => {
    if (!drawerConfig.value) return false
    return formFieldshowPermission.editLengthRange.includes(drawerConfig.value.type)
  })

  // 是否显示日期格式
  const editDateFormat = computed(() => {
    if (!drawerConfig.value) return false
    return formFieldshowPermission.editDateFormat.includes(drawerConfig.value.type)
  })

  // 是否必填
  const isRequired = computed(() => {
    if (!drawerConfig.value) return false
    return formFieldshowPermission.isRequired.includes(drawerConfig.value.type)
  })

  //是否在列表中显示
  const canShowColumn = computed(() => {
    if (!drawerConfig.value) return false
    return formFieldshowPermission.canShowColumn.includes(drawerConfig.value.type)
  })

  // 是否在统计中显示
  const canShowStatistic = computed(() => {
    if (!drawerConfig.value) return false
    return formFieldshowPermission.canShowStatistic.includes(drawerConfig.value.type)
  })

  //是否作为列表查询项
  const canSearch = computed(() => {
    if (!drawerConfig.value) return false
    return formFieldshowPermission.canSearch.includes(drawerConfig.value.type)
  })

  //是否有默认值
  const hasDefault = computed(() => {
    if (!drawerConfig.value) return false
    return formFieldshowPermission.hasDefault.includes(drawerConfig.value.type)
  })

  // 是否展示选项编辑
  const showOptionConfig = computed(() => {
    if (!drawerConfig.value) return false
    return formFieldshowPermission.showOptionConfig.includes(drawerConfig.value.type)
  })

  return {
    editPlaceholder,
    editLengthRange,
    editDateFormat,
    isRequired,
    canShowColumn,
    canShowStatistic,
    canSearch,
    hasDefault,
    showOptionConfig
  }
}

export const collectSourceTypeOptions: Array<Record<string, any>> = [
  { key: "0", value: "METADATA", label: "元数据取值" },
  { key: "1", value: "INTERFACE", label: "接口取值" }
]
// 标准表单字段(数据上报)
export const reportFormItemType = {
  1: "base_input",
  2: "base_number",
  3: "base_date",
  4: "base_number",
  5: "base_select",
  6: "base_select",
  7: "base_address"
}

export const FormItemTypeTitle = {
  base_input: "文本",
  base_date: "日期",
  base_range_date: "日期范围",
  base_radio: "单项选择",
  base_select: "下拉选择",
  base_checkbox: "多想选择",
  base_number: "数字",
  base_address: "地址", //地址

  group_headline: "分组标题",
  group_anchor_point: "锚点标题", //锚点标题
  table: "表格"
  // //身份证组
  // IdCardGroup = "id_card_group",
  // DescText = "desc_text", //说明文字
  // UserSign = "user_sign", //用户签名
  // StaffSign = "staff_sign", //工作人员签名
  // BloodDonationBarcode = "blood_donation_barcode", // 献血条码
  // InformedConsentFormSign = "informed_consent_form_sign", // 知情同意书签名
}

/* =============================== 校验规则 ================================= */
export const nameRequired = [
  { required: true, message: "请输入标题", trigger: "change" },
  { max: 50, message: "标题不能超过50个字", trigger: "change" }
]
