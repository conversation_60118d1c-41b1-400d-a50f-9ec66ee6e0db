import { rowStatusOptions } from "@/configs/options"
import type { TableColumnItem, BaseOptionItem, SearchFormConfigItem, DialogFormConfigItem } from "@/types"
import type { FormRules } from "element-plus"

export const menuId = "/collect/configuration"

export const tabsRouterList = [
  { path: "/collect/configuration/vendor-system", label: "厂商系统", name: "vendor-system" },
  { path: "/collect/configuration/data-origin", label: "数据源", name: "data-origin" },
  { path: "/collect/configuration/ftp-data-origin", label: "FTP数据源", name: "ftp-data-origin" },
  { path: "/collect/configuration/field-mapping", label: "字段映射", name: "field-mapping" }
]

/* ======================== 厂商系统配置 ======================== */

export const systemSearchFormConfig: Array<SearchFormConfigItem> = [
  { type: "input", label: "系统名称", prop: "systemName" },
  { type: "input", label: "厂商名称", prop: "provider" },
  { type: "select", label: "状态", prop: "status", options: rowStatusOptions }
]

export const systemTableColumns: Array<TableColumnItem> = [
  { prop: "appId", label: "APP ID", minWidth: 180 },
  { prop: "systemName", label: "系统名称" },
  { prop: "provider", label: "厂商名称" },
  { prop: "createdDatetime", label: "创建时间" },
  { prop: "modifiedDatetime", label: "修改时间" },
  { prop: "status", label: "状态", minWidth: 100 },
  { prop: "operation", label: "操作", width: 180, fixed: "right" }
]

export const systemDialogFormConfig: Array<DialogFormConfigItem> = [
  {
    type: "input",
    label: "APP ID",
    prop: "appId"
  },
  {
    type: "input",
    label: "系统名称",
    prop: "systemName",
    rules: [{ required: true, message: "请输入系统名称", trigger: "blur" }]
  },
  {
    type: "input",
    label: "厂商名称",
    prop: "provider",
    rules: [{ required: true, message: "请输入厂商名称", trigger: "blur" }]
  }
]

/* ======================== 数据源配置 ======================== */

export const dbTypeOptions: Array<BaseOptionItem> = [
  { label: "MYSQL", value: "MYSQL" },
  { label: "ORACLE", value: "ORACLE" },
  { label: "SQL_SERVER", value: "SQL_SERVER" }
]

export const dataOriginSearchFormConfig: Array<SearchFormConfigItem> = [
  { type: "input", label: "数据源名称", prop: "name" },
  { type: "select", label: "数据源类型", prop: "dbType", options: dbTypeOptions },
  { type: "select", label: "状态", prop: "status", options: rowStatusOptions }
]

export const DataOriginTableColumns: Array<TableColumnItem> = [
  { prop: "name", label: "数据源名称", minWidth: 120 },
  { prop: "dbType", label: "数据源类型", minWidth: 120 },
  { prop: "url", label: "URL", minWidth: 120 },
  { prop: "username", label: "用户名", minWidth: 120 },
  { prop: "password", label: "密码", minWidth: 120 },
  { prop: "dbName", label: "数据库名称", minWidth: 120 },
  { prop: "jdbcUrlParams", label: "连接参数", minWidth: 120 },
  { prop: "timeout", label: "超时时间(秒)", minWidth: 120 },
  { prop: "systemName", label: "厂商系统", minWidth: 120 },
  { prop: "status", label: "状态", minWidth: 100 },
  { prop: "operation", label: "操作", width: 200, fixed: "right" }
]

export const dataOriginFormRules: FormRules = {
  dbType: [{ required: true, message: "请选择数据源类型", trigger: "blur" }],
  systemWsid: [{ required: true, message: "请选择厂商系统", trigger: "blur" }],
  name: [{ required: true, message: "请输入数据源名称", trigger: "blur" }],
  url: [{ required: true, message: "请输入URL", trigger: "blur" }],
  username: [{ required: true, message: "请输入用户名", trigger: "blur" }],
  password: [{ required: true, message: "请输入密码", trigger: "blur" }],
  dbName: [{ required: true, message: "请输入数据库名称", trigger: "blur" }]
}

/* ======================== FTP数据源配置 ======================== */

export const ftpTypeOptions = [
  { label: "FTP", value: "FTP" },
  { label: "SFTP", value: "SFTP" }
]

export const FTPDataOriginSearchFormConfig: Array<SearchFormConfigItem> = [
  { type: "input", label: "数据源名称", prop: "name" },
  { type: "select", label: "数据源类型", prop: "ftpType", options: ftpTypeOptions },
  { type: "select", label: "状态", prop: "status", options: rowStatusOptions }
]

export const FTPDataOriginTableColumns: Array<TableColumnItem> = [
  { prop: "name", label: "数据源名称", minWidth: 120 },
  { prop: "userName", label: "用户名", minWidth: 120 },
  { prop: "password", label: "密码", minWidth: 120 },
  { prop: "hostName", label: "主机名", minWidth: 120 },
  { prop: "systemWsid", label: "厂商系统", minWidth: 120 },
  { prop: "remoteDirectory", label: "远程目录", minWidth: 120 },
  { prop: "ftpType", label: "类型", minWidth: 120 },
  { prop: "status", label: "状态", minWidth: 100 },
  { prop: "operation", label: "操作", width: 200, fixed: "right" }
]

export const FTPDataOriginFormRules: FormRules = {
  name: [{ required: true, message: "请输入数据源名称", trigger: "blur" }],
  ftpType: [{ required: true, message: "请选择数据源类型", trigger: "blur" }],
  systemWsid: [{ required: true, message: "请选择厂商系统", trigger: "blur" }],
  userName: [{ required: true, message: "请输入用户名", trigger: "blur" }],
  password: [{ required: true, message: "请输入密码", trigger: "blur" }],
  hostName: [{ required: true, message: "请输入主机名称", trigger: "blur" }],
  remoteDirectory: [{ required: true, message: "请输入远程目录", trigger: "blur" }]
}

/* ======================== 字段映射配置 ======================== */

export const fieldMappingTableColumns: Array<TableColumnItem> = [
  { prop: "collectSystemName", label: "厂商系统" },
  { prop: "systemElementCode", label: "源端字段" },
  { prop: "documentElementCode", label: "目标字段" },
  { prop: "documentElementName", label: "字段描述" },
  { prop: "operation", label: "操作", width: 140, fixed: "right" }
]

export const fieldMappingFormRules: FormRules = {
  collectSystemWsid: [{ required: true, message: "请选择厂商系统", trigger: "blur" }],
  systemElementCode: [{ required: true, message: "请输入源端字段", trigger: "blur" }],
  documentElementCode: [{ required: true, message: "请选择目标字段", trigger: "blur" }]
}
