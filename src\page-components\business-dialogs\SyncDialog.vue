<template>
  <DialogContainer v-model:visible="visible" title="同步分类" no-footer :close-callback="handleClose">
    <!-- 选择需要同步的分类 -->
    <div v-if="step === 1" class="sync-select">
      <div class="search">
        <el-input v-model="keyword" placeholder="请输入分类名称" clearable></el-input>
      </div>

      <!-- 搜索结果 -->
      <template v-if="keyword">
        <el-checkbox-group v-model="checkedClassifyList" class="classify-list--filtered" @change="handleCheck">
          <el-checkbox
            v-for="classify in filteredClassifyList"
            :key="classify.mrClassCode"
            :label="classify.mrClassCode"
          >
            {{ classify.mrClassName }}
          </el-checkbox>
        </el-checkbox-group>
      </template>

      <!-- 未搜索 -->
      <template v-else>
        <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">
          <span style="font-weight: bold">全选</span>
        </el-checkbox>
        <el-checkbox-group v-model="checkedClassifyList" class="classify-list" @change="handleCheck">
          <el-checkbox v-for="classify in classifyList" :key="classify.mrClassCode" :label="classify.mrClassCode">
            {{ classify.mrClassName }}
          </el-checkbox>
        </el-checkbox-group>
      </template>

      <div class="flex-center" style="margin-top: 10px">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="handleSync">同步</el-button>
      </div>
    </div>

    <!-- 同步结果回显 -->
    <div v-else class="success-table">
      <el-text v-if="!completeFlag" type="danger">关闭弹窗将取消剩余同步</el-text>

      <BaseTable :data="syncTableData" :columns="successTableColumns" border>
        <template #status="{ row }">
          <el-tag v-if="row.status === '同步中'" type="warning">{{ row.status }}</el-tag>
          <el-tag v-else-if="row.status === '成功'" type="success">{{ row.status }}</el-tag>
          <el-tag v-else type="danger">{{ row.status }}</el-tag>
        </template>
      </BaseTable>

      <div v-if="completeFlag" class="flex-center">
        <el-button type="primary" style="margin-top: 10px" @click="handleClose">已完成</el-button>
      </div>
    </div>
  </DialogContainer>
</template>

<script setup lang="ts">
import { ref, watch } from "vue"
import { DialogContainer, BaseTable } from "@/base-components"
import { getClassifyApi, syncClassifyApi } from "../../pages/wesign/medical-record-manage/interface"
import type { CheckboxValueType } from "element-plus"
import { getUsedMrClassApi } from "@/interfaces"
import { toastError } from "@/utils"

const props = defineProps<{
  inpNo: string
  checkedMrClassCode?: Array<string>
}>()

const emits = defineEmits(["success"])

const step = ref(1)

/* ============== 核心操作 ============== */

const loading = ref(false)
const visible = ref(false)
const classifyList = ref<Record<string, any>[]>([]) // 分类列表
const syncTableData = ref<Record<string, any>[]>([]) // 同步的回调结果列表
const completeFlag = ref(false) // 是否完成同步的标记

// 获取分类列表
const getClassify = () => {
  loading.value = true
  // getClassifyApi(props.inpNo)
  getUsedMrClassApi()
    .then(res => {
      classifyList.value = res.data.data
      loading.value = false
    })
    .catch(error => {
      toastError(error)
    })
}

// 确认同步
const handleSync = () => {
  step.value = 2
  const totalSyncCount = checkedClassifyList.value.length // 需要同步的总数

  // 设置回显数据的表单
  syncTableData.value = checkedClassifyList.value.map(code => ({
    mrClassCode: code,
    mrClassName: classifyList.value.find(classify => classify.mrClassCode === code)?.mrClassName,
    status: "同步中",
    fileCount: ""
  }))

  // 循环同步单个分类
  const syncSingleClassify = async index => {
    if (!visible.value) return
    try {
      const syncRes = (await syncClassifyApi(props.inpNo, checkedClassifyList.value[index])).data.data
      const curData = syncTableData.value.find(data => data.mrClassCode === checkedClassifyList.value[index])
      if (!curData) return

      // 判断是否同步成功
      if (!syncRes || syncRes?.length === 0) {
        curData.status = "失败"
      } else if (syncRes[0].isSuccess) {
        curData.status = "成功"
        curData.fileCount = syncRes[0].countFile
      } else if (syncRes[0].isSuccess === false) {
        curData.status = "失败"
      }

      if (index + 1 === totalSyncCount) {
        completeFlag.value = true
        return
      }
      return await syncSingleClassify(index + 1)
    } catch (error: any) {
      toastError(error)
      if (index + 1 === totalSyncCount) {
        completeFlag.value = true
        return
      }
      return await syncSingleClassify(index + 1)
    }
  }

  syncSingleClassify(0)
}

/* ============== 搜索 ============== */

const keyword = ref("") // 搜索关键字
const filteredClassifyList = ref<Record<string, any>[]>([])

watch(keyword, (newVal, oldVal) => {
  if (!newVal || newVal === oldVal) return
  filteredClassifyList.value = classifyList.value.filter(classify => {
    return classify.mrClassName.includes(newVal) || classify.mrClassCode.includes(newVal)
  })
})

/* ============== checkbox ============== */

const checkedClassifyList = ref<string[]>([]) // 选中的分类列表 mrClassCode[]
watch(
  () => props.checkedMrClassCode,
  val => {
    if (val) checkedClassifyList.value = val
  },
  {
    deep: true
  }
)
const checkAll = ref(false) // 是否全选
const isIndeterminate = ref(false) // 全选框

// 切换全选
const handleCheckAllChange = (checkAll: CheckboxValueType) => {
  checkedClassifyList.value = checkAll ? classifyList.value.map(classify => classify.mrClassCode) : []
  isIndeterminate.value = false
}

// 单选
const handleCheck = (checkedList: CheckboxValueType[]) => {
  const checkedCount = checkedList.length
  checkAll.value = checkedCount === classifyList.value.length
  isIndeterminate.value = checkedCount > 0 && checkedCount < classifyList.value.length
}

/* ============== columns ============== */

const successTableColumns = [
  { label: "分类名称", prop: "mrClassName" },
  { label: "状态", prop: "status" },
  { label: "文件数量", prop: "fileCount" }
]

/* ============== 其他 ============== */

const show = () => {
  visible.value = true
  getClassify()
}

const close = () => {
  visible.value = false
}

const handleClose = () => {
  close()
  if (completeFlag.value) emits("success")
  syncTableData.value = []
  checkedClassifyList.value = []
  completeFlag.value = false
  step.value = 1
}

defineExpose({ show, close })
</script>

<style lang="less" scoped>
.sync-select {
  .search {
    padding: 0 10px 20px 10px;
  }

  .classify-list {
    height: 30vh;
    overflow-y: auto;

    &--filtered {
      height: calc(30vh + 32px);
      overflow-y: auto;
    }
  }

  :deep(.el-checkbox) {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: row-reverse;
    margin-right: 0;
    padding: 0 10px;
    border-radius: 6px;

    &:hover {
      color: #409eff;
      background: #d9ecff;
    }
  }
}

.success-table {
  max-height: 30vh;
  overflow-y: scroll;
}
</style>
