import { MenuOperationEnum } from "@/configs"
import type { RouteRecordRaw } from "vue-router"

const printRouter: RouteRecordRaw = {
  path: "/print",
  name: "Print",
  redirect: "/print/statistics",
  meta: {
    title: "病案打印",
    icon: "ri-printer-cloud-line",
    type: "multiple",
    showBreadcrumb: true
  },
  children: [
    {
      path: "/print/statistics",
      meta: {
        title: "打印统计"
      },
      component: () => import("./print-statistics/index.vue")
    },
    {
      path: "/print/application",
      meta: {
        title: "打印申请",
        operations: []
      },
      component: () => import("./print-application/index.vue")
    },
    {
      path: "/print/approval",
      redirect: "/print/approval/waiting-approved",
      meta: {
        title: "打印审批",
        operations: [
          { id: MenuOperationEnum.Approve, name: "审批" },
          { id: MenuOperationEnum.View, name: "详情" },
          { id: MenuOperationEnum.Regenerate, name: "重新生成" }
        ]
      },
      children: [
        {
          path: "/print/approval/waiting-approved",
          meta: {
            title: "待审批"
          },
          component: () => import("./print-approval/WaitingApproved.vue")
        },
        {
          path: "/print/approval/approved",
          meta: {
            title: "已审批"
          },
          component: () => import("./print-approval/PrintApproved.vue")
        }
      ]
    },
    {
      path: "/print/distribution",
      meta: {
        title: "电子分发",
        operations: [
          { id: MenuOperationEnum.Progress, name: "进度" },
          { id: MenuOperationEnum.Export, name: "导出" },
          { id: MenuOperationEnum.Resend, name: "重新发送" }
        ]
      },
      component: () => import("./package-distribution/index.vue")
    },
    {
      path: "/print/order",
      meta: {
        title: "打印订单",
        operations: [
          { id: MenuOperationEnum.Print, name: "打印" },
          { id: MenuOperationEnum.Export, name: "导出" },
          { id: MenuOperationEnum.Printed, name: "打印完成" },
          { id: MenuOperationEnum.Mailed, name: "邮寄完成" }
        ]
      },
      component: () => import("./print-order/index.vue")
    },
    {
      path: "/print/writeoff",
      meta: {
        title: "自取核销",

        operations: [{ id: MenuOperationEnum.Take, name: "领取" }]
      },
      component: () => import("./self-writeoff/index.vue")
    },
    {
      path: "/print/record",
      meta: {
        title: "打印记录",
        operations: [
          { id: MenuOperationEnum.Export, name: "导出" },
          { id: MenuOperationEnum.View, name: "详情" }
        ]
      },
      component: () => import("./print-record/index.vue")
    },
    {
      path: "/print/refund-manage",
      meta: {
        title: "退款管理",
        operations: [
          { id: MenuOperationEnum.Reject, name: "驳回" },
          { id: MenuOperationEnum.Refund, name: "退款" }
        ]
      },
      component: () => import("./refund-manage/index.vue")
    },
    {
      path: "/print/reconciliation-manage",
      redirect: "/print/reconciliation-manage/transaction",
      meta: {
        title: "对账管理",
        operations: [
          { id: MenuOperationEnum.Export, name: "导出" },
          { id: MenuOperationEnum.Reconciliation, name: "对账明细" }
        ]
      },
      children: [
        {
          path: "/print/reconciliation-manage/transaction",
          meta: {
            title: "T+1交易对账"
          },
          component: () => import("./reconciliation-manage/TransactionReconciliation.vue")
        },
        {
          path: "/print/reconciliation-manage/fund-bill",
          meta: {
            title: "资金账单"
          },
          component: () => import("./reconciliation-manage/FundBill.vue")
        }
      ]
    }
  ]
}

export default printRouter
