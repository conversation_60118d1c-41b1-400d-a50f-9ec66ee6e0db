<template>
  <el-popover placement="bottom" width="210" trigger="hover" popper-class="export-popover">
    <template #reference>
      <el-button :icon="Download">导出</el-button>
    </template>

    <PopoverButton
      :center="false"
      :tip="`(当前选中${props.tableInfo?.selectedRows.length}条数据)`"
      @click="handleExportSelected"
    >
      导出选中
    </PopoverButton>
    <el-tooltip effect="dark" content="单次最大数据导出量10000条" placement="bottom">
      <PopoverButton :center="false" :tip="`(共${props.tableInfo?.total}条数据)`" @click="handleExportAll">
        导出全部
      </PopoverButton>
    </el-tooltip>
  </el-popover>

  <DialogContainer v-model:visible="dialogVisible" title="导出" :no-footer="true" :width="550">
    <!-- 导出成功 -->
    <el-result
      v-if="exportStatus === 'success'"
      icon="success"
      title="导出成功"
      sub-title="导出成功，请前往文件夹查看！"
    />

    <!-- 导出失败 -->
    <el-result v-else-if="exportStatus === 'error'" icon="error" title="导出失败" :sub-title="errorMessage" />

    <!-- 导出中 -->
    <el-result v-else title="导出中，请稍后..." sub-title="关闭当前窗口不会影响导出操作。">
      <template #icon><div v-loading="true" style="width: 56px; height: 56px"></div></template>
    </el-result>

    <!-- 导出进度 -->
    <el-progress :percentage="progress" :status="exportStatus === 'error' ? 'exception' : ''"></el-progress>
  </DialogContainer>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue"
import { Download } from "@element-plus/icons-vue"
import { Message, SystemPrompt, downloadFile, extractErrorMsg } from "@/utils"
import { DialogContainer } from "../containers"
import { PopoverButton } from "."

interface ExportButtonProps {
  tableInfo: any // 父页面table的state
  exportApi(params: any): any
  searchFilters: string
  filtersPrefix?: string
  rowIdKey?: string // 属性值 默认wsid
  sorts?: string //排序参数
}

const props = withDefaults(defineProps<ExportButtonProps>(), {
  rowIdKey: "wsid",
  filtersPrefix: "wsids",
  sorts: ""
})

const exportState = reactive({
  loading: false,
  exportAll: false // 是否导出所有
})

// 导出选中
const handleExportSelected = () => {
  console.log(props.tableInfo.selectedRows.length)
  if (!props.tableInfo.selectedRows?.length) return Message.warning("请至少勾选一条数据")
  SystemPrompt("确认导出数据吗").then(() => {
    exportState.exportAll = false
    confirmExport()
  })
}

// 导出所有
const handleExportAll = () => {
  if (!props.tableInfo?.total) {
    return Message.warning("没有数据可以导出")
  }
  SystemPrompt("确认导出全部数据吗").then(() => {
    exportState.exportAll = true
    confirmExport()
  })
}

// 确定导出
const confirmExport = () => {
  if (exportState.loading) return
  exportState.loading = true
  let wsidList = props.tableInfo?.selectedRows?.map(row => row[props.rowIdKey])
  let filters = `${props.filtersPrefix}=${wsidList.join(";")}`
  // 导出全部 id列表传空
  if (exportState.exportAll) {
    filters = `${props.searchFilters}`
  }
  progress.value = 0
  exportStatus.value = "underway"
  dialogVisible.value = true
  mockProgress()
  props
    .exportApi({ filters: filters, sorts: props.sorts })
    .then(res => {
      downloadFile({ fileData: res.data, fileType: "application/vnd.ms-excel", fileName: "导出数据.xlsx" })
      progress.value = 100
      exportStatus.value = "success"
      Message.success("导出数据成功！")
      exportState.loading = false
    })
    .catch(error => {
      Message.error(error.message)
      clearInterval(progressInterval)
      errorMessage.value = extractErrorMsg(error)
      exportStatus.value = "error"
    })
}

/* ============== 导出进度弹窗 ============== */

let progressInterval // 进度计时器
const dialogVisible = ref(false)
const progress = ref(0) // 导出进度
const exportStatus = ref<"underway" | "error" | "success">("underway") // 导出状态
const errorMessage = ref("导出失败，请稍后再试！") // 导出错误信息

// 模拟进度条
const mockProgress = () => {
  progressInterval = setInterval(() => {
    if (progress.value < 90) progress.value += 10
    else if (progress.value < 99) progress.value += 1
    // 到达99还没有导出成功则卡死在99等待导出结果
    else if (progress.value === 99) {
      clearInterval(progressInterval)
    }
  }, 100)
}
</script>
