<template>
  <DialogContainer
    :title="isBatch ? '批量借阅' : '借阅申请'"
    :width="700"
    :visible="visible"
    :cancel-callback="clearState"
    :close-callback="clearState"
    :confirm-callback="confirmBorrow"
    :confirm-loading="borrowState.loading"
  >
    <div class="borrow-dialog-body">
      <div v-if="!isBatch" class="borrow-dialog--left">
        <div class="borrow-desc">
          <div style="margin-bottom: 6px">借阅查看文书分类：</div>
          <div style="margin-bottom: 16px">(多份病案借阅时使用相同配置)</div>
        </div>

        <el-input v-model="treeState.keyword" placeholder="输入搜索内容" :prefix-icon="Search" />

        <div class="tree">
          <div class="tree-title">
            <span>选择文件</span>
            <el-checkbox v-model="treeState.checkAll" size="large" @change="checkAllNodes">全选</el-checkbox>
          </div>
          <div class="tree-content">
            <CommonTree
              ref="borrowTreeRef"
              node-key="wsid"
              :show-checkbox="true"
              :data="borrowTree"
              :filter-node-keyword="treeState.keyword"
              @check-node="getCheckedNodes"
            />
          </div>
        </div>
      </div>

      <div class="borrow-dialog--right borrow-info" :class="isBatch ? 'borrow-info--batch' : ''">
        <div class="borrow-info__total">
          <span>病案信息：共{{ packetList.length }}份</span>
          <span v-if="isBatch" class="batch-borrow-tip">（批量借阅只允许整体借阅患者病案文件）</span>
        </div>
        <div class="borrow-info__list">
          <div v-for="(packet, packetIndex) in packetList" :key="packetIndex">{{ packet }}</div>
        </div>
        <el-form ref="borrowFormRef" label-width="100px" :model="borrowFormState" :rules="borrowFormRules">
          <!-- <el-form-item label="文件数量：">
            <span>{{ borrowState.checkedFileList.length }}</span>
          </el-form-item> -->
          <el-form-item :required="!borrowFormState.long" label="借阅时长：" prop="timeLimit">
            <div class="time-limit">
              <el-input
                v-model="borrowFormState.timeLimit"
                :disabled="borrowFormState.long"
                type="number"
                :min="1"
                @input="getTimeLimit"
              ></el-input>
              <el-select v-model="borrowFormState.timeLimitUnit" :disabled="borrowFormState.long">
                <el-option
                  v-for="item in borrowTimeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-checkbox v-model="borrowFormState.long" label="永久" size="large" />
            </div>
          </el-form-item>
          <el-form-item required label="借阅权限：" prop="allowedPermission">
            <el-checkbox-group v-model="borrowFormState.allowedPermission">
              <el-checkbox label="viewPermission" disabled>查看</el-checkbox>
              <el-checkbox label="printPermission">打印</el-checkbox>
              <el-checkbox label="exportPermission">导出</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item required label="借阅类型：" prop="applyType">
            <el-select v-model="borrowFormState.applyType">
              <el-option
                v-for="option in globalOptionsStore.borrowTypeOptions"
                :key="option.value as string"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="借阅原因：" prop="applyReason">
            <el-input
              v-model="borrowFormState.applyReason"
              class="borrow-textarea"
              row="3"
              type="textarea"
              placeholder=""
            />
          </el-form-item>
        </el-form>
      </div>
    </div>
  </DialogContainer>
</template>

<script setup lang="ts">
import { reactive, ref, computed } from "vue"
import { Search } from "@element-plus/icons-vue"
import { DialogContainer, CommonTree } from "@/base-components"
import { useGlobalOptionsStore } from "@/stores"
import { Message, getFileNodeList, toastError } from "@/utils"
import { borrowTimeOptions } from "../config"
import { handleBorrowApplyApi } from "../interface"

const globalOptionsStore = useGlobalOptionsStore()

const props = defineProps<{
  borrowTree: Array<any>
  packetList: Array<any>
  tableRef?: any
}>()

const visible = ref(false)
const isBatch = computed(() => props.packetList.length > 1)

/*================ tree相关 ==================*/

const borrowTreeRef = ref()

const treeState = reactive({
  checkAll: false,
  keyword: ""
})

// 全选
const checkAllNodes = checkAll => {
  if (!checkAll) {
    borrowState.checkedFileList = []
  }
  borrowTreeRef.value.checkAllNodes(checkAll)
}

// 获取用户勾选的nodes，取出其中的file
const getCheckedNodes = (node, checkedNodes) => {
  const fileNodeList = getFileNodeList(props.borrowTree)
  const checkedNodeList = checkedNodes.filter(__node => __node.type === "FILE")
  // 若勾选的文件和总文件长度相等，则显示全选
  treeState.checkAll = (fileNodeList.length && fileNodeList.length === checkedNodeList.length) ? true : false
  borrowState.checkedFileList = checkedNodeList.map(__node => __node.wsid)
}

/*================ 借阅相关 ==================*/

interface BorrowState {
  checkedFileList: string[] // wsid list
  loading: boolean
}

const borrowState = reactive<BorrowState>({
  checkedFileList: [],
  loading: false
})

// 借阅表单rule
const borrowFormRules = reactive({
  timeLimit: [{ required: true, message: "请输入借阅时长", trigger: "blur" }],
  borrowRoot: [{ required: true, message: "请选择借阅权限", trigger: "blur" }],
  applyType: [{ required: true, message: "请选择借阅类型", trigger: "blur" }]
})
// form ref
const borrowFormRef = ref()
// form state
const borrowFormState = reactive({
  timeLimit: 1,
  timeLimitUnit: "DAY",
  long: false,
  applyType: "",
  allowedPermission: ["viewPermission"], // 操作权限
  applyReason: "" // 借阅原因
})

// 确认借阅
const confirmBorrow = () => {
  if (!borrowFormRef.value) return
  // 批量导出，勾选所有文件
  if (isBatch.value) {
    borrowState.checkedFileList = getFileNodeList(props.borrowTree).map(file => file.wsid)
  }
  // 单个借阅，检查勾选的文件数量
  else if (!borrowState.checkedFileList.length) {
    Message.error("请选择需要借阅的文件！")
    return
  }
  borrowFormRef.value.validate(valid => {
    if (!valid) return
    borrowState.loading = true
    const requestParams = {
      timeLimit: borrowFormState.long ? 99999 : borrowFormState.timeLimit,
      timeLimitUnit: borrowFormState.long ? "FOREVER" : borrowFormState.timeLimitUnit,
      applyType: borrowFormState.applyType,
      applyReason: borrowFormState.applyReason,
      viewPermission: "DISABLE",
      exportPermission: "DISABLE",
      printPermission: "DISABLE",
      documentWsids: borrowState.checkedFileList
    }
    borrowFormState.allowedPermission.forEach(permission => {
      requestParams[permission] = "ENABLE"
    })
    handleBorrowApplyApi(requestParams)
      .then(res => {
        const borrowApplyResult = res.data.data
        if (borrowApplyResult.status) {
          Message.success("借阅成功")
          props.tableRef?.refreshTableData()
          clearState()
        } else {
          Message.error("借阅失败，含有已借阅病案")
        }
        borrowState.loading = false
      })
      .catch(error => {
        toastError(error, "借阅失败")
        borrowState.loading = false
      })
  })
}

// 清空数据并关闭弹窗
const clearState = () => {
  treeState.keyword = ""
  treeState.checkAll = false
  isBatch.value || checkAllNodes(false) // 单个借阅需要清空勾选节点
  borrowFormRef.value.resetFields()
  close()
}

// 获取借阅时长
const getTimeLimit = val => {
  if (val < 1) borrowFormState.timeLimit = 1
  if (val % 1 !== 0) borrowFormState.timeLimit = Math.round(val)
}

const open = () => {
  visible.value = true
}

const close = () => {
  visible.value = false
}

defineExpose({ open, close })
</script>

<style scoped lang="less">
.borrow-dialog--left {
  min-width: 300px;
}

.borrow-dialog-body {
  display: flex;
  justify-content: space-around;
}

.tree {
  margin-top: 10px;
  border: 1px solid #e5e5e5;
}

.tree-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #333;
  background: #e5e5e5;
  padding: 0 10px;
}

.tree-content {
  padding: 0 10px;
  min-height: 30vh;
  max-height: 50vh;
  overflow-y: scroll;
}

.batch-borrow-tip {
  color: #999;
}

.borrow-info--batch {
  width: 90%;
}

.borrow-info__list {
  border: 1px solid #e5e5e5;
  height: 100px;
  padding: 10px;
  margin-top: 10px;
  margin-bottom: 20px;
  overflow-y: scroll;
}

.time-limit {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .el-input {
    width: 40%;
  }

  .el-select {
    width: 30% !important;
  }
}
</style>
