<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchForm
        :form-config="FTPDataOriginSearchFormConfig"
        :form-state="searchFilterForm"
        @query-btn-click="handleQuery"
        @reset-btn-click="handleReset"
      />
    </template>

    <template #table>
      <CommonTable
        ref="tableRef"
        :table-columns="FTPDataOriginTableColumns"
        :request-api="getFtpDataOriginListApi"
        :request-params="searchParams"
      >
        <template #header>
          <AddButton @click="showAddForm">新增</AddButton>
        </template>
        <template #systemWsid="{ row }">
          {{ systemVendorOptions.find(option => option.value === row.systemWsid)?.label || "--" }}
        </template>
        <template #createdDatetime="{ row }">
          {{ formatDatetime(row.createdDatetime) }}
        </template>
        <template #operation="{ row }">
          <TableButton @click="showEditForm(row)">编辑</TableButton>
          <TableButton @click="handleToggle(row)">
            {{ row.status === "ENABLE" ? "禁用" : "启用" }}
          </TableButton>
          <TableButton @click="handleDelete(row)">删除</TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>

  <DialogContainer
    v-model:visible="dialogVisible"
    :title="actionType === 'edit' ? '修改FTP数据源' : '添加FTP数据源'"
    :width="490"
  >
    <el-form
      ref="dialogFormRef"
      label-position="right"
      label-width="120px"
      label-suffix="："
      :model="ftpDataOriginFormState"
      :rules="FTPDataOriginFormRules"
    >
      <CommonInputFormItem v-model="ftpDataOriginFormState.name" label="数据源名称" prop="name" />

      <CommonSelectFormItem
        v-model="ftpDataOriginFormState.systemWsid"
        label="厂商系统"
        prop="systemWsid"
        :options="systemVendorOptions"
      />

      <CommonInputFormItem v-model="ftpDataOriginFormState.userName" label="用户名" prop="userName" />

      <CommonInputFormItem v-model="ftpDataOriginFormState.password" label="密码" prop="password" />

      <CommonInputFormItem v-model="ftpDataOriginFormState.hostName" label="主机名" prop="hostName" />

      <CommonInputFormItem v-model="ftpDataOriginFormState.remoteDirectory" label="远程目录" prop="remoteDirectory" />

      <CommonSelectFormItem
        v-model="ftpDataOriginFormState.ftpType"
        label="类型"
        prop="ftpType"
        :options="ftpTypeOptions"
      />
    </el-form>
    <template #footer>
      <el-button @click="handleClose()">取消</el-button>
      <el-button type="primary" :loading="testLoading" @click="handleTest()">测试数据源</el-button>
      <el-button type="primary" :loading="confirmLoading" @click="handleSave()">保存</el-button>
    </template>
  </DialogContainer>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import {
  PageContainer,
  DialogContainer,
  CommonTable,
  AddButton,
  TableButton,
  CommonSelectFormItem,
  CommonInputFormItem
} from "@/base-components"
import { TabsRouter, SearchForm } from "@/page-components"
import { useTableSearch, useFormSetting, useTableOperation, useCommonOptions } from "@/hooks"
import { SystemPrompt, Message, toastError, formatDatetime } from "@/utils"
import {
  tabsRouterList,
  FTPDataOriginTableColumns,
  FTPDataOriginSearchFormConfig,
  FTPDataOriginFormRules,
  ftpTypeOptions
} from "./config"
import {
  getFtpDataOriginListApi,
  editFtpDataOriginApi,
  addFtpDataOriginApi,
  deleteFtpDataOriginApi,
  modifyFtpDataOriginStatusApi,
  testFtpDataOriginApi,
  getAllSystemVendor
} from "./interface"
import type { IFtpDataOriginFormData } from "./interface"
import type { FormInstance } from "element-plus"

// 获取所有系统厂商
const { options: systemVendorOptions } = useCommonOptions({
  getOptionsApi: getAllSystemVendor,
  labelAlias: "systemName",
  valueAlias: "wsid"
})

/* ======================== 搜索 ======================== */

const searchFilterForm = reactive({
  name: "",
  dbType: "",
  status: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFilterForm)

/* ======================== 表格 ======================== */

const tableRef = ref<InstanceType<typeof CommonTable>>()
const dialogFormRef = ref<FormInstance>()
const dialogVisible = ref(false)
const actionType = ref<"add" | "edit">("add")
const testLoading = ref(false)
const confirmLoading = ref(false)

const initFormData = {
  wsid: "",
  name: "",
  userName: "",
  password: "",
  systemWsid: "",
  hostName: "",
  remoteDirectory: "",
  ftpType: ""
}

const ftpDataOriginFormState = reactive({ ...initFormData })

const { showAddForm, showEditForm } = useFormSetting(ftpDataOriginFormState, initFormData, actionType, dialogVisible)

const { confirmAdd, confirmEdit, confirmToggle, confirmDelete } = useTableOperation<IFtpDataOriginFormData>(
  ftpDataOriginFormState,
  dialogVisible,
  {
    addApi: addFtpDataOriginApi,
    editApi: editFtpDataOriginApi,
    toggleApi: modifyFtpDataOriginStatusApi,
    deleteApi: (wsid: string) => deleteFtpDataOriginApi(wsid)
  },
  tableRef,
  confirmLoading
)

// 关闭编辑弹窗
function handleClose() {
  dialogVisible.value = false
}

// 保存编辑
function handleSave() {
  dialogFormRef.value?.validate(valid => {
    if (!valid) return
    if (actionType.value === "edit") confirmEdit()
    else confirmAdd()
  })
}

// 切换启用&禁用状态
const handleToggle = row => {
  if (row.status === "ENABLE") {
    SystemPrompt(`您确定要禁用${row.name}`).then(() => confirmToggle(row.wsid, "DISABLE"))
  } else {
    SystemPrompt(`是否确定启用${row.name} `).then(() => confirmToggle(row.wsid, "ENABLE"))
  }
}

// 测试数据源
function handleTest() {
  dialogFormRef.value?.validate(valid => {
    if (valid) {
      testLoading.value = true
      testFtpDataOriginApi(ftpDataOriginFormState)
        .then(res => {
          if (res.data?.data) Message.success("测试成功")
          else Message.error("测试失败")
        })
        .catch(err => toastError(err, "测试失败"))
        .finally(() => (testLoading.value = false))
    }
  })
}

// 删除
function handleDelete(row) {
  SystemPrompt(`您确定要删除${row.name} ，删除后关联该数据源所有配置将不能使用`).then(() => confirmDelete(row.wsid))
}
</script>
