<template>
  <el-drawer v-model="visible" title="病案示踪">
    <div v-loading="loading">
      <el-timeline v-if="traceActions.length">
        <el-timeline-item
          v-for="(action, index) in traceActions"
          :key="index"
          :timestamp="action.timestamp"
          placement="top"
        >
          <div class="action">
            <div class="action-title flex-between" :style="{ background: action.color }">
              <div>{{ action.title }}</div>
              <div
                v-if="action.title?.includes('提交')"
                class="action-link flex-end"
                @click="checkSubmitHistory(action)"
              >
                <span>查看详情</span>
                <el-icon><ArrowRight /></el-icon>
              </div>
            </div>
            <div class="action-content">
              <div v-if="action.operDescription" style="margin-bottom: 10px">{{ action.operDescription }}</div>
              <div>操作人：{{ action.userName || "无" }}</div>
              <div v-if="action?.oper === 301 || action.oper === 303 || action.oper === 1002" style="margin-top: 5px">
                {{ action?.oper === 301 ? "备注：" : "原因：" }}
                <span style="color: #aaa">{{ action.remark }}</span>
              </div>
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
      <el-empty v-else></el-empty>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref } from "vue"
import { ArrowRight } from "@element-plus/icons-vue"
import { getRecordTraceInfoApi } from "../../interface"
import { useSystemStore } from "@/stores"
import { formatDatetime, toastError } from "@/utils"
const systemStore = useSystemStore()

const props = defineProps<{
  inpNo: string
}>()

const emits = defineEmits(["checkSubmit"])

const loading = ref(false)
const traceActions = ref<any[]>([])

// 获取示踪记录
const getTraceInfo = async () => {
  loading.value = true
  getRecordTraceInfoApi({ inpNo: props.inpNo })
    .then(res => {
      loading.value = false
      traceActions.value =
        res.data.data.map(log => {
          const curOption = systemStore.operLogEnum.find(option => option.oper === log.oper)
          return {
            ...log,
            timestamp: formatDatetime(log.createdDatetime),
            title: curOption?.des,
            color: curOption?.color
          }
        }) || []
    })
    .catch(error => {
      loading.value = false
      toastError(error)
    })
}

// 查看提交详情
const checkSubmitHistory = action => {
  console.log(`output->action`, action)
  emits("checkSubmit", action)
}

/* =========== options =========== */

// 流程行为选项配置
const actionOptions = [
  { label: "病案采集", value: 0, color: "#5E76CF" },
  { label: "患者出院", value: 1, color: "#5E76CF" },
  { label: "病案回收", value: 2, color: "#5E76CF" },
  { label: "病案提交", value: 3, color: "#5E76CF" },
  { label: "科室医疗质控", value: 4, color: "#5E76CF" },
  { label: "科室护理质控", value: 5, color: "#5E76CF" },
  { label: "交叉医疗质控", value: 6, color: "#5E76CF" },
  { label: "交叉护理质控", value: 7, color: "#5E76CF" },
  { label: "终末质控", value: 8, color: "#5E76CF" },
  { label: "编目", value: 9, color: "#5E76CF" },
  { label: "归档", value: 10, color: "#5E76CF" },
  { label: "封存", value: 11, color: "#5E76CF" },
  { label: "解封", value: 12, color: "#5E76CF" },
  { label: "借阅", value: 13, color: "#5E76CF" },
  { label: "召回", value: 14, color: "#5E76CF" },
  { label: "打印", value: 15, color: "#5E76CF" },
  { label: "导出", value: 16, color: "#5E76CF" },

  { label: "科室医疗质控通过", value: 101, color: "#61B32A" },
  { label: "科室医疗不通过", value: 102, color: "#FA5151" },
  { label: "科室护理质控通过", value: 103, color: "#61B32A" },
  { label: "科室护理不通过", value: 104, color: "#FA5151" },

  { label: "交叉医疗质控通过", value: 105, color: "#61B32A" },
  { label: "交叉医疗不通过", value: 106, color: "#FA5151" },
  { label: "交叉护理质控通过", value: 107, color: "#61B32A" },
  { label: "交叉护理不通过", value: 108, color: "#FA5151" },

  { label: "终末质控通过", value: 109, color: "#61B32A" },
  { label: "终末质控不通过", value: 110, color: "#FA5151" },

  { label: "科室评分", value: 111, color: "#5E76CF" },
  { label: "添加质控记录", value: 112, color: "#5E76CF" },
  { label: "终末评分", value: 113, color: "#5E76CF" },

  { label: "归档审批通过", value: 114, color: "#61B32A" },
  { label: "归档审批不通过", value: 115, color: "#FA5151" },

  { label: "封存审批通过", value: 116, color: "#61B32A" },
  { label: "封存审批不通过", value: 117, color: "#FA5151" },
  { label: "解封审批通过", value: 118, color: "#61B32A" },
  { label: "解封审批不通过", value: 119, color: "#FA5151" },

  { label: "借阅审批通过", value: 120, color: "#61B32A" },
  { label: "借阅审批不通过", value: 121, color: "#FA5151" },
  { label: "借阅授权", value: 122, color: "#5E76CF" },

  { label: "召回审批通过", value: 123, color: "#61B32A" },
  { label: "召回审批不通过", value: 124, color: "#FA5151" },

  { label: "提交退回", value: 125, color: "#FA5151" },
  { label: "编目退回", value: 126, color: "#FA5151" },

  { label: "病案编目", value: 127, color: "#5E76CF" },
  { label: "封存申请", value: 128, color: "#5E76CF" },
  { label: "解封申请", value: 129, color: "#5E76CF" },
  { label: "借阅申请", value: 130, color: "#5E76CF" },
  { label: "召回申请", value: 131, color: "#5E76CF" },
  { label: "病案打印", value: 132, color: "#5E76CF" },
  { label: "病案导出", value: 133, color: "#5E76CF" },

  { label: "未知", value: -99, color: "#FA5151" }
]

/* =========== drawer =========== */

const visible = ref(false)

const show = () => {
  visible.value = true
  getTraceInfo()
}

const close = () => {
  visible.value = false
}

defineExpose({ show, close })
</script>

<style lang="less" scoped>
.action {
  border-radius: 10px;
  background: #fff;
  margin-top: 20px;

  .action-title {
    color: #fff;
    font-weight: bold;
    padding: 8px 20px;
    border-radius: 10px 10px 0 0;

    .action-link {
      cursor: pointer;
    }
  }

  .action-desc {
    color: #6c7385;
    background: #f6f7f9;
    padding: 16px 20px;
    border-radius: 0 0 10px 10px;
  }

  .action-content {
    color: #6c7385;
    background: #f6f7f9;
    padding: 16px 20px;
    border-radius: 0 0 10px 10px;
    word-break: break-all;
    display: flex;
    flex-direction: column;
  }
}
</style>
