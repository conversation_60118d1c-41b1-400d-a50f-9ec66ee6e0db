import type { TableColumnItem, SearchFormConfigItem } from "@/types"

export const borrowBlacklistSearchFormConfig: Array<SearchFormConfigItem> = [
  { type: "input", label: "工号", prop: "queryJobId" },
  { type: "input", label: "姓名", prop: "queryRealName" }
]

export const borrowBlacklistColumns: Array<TableColumnItem> = [
  { prop: "jobId", label: "工号", minWidth: 150 },
  { prop: "realName", label: "姓名", minWidth: 120 },
  { prop: "reason", label: "原因", minWidth: 200 },
  { prop: "creator", label: "操作人", minWidth: 150 },
  { prop: "createdDatetime", label: "操作时间", minWidth: 150 },
  { prop: "operate", label: "操作", minWidth: 120 }
]
