<template>
  <div class="detail">
    <PdfPreviewComponent :src="pdfSrc"></PdfPreviewComponent>
    <!-- <PdfPreview :src="pdfSrc"></PdfPreview> -->
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue"
import { useRoute } from "vue-router"
import { PdfPreviewComponent, PdfPreview } from "@/base-components"
import { exportDynamicFormCatalogApi } from "@/interfaces"

const route = useRoute()

const pdfSrc = ref("")

onMounted(async () => {
  // const res = await exportDynamicFormCatalogApi(route.query?.registerNo as string)
  // pdfSrc.value = URL.createObjectURL(res.data)
  // console.log(`output->res`, URL.createObjectURL(res.data))

  pdfSrc.value = `/api/catalog/outpatient-hqms/${route.query?.registerNo}/first-page-export`
})
</script>

<style lang="scss" scoped>
.detail {
  height: 100%;
}
</style>
