import type { RouteRecordRaw } from "vue-router"

const medicalInsuranceReportRouter: RouteRecordRaw = {
  path: "/medical-insurance-report",
  meta: { title: "医保上报" },
  redirect: "/medical-insurance-report/list",
  children: [
    {
      path: "/medical-insurance-report/list",
      component: () => import("./List.vue"),
      meta: { title: "住院医保数据" }
    },
    {
      path: "/medical-insurance-report/export-record",
      component: () => import("./ExportRecord.vue"),
      meta: { title: "导出记录" }
    }
  ]
}

export default medicalInsuranceReportRouter
