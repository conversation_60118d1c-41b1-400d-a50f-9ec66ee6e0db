import type { RouteRecordRaw } from "vue-router"

const inventoryManagementRouter: RouteRecordRaw = {
  path: "/inventory-management",
  name: "InventoryManagement",
  redirect: "/inventory-management/packing-management",
  meta: {
    title: "库存管理",
    icon: "ri-server-line",
    type: "multiple",
    showBreadcrumb: true
  },
  children: [
    {
      path: "/inventory-management/storage-management",
      meta: {
        title: "库房管理"
      },
      component: () => import("./storage-management/index.vue")
    },
    {
      path: "/inventory-management/packing-management",
      meta: {
        title: "装箱管理"
      },
      component: () => import("./packing-management/index.vue")
    }
  ]
}

export default inventoryManagementRouter
