<template>
  <el-menu
    :default-active="activeMenu"
    text-color="#0A1633"
    active-text-color="#0A1633"
    background-color="#F2F6FF"
    :collapse="isCollapse"
    :router="true"
    unique-opened
    style="border-right: none"
  >
    <template v-for="item in menus" :key="item.path">
      <el-sub-menu v-if="item.children?.length && item.status && !item.hideMenu" :index="item.path">
        <template #title>
          <i :class="item.icon" style="font-size: 16px; margin-right: 12px"></i>
          <span>{{ item.name }}</span>
        </template>
        <template v-for="subRoute in item.children" :key="subRoute.path">
          <el-menu-item v-if="subRoute.status && !subRoute.hideMenu" :index="subRoute.path">
            {{ subRoute.name }}
          </el-menu-item>
        </template>
      </el-sub-menu>

      <el-menu-item v-else-if="item.status" :index="item.path">
        <i :class="item.icon" style="font-size: 16px; margin-right: 12px"></i>
        <span>{{ item.name }}</span>
      </el-menu-item>
    </template>
  </el-menu>
</template>

<script lang="ts" setup>
import { computed } from "vue"
import { useRoute } from "vue-router"
import type { MenuType } from "@/types"
import { adminMenuData, physicalExaminationMenuData, codingMenuData } from "@/pages/wesign/sub-routers"
import { useUserStore, useSystemStore } from "@/stores"

const props = defineProps<{
  isCollapse: boolean
  menuType: MenuType
  activeMenuType: string
}>()

const menus = computed(() => {
  // return systemStore.menus.filter(item => item.level === 0).find(item => item.id === props.activeMenuType)?.children
  return userStore.userMenus.filter(item => item.level === 0).find(item => item.id === props.activeMenuType)?.children
})

const route = useRoute()
const userStore = useUserStore()
const systemStore = useSystemStore()

// 当前激活菜单
const activeMenu = computed(() => {
  const routeNodes = route.fullPath.split("/")
  // 判断当前所在路由
  const matchedSubRouter = systemStore.menus.find(item => route.fullPath.startsWith(item.path))
  const subRouterType = matchedSubRouter?.ext?.type ?? ""
  if (routeNodes.length === 4) routeNodes.pop()
  else if (routeNodes.length === 3 && subRouterType === "single") routeNodes.pop()
  else if (route.fullPath.includes("physical-examination")) routeNodes.pop() // 仅在体检无纸化系统增加的判断，若有bug请进行修复
  return routeNodes.join("/")
})
</script>

<style lang="less" scoped>
:deep(.el-sub-menu) {
  margin: 0 16px !important;
  border-radius: 8px;
}

:deep(.el-menu-item) {
  margin: 0 16px !important;
  border-radius: 8px;
  height: 40px !important;
  line-height: 1 !important;
  margin-bottom: 8px !important;
}

.el-menu--collapse {
  :deep(.el-sub-menu__title) {
    justify-content: center !important;
  }
  :deep(.el-menu-item) {
    justify-content: center !important;
  }
}

:deep(.el-sub-menu__title) {
  height: 40px !important;
  line-height: 1 !important;
  margin-bottom: 8px !important;
  &:hover {
    background-color: rgba(56, 96, 244, 0.15);
    border-radius: 8px;
  }
}

.el-menu.el-menu--inline {
  .el-menu-item {
    // padding-left: 49px !important;
    &.is-active {
      background-color: rgba(56, 96, 244, 1) !important;
      color: #fff !important;
    }
    &:hover {
      background-color: rgba(56, 96, 244, 0.15);
    }
  }
}

.el-menu.el-menu--vertical {
  .el-menu-item {
    &.is-active {
      background-color: rgba(56, 96, 244, 1) !important;
      color: #fff !important;
      .el-sub-menu__title {
        background-color: #344058 !important;
      }
    }
    &:hover {
      background-color: rgba(56, 96, 244, 0.15);
    }
  }
}

.el-menu.el-menu--collapse {
  .el-menu-item,
  .el-sub-menu {
    &.is-active {
      background-color: rgba(56, 96, 244, 1) !important;
      color: #fff !important;
      .el-sub-menu__title {
        background-color: #344058 !important;
      }
    }
  }
}

.el-popper.is-pure.is-light {
  .el-menu--vertical {
    .el-menu--popup {
      .el-menu-item {
        // padding-left: 49px !important;
        &.is-active {
          background-color: rgba(56, 96, 244, 1) !important;
          color: #fff !important;
        }
        &:hover {
          background-color: rgba(56, 96, 244, 0.15);
        }
      }
    }
  }
}
</style>
