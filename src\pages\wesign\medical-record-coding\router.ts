import type { RouteRecordRaw } from "vue-router"
import { MenuOperationEnum } from "@/configs"

const codingRouter: RouteRecordRaw = {
  path: "/coding",
  name: "Coding",
  redirect: "/coding/hospitalization",
  meta: {
    title: "病案编码",
    icon: "ri-draft-line",
    type: "multiple",
    showBreadcrumb: true
  },
  children: [
    // 住院首页（原病案编目）
    {
      path: "/coding/hospitalization",
      redirect: "/coding/hospitalization/pending",
      meta: {
        title: "住院首页",
        operations: [
          { id: MenuOperationEnum.Catalog, name: "编目" },
          { id: MenuOperationEnum.View, name: "查看" },
          { id: MenuOperationEnum.Verify, name: "校验" }
        ]
      },
      children: [
        {
          path: "/coding/hospitalization/pending",
          meta: { title: "待编码" },
          component: () => import("./hospitalization/Pending.vue")
        },
        {
          path: "/coding/hospitalization/finished",
          meta: { title: "已编码" },
          component: () => import("./hospitalization/Finished.vue")
        },
        {
          path: "/coding/hospitalization/returned",
          meta: { title: "已退回" },
          component: () => import("./hospitalization/Returned.vue")
        }
      ]
    },
    // 门急诊首页
    {
      path: "/coding/outpatient-and-emergency",
      redirect: "/coding/outpatient-and-emergency/pending",
      meta: {
        title: "门急诊首页"
      },
      children: [
        {
          path: "/coding/outpatient-and-emergency/pending",
          meta: { title: "待编码" },
          component: () => import("./outpatient-and-emergency/Pending.vue")
        },
        {
          path: "/coding/outpatient-and-emergency/finished",
          meta: { title: "已编码" },
          component: () => import("./outpatient-and-emergency/Finished.vue")
        }
      ]
    },
    {
      path: "/coding/outpatient-and-emergency/detail",
      meta: { title: "门急诊首页详情", hideMenu: true },
      component: () => import("./outpatient-and-emergency/Detail.vue")
    },
    // 编码任务分配
    {
      path: "/coding/task-assignment",
      redirect: "/coding/task-assignment/hospitalization",
      meta: {
        title: "编码任务分配",
        operations: [
          { id: MenuOperationEnum.Assign, name: "分配" },
          { id: MenuOperationEnum.Progress, name: "编目进度查询" }
        ]
      },
      children: [
        {
          path: "/coding/task-assignment/hospitalization",
          meta: { title: "住院" },
          component: () => import("./task-assignment/Hospitalization.vue")
        },
        {
          path: "/coding/task-assignment/outpatient-and-emergency",
          meta: { title: "门急诊" },
          component: () => import("./task-assignment/OutpatientAndEmergency.vue")
        }
      ]
    },
    // 医保结算清单
    {
      path: "/coding/medical-insurance",
      redirect: "/coding/medical-insurance/pending",
      meta: {
        title: "医保结算清单"
      },
      children: [
        {
          path: "/coding/medical-insurance/pending",
          meta: { title: "待编码" },
          component: () => import("./medical-insurance/Pending.vue")
        },
        {
          path: "/coding/medical-insurance/finished",
          meta: { title: "已编码" },
          component: () => import("./medical-insurance/Finished.vue")
        }
      ]
    },
    // 编码编辑页面 点击查看跳转
    {
      path: "/coding/medical-insurance/detail",
      meta: { title: "病案详情", hideMenu: true },
      component: () => import("@/pages/wesign/medical-record-manage/sub-pages/detail.vue")
    }
  ]
}

export default codingRouter
