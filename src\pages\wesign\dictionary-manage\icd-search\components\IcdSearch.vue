<template>
  <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
    <el-form-item v-if="isShowVersionSelect" label="版本">
      <el-select v-model="searchFormState.icdVersionId" placeholder="请选择版本">
        <el-option v-for="item in icdVersionOptions" :key="item.id" :value="item.id" :label="item.name"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item>
      <el-input v-model.trim="searchFormState.inputValue" style="width: 300px">
        <template #prepend>
          <el-select v-model="searchFormState.inputField" style="width: 100px" @change="changeSelectValue">
            <el-option label="术语名称" value="queryName" />
            <el-option label="编码" value="queryCode" />
          </el-select>
        </template>
      </el-input>
    </el-form-item>
  </SearchContainer>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue"
import { SearchContainer } from "@/base-components"
import { getIcdLevelConfigApi } from "@/interfaces"
import { ICDTypeEnum } from "../config"

const props = defineProps({ icdType: { type: String, required: true }, filters: { type: String, required: true } })

const emits = defineEmits(["update:filters"])

const searchFormState = reactive({
  icdVersionId: undefined as number | undefined,
  inputField: "queryName",
  inputValue: ""
})

// 切换过滤属性
function changeSelectValue(nextField: string) {
  searchFormState.inputField = nextField
  searchFormState.inputValue = ""
}

// 根据type判断是否显示版本搜索项
const isShowVersionSelect = computed(() => {
  // return (
  //   props.icdType !== ICDTypeEnum.ICD_ZY &&
  //   props.icdType !== ICDTypeEnum.ICD_RJ &&
  //   props.icdType !== ICDTypeEnum.ICD_MZ &&
  //   props.icdType !== ICDTypeEnum.ICD_SS_DM &&
  //   props.icdType !== ICDTypeEnum.ICD_BZ_DM
  // )
  return true
})

// 触发搜索
function handleQuery() {
  const filterConditions: Array<string> = []
  if (searchFormState.icdVersionId) {
    filterConditions.push(`icdVersionId=${searchFormState.icdVersionId}`)
  }
  if (searchFormState.inputField && searchFormState.inputValue) {
    filterConditions.push(`${searchFormState.inputField}=${searchFormState.inputValue}`)
  }
  emits("update:filters", filterConditions.join(","))
}

// 重置搜索过滤
function handleReset() {
  searchFormState.inputValue = ""
  searchFormState.icdVersionId = icdVersionOptions.value?.[0]?.id
  searchFormState.inputField = "queryName"
  handleQuery()
}

/* ======================== 首次加载获取版本选项 ======================== */

interface IcdVersionOptionItem {
  id: number
  name: string
}

const icdVersionOptions = ref<Array<IcdVersionOptionItem>>([])

onMounted(() => {
  // 展示版本搜索项才请求版本相关信息
  if (isShowVersionSelect.value) {
    getIcdLevelConfigApi({ type: props.icdType }).then(res => {
      const options = res.data?.data ?? []
      icdVersionOptions.value = options
      if (options.length > 0) {
        searchFormState.icdVersionId = options[0].id
        handleQuery()
      }
    })
  }
})

defineExpose({
  searchFormState
})
</script>
