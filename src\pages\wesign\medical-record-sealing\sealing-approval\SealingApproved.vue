<template>
  <PageContainer>
    <!-- 头部搜索 -->
    <TabsRouter :tabs-config="tabsRouterList" />
    <SearchForm
      :form-config="sealingApprovalSearchFormConfig"
      :form-state="searchFilterForm"
      @query-btn-click="handleQuery"
      @reset-btn-click="handleReset"
    />

    <!-- 已审批表格 -->
    <CommonTable
      ref="sealingApprovedTableRef"
      table-id="sealingApprovedTable"
      :table-columns="sealingApprovedColumns"
      :request-params="searchParams"
      :request-api="getSealedApprovalListApi"
    >
      <template #applyReason="{ row }">
        <span>{{ row.type === "解封" ? row.unlockReason : row.lockReason }}</span>
      </template>
      <template #createdDatetime="{ row }">
        <span>{{ formatDatetime(row.createdDatetime) }}</span>
      </template>
      <template #outHospitalDatetime="{ row }">
        <span>{{ formatDatetime(row.outHospitalDatetime) }}</span>
      </template>
      <template #approvalDatetime="{ row }">
        <span>{{ formatDatetime(row.approvalDatetime) }}</span>
      </template>
      <template #operation="{ row }">
        <TableButton
          :disabled="canSign(row)"
          :tooltip="
            hasOperationPermission(menuId, MenuOperationEnum.Approve)
              ? '只有处理失败的审批可以重新签名'
              : '无此操作权限'
          "
          @click="resignSealedRecord(row)"
        >
          重新签名
        </TableButton>
        <TableButton
          :disabled="canAppend(row)"
          :tooltip="
            hasOperationPermission(menuId, MenuOperationEnum.Append) ? '该审批不可追加辅助资料' : '无此操作权限'
          "
          @click="openAppendDialog(row)"
        >
          追加
        </TableButton>
        <TableButton
          v-if="hasOperationPermission(menuId, MenuOperationEnum.View)"
          @click="handleViewApprovalRecord(row)"
        >
          详情
        </TableButton>
      </template>
    </CommonTable>

    <!-- 追加辅助资料弹窗 -->
    <DialogContainer
      v-model:visible="appendDialogState.visible"
      title="追加"
      :width="800"
      :confirm-loading="uploadLoading || appendLoading"
      :confirm-callback="confirmAppend"
    >
      <el-form>
        <el-form-item label="辅助材料追加：" prop="identificationMaterials">
          <div class="identification-materials-container">
            <UploadDocument ref="UploadDocumentRef" v-model:files="appendDialogState.file" />
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div style="display: flex">
          <el-button
            type="primary"
            :loading="UploadDocumentRef?.CameraDialogRef?.cameraLoading"
            @click="() => UploadDocumentRef?.takePhoto()"
          >
            拍摄
          </el-button>
          <div style="flex: 1; min-width: 0px">
            <el-button @click="appendDialogState.visible = false">取消</el-button>
            <el-button type="primary" @click="confirmAppend">确定</el-button>
          </div>
        </div>
      </template>
    </DialogContainer>

    <!-- 大图预览 -->
    <div v-if="biggerImageUrl" class="bigger-image-container">
      <el-icon class="close-bigger-image" @click="biggerImageUrl = ''"><Close /></el-icon>
      <img :src="biggerImageUrl" />
    </div>

    <!-- 封存审批结果详情 -->
    <el-drawer
      v-if="sealingState.visible"
      v-model="sealingState.visible"
      title="审批详情"
      :append-to-body="true"
      :size="600"
      class="seal-approved-detail-drawer"
    >
      <div v-loading="sealingState.loading">
        <div class="info">
          <span class="label">申请人：</span>
          <span>{{ sealingInfo.applicant }}</span>
        </div>
        <div class="info">
          <span class="label">申请人证件号：</span>
          <span>{{ sealingInfo.applicantCertificatesNo }}</span>
        </div>
        <div class="info">
          <span class="label">联系方式：</span>
          <span>{{ sealingInfo.applicantContent }}</span>
        </div>
        <div class="info">
          <span class="label">与患者关系：</span>
          <span>{{ sealingInfo.relationship }}</span>
        </div>
        <div v-if="sealingInfo.type === 'SEALING'" class="info">
          <span class="label">封存时间：</span>
          <span>{{ unlockTypeToStr(sealingInfo?.unlockType) }}</span>
        </div>
        <div v-if="sealingInfo?.unlockType !== 'UNLIMITED_TIME'" class="info">
          <span class="label">封存起止时间：</span>
          <span>
            {{ formatDatetime(sealingInfo.createdDatetime) }}
            <span v-if="sealingInfo?.unlockDatetime">至 {{ formatDatetime(sealingInfo.unlockDatetime) }}</span>
          </span>
        </div>
        <div v-if="sealingInfo.type === 'SEALING'" class="info">
          <span class="label">是否允许追加病历：</span>
          <span>{{ additionalCasesToStr(sealingInfo.additionalCases) }}</span>
        </div>
        <div v-if="sealingInfo.type === 'SEALING'" class="info">
          <span class="label">封存原因：</span>
          <span>{{ sealingInfo.lockReason }}</span>
        </div>
        <div v-else class="info">
          <span class="label">解封原因：</span>
          <span>{{ sealingInfo.unlockReason }}</span>
        </div>
        <div v-if="sealingInfo.type === 'SEALING'" class="tree-doc" style="height: 200px">
          <div class="label">封存文档：</div>
          <div class="tree">
            <CommonTree :data="sealingInfo.documentTree" />
          </div>
        </div>

        <div class="info">
          <span class="label">辅助资料：</span>
          <div class="auxiliary-file-list">
            <div v-for="item in sealingInfo.supportInfo" :key="item.fileWsid" class="auxiliary-file-item">
              <VuePdfEmbed
                class="auxiliary-file"
                :style="{ width: 88 + 'px' }"
                :width="88"
                :source="{
                  ...defaultPdfRenderConfigs,
                  url: item.source
                }"
                :page="1"
                disable-text-layer
              />
              <div class="thumbnail-mask">
                <el-icon @click="previewFile(item)"><ZoomIn /></el-icon>
              </div>
            </div>
          </div>
        </div>

        <!-- 审批详情 -->
        <ApprovalDetail :business-key="sealingInfo.wsid" class="approval-detail" />
      </div>
    </el-drawer>
    <!-- pdf 预览 -->
    <el-dialog
      v-model="pdfPreviewState.visible"
      class="preview-document-dialog-wrapper"
      :show-close="false"
      :fullscreen="true"
    >
      <div style="height: 100vh">
        <span class="return-back" @click="closePdfPreview">
          <i class="ri-arrow-left-line"></i>
          退出
        </span>
        <div ref="pdfPreviewRef" class="pdf-preview-content"></div>
      </div>
    </el-dialog>
  </PageContainer>
</template>

<script setup lang="ts">
import { computed, nextTick, reactive, ref } from "vue"
import PdfObject from "pdfobject"
import VuePdfEmbed from "vue-pdf-embed"
import { ZoomIn } from "@element-plus/icons-vue"
import { CommonTable, PdfPreview, CommonTree, TableButton, PageContainer, DialogContainer } from "@/base-components"
import { TabsRouter, SearchForm, ApprovalDetail } from "@/page-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { useUserStore } from "@/stores"
import { formatDatetime, toastError, Message, formatRecordTree, defaultPdfRenderConfigs } from "@/utils"
import UploadDocument from "../components/upload-document.vue"
import { UploadSupportInfo } from "../config"
import {
  getSealedApprovalListApi,
  appendSealingFile,
  getSealingDetailApi,
  resignSealedRecordApi,
  getSealSupportFileApi
} from "../interface"
import {
  tabsRouterList,
  sealingApprovedColumns,
  sealingApprovalSearchFormConfig,
  menuId,
  SealingStatusEnum,
  additionalCasesToStr,
  unlockTypeToStr
} from "./config"

const { hasOperationPermission } = useUserStore()

const UploadDocumentRef = ref()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFilterForm = reactive({
  patientFilterProp: "patientName",
  patientFilterValue: "",
  applicant: "",
  type: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFilterForm)
/* ======================== 已审批表格 ======================== */
const sealingApprovedTableRef = ref()

// 重新签名
function resignSealedRecord(row) {
  resignSealedRecordApi(row.id)
    .then(res => {
      if (sealingApprovedTableRef.value) sealingApprovedTableRef.value.refreshTableData()
      Message.success("重新签名成功")
    })
    .catch(err => {
      toastError(err, "重新签名失败")
    })
}

/* ======================== 追加辅助资料弹窗 ======================== */
const appendDialogState = reactive({
  visible: false,
  file: [] as Array<UploadSupportInfo>,
  row: null as null | Record<string, any>
})

function canAppend(row) {
  return !(
    row.sealingStatus === SealingStatusEnum.SEALED &&
    hasOperationPermission(menuId, MenuOperationEnum.Append) &&
    row.type === "封存"
  )
}

function canSign(row) {
  return !(row.fileStatus === "FAIL" && hasOperationPermission(menuId, MenuOperationEnum.Approve))
}

const biggerImageUrl = ref("")

// 打开弹窗
function openAppendDialog(row) {
  appendDialogState.file = []
  appendDialogState.row = row
  appendDialogState.visible = true
}

// 是否有文件正在上传
const uploadLoading = computed(() => {
  const haveUpload = appendDialogState.file.findIndex(item => item.status === "uploading")
  return haveUpload !== -1
})

const appendLoading = ref(false)
//确认追加辅助资料
function confirmAppend() {
  const params = {
    id: appendDialogState.row?.id,
    file: [] as Array<Record<string, any>>
  }
  const files = [] as Array<Record<string, any>>
  appendDialogState.file.forEach(item => {
    if (item.status === "success") files.push({ fileWsid: item.wsid, fileName: item.name, filePage: item.filePage })
  })
  params.file = files
  appendLoading.value = true
  appendSealingFile(params)
    .then(res => {
      appendDialogState.visible = false
      Message.success("追加成功")
    })
    .catch(err => {
      console.log(err)
      toastError(err, "追加失败")
    })
    .finally(() => {
      appendLoading.value = false
    })
}

/* ======================== 审批详情 ======================== */

const sealingInfo = ref<Record<string, any>>({})

const sealingState = reactive({
  visible: false,
  loading: false
})

const handleViewApprovalRecord = row => {
  sealingState.loading = true
  getSealingDetailApi({ id: row.id })
    .then(async res => {
      sealingInfo.value = res.data?.data
      sealingInfo.value.documentTree = formatRecordTree(res.data.data.treeInfo)
      if (sealingInfo.value?.supportInfo && sealingInfo.value?.supportInfo?.length > 0) {
        await Promise.all(
          sealingInfo.value.supportInfo?.map((item: Record<string, any>) => {
            return getSealSupportFileApi({
              "file-wsid": item.fileWsid
            }).then(res => {
              const source = URL.createObjectURL(new Blob([res.data], { type: "application/pdf" }))
              item.source = source
            })
          })
        )
      }
      sealingState.visible = true
    })
    .catch(err => {
      toastError(err)
    })
    .finally(() => {
      sealingState.loading = false
    })
}

// 预览文件
function previewFile(file) {
  pdfPreviewState.visible = true
  nextTick(() => {
    PdfObject.embed(file.source, pdfPreviewRef.value)
  })
}
// pdf 预览
const pdfPreviewState = reactive({
  visible: false,
  source: ""
})

const pdfPreviewRef = ref()

// 关闭pdf预览
const closePdfPreview = () => {
  pdfPreviewState.visible = false
}
</script>

<style lang="less" scoped>
:deep(.el-upload) {
  width: 88px;
  height: 124px;
}
:deep(.el-upload-list__item) {
  width: 88px;
  height: 124px;
}

.info {
  margin-bottom: 20px;
  display: flex;
  font-size: 14px;
  .label {
    display: inline-block;
    width: 130px;
    text-align: right;
    color: rgba(10, 22, 51, 0.6);
  }
  .auxiliary-file-list {
    display: flex;
    column-gap: 5px;
    row-gap: 5px;
    flex: 1;
    min-width: 0px;
    flex-wrap: wrap;
    .auxiliary-file {
      width: 88px;
      cursor: pointer;
    }
    .auxiliary-file-item {
      position: relative;
      cursor: pointer;
      height: 124px;
      &:hover .thumbnail-mask {
        display: block;
      }
    }
    .thumbnail-mask {
      position: absolute;
      width: 88px;
      height: 100%;
      left: 0;
      top: 0;
      background: rgba(0, 0, 0, 0.2);
      z-index: 1;

      :deep(.el-icon) {
        color: #fff;
        font-size: 24px;
        position: absolute;
        top: calc(50% - 12px);
        left: calc(50% - 12px);
      }
      display: none;
    }
  }
}
.tree-doc {
  display: flex;
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
  .label {
    width: 130px;
    text-align: right;
    color: rgba(10, 22, 51, 0.6);
    font-size: 14px;
  }
  .tree {
    width: calc(100% - 135px);
    padding: 16px;
    background-color: rgb(248 249 252);
    border: 1px solid rgb(225 226 230);
    border-radius: 4px;
    box-sizing: border-box;
  }
  .input {
    width: calc(100% - 135px);
  }
}
.identification-materials-container {
  display: flex;
  flex-direction: column;

  .upload-file {
    position: relative;
    width: 100%;
    .image-type {
      padding: 5px;
      height: 100%;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: 100%;
        object-fit: contain;
      }
    }

    .upload-hover {
      position: absolute;
      width: 88px;
      height: 100%;
      top: 0px;
      left: 0px;
      opacity: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      column-gap: 5px;
      color: #fff;
      font-size: 24px;
      &:hover {
        opacity: 1;
        background: rgba(0, 0, 0, 0.2);
      }
      .el-icon {
        cursor: pointer;
      }
    }

    .upload-status {
      position: absolute;
      top: 5px;
      right: 5px;
      font-size: 14px;
    }
  }
}

.bigger-image-container {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  .close-bigger-image {
    position: absolute;
    right: 20px;
    top: 20px;
    font-size: 28px;
    color: #fff;
    cursor: pointer;
  }
  img {
    width: 65%;
    height: 65%;
    object-fit: contain;
  }
}
</style>

<style lang="less">
.seal-approved-detail-drawer {
  .el-drawer__header {
    margin-bottom: 0px;
    padding: 16px;
    color: #0a1633;
    font-weight: bold;
  }
}
.preview-document-dialog-wrapper {
  .el-dialog__header {
    padding: 0;
  }
  .el-dialog__body {
    padding: 0;
  }
  .pdf-preview-header {
    justify-content: space-between !important;
  }
  .return-back {
    cursor: pointer;
    font-size: 14px;
    display: inline-block;
    padding-left: 4px;
    height: 32px;
    line-height: 32px;
  }
  .pdf-preview-content {
    height: calc(100% - 36px);
  }
}
</style>
