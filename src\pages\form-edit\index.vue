<template>
  <Spin :spinning="systemStore.pageLoading" :tip="loadingText">
    <div class="form-config-wrapper">
      <div class="header-actions flex-between">
        <div class="left-header flex-center">
          <el-icon @click="goBack"><ArrowLeft /></el-icon>
          <div v-if="!isEditFormName" class="form-name" @click="isEditFormName = true">
            <span>{{ classifyDetail.name || "表单名称" }}</span>
            <el-icon><Edit /></el-icon>
          </div>
          <el-input
            v-if="isEditFormName"
            v-model="classifyDetail.name"
            :suffix-icon="Edit"
            placeholder="表单名称"
            @blur="isEditFormName = false"
          />
        </div>
        <div>
          <el-button class="save-btn" @click="goBack">退出</el-button>
          <el-button class="save-btn" type="primary" @click="saveForm">保存</el-button>
        </div>
      </div>
      <div class="form-config-container">
        <!-- 控件选择区域 -->
        <WidgetSelectPanel
          v-if="activeFormWsid"
          :form-config="classifyDetail.formConfig"
          :app-type-code="classifyDetail.appTypeCode"
          class="panel"
          @is-moving="value => (isMoving = value)"
          @add-form-item="addFormItem"
        />

        <!-- 表单区域 -->
        <el-form class="form-area" label-position="top">
          <!-- 表单编辑区 -->
          <VueDraggable
            ref="el"
            v-model="classifyDetail.formConfig"
            class="form-edit-area"
            ghost-class="ghost"
            :group="{ name: 'people', pull: 'clone', put: true }"
            filter=".el-empty"
            :animation="500"
            :delay="0"
            :empty-insert-threshold="10"
            :clone="clone"
            @sort="move"
          >
            <template v-if="classifyDetail.formConfig.length || isMoving">
              <DraggableFormItem
                v-for="(item, index) in classifyDetail.formConfig"
                :key="item?.prop"
                :prop="item?.prop"
                :config="item"
                :index="index"
                :edit-card="editCard"
                :copy-card="copyCard"
                :delete-card="deleteCard"
                :active-config-index="activeConfigIndex"
                :active-table-config-index="activeTableConfigIndex"
                :active-table-config="activeTableConfig"
                @edit="editTableCard"
                @delete="deleteTableCard"
              />
            </template>
            <el-empty v-else style="width: 100%"></el-empty>
          </VueDraggable>
        </el-form>

        <el-empty v-if="!activeFormWsid" style="width: 100%"></el-empty>

        <!-- 控件属性编辑抽屉 -->
        <WidgetPropsDrawer
          v-model:visible="propsPanelVisible"
          v-model:classify-detail="classifyDetail"
          :config="activeConfig"
          :form-configs="classifyDetail.formConfig"
          :form-operation-config="classifyDetail.actionButtons"
        />
      </div>
    </div>

    <!-- 表单预览弹窗 -->
    <!-- <FormPreviewDialog v-model:visible="previewVisible" :save-form="saveForm" :form-config="classifyDetail.formConfig" /> -->

    <!-- 表单属性配置 -->
    <!-- <FormProperty
      v-model:visible="propertyPanelVisible"
      v-model:classifyDetail="classifyDetail"
      :form-configs="classifyDetail.formConfig"
    /> -->
  </Spin>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, watch, provide, computed, onBeforeUnmount } from "vue"
import { useRouter, useRoute } from "vue-router"
import { ElMessageBox } from "element-plus"
import { Spin } from "ant-design-vue"
import { flatten } from "lodash"
import { flow, map, compact, cloneDeep, prop, maxBy } from "lodash/fp"
import { v4 as uuidv4 } from "uuid"
import { VueDraggable } from "vue-draggable-plus"
import { Edit } from "@element-plus/icons-vue"
//   import { getAllFormsApi, deleteFormApi, saveFormApi, editFormApi, renameFormApi } from "@/interfaces"
//   import { routerConfig } from "../config"
import DraggableFormItem from "./components/draggable-form-item.vue"
// import FormPreviewDialog from "./components/form-preview-dialog.vue"
import {
  formOperationButtons,
  formOperationConfig,
  validateForm,
  formatSubmitRules,
  formatSubmitRuleToFront,
  createFormItem,
  tableGlobalConfiguration,
  tableOperationConfig
} from "./config"
import WidgetPropsDrawer from "./widget-props-drawer.vue"
import WidgetSelectPanel from "./widget-select-panel.vue"
import type { FormItemConfig } from "@/configs"
import { FormItemType } from "@/configs"
import { editFormConfigsApi, getFormConfigsDetailApi } from "@/interfaces"
import { useSystemStore } from "@/stores"
import { Message, SystemPrompt, toastError } from "@/utils"

const router = useRouter()
const route = useRoute()
//   import { TabsRouter } from "@/components"
//   import FormProperty from "./form-property.vue"

const systemStore = useSystemStore()

let loadingTimer

const loadingText = ref("")

watch(
  () => systemStore.pageLoading,
  val => {
    if (!val) {
      clearInterval(loadingTimer)
      return
    }
    loadingText.value = systemStore.loadingText
    loadingTimer = setInterval(() => {
      loadingText.value = loadingText.value.endsWith("...") ? loadingText.value.slice(0, -3) : loadingText.value + "."
    }, 500)
  }
)

onBeforeUnmount(() => clearInterval(loadingTimer))

const previewVisible = ref(false)
const isMoving = ref(false)

const initialFormConfig = {
  formConfig: [] as Array<FormItemConfig>,
  actionButtons: cloneDeep(formOperationConfig) as Array<any>, // 操作按钮
  showRule: [] as Array<any>, // 显示规则
  submitRule: [] as Array<any>, // 提交规则
  appScenario: "",
  appTypeCode: "",
  appTypeName: "",
  name: ""
}

// 表单配置
const classifyDetail = reactive(cloneDeep(initialFormConfig))

// 缓存的表单配置
const cacheFormConfig = reactive(cloneDeep(initialFormConfig))

// 比较是否有改动
const isSaveFn = () => {
  return JSON.stringify(classifyDetail) === JSON.stringify(cacheFormConfig)
}

provide("formConfig", classifyDetail.formConfig)

onMounted(() => {
  document.body.ondrop = function (event) {
    event.preventDefault()
    event.stopPropagation()
  }
  //   getAllForms()
  getFormConfigDetail()
})

const isEditFormName = ref(false)

/*================================= 表单配置 =================================*/
watch(
  () => classifyDetail,
  val => {
    // console.log("表单配置", val)
  },
  { deep: true }
)
//当前编辑表单项
const activeConfigIndex = ref(-1)

// 编辑抽屉
const propsPanelVisible = ref(false)

// 寻找当前的表单项
const findCard = (prop: string, callback?: any) => {
  const card = classifyDetail.formConfig?.filter(item => item.prop === prop)?.[0]
  const res = {
    card,
    index: classifyDetail.formConfig.indexOf(card)
  }
  if (callback) {
    callback(res)
  }
  return res
}

// 编辑表单项属性
function editCard(prop: string) {
  console.log(`output->prop`, prop)
  const formItem = classifyDetail.formConfig.find(item => item.prop === prop)
  if (!formItem) return
  if (formItem?.type === FormItemType.IdCardGroup) {
    return Message.warning("身份证组件不支持编辑")
  }

  if (formItem?.type === FormItemType.BloodDonationBarcode) {
    return Message.warning("献血条码组件不支持编辑")
  }

  const { index } = findCard(prop)
  if (index <= -1) return
  propsPanelVisible.value = true
  activeConfigIndex.value = index
  activeTableConfigIndex.value = -1
  activeConfig.value = classifyDetail.formConfig[activeConfigIndex.value] ?? null
}

// 删除表单项
function deleteCard(prop: string) {
  SystemPrompt("是否确认删除该字段").then(() => {
    const { index } = findCard(prop)
    if (index > -1) {
      classifyDetail.formConfig.splice(index, 1)
      activeConfig.value = classifyDetail.formConfig[0] ?? null
      console.log(`output->activeConfig.value`, activeConfig.value)
    }
  })
}

// 复制表单项
function copyCard(config: FormItemConfig) {
  const newConfig = cloneDeep(config)
  newConfig.prop = `${uuidv4().slice(0, 8)}`
  // 找到被复制的表单项
  const { index } = findCard(config.prop)
  // 插入到当前表单项的下一个
  classifyDetail.formConfig.splice(index + 1, 0, newConfig)
}

//重置classifyDetail
const clearFormState = () => {
  const resetConfig = (config, defaultButtons) => {
    Object.keys(config).forEach(key => {
      config[key] = key === "actionButtons" ? cloneDeep(defaultButtons) : []
    })
  }

  resetConfig(classifyDetail, formOperationButtons)
  resetConfig(cacheFormConfig, formOperationButtons)
}

/*========================================表单属性========================================*/
const propertyPanelVisible = ref(false)

const openPropertyPanel = () => {
  propertyPanelVisible.value = true
}
/*=============================表单列表=============================*/
const formList = ref<Array<any>>([])

provide("formList", formList)

const activeFormWsid = ref("aaa")

// 获取当前表单的配置
const getFormConfigDetail = async () => {
  try {
    systemStore.showLoading("加载中")
    const resData = (await getFormConfigsDetailApi(route.query?.formWsid as string))?.data?.data

    for (const key in classifyDetail) {
      classifyDetail[key] = resData[key] || classifyDetail[key]
    }
    classifyDetail.formConfig = JSON.parse(resData.formContent || "[]")?.formConfig ?? []
    classifyDetail.actionButtons =
      JSON.parse(resData.formContent || "[]")?.actionButtons ?? classifyDetail.actionButtons
    console.log(`output->classifyDetail`, classifyDetail)
    // classifyDetail.formConfig = classifyDetail.formConfig.filter(item => item !== null)
    classifyDetail.formConfig.forEach(item => {
      // 初始化新增配置字段 通过代码添加
      if (item.type === "table") {
        // item.tableGlobalConfiguration = cloneDeep(tableGlobalConfiguration)
        // item.tableOperationConfig = cloneDeep(tableOperationConfig)
        // item.min = 1
        // item.max = 20
      }
      if (item.type === "base_select") {
        // item.logicFormConfig = {}
      }
      // item.isLogicForm = false
      // item.mapField = ""
    })
    systemStore.hideLoading()
  } catch (err: any) {
    console.error(err)
    toastError(err)
    systemStore.hideLoading()
  }
}

//保存表单 isUpdateFormList默认是true,如果是切换表单时保存表单不需要刷新表单列表
const saveForm = async () => {
  try {
    const isValid = await validateForm(classifyDetail, formList.value)
    if (!isValid) return

    systemStore.showLoading("保存中")
    const activeForm = formList.value.find(item => item.wsid === activeFormWsid.value)
    const fieldJsonObj = {
      formConfig: classifyDetail.formConfig,
      actionButtons: classifyDetail.actionButtons
    }

    const submitRulesToEnd = formatSubmitRules(classifyDetail?.submitRule).map(item => ({
      ...item,
      rules: item.rules.map(i => {
        const { formWsid, value, ...rest } = i
        return {
          ...rest,
          ...(formWsid === activeFormWsid.value ? {} : { formWsid })
        }
      })
    }))
    console.log(`output->fieldJsonObj`, fieldJsonObj)

    await editFormConfigsApi({
      catalogFormConfigWsid: route.query?.formWsid as string,
      name: classifyDetail.name,
      formContent: JSON.stringify(fieldJsonObj)
    })

    Message.success("保存成功")

    previewVisible.value = false
    // activeFormWsid.value = null

    await getFormConfigDetail()
  } catch (error) {
    console.log(`output->error`, error)
    Message.error("保存失败，请重试")
  } finally {
    systemStore.hideLoading()
  }
}

function clone(element) {
  console.log(`output->element`, element)
  //限制 类型表单 拖进table
  if (element.type !== FormItemType.Table && element.type !== FormItemType.GroupHeadline) {
    const newFormItem = createFormItem(element.type, element.name, "", element)
    return newFormItem
  }
}

// 拖进table
const move = e => {
  if (e.to.className.includes("table-form-edit-area")) {
    if (e.data.type !== FormItemType.Table && e.data.type !== FormItemType.GroupHeadline) {
      const { index } = findCard(e.data.prop)
      if (index > -1) {
        classifyDetail.formConfig.splice(index, 1)
      }
    }
  }
}

const goBack = () => {
  location.href = `/system-manage/form-config`
}

const activeConfig = ref({} as any)

// 添加数据规范
const addFormItem = data => {
  classifyDetail.formConfig.push(data)
}
const activeTableConfigIndex = ref(-1)
const activeTableConfig = ref({} as any)

const editTableCard = (data, config) => {
  console.log(`output->data`, data)
  console.log(`output->config`, config)
  activeTableConfigIndex.value = config.tableFormConfig.findIndex(item => item.prop === data.prop)
  activeConfigIndex.value = -1
  activeTableConfig.value = config
  activeConfig.value = data
}
const deleteTableCard = data => {
  activeConfig.value = null
}
</script>

<style lang="less" scoped>
.form-config-wrapper {
  height: 100%;
  .header-actions {
    background-color: #fff;
    width: calc(100% - 40px);
    height: 60px;
    // border-top: 1px solid #ccc;
    padding: 0 20px;
    margin-bottom: 1px;

    .left-header {
      font-weight: 500;
      font-size: 14px;
      color: #666666;
      height: 100%;
      cursor: pointer;
      i {
        font-size: var(--custom-larger-font-size);
      }
      .list-total {
        height: 100%;
        background-color: #ccc;
        padding: 0 12px;
        display: flex;
        align-items: center;
        gap: 4px;
      }
      .add-form {
        border: 1px solid #ccc;
        height: 100%;
        display: flex;
        align-items: center;
        padding: 0 8px;
        gap: 4px;
        cursor: pointer;
      }
    }
  }
  .form-config-container {
    flex: 1;
    min-height: 0px;
    display: flex;
    height: calc(100vh - 61px);
    // padding: 0 20px;
    background-color: #fff;
    .form-list {
      width: 220px;
      min-width: 220px;
      height: 100%;
      border-right: 1px solid #ccc;
      box-sizing: border-box;
      overflow-y: auto;
      .form-item {
        width: 100%;
        padding: 8px 4px;
        box-sizing: border-box;
        cursor: pointer;
        border-bottom: 1px solid #ccc;
        &:hover {
          background-color: #f8f9fc;
        }
        :deep(.page-side-popover) {
          min-width: 20px;
          padding: 4pxd;
        }
      }
      .file-action {
        font-size: var(--custom-large-font-size);
        cursor: pointer;
        z-index: 2;
      }
    }
    .panel {
      height: 100%;
      overflow-y: hidden;
      overflow-x: hidden;
      // padding-right: 10px;
    }

    .form-area {
      flex: 1;
      min-height: 0px;
      overflow-y: auto;
      padding: 27px 39px;
      // border-left: 1px solid #ccc;
      background: #eff2f7;

      .form-edit-area {
        // border: 1px solid #ccc;
        width: calc(100% - 2px);
        min-height: 400px;
        background-color: #fff;
        border-radius: 4px;
        display: flex;
        // justify-content: space-between;
        flex-wrap: wrap;
        padding-bottom: 100px;
        box-sizing: border-box;
        align-items: flex-start;
        padding: 19px 12px;
      }

      .el-empty {
        height: 100%;
      }
    }
  }
}

.form-name {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 500;
  font-size: 14px;
  margin-left: 11px;
  color: #666666;
  span {
    margin-right: 8px;
  }
}
</style>
