<!-- 修改密码弹窗 -->
<template>
  <DialogContainer v-model:visible="visible" title="修改登录密码" :width="450" :confirm-callback="confirmModify">
    <el-form ref="ruleFormRef" label-position="top" :model="ruleForm" :rules="rules" class="form">
      <el-form-item label="旧密码" required prop="oldPassword">
        <el-input v-model="ruleForm.oldPassword" type="password" placeholder="请输入当前登录密码" show-password />
      </el-form-item>
      <el-form-item label="新密码" required prop="newPassword">
        <el-input
          v-model="ruleForm.newPassword"
          type="password"
          placeholder="请输入新密码（至少6位数字、大小写字母和字符的组合）"
          show-password
        />
      </el-form-item>
      <el-form-item label="确认新密码" required prop="confirmPassword">
        <el-input
          v-model="ruleForm.confirmPassword"
          type="password"
          placeholder="请再次确认您的登录密码"
          show-password
        />
      </el-form-item>
    </el-form>
  </DialogContainer>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue"
import { DialogContainer } from "@/base-components"
import { updatePassword } from "@/interfaces"
import { toastError, passwordValidator, Message } from "@/utils"
import type { FormInstance, FormRules } from "element-plus"

const visible = ref(false)
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive({
  oldPassword: "",
  newPassword: "",
  confirmPassword: ""
})

const rules: FormRules = {
  oldPassword: [{ required: true, message: "请输入当前登录密码", trigger: "blur" }],
  newPassword: [
    { required: true, message: "请输入新密码", trigger: "blur" },
    {
      validator: passwordValidator
    }
  ],
  confirmPassword: [
    { required: true, message: "请再次确认您的密码", trigger: "blur" },
    {
      validator: passwordValidator
    }
  ]
}

const openDialog = () => {
  visible.value = true
  for (let prop in ruleForm) {
    ruleForm[prop] = ""
  }
}

// 确认修改密码
const confirmModify = () => {
  if (ruleForm.newPassword !== ruleForm.confirmPassword) {
    return Message.error("两次输入密码不一致，请重新输入")
  }
  ruleFormRef.value?.validate(valid => {
    if (!valid) return
    updatePassword({
      oldPassword: ruleForm.oldPassword,
      newPassword: ruleForm.newPassword
    })
      .then(() => {
        Message.success("修改密码成功")
        visible.value = false
      })
      .catch(err => toastError(err, "修改密码失败"))
  })
}

defineExpose({
  openDialog
})
</script>
