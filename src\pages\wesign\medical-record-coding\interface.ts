import axios from "@/interfaces/axios-instance"

interface IGetCatalogListsParam extends IPaginationRequestParams {
  queryType: "wait_catalog" | "cataloged" | "catalog_assignment" | "catalog_plan"
}

export type RangeType = "IN_HOSPITAL" | "OUTPATIENT_SERVICE"

/**
 * @method GET
 * @desc   编码-分页列表
 */
export function getCatalogLists(params: IGetCatalogListsParam) {
  return axios({
    method: "get",
    url: `/api/catalog/catalogs`,
    params
  })
}

/**
 * @method GET
 * @desc   编码-获取所有编码员信息
 */
export function getAllCataloger() {
  return axios({
    method: "get",
    url: `/api/catalog/assignment/cataloger`
  })
}

interface UserDeptItem {
  userWsid: string
  deptWsid: string
}

interface IConfigureCatalogParametersData {
  userDepts: Array<UserDeptItem>
  assignmentType: "AUTO_ASSIGNMENT" | "USER_DEPT" | "MANUAL"
  autoAssignmentThreshold: string | number
  rangeType: RangeType // 区分住院/门急诊
}

/**
 * @method PUT
 * @desc   编码-配置编码参数
 */
export function configureCatalogParameters(data: IConfigureCatalogParametersData) {
  return axios({
    method: "put",
    url: `/api/catalog/assignment`,
    data
  })
}

interface IReassignData {
  inpNos: Array<string> // 住院首页参数-住院号
  userWsids?: Array<string>
}

/**
 * @method PUT
 * @desc   编码-重新分配
 */
export function reassignCatalog(data: IReassignData) {
  return axios({
    method: "put",
    url: `/api/catalog/catalogs/rest-cataloger`,
    data
  })
}

/**
 * @method GET
 * @desc   编码-病案袋归档和上架检查
 */
export function archiveCheck(inpNo: string) {
  return axios({
    method: "get",
    url: `/api/document/bags/${inpNo}/archive-check`
  })
}

/**
 * @method GET
 * @desc   编码-获取编码配置信息
 */
export function getCatalogConfigInfo(rangeType: RangeType) {
  return axios({
    method: "get",
    url: `/api/catalog/assignment`,
    params: { rangeType: rangeType }
  })
}

/**
 * @method GET
 * @desc   编码-获取编码详情数据
 */
export function getCatalogsInfo(params: Record<string, any>) {
  return axios({
    method: "get",
    url: `/api/catalog/catalogs/${params.inpNo}`
  })
}

interface IcdParams {
  item: string
  limit: number
}

/**
 * @method GET
 * @desc   编码-模糊查询诊疗信息
 */
export function getIcdInfo(params: IcdParams) {
  return axios({
    method: "get",
    url: `/api/hospital/icd/fuzzy-query/code`,
    params
  })
}

/**
 * @method GET
 * @desc   编码-模糊查询手术信息
 */
export function getOperationIcdInfo(params: IcdParams) {
  return axios({
    method: "get",
    url: `/api/hospital/icd-cm/fuzzy-query/code`,
    params
  })
}

/**
 * @method GET
 * @desc   编码-模糊查询病理（肿瘤）信息
 */
export function getPathologyIcdInfo(params: IcdParams) {
  return axios({
    method: "get",
    url: `/api/hospital/icd-o/fuzzy-query/code`,
    params
  })
}

interface ICatalogInfoData {
  inpNo: string
  catalogInfo: Record<string, any>
}

/**
 * @method PUT
 * @desc   编码-完成编码
 */
export function confirmCatalogInfo(data: ICatalogInfoData) {
  return axios({
    method: "put",
    url: `/api/catalog/catalogs/${data.inpNo}/confirm`,
    data: data.catalogInfo
  })
}

/**
 * @method PUT
 * @desc   编码-临时保存
 */
export function temporarySaveCatalogInfo(data: ICatalogInfoData) {
  return axios({
    method: "put",
    url: `/api/catalog/catalogs/${data.inpNo}/temporary`,
    data: data.catalogInfo
  })
}

/**
 * @method PUT
 * @desc   编码-取消临时保存
 */
export function cancelSaveCatalogInfo(inpNo: string) {
  return axios({
    method: "put",
    url: `/api/catalog/catalogs/${inpNo}/cancel`
  })
}

/**
 * @method GET
 * @desc   编码-子行政区查询
 */
export function getAdministrativeRegion() {
  return axios({
    method: "get",
    url: `/api/system/administrative-region/child`
  })
}

/**
 * @method POST
 * @desc   编码-首页质控检查
 */
export function checkCatalogData(data: ICatalogInfoData) {
  return axios({
    method: "post",
    url: `/api/catalog/catalogs/${data.inpNo}/qc-check`,
    data: data.catalogInfo
  })
}

/**
 * @method GET
 * @desc   编码-评分pdf下载
 */
export function downloadGradePdf(params) {
  return axios({
    method: "get",
    url: `/api/catalog/catalogs/${params.inpNo}/grade-pdf?key=${params.key}`
  })
}

/**
 * @method GET
 * @desc   编码-首页质控结果查看
 */
export function viewCatalogGrade(inpNo: string) {
  return axios({
    method: "get",
    url: `/api/catalog/catalogs/${inpNo}/grade`
  })
}

/**
 * @method DELETE
 * @desc   编码-退回
 */
export function catalogSendBack(data: Record<string, any>) {
  return axios({
    method: "delete",
    url: `/api/catalog/catalogs/${data.inpNo}/send-back`,
    data: {
      desc: data.desc,
      // operator: data.operator,
      // operatorName: data.operatorName,
      limitedModifyTime: data.limitedModifyTime
    }
  })
}

/**
 * @method PUT
 * @desc   编码-同步
 */
export function catalogSync(inpNo: string) {
  return axios({
    method: "put",
    url: `/api/document/bags/${inpNo}/catalog-sync`
  })
}

interface IFuzzyMedicalInfoParams {
  item: string
  limit: number
}

/**
 * @method GET
 * @desc   模糊查询医护信息
 */
export function queryFuzzyMedicalInfo(params: IFuzzyMedicalInfoParams) {
  return axios({
    method: "get",
    url: `/api/hospital/users/fuzzy-query-limit`,
    params
  })
}

/**
 * @method GET
 * @desc   编码-版本记录
 */
export function getCatalogVersionRecord(inpNo: string) {
  return axios({
    method: "get",
    url: `/api/catalog/catalogs/${inpNo}/sna-history/home-page`
  })
}

interface IVersionDetailParams {
  inpNo: string
  id: string
}

/**
 * @method GET
 * @desc   编码-版本详情
 */
export function getCatalogVersionRecordDetail(params: IVersionDetailParams) {
  return axios({
    method: "get",
    url: `/api/catalog/catalogs/${params.inpNo}/sna-history/home-page/${params.id}`
  })
}

/**
 * @method POST
 * @desc   编码-门诊诊断转换（临床-医保）
 */
export function outpatientClinicalToInsuranceApi(data: Array<Record<string, any>>) {
  return axios({
    method: "post",
    url: `/api/catalog/clinical-to-insurance/outpatient`,
    data: {
      params: data
    }
  })
}

/**
 * @method POST
 * @desc   编码-出院诊断转换（临床-医保）
 */
export function diagnosisClinicalToInsuranceApi(data: Array<Record<string, any>>) {
  return axios({
    method: "post",
    url: `/api/catalog/clinical-to-insurance/discharge-diagnosis`,
    data: {
      params: data
    }
  })
}

/**
 * @method POST
 * @desc   编码-手术诊断转换（临床-医保）
 */
export function operationClinicalToInsuranceApi(data: Array<Record<string, any>>) {
  return axios({
    method: "post",
    url: `/api/catalog/clinical-to-insurance/operation`,
    data: {
      params: data
    }
  })
}

/* ================= 门急诊 =================== */

// 获取门急诊列表
export const getOutpatientCatalogListApi = (params: IPaginationRequestParams) => {
  return axios({
    method: "get",
    url: `/api/catalog/outpatient/catalogs`,
    params: {
      ...params,
      // @ts-ignore
      mainDiagnostic: params.queryParams?.mainDiagnostic
    }
  })
}

// 手动分配
export const manualAssignOutpatientApi = (data: {
  registerNo?: Array<string> // 门诊首页参数-挂号
  userWsids: string[]
}) => {
  return axios({
    method: "put",
    url: `/api/catalog/catalogs/outpatient/rest-cataloger`,
    data
  })
}

// 门急诊已编码校验
export const getOutpatientValidateInfoApi = (data: Record<string, any>) => {
  return axios({
    method: "post",
    url: `/api/catalog/dynamic-form-catalog/${data.appScenario}/${data.registerNo}/qc-check`
  })
}

/**
 * @method GET
 * @desc   病案编目退回分页列表
 */
export function getReturnCatalogRecordApi(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/catalog/catalogs/return-records`,
    params
  })
}
