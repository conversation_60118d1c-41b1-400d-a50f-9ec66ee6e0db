<template>
  <CardContainer
    v-show="submitStore.activeStep === 1"
    :allowed-return="true"
    :return-url="route.query?.returnUrl as string"
    :title="props.patientName + '（' + props.inpNo + '）'"
    style="height: 100vh"
  >
    <template #header>
      <div class="header-right">
        <div class="header-right-title"></div>
        <div>
          <el-button @click="toPrev">上一份文件</el-button>
          <el-button @click="toNext">下一份文件</el-button>
          <el-button style="color: #3860f4" color="#D7DFFD" :disabled="state.submitLoading" @click="handleNext">
            下一步
          </el-button>
          <!-- <el-button color="#626aef" :dark="isDark" disabled>Disabled</el-button> -->
        </div>
      </div>
    </template>

    <!-- <el-steps :active="2" align-center>
      <el-step title="选择归档文档"></el-step>
      <el-step title="文档签名"></el-step>
    </el-steps> -->

    <!-- <div class="header">患者信息：{{ props.patientName }}（{{ props.inpNo }}）</div> -->
    <div class="sign-container">
      <!-- 待签署文档列表 -->
      <div class="document-list">
        <div class="document-list__header">待签名文档</div>
        <div class="document-list__content">
          <empty-content v-if="state.signDocuments.length === 0" desc="暂无数据" />
          <template v-else>
            <div
              v-for="(signDocument, signDocumentIndex) in state.signDocuments"
              :key="signDocument.wsid"
              :class="state.activeDocumentIndex === Number(signDocumentIndex) ? 'sign-document--active' : ''"
              class="sign-document"
              @click="setActiveDocument(signDocumentIndex)"
            >
              <div style="display: flex; flex: 1">
                <i v-if="!signDocument.isSupplement" class="ri-file-text-line electric-icon"></i>
                <i v-else class="ri-camera-line paper-icon"></i>
                <span v-if="!signDocument.renamable" style="padding-left: 10px">
                  {{ signDocument.title }}（{{ signDocument.filePage }}页）
                </span>
                <el-input
                  v-else
                  v-model="signDocument.title"
                  style="max-width: 90%"
                  size="small"
                  placeholder="请输入文件名称"
                  @click.stop
                  @blur="saveRename(signDocument)"
                >
                  <template #append>
                    <el-button size="small" :icon="Select" @click="saveRename(signDocument)" />
                  </template>
                </el-input>
              </div>
              <!-- 编辑文件名称 -->
              <i
                v-if="!signDocument.renamable"
                style="color: #3860f4"
                class="ri-edit-box-line"
                @click.stop="handleRename(signDocument)"
              ></i>
            </div>
          </template>
        </div>
      </div>

      <!-- 预览区域 -->
      <div class="pdf-container">
        <!-- <PdfPreview
          ref="pdfPreviewRef"
          :show-control="false"
          :default-width="1000"
          :enable-cache="false"
          :src="'/api' + state.signDocuments[state.activeDocumentIndex]?.contentFilePath"
        > -->
        <PdfPreviewComponent
          v-if="state.signDocuments[state.activeDocumentIndex]?.contentFilePath"
          ref="pdfPreviewRef"
          :src="'/api' + state.signDocuments[state.activeDocumentIndex]?.contentFilePath"
        >
          <template v-if="state.signDocuments[state.activeDocumentIndex]?.forms" #sign>
            <!-- 循环签署表单 -->
            <template v-for="form in state.signDocuments[state.activeDocumentIndex]?.forms">
              <!-- 签署表单 -->
              <div
                v-for="formPosition in form.formPositionList"
                :key="formPosition.formName"
                v-loading="form.loading"
                class="sign-form"
                :style="getFormStyle(formPosition)"
              >
                <span v-if="!form.signerInfoList?.length">{{ form.templateFormName }}</span>
              </div>

              <!-- 签署者签名图片 先循环外层签署者列表 -->
              <template v-for="signerInfo in form.signerInfoList" :key="signerInfo.jobId">
                <!-- 循环每个签署者的签名位置信息 -->
                <div
                  v-for="signPosition in signerInfo.signPositionList"
                  :key="signPosition.jobId + 'sign'"
                  class="sign-image__wrapper"
                  :style="getFormStyle(signPosition)"
                >
                  <img v-if="signerInfo.base64Pic" class="sign-image" :src="signerInfo.base64Pic" />
                  <span v-else style="line-height: 1">用户签名</span>
                </div>
              </template>
            </template>
          </template>
        </PdfPreviewComponent>
        <!-- </PdfPreview> -->
      </div>

      <!-- 右侧签署流程区域 -->
      <div v-loading="state.signLoading" class="sign-process">
        <div class="sign-process__header">签署流程</div>
        <div class="sign-process__content">
          <empty-content v-if="state.activeDocumentIndex < 0" desc="暂无数据" />
          <template v-else>
            <el-form label-position="top">
              <el-form-item
                v-for="(form, formIndex) in state.signDocuments[state.activeDocumentIndex]?.forms"
                :key="form.templateFormName + formIndex"
                :label="form.participantName"
                required
              >
                <div class="form-signer">
                  <el-select
                    v-model="form.singerJobIdList"
                    v-loading="form.loading"
                    multiple
                    :multiple-limit="Number(form.maxSigner)"
                    filterable
                    :filter-method="filterDeptUsers"
                    placeholder="请输入工号/姓名"
                    @change="setSigner($event, form)"
                  >
                    <template #prefix>
                      <i class="ri-user-add-line"></i>
                    </template>
                    <el-option
                      v-for="user in state.filteredUserList"
                      :key="user.jobId"
                      :disabled="form.loading"
                      :label="user.realName"
                      :value="user.jobId"
                    />
                  </el-select>

                  <!-- 属性弹窗 -->
                  <el-popover trigger="click" placement="bottom" :width="300">
                    <el-descriptions title="属性" border :column="1">
                      <el-descriptions-item label="定位方式">{{ form.positionType }}</el-descriptions-item>
                      <el-descriptions-item label="最大签名人数">{{ form.maxSigner }}</el-descriptions-item>
                      <el-descriptions-item label="签署角色">{{ form.participantName }}</el-descriptions-item>
                      <el-descriptions-item label="签署顺序">{{ form.signSnType }}</el-descriptions-item>
                      <el-descriptions-item label="签名方式">{{ form.signType }}</el-descriptions-item>
                    </el-descriptions>
                    <template #reference>
                      <el-button type="default" plain style="margin-left: 8px">属性</el-button>
                    </template>
                  </el-popover>
                </div>
              </el-form-item>
            </el-form>
          </template>
        </div>
      </div>
    </div>
  </CardContainer>

  <!-- 进度弹窗 -->
  <SubmitProgressDialog
    ref="progressDialogRef"
    :inp-no="props.inpNo"
    @success="handleSubmitSuccess"
    @fail="handleSubmitFail"
  />

  <!-- 完整性弹窗 -->
  <!-- <IntegralityDrawer
    ref="integralityDrawerRef"
    :inp-no="props.inpNo"
    :allow-missing="props.allowMissing"
    @success="confirmSubmit"
  /> -->
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch } from "vue"
import { useRouter, useRoute } from "vue-router"
import { ElMessageBox } from "element-plus"
import { debounce } from "lodash-es"
import { Select } from "@element-plus/icons-vue"
import { EmptyContent, PdfPreview, CardContainer, PdfPreviewComponent } from "@/base-components"
import { useSubmitStore } from "../useSubmitStore"
import IntegralityDrawer from "./IntegralityDrawer.vue"
import SubmitProgressDialog from "./SubmitProgressDialog.vue"
import type { AllowMissingType } from "../config"
import {
  getSubmitProgressApi,
  submitMedicalRecordApi,
  getDepartmentMemberApi,
  getNeedSignDocumentsApi,
  getSignFormsApi,
  getSignerInfoApi,
  getEverySignPositionApi,
  type GetSignPositionParams,
  type SignInfo,
  fileRenameApi
} from "@/interfaces"
import { useSystemStore } from "@/stores"
import { SystemAlert, toastError, Message } from "@/utils"

const router = useRouter()
const route = useRoute()
const submitStore = useSubmitStore()
const systemStore = useSystemStore()
const pdfPreviewRef = ref()
const ratio = computed(() => pdfPreviewRef.value?.ratio) // pdf缩放比例

interface SignProps {
  inpNo: string // 病案号
  patientName: string // 患者名称
  allowMissing: AllowMissingType // 是否允许缺失
}

const props = defineProps<SignProps>()

/* ============== 文档签署相关 ============== */

interface SignState {
  signDocuments: Record<string, any>[] // 待签署的文件列表
  activeDocumentIndex: number // 用户当前所选的pdf文件
  deptUserList: Record<string, any>[] // 当前科室成员
  filteredUserList: Record<string, any>[] // 过滤出的科室用户列表
  submitLoading: boolean // 提交的loading状态
  signLoading: boolean // 正在加载文档的签字内容，快速点击时可能位置信息还没请求成功，所以此处需要加loading
}

const state = reactive<SignState>({
  signDocuments: [],
  activeDocumentIndex: 0,
  deptUserList: [],
  filteredUserList: [],
  submitLoading: false,
  signLoading: false
})

// 获取待签署文档列表
const getNeedSignDocuments = () => {
  state.activeDocumentIndex = -1
  getNeedSignDocumentsApi(props.inpNo).then(res => {
    let signDocuments = res.data.data?.documents ?? []
    state.signDocuments = signDocuments.map(doc => {
      doc.renamable = false
      if (!doc.forms?.length) return doc
      doc.forms.forEach(form => {
        form.loading = false // 添加loading属性，用于表单加载
        form.singerJobIdList = [] as string[] // form签署者的wsid列表
        form.signerInfoList = [] as Record<string, any> // form签署者信息列表jobId，imgSrc, autoSign
        form.signerImageCount = 0 // 已有签署者的图片数量
      })
      return doc
    })
    // 无待签署文件，默认切换到下一步
    if (state.signDocuments.length === 0) handleNext()
    setActiveDocument(0, true)
  })
}

// 获取用户列表
const getDeptUserList = (signSearchValue?: string) => {
  getDepartmentMemberApi(signSearchValue).then(res => {
    state.deptUserList = res.data.data
    state.filteredUserList = res.data.data
  })
}

/**
 * 改变左侧选中的代签名文档
 * @param index
 * @param isNew 是否是重新设置文档
 */
const setActiveDocument = async (index: number, isNew?: boolean) => {
  if (!isNew && state.activeDocumentIndex === index) return
  state.activeDocumentIndex = index
  // 没有form不请求
  if (!state.signDocuments[index]?.forms) return
  // 如果已经请求过form位置则不再请求
  if (state.signDocuments[index]?.forms[0]?.formPositionList?.length) return
  state.signLoading = true
  const formPositions = await getSignFormsApi(state.signDocuments[index].wsid)
  // 循环form将每个签名框放入form
  state.signDocuments[index].forms.forEach(form => {
    for (let formPosition of formPositions) {
      if (form.templateFormName === formPosition.formName) {
        form.formPositionList = formPosition.keywordData
        break
      }
    }
  })
  state.signLoading = false
}

// 设置form表单的签名
const setSigner = debounce((singerJobIdList, form) => {
  if (!singerJobIdList.length) {
    form.signerInfoList = []
    form.signerImageCount = 0
    return
  }
  let getSignPositionParams: GetSignPositionParams = {
    signPositions: [
      {
        formName: form.templateFormName,
        joIds: singerJobIdList,
        keywordData: form.formPositionList
      }
    ]
  }
  form.loading = true
  // 请求签名图片和每个签名的位置
  Promise.all([getSignerInfoApi(singerJobIdList), getEverySignPositionApi(getSignPositionParams)])
    .then(res => {
      form.loading = false
      const signerInfoList = res[0]
      const signFormList = res[1]
      // 循环签名信息，将每个签名的坐标放入每个签名信息对象列表中
      for (let signerInfo of signerInfoList) {
        signerInfo.signPositionList = []
        // jobId一致则放入签名列表
        for (let signForm of signFormList) {
          // 使用keywordData进行循环
          for (let signPosition of signForm.keywordData) {
            if (signerInfo.jobId === signPosition.jobId) {
              signerInfo.signPositionList.push(signPosition)
            }
          }
        }
      }
      form.signerInfoList = signerInfoList
    })
    .catch(() => {
      form.singerJobIdList.pop()
      form.loading = false
    })
}, 200)

/* ============== 提交 ============== */

const integralityDrawerRef = ref<InstanceType<typeof IntegralityDrawer>>()

// 点击提交先校验完整性
const handelPreCheck = () => {
  if (submitStore.status === "REPAIR") {
    ElMessageBox.confirm("当前病案数据已返修，请确认您已同步过病案", "注意", {
      type: "warning",
      confirmButtonText: "已同步"
    }).then(() => {
      integralityDrawerRef.value?.checkIntegrality()
    })
  } else {
    integralityDrawerRef.value?.checkIntegrality()
  }
}

// 确认提交
const confirmSubmit = () => {
  // if (!submitStore?.homePageFlag) {
  //   SystemAlert("当前病案没有首页，无法提交！")
  //   return
  // }
  // if (!state.signDocuments?.length) {
  //   SystemAlert("当前没有待签名文档！")
  //   return
  // }
  if (route.query?.needSign === "YES") {
    let signInfoList: SignInfo[] = [] // 参数：签署信息列表
    let unSignedFormCount = 0 // 未设置签署人的form数量
    // 循环每个文档中的表单，查看签署者
    for (let signDocument of state.signDocuments) {
      if (!signDocument.forms?.length) continue
      for (let form of signDocument.forms) {
        if (form.singerJobIdList.length) {
          signInfoList.push({
            documentWsid: signDocument.wsid,
            templateFormName: form.templateFormName,
            jobIds: form.singerJobIdList
          })
        } else {
          unSignedFormCount += 1
        }
      }
    }
    // 所有文档必须要设置了表单签署者才能提交
    if (unSignedFormCount >= 1) {
      SystemAlert(`您有${unSignedFormCount}处签名位置未设置签名用户，请处理完成再提交`)
      return
    }
    // 调用提交
    state.submitLoading = true
    systemStore.showLoading("提交中")
    submitMedicalRecordApi({
      inpNo: props.inpNo,
      confirmDocumentWsids: submitStore.checkedArchiveDocumentWsidList,
      signInfos: signInfoList,
      submitRemark: submitStore.submitRemark
    })
      .then(res => {
        getSubmitProgress(res.data.data) // totalFileCount
      })
      .catch(error => {
        state.submitLoading = false
        systemStore.hideLoading()
        toastError(error)
      })
  } else {
    state.submitLoading = true
    systemStore.showLoading("提交中")
    submitMedicalRecordApi({
      inpNo: props.inpNo,
      confirmDocumentWsids: submitStore.checkedArchiveDocumentWsidList,
      submitRemark: submitStore.submitRemark
    })
      .then(() => {
        ElMessageBox.alert("提交成功，将返回列表页面", "系统提示", {
          type: "success",
          confirmButtonText: "知道了",
          showClose: false
        }).then(() => {
          router.replace((route.query?.returnUrl as string) || "/medical-record/list")
        })
        systemStore.hideLoading()
      })
      .catch(error => {
        systemStore.hideLoading()
        state.submitLoading = false
        toastError(error)
      })
  }
}

/* ============== 提交进度 ============== */

const progressDialogRef = ref()

const getSubmitProgress = (totalFileCount: number) => {
  if (totalFileCount === 0) {
    handleSubmitSuccess()
    return
  }
  getSubmitProgressApi(props.inpNo)
    .then(res => {
      const { completeFlag, errorMsg } = res.data.data
      if (errorMsg) {
        Message.error("提交失败！请联系管理员")
        state.submitLoading = false
        systemStore.hideLoading()
        return
      }
      if (completeFlag === 2) {
        // 提交成功 延时完成让用户看到效果
        setTimeout(() => handleSubmitSuccess(), 1000)
      } else if (completeFlag === 1) {
        // 已经提交但在提交中
        progressDialogRef.value.init(res.data.data.submitCount, res.data.data.total)
      } else {
        // 未提交，正常提交流程-正常情况不会出现
      }
    })
    .catch(error => {
      toastError(error)
      systemStore.hideLoading()
    })
}

// 处理提交成功
const handleSubmitSuccess = () => {
  SystemAlert("提交成功，点击按钮将跳转到提交列表页面", "success").finally(() => {
    router.replace((route.query?.returnUrl as string) || "/medical-record/list")
  })
  state.submitLoading = false
  systemStore.hideLoading()
}

// 处理提交失败
const handleSubmitFail = (errorMsg: string) => {
  SystemAlert(`提交失败！${errorMsg}`, "error")
}

const emits = defineEmits(["refresh"])

// 点击下一步
const handleNext = () => {
  submitStore.activeStep = 2
  emits("refresh")
}

/*=================== 格式化 ==================== */

// 过滤科室用户
const filterDeptUsers = keyword => {
  state.filteredUserList = state.deptUserList.filter(user => {
    return user.jobId.includes(keyword) || user.realName.includes(keyword)
  })
}

/**
 * 获取form表单的样式(包括签名)
 * formPosition中返回的是签署关键字（即key）的位置，故需要基于关键字位置计算坐标
 */
const getFormStyle = formPosition => {
  const width = (formPosition.lrX - formPosition.ulX) * ratio.value
  const height = (formPosition.lrY - formPosition.ulY) * ratio.value
  const left = formPosition.ulX * ratio.value
  const accumulationTop = formPosition.pageHeight * (formPosition.page - 1) // 前面页数的高度
  let top = (formPosition.ulY + accumulationTop) * ratio.value // 加上前面页数的高度
  top += formPosition.page // 加上每一页的border
  return {
    width: width + "px",
    maxWidth: width + "px",
    height: height + "px",
    lineHeight: height + "px",
    left: left + "px",
    top: top + "px"
  }
}

/*=================== watch ==================== */

// 监听数据改变，获取需要签署的文件
watch(
  () => props.inpNo,
  val => {
    if (!val) return
    getDeptUserList()
    getNeedSignDocuments()
  },
  { immediate: true, deep: true }
)

/*=================== expose ==================== */

// 第一页删除文件时，删除第二页对应需要签署的文档
const deleteDocument = (fileWsid: string) => {
  state.signDocuments = state.signDocuments.filter(doc => {
    return doc.fileWsid !== fileWsid
  })
  if (!state.signDocuments.length) {
    state.activeDocumentIndex = -1 // 设置pdf预览为空
  } else {
    setActiveDocument(0)
  }
}

// pdf切换
const toPrev = debounce(() => {
  state.activeDocumentIndex--
  if (state.activeDocumentIndex < 0) {
    state.activeDocumentIndex = 0
    return
  }
  // setActiveDocument(state.activeDocumentIndex)
}, 300)

const toNext = debounce(() => {
  state.activeDocumentIndex++
  if (state.activeDocumentIndex >= state.signDocuments.length - 1) {
    state.activeDocumentIndex = state.signDocuments.length - 1
    return
  }
  // setActiveDocument(state.activeDocumentIndex)
}, 300)

// 文件重命名
const saveRename = async data => {
  try {
    data.renamable = false
    await fileRenameApi(data.wsid, data.title)
  } catch (error: any) {
    toastError(error, "文件重命名失败！")
  }
}

const handleRename = data => {
  data.renamable = true
}

defineExpose({ getNeedSignDocuments, deleteDocument, confirmSubmit })
</script>

<style lang="less" scoped>
// 提交进度条
.submit-process {
  width: 40vw;

  &__text {
    font-size: 14px;
    margin-bottom: 4px;
  }

  &__tip {
    font-size: 12px;
    color: #f56c6c;
    margin-left: 10px;
  }
}

// 整体容器
.sign-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  // margin-top: 20px;
  // height: 95%;
  height: calc(100% - 32px);
  margin: 16px;
  // background: #fff;
}

// 文档列表
.document-list {
  min-width: 300px;
  width: 350px;
  border: 1px solid #ecedef;
  height: 100%;
  overflow-y: auto;
  background-color: #fff;

  &__content {
    padding: 10px;
    height: calc(100% - 60px);
    overflow-y: auto;
  }

  .sign-document {
    cursor: pointer;
    height: 30px;
    // line-height: 30px;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 20px;

    &--active {
      background: rgba(56, 96, 244, 0.2);
      color: #3860f4;
    }
  }
}

.document-list__header,
.sign-process__header {
  position: sticky;
  color: #0a1633;
  font-size: 14px;
  height: 40px;
  line-height: 40px;
  padding-left: 10px;
  background: #f6f8fc;
}

.pdf-container {
  border: 1px solid #e5e5e5;
  height: 100%;
  flex: 1;
  // margin: 0 10px;
  min-width: 900px;
  border-left: none;
  border-right: none;
}

// 右侧签署信息
.sign-process {
  min-width: 300px;
  width: 300px;
  border: 1px solid #ecedef;
  height: 100%;
  overflow-y: auto;

  &__content {
    padding: 10px;
    height: calc(100% - 60px);
    overflow-y: auto;
    background-color: #fff;
  }

  .form-signer {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

// 表单相关
.sign-form {
  position: absolute;
  background: rgba(56, 96, 244, 0.1);
  border: 1px dashed #3860f4;
  border-radius: 4px;
  // white-space: nowrap;
  text-align: center;

  &::-webkit-scrollbar {
    display: none;
  }
}

.sign-image {
  width: 100%;
  height: 100%;
  object-fit: contain;

  &__wrapper {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.header-right {
  flex: 1;
  display: flex;
  justify-content: space-between;
  // border-left: 1px solid #e5e5e5;
  padding-left: 16px;
  // margin-left: 190px;

  &-title {
    font-weight: 600;
    font-size: 14px;
    color: #0a1633;
  }
}

:deep(.custom-card) {
  height: 100vh !important;
}

:deep(.custom-card-title) {
  width: 334px !important;
}

:deep(.custom-card-body) {
  padding: 0 !important;
}
.electric-icon {
  color: #04c3a1;
  font-size: 16px;
  display: flex;
  align-items: center;
}
.paper-icon {
  color: #409eff;
  font-size: 16px;
}
</style>
