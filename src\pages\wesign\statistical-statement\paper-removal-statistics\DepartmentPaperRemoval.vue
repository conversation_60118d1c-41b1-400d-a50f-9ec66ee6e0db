<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList"></TabsRouter>
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <DepartmentFormItem v-model:model-value="deptPaperRemovalParams.deptWsid" label="科室"></DepartmentFormItem>

        <StatisticalTypeFormItem
          v-model:filter-prop="searchFormState.statisticalTypeFilterProp"
          v-model:filter-value="searchFormState.statisticalTypeFilterValue"
          label="统计类型"
        ></StatisticalTypeFormItem>
      </SearchContainer>
    </template>

    <template #table>
      <!-- 表格 -->
      <CommonTable
        v-if="isDisplayTable"
        :table-columns="departmentPaperRemovalColumns"
        :request-api="getDepartmentPaperStatisticsApi"
        :request-params="searchParams.queryParams"
        :data-callback="formatTableData"
        :pagination="false"
        class="deptPaperRemovalTable"
      >
        <template #header>
          <div class="header-container flex-between">
            <div>
              <ExportButton
                :export-api="exportDepartmentStatisticsApi"
                :export-params="exportParams"
                :file-name="`脱纸率统计表_${getCurrentDate()}.xlsx`"
                file-type="application/vnd.ms-excel"
              >
                导出
              </ExportButton>
            </div>

            <div class="table-chart-tool">
              <el-tooltip content="表格展示">
                <i v-if="isDisplayTable"><img src="@/assets/svg/statistical-statement/table-active.svg" alt="" /></i>
                <i v-else @click="() => (isDisplayTable = !isDisplayTable)">
                  <img src="@/assets/svg/statistical-statement/table.svg" alt="" />
                </i>
              </el-tooltip>
              <el-tooltip content="图表展示">
                <i v-if="isDisplayTable" @click="() => (isDisplayTable = !isDisplayTable)">
                  <img src="@/assets/svg/statistical-statement/chart.svg" alt="" />
                </i>
                <i v-else><img src="@/assets/svg/statistical-statement/chart-active.svg" alt="" /></i>
              </el-tooltip>
            </div>
          </div>
        </template>
      </CommonTable>

      <!-- 图表 -->
      <DepartmentPaperRemovalChart
        v-else
        v-model:show-chart="isDisplayTable"
        :request-params="searchParams.queryParams"
      ></DepartmentPaperRemovalChart>
    </template>
  </PageContainer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from "vue"
import { cloneDeep } from "lodash-es"
import { PageContainer, SearchContainer, CommonTable, DepartmentFormItem, ExportButton } from "@/base-components"
import { TabsRouter } from "@/page-components"
import { useTableSearch } from "@/hooks"
import { getCurrentDate } from "../config"
import { getDepartmentPaperStatisticsApi, exportDepartmentStatisticsApi } from "../interface"
import DepartmentPaperRemovalChart from "./components/DepartmentPaperRemovalChart.vue"
import StatisticalTypeFormItem from "./components/StatisticalTypeFormItem.vue"
import { tabsRouterList, departmentPaperRemovalColumns, handleYear, handleMonth, getQuartersTimestamps } from "./config"

const archiveDateEnd = new Date(new Date().setHours(23, 59, 59, 999)).getTime()
const archiveDateStart = new Date(
  new Date(new Date().setMonth(new Date().getMonth() - 1)).setHours(0, 0, 0, 0)
).getTime()

const isDisplayTable = ref(true)

/* ======================== 获取当前季度时间戳 ======================== */

const year = new Date().getFullYear() // 获取当前年份
const quartersTimestamps = getQuartersTimestamps(year)

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  deptWsid: "",
  statisticalTypeFilterProp: "section",
  statisticalTypeFilterValue: [archiveDateStart, archiveDateEnd] as any
})

const deptPaperRemovalParams = reactive({
  deptWsid: "",
  archiveDateStart: archiveDateStart,
  archiveDateEnd: archiveDateEnd
})

const { searchParams, handleQuery, handleReset } = useTableSearch(
  {},
  "",
  { ...searchFormState, ...deptPaperRemovalParams },
  deptPaperRemovalParams,
  searchFormState
)

watch(
  () => searchFormState.statisticalTypeFilterValue,
  val => {
    if (searchFormState.statisticalTypeFilterProp === "year") {
      val = handleYear(val)
    } else if (searchFormState.statisticalTypeFilterProp === "month") {
      val = handleMonth(val)
    } else if (searchFormState.statisticalTypeFilterProp === "quarter") {
      val = quartersTimestamps[val]
    }
    deptPaperRemovalParams.archiveDateStart = val[0] || val.start
    deptPaperRemovalParams.archiveDateEnd = val[1] || val.end
  }
)

const exportParams = computed(() => {
  return {
    archiveDateStart: searchFormState.statisticalTypeFilterValue[0],
    archiveDateEnd: searchFormState.statisticalTypeFilterValue[1]
  }
})

function formatTableData(resData) {
  return resData.data.data.rows
}
</script>

<style lang="less" scoped>
.chart-container {
  height: calc(100% - 20px);

  .date-picker {
    :deep(.el-radio-group) {
      margin-bottom: 0 !important;
      margin-right: 10px;
      flex-wrap: nowrap;
      padding: 5px 10px;
      background-color: #eff2f7;
      border-radius: 4px;
    }

    :deep(.el-radio-button__inner) {
      border: none;
      background-color: transparent;
      box-shadow: none;
      &:hover {
        color: #3860f4;
      }
    }
    :deep(.el-radio-group) {
      .is-active {
        background-color: #3860f4;
        border-radius: 4px;
        color: #fff;
        &:hover {
          .el-radio-button__inner {
            color: #fff;
          }
        }
      }
    }
  }
}
:deep(.el-form-item__content) {
  align-items: flex-start !important;
}
</style>

<style>
.deptPaperRemovalTable.common-table-container {
  .common-table-header {
    display: block;
  }
}
</style>
