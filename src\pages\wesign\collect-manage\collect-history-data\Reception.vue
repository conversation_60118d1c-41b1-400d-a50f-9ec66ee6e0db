<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />

      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <CommonInputFormItem v-model="searchFormState.systemName" label="厂商系统" />
        <CommonSelectFormItem v-model="searchFormState.dataType" label="数据类型" :options="receptionDataTypeOptions" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="tableRef"
        :table-columns="receptionTableColumns"
        :request-api="getReceptionHistoryApi"
        :request-params="searchParams"
      >
        <template #header>
          <AddButton @click="showAddForm">新增</AddButton>
        </template>
        <template #dataType="{ row }">
          {{ receptionDataTypeOptions.find(option => option.value === row.dataType)?.label || "--" }}
        </template>
        <template #operation="{ row }">
          <TableButton @click="showEditForm(row)">编辑</TableButton>
          <TableButton @click="confirmDelete(row, row.wsid)">删除</TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>

  <DialogContainer
    v-model:visible="visible"
    :title="actionType === 'edit' ? '修改接收配置' : '添加接收配置'"
    :width="700"
    class="reception-form-dialog"
    style="max-height: 75vh"
  >
    <el-form
      ref="formRef"
      label-position="right"
      label-width="145px"
      label-suffix="："
      :model="formState"
      :rules="receptionFormRules"
    >
      <CommonSelectFormItem
        v-model="formState.systemWsid"
        label="厂商系统"
        prop="systemWsid"
        :options="systemVendorOptions"
      />

      <CommonSelectFormItem
        v-model="formState.dataType"
        label="数据类型"
        prop="dataType"
        :options="receptionDataTypeOptions"
      />

      <el-form-item ref="largeScreen" label="数据清洗" prop="sourceCode" class="data-clean">
        <Codemirror
          v-model="formState.sourceCode"
          placeholder="请输入"
          :tab-size="10"
          :extensions="dataExtensions"
          :style="{ height: '100%', width: '100%' }"
        />
        <div ref="fullScreen" class="fullscreen-btn">
          <i v-if="!isFullscreen" title="全屏显示" class="ri-fullscreen-line" @click="toggle"></i>
          <i v-else title="退出全屏" class="ri-fullscreen-exit-line" @click="toggle"></i>
        </div>
      </el-form-item>

      <!-- 配置描述textarea -->
      <el-form-item label="配置描述" prop="remark">
        <el-input
          v-model="formState.remark"
          type="textarea"
          :rows="4"
          placeholder="请输入配置描述"
          :maxlength="200"
          show-word-limit
          clearable
        ></el-input>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button :loading="actionLoading" @click="closeFormDialog()">取消</el-button>
      <el-button :loading="actionLoading" type="primary" @click="handleSave">保存</el-button>
    </template>
  </DialogContainer>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import { java } from "@codemirror/lang-java"
import { useFullscreen } from "@vueuse/core"
import { Codemirror } from "vue-codemirror"
import {
  PageContainer,
  DialogContainer,
  CommonTable,
  AddButton,
  TableButton,
  CommonSelectFormItem,
  CommonInputFormItem,
  SearchContainer
} from "@/base-components"
import { TabsRouter } from "@/page-components"
import { useTableSearch, useCommonOptions } from "@/hooks"
import useFormSetting from "@/hooks/useFormSetting_v2"
import useTableOperation from "@/hooks/useTableOperation_v2"
import { SystemPrompt } from "@/utils"
import { getCollectSystemList } from "../collect-way/interface"
import { myTheme, receptionTableColumns, receptionFormRules, tabsRouterList, receptionDataTypeOptions } from "./config"
import {
  getReceptionHistoryApi,
  addReceptionHistoryApi,
  deleteReceptionHistoryApi,
  updateReceptionHistoryApi
} from "./interface"
import type { FormInstance } from "element-plus"

/* ======================== 页面加载时获取选项 ======================== */

// 获取所有厂商系统选项
const { options: systemVendorOptions } = useCommonOptions({
  getOptionsApi: getCollectSystemList,
  labelAlias: "systemName",
  valueAlias: "wsid"
})

/* ======================== 搜索 ======================== */

const tableRef = ref<InstanceType<typeof CommonTable>>()

const searchFormState = reactive({
  systemName: "",
  dataType: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/* ======================== 行操作 ======================== */

const showAddForm = () => {
  // changeDataType()
  showForm("add")
}

const showEditForm = row => {
  // changeDataType()
  showForm("edit", row)
}

/* ======================== 表格 ======================== */

const formRef = ref<FormInstance>()
const testLoading = ref(false)

const formInitialValue = {
  id: "",
  systemWsid: "", // 厂商系统
  dataType: "", // 数据类型
  sourceCode: "", // 数据清洗
  remark: "" // 备注
}

const { visible, actionType, formState, showForm, closeForm } = useFormSetting(formInitialValue)

const { confirmAdd, confirmEdit, confirmDelete, actionLoading } = useTableOperation(formState, {
  apiGroup: {
    addApi: addReceptionHistoryApi,
    editApi: updateReceptionHistoryApi,
    deleteApi: deleteReceptionHistoryApi
  },
  closeForm: closeFormDialog,
  deleteConfirmation: row => SystemPrompt(`您确定要删除“${row?.name}”吗？`),
  callback: () => tableRef.value?.refreshTableData()
})

const handleSave = () => {
  formRef.value?.validate(valid => {
    if (!valid) return
    actionType.value === "edit" ? confirmEdit() : confirmAdd()
  })
}

// 关闭编辑弹窗
function closeFormDialog() {
  closeForm()
  testLoading.value = false
}

/* ======================== 代码块 ======================== */

// 数据清洗
const dataExtensions = [java(), myTheme]

const largeScreen = ref(null)
const fullScreen = ref(null)
const { toggle, isFullscreen } = useFullscreen(largeScreen)
</script>

<style lang="less" scoped>
:deep(.cm-editor) {
  border: 1px solid #dcdfe6;
}

:deep(.cm-focused) {
  outline: none !important;
}

:deep(.cm-lineNumbers) {
  border-right: 1px solid #dcdfe6;
}

.reception-form-dialog {
  :deep(.el-dialog__body) {
    max-height: 75vh;
    overflow-y: auto;
  }
}

.data-clean {
  :deep(.el-form-item__content) {
    align-items: baseline;
    position: relative;
  }
}

.fullscreen-btn {
  position: absolute;
  cursor: pointer;
  right: 10px;
  top: 15px;
  line-height: 1;
  transform: translateY(-50%);
  i {
    opacity: 0.5;
    font-size: 20px;
    color: #333;
  }
  &:hover {
    i {
      opacity: 1;
    }
  }
}
</style>
