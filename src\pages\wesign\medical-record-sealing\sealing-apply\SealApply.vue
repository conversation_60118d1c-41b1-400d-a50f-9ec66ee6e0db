<template>
  <PageContainer separate>
    <!-- 头部搜索 -->
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchForm
        :form-config="sealingApplySearchFormConfig"
        :form-state="searchFormState"
        @query-btn-click="handleQuery"
        @reset-btn-click="handleReset"
      />
    </template>

    <!-- 封存申请表格 -->
    <template #table>
      <CommonTable
        ref="sealingApplyTableRef"
        table-id="sealingApplyTable"
        :table-columns="sealingTableColumns"
        :request-api="getSealingListApi"
        :request-params="searchParams"
      >
        <template #inHospitalDatetime="{ row }">
          <span>{{ formatDatetime(row.inHospitalDatetime) }}</span>
        </template>
        <template #outHospitalDatetime="{ row }">
          <span>{{ formatDatetime(row.outHospitalDatetime) }}</span>
        </template>
        <template #sealingStatus="{ row }">
          <span>{{ getSealingStatusDesc(row.sealingStatus) }}</span>
        </template>
        <template #operation="{ row }">
          <TableButton
            v-if="
              ['UNSEALED', 'REQUESTED_SEALING'].includes(row.sealingStatus) &&
              hasOperationPermission(menuId, MenuOperationEnum.Sealing)
            "
            :disabled="row.sealingStatus === 'REQUESTED_SEALING'"
            @click="showApplyDialog(row)"
          >
            封存
          </TableButton>
        </template>
      </CommonTable>
    </template>

    <!-- 封存弹窗 -->
    <apply-dialog ref="applyDialogRef" :need-sign="needSign" @success="handleApplySuccess" />
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import { CommonTable, TableButton, PageContainer } from "@/base-components"
import { SearchForm, TabsRouter } from "@/page-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { useUserStore } from "@/stores"
import { formatDatetime } from "@/utils"
import { getSealingListApi } from "../interface"
import { useSealingConfig } from "../useSealingConfig"
import ApplyDialog from "./ApplyDialog.vue"
import { sealingTableColumns, getSealingStatusDesc, sealingApplySearchFormConfig, tabsRouterList } from "./config"

let menuId = "/sealing/apply"
const { hasOperationPermission } = useUserStore()
const { needSign } = useSealingConfig()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  patientFilterProp: "patientName",
  patientFilterValue: "",
  secrecyGrade: "",
  outHospitalDatetime: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState, "sealingStatus=UNSEALED")

const sealingApplyTableRef = ref<InstanceType<typeof CommonTable>>()

/* ======================== 封存弹窗 ======================== */

const applyDialogRef = ref<InstanceType<typeof ApplyDialog>>()

const showApplyDialog = row => {
  applyDialogRef.value?.show(row)
}

// 申请成功刷新弹窗
const handleApplySuccess = () => {
  sealingApplyTableRef.value?.refreshTableData()
}
</script>
