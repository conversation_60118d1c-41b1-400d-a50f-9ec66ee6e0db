<template>
  <!-- <el-drawer v-model="visible" title="缺失信息" size="700" class="integrality-drawer" @close="handleClosed"> -->
  <div class="integrality-container">
    <el-tabs v-model="currentTab">
      <!-- 病案完整性 -->
      <el-tab-pane label="病案完整性" name="mr" style="max-height: 100%; overflow-y: auto">
        <el-collapse v-model="activeNames">
          <!-- 首页完整性 -->
          <el-collapse-item name="home">
            <template #title="{ isActive }">
              <div class="form-group-header">
                <div class="title-bar"></div>
                <div>首页完整性验证</div>
              </div>
            </template>
            <!-- <el-table
              :data="homeIntegralityList"
              border
              :header-cell-style="{ background: '#f5f5f5' }"
              class="home-integrality-table"
            >
              <el-table-column prop="collectItem" label="问题大类"></el-table-column>
              <el-table-column prop="lackProject" label="缺陷项目"></el-table-column>
            </el-table> -->
            <div
              v-for="item in homeIntegralityList"
              :key="item.fieldName"
              class="integrality-item"
              style="border: none"
            >
              <div class="integrality-title" :style="{ color: item.qcResult === 'REGISTERED' ? '#f59a23' : '#f52f3e' }">
                *{{ item.ruleName }}
              </div>
              <div v-if="item.promptInfo" class="integrality-des">提示：{{ item.promptInfo }}</div>
            </div>
          </el-collapse-item>
          <!-- 文书完整性验证 -->
          <el-collapse-item name="file">
            <template #title="{ isActive }">
              <div class="form-group-header">
                <div class="title-bar"></div>
                <div>文书完整性验证</div>
              </div>
            </template>
            <div v-for="item in documentIntegralityList" :key="item.mrClassCode" class="integrality-item">
              <div class="integrality-title" :style="{ color: item.qcResult === 'REGISTERED' ? '#f59a23' : '#f52f3e' }">
                *{{ item.mrClassName }}
                <div v-if="item.qcResult === 'REGISTERED'" class="status">
                  <el-button size="small" type="success">已登记</el-button>
                </div>
              </div>
              <div class="integrality-des">{{ item.conditionHitDesc.join(",") }}</div>
              <div class="integrality-operation">
                <el-button
                  v-if="item.qcResult === 'NO_GATHER' || item.qcResult === 'REGISTERED'"
                  size="small"
                  type="primary"
                  plain
                  color="#3860F4"
                  @click="handleMissingReport(item)"
                >
                  缺失登记
                </el-button>
                <el-button
                  v-if="item.canSync"
                  size="small"
                  type="primary"
                  plain
                  color="#3860F4"
                  @click="handleSync(item)"
                >
                  同步
                </el-button>
                <el-button
                  v-if="item.canPhotocopy"
                  size="small"
                  type="primary"
                  plain
                  color="#3860F4"
                  @click="toCapture"
                >
                  拍摄
                </el-button>
              </div>
            </div>
            <div v-if="!documentIntegralityList.length" class="empty-integrality">暂无数据</div>
          </el-collapse-item>
          <!-- 缺失未提交报告列表 -->
          <el-collapse-item name="missing">
            <template #title="{ isActive }">
              <div class="form-group-header">
                <div class="title-bar"></div>
                <div>缺失未提交报告列表</div>
              </div>
            </template>
            <BaseTable
              border
              :data="missingReportList"
              :columns="missingReportTableColumns"
              class="mr-integrality-table"
            >
              <template #header>
                <el-button size="small" type="primary" plain color="#3860F4" @click="handleMissingReport({})">
                  缺失登记
                </el-button>
                <el-button size="small" type="primary" plain color="#3860F4" @click="toCapture">拍摄</el-button>
              </template>
              <template #predictDatetime="{ row }">
                {{ formatDate(row.predictDatetime) }}
              </template>
              <template #type="{ row }">
                <span v-if="row.type === MissingReportTypeEnum.ELECTRON">电子</span>
                <span v-else-if="row.type === MissingReportTypeEnum.PAPER">纸质</span>
              </template>
            </BaseTable>
          </el-collapse-item>
        </el-collapse>
      </el-tab-pane>

      <!-- 第三方质控数据 -->
      <el-tab-pane label="病案质控" name="qc">
        <div v-if="qualityControlInfo" style="color: #999">
          <span>质控评分：{{ qualityControlInfo?.score }}</span>
          <span style="margin-left: 10px">病案等级：{{ qualityControlInfo?.grade }}</span>
        </div>
        <el-table
          :data="qualityControlInfo?.qcInfo"
          border
          :header-cell-style="{ background: '#f5f5f5', position: 'sticky' }"
          class="qc-table"
        >
          <el-table-column prop="name" label="评分项目" />
          <el-table-column prop="desc" label="异常描述" />
        </el-table>
      </el-tab-pane>

      <!-- 签名完整性 -->
      <el-tab-pane label="签名验证" name="signature" style="max-height: 100%; overflow-y: auto">
        <el-collapse v-model="activeNames">
          <!-- 签名验签 -->
          <el-collapse-item name="integralityTable">
            <template #title="{ isActive }">
              <div class="form-group-header">
                <div class="title-bar"></div>
                <div>签名验签</div>
              </div>
            </template>
            <el-table
              :data="signatureIntegralityList"
              border
              :header-cell-style="{ background: '#f5f5f5', position: 'sticky' }"
              class="signature-integrality-table"
            >
              <el-table-column prop="fileInfoResult" label="分类名称">
                <template #default="{ row }">
                  {{ row.fileInfoResult.fileName }}
                </template>
              </el-table-column>
              <el-table-column prop="verifyResults" label="签名个数">
                <template #default="{ row }">
                  {{ row.verifyResults?.length || "--" }}
                </template>
              </el-table-column>
              <el-table-column prop="verifyResults" label="签名完成时间">
                <template #default="{ row }">
                  {{ formatDate(row.fileInfoResult?.fileLastSignDatetime) || "--" }}
                </template>
              </el-table-column>
              <el-table-column prop="verifyStatusLevel" label="验签">
                <template #default="{ row }">
                  <el-tag v-if="row.verifyStatusLevel === verifyStatusEnum.INFO" type="success">验证通过</el-tag>
                  <el-tag v-else-if="row.verifyStatus === 'FILE_UNSIGNED'" type="danger">未签名</el-tag>
                  <el-tag v-else-if="row.verifyStatus === 'FILE_INTEGRAL_ROOT_CERT_UNAUTHENTIC'" type="success">
                    验证通过
                  </el-tag>
                  <el-tag v-else-if="row.verifyStatusLevel === verifyStatusEnum.WARN" type="info">签名存在风险</el-tag>
                  <el-tag v-else-if="row.verifyStatusLevel === verifyStatusEnum.ERROR" type="danger">验证失败</el-tag>
                  <el-tag v-else type="info">未知</el-tag>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>

          <!-- 待签文书 -->
          <el-collapse-item name="unreceivedTable">
            <template #title="{ isActive }">
              <div class="form-group-header">
                <div class="title-bar"></div>
                <div style="display: flex; align-items: center">
                  待签文书
                  <el-popover
                    placement="right"
                    trigger="hover"
                    width="300"
                    :popper-style="{ background: 'rgba(0, 0, 0, 0.5)', color: '#fff' }"
                  >
                    <div class="tips-content">本次住院期间发起且未完成的文件</div>
                    <template #reference>
                      <el-icon style="position: absolute; left: 70px">
                        <QuestionFilled />
                      </el-icon>
                    </template>
                  </el-popover>
                </div>
              </div>
            </template>

            <el-table
              :data="unreceivedData"
              border
              :header-cell-style="{ background: '#f5f5f5', position: 'sticky' }"
              class="signature-integrality-table"
            >
              <el-table-column prop="fileName" label="文书名称">
                <template #default="{ row }">
                  {{ row.fileName }}
                </template>
              </el-table-column>
              <el-table-column prop="createdTime" label="发起时间">
                <template #default="{ row }">
                  {{ formatDate(row.createdTime) }}
                </template>
              </el-table-column>
              <el-table-column prop="toDoPerson" label="待签用户">
                <template #default="{ row }">
                  {{ row.toDoPerson }}
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>
        </el-collapse>
      </el-tab-pane>

      <!-- 提交备注 -->
      <el-tab-pane label="提交备注" name="submitRemark">
        <el-input v-model="submitRemarkValue" type="textarea" :rows="10" placeholder="请输入备注信息" clearable />
      </el-tab-pane>
    </el-tabs>

    <!-- </el-drawer> -->

    <!-- 缺失登记弹窗 -->
    <MissingReportDialog
      ref="missingReportDialogRef"
      :get-record-type="getRecordType"
      :selected-row="selectedRow"
      @success="checkIntegrality"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, computed } from "vue"
import { useRouter, useRoute } from "vue-router"
import { BaseTable, FormGroupHeader } from "@/base-components"
import { missingReportTableColumns, integralityEnum, verifyStatusEnum, type AllowMissingType } from "../config"
import MissingReportDialog from "./MissingReportDialog.vue"
import type { TableColumnCtx } from "element-plus"
import { MissingReportTypeEnum } from "@/configs"
import { useCommonOptions } from "@/hooks"
import {
  getAllDocTypes,
  checkIntegralityApi,
  checkSignIntegralityApi,
  sendModifyMessageApi,
  getQCInfoApi,
  getUnreceivedFileApi
} from "@/interfaces"
import { useSystemStore } from "@/stores"
import { SystemAlert, formatDate, toastError } from "@/utils"

const router = useRouter()
const route = useRoute()

const systemStore = useSystemStore()

const props = defineProps<{
  inpNo: string
  allowMissing: AllowMissingType
  submitRemark: string
  outHospitalDeptWsid: string
  wsid: string
}>()
const emits = defineEmits(["success", "sync", "update:submitRemark"])

const submitRemarkValue = computed({
  get: () => props.submitRemark,
  set: val => emits("update:submitRemark", val)
})

const currentTab = ref<"home" | "mr" | "sign" | "qc" | "submitRemark">("mr")
const documentIntegralityList = ref<any[]>([]) // 病案完整性
const signatureIntegralityList = ref<any[]>([]) // 签名完整性
const homeIntegralityList = ref<any[]>([]) // 首页完整性
const missingReportList = ref<any[]>([]) // 缺失报告表格
const qualityControlInfo = ref<Record<string, any>>()
const activeNames = ref(["home", "missing", "file"])
const unreceivedData = ref<any[]>([])

// 病案类型数据
const { getOptionLabel: getRecordType } = useCommonOptions({
  getOptionsApi: getAllDocTypes,
  labelAlias: "mrClassName",
  valueAlias: "mrClassCode"
})
const checkPass = ref(false)

// 检查完整性 TODO: 定西暂时取消待签文件完整性校验
const checkIntegrality = async () => {
  systemStore.pageLoading = true
  try {
    const { isCheckPass, documentIntegrityList, homePageIntegrityList, deletionDtoList } = await checkIntegralityApi(
      props.inpNo
    )
    checkPass.value = isCheckPass
    // 按照中文排序
    documentIntegralityList.value = documentIntegrityList
      // .sort((prev, next) => {
      //   return prev?.mrClassName?.localeCompare(next?.mrClassName, "zh")
      // })
      .filter(item => item.conditionHit && item.qcResult !== "COMPLETE")
    homeIntegralityList.value = homePageIntegrityList
    missingReportList.value = deletionDtoList

    activeNames.value = [
      homeIntegralityList.value?.length ? "home" : "",
      missingReportList.value?.length ? "missing" : "",
      documentIntegralityList.value?.length ? "file" : ""
    ]
  } catch (error: any) {
    console.log(`output->error`, error)
    systemStore.pageLoading = false
    toastError(error, "检查病案完整性失败！")
  }

  try {
    const { verifiers = [] } = await checkSignIntegralityApi(props.inpNo)
    signatureIntegralityList.value = verifiers
    if (checkPass.value && verifiers.every(signature => signature.verifyStatusLevel === "INFO")) {
      emits("success")
      systemStore.pageLoading = false
    }
  } catch (error: any) {
    console.log(`output->error`, error)
    systemStore.pageLoading = false
    toastError(error, "检查病案完整性失败！")
  }

  try {
    const _qualityControlInfo = await getQCInfoApi(props.inpNo)
    qualityControlInfo.value = _qualityControlInfo
  } catch (error: any) {
    console.log(`output->error`, error)
    systemStore.pageLoading = false
    toastError(error, "检查病案完整性失败！")
  }

  try {
    const resData = await getUnreceivedFileApi(props.inpNo)
    unreceivedData.value = resData?.data?.data
  } catch (error: any) {
    console.log(`output->error`, error)
    systemStore.pageLoading = false
    toastError(error, "检查病案完整性失败！")
  }

  systemStore.pageLoading = false
}

// 确认提交
const sendNotify = () => {
  systemStore.pageLoading = true
  sendModifyMessageApi(props.inpNo)
    .then(() => {
      systemStore.pageLoading = false
      SystemAlert("整改通知发送成功，将返回列表页面", "success").then(() => {
        router.replace("/medical-record/submission")
      })
    })
    .catch(error => {
      systemStore.pageLoading = false
      toastError(error, "发送通知失败！")
    })
}

// 关闭
const handleClosed = () => {
  currentTab.value = "home"
}

/* ===================== 合并表格 ======================= */

interface SpanMethodProps {
  row: any
  column: TableColumnCtx<any>
  rowIndex: number
  columnIndex: number
}

// 合并相同类的行
const mergeSameClassify = ({ row, column, rowIndex, columnIndex }: SpanMethodProps) => {
  if (columnIndex === 0) {
    // 获取上一行的分类名称值
    const prevValue = row[rowIndex - 1] ? row[rowIndex - 1].documentMrClassName : null
    // 若当前行的分类名称等于上一行的分类名称，则隐藏
    if (row.documentMrClassName === prevValue) {
      return { rowspan: 0, colspan: 0 }
    } else {
      const rowspan = documentIntegralityList.value.filter(integrality => {
        return integrality.documentMrClassName === row.documentMrClassName
      })?.length
      return { rowspan: rowspan, colspan: 1 }
    }
  }
}

/* =================== 展示 ====================== */

const visible = ref(false)

const show = () => {
  visible.value = true
}

const close = () => {
  visible.value = false
}

defineExpose({ checkIntegrality, show, close, documentIntegralityList, homeIntegralityList })

/* =================== 文书完整性验证操作 ====================== */

// 缺失登记
const selectedRow = ref<Record<string, any>>({})
const missingReportDialogRef = ref<InstanceType<typeof MissingReportDialog>>()
const handleMissingReport = row => {
  row.outHospitalDeptWsid = props.outHospitalDeptWsid
  row.wsid = props.wsid
  row.inpNo = props.inpNo
  selectedRow.value = row
  console.log(`output->selectedRow.value`, selectedRow.value)
  nextTick(() => {
    missingReportDialogRef.value?.show()
  })
}

// 同步
const handleSync = item => {
  emits("sync", item?.mrClassCode)
}

// 拍摄
const toCapture = item => {
  const queryString = Object.keys(route.query)
    .map(key => `${key}=${route.query[key]}`)
    .join("&")
  window.open(`/scan-system?${queryString}`)
}
</script>

<style lang="less" scoped>
.footer {
  position: fixed;
  bottom: 30px;
  right: 30px;
}

@media screen and (max-height: 720px) {
  .home-integrality-table {
    max-height: 60vh;
  }

  .mr-integrality-table {
    max-height: 25vh;
  }

  .signature-integrality-table {
    max-height: 60vh;
  }
}

@media screen and (min-height: 721px) and (max-height: 1080px) {
  .home-integrality-table {
    max-height: 70vh;
  }

  .mr-integrality-table {
    max-height: 30vh;
  }

  .signature-integrality-table {
    max-height: 70vh;
  }
}

@media screen and (min-height: 1081px) and (max-height: 1440px) {
  .home-integrality-table {
    max-height: 80vh;
  }

  .mr-integrality-table {
    max-height: 35vh;
  }

  .signature-integrality-table {
    max-height: 80vh;
  }
}
.home-integrality-table,
.mr-integrality-table,
.signature-integrality-table,
.qc-table {
  overflow-y: auto;
}
.form-group-header {
  height: 30px;
  font-weight: bold;
  font-size: 14px;
  color: #0a1633;
  line-height: 30px;
  display: flex;
  align-items: center;
  padding: 12px 0;
  font-weight: bold;
  .title-bar {
    height: 16px;
    width: 4px;
    background: #3860f4;
    margin-right: 8px;
  }
}

.integrality-container {
  height: 100%;
  overflow-x: auto;
  // width: 408px;
  // border-left: 1px solid #eee;
  padding: 0 18px;
}

.integrality {
  &-item {
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid #eaedf2;
    padding-bottom: 25px;
    padding-top: 16px;
    &:last-child {
      border-bottom: none;
      padding-bottom: 0;
    }
  }

  &-title {
    display: flex;
    align-items: center;
    width: 100%;
    font-weight: 500;
    font-size: 14px;
    color: #f52f3e;
    margin-bottom: 8px;
    line-height: 1;

    .status {
      margin-left: 30px;
    }
  }

  &-des {
    font-weight: 500;
    font-size: 12px;
    color: #545c71;
    margin-bottom: 12px;
  }

  &-operation {
    display: flex;
    justify-content: flex-end;
  }
}
.empty-integrality {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: rgb(144, 147, 153);
}
</style>
