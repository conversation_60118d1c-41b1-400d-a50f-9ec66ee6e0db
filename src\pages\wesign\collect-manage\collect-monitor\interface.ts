import axios from "@/interfaces/axios-instance"

/**
 * @method GET
 * @desc   采集记录-分页数据
 */
export function getCollectMonitorList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/collect/tasks`,
    params
  })
}

/**
 * @method PUT
 * @desc   采集记录-重新采集
 */
export function recaptureData(data) {
  return axios({
    method: "put",
    url: `/api/collect/tasks`,
    data,
    timeout: 60000
  })
}

/**
 * @method GET
 * @desc   翻拍日志-分页数据
 */
export function getRemakeList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/document/bags/remake/paging`,
    params
  })
}

/**
 * @method GET
 * @desc   第三方推送日志-分页数据
 */
export function getThirdPartyPushList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    // url: `/api/document/bags/third/paging`,
    url: `/api/collect/push-doc-records`,
    params
  })
}
