<template>
  <PageContainer>
    <TabsRouter :tabs-config="tabsRouterList" />
    <div v-loading="state.loading" style="overflow: hidden">
      <div class="function-config">
        <div class="config-item">
          <el-form ref="formRef" :model="formData" label-position="left" label-width="220px">
            <FormGroupHeader title="病案迟归"></FormGroupHeader>
            <el-form-item label="病人出院时间超过：" prop="exceedDischargeTime" :rules="intRules">
              <el-input v-model="formData.exceedDischargeTime" type="number" step="">
                <template #append>天</template>
              </el-input>
              <span class="describe">（病人出院超过N天，住院病案未完成归档）</span>
            </el-form-item>
            <el-divider />

            <FormGroupHeader title="病案目录"></FormGroupHeader>
            <el-form-item label="病案目录树是否显示文书分类：" prop="isShowTreeMrClass" :rules="requiredRules">
              <el-radio-group v-model="formData.isShowTreeMrClass">
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-divider />

            <FormGroupHeader title="导航"></FormGroupHeader>
            <el-form-item label="窗口导航：" prop="isShowTreeMrClass" :rules="requiredRules">
              <el-radio-group v-model="formData.menuNavigation">
                <el-radio label="1">多窗口</el-radio>
                <el-radio label="0">面包屑</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-divider />

            <FormGroupHeader title="病案召回"></FormGroupHeader>
            <el-form-item label="患者已打印病案是否允许召回：" prop="printRecall" :rules="requiredRules" class="recall">
              <el-radio-group v-model="formData.printRecall">
                <el-radio label="YES">是</el-radio>
                <el-radio label="NO">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-divider />

            <FormGroupHeader title="病案封存"></FormGroupHeader>
            <el-form-item label="病案封存是否可借阅：" prop="sealedLoan" :rules="requiredRules">
              <el-radio-group v-model="formData.sealedLoan">
                <el-radio label="YES">是</el-radio>
                <el-radio label="NO">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="病案封存是否可打印：" prop="sealedPrint" :rules="requiredRules">
              <el-radio-group v-model="formData.sealedPrint">
                <el-radio label="YES">是</el-radio>
                <el-radio label="NO">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="病案封存申请是否签名：" prop="sealedApplySign" :rules="requiredRules">
              <el-radio-group v-model="formData.sealedApplySign">
                <el-radio label="YES">是</el-radio>
                <el-radio label="NO">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="病案封存签章：" class="flex-wrap" :rules="requiredRules">
              <div class="img-container" @click="handleUpload('sealedSeal')">
                <img v-if="imagesSrc['sealedSeal']" class="seal-img" :src="imagesSrc['sealedSeal']" />
                <div v-else class="img-container-desc">
                  <i class="ri-add-line add-btn"></i>
                  <span>上传图片</span>
                </div>
              </div>
              <div class="tips">支持类型PNG，尺寸200*200</div>
            </el-form-item>

            <el-divider />

            <!-- <FormGroupHeader title="病案归档"></FormGroupHeader>
            <el-form-item label="病案归档签章：" class="flex-wrap" :rules="requiredRules">
              <div class="img-container" @click="handleUpload('archiveSeal')">
                <img v-if="imagesSrc['archiveSeal']" class="seal-img" :src="imagesSrc['archiveSeal']" />
                <div v-else class="img-container-desc">
                  <i class="ri-add-line add-btn"></i>
                  <span>上传图片</span>
                </div>
              </div>
              <div class="tips">支持类型PNG，尺寸200*200</div>
            </el-form-item> -->

            <!-- <el-divider /> -->

            <FormGroupHeader title="借阅管理"></FormGroupHeader>
            <el-form-item label="允许借阅病案保密等级：" prop="borrowSecrecyGrade">
              <el-select
                v-model="formData.borrowSecrecyGrade"
                class="select-wrapper"
                multiple
                clearable
                :collapse-tags="true"
                :collapse-tags-tooltip="true"
                @change="value => changeSecretGrade(value, 'borrowSecrecyGrade')"
              >
                <el-option
                  v-for="item in state.secrecyGradeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="不允许借阅特殊病案属性：" prop="borrowSpecialProperty">
              <el-checkbox-group v-model="formData.borrowSpecialProperty">
                <el-checkbox v-for="item in specialPropertyOptions" :key="item.key" :label="item.key">
                  {{ item.value }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="缺失病案是否可借阅：" prop="borrowDeletionStatus" :rules="requiredRules">
              <el-radio-group v-model="formData.borrowDeletionStatus">
                <el-radio label="YES">是</el-radio>
                <el-radio label="NO">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="死亡病案是否可借阅：" prop="borrowDeathStatus" :rules="requiredRules">
              <el-radio-group v-model="formData.borrowDeathStatus">
                <el-radio label="YES">是</el-radio>
                <el-radio label="NO">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="单次最多可借阅病案：" prop="maxBorrowNumber" :rules="intRules">
              <el-input v-model="formData.maxBorrowNumber" type="number" :step="1">
                <template #append>份</template>
              </el-input>
            </el-form-item>
            <el-form-item label="借阅审批超过：" prop="borrowAuditDay" :rules="intRules">
              <el-input v-model="formData.borrowAuditDay" type="number" :step="1">
                <template #append>天</template>
              </el-input>
              <span class="describe">（借阅审批超过N天未审批，审批逾期）</span>
            </el-form-item>

            <el-divider />

            <FormGroupHeader title="病案打印"></FormGroupHeader>
            <el-form-item label="订单编号规则：" prop="printSecrecyGrade">
              <el-select
                v-model="formData.printOrderPrefix"
                class="select-wrapper"
                placeholder="日期样式"
                clearable
                :collapse-tags="true"
                :collapse-tags-tooltip="true"
              >
                <el-option
                  v-for="item in state.printOrderPrefixOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-select
                v-model="formData.printOrderSuffix"
                class="select-wrapper"
                placeholder="流水号"
                clearable
                :collapse-tags="true"
                :collapse-tags-tooltip="true"
              >
                <el-option
                  v-for="item in state.printOrderSuffixOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="允许打印病案保密等级：" prop="printSecrecyGrade">
              <el-select
                v-model="formData.printSecrecyGrade"
                class="select-wrapper"
                multiple
                clearable
                :collapse-tags="true"
                :collapse-tags-tooltip="true"
                @change="value => changeSecretGrade(value, 'printSecrecyGrade')"
              >
                <el-option
                  v-for="item in state.secrecyGradeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="打印病案数：" prop="maxPrintDocumentBag" :rules="intRules">
              <el-input v-model="formData.maxPrintDocumentBag" type="number" :step="1">
                <template #append>份</template>
              </el-input>
              <span class="describe">（单次申请打印住院病案数，按患者编号区分）</span>
            </el-form-item>
            <el-form-item label="打印份最多：" prop="maxPrintDuplicateNum" :rules="intRules">
              <el-input v-model="formData.maxPrintDuplicateNum" type="number" :step="1">
                <template #append>份</template>
              </el-input>
              <span class="describe">（单个申请允许最多打印份）</span>
            </el-form-item>
            <el-form-item label="复印费：" prop="printAmount" :rules="priceRules">
              <el-input v-model="formData.printAmount" type="number" :step="1">
                <template #append>元/张</template>
              </el-input>
            </el-form-item>
            <el-form-item label="自取有效期：" prop="ownTakeValidity" :rules="intRules">
              <el-input v-model="formData.ownTakeValidity" type="number" :step="1">
                <template #append>份</template>
              </el-input>
            </el-form-item>
            <el-form-item label="邮寄完成状态更改：" prop="mailComplete" :rules="intRules">
              <el-input v-model="formData.mailComplete" type="number" :step="1">
                <template #append>天</template>
              </el-input>
              <span class="describe">
                （系统推送取件码之日起，保留N天，逾期未领取将做销毁处理。
                病案寄出之日起，连续N日后，订单状态变为已完成）
              </span>
            </el-form-item>
            <el-form-item label="电子分发有效期：" prop="electronicDistributionValidity" :rules="intRules">
              <el-input v-model="formData.electronicDistributionValidity" type="number" :step="1">
                <template #append>天</template>
              </el-input>
              <span class="describe">（逾期系统自动清理，需要重新申请）</span>
            </el-form-item>
            <el-form-item label="打印病案水印" prop="printWatermarkType" :rules="watermarkRules">
              <el-radio-group
                v-model="formData.printWatermarkType"
                class="ml-4"
                @change="formData.printWatermarkContent = ''"
              >
                <el-radio label="PATIENT_NAME" size="large">
                  <div class="custom-print">患者姓名</div>
                </el-radio>
                <el-radio label="CUSTOM" size="large">
                  <div class="custom-print">
                    自定义内容
                    <el-input
                      v-model="formData.printWatermarkContent"
                      :disabled="formData.printWatermarkType !== 'CUSTOM'"
                      style="width: 200px"
                    />
                  </div>
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="打印病案签章：" prop="printSignConfig" :rules="requiredRules">
              <el-checkbox-group v-model="formData.printSignConfig">
                <el-checkbox label="PAPER">物理打印（医院打印纸质病案）</el-checkbox>
                <el-checkbox label="DISTRIBUTE">电子分发</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="" class="flex-wrap" :rules="requiredRules">
              <div class="img-container" @click="handleUpload('printSeal')">
                <img v-if="imagesSrc['printSeal']" class="seal-img" :src="imagesSrc['printSeal']" />
                <div v-else class="img-container-desc">
                  <i class="ri-add-line add-btn"></i>
                  <span>上传图片</span>
                </div>
              </div>
              <div class="tips">支持类型PNG，尺寸200*200</div>
            </el-form-item>

            <el-divider />

            <FormGroupHeader title="病案封面"></FormGroupHeader>
            <el-form-item label="病案封面：" class="flex-wrap">
              <div class="img-container" @click="handleUpload('coverImage')">
                <img v-if="imagesSrc['coverImage']" :src="imagesSrc['coverImage']" />
                <div v-else class="img-container-desc">
                  <i class="ri-add-line add-btn"></i>
                  <span>上传图片</span>
                </div>
              </div>
              <div class="tips">支持类型JPG、JPEG、PNG，尺寸A4纸大小</div>
            </el-form-item>
            <el-form-item label="二维码位置：" prop="coverQRcodePosition" :rules="requiredRules">
              <el-radio-group v-model="formData.coverQRcodePosition" @change="setCoverQRcodeSize">
                <el-radio v-for="item in coverQRcodePositionOptions" :key="item.value" :label="item.value">
                  {{ item.label }}
                </el-radio>
              </el-radio-group>

              <div class="QR-code-container img-container">
                <img v-if="imagesSrc['coverImage']" :src="imagesSrc['coverImage']" />
                <div ref="QRcodeRef" class="QR-code" :style="QRcodePositionStyle[formData.coverQRcodePosition]">
                  <img src="@/assets/png/QR-code.png" />
                </div>
              </div>
            </el-form-item>
            <el-form-item label="二维码尺寸：" prop="coverQRcodeSize" :rules="priceRules">
              <el-input v-model="formData.coverQRcodeSize" type="number" max="500" min="0" @input="setCoverQRcodeSize">
                <template #append>点</template>
              </el-input>
              <span class="describe">（最大可设为500点）</span>
            </el-form-item>

            <FormGroupHeader title="病案导出"></FormGroupHeader>
            <el-form-item label="病案导出PDF加盖骑缝章：" prop="exportStraddleStamp" :rules="requiredRules">
              <el-checkbox-group v-model="formData.exportStraddleStamp">
                <el-checkbox v-for="item in exportStraddleStampOptions" :key="item.value" :label="item.value">
                  {{ item.label }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>

            <el-form-item label="骑缝章印模：" class="flex-wrap">
              <div class="paging-seal" @click="handleUpload('straddleStampImg')">
                <img v-if="imagesSrc['straddleStampImg']" :src="imagesSrc['straddleStampImg']" />
                <div v-else class="img-container-desc">
                  <i class="ri-add-line add-btn"></i>
                  <span>上传图片</span>
                </div>
              </div>
              <div class="tips">支持类型PNG，尺寸200*200</div>
            </el-form-item>

            <el-form-item label="骑缝章位置：" prop="straddleStampLocation" :rules="requiredRules">
              <el-radio-group v-model="formData.straddleStampLocation" @change="setCoverQRcodeSize">
                <el-radio v-for="item in straddleStampLocationOptions" :key="item.value" :label="item.value">
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
              <div class="QR-code-container img-container">
                <img v-if="imagesSrc['straddleStampImg']" src="@/assets/png/paging-seal-file.png" />
                <div class="QR-code" :style="straddleStampLocationStyle[formData.straddleStampLocation]">
                  <img :src="imagesSrc['straddleStampImg']" />
                </div>
              </div>
            </el-form-item>

            <!-- <el-form-item label="自动导出上报数据开关:" prop="outpatientHqmsExportAutoSwitch" :rules="requiredRules">
              <el-radio-group v-model="formData.outpatientHqmsExportAutoSwitch">
                <el-radio label="1">开启</el-radio>
                <el-radio label="0">关闭</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="IP地址:" prop="outpatientHqmsExportIp" :rules="requiredRules">
              <el-input v-model="formData.outpatientHqmsExportIp" />
            </el-form-item>

            <el-form-item label="目录地址:" prop="outpatientHqmsExportAutoSwitch" :rules="requiredRules">
              <el-input v-model="formData.outpatientHqmsExportDirectory" />
            </el-form-item> -->
          </el-form>
        </div>
        <div class="btn-container">
          <el-button type="primary" @click="save">保存</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </div>
    </div>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, onMounted, ref } from "vue"
import { cloneDeep } from "lodash-es"
import { PageContainer, FormGroupHeader, CommonSelectFormItem } from "@/base-components"
import { TabsRouter } from "@/page-components"
import { tabsRouterList, QRcodePositionStyle, straddleStampLocationStyle } from "./config"
import type { BaseOptionItem } from "@/types"
import {
  getImage,
  uploadSystemFile,
  getApproveReasonApi,
  getSecreciesList,
  querySystemConfig,
  updateSystemConfig
} from "@/interfaces"
import { Message, selectFile, blobToBase64, toastError } from "@/utils"
interface FunctionConfigFormData {
  exceedDischargeTime: number
  sealedLoan: string
  sealedPrint: string
  // loanableStatus: []
  maxBorrowNumber: number
  borrowAuditDay: number
  maxPrintDocumentBag: number
  maxPrintDuplicateNum: number
  printAmount: number
  ownTakeValidity: number
  mailComplete: number
  electronicDistributionValidity: number
  printSecrecyGrade: Array<string> // 打印保密等级
  borrowSecrecyGrade: Array<string> // 借阅保密等级
  borrowSpecialProperty: Array<string> // 借阅特殊属性
  borrowDeletionStatus: string // 缺失病案是否可借阅
  borrowDeathStatus: string // 死亡病案是否可借阅
  coverImage: string
  printWatermarkType: string
  printWatermarkContent: string
  sealedSeal: string
  archiveSeal: string
  printSeal: string
  printSignConfig: Array<string> // 打印病案签章
  sealedApplySign: string
  printRecall: string
  isShowTreeMrClass: string
  printOrderPrefix: string //订单编号规则前缀
  printOrderSuffix: string //订单编号规则后缀
  menuNavigation: string // 菜单导航控制
  coverQRcodePosition: string //二维码位置
  coverQRcodeSize: number //二维码尺寸
  exportStraddleStamp: string //导出PDF加盖骑缝章
  straddleStampImg: string //骑缝章图片
  straddleStampLocation: string //骑缝章位置
  outpatientHqmsExportAutoSwitch: string //自动导出上报数据开关
  outpatientHqmsExportIp: string //IP地址
  outpatientHqmsExportDirectory: string //目录地址
}
const formData = reactive<FunctionConfigFormData>({
  exceedDischargeTime: 0,
  sealedLoan: "NO",
  sealedPrint: "NO",
  // loanableStatus: [],
  maxBorrowNumber: 0,
  borrowAuditDay: 0,
  maxPrintDocumentBag: 0,
  maxPrintDuplicateNum: 0,
  printAmount: 0,
  ownTakeValidity: 0,
  mailComplete: 0,
  electronicDistributionValidity: 0,
  printSecrecyGrade: [],
  borrowSecrecyGrade: [],
  borrowSpecialProperty: [],
  borrowDeletionStatus: "NO",
  borrowDeathStatus: "NO",
  coverImage: "",
  printWatermarkType: "PATIENT_NAME",
  printWatermarkContent: "",
  // 系统印章
  sealedSeal: "",
  archiveSeal: "",
  printSeal: "",
  printSignConfig: [],
  sealedApplySign: "",
  printRecall: "",
  isShowTreeMrClass: "",
  printOrderPrefix: "",
  printOrderSuffix: "",
  menuNavigation: "",
  coverQRcodePosition: "",
  coverQRcodeSize: 0,
  exportStraddleStamp: "",
  straddleStampImg: "",
  straddleStampLocation: "",
  outpatientHqmsExportAutoSwitch: "",
  outpatientHqmsExportIp: "",
  outpatientHqmsExportDirectory: ""
})
interface StateType {
  loading: boolean
  data: Array<{
    key: string
    value?: any
  }>
  secrecyGradeOptions: Array<BaseOptionItem>

  printOrderPrefixOptions: Array<BaseOptionItem>
  printOrderSuffixOptions: Array<BaseOptionItem>
}
const state = reactive<StateType>({
  loading: true,
  data: [],
  secrecyGradeOptions: [],
  printOrderPrefixOptions: [],
  printOrderSuffixOptions: []
})

// 印章、封面
const imagesSrc = reactive({
  sealedSeal: "",
  archiveSeal: "",
  printSeal: "",
  coverImage: "",
  straddleStampImg: ""
})

// 特殊病案属性源数据
const specialPropertyOptions = ref<Array<Record<string, any>>>([])
const coverQRcodePositionOptions = ref<Array<Record<string, any>>>([])

// 导出PDF加盖骑缝章
const exportStraddleStampOptions = ref<Array<Record<string, any>>>([])
// 骑缝章位置
const straddleStampLocationOptions = ref<Array<Record<string, any>>>([])

onMounted(async () => {
  try {
    await querySystemConfig({ type: "FUNCTION" }).then(res => {
      res.data.data?.forEach(item => {
        if (
          item.key === "sealedLoan" ||
          item.key === "sealedPrint" ||
          item.key === "sealedApplySign" ||
          item.key === "borrowDeathStatus" ||
          item.key === "borrowDeletionStatus" ||
          item.key === "printRecall"
        ) {
          formData[item.key] = item.value || "NO"
        } else if (item.key === "exportStraddleStamp") {
          formData[item.key] = item.value.split(",")
          exportStraddleStampOptions.value = item.optionalValue.split(",").map(item => {
            return { label: item.split("|")[1], value: item.split("|")[0] }
          })
        } else if (item.key === "straddleStampLocation") {
          straddleStampLocationOptions.value = item.optionalValue.split(",").map(item => {
            return { label: item.split("|")[1], value: item.split("|")[0] }
          })
          formData[item.key] = item.value
        } else if (item.key === "straddleStampImg" && item.value) {
          getImage(item.value).then(res => {
            imagesSrc[item.key] = res
            formData[item.key] = item.value
          })
        } else if (item.key === "borrowSpecialProperty" || item.key === "borrowSecrecyGrade") {
          formData[item.key] = item.value?.split(",").filter(item => item) || []
        } else if (item.value) {
          formData[item.key] = Number(item.value)
        }
      })
    })
    await querySystemConfig({ type: "PRINT" }).then(res => {
      res.data.data?.forEach(item => {
        if (item.key === "printSecrecyGrade" || item.key === "printSignConfig")
          formData[item.key] = item?.value.split(",").filter(item => item) || []
        else if (item.key === "printWatermarkContent" || item.key === "printWatermarkType") {
          formData[item.key] = item.value
        } else if (item.key === "printOrderPrefix" || item.key === "printOrderSuffix") {
          formData[item.key] = item.value
          state[`${item.key}Options`] = item.optionalValue.split(",").map(item => {
            return { label: item, value: item }
          })
        } else if (item.value) {
          formData[item.key] = Number(item.value)
        }
      })
    })
    await querySystemConfig({ type: "ARCHIVIST" }).then(res => {
      res.data.data?.forEach((item: Record<string, any>) => {
        if (item.key === "coverImage" && item.value) {
          getImage(item.value).then(res => {
            imagesSrc[item.key] = res
            formData[item.key] = item.value
          })
        } else if (item.key === "coverQRcodePosition") {
          coverQRcodePositionOptions.value = item.optionalValue.split(",").map(item => {
            return { label: item.split("|")[1], value: item.split("|")[0] }
          })
          formData[item.key] = item.value
        } else if (item.value) {
          formData[item.key] = Number(item.value)
        }
      })
    })
    await querySystemConfig({ type: "SEAL_IMAGE" }).then(res => {
      res.data.data?.forEach((item: Record<string, any>) => {
        if (item.value) {
          getImage(item.value).then(res => {
            imagesSrc[item.key] = res
            formData[item.key] = item.value
          })
        }
      })
    })
    await querySystemConfig({ type: "EMR_TREE" }).then(res => {
      res.data.data?.forEach((item: Record<string, any>) => {
        formData[item.key] = item.value
      })
    })
    await querySystemConfig({ type: "MENU_NAVIGATION" }).then(res => {
      res.data.data?.forEach((item: Record<string, any>) => {
        formData[item.key] = item.value
      })
    })
    // await querySystemConfig({ type: "OUTPATIENT_HQMS" }).then(res => {
    //   res.data.data?.forEach((item: Record<string, any>) => {
    //     formData[item.key] = item.value
    //   })
    // })

    // 获取特殊病案属性字典
    await getApproveReasonApi({ groupKey: "BAG_SPECIAL_PROPERTY" }).then(resData => {
      specialPropertyOptions.value = cloneDeep(resData)
    })

    state.secrecyGradeOptions = (await getSecreciesList()).data.data.map(item => {
      return { label: item.secrecyName, value: item.secrecyValue }
    })
    setCoverQRcodeSize(formData.coverQRcodeSize)
    state.loading = false
  } catch (err: any) {
    console.log(`output->err`, err)
    toastError(err, "加载数据失败")
  }
})

const reset = () => {
  for (let key in formData) {
    if (key === "sealedLoan" || key === "sealedPrint") formData[key] = "NO"
    else formData[key] = ""
    state.data.push({ key: key, value: formData[key] })
  }
}

const getSingle = (array1: Array<string>, array2: Array<string>) => {
  const set1 = new Set(array1)
  const set2 = new Set(array2)
  const diff1 = [...set1].filter(item => !set2.has(item))
  const diff2 = [...set2].filter(item => !set1.has(item))
  return [...diff1, ...diff2]
}

/*=====================病案打印保密等级\借阅保密等级=====================*/
const lastValue = ref([] as any)
const changeSecretGrade = (value, key) => {
  // 判断是勾选还是取消勾选
  const isCheck = value.length > lastValue.value.length
  // 每次勾选或取消勾选的选项
  const single = getSingle(cloneDeep(value), cloneDeep(lastValue.value))
  // 勾选要把所有等级比自己大的都勾上
  if (isCheck) {
    formData[key] = state.secrecyGradeOptions
      .filter(item => Number(single[0]) <= Number(item.value))
      .map(item => item.value as string)
  } else {
    // 取消勾选只保留等级比自己大的
    formData[key] = state.secrecyGradeOptions
      .filter(item => Number(single[0]) < Number(item.value))
      .map(item => item.value as string)
  }
  // 以每次勾选完的结果和下一次勾选时的value比较
  lastValue.value = cloneDeep(formData[key])
}

/*=====================病案封面=====================*/

function handleUpload(key) {
  const type = key === "coverImage" ? ".jpg,.jpeg,.png,.svg" : ".png"
  selectFile(type, 1024 * 1024 * 30)
    .then(async file => {
      const reader = new FileReader()
      reader.readAsDataURL(file as Blob)
      reader.onload = function (e) {
        const img = new Image()
        img.src = e.target?.result as string
        img.onload = async function () {
          imagesSrc[key] = (await blobToBase64(file as Blob)) as string
          uploadSystemFile({ file: file })
            .then(res => {
              formData[key] = res.data.data.fileWsid
              Message.success("上传图片成功")
            })
            .catch(error => {
              toastError(error, "上传图片失败")
            })
        }
      }
    })
    .catch(err => {
      if (err.message === "ERROR_FILE_TYPE") {
        Message.error("选择的文件类型错误，请选择正确的文件")
      } else if (err.message === "ERROR_FILE_SIZE") {
        Message.error("文件的大小超过限制")
      }
    })
}
// 二维码尺寸（px）
const QRcodeRef = ref()
const QRcodeSize = ref(0)
const setCoverQRcodeSize = val => {
  if (val > 500) {
    formData.coverQRcodeSize = 500
  }
  // 当前设备的dpi
  const devicePixelRatio = window.devicePixelRatio || 1
  // 210：当前页面图片宽度px  595.27：A4的宽度pt
  const currentScale = 210 / (595.27 * ((devicePixelRatio * 96) / 72))
  // 根据输入的值(pt)计算对应的二维码的尺寸(px)
  QRcodeSize.value = ((devicePixelRatio * 96) / 72) * formData.coverQRcodeSize * currentScale
  QRcodeRef.value.style.width = QRcodeSize.value + "px"
  QRcodeRef.value.style.height = QRcodeSize.value + "px"
}

/*=====================保存数据=====================*/
// 校验
const intRules = [
  { required: true, message: "该数据不可为空！", trigger: "change" },
  { validator: intValidator, trigger: "change" }
]

const priceRules = [
  { required: true, message: "该数据不可为空！", trigger: "change" },
  { validator: priceValidator, trigger: "change" }
]

const requiredRules = [{ required: true, message: "该数据不可为空！", trigger: "change" }]

const watermarkRules = [
  { required: true, message: "该数据不可为空！", trigger: "change" },
  { validator: watermarkValidator, trigger: "change" }
]

function intValidator(rule, value, callback) {
  if (value < 1) return callback(new Error("该数据最小值为1！"))
  if (value % 1 !== 0) return callback(new Error("该数据为整数"))
  return callback()
}
function priceValidator(rule, value, callback) {
  if (value <= 0) return callback(new Error("该数据必须大于0！"))
  return callback()
}

function watermarkValidator(rule, value, callback) {
  if (value === "CUSTOM") {
    if (formData.printWatermarkContent) return callback()
    else return callback(new Error("请输入自定义水印内容"))
  }
  return callback()
}

const formRef = ref()

const save = () => {
  if (!formRef.value) return
  formRef.value.validateField().then(() => {
    if (state.loading) return
    state.loading = true
    for (let key in formData) {
      if (
        key === "printSecrecyGrade" ||
        key === "printSignConfig" ||
        key === "borrowSecrecyGrade" ||
        key === "borrowSpecialProperty" ||
        key === "exportStraddleStamp"
      ) {
        const data = formData[key].join(",")
        state.data.push({ key: key, value: data })
      } else {
        state.data.push({ key: key, value: formData[key] })
      }
    }
    updateSystemConfig(state.data)
      .then(() => {
        Message.success("保存成功")
        state.data = []
        window.location.reload()
      })
      .catch(error => {
        if (error.response.errMessage) {
          Message.error(`${error.response.errMessage}`)
        } else {
          Message.error("保存失败")
        }
        state.data = []
      })
      .finally(() => {
        state.loading = false
      })
  })
}
</script>

<style lang="less" scoped>
.function-config {
  position: relative;
  overflow-y: scroll;
  height: calc(100% - 200px);
  padding-right: 20px;
  padding-bottom: 100px;
  :deep(.el-input) {
    width: 20%;
    // 隐藏number的原生箭头
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
      -webkit-appearance: none;
    }
    input[type="number"] {
      -moz-appearance: textfield;
    }

    .el-input__inner {
      text-align: left;
    }

    .input-number-controls {
      display: flex;
      flex-direction: column;
      justify-content: center;
      font-size: 12px;
      height: 100%;
      line-height: 1;
      i {
        flex: 1;
        display: flex;
        align-items: center;
        padding: 0px 5px;
        color: #a8abb2;
        cursor: pointer;
      }
      i:first-child {
        border-bottom: 1px solid #a8abb2;
      }
    }
  }

  :deep(.el-form) {
    padding-left: 28px;
  }

  :deep(.el-form-item__label) {
    // justify-content: flex-end;
    // width: 180px;
  }

  .config-item {
    width: 100%;
    // padding: 40px 0;
    padding-top: 40px;
    padding-bottom: 20px;

    .config-item-title {
      font-weight: bold;
      margin-left: -25px;
      padding-bottom: 10px;
    }
    .el-divider--horizontal {
      width: calc(100% + 25px);
      margin: 30px 0px 40px -25px;
      border-color: #ebedf0;
    }

    .describe {
      font-size: 12px;
      color: #7f8997;
    }

    .custom-print {
      height: 32px;
      display: flex;
      align-items: center;
      column-gap: 10px;
    }

    .flex-wrap {
      :deep(.el-form-item__content) {
        flex-direction: column;
        align-items: start;
      }
    }

    .img-container {
      border: 1px dashed var(--el-border-color);
      padding: 5px;
      height: fit-content;
      line-height: 1;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 100px;
      min-height: 100px;
      box-sizing: border-box;
      background: rgba(56, 96, 244, 0.2);

      img {
        width: 210px;
        height: 297px;
        object-fit: contain;
      }

      .seal-img {
        width: 200px;
        height: 200px;
        object-fit: contain;
      }

      .add-btn {
        width: 24px;
        height: 24px;
        font-size: 24px;
      }
    }
  }
}
.form-group-header {
  margin-bottom: 14px;
}

.select-wrapper {
  :deep(.el-input) {
    width: auto !important;
  }
}

.recall {
  :deep(.el-form-item__label) {
    width: 220px !important;
  }
}
.btn-container {
  position: fixed;
  bottom: 60px;
  padding-left: 28px;
}

.QR-code-container {
  position: absolute;
  right: 10vw;
  bottom: 0;

  .QR-code {
    width: 50px;
    height: 50px;
    position: absolute;
    z-index: 999 !important;
    img {
      width: 100% !important;
      height: 100% !important;
    }
  }
}

.paging-seal {
  width: 200px;
  height: 200px;
  cursor: pointer;
  border: 1px dashed var(--el-border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    object-fit: scale-down;
    width: 100%;
    height: 100%;
  }
}
.img-container-desc {
  display: flex;
  flex-direction: column;
  align-items: center;
  row-gap: 5px;
  color: #999;
}
</style>
