<template>
  <div class="data-collect-chart-container">
    <div class="chart-title"></div>
    <div class="data-collect-header">
      <el-button
        v-for="dateOption in dateOptions"
        :key="dateOption.value"
        class="date-option"
        :type="selectedDateOption === dateOption.value ? 'primary' : 'default'"
        size="small"
        @click="handleSelectDateOption(dateOption.value)"
      >
        {{ dateOption.label }}
      </el-button>
      <el-date-picker
        v-model="selectedDateRange"
        style="margin-left: 12px"
        type="daterange"
        unlink-panels
        size="small"
        start-placeholder="开始日期"
        range-separator="至"
        end-placeholder="结束日期"
        :shortcuts="shortcuts"
        value-format="YYYY-MM-DD"
        @change="handleDateRangeChange"
      />
    </div>
    <div class="chart-container">
      <v-chart class="data-collect-chart" :option="option" autoresize />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, provide, onMounted, computed, onBeforeUnmount, watch } from "vue"
import { BarChart } from "echarts/charts"
import { TooltipComponent, GridComponent, LegendComponent } from "echarts/components"
import { use } from "echarts/core"
import { CanvasRenderer } from "echarts/renderers"
import VChart, { THEME_KEY } from "vue-echarts"
import { shortcuts, StatisticTypeEnum } from "@/configs"
import { dateOptions, getRangeDateByType, noDataOption } from "../config"
import { getNormalCollectDataApi } from "../interface"

/* ===================== 统计数据处理 ===================== */

// 统计数据刷新时间间隔
const intervalId = ref()

onMounted(() => {
  intervalId.value = setInterval(getNormalCollectData, 300000)
})

onBeforeUnmount(() => {
  clearInterval(intervalId.value)
})

// 数据采集所选时间选项
const selectedDateOption = ref(dateOptions[0].value)

// 统计数据时间范围
const selectedDateRange = ref<[string, string]>(getRangeDateByType(dateOptions[0].value as StatisticTypeEnum) as any)

// y轴
const yAxisData = ref<Array<string>>([])

// 对应数据
const seriesData = ref<Array<number>>([])

// 监听日期选项变化
watch(
  () => selectedDateRange.value,
  () => {
    getNormalCollectData()
  },
  { immediate: true }
)

// 获取采集数据
function getNormalCollectData() {
  const requestParams = { startDate: selectedDateRange.value[0], endDate: selectedDateRange.value[1] }
  getNormalCollectDataApi(requestParams).then(res => {
    const resData = res.data.data
    let yAxisDataTemp: Array<string> = []
    let seriesDataTemp: Array<number> = []
    resData.forEach(item => {
      yAxisDataTemp.push(item.systemName)
      seriesDataTemp.push(item.count)
    })
    yAxisData.value = yAxisDataTemp
    seriesData.value = seriesDataTemp
  })
}

// 选择时间范围选项
const handleSelectDateOption = (value: StatisticTypeEnum | "") => {
  const startTime = getRangeDateByType(value)
  selectedDateOption.value = value
  selectedDateRange.value = startTime as any
}

// 改变picker数据日期
const handleDateRangeChange = (dateRange: [string, string]) => {
  selectedDateOption.value = ""
  selectedDateRange.value = dateRange
}

/* ===================== 数据采集柱状图配置项 ===================== */

use([TooltipComponent, GridComponent, LegendComponent, BarChart, CanvasRenderer])
provide(THEME_KEY, "dark")

const option = computed(() => {
  if (!seriesData.value.length) return noDataOption.value // 没有数据时的配置项
  return dataOption.value
})

const dataOption = ref({
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "shadow"
    }
  },
  grid: {
    left: "3%",
    right: "4%",
    bottom: "3%",
    containLabel: true
  },
  xAxis: {
    type: "value"
  },
  yAxis: {
    type: "category",
    data: yAxisData
  },
  series: [
    {
      type: "bar",
      data: seriesData,
      itemStyle: {
        color: "#1CF1FF"
      }
    }
  ]
})
</script>

<style lang="less" scoped>
.data-collect-chart-container {
  width: 28%;
  min-width: 458px;
  margin-top: 20px;
  .chart-title {
    width: 100%;
    height: 43px;
    background: url("@/assets/svg/monitor/collect-report.svg") no-repeat;
    background-size: 100%;
  }
  .data-collect-header {
    margin-top: 15px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  .chart-container {
    margin-top: 15px;
    .data-collect-chart {
      width: 100%;
      height: 400px;
    }
  }
}
</style>
