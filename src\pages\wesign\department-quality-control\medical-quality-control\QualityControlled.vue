<template>
  <PageContainer separate>
    <!-- 头部搜索 -->
    <template #search>
      <TabsRouter :tabs-config="medicalQcTabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
        />

        <VisitIdFormItem
          v-model:input-value="searchFormState.visitIdCount"
          v-model:select-value="searchFormState.visitIdSymbol"
          label="住院次数"
        />

        <CountRangeFormItem
          v-model:from="searchFormState.inHospitalDaysFrom"
          v-model:to="searchFormState.inHospitalDaysTo"
          :min="0"
          label="住院天数"
          unit="天"
        />

        <el-form-item label="主治医师">
          <el-input v-model="searchFormState.doctorName" />
        </el-form-item>

        <DaterangeFormItem v-model:model-value="searchFormState.outHospitalDatetime" label="出院时间" />

        <PatientLabelFormItem v-model="searchFormState.patientLabel" />
      </SearchContainer>
    </template>

    <!-- 已质控表格 -->
    <template #table>
      <CommonTable
        table-id="qualityControlledTableIdent"
        :table-columns="qualityControlledColumns"
        :request-api="getAllFinishedDeptQcList"
        :request-params="{ ...searchParams }"
        :data-callback="dataCallback"
      >
        <!-- 患者姓名 -->
        <template #patientName="{ row }">
          <div style="display: flex; align-items: center; flex-wrap: wrap">
            <span style="margin: 0 16px">{{ row.patientName }}</span>
            <PatientLabelTag :row="row" />
          </div>
        </template>
        <template #deptScoreStatusEnum="{ row }">
          <el-tag :type="row.deptScoreStatusEnum === 'YES' ? 'success' : 'info'">
            {{ row.deptScoreStatusEnum === "YES" ? "已评分" : "待评分" }}
          </el-tag>
        </template>
        <template #qcDeptStatusEnum="{ row }">
          <el-tag :type="getQcStatusTagType(row.qcDeptStatusEnum)">{{ getQcStatus(row.qcDeptStatusEnum) }}</el-tag>
        </template>
        <template #operation="{ row }">
          <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.View)" @click="handleRowClick(row)">
            查看
          </TableButton>
          <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.Track)" @click="openQcTraceDrawer(row)">
            质控轨迹
          </TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>

  <!-- 封存病案密钥 -->
  <SealDialog ref="sealDialogRef" :selected-row="medicalRecord" :confirm-callback="confirmOperation" />

  <!-- 质控轨迹抽屉 -->
  <TraceDrawer v-model:visible="qcTraceVisible" :inp-no="activeInpNo" />
</template>

<script setup lang="ts">
import { ref, reactive } from "vue"
import { useRouter } from "vue-router"
import { cloneDeep } from "lodash-es"
import {
  SearchContainer,
  CommonTable,
  TableButton,
  PatientFilterFormItem,
  DaterangeFormItem,
  VisitIdFormItem,
  PageContainer,
  CountRangeFormItem,
  PatientLabelTag,
  PatientLabelFormItem
} from "@/base-components"
import { TabsRouter, SealDialog, TraceDrawer } from "@/page-components"
import { MenuOperationEnum, QualityControlTypeEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { useUserStore } from "@/stores"
import { formatDatetime, encryptStr, getQcStatusTagType, getQcStatus } from "@/utils"
import { qualityControlledColumns, medicalQcTabsRouterList, menuId } from "../config"
import { getAllFinishedDeptQcList } from "../interface"

const { hasOperationPermission } = useUserStore()

const router = useRouter()

/* ======================== 搜索相关数据及方法 ======================== */
const initFormData = {
  patientFilterValue: "",
  patientFilterProp: "",
  outHospitalDeptWsid: "",
  outHospitalDatetime: "",
  visitIdSymbol: "",
  visitIdCount: "",
  doctorName: "",
  inHospitalDaysFrom: "",
  inHospitalDaysTo: "",
  death: "",
  patientLabel: ""
}

const searchFormState = reactive(cloneDeep(initFormData))

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState, ``)
/* ======================== 表格相关方法 ======================== */

function dataCallback(data: Array<Record<string, any>>) {
  data.forEach(item => {
    item.inHospitalDatetime = formatDatetime(item.inHospitalDatetime)
    item.outHospitalDatetime = formatDatetime(item.outHospitalDatetime)
    item.qcDatetime = formatDatetime(item.qcDatetime)
    item.submitterTime = formatDatetime(item?.submitterTime)
  })
  return data
}

/* ======================== 封存弹窗相关 ======================== */

const medicalRecord = ref<Record<string, any> | null>(null) // 用户点击的病案
const sealDialogRef = ref() // SealDialog组件ref

const handleRowClick = row => {
  medicalRecord.value = row
  sealDialogRef.value.checkSealed()
}

// 检查封存之后的回调
const confirmOperation = () => {
  const query = {
    inpNo: medicalRecord.value?.inpNo,
    wsid: medicalRecord.value?.wsid,
    urlType: QualityControlTypeEnum.DEPT_QC_MEDICAL
  }
  if (sealDialogRef.value.secretKey) {
    query["secretKey"] = encryptStr(sealDialogRef.value.secretKey)
  }
  router.push({
    path: "/department-quality-control/medical-qc/view",
    query: query
  })
}

/* ======================== 质控轨迹抽屉 ======================== */
const qcTraceVisible = ref(false)
const activeInpNo = ref("")

function openQcTraceDrawer(row) {
  qcTraceVisible.value = true
  activeInpNo.value = row.inpNo
}
</script>

<style lang="less" scoped>
.in-hospital-days-input {
  width: 65px;
}
:deep(.el-input) {
  input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
  }
}
</style>
