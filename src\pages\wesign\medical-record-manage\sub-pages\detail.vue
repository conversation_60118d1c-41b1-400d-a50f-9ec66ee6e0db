<template>
  <div id="medical-record-detail">
    <!-- 左侧文件列表（树形结构） -->
    <div v-loading="state.loading" class="view-left">
      <MedicalRecordTree
        ref="recordTreeRef"
        can-collapse
        :base-info="state.baseInfo"
        :tree-info="state.treeInfo"
        @click-pdf="handlePdfClick"
      />
    </div>

    <div class="view-right">
      <!-- 顶部信息及按钮 -->
      <view class="tool-bar">
        <view class="document-name">{{ state.documentName }}</view>

        <view class="tool-bar-buttons">
          <el-button @click="toPrev">上一份文件</el-button>
          <el-button @click="toNext">下一份文件</el-button>

          <el-button v-if="quashPermission" type="primary" @click="toQuash">撤销</el-button>
          <el-button
            v-if="!state.baseInfo.outHospitalDatetime"
            type="primary"
            plain
            :disabled="state.loading"
            @click="openSyncDialog"
          >
            同步
          </el-button>
        </view>
      </view>

      <!-- pdf -->
      <div class="view-middle common-box-shadow">
        <!-- 病案备注 -->
        <div
          v-if="state.baseInfo.submitRemark && state.baseInfo.submitStatusEnum === 'SUBMIT_SUCCESS'"
          class="remark"
          @click="showSubmitRemark"
        >
          <div>备注：{{ state.baseInfo.submitRemark }}</div>
        </div>

        <PdfPreviewComponent
          ref="pdfPreviewRef"
          :src="state.pdfSrc"
          :file-wsid="state.targetFileWsid"
          :medical-location="state.baseInfo?.documentStorageLocation"
        />
      </div>

      <!-- 文档元数据 -->
      <div v-if="!props.catalog" v-loading="state.loading" style="height: calc(100% - 60px)">
        <DocumentMeta
          can-collapse
          :base-info-data="state.firstPageFields"
          :file-wsid="state.targetFileWsid"
          :document-wsid="state.documentWsid"
        />
      </div>
    </div>

    <!-- 备注、撤销相关弹窗 -->
    <DialogContainer v-model:visible="remarkDialogVisible" title="提交备注" :width="550">
      <el-input disabled :value="state.baseInfo.submitRemark" type="textarea" :rows="10" />
      <template #footer>
        <div>
          <el-button type="primary" @click="remarkDialogVisible = false">知道了</el-button>
        </div>
      </template>
    </DialogContainer>

    <DialogContainer
      v-model:visible="quashDialogVisible"
      title="撤销"
      :width="550"
      :confirm-callback="confirmQuash"
      :confirm-loading="confirmQuashLoading"
    >
      <div style="margin-bottom: 10px">撤销原因：</div>
      <el-input v-model="state.quashCause" type="textarea" :rows="5" />
    </DialogContainer>

    <!-- 同步弹窗 -->
    <SyncDialog ref="syncDialogRef" :inp-no="state.baseInfo.inpNo" @success="handleSyncSuccess" />
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, watch, ref, computed } from "vue"
import { useRoute } from "vue-router"
import { ElMessageBox } from "element-plus"
import { debounce } from "lodash-es"
import { PdfPreviewComponent, DialogContainer } from "@/base-components"
import { MedicalRecordTree, DocumentMeta, SyncDialog } from "@/page-components"
import { getSubmissionTreeApi, getInactiveDocumentsApi } from "../interface"
import { getRecordViewData, getSubmitPermissionApi, quashMedicalApi } from "@/interfaces"
import { decryptStr, toastError, Message } from "@/utils"

const route = useRoute()

interface Detail {
  catalog: boolean //是否是编目中的病历
}

const props = withDefaults(defineProps<Detail>(), {
  catalog: false
})

watch(
  () => route.query.inpNo,
  val => {
    if (val) {
      getData()
    }
  }
)

onMounted(async () => {
  await getData()
  registerKeyboardEvent()
})

const getData = async () => {
  const inpNo = route.query.inpNo as string
  const secretKey = (route.query?.secretKey ?? "") as string
  const params = { inpNo: inpNo }
  if (secretKey) {
    const sealKey = decryptStr(secretKey)
    params["sealKey"] = sealKey
  }
  state.loading = true
  await getRecordViewData(params).then(res => {
    if (res.data.code !== "100100000") return
    const recordDetail = res.data.data
    state.baseInfo = recordDetail.baseInfo || {}
    state.firstPageFields = recordDetail.firstPageFields
    state.treeInfo = recordDetail.treeInfo
    state.loading = false
  })
  // // 已提交的记录才可撤销
  // if (state.baseInfo.submitStatusEnum === "SUBMIT_SUCCESS") {
  //   quashPermission.value = (await getSubmitPermissionApi(route.query.inpNo as string))?.data?.data?.quashPermission
  // }
  quashPermission.value = (await getSubmitPermissionApi(route.query.inpNo as string))?.data?.data?.quashPermission
}

const state = reactive({
  loading: false,
  baseInfo: {} as Record<string, any>, // 患者基础信息
  pdfSrc: "",
  targetFileWsid: "",
  firstPageFields: [] as Array<any>, // 首页字段
  treeInfo: {} as { mrClassTree: any[]; noCatalogDocuments: any[] }, // 左侧tree需要的原始数据
  documentName: "", // 当前文档名称
  documentWsid: "" as string, // 当前点击的文档wsid
  quashCause: ""
})

// 切换pdf源
const handlePdfClick = node => {
  console.log(`output->node`, node)
  let fileUrl = node?.contentFilePath
  state.pdfSrc = "/api" + fileUrl
  state.targetFileWsid = node?.fileWsid
  state.documentName = node?.title
  state.documentWsid = node?.wsid
}

/* =============== toolbar =============== */

const recordTreeRef = ref<InstanceType<typeof MedicalRecordTree>>()
const documentNodeList = computed(() => recordTreeRef.value?.state.documentNodeList)

const registerKeyboardEvent = () => {
  document.onkeyup = e => {
    const keyCode = e.code
    // 左/上键
    if (keyCode === "ArrowLeft" || keyCode === "ArrowUp") {
      toPrev()
    }
    // 右/下键
    else if (keyCode === "ArrowRight" || keyCode === "ArrowDown") {
      toNext()
    }
  }
}

const toPrev = debounce(() => {
  if (!documentNodeList.value) return
  const index = documentNodeList.value.findIndex(item => item.wsid === state.documentWsid) || 0
  if (index <= 0) return
  const targetNode = documentNodeList.value[index - 1]
  handlePdfClick(targetNode)
  recordTreeRef.value?.setCurrentKey(targetNode.wsid)
}, 300)

const toNext = debounce(() => {
  if (!documentNodeList.value) return
  const index = documentNodeList.value.findIndex(item => item.wsid === state.documentWsid)
  if (index >= documentNodeList.value.length - 1) return
  const targetNode = documentNodeList.value[index + 1]
  handlePdfClick(targetNode)
  recordTreeRef.value?.setCurrentKey(targetNode.wsid)
}, 300)

// 展示备注
const remarkDialogVisible = ref(false)
const showSubmitRemark = () => {
  remarkDialogVisible.value = true
}

// 撤销
const quashPermission = ref(false)
const quashDialogVisible = ref(false)
const confirmQuashLoading = ref(false)

const toQuash = () => {
  quashDialogVisible.value = true
}
const confirmQuash = async () => {
  try {
    confirmQuashLoading.value = true
    const result = (
      await quashMedicalApi({
        inpNo: route.query.inpNo as string,
        quashCause: state.quashCause
      })
    )?.data
    Message.success("撤销成功")
    confirmQuashLoading.value = false
    quashDialogVisible.value = false
    await getData()
  } catch (err: any) {
    console.log(`output->err`, err)
    confirmQuashLoading.value = false
    toastError(err, "撤销失败")
  }
}

/* ============== 同步 =============== */

const syncDialogRef = ref<InstanceType<typeof SyncDialog>>()

const openSyncDialog = () => {
  syncDialogRef.value?.show()
}

// 同步病案
const handleSyncSuccess = () => {
  getSubmissionTree()
}

const getSubmissionTree = () => {
  state.loading = true
  getSubmissionTreeApi(route.query?.inpNo as string, route.query?.secretKey as string)
    .then(res => {
      state.treeInfo = res.data.data.treeInfo
      state.loading = false
    })
    .catch(() => {
      state.loading = false
    })
}
</script>

<style lang="less" scoped>
#medical-record-detail {
  display: flex;
  justify-content: space-between;
  height: 100%;

  .view-middle {
    // overflow-y: auto;
    overflow-y: hidden;
    height: calc(100% - 60px);
    min-width: 400px;
    flex: 1;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
  }
}

.tool-bar {
  background: #fff;
  border-radius: 8px;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 40px;

  .document-name {
    font-size: 16px;
    font-weight: 600;
  }
}

.remark {
  color: #f59a23;
  background-color: #fdf6ec;
  padding: 10px;
  cursor: pointer;
  div {
    max-width: 800px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.view-right {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
}
</style>
