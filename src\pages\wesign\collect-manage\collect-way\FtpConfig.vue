<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <CommonInputFormItem v-model="searchFormState.name" label="名称" />
        <CommonSelectFormItem v-model="searchFormState.triggerType" label="触发方式" :options="triggerTypeOptions" />
        <CommonSelectFormItem v-model="searchFormState.scene" label="业务类型" :options="sceneOptions" />
        <CommonSelectFormItem v-model="searchFormState.status" label="状态" :options="rowStatusOptions" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="commonTableRef"
        :table-columns="ftpTableColumns"
        :request-api="getFTPList"
        :request-params="searchParams"
      >
        <template #header>
          <AddButton @click="showForm('add')">新增</AddButton>
        </template>
        <template #triggerType="{ row }">
          <span>{{ getTriggerTypeDesc(row.triggerType) }}</span>
        </template>
        <template #scene="{ row }">
          <span>{{ getSceneDesc(row.scene) }}</span>
        </template>
        <template #dataType="{ row }">
          <span>{{ getDataTypeDesc(row.dataType) }}</span>
        </template>
        <template #operation="{ row }">
          <TableButton @click="showForm('edit', row)">编辑</TableButton>
          <TableButton v-if="row.triggerType !== 'BUSINESS'" @click="triggerJob(row)">执行一次</TableButton>
          <TableButton @click="handleRecord(row)">记录</TableButton>
          <TableButton @click="modifyStatus(row)">
            {{ row.status ? "禁用" : "启用" }}
          </TableButton>
          <TableButton @click="handleDelete(row)">删除</TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>

  <DialogContainer
    v-model:visible="formDialogVisible"
    :title="actionType === 'edit' ? '修改FTP配置' : '添加FTP配置'"
    :width="1000"
    :cancel-callback="closeForm"
    :confirm-callback="handleSave"
  >
    <el-form
      ref="ruleFormRef"
      label-position="right"
      label-width="150px"
      label-suffix="："
      :model="formState"
      :rules="ftpFormRules"
    >
      <CommonInputFormItem v-model="formState.name" label="名称" prop="name" />

      <CommonRadioFormItem
        v-model="formState.triggerType"
        label="触发方式"
        prop="triggerType"
        :options="triggerTypeOptions"
      />

      <CommonSelectFormItem v-model="formState.scene" label="业务类型" prop="scene" :options="sceneOptions" />

      <CommonSelectFormItem
        v-model="formState.systemWsid"
        label="厂商系统"
        prop="systemWsid"
        :options="systemVendorOptions"
      />

      <CommonSelectFormItem
        v-model="formState.ftpSourceWsid"
        label="数据源名称"
        prop="ftpSourceWsid"
        :options="dataSourceTypeOptions"
      />

      <CommonSelectFormItem v-model="formState.dataType" label="数据类型" prop="dataType" :options="dataTypeOptions" />

      <CommonSelectFormItem
        v-if="formState.dataType === 'MR_CONTENT'"
        v-model="formState.mrClassCode"
        label="文书分类"
        prop="mrClassCode"
        :options="dataMrOptions"
      />

      <el-form-item label="解析表达式" prop="parseEl">
        <el-input v-model.trim="formState.parseEl" placeholder="请输入解析表达式" type="textarea" :rows="4" />
      </el-form-item>

      <el-form-item v-if="formState.triggerType === 'TIME'" label="采集频率(cron)" prop="cron">
        <el-input v-model="formState.cron" placeholder="0 0/10 * * * ?" />
      </el-form-item>

      <el-form-item label="超时时间" prop="timeout">
        <el-input v-model.trim="formState.timeout" maxlength="5" type="string" placeholder="请输入超时时间">
          <template #append>秒</template>
        </el-input>
      </el-form-item>
    </el-form>
  </DialogContainer>

  <!-- 记录弹窗 -->
  <Record :record-visible-dialog="recordVisibleDialog" :selected-row="selectedRow" @close="closeRecordDialog"></Record>
</template>

<script setup lang="ts">
import { reactive, ref, nextTick } from "vue"
import {
  SearchContainer,
  AddButton,
  DialogContainer,
  CommonTable,
  TableButton,
  PageContainer,
  CommonSelectFormItem,
  CommonInputFormItem,
  CommonRadioFormItem
} from "@/base-components"
import { TabsRouter } from "@/page-components"
import { rowStatusOptions } from "@/configs"
import { useCommonOptions, useTableSearch } from "@/hooks"
import useFormSetting from "@/hooks/useFormSetting_v2"
import { getMrClassList } from "@/interfaces"
import { useSystemStore } from "@/stores"
import { SystemPrompt, Message, extractErrorMsg, toastError } from "@/utils"
import Record from "./components/record.vue"
import {
  tabsRouterList,
  ftpTableColumns,
  ftpFormRules,
  dataTypeOptions,
  getDataTypeDesc,
  sceneOptions,
  triggerTypeOptions,
  getTriggerTypeDesc,
  getSceneDesc
} from "./config"
import {
  modifyIntegrations,
  addIntegrations,
  editIntegrations,
  getCollectSystemList,
  triggerJobApi,
  getFTPList,
  getFtpDataSourceListApi
} from "./interface"
import type { FormInstance } from "element-plus"

const systemStore = useSystemStore()

/* ======================== 页面加载时获取选项 ======================== */

// 厂商系统选项
const { options: systemVendorOptions } = useCommonOptions({
  getOptionsApi: getCollectSystemList,
  labelAlias: "systemName",
  valueAlias: "wsid"
})

// 获取所有文书分类选项
const { options: dataMrOptions } = useCommonOptions({
  getOptionsApi: getMrClassList,
  labelAlias: "mrClassName",
  valueAlias: "mrClassCode"
})

// 获取所有数据源选项
const { options: dataSourceTypeOptions } = useCommonOptions({
  getOptionsApi: getFtpDataSourceListApi,
  labelAlias: "name",
  valueAlias: "wsid"
})

/* ======================== 搜索 ======================== */

const commonTableRef = ref<InstanceType<typeof CommonTable>>()

const searchFormState = reactive({
  name: "",
  hostName: "",
  triggerType: "", // 触发方式
  scene: "", // 业务类型（场景）
  status: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/* ======================== 弹窗编辑 ======================== */

const ruleFormRef = ref<FormInstance>()

const formInitialValues = {
  wsid: "", // 配置wsid
  name: "", // FTP名称
  triggerType: "TIME", // 触发方式
  scene: "", // 业务类型（场景）
  systemWsid: "", // 厂商系统
  ftpSourceWsid: "", // 数据源名称
  dataType: "", // 数据类型
  mrClassCode: "", // 文书分类
  parseEl: "", // 解析表达式
  cron: "", // 采集频率
  timeout: "" // 超时时间
}

const { visible: formDialogVisible, showForm, closeForm, actionType, formState } = useFormSetting(formInitialValues)

// 保存编辑
const handleSave = () => {
  if (!ruleFormRef.value) return
  ruleFormRef.value.validate(valid => {
    if (!valid) return

    if (!systemVendorOptions.value.find(item => item.value === formState.systemWsid)) {
      Message.error("该厂商系统已不存在或已被禁用，请重新选择")
      return false
    }
    systemStore.showLoading("正在提交")
    const requestData = {
      systemWsid: formState.systemWsid,
      integrationWsid: formState.wsid,
      dataType: formState.dataType,
      mrClassCode: formState.mrClassCode,
      cron: formState.cron,
      scene: formState.scene,
      triggerType: formState.triggerType,
      ftpConfig: {
        name: formState.name,
        parseEl: formState.parseEl,
        timeout: formState.timeout,
        ftpSourceWsid: formState.ftpSourceWsid
      }
    }
    if (actionType.value === "edit") {
      editIntegrations(requestData)
        .then(() => {
          systemStore.hideLoading()
          Message.success("修改FTP配置成功")
          closeForm()
          commonTableRef.value?.refreshTableData()
        })
        .catch(err => {
          toastError(err, "修改失败")
        })
    } else {
      addIntegrations(requestData)
        .then(() => {
          systemStore.hideLoading()
          closeForm()
          Message.success("添加FTP配置成功")
          commonTableRef.value?.refreshTableData()
        })
        .catch(err => {
          systemStore.hideLoading()
          toastError(err, "添加失败")
        })
    }
  })
}

/* ======================== 行操作 ======================== */

// 执行一次
function triggerJob(row) {
  triggerJobApi(row.wsid)
    .then(res => {
      if (res.data?.message) {
        Message.success(res.data.message)
      }
    })
    .catch(err => Message.error(extractErrorMsg(err, "操作失败")))
}

// 切换启用&禁用状态
const modifyStatus = row => {
  const nextStatus = row.status ? "DISABLE" : "ENABLE"
  if (nextStatus === "ENABLE") {
    modifyDataSourcesInfo(row.wsid, "ENABLE")
  } else {
    SystemPrompt(`您确定要禁用${row.name} ，禁用后关联该FTP所有配置将不能使用`).then(async () =>
      modifyDataSourcesInfo(row.wsid, "DISABLE")
    )
  }
}

// 删除
function handleDelete(row) {
  SystemPrompt(`您确定要删除${row.name} ，删除后关联该FTP所有配置将不能使用`, "error").then(() =>
    modifyDataSourcesInfo(row.wsid, "DEL")
  )
}

const modifyDataSourcesInfo = (targetWsid: string, status: "ENABLE" | "DISABLE" | "DEL") => {
  systemStore.showLoading("请稍候")
  modifyIntegrations({ targetWsid, status })
    .then(() => {
      systemStore.hideLoading()
      Message.success(status === "DEL" ? "删除数据成功" : "修改状态成功")
      commonTableRef.value?.refreshTableData()
    })
    .catch(err => {
      systemStore.hideLoading()
      toastError(err, "操作失败")
    })
}

/* ======================== 记录弹窗 ======================== */
const recordVisibleDialog = ref(false)
const selectedRow = ref()
const handleRecord = row => {
  selectedRow.value = row
  nextTick(() => {
    recordVisibleDialog.value = true
  })
}
const closeRecordDialog = () => {
  recordVisibleDialog.value = false
}
</script>

<style lang="less" scoped>
:deep(.el-form-item) {
  margin-bottom: 15px !important;
}
</style>
