<template>
  <PageContainer separate>
    <template #search>
      <!-- <TabsRouter :tabs-config="tabsRouterList" /> -->
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="pageHandleReset">
        <CommonInputFormItem v-model="queryParams.mrNo" label="病案号" />

        <CommonSelectFormItem
          v-model="queryParams.outHospitalDeptWsid"
          :filterable="true"
          label="出院科室"
          :options="outDeptOptions"
        />

        <DaterangeFormItem v-model:model-value="searchFormState.outHospitalDatetime" label="出院日期" />

        <CommonSelectFormItem v-model="outHospitalWayCode" label="离院方式" :options="outHospitalWayOptions" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="submitDocumentTableRef"
        table-id="submitDocumentTableIdent"
        :table-columns="submitDocumentsTableColumns"
        :request-api="getSubmitDocumentList"
        :request-params="searchParams"
        :data-callback="dataCallback"
        :selectable="handleSelectable"
      >
        <template #header>
          <BatchOperationButton :icon="DocumentAdd" :disabled="!enableGenerateRows.length" @click="handleGenerate">
            生成上报PDF
          </BatchOperationButton>
          <BatchOperationButton :disabled="!enableExportRows.length" :icon="Download" @click="handleExport">
            导出PDF文书
          </BatchOperationButton>
        </template>

        <template #exportStatus="{ row }">{{ row.exportStatus === "YES" ? "是" : "否" }}</template>
        <template #operation="{ row }">
          <TableButton @click="handleRowClick(row)">查看</TableButton>
          <TableButton v-if="row.exportStatus === 'YES'" @click="handleRowExport(row)">导出PDF</TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref, computed, watch } from "vue"
import { useRouter } from "vue-router"
import { Download, DocumentAdd } from "@element-plus/icons-vue"
import {
  PageContainer,
  SearchContainer,
  CommonTable,
  CommonInputFormItem,
  DaterangeFormItem,
  CommonSelectFormItem,
  TableButton,
  BatchOperationButton
} from "@/base-components"
import { useTableSearch, useCommonOptions } from "@/hooks"
import { getHQMSDepartmentApi } from "@/interfaces"
import { useSystemStore } from "@/stores"
import { formatDatetime, Message, toastError, downloadFile } from "@/utils"
import { submitDocumentsTableColumns, outHospitalWayOptions } from "../config"
import { getSubmitDocumentList, downloadSubmitDocumentPdf, generateHqmsDataApi } from "../interface"

const systemStore = useSystemStore()

const { options: outDeptOptions } = useCommonOptions({
  getOptionsApi: getHQMSDepartmentApi,
  labelAlias: "value",
  valueAlias: "key"
})

const router = useRouter()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  outHospitalDatetime: ""
})

const outHospitalWayCode = ref("")

const queryParams = reactive({
  outHospitalTimeStart: "",
  outHospitalTimeEnd: "",
  outHospitalDeptWsid: "",
  outHospitalWayCode: "",
  mrNo: ""
})

watch(
  () => searchFormState.outHospitalDatetime,
  val => {
    queryParams.outHospitalTimeStart = val ? val[0] : ""
    queryParams.outHospitalTimeEnd = val ? val[1] : ""
  }
)

watch(
  () => outHospitalWayCode.value,
  val => {
    queryParams.outHospitalWayCode = val ? val : "4,5"
  },
  {
    immediate: true
  }
)

const { searchParams, handleQuery, handleReset } = useTableSearch(
  {},
  "",
  {
    outHospitalTimeStart: "",
    outHospitalTimeEnd: "",
    outHospitalDeptWsid: "",
    mrNo: "",
    outHospitalWayCode: "4,5"
  },
  queryParams,
  searchFormState
)

// 重置查询
const pageHandleReset = () => {
  outHospitalWayCode.value = ""
  handleReset()
}

/* ======================== 表格相关方法 ======================== */

const submitDocumentTableRef = ref<InstanceType<typeof CommonTable>>()

// 当前选中表格项
const selectedRows = computed(() => submitDocumentTableRef.value?.tableState?.selectedRows ?? [])

// 可生成上报pdf的选中项
const enableGenerateRows = computed(
  () => submitDocumentTableRef.value?.tableState?.selectedRows.filter(item => item.exportStatus === "NO") ?? []
)

// 可导出pdf的选中项
const enableExportRows = computed(
  () => submitDocumentTableRef.value?.tableState?.selectedRows.filter(item => item.exportStatus === "YES") ?? []
)

const dataCallback = (data: Array<Record<string, any>>) => {
  return data.map(item => ({
    ...item,
    outHospitalDatetime: formatDatetime(item.outHospitalDatetime),
    inHospitalDatetime: formatDatetime(item.inHospitalDatetime)
  }))
}

const fileType = ref("application/pdf")

watch(
  () => submitDocumentTableRef.value?.tableState?.selectedRows,
  val => {
    if (val?.length) {
      fileType.value = val?.length > 1 ? "application/zip" : "application/pdf"
    }
  },
  {
    immediate: true,
    deep: true
  }
)

// 生成上报pdf
const handleGenerate = async () => {
  systemStore.showLoading("生成中")
  await generateHqmsDataApi(enableGenerateRows.value.map(item => item.wsid).join(","))
    .then(res => {
      Message.success("生成成功")
      pageHandleReset()
      systemStore.hideLoading()
    })
    .catch(err => {
      systemStore.hideLoading()
      toastError(err)
    })
}

const exportParams = computed(() => {
  return {
    data: `documentBagWsid=${submitDocumentTableRef.value?.tableState?.selectedRows.map(item => item.wsid).join(",")}`
  }
})

const exportApi = (data: Record<string, any>) => {
  systemStore.showLoading("导出中")
  downloadSubmitDocumentPdf(data)
    .then(res => {
      const fileName = decodeURIComponent(res.headers["content-disposition"].split("filename=")[1].replaceAll('"', ""))
      downloadFile({ fileData: res.data, fileType: fileType.value, fileName: fileName })
      Message.success("导出成功")
      systemStore.hideLoading()
    })
    .catch(err => {
      systemStore.hideLoading()
      toastError(err)
    })
}

// 批量导出
const handleExport = () => {
  exportApi(exportParams.value)
}

// 单个导出
const handleRowExport = row => {
  exportApi({ data: `documentBagWsid=${row.wsid}` })
}

// 查看
const handleRowClick = row => {
  router.push({
    path: "/medical-record/detail",
    query: { inpNo: row.inpNo }
  })
}

// 是否可勾选
const handleSelectable = (row, index) => {
  // return row.exportStatus === "YES"
  return true
}
</script>
