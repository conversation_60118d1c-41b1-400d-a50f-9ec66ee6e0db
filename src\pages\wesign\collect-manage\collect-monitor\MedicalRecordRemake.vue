<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
          :filterPropOptions="filterPropOptions"
        />

        <DaterangeFormItem v-model:model-value="searchFormState.createdDatetime" label="拍摄时间" />

        <CommonSelectFormItem
          v-model="searchFormState.mrClassCode"
          label="文档类型"
          :options="documentTypeOptions"
          filterable
        />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="remakeTableRef"
        table-id="remakeTableIdent"
        :table-columns="remakeColumns"
        :request-api="getRemakeList"
        :request-params="searchParams"
        :data-callback="dataCallback"
      ></CommonTable>
    </template>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref, computed } from "vue"
import {
  PageContainer,
  SearchContainer,
  CommonTable,
  CommonInputFormItem,
  DaterangeFormItem,
  CommonSelectFormItem,
  PatientFilterFormItem
} from "@/base-components"
import { TabsRouter } from "@/page-components"
import { useCommonOptions, useTableSearch } from "@/hooks"
import { getAllDocTypes } from "@/interfaces"
import { formatDatetime } from "@/utils"
import { remakeColumns, tabsRouterList } from "./config"
import { getRemakeList } from "./interface"

// 获取所有病案类型选项
const { options: documentTypeOptions } = useCommonOptions({
  getOptionsApi: getAllDocTypes,
  labelAlias: "mrClassName",
  valueAlias: "mrClassCode"
})

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  patientFilterProp: "",
  patientFilterValue: "",
  createdDatetime: "",
  mrClassCode: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/* ======================== 表格相关方法 ======================== */
const filterPropOptions = [
  { label: "姓名", value: "patientName" },
  { label: "患者编号", value: "inpNo" },
  { label: "病案号", value: "mrNo" }
]

const remakeTableRef = ref<InstanceType<typeof CommonTable>>()

const dataCallback = (data: Array<Record<string, any>>) => {
  return data.map(item => ({
    ...item,
    createdDatetime: formatDatetime(item.createdDatetime)
  }))
}
</script>
