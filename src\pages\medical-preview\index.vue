<template>
  <div id="medical-preview">
    <!-- 左侧文件列表（树形结构） -->
    <div v-loading="state.loading" class="view-left">
      <div class="document-type">
        <el-select v-model="selectedBagType" @change="handleBagTypeChange">
          <el-option
            v-for="option in documentBagsInfoTypeOptions"
            :key="option.bagNum"
            :label="option.label"
            :value="option.bagNum"
          ></el-option>
        </el-select>
      </div>

      <MedicalRecordTree
        ref="recordTreeRef"
        can-collapse
        :show-mr-select="selectedBag?.bagType !== 'CHECKUP'"
        :base-info="state.baseInfo"
        :tree-info="state.treeInfo"
        @click-pdf="handlePdfClick"
      />
    </div>

    <div class="view-right">
      <!-- 顶部信息及按钮 -->
      <view class="tool-bar">
        <view class="document-name">{{ state.documentName }}</view>

        <view class="tool-bar-buttons">
          <el-button @click="toPrev">上一份文件</el-button>
          <el-button @click="toNext">下一份文件</el-button>
        </view>
      </view>

      <!-- pdf -->
      <div class="view-middle common-box-shadow">
        <!-- 病案备注 -->
        <div
          v-if="state.baseInfo.submitRemark && state.baseInfo.submitStatusEnum === 'SUBMIT_SUCCESS'"
          class="remark"
          @click="showSubmitRemark"
        >
          <div>备注：{{ state.baseInfo.submitRemark }}</div>
        </div>

        <PdfPreviewComponent
          v-if="state.pdfSrc"
          ref="pdfPreviewRef"
          :src="state.pdfSrc"
          :medical-location="state.baseInfo?.documentStorageLocation"
          session-less
        ></PdfPreviewComponent>
      </div>
    </div>

    <!-- 备注、撤销相关弹窗 -->
    <DialogContainer v-model:visible="remarkDialogVisible" title="提交备注" :width="550">
      <el-input disabled :value="state.baseInfo.submitRemark" type="textarea" :rows="10" />
      <template #footer>
        <div>
          <el-button type="primary" @click="remarkDialogVisible = false">知道了</el-button>
        </div>
      </template>
    </DialogContainer>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, ref, computed } from "vue"
import { useRoute } from "vue-router"
import { debounce } from "lodash-es"
import { PdfPreviewComponent, DialogContainer } from "@/base-components"
import { MedicalRecordTree } from "@/page-components"
import { formatDate } from "@/utils"
import { getDocumentBagsInfoApi, getDocumentBagsInfoByTypeApi, getDocumentBagsTypeApi } from "./interface"

const route = useRoute()

onMounted(async () => {
  // await getDocumentBagsInfo()
  await getDocumentBagsInfoType()
  registerKeyboardEvent()
})

const getData = async () => {
  const inpNo = route.query.inpNo as string
  const mrClassCode = route.query.mrClassCode as string
  const secretKey = (route.query?.secretKey ?? "") as string
  const params = {
    inpNo: inpNo,
    jobId: route.query.jobId as string,
    method: "get",
    params: { inpNo: inpNo, mrClassCode: mrClassCode },
    url: `/api/document/document-bags/view/${inpNo}`
  }
  if (secretKey) {
    const sealKey = decryptStr(secretKey)
    params["sealKey"] = sealKey
  }
  state.loading = true
  // await getRecordViewData(params)
  await sessionLessAxios(params).then(res => {
    if (res.data.code !== "100100000") return
    const recordDetail = res.data.data
    state.baseInfo = recordDetail.baseInfo || {}
    state.firstPageFields = recordDetail.firstPageFields
    state.treeInfo = recordDetail.treeInfo
    state.loading = false
  })
  // 已提交的记录才可撤销
  // if (state.baseInfo.submitStatusEnum === "SUBMIT_SUCCESS") {
  //   quashPermission.value = (await getSubmitPermissionApi(route.query.inpNo as string))?.data?.data?.quashPermission
  // }
}

const state = reactive({
  loading: false,
  baseInfo: {} as Record<string, any>, // 患者基础信息
  pdfSrc: "",
  targetFileWsid: "",
  treeInfo: {} as { mrClassTree: any[]; noCatalogDocuments: any[] }, // 左侧tree需要的原始数据
  documentName: "", // 当前文档名称
  currentNodeWsid: "" as string // 当前点击的文档wsid
})

/* =============== PDF =============== */

// 切换pdf源
const handlePdfClick = node => {
  console.log(node)
  // 病案
  if (selectedBag.value?.bagType !== "CHECKUP") {
    state.pdfSrc = "/api" + node.contentFilePath
  state.targetFileWsid = node?.fileWsid
  state.documentName = node?.title
    state.currentNodeWsid = node?.wsid
}
  // 体检报告
  else {
    state.pdfSrc = `/api/checkup/documents/${node.wsid}/file/${node.fileWsid}`
    state.targetFileWsid = node?.fileWsid
    state.documentName = node?.title
    state.currentNodeWsid = node?.wsid
  }
}

/* =============== 病案袋类型 =============== */

const selectedBagType = ref("")
const documentBagsInfoTypeOptions = ref<Record<string, any>[]>([])

const selectedBag = computed(() =>
  documentBagsInfoTypeOptions.value.find(type => type.bagNum === selectedBagType.value)
)

// 获取病案袋类型
const getDocumentBagsInfoType = async () => {
  documentBagsInfoTypeOptions.value = (await getDocumentBagsTypeApi(route.query.idCard as string)).data.data.map(
    type => ({
      ...type,
      label: `${getBagTypeLabel(type.bagType)}（${type.bagNum}） ${formatDate(type.dateTime)}`
    })
  )
  selectedBagType.value = documentBagsInfoTypeOptions.value[0].bagNum
  handleBagTypeChange(selectedBagType.value)
}

// 获取病案袋信息
// const getDocumentBagsInfo = async () => {
//   const inpNo = route.query.inpNo as string
//   state.loading = true
//   await getDocumentBagsInfoApi(inpNo).then(res => {
//     if (res.data.code !== "100100000") return
//     const recordDetail = res.data.data
//     state.baseInfo = recordDetail.baseInfo || {}
//     state.treeInfo = recordDetail.treeInfo
//     state.loading = false
//   })
// }

// 切换文件类型
const handleBagTypeChange = async val => {
  if (!selectedBag.value) return
  state.loading = true
  const res = await getDocumentBagsInfoByTypeApi(selectedBag.value.bagNum, selectedBag.value.bagType)
  const bagInfo = res.data.data.documentBag
  state.baseInfo = bagInfo.baseInfo || {}
  // 体检报告没有mrClassTree，需要手动添加空数组防止报错
  state.treeInfo = bagInfo.treeInfo.mrClassTree ? bagInfo.treeInfo : { ...bagInfo.treeInfo, mrClassTree: [] }
  state.loading = false
}

/* =============== 病案袋类型option =============== */

const bagTypeOptions = [
  { label: "体检", value: "CHECKUP" },
  { label: "住院", value: "IN_HOSPITAL" }
]

const getBagTypeLabel = (val: string) => {
  return bagTypeOptions.find(option => option.value === val)?.label
}

/* =============== toolbar =============== */

const recordTreeRef = ref<InstanceType<typeof MedicalRecordTree>>()
const documentNodeList = computed(() => recordTreeRef.value?.state.documentNodeList)

const registerKeyboardEvent = () => {
  document.onkeyup = e => {
    const keyCode = e.code
    // 左/上键
    if (keyCode === "ArrowLeft" || keyCode === "ArrowUp") {
      toPrev()
    }
    // 右/下键
    else if (keyCode === "ArrowRight" || keyCode === "ArrowDown") {
      toNext()
    }
  }
}

const toPrev = debounce(() => {
  if (!documentNodeList.value) return
  const index = documentNodeList.value.findIndex(item => item.wsid === state.currentNodeWsid) || 0
  if (index <= 0) return
  const targetNode = documentNodeList.value[index - 1]
  handlePdfClick(targetNode)
  recordTreeRef.value?.setCurrentKey(targetNode.wsid)
}, 300)

const toNext = debounce(() => {
  if (!documentNodeList.value) return
  const index = documentNodeList.value.findIndex(item => item.wsid === state.currentNodeWsid)
  if (index >= documentNodeList.value.length - 1) return
  const targetNode = documentNodeList.value[index + 1]
  handlePdfClick(targetNode)
  recordTreeRef.value?.setCurrentKey(targetNode.wsid)
}, 300)

// 展示备注
const remarkDialogVisible = ref(false)

const showSubmitRemark = () => {
  remarkDialogVisible.value = true
}
</script>

<style lang="less" scoped>
#medical-preview {
  display: flex;
  justify-content: space-between;
  height: 100vh;
  overflow-y: hidden;

  .view-left {
    background: #fff;

    .document-type {
      height: 58px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .view-middle {
    overflow-y: auto;
    // overflow-y: hidden;
    height: calc(100% - 60px);
    min-width: 400px;
    flex: 1;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
  }
}

.tool-bar {
  background: #fff;
  border-radius: 8px;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 40px;

  .document-name {
    font-size: 16px;
    font-weight: 600;
  }
}

.remark {
  color: #f59a23;
  background-color: #fdf6ec;
  padding: 10px;
  cursor: pointer;
  div {
    max-width: 800px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.view-right {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
}
</style>
