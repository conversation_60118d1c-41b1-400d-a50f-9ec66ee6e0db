import axios from "@/interfaces/axios-instance"

/**
 * @method GET
 * @desc   待上级医生科室质控列表查询
 */
export function getSeniorWaitingQcDeptList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    // url: `/api/document/bag/qc/wait-dept-qc`,
    url: `/api/document/bag/qc/wait-senior-review-qc`,
    params
  })
}

/**
 * @method GET
 * @desc   待质控护士检查列表查询
 */
export function getNurseWaitingQcDeptList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    // url: `/api/document/bag/qc/wait-dept-qc`,
    url: `/api/document/bag/qc/wait-nurse-review-qc`,
    params
  })
}

/**
 * @method GET
 * @desc   科室质控列表--已质控
 */
export function getQualityControlledList(params) {
  const { urlType, offset, limit, filters } = params
  return axios({
    method: "get",
    url: `/api/qc/${urlType}/qc-records/current-user`,
    params: {
      offset,
      limit,
      filters
    }
  })
}

/**
 * @method GET
 * @desc   科室质控任务列表--已质控
 */
export function getQualityControlledDeptTaskList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/qc/dept-qc/qc-records/current-user`,
    params
  })
}

/**
 * @method GET
 * @desc   查询指定科室质控明细
 */
export function getQualityControlledDetail(obj) {
  const { wsid } = obj
  return axios({
    method: "get",
    url: `/api/qc/department/detail/${wsid}`
  })
}

/**
 * @method POST
 * @desc   指定病案袋进行科室质控
 */
export function qualityControlledBag(obj) {
  const { inpNo } = obj
  return axios({
    method: "post",
    url: `/api/qc/department?inpNo=${inpNo}`
  })
}

/**
 * @method GET
 * @desc   交叉质控任务分配
 */
export function getDistributeQcCrossList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/document/bag/qc/wait-cross-qc`,
    params
  })
}

/**
 * @method GET
 * @desc    获取质控缺陷问题类型
 */
export function getQcQuestionTypeList() {
  return axios({
    method: "get",
    url: `/api/qc/qc-comments/question-types`
  })
}

/**
 * @method GET
 * @desc   待科室分页列表（所有类型）
 */
export function getAllWaitingDeptQcList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/document/bag/qc/wait-senior-nurse-review-qc`,
    params
  })
}

/**
 * @method GET
 * @desc   已质控分页数据（科室质控所有类型）
 */
export function getAllFinishedDeptQcList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/qc/qc-records/dept-qc/current-user`,
    params
  })
}
