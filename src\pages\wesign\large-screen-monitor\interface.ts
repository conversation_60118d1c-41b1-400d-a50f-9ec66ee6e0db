import axios from "@/interfaces/axios-instance"

/**
 * @method GET
 * @desc   大屏监控-汇总数据
 */
export const getLargeScreenMonitorStatisticsApi = () => {
  return axios({
    method: "get",
    url: "/api/collect/statistics",
  })
}

/**
 * @method GET
 * @desc   采集记录分页数据
 */
export const getCollectRecordsApi = (params: IPaginationRequestParams) => {
  return axios({
    method: "get",
    url: "/api/collect/statistics/records-paging",
    params
  })
}

/**
 * @method GET
 * @desc   统计厂商正常采集的文件
 */
interface NormalCollectParams {
  startDate?: string // 开始时间
  endDate?: string // 结束时间
}
export const getNormalCollectDataApi = (params: NormalCollectParams) => {
  return axios({
    method: "get",
    url: "/api/collect/sys-normal-statistics",
    params
  })
}
