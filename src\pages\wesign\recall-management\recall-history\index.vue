<template>
  <PageContainer separate>
    <!-- 搜索区域 -->
    <template #search>
      <SearchForm
        :form-config="recallRecordSearchFormConfig"
        :form-state="searchFormState"
        @query-btn-click="handleQuery"
        @reset-btn-click="handleReset"
      />
    </template>

    <!-- 表格展示 -->
    <template #table>
      <CommonTable
        table-id="recallApplyTableIdent"
        :request-params="searchParams"
        :table-columns="recallApprovalRecordTableColumns"
        :request-api="getRecallApprovedHistoryApi"
      >
        <template #inHospitalDatetime="{ row }">
          {{ formatDatetime(row.inHospitalDatetime) }}
        </template>
        <template #outHospitalDatetime="{ row }">
          {{ formatDatetime(row.outHospitalDatetime) }}
        </template>
        <template #createdDatetime="{ row }">
          {{ formatDatetime(row.createdDatetime) }}
        </template>
        <template #archivistName="{ row }">
          {{ row.archivistName || "--" }}
        </template>
        <template #operation="{ row }">
          <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.View)" @click="toToDetail(row)">
            查看
          </TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive } from "vue"
import { useRouter } from "vue-router"
import { CommonTable, TableButton, PageContainer } from "@/base-components"
import { SearchForm } from "@/page-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { useUserStore } from "@/stores"
import { formatDatetime, Message } from "@/utils"
import { getRecallApprovedHistoryApi } from "../interface"
import { recallRecordSearchFormConfig, recallApprovalRecordTableColumns } from "./config"

const router = useRouter()
const menuId = "/recall/history"

const { hasOperationPermission } = useUserStore()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  patientFilterProp: "patientName",
  patientFilterValue: "",
  inHospitalDatetime: "",
  outHospitalDatetime: "",
  approverDatetime: "",
  status: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState, "status=APPROVAL_PASS")

const toToDetail = row => {
  if (!row?.documentBagSnapshotWsid) return Message.error("未获取到病历数据 documentBagSnapshotWsid ")
  router.push({ path: "/recall/history/detail", query: { wsid: row?.wsid, inpNo: row?.inpNo } })
}
</script>
