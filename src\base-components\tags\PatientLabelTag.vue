<template>
  <template v-if="props.row.labelsCh?.length">
    <el-tag
      v-for="(label, labelIndex) in props.row.labelsCh"
      :key="labelIndex"
      :type="getPatientLabelTagType(label)"
      :style="getTagStyle"
    >
      {{ label || "未知" }}
    </el-tag>
  </template>
</template>

<script setup lang="ts">
import { computed, type CSSProperties } from "vue"

const props = defineProps<{
  row: Record<string, any>
}>()

const getPatientLabelTagType = label => {
  if (label.includes("死亡")) {
    return "info"
  } else if (label.includes("病危") || label.includes("病重")) {
    return "danger"
  } else {
    return "warning"
  }
}

const getTagStyle = computed<CSSProperties>(() => {
  if (props.row.labelsCh?.length > 1) {
    return { margin: "2px" }
  } else {
    return {}
  }
})
</script>
