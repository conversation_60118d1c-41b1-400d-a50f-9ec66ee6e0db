<template>
  <div class="user-container">
    <div class="nav-container">
      <RouterLink to="/user-setting/basic-info">基本信息</RouterLink>
      <RouterLink to="/user-setting/security-center">安全中心</RouterLink>
    </div>
    <RouterView></RouterView>
  </div>
</template>

<style lang="less" scoped>
.user-container {
  display: flex;
  justify-content: center;
}

.nav-container {
  display: flex;
  flex-direction: column;
  width: 300px;
  box-shadow: 0px 5px 50px #ebedf0;
  padding: 0px 15px;
  border-radius: 4px;
  height: fit-content;
  margin-right: 20px;
  background: #fff;
  min-width: 110px;

  a:not(:last-child) {
    border-bottom: 1px solid #ebedf0;
  }

  a {
    width: 100%;
    line-height: 50px;
    height: 50px;
    cursor: pointer;
  }

  .router-link-active {
    font-weight: 700;
  }
}
</style>
