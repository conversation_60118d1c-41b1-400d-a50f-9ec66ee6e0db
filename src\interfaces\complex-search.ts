import axios from "./axios-instance"
// 复杂查询

/**
 * @description 获取综合查询条件
 **/
export function getIntegratedQueryTermsApi() {
  return axios({
    method: "get",
    url: `/api/document/comprehensive-query/conditions`
  })
}

/**
 * @description 查询条件查询模板
 **/
export function getFilterTemplateApi(module) {
  return axios({
    method: "get",
    url: `/api/system/system-filter-template`,
    params: { module }
  })
}

/**
 * @description 新增条件模板
 **/
export function createFilterTemplateApi(data) {
  return axios({
    method: "post",
    url: `/api/system/system-filter-template`,
    data
  })
}

/**
 * @description 编辑条件模板
 **/
export function editFilterTemplateApi(data) {
  return axios({
    method: "put",
    url: `/api/system/system-filter-template`,
    data
  })
}

/**
 * @description 删除指定模板
 **/
export function deleteFilterTemplateApi(wsid) {
  return axios({
    method: "delete",
    url: `/api/system/system-filter-template/${wsid}`
  })
}

/**
 * @description 调整模板顺序
 **/
export function changeFilterTemplateSortApi(data) {
  return axios({
    method: "put",
    url: `/api/system/system-filter-template/update-sort`,
    data
  })
}
