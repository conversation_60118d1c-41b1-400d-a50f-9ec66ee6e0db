import axios from "axios"
import { throttle } from "lodash-es"
import type { AxiosRequestConfig, AxiosError } from "axios"
import { useSystemStore } from "@/stores"
import { Message } from "@/utils/message-tool"

const instance = axios.create({
  baseURL: "/",
  timeout: 20000
})
instance.defaults.withCredentials = true
instance.defaults.headers.post["Access-Control-Allow-Origin-Type"] = "*"

// 请求拦截器
instance.interceptors.request.use(
  config => {
    const sessionWsid = localStorage.getItem("SessionWsid")
    // 为sessionless时无需传sessionWsid
    if (sessionWsid && config.headers && !String(config.headers["X-Requested-Session"]).includes("sessionless"))
      config.headers["X-requested-Session"] = sessionWsid

    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器报错提示
const toastInterceptorError = throttle(
  () => {
    Message.warning("登录失效！请您重新登录")
  },
  3000,
  { trailing: false }
)

// 响应拦截器
instance.interceptors.response.use(
  response => {
    localStorage.setItem("activeTime", Date.now().toString())
    return response
  },
  error => {
    const statusCode = error.response?.status
    const { origin, pathname, search } = window.location
    if (statusCode === 401) {
      localStorage.getItem("SessionWsid") && toastInterceptorError()
      //登录失效后清空已保存的tabs
      const systemStore = useSystemStore()
      systemStore.setPageTabsData([], "")
      if (!pathname.startsWith("/login") && !pathname.startsWith("/sso") && !pathname.startsWith("/medical-preview")) {
        // window.location.href = `${origin}/login?targetPage=${pathname}${search}`
        window.location.href = `${origin}/login`
        // 终止接口后续处理
        return new Promise((resolve, reject) => {})
      }
    }
    return Promise.reject(error)
  }
)

// 数据增、删、改专用axios
export const operationAxios = (requestConfig: AxiosRequestConfig) => {
  return instance(requestConfig)
    .then(response => {
      const successMsgMap = new Map([
        ["POST", "添加成功"],
        ["PUT", "修改成功"],
        ["DELETE", "删除成功"]
      ])
      const method = response.config.method as string
      const successMsg = successMsgMap.get(method.toUpperCase()) || "操作成功"
      Message.success(successMsg)
    })
    .catch((error: AxiosError<any, any>) => {
      Message.error(error.response?.data?.userMessage || error.response?.data?.message || "操作失败")
    })
}

export default instance
