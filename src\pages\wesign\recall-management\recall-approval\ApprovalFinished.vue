<template>
  <PageContainer separate>
    <!-- 搜索区域 -->
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchForm
        :form-config="approvalFinishedSearchFormConfig"
        :form-state="searchFormState"
        @query-btn-click="handleQuery"
        @reset-btn-click="handleReset"
      />
    </template>

    <!-- 表格展示 -->
    <template #table>
      <CommonTable
        ref="recallApplyTableRef"
        table-id="recallApplyTableIdent"
        :request-params="searchParams"
        :table-columns="recallApprovalFinishedTableColumns"
        :request-api="getRecallApprovedRecordApi"
      >
        <template #inHospitalDatetime="{ row }">
          {{ formatDatetime(row.inHospitalDatetime) }}
        </template>
        <template #outHospitalDatetime="{ row }">
          {{ formatDatetime(row.outHospitalDatetime) }}
        </template>
        <template #archivistName="{ row }">
          {{ row.archivistName || "--" }}
        </template>
        <template #approveStatus="{ row }">
          {{ getApprovalStatus(row.approveStatus) || "--" }}
        </template>
        <template #approverDatetime="{ row }">
          {{ formatDatetime(row.approverDatetime) }}
        </template>
        <template #createdDatetime="{ row }">
          {{ formatDatetime(row.createdDatetime) }}
        </template>
        <template #operation="{ row }">
          <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.View)" @click="showDetailDialog(row)">
            详情
          </TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>

  <!-- 审批详情弹窗 -->
  <el-drawer
    v-if="approvalResultState.recallApprovalResultDialogVisible"
    v-model="approvalResultState.recallApprovalResultDialogVisible"
    title="审批详情"
    :size="500"
    :before-close="closeApprovalResultDialog"
  >
    <div class="approval-form">
      <div class="approval-form-item">
        <span>召回原因：</span>
        <span>{{ approvalResultState.recallReason }}</span>
      </div>
      <div class="approval-form-item">
        <span>召回时长：</span>
        <span>{{ approvalResultState.recallTimesDescribe }}</span>
      </div>
      <div v-if="approvalResultState.status === 'REJECT'" class="approval-form-item">
        <span>驳回原因：</span>
        <span>{{ approvalResultState?.rejectReason }}</span>
      </div>
      <div class="tree">
        <div class="tree-title">
          <span>病人姓名</span>
        </div>
        <div class="tree-content">
          <CommonTree ref="approvalTreeRef" node-key="wsid" :data="approvalResultState.mrClassTree" />
        </div>
      </div>
      <ApprovalDetail :business-key="approvalResultState.wsid" />
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue"
import { CommonTable, CommonTree, TableButton, PageContainer } from "@/base-components"
import { TabsRouter, SearchForm, ApprovalDetail } from "@/page-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { useUserStore } from "@/stores"
import { formatDatetime, formatRecordTree, toastError } from "@/utils"
import { getRecallApprovedRecordApi, getRecallDetailApi } from "../interface"
import {
  tabsRouterList,
  getApprovalStatus,
  menuId,
  approvalFinishedSearchFormConfig,
  recallApprovalFinishedTableColumns
} from "./config"

const { hasOperationPermission } = useUserStore()

const recallApplyTableRef = ref()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  patientFilterProp: "patientName",
  patientFilterValue: "",
  inHospitalDatetime: "",
  outHospitalDatetime: "",
  approverDatetime: "",
  approveStatus: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/*========================= 审批结果 ======================= */

// 召回审批结果回显的数据
const approvalResultState = reactive({
  recallApprovalResultDialogVisible: false,
  recallTimesDescribe: "",
  recallReason: "",
  mrClassTree: [],
  rejectReason: "",
  status: "",
  wsid: ""
})

// 开启审批详情弹窗，并获取详情数据
const showDetailDialog = (row: Record<string, any>) => {
  approvalResultState.recallApprovalResultDialogVisible = true
  if (row.wsid) {
    approvalResultState.recallTimesDescribe = row.recallTimesDescribe
    approvalResultState.recallReason = row.recallReason
    approvalResultState.status = row.approveStatus
    approvalResultState.wsid = row.wsid
    getRecallDetailApi(row.wsid)
      .then(res => {
        const result = res.data.data
        approvalResultState.mrClassTree = formatRecordTree(result)
        if (row.approveStatus === "DENY" && result?.rejectReason) {
          approvalResultState.rejectReason = result?.rejectReason
        }
      })
      .catch(error => {
        toastError(error, "获取详情内容失败")
      })
  }
}

// 召回文件tree
const approvalTreeRef = ref()

// 关闭审批结果弹窗
const closeApprovalResultDialog = () => {
  approvalResultState.recallApprovalResultDialogVisible = false
  approvalResultState.recallTimesDescribe = ""
  approvalResultState.recallReason = ""
  approvalResultState.mrClassTree = []
  approvalResultState.rejectReason = ""
  approvalResultState.status = ""
  approvalResultState.wsid = ""
}
</script>

<style lang="less" scoped>
.approval-form {
  &-item {
    padding: 5px 0;
  }
}

.tree {
  margin-top: 10px;
  border: 1px solid #e5e5e5;
}

.tree-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #333;
  background: #e5e5e5;
  padding: 5px 10px;
}

.tree-content {
  padding: 0 10px;
  min-height: 20vh;
  max-height: 25vh;
  overflow-y: scroll;
}
</style>
