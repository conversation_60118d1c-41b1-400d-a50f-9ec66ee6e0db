<template>
  <PageContainer separate>
    <!-- 头部搜素 -->
    <template #search>
      <SearchContainer @reset-btn-click="handleReset" @query-btn-click="handleQuery">
        <!-- 科室类别 -->
        <el-form-item label="科室名称">
          <el-select
            v-model="hospitalDeptWsid"
            placeholder="请选择科室"
            :loading="hospitalDeptLoading"
            :filterable="true"
            :filter-method="getHospitalDepartments"
            @visible-change="getHospitalDepartments"
          >
            <el-option v-for="item in hospitalDeptOptions" :key="item.wsid" :label="item.deptName" :value="item.wsid" />
          </el-select>
        </el-form-item>
      </SearchContainer>
    </template>

    <!-- 表格主体 -->
    <template #table>
      <!-- 映射表格 -->
      <CommonTable
        ref="commonTableRef"
        :table-columns="departmentManageColumns"
        :request-api="getDepartmentReflectApi"
        :request-params="requestParams"
      >
        <template #header>
          <AddButton @click="openDialog('新增', null)">新增</AddButton>
        </template>
        <template #operation="{ row }">
          <TableButton @click="openDialog('编辑', row)">编辑</TableButton>
          <TableButton @click="deleteReflect(row)">删除</TableButton>
        </template>
      </CommonTable>
    </template>

    <!-- 新增/编辑弹窗 -->
    <DialogContainer
      v-model:visible="dialogState.visible"
      :title="dialogState.title"
      :confirm-callback="changeReflection"
      :confirm-loading="dialogState.submitLoading"
    >
      <el-form label-suffix=":" label-width="90px">
        <!-- 编辑映射的科室名称 -->
        <el-form-item label="科室名称">
          <el-input
            v-if="dialogState.title === '编辑'"
            :value="dialogState.clickRow?.hospitalDeptName"
            :disabled="true"
          />
          <!-- 新增映射的科室名称 -->
          <el-select
            v-else
            v-model="formState.hospitalDeptWsid"
            placeholder="请选择科室"
            :loading="hospitalDeptLoading"
            :filterable="true"
            :filter-method="getHospitalDepartments"
            @visible-change="getHospitalDepartments"
          >
            <el-option v-for="item in hospitalDeptOptions" :key="item.wsid" :label="item.deptName" :value="item.wsid" />
          </el-select>
        </el-form-item>
        <el-form-item label="HQMS科室">
          <el-select
            v-model="formState.standardKey"
            placeholder="请选择对应的HQMS科室"
            :loading="standardDeptLoading"
            :filterable="true"
            :filter-method="searchStandardDepartments"
            @visible-change="getStandardDepartments"
          >
            <el-option
              v-for="item in filterStandardDeptOptions"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </DialogContainer>
  </PageContainer>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from "vue"
import { PageContainer, SearchContainer, CommonTable, TableButton, DialogContainer, AddButton } from "@/base-components"
import { getHospitalDeptsApi, getHQMSDepartmentApi } from "@/interfaces"
import { Message, SystemPrompt, toastError } from "@/utils"
import { getDepartmentReflectApi, changeHQMSReflectionApi, deleteHQMSReflectionApi } from "../interface"
import { departmentManageColumns } from "./config"

onMounted(() => {
  getHospitalDepartments(true)
  getStandardDepartments(true)
})

/* =========================== 搜索表单 ========================== */
const hospitalDeptWsid = ref("")
// 获取科室列表的loading
const hospitalDeptLoading = ref(false)
// 系统科室列表
const hospitalDeptOptions = ref<Array<Record<string, any>>>([])
const requestParams = reactive({ filters: "" })

// 搜索表单
function handleQuery() {
  requestParams.filters = `hospitalDeptWsid=${hospitalDeptWsid.value}`
}

// 重置表单
function handleReset() {
  hospitalDeptWsid.value = ""
  requestParams.filters = ""
}

// 获取系统科室:value 搜索的值或Boolean（是否打开弹窗）
function getHospitalDepartments(value) {
  // 关闭弹窗
  if (value === false) return
  //   打开弹窗或者搜索科室
  hospitalDeptLoading.value = true
  getHospitalDeptsApi(value === true ? "" : value)
    .then(res => {
      hospitalDeptOptions.value = res.data.data
    })
    .catch(err => {
      toastError(err, "获取系统科室失败")
    })
    .finally(() => {
      hospitalDeptLoading.value = false
    })
}

/* ===================== 编辑弹窗 ===================== */
const commonTableRef = ref()

// 获取hqms科室列表的loading
const standardDeptLoading = ref(false)
// hqms科室列表
const standardDeptOptions = ref<Array<Record<string, any>>>([])
// 经过筛选的hqms科室列表
const filterStandardDeptOptions = ref<Array<Record<string, any>>>([])

// 获取HQMS科室
function getStandardDepartments(visible) {
  if (!visible) return
  standardDeptLoading.value = true
  getHQMSDepartmentApi()
    .then(res => {
      standardDeptOptions.value = res.data.data
      filterStandardDeptOptions.value = res.data.data
    })
    .catch(err => {
      toastError(err, "获取hqms科室列表失败")
    })
    .finally(() => {
      standardDeptLoading.value = false
    })
}

// 筛选HQMS科室
function searchStandardDepartments(value) {
  filterStandardDeptOptions.value = standardDeptOptions.value.filter(item => item.value.indexOf(value) !== -1)
}

// 弹窗状态
const dialogState = reactive({
  title: "",
  clickRow: null as null | Record<string, any>,
  visible: false,
  submitLoading: false
})

// 表单数据
const formState = reactive({
  standardKey: "",
  hospitalDeptWsid: ""
})

// 打开弹窗
function openDialog(title, row) {
  dialogState.clickRow = row
  dialogState.title = title
  formState.hospitalDeptWsid = title === "编辑" ? row?.hospitalDeptWsid : ""
  formState.standardKey = title === "编辑" ? row?.standardKey : ""
  dialogState.visible = true
}

// 新增/编辑映射关系
function changeReflection() {
  let data: Record<string, any> = {
    standardType: "HQMS"
  }
  //编辑获取id
  if (dialogState.title === "编辑") data.id = dialogState.clickRow?.id

  //获取当前选择的系统科室
  const hospitalDept = hospitalDeptOptions.value.find(item => item.wsid === formState.hospitalDeptWsid)
  if (!hospitalDept) return Message.error("请检查系统科室")
  data.hospitalDeptWsid = hospitalDept.wsid
  data.hospitalDeptCode = hospitalDept.deptCode
  data.hospitalDeptName = hospitalDept.deptName

  //获取当前选择的hqms科室
  const standardDept = filterStandardDeptOptions.value.find(item => item.key === formState.standardKey)
  if (!standardDept) return Message.error("请检查HQMS科室")
  data.standardKey = standardDept.key
  data.standardName = standardDept.value

  //新增/编辑请求
  dialogState.submitLoading = true
  changeHQMSReflectionApi(data)
    .then(res => {
      if (commonTableRef.value) commonTableRef.value.refreshTableData()
      dialogState.visible = false
      Message.success(`${dialogState.title}成功`)
    })
    .catch(err => {
      toastError(err, `${dialogState.title}失败`)
    })
    .finally(() => {
      dialogState.submitLoading = false
    })
}

/* ===================== 删除映射 ===================== */
function deleteReflect(row) {
  SystemPrompt(`请确认是否要删除${row.hospitalDeptName}和${row.standardName}的映射关系`).then(() => {
    deleteHQMSReflectionApi(row.id)
      .then(res => {
        if (commonTableRef.value) commonTableRef.value.refreshTableData()
        Message.success("删除成功")
      })
      .catch(err => {
        toastError(err, "删除失败")
      })
  })
}
</script>
