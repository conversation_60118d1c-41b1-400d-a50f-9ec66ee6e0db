<template>
  <Spin :spinning="systemStore.pageLoading" :tip="systemStore.loadingText">
    <div id="sso"></div>
  </Spin>
</template>

<script setup lang="ts">
import { onMounted, h } from "vue"
import { useRouter, useRoute } from "vue-router"
import { ElNotification } from "element-plus"
import { Spin } from "ant-design-vue"
import { querySystemIcon, getUserPermissionsApi, getUserHospitalInfo } from "@/interfaces"
import { useSystemStore, useUserStore, useResourceStore } from "@/stores"
import { toastError, getUrlBase64, SystemAlert } from "@/utils"
import { ssoLogin } from "./interface"
const router = useRouter()
const route = useRoute()

const systemStore = useSystemStore()
const userStore = useUserStore()
const resourceStore = useResourceStore()

onMounted(() => {
  systemStore.showLoading("加载中")
  ssoLogin((route.query?.token as string) ?? "")
    .then(res => {
      getUserInfo(res.data.data)
    })
    .catch(err => {
      toastError(err, "接口响应失败")
    })
})

async function getUserInfo(resData) {
  // 获取用户配置信息
  try {
    const { sessionId, passwordExpire } = resData
    localStorage.setItem("SessionWsid", sessionId)
    userStore.setUserInfo({ ...resData })
    userStore.$patch({ passwordExpire })

    systemStore.showLoading("正在加载用户配置信息...")

    // 获取菜单权限树
    const res = await getUserPermissionsApi()
    const jsonData = res.data?.data ?? "[]"
    const userResourceConfig = JSON.parse(jsonData)
    userStore.$patch({ userMenus: userResourceConfig })
    if (!userResourceConfig.length) {
      localStorage.removeItem("SessionWsid")
      return SystemAlert("当前账号尚未配置权限，请联系管理员配置权限")
    }

    // 获取hospital信息
    const hospitalInfo = await getUserHospitalInfo()
    userStore.setUserHospitalInfo(hospitalInfo)

    // 每次登录获取最新配置的logo和favicon
    getIconSrc("favicon")
    getIconSrc("logo")

    // 跳转至首个有权限的菜单
    ElNotification({
      type: "success",
      title: userStore.userHospitalInfo.realName + "，您好",
      message: h("div", {}, [
        h("span", {}, "欢迎登录无纸化病案系统"),
        h("i", { class: "ri-sparkling-line", style: { fontSize: "18px", marginLeft: "4px", color: "#ffba08" } })
      ]),
      offset: 50
    })
    router.push({ path: userResourceConfig[0]?.path ?? "/user-setting" })
  } catch (err: any) {
    toastError(err, "获取用户配置信息失败")
  } finally {
    systemStore.hideLoading()
  }
}

// 获取远程icon
const getIconSrc = (type: "favicon" | "logo") => {
  querySystemIcon(type)
    .then(async res => {
      if (type === "favicon") {
        const faviconBase = await getUrlBase64(res)
        resourceStore.setFavicon(faviconBase)
      } else {
        const faviconBase = await getUrlBase64(res)
        resourceStore.setLogo(faviconBase)
      }
    })
    .catch(async err => {
      if (type === "favicon") {
        const faviconBase = ""
        resourceStore.setFavicon(faviconBase)
      } else {
        const faviconBase = ""
        resourceStore.setLogo(faviconBase)
      }
    })
}
</script>

<style lang="less" scoped>
#sso {
  height: 100vh;
}
</style>
