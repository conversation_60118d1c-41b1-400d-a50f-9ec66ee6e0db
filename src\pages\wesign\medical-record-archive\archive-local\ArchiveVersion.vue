<template>
  <div id="medical-record-version">
    <div v-loading="state.loading" class="view-left">
      <div class="select-history" :style="{ width: recordTreeRef?.state.isCollapse ? '0px' : '264px' }">
        <div class="version-title">版本</div>
        <el-select v-model="state.selectValue" class="version-select" placeholder="请选择版本">
          <el-option
            v-for="item in state.versionOptions"
            :key="item.code"
            :label="item.versionName"
            :value="item.code"
          />
        </el-select>
      </div>
      <div class="content">
        <MedicalRecordTree
          ref="recordTreeRef"
          can-collapse
          :base-info="state.baseInfo"
          :tree-info="state.treeInfo"
          @click-pdf="handlePdfClick"
        />
      </div>
    </div>
    <div class="view-middle common-box-shadow">
      <PdfPreviewComponent :src="state.pdfSrc" />
    </div>
    <div class="view-right">
      <DocumentMeta
        v-loading="state.loading"
        can-collapse
        :document-wsid="state.documentWsid"
        :base-info-data="state.firstPageFields"
        :file-wsid="state.targetFileWsid"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, watch, ref } from "vue"
import { useRoute } from "vue-router"
import { PdfPreviewComponent } from "@/base-components"
import { MedicalRecordTree, DocumentMeta } from "@/page-components"
import { getArchiveVersionDetailApi } from "@/interfaces"
import { toastError } from "@/utils"
import { getArchiveVersionsApi } from "./interface"
import type { BaseOptionItem } from "@/types"

const route = useRoute()
const inpNo = route.query?.inpNo as string

const recordTreeRef = ref()

const state = reactive({
  loading: false,
  pdfSrc: "",
  baseInfo: {},
  firstPageFields: [],
  versionOptions: [] as Array<Record<string, string>>,
  selectValue: "",
  targetFileWsid: "",
  treeInfo: {} as any,
  documentWsid: ""
})

onMounted(() => {
  getVersionSource(inpNo)
})

const handlePdfClick = node => {
  state.pdfSrc = "/api" + node?.contentFilePath
  state.targetFileWsid = node?.fileWsid
  state.documentWsid = node?.wsid
}

// 获取指定版本的数据
const getSelectedVersionData = () => {
  if (!state.selectValue) return
  const params = { inpNo, versionCode: state.selectValue }

  state.loading = true
  getArchiveVersionDetailApi(params)
    .then(res => {
      state.baseInfo = res.data.data.baseInfo
      state.firstPageFields = res.data.data.firstPageFields
      state.treeInfo = res.data.data.treeInfo
      state.loading = false
    })
    .catch(err => {
      toastError(err)
      state.loading = false
    })
}

// 获取所有版本
function getVersionSource(inpNo) {
  if (!inpNo) return
  getArchiveVersionsApi(inpNo).then(res => {
    state.versionOptions = res.data.data
    state.selectValue = state.versionOptions[state.versionOptions.length - 1]?.code
  })
}

watch(
  () => state.selectValue,
  () => getSelectedVersionData(),
  {
    immediate: true
  }
)
</script>

<style lang="less" scoped>
#medical-record-version {
  display: flex;
  justify-content: space-between;
  height: 100%;
  .view-middle {
    overflow-x: auto;
    min-width: 400px;
    flex: 1;
    margin: 0 16px 0px 0px;
    box-sizing: border-box;
  }
  .view-left {
    height: 100%;
    .select-history {
      display: flex;
      width: 234px;
      height: 56px;
      margin-bottom: 16px;
      background-color: white;
      box-shadow: 0 2px 8px rgb(0 35 114 / 10%);
      line-height: 56px;
      gap: 20px;
      align-items: center;
      overflow: hidden;
      transition: all 0.5s;
      .version-title {
        font-size: 16px;
        color: rgb(3 8 20 / 85%);
        font-weight: 700;
        font-style: normal;
        margin-left: 15px;
        white-space: nowrap;
      }
      .version-select {
        width: 180px;
      }
    }
    .content {
      height: calc(100% - 72px);
    }
  }
}
</style>
