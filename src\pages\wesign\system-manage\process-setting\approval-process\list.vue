<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />

      <SearchForm
        :form-config="processSearchFormConfig"
        :form-state="searchFormState"
        @query-btn-click="handleQuery"
        @reset-btn-click="handleReset"
      />
    </template>

    <template #table>
      <CommonTable
        ref="commonTableRef"
        :table-columns="approvalProcessTableColumns"
        :request-api="getApprovalProcessListApi"
        :request-params="searchParams"
      >
        <template #header>
          <AddButton @click="createProcess">新建流程</AddButton>
        </template>
        <template #operation="{ row }">
          <TableButton @click="editProcess(row)">编辑</TableButton>
          <TableButton @click="modifyStatus(row)">{{ row.status === "ENABLE" ? "禁用" : "启用" }}</TableButton>
          <TableButton @click="deleteProcess(row)">删除</TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import { useRouter } from "vue-router"
import { PageContainer, TableButton, CommonTable, AddButton } from "@/base-components"
import { SearchForm, TabsRouter } from "@/page-components"
import { useTableSearch } from "@/hooks"
import { SystemPrompt, Message } from "@/utils"
import { deleteApprovalProcessApi, changeApprovalProcessStatusApi, getApprovalProcessListApi } from "../../interface"
import { tabsRouterList } from "../config"
import { processSearchFormConfig, approvalProcessTableColumns } from "./config"

const router = useRouter()

const commonTableRef = ref<InstanceType<typeof CommonTable>>()

/*================================= 搜索表单 =================================*/

const searchFormState = reactive({
  // 流程名称
  flowName: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/*===========================流程配置=======================*/

// 新建流程
const createProcess = () => {
  router.push({
    path: "/system-manage/process-setting/create"
  })
}

//编辑流程
const editProcess = (row: Record<string, any>) => {
  if (!row?.id) return
  router.push({
    path: "/system-manage/process-setting/edit",
    query: {
      processId: row.id,
      applyDeptCode: row.applyDeptCode,
      flowType: row.flowType,
      flowName: row.flowName,
      flowDefineId: row.flowDefineId
    }
  })
}

// 切换启用&禁用状态
const modifyStatus = (row: Record<string, any>) => {
  SystemPrompt(
    `您正在${row.status === "DISABLE" ? "启用" : "禁用"}名称为“${row.flowName}”的审批流程，确定${
      row.status === "DISABLE" ? "启用" : "禁用"
    }？`,
    "warning"
  ).then(() => {
    changeApprovalProcessStatusApi(row.id, row.status === "ENABLE" ? "DISABLE" : "ENABLE")
      .then(() => {
        Message.success(`${row.status === "DISABLE" ? "启用" : "禁用"}成功`)
        commonTableRef.value?.refreshTableData()
      })
      .catch(error => {
        Message.error(error?.response?.errMessage ?? `${row.status === "DISABLE" ? "启用" : "禁用"}失败`)
      })
  })
}

// 删除流程
const deleteProcess = (row: Record<string, any>) => {
  SystemPrompt(`您正在删除名称为“${row.flowName}”的审批流程，确定删除？`, "error").then(() =>
    deleteApprovalProcessApi(row.id)
      .then(() => {
        Message.success("删除成功")
        commonTableRef.value?.refreshTableData()
      })
      .catch(error => {
        Message.error(error?.response?.errMessage ?? "删除失败")
      })
  )
}
</script>
