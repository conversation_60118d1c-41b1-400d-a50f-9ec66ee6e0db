<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="outpatientTabsConfig" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <el-form-item label="导出日期">
          <el-date-picker
            v-model="searchFormState.createdDatetime"
            type="daterange"
            clearable
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :disabled-date="disabledDate"
            value-format="x"
            @calendar-change="handleCalendarChange"
          ></el-date-picker>
        </el-form-item>
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="tableRef"
        row-key="wsid"
        table-id="outpatientExportRecordTableIdent"
        :table-columns="outpatientExportRecordTableColumns"
        :request-api="getOutpatientExportRecordApi"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <template #header>
          <div class="flex-start">
            已选中
            <span style="color: #f49a23">{{ childRows.length }}</span>
            项，共
            <span style="color: #f49a23">{{ totalRecordCount }}</span>
            条记录
          </div>
        </template>
        <template #createdDatetime="{ row }">
          <i v-if="row.wsid" class="ri-folder-fill" style="margin: 0 4px; color: #f49a23"></i>
          <i v-else class="ri-file-line" style="margin: 0 4px; color: #2aa515"></i>
          {{ row.wsid ? formatDatetime(row.createdDatetime) : row.fileName }}
        </template>
        <template #startDatetime="{ row }">
          {{ formatDatetime(row.startDatetime) }}
        </template>
        <template #endDatetime="{ row }">
          {{ formatDatetime(row.endDatetime) }}
        </template>
        <template #status="{ row }">
          <!-- @vue-ignore -->
          <el-tag :type="getExportStatusTagType(row)">{{ getExportStatusText(row) }}</el-tag>
        </template>
        <template #operation="{ row }">
          <TableButton v-if="isDownloadable(row)" @click="handleDownload(row)">下载</TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref, computed } from "vue"
import { cloneDeep } from "lodash-es"
import { PageContainer, SearchContainer, CommonTable, TableButton } from "@/base-components"
import { TabsRouter } from "@/page-components"
import { useTableSearch } from "@/hooks"
import axios from "@/interfaces/axios-instance"
import { useSystemStore } from "@/stores"
import { downloadFile, formatDatetime, Message, toastError } from "@/utils"
import { exportStatusOptions, outpatientExportRecordTableColumns, outpatientTabsConfig } from "../config"
import { downloadOutpatientFileApi, getOutpatientExportRecordApi } from "../interface"

const systemStore = useSystemStore()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  createdDatetime: "" // 导出日期
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/* ======================== 表格数据 ======================== */

const tableRef = ref<InstanceType<typeof CommonTable>>()

const dataCallback = (data: Array<Record<string, any>>) => {
  return data.map(row => {
    row.children = row.details ? cloneDeep(row.details) : []
    delete row.details
    row.children.forEach(child => {
      child.createdDatetime = row.createdDatetime
    })
    return row
  })
}

// 当前选中表格项
const selectedRows = computed(() => tableRef.value?.tableState?.selectedRows ?? [])
const childRows = computed(() => selectedRows.value.filter(item => !item.wsid))

// 当前选中的总记录数
const totalRecordCount = computed(() => {
  return childRows.value.reduce((count, row) => {
    count += row.recordNum
    return count
  }, 0)
})

// 获取导出状态tagType
const getExportStatusTagType = row => {
  return exportStatusOptions.find(option => option.value === row.status)?.tagType || "info"
}

// 获取导出状态tagText
const getExportStatusText = row => {
  return exportStatusOptions.find(option => option.value === row.status)?.label || "--"
}

// 下载按钮禁用：没有文件wsid则禁用
const isDownloadable = row => {
  if (row.wsid) {
    return row.children.find(child => child.fileWsid)
  } else {
    return row.fileWsid
  }
}

/* ======================== 下载 ======================== */

// 下载
const handleDownload = async row => {
  // 选中的父节点
  if (row.wsid) {
    try {
      systemStore.showLoading()
      const res = await downloadOutpatientFileApi([row.wsid])
      systemStore.hideLoading()
      downloadFile({
        fileData: res.data,
        fileType: "application/zip",
        fileName: decodeURIComponent(
          res.headers["content-disposition"].split("filename")[1].split("=")[1].replaceAll(`"`, "")
        )
      })
      Message.success("下载数据成功！")
    } catch (error: any) {
      systemStore.hideLoading()
      toastError(error)
    }
  }
  // 选中的子节点
  else {
    try {
      systemStore.showLoading()
      const res = await axios({
        method: "get",
        url: `/api/files/${row.fileWsid}`,
        responseType: "blob"
      })
      systemStore.hideLoading()
      downloadFile({
        fileData: res.data,
        fileType: "application/vnd.ms-excel",
        fileName: decodeURIComponent(
          res.headers["content-disposition"].split("filename")[1].split("=")[1].replaceAll(`"`, "")
        )
      })
      Message.success("下载数据成功！")
    } catch (error: any) {
      systemStore.hideLoading()
      toastError(error)
    }
  }
}

/* ======================== 时间筛选限制 ======================== */

const startDate = ref()
const days = 30 * 24 * 3600 * 1000

const handleCalendarChange = date => {
  const [minDate, maxDate] = date
  if (minDate && !maxDate) {
    startDate.value = minDate // 记录选中的首个日期
  } else {
    startDate.value = null
  }
}

const disabledDate = time => {
  // 如果已经选择了开始日期，则禁用所有超出30天范围的日期
  if (startDate.value) {
    return time.getTime() < startDate.value?.getTime() - days || time.getTime() > startDate.value?.getTime() + days
  } else {
    return false
  }
}
</script>
