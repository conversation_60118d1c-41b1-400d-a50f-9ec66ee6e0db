<template>
  <el-tooltip :disabled="!$props.disabled" :content="$props.tooltip" placement="top">
    <span class="table-button">
      <el-button link type="primary" :disabled="$props.disabled" @click="$emit('click')">
        <slot></slot>
      </el-button>
    </span>
  </el-tooltip>
</template>

<script setup lang="ts">
defineProps({
  tooltip: { type: String, default: "无此操作权限" },
  disabled: { type: Boolean, default: false }
})

defineEmits(["click"])
</script>

<style lang="less" scoped>
.table-button {
  margin-left: 4px;
  :deep(.is-disabled, .is-disabled:hover) {
    color: #aaa !important;
  }

  :deep(button.is-link:focus) {
    color: var(--el-color-primary);
  }
}
</style>
