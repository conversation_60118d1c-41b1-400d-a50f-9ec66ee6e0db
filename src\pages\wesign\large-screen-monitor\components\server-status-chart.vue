<template>
  <div class="server-status-container">
    <div class="server-status-title"></div>
    <div class="server-status">
      <img v-if="systemStatus === SystemStatusEnum.ERROR" src="@/assets/svg/monitor/abnormal.svg" />
      <img v-else-if="systemStatus === SystemStatusEnum.WARN" src="@/assets/svg/monitor/warn.svg" />
      <img v-else-if="systemStatus === SystemStatusEnum.GOOD" src="@/assets/svg/monitor/health.svg" />
      <span class="status-text">系统运行状态</span>
      <span v-if="systemStatus === SystemStatusEnum.ERROR" class="text-item" style="color: #d03944">异常</span>
      <span v-else-if="systemStatus === SystemStatusEnum.WARN" class="text-item" style="color: #fdce00">警告</span>
      <span v-else-if="systemStatus === SystemStatusEnum.GOOD" class="text-item" style="color: #37ce56">健康</span>
    </div>
    <div class="chart-container">
      <v-chart class="server-status-chart" :option="option" autoresize></v-chart>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, provide, watch } from "vue"
import { BarChart } from "echarts/charts"
import { TooltipComponent, GridComponent, LegendComponent } from "echarts/components"
import { use } from "echarts/core"
import { CanvasRenderer } from "echarts/renderers"
import VChart, { THEME_KEY } from "vue-echarts"
import { SystemStatusEnum } from "../config"

/* ===================== 数据处理操作 ===================== */

const props = defineProps({
  systemMonitorData: { type: Object, default: null }
})

// cpu、内存、硬盘总容量(未经过处理的)
const originalTotalData = ref([] as any)
// cpu、内存、硬盘总容量
const totalData = ref([] as any)
// cpu、内存、硬盘剩余容量
const remainingData = ref([] as any)
// 系统运行状态
const systemStatus = ref("")
// 硬盘状态
const diskStatus = ref("")
// 内存状态
const memoryStatus = ref("")
// cpu状态
const cpuStatus = ref("")
// 硬盘状态对应的背景色
const diskStatusColor = ref("")
// 内存状态对应的背景色
const memoryStatusColor = ref("")
// cpu状态对应的背景色
const cpuStatusColor = ref("")

// 二次处理系统硬件数据，转换为需要的格式
function formatData(systemMonitorData) {
  systemStatus.value = systemMonitorData.totalStatus // 系统运行状态
  diskStatus.value = systemMonitorData.diskStatus // 硬盘状态
  memoryStatus.value = systemMonitorData.memoryStatus // 内存状态
  cpuStatus.value = systemMonitorData.cpuStatus // cpu状态
  originalTotalData.value = [ systemMonitorData.diskTotal, systemMonitorData.memoryTotal, systemMonitorData.cpuTotal ]
  // 此处操作是为了让条形图显示一样的长度，所以统一扩大到和硬盘总容量一样的大小
  // 物理内存扩大倍数
  const memoryMultiple = parseFloat(systemMonitorData.diskTotal) / parseFloat(systemMonitorData.memoryTotal)
  // cpu总内存扩大倍数
  const cpuMultiple = parseFloat(systemMonitorData.diskTotal) / parseFloat(systemMonitorData.cpuTotal)
  totalData.value = [
    { value: parseFloat(systemMonitorData.diskTotal), name: "硬盘总容量", multiple: 1 },
    { value: parseFloat(systemMonitorData.diskTotal), name: "内存总容量", multiple: Number(memoryMultiple) },
    { value: parseFloat(systemMonitorData.diskTotal), name: "CPU总容量", multiple: Number(cpuMultiple) }
  ]
  remainingData.value = [
    parseFloat(systemMonitorData.diskTotal) - parseFloat(systemMonitorData.diskRemaining),
    parseFloat(systemMonitorData.diskTotal) - parseFloat(systemMonitorData.memoryRemaining) * memoryMultiple,
    parseFloat(systemMonitorData.diskTotal) - parseFloat(systemMonitorData.cpuRemaining) * cpuMultiple
  ]
}

// 根据硬件状态获取所对应的背景色
function getHardwareStatusColor(hardwareStatus, hardwareStatusColor) {
  if (hardwareStatus.value === SystemStatusEnum.ERROR) {
    hardwareStatusColor.value = "#FF5059"
  } else if (hardwareStatus.value === SystemStatusEnum.WARN) {
    hardwareStatusColor.value = "#FDCE00"
  } else {
    hardwareStatusColor.value = "#37CE56"
  }
}

watch(
  () => props.systemMonitorData,
  () => {
    formatData(props.systemMonitorData)
    getHardwareStatusColor(diskStatus, diskStatusColor)
    getHardwareStatusColor(memoryStatus, memoryStatusColor)
    getHardwareStatusColor(cpuStatus, cpuStatusColor)
  }
)

/* ===================== 配置项 ===================== */
use([TooltipComponent, GridComponent, LegendComponent, BarChart, CanvasRenderer])
provide(THEME_KEY, "dark")

// 存储背景色
const myColor = ref([{ color: diskStatusColor }, { color: memoryStatusColor }, { color: cpuStatusColor }])

const option = ref({
  grid: {
    left: "10%",
    top: "13",
    right: "0",
    bottom: "0",
    containLabel: false
  },

  xAxis: {
    type: "value",
    splitLine: { show: false }, //坐标轴在 grid 区域中的分隔线
    axisLabel: { show: false }, //坐标轴刻度标签
    axisTick: { show: false }, //坐标轴刻度
    axisLine: { show: false } //坐标轴轴线
  },
  yAxis: {
    nameTextStyle: {
      align: "center",
      verticalAlign: "middle"
    },
    type: "category",
    axisTick: { show: false },
    axisLine: { show: false },
    data: ["", "", ""]
  },
  series: [
    {
      type: "bar",
      label: {
        show: true,
        position: ["68%", -22],
        label: {
          show: true
        },
        formatter(params) {
          return params.name + ": " + originalTotalData.value[params.dataIndex]
        },
        color: "rgba(255,255,255,0.6)",
        fontSize: 12
      },
      barGap: "-100%",

      barWidth: 20,
      data: totalData,
      color: "rgba(255,255,255,.3)",
      itemStyle: {
        normal: {
          barBorderRadius: 10
        }
      }
    },
    {
      type: "bar",
      barWidth: 20,
      data: remainingData,
      label: {
        show: true,
        position: [0, -22],
        formatter(params) {
          return "使用" + ((params.value / totalData.value[params.dataIndex].value) * 100).toFixed(0) + "%"
        },
        color: "rgba(255,255,255,1)",
        fontSize: 12
      },
      itemStyle: {
        normal: {
          barBorderRadius: 10,
          color: function (params) {
            return myColor.value[params.dataIndex].color
          }
        }
      }
    }
  ]
})
</script>

<style lang="less" scoped>
.server-status-container {
  min-width: 458px;
  width: 28%;
  margin-left: 20px;
  .server-status-title {
    width: 100%;
    height: 43px;
    background: url("@/assets/svg/monitor/server-status.svg") no-repeat;
    background-size: 100%;
  }
  .server-status {
    position: relative;
    display: flex;
    justify-content: center;
    width: 100%;
    height: 150px;
    margin-top: 15px;
    background: linear-gradient(180deg, #061742 0%, rgba(6, 23, 66, 0) 49%);
    img {
      width: 100%;
      height: 100%;
    }
    .status-text {
      position: absolute;
      top: 10px;
      color: rgba(255, 255, 255, 0.6);
      font-weight: 500;
      font-size: 14px;
    }
    .text-item {
      position: absolute;
      top: 30px;
      font-size: 36px;
      font-weight: bold;
    }
  }
  .chart-container {
    .server-status-chart {
      width: 100%;
      height: 224px;
    }
  }
}
</style>
