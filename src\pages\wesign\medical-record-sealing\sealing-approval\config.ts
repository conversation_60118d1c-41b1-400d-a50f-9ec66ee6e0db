import type { SearchFormConfigItem, TableColumnItem } from "@/types"

export const menuId = "/sealing/approval"

export const sealingApprovedColumns: Array<TableColumnItem> = [
  { prop: "createdDatetime", label: "申请时间", minWidth: 170, sortable: true },
  { prop: "applicant", label: "申请人", minWidth: 100, must: true },
  { prop: "applicantCertificatesNo", label: "申请人证件号", minWidth: 170, must: true },
  { prop: "applicantContent", label: "联系方式", minWidth: 150 },
  { prop: "relationship", label: "与患者关系", minWidth: 120 },
  { prop: "mrNo", label: "病案号", minWidth: 100, sortable: true },
  { prop: "patientId", label: "患者编号", minWidth: 120, sortable: true },
  { prop: "patientName", label: "姓名", minWidth: 100 },
  { prop: "visitId", label: "住院次数", minWidth: 120, sortable: true },
  { prop: "outHospitalDatetime", label: "出院时间", minWidth: 170, sortable: true },
  { prop: "type", label: "申请操作类型", minWidth: 120 },
  { prop: "authorName", label: "操作人", minWidth: 100 },
  { prop: "applyReason", label: "申请原因", minWidth: 100 },
  { prop: "status", label: "审批状态", minWidth: 100 },
  { prop: "fileStatusName", label: "签名状态", minWidth: 100 },
  { prop: "operation", label: "操作", width: 180, fixed: "right" }
]

export const sealingWaitingApproveColumns: Array<TableColumnItem> = [
  { prop: "createdDatetime", label: "申请时间", minWidth: 170, sortable: true },
  { prop: "applicant", label: "申请人", minWidth: 100, must: true },
  { prop: "applicantCertificatesNo", label: "申请人证件号", minWidth: 170, must: true },
  { prop: "applicantContent", label: "联系方式", minWidth: 150 },
  { prop: "relationship", label: "与患者关系", minWidth: 120 },
  { prop: "mrNo", label: "病案号", minWidth: 100, sortable: true },
  { prop: "patientId", label: "患者编号", minWidth: 120, sortable: true },
  { prop: "patientName", label: "姓名", minWidth: 100 },
  { prop: "visitId", label: "住院次数", minWidth: 120, sortable: true },
  { prop: "outHospitalDatetime", label: "出院时间", minWidth: 170, sortable: true },
  { prop: "type", label: "申请操作类型", minWidth: 120 },
  { prop: "authorName", label: "操作人", minWidth: 100 },
  { prop: "applyReason", label: "申请原因", minWidth: 100 },
  { prop: "status", label: "审批状态", minWidth: 100 },
  { prop: "operation", label: "操作", width: 120, fixed: "right" }
]

export const tabsRouterList = [
  { label: "待审批", path: "/sealing/approval/waiting-approve" },
  { label: "已审批", path: "/sealing/approval/approved" }
]

// 封存时间类型转中文
export const unlockTypeToStr = type => {
  switch (type) {
    case "UNLIMITED_TIME":
      return "不限定解封时间"
    case "LIMIT_TIME":
      return "限定解封时间"
    default:
      return "未知"
  }
}

// 是否允许追加病历 类型转中文
export const additionalCasesToStr = type => {
  switch (type) {
    case "ALLOW":
      return "允许"
    case "NOT_ALLOW":
      return "不允许"
    default:
      return "未知"
  }
}

export const applyControlOptions = [
  { label: "封存", value: "0" },
  { label: "解封", value: "1" }
]

export const sealingApprovalSearchFormConfig: Array<SearchFormConfigItem> = [
  { type: "patient", label: "患者信息" },
  { type: "input", label: "申请人", prop: "applicant" },
  { type: "select", label: "申请类型", prop: "type", options: applyControlOptions }
]

export enum SealingStatusEnum {
  UNSEALED = "UNSEALED",
  REQUESTED_SEALING = "REQUESTED_SEALING",
  REQUESTED_UNBLOCK = "REQUESTED_UNBLOCK",
  SEALED = "SEALED"
}
