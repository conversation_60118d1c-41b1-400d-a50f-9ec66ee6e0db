import { ref, onMounted } from "vue"
import { BaseOptionItem } from "@/types"

interface CommonOptionsHookProps {
  getOptionsApi?: () => Promise<CommonObjectList>
  optionsDataSource?: CommonObjectList
  labelAlias?: string
  valueAlias?: string
}

// getOptionsApi和optionsData必须传一个
const useCommonOptions = ({
  getOptionsApi, // 获取选项的接口
  optionsDataSource = [], // 选项数据源
  labelAlias = "label",
  valueAlias = "value"
}: CommonOptionsHookProps) => {
  const options = ref<Array<BaseOptionItem>>(
    optionsDataSource.map(item => ({ label: item[labelAlias], value: item[valueAlias] }))
  )

  onMounted(() => {
    if (getOptionsApi) {
      getOptionsApi().then(resData => {
        options.value = resData.map(item => ({
          label: item[labelAlias],
          value: item[valueAlias]
        }))
      })
    }
  })

  // 根据option的value获取label名称
  const getOptionLabel = (targetValue: string | number) => {
    const targetOption = options.value.find(option => option.value === targetValue)
    return targetOption ? targetOption.label : ""
  }

  return { options, getOptionLabel }
}

export default useCommonOptions
