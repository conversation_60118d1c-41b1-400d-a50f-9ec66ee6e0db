import type { TableColumnItem, BaseOptionItem } from "@/types"

export const menuId = "/collect/monitor"

export const tabsRouterList = [
  { path: "/collect/monitor/index", label: "采集监控" },
  { path: "/collect/monitor/third-party-push", label: "第三方推送" },
  { path: "/collect/monitor/medical-record-remake", label: "病案翻拍" }
]

export const monitorColumns: Array<TableColumnItem> = [
  { type: "selection", width: 50, fixed: "left" },
  { prop: "patientName", label: "姓名", minWidth: 100 },
  { prop: "patientId", label: "患者编号", minWidth: 120 },
  { prop: "mrNo", label: "病案号", minWidth: 150 },
  { prop: "visitId", label: "住院次数", minWidth: 100 },
  { prop: "dataSourceName", label: "业务系统", minWidth: 150 },
  { prop: "mrClassName", label: "文档类型", minWidth: 150 },
  { prop: "integrationName", label: "采集方式", minWidth: 150 },
  { prop: "countFile", label: "采集成功数", minWidth: 120 },
  { prop: "executionTime", label: "采集时间", minWidth: 180 },
  { prop: "collectStatusDesc", label: "采集状态", minWidth: 120 },
  { prop: "errorInfo", label: "错误信息", minWidth: 180 },
  { prop: "executor", label: "操作人", minWidth: 120 },
  { prop: "operation", label: "操作", width: 100, fixed: "right" }
]

export enum CollectStatusEnum {
  COLLECT_FAIL = "COLLECT_FAIL",
  COLLECT_WAIT = "COLLECT_WAIT",
  COLLECT_SUCCESS = "COLLECT_SUCCESS"
}

export const collectStatusOptions: Array<BaseOptionItem> = [
  { label: "采集失败", value: CollectStatusEnum.COLLECT_FAIL },
  { label: "待采集", value: CollectStatusEnum.COLLECT_WAIT },
  { label: "采集成功", value: CollectStatusEnum.COLLECT_SUCCESS }
]

export enum receiveStatusEnum {
  ENABLE = "ENABLE",
  DISABLE = "DISABLE"
}

export const receiveStatusOptions: Array<BaseOptionItem> = [
  { label: "失败", value: receiveStatusEnum.DISABLE },
  { label: "成功", value: receiveStatusEnum.ENABLE }
]

export const thirdPartyPushColumns: Array<TableColumnItem> = [
  { prop: "inpNo", label: "患者编号", minWidth: 100 },
  { prop: "sourceSystemName", label: "业务系统", minWidth: 150 },
  { prop: "mrClassName", label: "文档类型", minWidth: 150 },
  { prop: "fileQuantity", label: "推送数", minWidth: 150 },
  { prop: "createdDatetime", label: "接收时间", minWidth: 180 },
  { prop: "status", label: "接收状态", minWidth: 120 },
  { prop: "errorMessage", label: "错误信息", minWidth: 180 }
]

export const remakeColumns: Array<TableColumnItem> = [
  { prop: "patientName", label: "姓名", minWidth: 100 },
  { prop: "mrNo", label: "病案号", minWidth: 150 },
  { prop: "inpNo", label: "患者编号", minWidth: 100 },
  { prop: "mrClassName", label: "文档类型", minWidth: 150 },
  { prop: "remakePageCount", label: "拍摄页数", minWidth: 150 },
  { prop: "createdDatetime", label: "拍摄时间", minWidth: 180 },
  { prop: "creator", label: "操作人", minWidth: 120 }
]
