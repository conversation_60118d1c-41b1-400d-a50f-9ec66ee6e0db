<template>
  <PageContainer separate>
    <!-- 搜索区域 -->
    <template #search>
      <SearchForm
        :form-config="packingSearchFormConfig"
        :form-state="searchFormState"
        @query-btn-click="handleQuery"
        @reset-btn-click="handleReset"
      />
    </template>

    <!-- 表格展示 -->
    <template #table>
      <BaseTable
        ref="packingTableRef"
        :loading="packingBoxState.loading"
        class="package-table"
        :columns="packingTableColumns"
        :data="packingBoxState.packingBoxesList"
      >
        <template #header>
          <el-button type="primary" @click="openPackingDialog">装箱</el-button>
          <el-button type="primary" @click="openOnShelfOrOffShelfDialog('onShelf')">上架</el-button>
          <el-button type="primary" @click="openOnShelfOrOffShelfDialog('offShelf')">移库</el-button>
        </template>

        <template #documentStorageBoxShelveDatetime="{ row }">
          {{ formatDatetime(Number(row.documentStorageBoxShelveDatetime)) }}
        </template>
        <template #operation="{ row }">
          <TableButton @click="viewPackage(row)">查看</TableButton>
          <TableButton @click="deletePackage(row)">删除</TableButton>
        </template>
      </BaseTable>
      <el-pagination
        v-model:page-size="packingPageConfig.pageSize"
        v-model:current-page="packingPageConfig.currentPage"
        :pager-count="5"
        size="small"
        class="package-table-pagination"
        background
        :page-sizes="[10, 25, 50, 100]"
        layout="sizes, prev, pager, next, jumper, total"
        :total="packingBoxState.totalPackingBoxes"
        @update:page-size="refreshPackingList"
        @current-change="getPackingList"
      />
    </template>
  </PageContainer>

  <!-- 装箱 -->
  <DialogContainer
    :close-callback="closePackingDialog"
    :cancel-callback="closePackingDialog"
    :confirm-callback="submitPacking"
    :opened-callback="handleFocus"
    :width="650"
    title="装箱"
    :visible="packingState.visible"
  >
    <el-form
      ref="packingFormRef"
      class="packing-form"
      :rules="packingFormRules"
      :model="packingState"
      label-suffix=":"
      label-width="auto"
    >
      <el-form-item label="箱号">
        <div class="form-content">
          <el-input
            ref="documentBoxRef"
            v-model="packingState.packingCode"
            :suffix-icon="FullScreen"
            @change="scanCode"
          />
        </div>
      </el-form-item>
      <el-form-item label="明细条码">
        <div class="form-content">
          <el-input
            ref="documentCodeRef"
            v-model="packingState.packingBarCode"
            :suffix-icon="FullScreen"
            @change="scanBarCode"
          />
        </div>
      </el-form-item>
      <el-form-item label-width="0">
        <BaseTable
          class="packing-box-table"
          :data="packingState.storedDocumentList"
          :columns="packageOperationColumns"
          border
        >
          <template #operation="{ row }">
            <i class="ri-delete-bin-6-line" @click="deleteMedical(row)"></i>
          </template>
        </BaseTable>
      </el-form-item>
      <div>数量:&nbsp;{{ packingState.storedDocumentList?.length }}份</div>
    </el-form>
  </DialogContainer>

  <!-- 上架弹窗 -->
  <DialogContainer
    :title="onShelfState.dialogType === 'onShelf' ? '上架' : '移库'"
    :width="1000"
    :visible="onShelfState.visible"
    :confirm-callback="confirmOnShelfOrOffShelfPacking"
    :close-callback="closeOnShelfOrOffShelfPackingDialog"
    :cancel-callback="closeOnShelfOrOffShelfPackingDialog"
  >
    <div class="storeroom-wrapper">
      <!-- 库房列表 -->
      <div class="storeroom-list">
        <div
          v-for="item in onShelfState.storeroom"
          :key="item.documentStorageRoomWsid"
          class="storeroom-item"
          :class="{ active: item.documentStorageRoomWsid === onShelfState.activatedStoreroomWsid }"
          @click="changeStoreroom(item)"
        >
          <OverflowTooltip :content="item.name" />
        </div>
      </div>

      <!-- 库房详情 -->

      <div v-if="onShelfState.activatedStoreroomWsid" class="store-detail">
        <BaseTable
          ref="storeLocationTableRef"
          class="store-location-table"
          :columns="storeLocationColumns"
          border
          :multiple="false"
          :data="onShelfState.storeLocationList"
        />
        <el-pagination
          v-model:page-size="paginationConfig.pageSize"
          v-model:current-page="paginationConfig.currentPage"
          :pager-count="5"
          size="small"
          class="store-location-pagination"
          background
          :page-sizes="[10, 25, 50, 100]"
          layout="sizes, prev, pager, next, jumper, total"
          :total="onShelfState.totalStoreLocation"
          @update:page-size="refreshStoreLocationList"
        />
      </div>
      <el-empty v-else description="请在左侧选择库房" />
    </div>
  </DialogContainer>

  <!-- 查看弹窗 -->
  <ViewPackageDialog ref="viewPackageRef" :confirm-callback="getPackingList" />
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, watch, onMounted, computed } from "vue"
import { FullScreen } from "@element-plus/icons-vue"
import { TableButton, BaseTable, DialogContainer, PageContainer, OverflowTooltip } from "@/base-components"
import { SearchForm } from "@/page-components"
import { formatDatetime, SystemPrompt, Message, toastError } from "@/utils"
import {
  getPackingListApi,
  createPackingApi,
  removePackingApi,
  getStoreroomListApi,
  getStoreLocationListApi,
  onShelfPackingApi,
  offShelfPackingApi,
  getDocumentByBarCodeApi,
  checkStorageDeleteApi
} from "../interface"
import ViewPackageDialog from "./components/view-package-dialog.vue"
import {
  packingTableColumns,
  storeLocationColumns,
  packingSearchFormConfig,
  packageOperationColumns,
  packingFormRules
} from "./config"

const isLoading = ref(false)

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  documentStorageBoxLocation: "",
  documentStorageBoxNumber: ""
})

const searchParams = reactive({
  fuzzyQueryParams: [] as Array<Record<string, any>>
})
const handleQuery = () => {
  searchParams.fuzzyQueryParams = Object.entries(searchFormState).map(([key, value]) => ({
    param: key,
    fuzzyValue: value
  }))
  refreshPackingList()
}

const handleReset = () => {
  searchFormState.documentStorageBoxLocation = ""
  searchFormState.documentStorageBoxNumber = ""
  searchParams.fuzzyQueryParams = []
  refreshPackingList()
}

onMounted(async () => {
  isLoading.value = true
  await getPackingList()
  isLoading.value = false
})

/*====================================病案箱表格数据处理====================================*/
const packingTableRef = ref()
// 当前选中的病案箱
const selectedPackingRows = computed(() => packingTableRef.value?.tableState?.selectedRows ?? [])

const packingBoxState = reactive({
  packingBoxesList: [], // 病案箱列表
  totalPackingBoxes: 0, // 病案箱总数
  loading: false
})

// 病案箱分页配置
const packingPageConfig = reactive({
  currentPage: 1, // 当前页
  pageSize: 25 // 每页显示条数
})

// 获取病案箱列表
const getPackingList = async () => {
  packingBoxState.loading = true
  getPackingListApi({
    offset: packingPageConfig.pageSize * (packingPageConfig.currentPage - 1),
    limit: packingPageConfig.pageSize,
    ...searchParams
  })
    .then(res => {
      packingBoxState.packingBoxesList = res.data.data?.documentStorageBoxes
      packingBoxState.totalPackingBoxes = res.data.data?.pageMetadata?.totalElements
    })
    .catch(err => {
      toastError(err, "获取病案箱列表失败")
    })
    .finally(() => {
      packingBoxState.loading = false
    })
}

watch(
  () => [packingPageConfig],
  () => {
    getPackingList()
  },
  {
    deep: true
  }
)

const refreshPackingList = () => {
  // 如果当前就是第一页则强制刷新
  if (packingPageConfig.currentPage === 1) getPackingList()
  else packingPageConfig.currentPage = 1
}

/*==============================装箱管理==============================*/
const packingState = reactive({
  visible: false,
  packingCode: "", // 箱号
  packingBarCode: "", // 明细条码
  storedDocumentList: [] as Array<Record<string, any>> // 已扫描的病案
})

const packingFormRef = ref()

const documentBoxRef = ref() // 箱号

const documentCodeRef = ref() // 明细条码

//打开装箱弹窗
const openPackingDialog = () => {
  packingState.visible = true
  if (selectedPackingRows.value.length > 1) {
    Message.warning("只能选择一条数据")
  }
  packingState.packingCode = selectedPackingRows.value[0]?.documentStorageBoxNumber
}

// 打开装箱弹窗时，自动聚焦到箱号输入框
const handleFocus = () => {
  nextTick(() => {
    if (!packingState.packingCode) {
      documentBoxRef.value.focus()
    } else {
      documentCodeRef.value.focus()
    }
  })
}
//关闭装箱弹窗
const closePackingDialog = () => {
  packingState.visible = false
  packingState.packingCode = ""
  packingState.packingBarCode = ""
  packingState.storedDocumentList = []
}

// 删除箱子
const deletePackage = row => {
  checkStorageDeleteApi(row.documentStorageBoxWsid)
    .then((res: any) => {
      if (!res.data.data) {
        return Message.warning("当前箱号已保存了病案，不支持删除")
      }
      SystemPrompt("确定要删除该箱号吗?").then(() => {
        removePackingApi(row.documentStorageBoxWsid)
          .then(() => {
            Message.success("删除成功")
            getPackingList()
          })
          .catch(err => {
            toastError(err, "删除失败")
          })
      })
    })
    .catch(err => {
      toastError(err)
    })
}
// 扫描箱号
const scanCode = () => {
  documentCodeRef.value?.focus()
}

// 扫描明细条码
const scanBarCode = () => {
  //如果条码已经扫描过则不再添加
  if (packingState.storedDocumentList.find(item => item.barCode === packingState.packingBarCode)) {
    Message.warning("该明细条码已扫描")
    return
  }
  getDocumentByBarCodeApi(packingState.packingBarCode)
    .then(res => {
      packingState.packingBarCode = ""
      if (res.data.data.length === 0) {
        Message.warning("未找到对应病案")
        return
      }
      const data = res.data.data?.[0]
      packingState.storedDocumentList.push({
        barCode: data?.barCode,
        patientName: data?.patientName,
        mrNo: data?.mrNo
      })
    })
    .finally(() => {
      packingState.packingBarCode = ""
    })
}

// 删除已扫描的病案
const deleteMedical = row => {
  const index = packingState.storedDocumentList.findIndex(item => item.barCode === row.barCode)
  packingState.storedDocumentList.splice(index, 1)
}

//提交装箱
const submitPacking = () => {
  if (!packingState.packingCode) {
    Message.warning("请输入箱号")
    return
  }
  if (packingState.storedDocumentList?.length === 0) {
    Message.warning("请扫描明细条码")
    return
  }
  const data = {
    documentStorageBoxNumber: packingState.packingCode,
    documents: packingState.storedDocumentList.map(item => ({
      barCode: item.barCode
    }))
  }

  createPackingApi(data)
    .then(() => {
      Message.success("装箱成功")
      packingState.visible = false
      refreshPackingList()
    })
    .catch(err => {
      toastError(err, "装箱失败")
    })
}

/*=============================上架/移库=============================*/
const onShelfState = reactive({
  visible: false,
  dialogType: "onShelf" as "onShelf" | "offShelf", // 弹窗类型
  activatedStoreroomWsid: "", // 当前选中的库房
  storeroom: [] as Array<Record<string, any>>, // 库房列表
  totalStoreLocation: 0, // 库位总数
  storeLocationList: [] as Array<Record<string, any>>, // 库位列表
  activatedPacking: [] as Array<Record<string, any>> // 当前选中的箱子
})

const storeLocationTableRef = ref()

const selectedStoreLocationRows = computed(() => storeLocationTableRef.value?.tableState?.selectedRows ?? [])

const paginationConfig = reactive({
  currentPage: 1,
  pageSize: 25
})
const confirmOnShelfOrOffShelfPacking = () => {
  if (selectedStoreLocationRows.value?.length === 0) {
    return Message.warning("请选择库位")
  }
  const requestApi = onShelfState.dialogType === "onShelf" ? onShelfPackingApi : offShelfPackingApi
  requestApi({
    documentStorageRackWsid: selectedStoreLocationRows.value?.[0]?.documentStorageRackWsid,
    documentStorageBoxWsids: onShelfState.activatedPacking.map(item => item.documentStorageBoxWsid)
  })
    .then(() => {
      Message.success("上架成功")
      onShelfState.visible = false
      getPackingList()
    })
    .catch(err => {
      toastError(err, "上架失败")
    })
}

const closeOnShelfOrOffShelfPackingDialog = () => {
  onShelfState.visible = false
}

const openOnShelfOrOffShelfDialog = async dialogType => {
  if (selectedPackingRows.value.length === 0) {
    return Message.warning("未选中任何数据，请先选择箱号")
  }
  onShelfState.dialogType = dialogType
  onShelfState.activatedPacking = []
  //上架时，需要判断选中的箱子是否已经存放于某个库位,如果已经存放则不允许再次上架
  if (onShelfState.dialogType === "onShelf") {
    selectedPackingRows.value?.map((item: any) => {
      if (!item?.documentStorageBoxLocation || item.documentStorageBoxLocation?.length === 0) {
        if (!onShelfState.activatedPacking.find(i => i.documentStorageBoxWsid === item.documentStorageBoxWsid)) {
          onShelfState.activatedPacking.push(item)
        }
      }
    })
    if (onShelfState.activatedPacking?.length === 0) {
      return Message.warning("选中的所有箱号已全部上架，请重新选择")
    }
    if (onShelfState.activatedPacking?.length < selectedPackingRows.value.length) {
      await SystemPrompt(
        "选中的箱号中存在已上架的，若要修改它们的存址，请通过移库进行操作，是否继续上架其余箱号？"
      ).then(async () => {
        onShelfState.visible = true
        await getStoreroomList()
        return
      })
    }
  }

  // 移库时，需要判断选中的箱子是否已经存放于某个库位,如果未存放则不允许移库
  if (onShelfState.dialogType === "offShelf") {
    selectedPackingRows.value?.map((item: any) => {
      if (item?.documentStorageBoxLocation && item.documentStorageBoxLocation?.length > 0) {
        if (!onShelfState.activatedPacking.find(i => i.documentStorageBoxWsid === item.documentStorageBoxWsid)) {
          onShelfState.activatedPacking.push(item)
        }
      }
    })
    if (onShelfState.activatedPacking?.length === 0) {
      return Message.warning("选中的所有箱号都未上架，请重新选择")
    }
    if (onShelfState.activatedPacking?.length < selectedPackingRows.value.length) {
      await SystemPrompt("选中的箱号中存在未上架的，请先将它们上架，是否继续将其余箱号移库？").then(async () => {
        onShelfState.visible = true
        await getStoreroomList()
        return
      })
    }
  }

  onShelfState.visible = true
  await getStoreroomList()
}

// 获取所有的库房
const getStoreroomList = () => {
  onShelfState.storeroom = []
  getStoreroomListApi().then(res => {
    onShelfState.storeroom = res.data.data.documentStorageRooms.map((item: any) => {
      return {
        ...item,
        name: `${item.documentStorageRoomName}(${item.documentStorageRoomNumber})`,
        wsid: item.documentStorageRoomWsid,
        code: item.documentStorageRoomNumber,
        address: item.documentStorageRoomAddress
      }
    })
  })
}

//获取选中的库房下的库位列表
const getStoreLocationList = () => {
  getStoreLocationListApi({
    documentStorageRoomWsid: onShelfState.activatedStoreroomWsid,
    offset: paginationConfig.pageSize * (paginationConfig.currentPage - 1),
    limit: paginationConfig.pageSize
  }).then((res: any) => {
    const data = res.data.data
    onShelfState.storeLocationList = data.documentStorageRacks
    onShelfState.totalStoreLocation = data.pageMetadata?.totalElements
  })
}

const refreshStoreLocationList = () => {
  // 如果当前就是第一页则强制刷新
  if (paginationConfig.currentPage === 1) getStoreLocationList()
  else paginationConfig.currentPage = 1
}

// 切换库房
const changeStoreroom = (data: Record<string, any>) => {
  onShelfState.activatedStoreroomWsid = data.documentStorageRoomWsid
  refreshStoreLocationList()
}

/*======================================查看弹窗======================================*/
const viewPackageRef = ref()

const viewPackage = row => {
  viewPackageRef.value.viewPackage(row)
}
</script>

<style lang="less" scoped>
.packing-form {
  .form-content {
    display: flex;
    gap: 20px;
    width: 100%;
    i {
      font-size: 20px;
      cursor: pointer;
    }
  }
  .packing-box-table {
    height: 300px;
  }
}

.package-table {
  height: calc(100% - 60px);
}
.package-table-pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
.storeroom-wrapper {
  display: flex;
  gap: 20px;
  width: 100%;
  height: 500px;
  .storeroom-list {
    width: 200px;
    min-width: 200px;
    height: 100%;
    overflow: auto;
    .storeroom-item {
      padding: 10px;
      font-size: 14px;
      cursor: pointer;
      &:hover {
        background-color: #f5f7fa;
      }
      &.active {
        background-color: #f5f7fa;
      }
    }
  }
  .store-detail {
    // flex: 1;
    width: calc(100% - 220px);
    .store-location-table {
      height: calc(100% - 40px);
    }
    .store-location-pagination {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
    :deep(.base-table-header) {
      margin-bottom: 0;
    }
  }
  :deep(.el-empty) {
    margin: auto;
  }
}
</style>
