import type { SearchFormConfigItem, TableColumnItem } from "@/types"

export const mrnoAuthorizationSearchFormConfig: SearchFormConfigItem[] = [
  { type: "patient", label: "患者信息" },
  { type: "department", label: "出院科室", prop: "outHospitalDeptWsid" },
  { type: "daterange", label: "出院日期", prop: "outHospitalDatetime" }
]

// 病案号授权表格字段
export const mrnoAuthorizationTableColumns: TableColumnItem[] = [
  { prop: "mrNo", label: "病案号", minWidth: 100, sortable: true, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 100, sortable: true, must: true },
  { prop: "visitId", label: "住院次数", minWidth: 100 },
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "outHospitalDatetime", label: "出院时间", minWidth: 180, sortable: true },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 100 },
  { prop: "outDischargeDiagnosisName", label: "出院诊断", minWidth: 100 },
  { prop: "secrecyGradeEnumName", label: "保密等级", minWidth: 100 },
  { prop: "authorizerName", label: "授权人", minWidth: 100 },
  { prop: "operationTime", label: "最后操作时间", minWidth: 180, sortable: true },
  { prop: "operation", label: "操作", width: 140, fixed: "right", must: true }
]

// 科室已授权用户表格字段
export const mrnoAuthorizationUsersTableColumns: TableColumnItem[] = [
  { prop: "realName", label: "用户名", minWidth: 100 },
  { prop: "jobId", label: "工号", minWidth: 120 }
]

// 科室已授权科室表格字段
export const mrnoAuthorizationDeptsTableColumns: TableColumnItem[] = [
  { prop: "deptName", label: "科室", minWidth: 100 }
]
