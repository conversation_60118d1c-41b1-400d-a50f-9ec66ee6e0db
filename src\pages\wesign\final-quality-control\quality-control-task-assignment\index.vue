<template>
  <PageContainer separate>
    <template #search>
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
        />

        <VisitIdFormItem
          v-model:input-value="searchFormState.visitIdCount"
          v-model:select-value="searchFormState.visitIdSymbol"
          label="住院次数"
        />

        <DepartmentFormItem v-model:model-value="searchFormState.outHospitalDeptWsid" label="出院科室" />

        <CountRangeFormItem
          v-model:from="searchFormState.inHospitalDaysFrom"
          v-model:to="searchFormState.inHospitalDaysTo"
          :min="0"
          label="住院天数"
          unit="天"
        />

        <DaterangeFormItem v-model:model-value="searchFormState.outHospitalDatetime" label="出院时间" />

        <PatientLabelFormItem v-model="searchFormState.patientLabel" />
      </SearchContainer>
    </template>
    <template #table>
      <CommonTable
        ref="finalQualityControlTableRef"
        :table-columns="qualityControlTaskAssignmentColumns"
        :request-api="getDistributeQcFinalList"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <template #header>
          <AddButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Distribute)"
            :disabled="selectedRows.length === 0"
            @click="handleAssignment"
          >
            手动分配
          </AddButton>
          <AddButton v-if="hasOperationPermission(menuId, MenuOperationEnum.Distribute)" @click="handleAutoAssignment">
            自动分配
          </AddButton>
          <el-button
            v-if="hasOperationPermission(menuId, MenuOperationEnum.ProcessSearch)"
            :icon="h('i', { class: 'ri-search-line', style: { 'font-size': '18px' } })"
            @click="handleQcProcess"
          >
            质控进度查询
          </el-button>
        </template>
        <template #inHospitalDatetime="{ row }">
          {{ formatDatetime(row.inHospitalDatetime) }}
        </template>
        <template #outHospitalDatetime="{ row }">
          {{ formatDatetime(row.outHospitalDatetime) }}
        </template>

        <!-- 患者姓名 -->
        <template #patientName="{ row }">
          <div style="display: flex; align-items: center; flex-wrap: wrap">
            <span style="margin: 0 16px">{{ row.patientName }}</span>
            <PatientLabelTag :row="row" />
          </div>
        </template>

        <template #isAssignmentFinalQc="{ row }">
          {{ row.isAssignmentFinalQc ? "已分配" : "未分配" }}
        </template>
        <template #qcFinalDatetime="{ row }">
          {{ formatDatetime(row.qcFinalDatetime) }}
        </template>
        <template #qcFinalStatus="{ row }">{{ qcFinalStatusOption[row.qcFinalStatus] }}</template>
        <template #patientPrintCount="{ row }">
          {{ row.patientPrintCount > 0 ? "已打印" : "未打印" }}
        </template>
      </CommonTable>
    </template>
  </PageContainer>

  <!-- 分配质控任务弹窗 -->
  <DialogContainer
    v-model:visible="assignmentVisible"
    title="分配质控任务"
    :confirm-callback="confirmAssign"
    :width="500"
  >
    <el-input
      v-model="distributeDialogState.searchValue"
      placeholder="工号/姓名"
      clearable
      :prefix-icon="Search"
      @change="getDistributeTable"
    ></el-input>
    <BaseTable
      ref="distributeTableRef"
      :loading="distributeDialogState.loading"
      class="distribute-table"
      :columns="assignmentDialogColumns"
      :data="distributeDialogState.data"
      :border="true"
    />
  </DialogContainer>

  <!-- 质控进度查询弹窗 -->
  <DialogContainer v-model:visible="qcProcessVisible" title="质控进度查询" :width="1150" :no-footer="true">
    <SearchContainer @query-btn-click="handleQcProcessQuery" @reset-btn-click="handleQcProcessReset">
      <CommonInputFormItem v-model="qcProcessFormState.qcOperator" label="质控员" />

      <DaterangeFormItem v-model:model-value="qcProcessFormState.createDatetime" label="分配日期" />

      <CommonSelectFormItem
        v-model:model-value="qcProcessFormState.qcStatus"
        label="质控状态"
        :options="qcStatusOptions"
      />
    </SearchContainer>

    <div style="min-height: 320px; height: 60vh">
      <CommonTable
        :table-columns="qualityControlProcessColumns"
        :request-api="getDistributeQcTaskApi"
        :request-params="{ urlType: QualityControlTypeEnum.FINAL_QC, ...qcProcessSearchParams }"
      >
        <template #qcDatetime="{ row }">
          {{ formatDatetime(row.qcDatetime) }}
        </template>
        <template #createDatetime="{ row }">
          {{ formatDatetime(row.createDatetime) }}
        </template>
        <template #qcStatus="{ row }">
          <el-tag :type="getQcStatusTagType(row.qcStatus)">{{ getQcStatus(row.qcStatus) }}</el-tag>
        </template>
      </CommonTable>
    </div>
  </DialogContainer>
</template>

<script setup lang="ts">
import { h, ref, reactive, computed } from "vue"
import { Search } from "@element-plus/icons-vue"
import {
  PageContainer,
  SearchContainer,
  BaseTable,
  CommonTable,
  AddButton,
  PatientFilterFormItem,
  DepartmentFormItem,
  DaterangeFormItem,
  VisitIdFormItem,
  DialogContainer,
  CountRangeFormItem,
  CommonSelectFormItem,
  CommonInputFormItem,
  PatientLabelTag,
  PatientLabelFormItem
} from "@/base-components"
import { MenuOperationEnum, qcStatusOptions, QualityControlTypeEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import {
  getQualityControlUserApi,
  distributeQcTaskApi,
  getDistributeQcTaskApi,
  autoDistributeQcTaskApi
} from "@/interfaces"
import { useUserStore } from "@/stores"
import { Message, toastError, getQcStatus, getQcStatusTagType, formatDatetime, SystemPrompt } from "@/utils"
import { getDistributeQcFinalList } from "../interface"
import {
  qualityControlTaskAssignmentColumns,
  qualityControlProcessColumns,
  assignmentDialogColumns,
  qcFinalStatusOption
} from "./config"

const { hasOperationPermission } = useUserStore()
const menuId = "/final-quality-control/quality-control-assignment"

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  patientFilterValue: "",
  patientFilterProp: "",
  outHospitalDeptWsid: "",
  outHospitalDatetime: "",
  visitIdSymbol: "",
  visitIdCount: "",
  inHospitalDaysFrom: "",
  inHospitalDaysTo: "",
  death: "",
  patientLabel: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/* ======================== 弹窗相关数据操作 ======================== */
// 质控任务分配弹窗
const assignmentVisible = ref(false)

// 质控进度查询弹窗
const qcProcessVisible = ref(false)

// 处理分配质控任务弹窗
function handleAssignment() {
  getDistributeTable()
  assignmentVisible.value = true
}

// 自动分配终末质控
function handleAutoAssignment() {
  SystemPrompt("系统自动将待质控任务随机分配给质控员！").then(() => {
    autoDistributeQcTaskApi({ urlType: QualityControlTypeEnum.final_qc })
      .then(() => {
        Message.success("自动分配成功")
        finalQualityControlTableRef.value?.refreshTableData()
      })
      .catch(err => {
        toastError(err, "自动分配失败")
      })
  })
}

// 处理质控进度查询弹窗
function handleQcProcess() {
  qcProcessVisible.value = true
}

/* ======================== 分配相关操作 ======================== */
const finalQualityControlTableRef = ref()

const distributeTableRef = ref()

const selectedRows = computed(() => {
  return finalQualityControlTableRef.value?.tableState?.selectedRows || []
})

// 分配任务弹窗
const distributeDialogState = reactive({
  visible: false,
  loading: false,
  data: [] as Array<Record<string, any>>,
  searchValue: "",
  submitLoading: false
})

function getDistributeTable() {
  distributeDialogState.loading = true
  getQualityControlUserApi(QualityControlTypeEnum.FINAL_QC, distributeDialogState.searchValue)
    .then(res => {
      distributeDialogState.data = res.data.data || []
    })
    .catch(err => {
      toastError(err, "获取质控员列表失败")
    })
    .finally(() => {
      distributeDialogState.loading = false
    })
}

// 确认分配质控任务
function confirmAssign() {
  const selectedDistribute = distributeTableRef.value?.tableState?.selectedRows || []
  if (selectedDistribute.length === 0) Message.warning("请选择要分配的质控员")
  else {
    distributeDialogState.submitLoading = true
    //病案
    const tasks: Array<Record<string, any>> = []
    selectedRows.value.forEach(item => {
      tasks.push({ inpNo: item.inpNo })
    })
    //质控员
    const operators: Array<string> = []
    selectedDistribute.forEach(item => {
      operators.push(item.wsid)
    })

    distributeQcTaskApi({ tasks, operators, qcType: QualityControlTypeEnum.FINAL_QC })
      .then(() => {
        finalQualityControlTableRef.value?.refreshTableData()
        Message.success("分配成功")
        assignmentVisible.value = false
      })
      .catch(err => {
        toastError(err, "分配失败")
      })
      .finally(() => {
        distributeDialogState.submitLoading = false
      })
  }
}

const dataCallback = (data: Array<Record<string, any>>) => {
  return data.map(item => ({
    ...item,
    outClinicalDeptDatetime: formatDatetime(item?.outClinicalDeptDatetime)
  }))
}

/* ======================== 质控进度查询相关操作 ======================== */

const qcProcessFormState = reactive({
  qcOperator: "",
  createDatetime: "",
  qcStatus: ""
})

const {
  searchParams: qcProcessSearchParams,
  handleQuery: handleQcProcessQuery,
  handleReset: handleQcProcessReset
} = useTableSearch(qcProcessFormState)
</script>

<style lang="less" scoped>
:deep(.el-dialog__body) {
  .el-form-item {
    margin-bottom: 18px !important;
  }
}
.distribute-table {
  margin-top: 30px;
  height: 320px;
}
</style>
