<template>
  <div>
    <el-dialog
      v-model="dialogContainerVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="props.destroyOnClose"
      :show-close="props.showClose"
      :style="{ width: props.width + 'px' }"
      align-center
      @close="handleClose()"
      @opened="handleOpened()"
    >
      <template #header>
        <span>{{ props.title }}</span>
      </template>
      <slot></slot>
      <template v-if="!props.noFooter" #footer>
        <slot name="footer">
          <el-button @click="handleCancel()">取消</el-button>
          <el-button v-if="props.defaultBtn" @click="props.systemDefaultCallback()">系统默认</el-button>
          <el-button
            type="primary"
            style="margin-left: 16px"
            :loading="props.confirmLoading"
            @click="props.confirmCallback()"
          >
            确定
          </el-button>
        </slot>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue"
import { noop } from "lodash-es"

const props = defineProps({
  visible: { type: Boolean, required: true },
  title: { type: String, required: true },
  width: { type: Number, default: 400 },
  confirmCallback: { type: Function, default: noop },
  cancelCallback: { type: Function, default: noop },
  closeCallback: { type: Function, default: noop },
  openedCallback: { type: Function, default: noop }, // 对话框打开时的回调
  showClose: { type: Boolean, default: true },
  destroyOnClose: { type: Boolean, default: true },
  noFooter: { type: Boolean, default: false },
  confirmLoading: { type: Boolean, default: false },
  defaultBtn: { type: Boolean, default: false },
  systemDefaultCallback: { type: Function, default: noop }
})

const emits = defineEmits(["update:visible"])

const dialogContainerVisible = computed({
  get: () => props.visible,
  set: val => emits("update:visible", val)
})

function handleCancel() {
  if (props.cancelCallback === noop) emits("update:visible", false)
  else props.cancelCallback()
}

function handleClose() {
  if (props.closeCallback === noop) emits("update:visible", false)
  else props.closeCallback()
}

function handleOpened() {
  if (props.openedCallback === noop) return
  props.openedCallback()
}
</script>

<style lang="less" scoped>
:deep(.el-dialog) {
  border-radius: 6px;

  .el-dialog__header {
    padding: 12px 24px;
    margin-right: 0;
    font-size: 16px;
    text-align: left;
    font-weight: bold;
    border-bottom: 1px solid rgb(237 238 240);
    .el-dialog__headerbtn {
      top: -4px;
    }
  }
  .el-dialog__body {
    padding: 24px;
    .el-form-item:last-child {
      margin-bottom: 0;
    }
    .el-select {
      width: 100%;
    }
  }
  .el-dialog__footer {
    padding: 0 24px 24px;
  }
}
</style>
