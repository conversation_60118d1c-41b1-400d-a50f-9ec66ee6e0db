import axios from "@/interfaces/axios-instance"
import { RecallFilesProps } from "./config"
/**
 * @method GET
 * @desc   召回管理-获取申请列表
 */
export function getRecallApplyListApi(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/document/recall-can`,
    params
  })
}

/**
 * @method GET
 * @desc   召回管理-获取待审批记录
 */
export function getRecallWaitApproveRecordApi(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/document/recall/wait-approve`,
    params: {
      ...params,
      sorts: "-createdDatetime"
    }
  })
}

/**
 * @method GET
 * @desc   召回管理-获取已审批记录
 */
export function getRecallApprovedRecordApi(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/document/recall/approved`,
    params
  })
}

/**
 * @method GET
 * @desc   召回管理-获取召回记录
 */
export function getRecallApprovedHistoryApi(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api//document/recall`,
    params
  })
}

/**
 * @method POST
 * @desc   召回管理-发起召回申请
 */
export function recallApplyApi(params) {
  return axios({
    method: "post",
    url: `/api/document/recall`,
    data: {
      recallFiles: params.recallFiles,
      recallReason: params.recallReason,
      recallTimes: params.recallTimes,
      inpNo: params.inpNo,
      recallTimesDescribe: params.recallTimesDescribe
    }
  })
}

/**
 * @method GET
 * @desc   召回管理-召回记录审批通过的记录详情
 */
export function getRecallApprovedDetailApi(wsid) {
  return axios({
    method: "get",
    url: `/api/document/recall/view/${wsid}`
  })
}

/**
 * @method GET
 * @desc   召回管理-召回申请详情(审批回显)
 */
export function getRecallDetailApi(wsid) {
  return axios({
    method: "get",
    url: `/api/document/recall/${wsid}`
  })
}

/**
 * @method PUT
 * @desc   召回管理-召回审批处理
 */
export function updateRecallApprovalApi(obj) {
  const { wsid, status, desc, processTaskId } = obj
  return axios({
    method: "put",
    url: "/api/document/recall",
    data: {
      wsid,
      status,
      desc,
      processTaskId
    }
  })
}

interface RollBackParams {
  processTaskId: string
  processTarActId: string
  desc: string
}

// 召回申请回退
export function recallApplyRollBackApi(params: RollBackParams) {
  return axios({
    method: "post",
    url: `/api/document/recall/approve/rollback`,
    data: params
  })
}

// 获取召回版本
export function getRecallVersionApi(inpNo) {
  return axios({
    method: "get",
    url: `/api/document/recall/version`,
    params: {
      inpNo
    }
  })
}

// 获取归档版本
export function getArchiveVersionApi(inpNo) {
  return axios({
    method: "get",
    url: `/api/document/bags/${inpNo}/supplement-archive/version`
  })
}
