<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <CommonInputFormItem v-model="searchForm.catalogueName" label="目录名称" />
      </SearchContainer>
    </template>

    <template #table>
      <TreeTable
        ref="commonTableRef"
        :table-columns="directoryManageColumns"
        :request-api="getDirectoryManageTreeApi"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <template #header>
          <AddButton @click="handleAdd">新增</AddButton>
          <el-button :icon="Link" @click="handleRelevance">关联</el-button>
        </template>
        <template #name="{ row }">
          <span v-if="row.type === 'MR_CLASS'">
            <i class="ri-file-text-line" style="margin-right: 4px"></i>
            <span>{{ row.name }}</span>
          </span>
          <span v-else>{{ row.name }}</span>
        </template>
        <template #type="{ row }">
          <el-tag v-if="row.type === 'CATALOGUE' && row.isRoot">一级目录</el-tag>
          <el-tag v-else-if="row.type === 'CATALOGUE' && !row.isRoot" type="warning">子目录</el-tag>
          <el-tag v-else-if="row.type === 'MR_CLASS'" type="success">文档</el-tag>
          <el-tag v-else type="info">未知</el-tag>
        </template>
        <template #operation="{ row }">
          <TableButton v-if="row.type === 'CATALOGUE'" @click="handleEdit(row)">编辑</TableButton>
          <TableButton v-if="row.type === 'CATALOGUE'" @click="handleAddChild(row)">添加子目录</TableButton>
          <TableButton @click="handleDelete(row)">删除</TableButton>
        </template>
      </TreeTable>
    </template>

    <DialogContainer
      v-model:visible="dialogVisible"
      :title="dialogTitle"
      :width="480"
      :confirm-callback="handleConfirm"
    >
      <el-form
        ref="dialogFormRef"
        :model="dialogFormData"
        :rules="directoryManageFormRules"
        label-width="140"
        label-suffix="："
      >
        <CommonSelectFormItem
          v-model="dialogFormData.parentWsid"
          label="所属目录"
          prop="parentWsid"
          :disabled="dialogType !== 'add'"
          :options="parentDirectoryOptions"
        />

        <CommonInputFormItem v-model="dialogFormData.catalogueName" label="目录名称" prop="catalogueName" />

        <CommonInputFormItem v-model="dialogFormData.catalogueCode" label="目录代码" prop="catalogueCode" />
      </el-form>
    </DialogContainer>

    <DialogContainer
      v-model:visible="relevanceState.dialogVisible"
      title="关联"
      :width="1000"
      :confirm-callback="handleRelevanceConfirm"
    >
      <RelevanceDialog
        ref="relevanceDialogRef"
        v-loading="relevanceState.loading"
        :allow-add-directory="false"
        :list-data="relevanceState.allClass"
        :tree-data="relevanceState.catalogueTree"
      />
    </DialogContainer>
  </PageContainer>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from "vue"
import { Link } from "@element-plus/icons-vue"
import {
  PageContainer,
  SearchContainer,
  DialogContainer,
  CommonTable,
  TreeTable,
  AddButton,
  TableButton,
  CommonInputFormItem,
  CommonSelectFormItem
} from "@/base-components"
import { TabsRouter } from "@/page-components"
import {
  hasChildren,
  toastError,
  SystemAlert,
  Message,
  SystemPrompt,
  formatRecordDocumentTree,
  flattenTree
} from "@/utils"

import { RelevanceDialog } from "./components"
import { directoryManageColumns, directoryManageFormRules, tabsRouterList } from "./config"
import {
  getDirectoryManageTreeApi,
  addDirectoryCatalogueApi,
  delDirectoryCatalogueApi,
  editDirectoryCatalogueApi,
  getRelevanceInfoApi,
  changeDirectoryRelevanceInfoApi,
  delDirectoryMrclassApi
} from "./interface"
import type { BaseOptionItem } from "@/types"

/* ======================== 获取一级目录选项 ======================== */

const parentDirectoryOptions = ref<Array<BaseOptionItem & { hasDocument: boolean }>>([])

// 新增时使用一级目录
const firstLevelCatalog = ref<Array<BaseOptionItem & { hasDocument: boolean }>>([])

// 二级目录及以下添加子目录时使用
const allLevelCatalog = ref<Array<BaseOptionItem & { hasDocument: boolean }>>([])

function refreshParentDirectoryOptions() {
  getDirectoryManageTreeApi({ catalogueName: "" }).then(resData => {
    const options = resData.map(item => ({
      label: item.name,
      value: item.wsid,
      hasDocument: hasChildren(item) && item.children[0].type === "MR_CLASS"
    }))
    options.unshift({ label: "根目录", value: "", hasDocument: false })
    parentDirectoryOptions.value = options

    firstLevelCatalog.value = options
    allLevelCatalog.value = flattenTree(formatRecordDocumentTree(resData))
      .filter(item => item.type === "CATALOGUE")
      .map(item => ({
        label: item.name,
        value: item.wsid,
        hasDocument: hasChildren(item) && item.children[0].type === "MR_CLASS"
      }))
  })
}

onMounted(refreshParentDirectoryOptions)

/* ======================== 搜索 ======================== */

const searchForm = reactive({
  catalogueName: ""
})

const searchParams = reactive({
  catalogueName: ""
})

// 触发搜索
function handleQuery() {
  searchParams.catalogueName = searchForm.catalogueName
}

// 重置搜索
function handleReset() {
  searchForm.catalogueName = ""
  searchParams.catalogueName = ""
}
/* ======================== 目录表格&编辑弹窗 ======================== */

const commonTableRef = ref<InstanceType<typeof CommonTable>>()

const dialogType = ref<"add" | "edit" | "child">("add")
const dialogVisible = ref(false)
const dialogFormData = reactive({
  wsid: "",
  parentWsid: "",
  catalogueCode: "",
  catalogueName: ""
})

const dialogTitle = computed(() => {
  switch (dialogType.value) {
    case "add":
      return "新增目录"
    case "edit":
      return "编辑目录"
    case "child":
      return "添加子目录"
    default:
      return "未知"
  }
})

function dataCallback(data: Array<Record<string, any>>) {
  const treeData = formatRecordDocumentTree(data)
  treeData.forEach(item => {
    item.isRoot = true
    if (hasChildren(item)) {
      item.children.forEach(subItem => {
        subItem.parentWsid = item.wsid
      })
    }
  })
  console.log(`output->treeData`, treeData)
  return treeData
}

// 新增目录
const handleAdd = () => {
  parentDirectoryOptions.value = firstLevelCatalog.value
  dialogType.value = "add"
  for (let prop in dialogFormData) {
    dialogFormData[prop] = ""
  }
  dialogVisible.value = true
}

// 编辑目录
const handleEdit = row => {
  dialogType.value = "edit"
  dialogFormData.wsid = row.wsid
  dialogFormData.catalogueName = row.name
  dialogFormData.catalogueCode = row.code
  if (row.isRoot) dialogFormData.parentWsid = ""
  else dialogFormData.parentWsid = row.parentWsid
  dialogVisible.value = true
}

// 添加子目录
const handleAddChild = (row: any) => {
  parentDirectoryOptions.value = row?.isRoot ? firstLevelCatalog.value : allLevelCatalog.value
  // if (hasChildren(row) && row.children[0].type === "MR_CLASS") {
  //   return SystemAlert("该目录已关联文档类型，添加子目录需删除关联的文档类型", "warning")
  // }
  dialogType.value = "child"
  for (let prop in dialogFormData) {
    dialogFormData[prop] = ""
  }
  dialogFormData.parentWsid = row.wsid
  dialogVisible.value = true
}

// 确认新增&编辑
function handleConfirm() {
  const parentDirectory = parentDirectoryOptions.value.find(item => item.value === dialogFormData.parentWsid)
  // if (parentDirectory?.hasDocument) {
  //   return SystemAlert("该所属目录已关联文档类型，添加子目录需删除关联的文档类型", "warning")
  // }
  if (dialogType.value === "add" || dialogType.value === "child") {
    addDirectoryCatalogueApi(dialogFormData)
      .then(() => {
        dialogVisible.value = false
        Message.success("添加成功")
        commonTableRef.value?.refreshTableData()
        refreshParentDirectoryOptions()
      })
      .catch(err => toastError(err))
  } else {
    editDirectoryCatalogueApi(dialogFormData)
      .then(() => {
        dialogVisible.value = false
        Message.success("修改成功")
        commonTableRef.value?.refreshTableData()
        refreshParentDirectoryOptions()
      })
      .catch(err => toastError(err))
  }
}

// 删除目录&文档
function handleDelete(row) {
  if (row.type === "CATALOGUE") {
    SystemPrompt(
      `您正在删除名称为“${row.name}”的目录，删除目录的同时会删除目录下的所有关联文档，确定删除？`,
      "error"
    ).then(() => {
      delDirectoryCatalogueApi({ catalogueWsid: row.wsid })
        .then(() => {
          Message.success("删除成功")
          commonTableRef.value?.refreshTableData()
          refreshParentDirectoryOptions()
        })
        .catch(err => toastError(err))
    })
  } else {
    SystemPrompt(`您正在删除名称为“${row.name}”的文档，确定删除？`, "error").then(() => {
      delDirectoryMrclassApi({ wsid: row.wsid })
        .then(() => {
          Message.success("删除成功")
          commonTableRef.value?.refreshTableData()
          refreshParentDirectoryOptions()
        })
        .catch(err => toastError(err))
    })
  }
}

/* ======================== 关联对话框 ======================== */

interface IRelevanceState {
  dialogVisible: boolean
  inputValue: string
  allClass: any[]
  catalogueTree: any[]
  loading: boolean
}

const relevanceDialogRef = ref()

const relevanceState = reactive<IRelevanceState>({
  dialogVisible: false,
  inputValue: "",
  allClass: [],
  catalogueTree: [],
  loading: false
})

async function handleRelevance() {
  relevanceState.dialogVisible = true
  relevanceState.loading = true
  try {
    const res = await getRelevanceInfoApi()
    const { allClass = "", catalogueTree = "" } = res.data.data
    relevanceState.allClass = allClass
    relevanceState.catalogueTree = formatRecordDocumentTree(catalogueTree)
  } catch (error) {
    toastError(error)
  }
  relevanceState.loading = false
}

// 确认关联
function handleRelevanceConfirm() {
  const catalogueTree = relevanceDialogRef.value?.getRenderTree()
  console.log(`output->relevanceState.catalogueTree`, relevanceState.catalogueTree)
  // console.log(`output->catalogueTree`, catalogueTree)
  changeDirectoryRelevanceInfoApi({ catalogueTree })
    .then(() => {
      Message.success("操作成功")
      commonTableRef.value?.refreshTableData()
      refreshParentDirectoryOptions()
      relevanceState.dialogVisible = false
    })
    .catch(error => toastError(error))
}
</script>
