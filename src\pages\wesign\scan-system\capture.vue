<template>
  <div class="capture-wrapper">
    <!-- 头部扫描拍摄操作栏 -->
    <div class="capture-header">
      <IconContainer name="拍摄" size="medium" icon="ri-instagram-line" @click="handleShoot" />

      <IconContainer
        name="提交上传"
        size="medium"
        icon="ri-upload-line"
        :disabled="!patientEmrDocumentState.patientInfo"
        @click="handleSubmit"
      />

      <el-upload accept=".jpg,.jpeg,.png" :show-file-list="false" :before-upload="importImage">
        <IconContainer name="导入图片" desc="本地上传扫描图片" size="medium" icon="ri-import-fill" />
      </el-upload>

      <IconContainer
        name="重拍"
        desc="覆盖当前预览中的图片"
        size="medium"
        :disabled="!currentImage"
        icon="ri-camera-line"
        @click="handleRecapture"
      />

      <IconContainer
        name="删除"
        desc="删除已选中的扫描图片"
        :disabled="!currentImage?.id"
        size="medium"
        icon="ri-delete-bin-5-line"
        @click="deleteImage"
      />

      <IconContainer
        name="清空"
        size="medium"
        icon="ri-delete-bin-2-line"
        desc="清空当前患者已扫描图片"
        :disabled="!patientEmrDocumentState.patientInfo"
        @click="clearImage"
      />

      <IconContainer
        v-if="ScanMachine !== 'LT'"
        name="左旋转"
        size="medium"
        icon="ri-anticlockwise-2-fill"
        desc="摄像头左旋转90度"
        @click="rotate('left')"
      />

      <IconContainer
        v-if="ScanMachine !== 'LT'"
        name="右旋转"
        size="medium"
        icon="ri-clockwise-fill"
        desc="摄像头右旋转90度"
        @click="rotate('right')"
      />

      <IconContainer
        name="图像编辑"
        size="medium"
        icon="ri-image-edit-line"
        desc="图像编辑"
        @click="$router.push('/scan-system/image-editor')"
      />

      <!-- 当前版本不考虑图片编辑功能 -->
      <!-- <IconContainer
          :name="currentImageMode === 'edit' ? '退出编辑' : '图片编辑'"
          size="medium"
          icon="ri-image-2-line"
          :disabled="!currentImage"
          :desc="currentImageMode === 'edit' ? '退出编辑模式' : '进入编辑模式'"
          @click="editImage"
        /> -->
    </div>

    <!-- 内容区域 -->
    <div class="capture-container">
      <!-- 左边内容区域 -->
      <div class="capture-container-left">
        <!-- 用户信息搜索展示 -->
        <PatientSearchHeader v-model:patient-info="patientEmrDocumentState.patientInfo" />

        <div class="capture-container-left__box">
          <!-- 侧边图片快捷编辑操作 -->
          <div v-if="currentImageMode === 'edit' && ScanMachine !== 'LT'" class="capture-container-left__aside">
            <IconContainer name="保存" desc="将当前修改覆盖初始图片" icon="ri-save-fill" />

            <IconContainer name="撤销" desc="撤销已执行的编辑操作" icon="ri-reply-fill" />

            <IconContainer name="剪裁" desc="对当前图片进行尺寸裁剪" icon="ri-scissors-fill" />

            <IconContainer name="擦除" desc="擦粗图片中部分区域内容" icon="ri-brush-2-fill" />

            <IconContainer name="亮度" desc="调整当前图片亮度" icon="ri-sun-fill" />

            <IconContainer name="对比度" desc="调整当前图片对比度" icon="ri-contrast-fill" />

            <IconContainer name="黑白" desc="将当前图片转为黑白图片" icon="ri-image-edit-line" />
          </div>

          <!-- 右边pdf预览区 -->
          <div class="capture-container-left__content">
            <el-tabs v-model="activeCaptureOperation" type="border-card">
              <el-tab-pane label="拍摄图像" name="capturedImage" style="height: 100%">
                <div class="capture-image__content">
                  <img v-if="currentImage" :src="currentImage.fileBase64 || currentImage.filePath" />
                  <EmptyContent v-else desc="暂无数据" />
                </div>
              </el-tab-pane>
              <el-tab-pane label="缩略图" name="thumbnail" style="height: 100%">
                <ThumbnailImage
                  v-model:capture-classify="patientEmrDocumentState.captureClassify"
                  v-model:current-image="currentImage"
                />
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </div>

      <!-- 右边内容区域 -->
      <div class="capture-container-right">
        <!-- 图像预览 -->
        <div class="capture-preview">
          <div class="capture-preview__title">图像预览</div>
          <div class="capture-preview__content">
            <LTScanCamera
              v-if="ScanMachine === 'LT'"
              ref="scanCamera"
              @handle-message="handleMessage"
              @update-bar-code="updateBarCode"
            />
            <JYScanCamera
              v-else-if="ScanMachine === 'JY'"
              ref="scanCamera"
              @handle-message="handleMessage"
              @update-bar-code="updateBarCode"
            />
          </div>
        </div>
        <!-- 图像分类 -->
        <MrClassSelectorPanel
          v-model:mr-class-code="activatedDocType.mrClassCode"
          v-model:mr-class-name="activatedDocType.mrClassName"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, watch, onMounted } from "vue"
import localforage from "localforage"
import { cloneDeep } from "lodash"
import { nanoid } from "nanoid"
import { v4 as uuidv4 } from "uuid"
import { EmptyContent, IconContainer } from "@/base-components"
import { call_GetApplicationConfig } from "@/call-cef-control.js"
import { submitCaptureImages } from "@/interfaces"
import { blobToBase64, getUrlBase64 } from "@/utils"
import { Message, SystemAlert, SystemPrompt, toastError } from "@/utils/message-tool"
import MrClassSelectorPanel from "./components/MrClassSelectorPanel.vue"
import PatientSearchHeader from "./components/PatientSearchHeader.vue"
import { LTScanCamera, JYScanCamera } from "./components/ScanCamera"
import ThumbnailImage from "./components/ThumbnailImage.vue"
import type { CaptureImageItem, CaptureGroupItem, DocTypeItem } from "./config"

// 当前激活的tab，分为拍摄图像tab和缩略图tab
const activeCaptureOperation = ref<"capturedImage" | "thumbnail">("capturedImage")

// 当前拍摄图像所处模式，分为预览模式和编辑模式
const currentImageMode = ref<"preview" | "edit">("preview")

const ScanMachine = window.__GLOBAL_CONFIG__?.ScanMachine

/*================== 扫描图像存储 ==================*/

// 当前激活的图像
const currentImage = ref<null | CaptureImageItem>(null)

interface IPatientEmrDocumentState {
  patientInfo: Record<string, any> | null // 患者信息
  captureClassify: Array<CaptureGroupItem> // 扫描图像集合
}

const patientEmrDocumentState = reactive<IPatientEmrDocumentState>({
  patientInfo: null,
  captureClassify: []
})

// 扫描图像数据更新后自动同步到浏览器本地存储数据库
watch(
  () => patientEmrDocumentState.captureClassify,
  () => {
    const patientWsid = patientEmrDocumentState.patientInfo?.patientWsid ?? ""
    if (patientWsid) {
      localforage.setItem(patientWsid, cloneDeep(patientEmrDocumentState))
    }
  },
  { deep: true }
)

// 切换患者后自动从浏览器本地存储获取缓存数据
watch(
  () => patientEmrDocumentState.patientInfo,
  () => {
    const patientWsid = patientEmrDocumentState.patientInfo?.patientWsid ?? ""
    if (patientWsid) {
      localforage.getItem<IPatientEmrDocumentState>(patientWsid).then(data => {
        patientEmrDocumentState.captureClassify = data?.captureClassify ?? []
      })
    }
  },
  { immediate: true, deep: true }
)

function updateBarCode(barCode) {
  if (!patientEmrDocumentState.patientInfo || !barCode) return

  patientEmrDocumentState.patientInfo.barCode = barCode
}

/*==================图像分类搜索==================*/

// 当前选中的图像分类
const activatedDocType = reactive<DocTypeItem>({
  mrClassName: "",
  mrClassCode: "",
  index: ""
})

/*==================顶部图像操作区==================*/

const scanCamera = ref()

function getApplicationConfig() {
  console.log("call_GetApplicaitonConfig")
  call_GetApplicationConfig("call_GetApplicaitonConfig")
}

// 拍摄前校验是否已选定患者信息和图片分类
function validateRequiredInfo() {
  if (!patientEmrDocumentState.patientInfo) {
    SystemAlert("请先查询选择患者信息")
    return false
  } else if (!activatedDocType.mrClassCode) {
    SystemAlert("请先选择图片分类")
    return true
  } else {
    return true
  }
}

// 插入扫描图片
const createdCapturePhoto = (captureImage: CaptureImageItem) => {
  const targetGroup = patientEmrDocumentState.captureClassify.find(
    item => item.mrClassCode === activatedDocType.mrClassCode
  )
  // 若该类型下已有图片则追加，否则创建分组
  if (targetGroup) {
    targetGroup.imageFiles.push(captureImage)
  } else {
    patientEmrDocumentState.captureClassify.push({ ...activatedDocType, imageFiles: [captureImage] })
  }
}

// 拍摄
const handleShoot = () => {
  if (!validateRequiredInfo()) return

  scanCamera.value.shootImage()
  // 模拟拍摄,真实拍摄时从websocket获取的图像信息替换下面属性即可
  // currentImage.value = {
  //   id: uuidv4(),
  //   fileName: `image_${nanoid(6)}`, // 图片名称
  //   filePath: "/src/demo/capture-image.png", // 图片路径
  //   fileBase64: "", // 图片原始base64数据
  //   thumbnailBase64: "", // 缩略图base64数据
  //   createdDatetime: new Date().getTime(), // 创建时间
  //   lastModifyDateTime: new Date().getTime() // 最后修改时间
  // }
  // 图片path转base64
  // getUrlBase64(currentImage.value.filePath).then(async (res: any) => {
  //   const imageBase = res as string
  //   if (!currentImage.value) return
  //   currentImage.value.fileBase64 = imageBase
  //   createdCapturePhoto(currentImage.value)
  // })
}

// 提交上传
const handleSubmit = () => {
  // scanCamera.value.mergePDF()
  // if (!patientEmrDocumentState.patientInfo?.barCode) return Message.warning("请填写或扫描病案条形码")

  SystemPrompt("请确认是否要提交所拍摄图像").then(() => {
    Message.info("正在提交中，请稍后...")

    submitCaptureImages(patientEmrDocumentState)
      .then(res => {
        const results = res.data?.data?.results ?? []
        // 提交成功的总数
        const totalSuccessCount = results.reduce((preTotal, item) => {
          const curTotal = item?.successCount ?? 0
          return preTotal + curTotal
        }, 0)
        // 提交的总数
        const totalCount = patientEmrDocumentState.captureClassify.reduce((preTotal, item) => {
          return preTotal + item.imageFiles.length
        }, 0)
        SystemAlert(`提交完成，合成成功${totalSuccessCount}张, 失败${totalCount - totalSuccessCount}张`, "success")
        localStorage.setItem("PATIENT_INFO", JSON.stringify(patientEmrDocumentState.patientInfo))

        // 提交成功后清空本地缓存图像
        const patientWsid = patientEmrDocumentState.patientInfo?.patientWsid ?? ""
        if (patientWsid) {
          localforage.removeItem(patientWsid)
          patientEmrDocumentState.captureClassify = []
        }
      })
      .catch(err => {
        toastError(err, "提交失败")
      })
  })
}

//导入图片
const importImage = file => {
  if (!validateRequiredInfo()) return false
  currentImage.value = {
    id: uuidv4(),
    fileName: file.name, // 图片名称
    filePath: file.name, // 图片路径
    fileBase64: "", // 图片原始base64数据
    thumbnailBase64: "",
    createdDatetime: new Date().getTime(), // 创建时间
    lastModifyDateTime: new Date().getTime() // 最后修改时间
  }
  // 本地图片转base64
  blobToBase64(file).then(res => {
    if (!currentImage.value) return
    currentImage.value.fileBase64 = res as string
    createdCapturePhoto(currentImage.value)
  })
  return false
}

// 重拍
const handleRecapture = () => {
  if (validateRequiredInfo() && currentImage.value) {
    scanCamera.value.reShootImage()
    // 连接客户端调试时将下面属性换成高拍仪传过来的
    // currentImage.value.fileName = `image_${nanoid(6)}`
    // currentImage.value.fileBase64 = "/src/demo/capture-image.png"
    // currentImage.value.lastModifyDateTime = new Date().getTime()
  }
}

// 删除指定图片
const deleteImage = () => {
  if (!currentImage.value) return SystemAlert("请先选择要删除的图片")
  SystemPrompt("是否确认删除当前图像").then(() => {
    const allThumbnails = patientEmrDocumentState.captureClassify.flatMap(item => item.imageFiles)
    const targetThumbnail = allThumbnails.find(item => item.id === currentImage.value?.id)
    if (targetThumbnail) {
      // 遍历所有分类，找到该图片所在的分类，然后删除该分类下的该图片
      patientEmrDocumentState.captureClassify.forEach(item => {
        const targetIndex = item.imageFiles.findIndex(image => image.id === currentImage.value?.id)
        if (targetIndex !== -1) {
          item.imageFiles.splice(targetIndex, 1)
          currentImage.value = null
        }
      })
    }
  })
}

// 清空当前患者所有扫描图片
const clearImage = () => {
  const patientWsid = patientEmrDocumentState.patientInfo?.patientWsid ?? ""
  if (patientWsid) {
    SystemPrompt("是否确认清空当前患者已扫描图像").then(() => {
      patientEmrDocumentState.captureClassify = []
      localforage.removeItem(patientWsid).then(() => {
        Message.success("当前患者扫描图像已清空")
      })
    })
  }
}

// 旋转
const angle = ref(0)
const rotate = direction => {
  if (direction === "right") {
    if (angle.value >= 90) angle.value -= 90
    else angle.value = 270
  } else {
    if (angle.value < 270) angle.value += 90
    else angle.value = 0
  }
  scanCamera.value?.rotateCamera(angle.value)
}

// 图片编辑
const editImage = () => {
  currentImageMode.value = currentImageMode.value === "edit" ? "preview" : "edit"
}

const handleMessage = handleMessage => {
  // 重拍覆盖图片
  if (handleMessage.indexOf("recover,") !== -1 && currentImage.value) {
    handleMessage = handleMessage.replace("recover,", "")
    currentImage.value.fileBase64 = "data:image/jpeg;base64," + handleMessage
    currentImage.value.lastModifyDateTime = new Date().getTime() // 最后修改时间
  }
  // 创建新图片
  else {
    const targetGroup = patientEmrDocumentState.captureClassify.find(
      group => group.mrClassCode === activatedDocType.mrClassCode
    )
    const sequence = targetGroup ? targetGroup.imageFiles.length + 1 : 1
    currentImage.value = {
      id: uuidv4(),
      fileName: `${activatedDocType.mrClassName}_${sequence}`, // 图片名称
      filePath: "", // 图片本地路径
      fileBase64: "data:image/jpeg;base64," + handleMessage,
      thumbnailBase64: "", // 缩略图base64数据
      createdDatetime: new Date().getTime(), // 创建时间
      lastModifyDateTime: new Date().getTime() // 最后修改时间
    }
    createdCapturePhoto(currentImage.value)
  }
}
</script>

<style lang="less" scoped>
.capture-wrapper {
  background: #f5f7f9;
  border-radius: 4px;
  height: calc(100% - 2px);
  overflow: auto;
  border: 1px solid #cdd4da;

  .capture-header {
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    border-bottom: 1px solid #d9dfe4;
    > div:first-child {
      margin-left: 4px;
    }
  }

  .capture-container {
    display: flex;
    justify-content: space-between;
    height: calc(100% - 70px);

    .capture-container-left {
      flex: 1;
      min-width: 0px;
      height: 100%;
      display: flex;
      flex-direction: column;

      &__box {
        display: flex;
        justify-content: space-between;
        overflow: auto;
        flex: 1;
        min-height: 0px;
      }
      &__aside {
        background: #f5f7f9;
        display: flex;
        flex-direction: column;
        border-right: 1px solid #cdd4da;
        height: 100%;
      }
      &__content {
        flex: 1;
        min-width: 0px;
        height: 100%;
        :deep(.el-tabs) {
          border: none;
          height: 100%;
        }

        :deep(.el-tabs__content) {
          height: calc(100% - 40px);
          overflow: auto;
        }
        :deep(.el-tabs__header) {
          background-color: #cdd4da;
          border-bottom: none;
          .el-tabs__item {
            color: #262626;
          }
          .is-active {
            color: #409eff;
          }
        }
        :deep(.el-tabs__content) {
          padding: 0;
        }
        :deep(.el-empty) {
          height: 100%;
        }
      }
    }
    .capture-container-right {
      height: 100%;
      width: 320px;
      border-left: 1px solid #cdd4da;
      .capture-preview {
        height: 50%;
        min-height: 240px;

        &__title {
          font-size: 14px;
          color: #000;
          padding: 12px 20px;
          background: #d7dde5;
        }
        &__content {
          padding: 8px;
          height: calc(100% - 43px);
          overflow: scroll;
          box-sizing: border-box;
        }
      }
    }
  }
  .capture-image__content {
    width: 100%;
    height: 100%;
    overflow: auto;
    img {
      width: 100%;
    }
  }
}
</style>
