import currency from "currency.js"
import { isNumber } from "lodash"
import { fixedNumberWithCommas } from "@/utils"

export const add = (target, options) => value => currency(target, options).add(value).value

export const subtract = (target, options) => value => currency(target, options).subtract(value).value

export const multiply = (target, options) => value => currency(target, options).multiply(value).value

export const divide = (target, options) => value => currency(target, options).divide(value).value

export const parseInt = (target, options?: any) => target

export const parseFloat = (target, options) => currency(target, options).value

export const PERCENTAGE = "PERCENTAGE"
export const INTEGER = "INTEGER"
export const FLOAT = "FLOAT"
export const FIXED_NUMBER = "FIXED_NUMBER"

export const percentageParser = (value, precision = 2) => divide(value, { precision: precision + 2 })(100)
export const integerParser = value => parseInt(value)
export const floatParser = (value, precision = 2) => parseFloat(value, { precision })
export const percentageFormat = (value, precision = 2) => multiply(value, { precision: precision + 2 })(100)
export const fixedNumberFormat = (value, precision = 2) => fixedNumberWithCommas(value, { digit: precision })
