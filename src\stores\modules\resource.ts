import { defineStore } from "pinia"

/* ======================== 系统资源信息 ======================== */

const useResourceStore = defineStore("RESOURCE_INFO", {
  state: () => {
    return {
      logo: "",
      favicon: ""
    }
  },
  actions: {
    setLogo(logo) {
      this.logo = logo
    },
    setFavicon(favicon) {
      this.favicon = favicon
    }
  },
  persist: true
})

export default useResourceStore
