import type { TableColumnItem } from "@/types"

export const storeLocationTableColumns: Array<TableColumnItem> = [
  { prop: "name", label: "库位名称", minWidth: 180, must: true },
  { prop: "code", label: "库位编码", minWidth: 120, must: true },
  { prop: "documentStorageRackCount", label: "已存数量（箱）", minWidth: 180, sortable: true },
  { prop: "operation", label: "操作", width: 120, fixed: "right", must: true }
]
