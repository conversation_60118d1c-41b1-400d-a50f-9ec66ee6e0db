import type { TableColumnItem } from "@/types"
import type { FormRules } from "element-plus"

export const menuId = "/quality-control-config/scoring-standard"

export const tableColumns: Array<TableColumnItem> = [
  { prop: "PROBLEM_CATEGORIES", label: "问题大类", minWidth: 100 },
  { prop: "PROBLEM_SUBCLASS", label: "问题子类", minWidth: 300 },
  { prop: "SCORE_ITEM", label: "评分项", minWidth: 200 },
  { prop: "singleReject", label: "单项否决", minWidth: 50 },
  { prop: "rejectLevel", label: "否决评分项", minWidth: 100 },
  { prop: "score", label: "扣分", minWidth: 50 }
]

export const contentTabsRouterList = [
  {
    label: "内容设置",
    path: "/quality-control-config/scoring-standard/content-setting",
    name: "qualityControlConfig_scoring-standard/content-setting"
  }
]

export const homePageTabsRouterList = [
  {
    label: "首页质控",
    path: "/quality-control-config/scoring-standard/home-page",
    name: "qualityControlConfig_scoring-standard/home-page"
  },
  {
    label: "科室质控",
    path: "/quality-control-config/scoring-standard/department-page",
    name: "qualityControlConfig_scoring-standard/department-page"
  },
  {
    label: "终末质控",
    path: "/quality-control-config/scoring-standard/final-score-page",
    name: "qualityControlConfig_scoring-standard/final-score-page"
  }
]

export const scoreTypeConfig = {
  首页质控: "FIRST_HOME_SCORE",
  科室质控: "DEPT_SCORE",
  终末质控: "FINAL_SCORE"
}

export const documentTypeOptions = [{ label: "住院病案首页", value: "FIRST_HOME_PAGE" }]

export const singleRejectOptions = [
  { label: "是", value: "YES" },
  { label: "否", value: "NO" }
]

export const rejectLevelOptions = [
  // { label: "甲", value: "LEVE_ONE" },
  { label: "乙", value: "LEVE_TWO" },
  { label: "丙", value: "LEVE_THREE" }
]

const checkScore = (rule, value, callback) => {
  if (value === "") {
    callback(new Error("请输入分值"))
  } else {
    const reg = /^[+]?(0|([1-9]\d*))(\.\d{1})?$/
    if (reg.test(value)) {
      callback()
    } else {
      callback(new Error("请输入最多1位小数的分值"))
    }
  }
}

export const categoryRule: FormRules = {
  name: [{ required: true, message: "请输入问题大类名称", trigger: "blur" }],
  documentType: [{ required: true, message: "请选择", trigger: "blur" }],
  totalScore: [{ required: true, validator: checkScore, trigger: "blur" }]
}

export const subclassRule: FormRules = {
  name: [{ required: true, message: "请输入名称", trigger: "blur" }],
  singleReject: [{ required: true, message: "请选择是否单项否决", trigger: "blur" }],
  rejectLevel: [{ required: true, message: "请选择否决评分级别", trigger: "blur" }],
  score: [{ required: true, validator: checkScore, trigger: "blur" }],
  totalScore: [{ required: true, validator: checkScore, trigger: "blur" }]
}
