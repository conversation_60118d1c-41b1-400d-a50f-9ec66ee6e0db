import axios from "@/interfaces/axios-instance"

/**
 * @method GET
 * @desc   获取所有值域列表
 */
export function searchStandardRangeClassifyApi() {
  return axios({
    method: "get",
    url: "/api/catalog/standard/options/classify"
  })
}

/**
 * @method POST
 * @desc   新增首页标准字段范围分类
 */
type TypeAddStandardRangeClassifyParams = {
  code: string
  name: string
}
export function addStandardRangeClassifyApi(data: TypeAddStandardRangeClassifyParams) {
  return axios({
    method: "post",
    url: "/api/catalog/standard/options/classify",
    data
  })
}

/**
 * @method PUT
 * @desc   编辑首页标准字段范围分类
 */
type TypeEditStandardRangeClassifyParams = {
  code: string
  name: string
}
export function editStandardRangeClassifyApi(data: TypeEditStandardRangeClassifyParams) {
  return axios({
    method: "put",
    url: "/api/catalog/standard/options/classify",
    data
  })
}

/**
 * @method DELETE
 * @desc   删除首页标准字段范围分类
 */
export function deleteStandardRangeClassifyApi(id: string) {
  return axios({
    method: "delete",
    url: `/api/catalog/standard/options/classify/${id}`
  })
}

/**
 * @method GET
 * @desc   查询首页标准字段范围-分页
 */
export function searchMetaCodeTableFieldApi(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: "/api/catalog/standard/options",
    params
  })
}

/**
 * @method POST
 * @desc   新增首页标准字段范围
 */
type TypeAddStandardRangeParams = {
  code: string
  key: string
  value: string
  remark: string
  hospitalKey: string
  hospitalValue: string
}
export function addStandardRangeApi(data: TypeAddStandardRangeParams) {
  return axios({
    method: "post",
    url: "/api/catalog/standard/options",
    data
  })
}

/**
 * @method PUT
 * @desc   编辑首页标准字段范围
 */
type TypeEditStandardRangeParams = {
  id: number
  code: string
  key: string
  value: string
  remark: string
  hospitalKey: string
  hospitalValue: string
}
export function editStandardRangeApi(data: TypeEditStandardRangeParams) {
  return axios({
    method: "put",
    url: "/api/catalog/standard/options",
    data
  })
}

/**
 * @method DELETE
 * @desc   删除首页标准字段范围
 */
export function deleteStandardRangeApi(id: string) {
  return axios({
    method: "delete",
    url: `/api/catalog/standard/options/${id}`
  })
}

/**
 * @method GET
 * @desc   下载导入首页标准字段范围模板
 */
export function downloadMetaCodeTemplateApi() {
  return axios({
    method: "get",
    url: "/api/catalog/standard/options/template",
    responseType: "arraybuffer"
  })
}

/**
 * @method POST
 * @desc   导入首页标准字段范围
 */
export function metaCodeImportApi({ file }: { file: File }) {
  const formData = new FormData()
  formData.append("file", file)
  return axios({
    method: "post",
    url: "/api/catalog/standard/options/import",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  })
}

/**
 * @method POST
 * @desc   导出首页标准字段范围数据
 */
export function metaCodeExportApi() {
  return axios({
    method: "post",
    url: "/api/catalog/standard/options/export",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    responseType: "arraybuffer"
  })
}

/**
 * @method PUT
 * @desc   元数据取值设置默认值
 */
interface IOptionsDefault {
  code: string
  key: string
  enabledDefault: string
}
export function setStandardOptionsDefaultApi(data: IOptionsDefault) {
  return axios({
    method: "put",
    url: "/api/catalog/standard/options-default",
    data
  })
}
