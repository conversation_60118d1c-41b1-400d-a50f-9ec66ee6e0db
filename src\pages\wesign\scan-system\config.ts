// 扫描图片对象
export type CaptureImageItem = {
  id: string // 图片唯一id
  fileName: string // 图片名称
  filePath: string // 图片本地路径
  fileBase64: string // 图片base64数据
  thumbnailBase64: string // 缩略图base64数据（由fileBase64压缩得到）
  createdDatetime: string | number | Date // 创建时间
  lastModifyDateTime: string | number | Date // 最后修改时间
}

// 扫描图片分组对象

// 病案文档类型
export interface DocTypeItem {
  mrClassName: string
  mrClassCode: string
  index: string
}

export interface CaptureGroupItem extends DocTypeItem {
  imageFiles: Array<CaptureImageItem>
}
