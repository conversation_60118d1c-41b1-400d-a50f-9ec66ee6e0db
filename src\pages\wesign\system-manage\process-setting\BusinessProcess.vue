<template>
  <div v-loading="fullLoading" class="business-process-wrapper">
    <div>
      <TabsRouter :tabs-config="tabsRouterList" />
      <div class="operation-action">
        <el-button type="primary" @click="handleAllConfig">保存</el-button>
      </div>
    </div>

    <div class="business-process">
      <div class="business-list">
        <!-- 左侧勾选业务区域 -->
        <div v-for="business in businessProcessData" :key="business.businessNodeCode" class="business-item">
          <el-checkbox
            v-if="!business.children"
            v-model="business.checked"
            :disabled="business?.required"
            @change="handleBusinessStatus(business)"
          >
            <span class="business-item-name">
              <img :src="business.icon" alt="" />
              <span>{{ business.businessNodeName }}</span>
            </span>
          </el-checkbox>

          <!-- 科室管理质控 -->
          <template v-else>
            <el-checkbox
              v-model="business.checked"
              :indeterminate="isIndeterminate"
              :disabled="business?.required"
              @change="handleBusinessStatus(business)"
            >
              <span class="business-item-name">
                <img :src="business.icon" alt="" />
                <span>{{ business.businessNodeName }}</span>
              </span>
            </el-checkbox>
            <div
              v-for="child in business.children"
              :key="child.businessNodeCode"
              class="business-item"
              style="padding-right: 0; padding-left: 30px"
            >
              <el-checkbox v-model="child.checked" :disabled="child?.required" @change="handleBusinessStatus(child)">
                <span class="business-item-name">
                  <img :src="child.icon" alt="" />
                  <span>{{ child.businessNodeName }}</span>
                </span>
              </el-checkbox>
            </div>
          </template>
        </div>
      </div>

      <!-- 流程图区域 -->
      <div class="business-process-graph">
        <div class="graph-container">
          <div class="graph-start">开始</div>
          <div class="connect-line"></div>
          <VueDraggable
            v-model="businessProcessData"
            drag-class="dragging"
            direction="horizontal"
            draggable=".draggable-item"
            filter=".non-draggable-item"
            :animation="150"
            ghost-class="ghost"
          >
            <!-- 病案提交固定第一个节点，其余节点均可拖拽 -->
            <div
              v-for="item in businessProcessData"
              :key="item.businessNodeName"
              :style="{
                paddingTop: item.children && item.checked ? '40px' : '0'
              }"
              :class="item.businessNodeCode !== '1001' ? 'draggable-item' : 'non-draggable-item'"
              @click="openBusinessProcessDrawer(item)"
            >
              <!-- 普通节点 -->
              <div
                v-if="!item.children && item.checked"
                class="graph-item-content"
                :style="{
                  borderColor: activeBusinessState.businessNodeCode === item.businessNodeCode ? '#3860f4' : '#fff'
                }"
              >
                <div class="graph-item-name flex-center">
                  <img :src="item.icon" alt="" />
                  <span>{{ item.businessNodeName }}</span>
                </div>
                <div class="graph-item-checkbox">
                  <el-checkbox
                    v-for="action in item?.simConfigs"
                    :key="action.value"
                    :model-value="action.select"
                    @click.stop
                    @change="e => changeAction(e, item.businessNodeCode, action.businessKey)"
                  >
                    <template #default>
                      {{ action.businessKeyName }}
                      <el-tooltip :content="action.describe" placement="top">
                        <i class="ri-question-fill"></i>
                      </el-tooltip>
                    </template>
                  </el-checkbox>
                </div>
              </div>

              <!-- 科室质控节点 -->
              <template v-if="item.children && (isIndeterminate || item.checked)">
                <!-- 标题 -->
                <div class="graph-item-content group-title">
                  <div class="graph-item-name flex-center">
                    <img :src="item.icon" alt="" />
                    <span>{{ item.businessNodeName }}</span>
                  </div>
                </div>
                <VueDraggable
                  v-model="item.children"
                  :style="{ paddingTop: isIndeterminate ? '80px' : '40px' }"
                  drag-class="dragging"
                  direction="horizontal"
                  draggable=".draggable-item"
                  filter=".non-draggable-item"
                  :animation="150"
                  ghost-class="ghost"
                >
                  <div
                    v-for="(child, index) in item.children"
                    :key="child.businessNodeName"
                    :style="{
                      display: child.checked ? 'block' : 'none'
                    }"
                    :class="child.businessNodeCode !== '1001' ? 'draggable-item' : 'non-draggable-item'"
                    @click.stop="openBusinessProcessDrawer(child)"
                  >
                    <div
                      class="graph-item-content"
                      :style="{
                        borderColor:
                          activeBusinessState.businessNodeCode === child.businessNodeCode ? '#3860f4' : '#fff'
                      }"
                    >
                      <div class="graph-item-name flex-center">
                        <img :src="child.icon" alt="" />
                        <span>{{ child.businessNodeName }}</span>
                      </div>
                      <div class="graph-item-checkbox">
                        <el-checkbox
                          v-for="action in child.simConfigs"
                          :key="action.value"
                          :model-value="action.select"
                          @click.stop
                          @change="e => changeAction(e, child.businessNodeCode, action.businessKey)"
                        >
                          <template #default>
                            {{ action.businessKeyName }}
                            <el-tooltip :content="action.describe" placement="top">
                              <i class="ri-question-fill"></i>
                            </el-tooltip>
                          </template>
                        </el-checkbox>
                      </div>
                    </div>
                    <!-- 箭头 -->
                    <div class="connect-line"></div>
                  </div>
                </VueDraggable>
              </template>
              <!-- 箭头 -->
              <div v-if="!item.children && item.checked" class="connect-line"></div>
            </div>
          </VueDraggable>
          <div class="graph-end">结束</div>
          <div style="height: 45px"></div>
        </div>
      </div>

      <!-- 业务流程操作区 -->
      <DynamicForm
        v-model:configs="activeBusinessState.configs"
        :visible="activeBusinessState.drawerVisible"
        :title="activeBusinessState.businessNodeName"
        :handle-close="handleCloseBusinessProcess"
        :business-node-code="activeBusinessState.businessNodeCode"
        :active-business-process-data="activeBusinessProcessData"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, computed, onMounted, watch } from "vue"
import { ElMessage } from "element-plus"
import { cloneDeep, last } from "lodash-es"

import { VueDraggable } from "vue-draggable-plus"
import { Tools } from "@element-plus/icons-vue"
import { TabsRouter } from "@/page-components"
import { updateBusinessProcessConfigApi, getBusinessProcessConfigApi } from "../interface"
import DynamicForm from "./components/DynamicForm.vue"
import { tabsRouterList, simpleConfigEnum } from "./config"
import { getImage } from "@/interfaces"
import { Message, businessProcessIcons, checkBusinessProcessAndGlobalConfig } from "@/utils"

interface ProcessItemData {
  businessNodeCode: string
  businessNodeName: string
  required: boolean
  configs: Array<Record<string, any>>
  checked: boolean
  icon: string
  [key: string]: any
}
const fullLoading = ref(false)

const businessProcessData = ref<Array<ProcessItemData>>([])

// 获取业务图标
const findBusinessHref = (businessName: string, index: number) => {
  const targetIcon = businessProcessIcons.find(item => item.label === businessName)
  if (targetIcon) {
    return targetIcon.img
  }
  return businessProcessIcons[index]?.img
}

/*======================================业务流程======================================*/
// 激活的模块
const activeBusinessProcessData = computed(() => {
  return businessProcessData.value.filter(item => item.checked)
})

const updateConfigs = configs => {
  configs.forEach((item: Record<string, any>) => {
    if (item.formType === "SELECT") {
      const formOptions = JSON.parse(item.formOptions)
      //如果props.activeBusinessProcessData的任意一项在formOptions中
      if (formOptions.find((v: Record<string, any>) => v.value === item.value)) {
        if (!activeBusinessProcessData.value.find((v: Record<string, any>) => v.businessNodeCode === item.value)) {
          item.value = ""
        }
      }
    }
  })
}

// 打开抽屉时激活的业务配置
const activeBusinessState = reactive({
  drawerVisible: false,
  businessNodeCode: "",
  businessNodeName: "",
  configs: [] as Array<Record<string, any>>
})

// 获取业务流程配置
const getBusinessProcessConfig = () => {
  getBusinessProcessConfigApi().then(res => {
    fullLoading.value = false
    const initBusinessProcessData = res?.data?.data
    let baseBusinessProcessData: any = []
    //找到结束节点。也就是没有nextNodeCode的节点，这个是结束节点
    const endNode = initBusinessProcessData.find(item => !item.nextNodeCode)
    baseBusinessProcessData.push(endNode)

    //然后找某个节点的nextNodeCode是这个节点的，就是这个节点的上一个节点
    const findPreNode = (node: any) => {
      const preNode = initBusinessProcessData.find(item => item.nextNodeCode === node.businessNodeCode)
      if (preNode) {
        baseBusinessProcessData.push(preNode)
        findPreNode(preNode)
      }
    }
    findPreNode(endNode)
    baseBusinessProcessData = baseBusinessProcessData.reverse()

    let result = baseBusinessProcessData.map((item, index) => {
      const simpleConfigs = item?.simConfigs
        .map(config => {
          return {
            ...config,
            businessKeyName: simpleConfigEnum?.[config.businessKey] ?? config.businessKey
          }
        })
        .sort((a, b) => a.formShowSn - b.formShowSn)
      return {
        ...item,
        required: item?.required || false,
        configs: item?.configs?.map(item => {
          return { ...item, value: item.businessKey.includes("VIEW_DOC") ? item.value.split(",") : item.value }
        }),
        checked: item?.status === "ENABLE",
        simConfigs: cloneDeep(simpleConfigs),
        icon: findBusinessHref(item.businessNodeName, index)
      }
    })
    result.forEach(item => {
      item?.configs?.forEach(config => {
        if (config.formType === "RADIO_PLUS" && config.extraValue?.includes("WSID_FILE")) {
          if (config?.extraValue && config.extraValue.includes("WSID_FILE")) {
            let extraMetadata = config.extraMetadata
            if (extraMetadata) {
              extraMetadata = JSON.parse(extraMetadata)
            }
            config.extraName = extraMetadata?.extraFileName || extraMetadata?.name || ""
          }
        }
      })
    })
    // 分组节点
    const groupNode = cloneDeep(result)
      .map(item => {
        if (item.groupName) {
          return {
            groupName: item.groupName,
            children: item
          }
        } else {
          return item
        }
      })
      .reduce(
        (pre, next) => {
          if (next.groupName) {
            pre.children = [...new Set(pre?.children?.concat(next.children))]
            pre.businessNodeName = next.groupName
          }
          return pre
        },
        {
          businessNodeCode: "",
          nextNodeCode: "",
          children: [],
          businessNodeName: "",
          required: false,
          checked: false
        }
      )
    groupNode.businessNodeCode = groupNode?.children[0]?.businessNodeCode
    groupNode.nextNodeCode = groupNode?.children[groupNode?.children?.length - 1]?.nextNodeCode
    groupNode.checked = groupNode.children.every(item => item.checked)
    isIndeterminate.value = groupNode.children.some(item => item.checked)
    if (groupNode.checked) isIndeterminate.value = false
    groupNode.icon = findBusinessHref(groupNode.businessNodeName, 0)

    // 重新组装
    let normalNode = cloneDeep(result.filter(item => !item.groupName))
    const index = normalNode.findIndex(item => item.nextNodeCode === groupNode.businessNodeCode)
    normalNode.splice(index + 1, 0, groupNode)
    console.log(`output->normalNode`, normalNode)
    // businessProcessData.value = cloneDeep(result)
    businessProcessData.value = cloneDeep(normalNode)
  })
}

const changeAction = (e, businessNodeCode, businessKey) => {
  console.log(`output->e`, e)
  //遍历businessProcessData里的actions,将里面value为action的checked设置为false
  //然后再将节点的businessNodeCode为businessNodeCode的action的checked设置为true
  businessProcessData.value.forEach(item => {
    if (!item.children) {
      console.log(`output->item.simConfigs`, item.simConfigs)
      item.simConfigs.forEach(action => {
        if (action.businessKey === businessKey) {
          action.select = false
        }
      })
      if (item.businessNodeCode === businessNodeCode) {
        item.simConfigs.forEach(action => {
          if (action.businessKey === businessKey) {
            action.select = e
          }
        })
      }
    } else {
      item.children.forEach(child => {
        child.simConfigs.forEach(action => {
          if (action.businessKey === businessKey) {
            action.select = false
          }
        })
        if (child.businessNodeCode === businessNodeCode) {
          child.simConfigs.forEach(action => {
            if (action.businessKey === businessKey) {
              action.select = e
            }
          })
        }
      })
    }
  })
  console.log("businessProcessData", businessNodeCode, businessKey, businessProcessData.value)
}

onMounted(async () => {
  fullLoading.value = true
  await getBusinessProcessConfig()
})

const isIndeterminate = ref(false)
// 修改业务状态
const handleBusinessStatus = business => {
  console.log(`output->business`, business)
  business.status = business.checked ? "ENABLE" : "DISABLE"
  // 全选
  if (business?.children?.length) {
    isIndeterminate.value = false
    business.children.forEach(item => {
      item.checked = business.checked
      item.status = business.checked ? "ENABLE" : "DISABLE"

      if (!item.checked) {
        const defaultSimSelectNode =
          business.children.find(item => item.defaultSimSelectNode) || last(business.children)
        item.simConfigs.forEach(action => {
          if (action.select) {
            action.select = false
            //同时将defaultSimSelectNode这个节点里的simConfigs的select设置为true
            business.children.forEach(item => {
              if (item.businessNodeCode === defaultSimSelectNode?.businessNodeCode) {
                item.simConfigs.forEach(v => {
                  console.log("action", v.businessKey, action.businessKey)
                  if (v.businessKey === action.businessKey) {
                    console.log("action", v.businessKey, action.businessKey)
                    v.select = true
                  }
                })
              }
            })
          }
        })
      }
    })
  }
  // 判断当前节点的simConfigs是否有选中的，如果有选中的,并且这个节点被禁用了，那么就将这个节点的simConfigs的select设置为false
  if (!business.checked && !business.children) {
    const defaultSimSelectNode =
      businessProcessData.value.find(item => item.defaultSimSelectNode) || last(businessProcessData.value)
    business.simConfigs.forEach(action => {
      if (action.select) {
        action.select = false
        //同时将defaultSimSelectNode这个节点里的simConfigs的select设置为true
        businessProcessData.value.forEach(item => {
          if (item.businessNodeCode === defaultSimSelectNode?.businessNodeCode) {
            item.simConfigs.forEach(v => {
              console.log("action", v.businessKey, action.businessKey)
              if (v.businessKey === action.businessKey) {
                console.log("action", v.businessKey, action.businessKey)
                v.select = true
              }
            })
          }
        })
      }
    })
  }
  if (!business.children) {
    businessProcessData.value.find(item => item.businessNodeName === business.groupName).checked =
      businessProcessData.value
        .find(item => item.businessNodeName === business.groupName)
        ?.children.every(item => item.checked)
  }
  const checkedCount = businessProcessData.value
    ?.find(item => item.businessNodeName === business?.groupName)
    ?.children.filter(item => item.checked)
  if (checkedCount.length > 0) {
    isIndeterminate.value =
      checkedCount.length > 0 &&
      checkedCount.length <
        businessProcessData.value.find(item => item.businessNodeName === business.groupName)?.children.length
  } else {
    isIndeterminate.value = false
  }
}

// 打开抽屉
const openBusinessProcessDrawer = (business: Record<string, any>) => {
  if (business.children) return
  if (!business?.configs || business?.configs.length === 0) {
    Message.warning(`${business?.businessNodeName}没有配置项`)
    return
  }
  const businessData = business
  activeBusinessState.drawerVisible = true
  activeBusinessState.businessNodeCode = businessData.businessNodeCode
  activeBusinessState.businessNodeName = businessData.businessNodeName
  activeBusinessState.configs = businessData.configs
  //遍历configs，如果config的formType是IMAGE,那么需要调用getImage方法获取图片资源
  activeBusinessState.configs.forEach(config => {
    if (config.formType === "IMAGE") {
      getImage(config.value).then(res => {
        config.imageSrc = res
      })
    }
  })
}

// 关闭抽屉
const handleCloseBusinessProcess = () => {
  activeBusinessState.drawerVisible = false
  activeBusinessState.businessNodeCode = ""
  activeBusinessState.businessNodeName = ""
  activeBusinessState.configs = []
}

/*==================================保存==================================*/

// 保存业务流程
const handleSaveBusinessProcess = async () => {
  // let errorsInfo = {}
  // businessProcessData.value.forEach(business => {
  //   const { errorInfo } = checkBusinessProcessAndGlobalConfig(business?.configs, business?.businessNodeName)
  //   errorsInfo = {
  //     ...errorsInfo,
  //     ...errorInfo
  //   }
  // })

  // if (errorsInfo && Object.keys(errorsInfo).length > 0) {
  //   let warningTexts = ""
  //   for (let key in errorsInfo) {
  //     if (errorsInfo[key] instanceof Object) {
  //       for (let k in errorsInfo[key]) {
  //         const errorMsg =
  //           errorsInfo[key][k] === undefined || errorsInfo[key][k] === "undefined" ? "空" : errorsInfo[key][k]
  //         warningTexts += `${key}:${k}${errorMsg}.`
  //       }
  //     } else {
  //       const errorMsg = errorsInfo[key] === undefined || errorsInfo[key] === "undefined" ? "空" : errorsInfo[key]
  //       warningTexts += `${key}${errorMsg}.`
  //     }
  //   }
  //   return ElMessage.error(warningTexts)
  // }

  const data = toBackEndData.value.map((business, index) => {
    const { icon, checked, configs, ...rest } = business

    const formatterConfigs = configs.map(config => {
      const { extraName, extraMetadata, ...rest } = config
      if (config.formType === "RADIO_PLUS" && config.extraValue?.includes("WSID_FILE")) {
        return {
          ...rest,
          extraMetadata: JSON.stringify({
            extraFileName: extraName
          }),
          ...(config.extraValue?.length > 0
            ? {
                extraValue: config.extraValue
              }
            : {})
        }
      }
      return rest
    })
    return {
      ...business,
      configs: formatterConfigs,
      ...(index !== toBackEndData.value.length - 1
        ? {
            nextNodeCode: toBackEndData.value[index + 1]?.businessNodeCode
          }
        : {})
    }
  })
  let res = ""
  // 最后一个节点无nextNodeCode
  data[data.length - 1].nextNodeCode = ""
  console.log(`output->data`, data)
  data.forEach(item => {
    if (item.configs.length) {
      item.configs.forEach(config => {
        if (Array.isArray(config.value)) {
          config.value = config.value.join(",")
        }
      })
    }
  })
  await updateBusinessProcessConfigApi(data)
    .then(() => {
      res = "success"
    })
    .catch(err => {
      res = "error"
    })
  return res
}

const toBackEndData = ref<Array<any>>([])

// 保存所有配置
const handleAllConfig = async () => {
  console.log(`output->businessProcessData.value`, businessProcessData.value)
  toBackEndData.value = businessProcessData.value
    .map(item => {
      if (!item.children) {
        return item
      } else {
        return item.children
      }
    })
    .reduce((pre, next) => {
      if (!next.length) {
        return pre.concat([next])
      } else {
        return pre.concat(next)
      }
    }, [])
  console.log(`output->toBackEndData.value`, toBackEndData.value)
  // 先校验节点上的简单配置项是否有选中的，如果没有，那么就提示用户
  // 遍历businessProcessData,在遍历节点的simConfigs，看是否所有节点的simConfigs都没有选中的，如果有，那么就提示用户
  // const selectedSimpleConfigsSize = businessProcessData.value
  const selectedSimpleConfigsSize = toBackEndData.value
    .map(item => {
      return item.simConfigs
    })
    .flat()
    .map(item => {
      return item.select
    })
    .filter(item => item).length

  if (Math.max(selectedSimpleConfigsSize, 0) < 3) {
    // 找出是哪个节点没有选中
    let noSelectedConfigNode = [] as Array<string>
    // const unSelectedconfigs = businessProcessData.value
    const unSelectedconfigs = toBackEndData.value
      .map(item => {
        return item.simConfigs
      })
      .flat()
      // 将unSelectedconfigs里businessKey是一样的放在一起
      .reduce((acc, cur) => {
        if (!cur.select) {
          if (!acc[cur.businessKey]) {
            acc[cur.businessKey] = []
          }
          acc[cur.businessKey].push(cur)
        }
        return acc
      }, {})
    // 看哪一项的长度等于businessProcessData.value.length,说明这个配置项没有选中
    // for (let key in unSelectedconfigs) {
    //   if (unSelectedconfigs[key].length === businessProcessData.value.length) {
    //     noSelectedConfigNode.push(simpleConfigEnum[key])
    //   }
    // }
    // const warnTexts = noSelectedConfigNode.join("、")
    // ElMessage.error(`请选择${warnTexts}配置项`)
    // return
  }

  const saveBusinessRes = await handleSaveBusinessProcess()
  if (saveBusinessRes === "success") {
    ElMessage.success("保存成功")
  } else {
    if (saveBusinessRes === "error") {
      ElMessage.error("保存业务流程和全局配置失败")
    }
    if (saveBusinessRes === "error") {
      ElMessage.error("保存业务流程失败")
    }
  }
}
</script>

<style lang="less" scoped>
.business-process-wrapper {
  padding: 10px 20px;
  height: calc(100% - 20px);
  background-color: #fff;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px rgb(0 35 114 / 10%);
  .operation-action {
    position: absolute;
    right: 20px;
    top: 5px;
  }
  .business-process {
    display: flex;
    height: calc(100% - 44px);
    position: relative;
    .business-list {
      width: 200px;
      border-right: 1px solid #ebeef5;
      padding: 20px 20px 0 0;
      .business-item {
        padding: 7px 16px 5px 8px;
        border-radius: 4px;
        :deep(.el-checkbox) {
          flex-direction: row-reverse;
          justify-content: space-between;
          width: 100%;
        }
        .business-item-name {
          display: flex;
          align-items: center;
          font-size: 14px;
          color: #0a1633;
          font-weight: 500;
          line-height: 16px;
          img {
            margin-right: 8px;
          }
          span {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 100px;
          }
        }
        &:hover {
          background-color: #e1e9f7;
        }
      }
    }

    .business-process-graph {
      background-color: #eff2f7ff;
      flex: 1;
      display: flex;
      justify-content: center;
      overflow: auto;
      padding-top: 45px;

      .graph-container {
        .graph-start {
          width: 116px;
          height: 32px;
          background: #42b625;
          box-shadow: 0px 2px 8px 0px rgba(51, 51, 51, 0.1);
          border-radius: 30px;
          color: #fff;
          text-align: center;
          line-height: 32px;
          margin: auto;
        }
        .graph-end {
          color: #fff;
          text-align: center;
          line-height: 32px;
          width: 116px;
          height: 32px;
          background: #333333;
          box-shadow: 0px 2px 8px 0px rgba(51, 51, 51, 0.1);
          border-radius: 34px;
          margin: auto;
        }
        .business-graph-item,
        .draggable-item,
        .non-draggable-item {
          color: #0a1633;
          cursor: pointer;
          position: relative;
        }

        .graph-item-content {
          display: flex;
          align-items: center;
          flex-direction: column;
          font-size: 16px;
          color: #0a1633;
          font-weight: 500;
          line-height: 16px;
          border: 1px solid #fff;
          // padding: 10px 16px;
          font-size: 14px;
          font-weight: 500;
          line-height: 16px;
          border-radius: 8px;
          .graph-item-name {
            background-color: #f0f4fbff;
            width: 100%;
            padding: 8px 0px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
          }
          .graph-item-checkbox {
            padding: 8px 24px;
            background-color: #fff;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            i {
              color: #ced1daff;
              font-size: 18px;
            }
          }
          &:hover {
            border-color: #3860f4;
          }
          img {
            margin-right: 8px;
          }
          span {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 100px;
          }
        }
        .connect-line {
          width: 1px;
          height: 40px;
          background-color: #b3b6ba;
          margin: auto;
          position: relative;
          margin-bottom: 10px;
          &::after {
            //加一个三角形
            content: "";
            display: block;
            width: 0;
            height: 0;
            position: absolute;
            left: calc(50% - 5px);
            bottom: 0;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #b3b6ba;
            margin: 0 auto;
          }
        }
        .dragging {
          .connect-line {
            visibility: hidden;
          }
        }
      }
    }
  }
}
.group-title {
  position: absolute;
  width: 496px;
  left: -44px;
  right: 0;
  top: 0;
  bottom: 0;
  background: #e4eaf2;
  height: calc(100% - 20px);
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
  border: 1px dashed #3860f4;
}
</style>

<style lang="less">
.process-config-warning-box {
  background-color: rgb(245, 246, 246);
  padding: 6px 10px;
  margin-bottom: 10px;
  border-radius: 4px;
}
</style>
