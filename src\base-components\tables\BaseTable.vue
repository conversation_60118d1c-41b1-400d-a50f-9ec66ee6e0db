<template>
  <div class="base-table-container">
    <div class="base-table-header">
      <div>
        <slot name="header"></slot>
      </div>
    </div>
    <el-table
      ref="tableRef"
      v-loading="$props.loading"
      :element-loading-spinner="tableLoadingSvg"
      element-loading-svg-view-box="-10, -10, 50, 50"
      :data="$props.data"
      :border="props.border"
      :header-cell-class-name="headerCellClass"
      :header-cell-style="{ background: 'rgb(248,249,252)', color: '#030814' }"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <template v-for="item in props.columns" :key="item.prop">
        <!-- 表格左侧选择栏或索引栏 -->
        <el-table-column
          v-if="item.type === 'selection' || item.type === 'index'"
          :type="item.type"
          :width="item.width || 50"
          fixed="left"
          :selectable="handleSelectable"
          :label="item?.label || ''"
        ></el-table-column>
        <el-table-column
          v-if="!item.type && item.prop"
          :prop="item.prop"
          :label="item.label"
          :sortable="item.sortable"
          :width="item.width"
          :min-width="item.minWidth"
          :fixed="item.fixed"
          :show-overflow-tooltip="true"
        >
          <template #default="{ row, $index }">
            <slot :name="item.prop" :row="row" :index="$index">
              <span>{{ item.prop ? row[item.prop] : "" }}</span>
            </slot>
          </template>
        </el-table-column>
      </template>
      <template #empty>
        <EmptyContent />
      </template>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import { last } from "lodash"
import { tableLoadingSvg } from "@/configs"
import EmptyContent from "../EmptyContent.vue"
import type { TableColumnItem } from "@/types"

interface PropsType {
  loading?: boolean
  border?: boolean
  columns: Array<TableColumnItem>
  data: Array<Record<string, any>>
  selectable?: (row: any, index: number) => boolean
  multiple?: boolean //是否可以多选
}

const props = withDefaults(defineProps<PropsType>(), {
  loading: false,
  border: false,
  pagination: true,
  multiple: true,
  selectable: undefined
})

const tableRef = ref()

const state = reactive({
  selectedRows: [] as Array<any> // 选中的行
})

const emits = defineEmits(["rowClick"])

//如果有多选这一列同时只支持单选那么需要隐藏表头的全选框
const headerCellClass = ({ column }) => {
  return column.type === "selection" && !props.multiple ? "hidden-check" : ""
}

// 是否可勾选
function handleSelectable(row, index) {
  if (props.selectable) {
    return props.selectable(row, index)
  } else return true
}

function handleSelectionChange(rows: Array<any>) {
  if (props.multiple || rows.length === 1) {
    state.selectedRows = rows
    return
  }

  if (rows.length > 1) {
    state.selectedRows = last(rows)
    toggleRowSelection(rows[0], false)
  }
}

// 设置选择项
function toggleRowSelection(row, isSelect) {
  if (tableRef.value) tableRef.value.toggleRowSelection(row, isSelect)
}

// 数据项点击
function handleRowClick(row: Record<string, any>) {
  emits("rowClick", row)
}

defineExpose({
  toggleRowSelection,
  tableState: state
})
</script>

<style lang="less" scoped>
.base-table-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  :deep(.el-table) {
    height: 100%;
    .el-scrollbar__view {
      height: 100%;
    }
  }
  :deep(.hidden-check) {
    .el-checkbox {
      display: none;
    }
  }

  .base-table-header {
    margin-bottom: 8px;
  }
}
</style>
