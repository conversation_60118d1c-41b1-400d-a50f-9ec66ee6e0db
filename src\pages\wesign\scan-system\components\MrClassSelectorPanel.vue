<template>
  <div class="capture-classify">
    <div class="capture-classify-title">图像分类</div>
    <div class="capture-classify__content">
      <el-descriptions class="select-classify">
        <el-descriptions-item label="当前所选扫描分类：">
          <el-tooltip effect="light" placement="top" :show-after="200" :content="props.mrClassName || '--'">
            <el-tag>{{ props.mrClassName || "--" }}</el-tag>
          </el-tooltip>
        </el-descriptions-item>
      </el-descriptions>
      <el-input
        v-model="searchDocTypes.searchValue"
        class="capture-classify__search"
        placeholder="请输入关键词"
        clearable
        :prefix-icon="Search"
      />

      <!-- 分类列表 -->
      <div v-if="searchDocTypesResult?.length > 0" class="capture-classify-list">
        <div
          v-for="docType in searchDocTypesResult"
          :key="docType.mrClassCode"
          :class="docType.mrClassCode === props.mrClassCode ? 'active' : ''"
          class="capture-classify-item"
          @click="changeCaptureClassify(docType)"
        >
          <OverflowTooltip :content="`${docType.index}&nbsp;.&nbsp;${docType.mrClassName}`" />
        </div>
      </div>

      <EmptyContent v-else class="empty-classify" desc="暂无数据" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, reactive, onMounted } from "vue"
import { Search } from "@element-plus/icons-vue"
import { EmptyContent, OverflowTooltip } from "@/base-components"
import { getAllDocTypes } from "@/interfaces"
import type { DocTypeItem } from "../config"

interface PropsType {
  mrClassName: string
  mrClassCode: string
}
const props = defineProps<PropsType>()

const emits = defineEmits(["update:mrClassName", "update:mrClassCode"])

const searchDocTypes = reactive({
  // 图像分类数据源
  docTypesSource: [] as Array<DocTypeItem>,
  searchValue: ""
})

// 获取所有文档类型列表
onMounted(() => {
  getAllDocTypes().then(resData => {
    const data = resData
    searchDocTypes.docTypesSource = data.map((item, index) => {
      return {
        index: index.toString(),
        ...item
      } as DocTypeItem
    })
    // 初始化扫描配置为第一个分类
    if (resData.length > 0) {
      emits("update:mrClassName", resData[0].mrClassName)
      emits("update:mrClassCode", resData[0].mrClassCode)
    }
  })
})

// 根据过滤参数筛选文档分类列表
const searchDocTypesResult = computed(() => {
  if (searchDocTypes.searchValue) {
    return searchDocTypes.docTypesSource.filter((item: DocTypeItem) => {
      if (
        item.mrClassName.includes(searchDocTypes.searchValue) ||
        item.mrClassCode.includes(searchDocTypes.searchValue) ||
        item.index.includes(searchDocTypes.searchValue)
      ) {
        return item
      }
    })
  }
  return searchDocTypes.docTypesSource
})

// 切换图像分类
function changeCaptureClassify(docType: DocTypeItem) {
  emits("update:mrClassName", docType.mrClassName)
  emits("update:mrClassCode", docType.mrClassCode)
}
</script>

<style lang="less" scoped>
.capture-classify {
  background-color: #fff;
  height: 50%;
  min-height: 200px;
  .capture-classify-title {
    font-size: 14px;
    color: #000;
    padding: 12px 20px;
    background: #d7dde5;
  }

  .capture-classify__content {
    padding: 10px 0px 0px 10px;
    background: #fff;
    height: calc(100% - 43px);
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .capture-classify__search {
      margin-top: 10px;
      width: 275px;
    }
    .select-classify {
      :deep(.el-descriptions__cell) {
        padding-bottom: 0px;
        .el-descriptions__label {
          margin-right: 0px;
          white-space: nowrap;
        }
      }
      :deep(.el-tag) {
        max-width: 200px;
        .el-tag__content {
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
    .capture-classify-list {
      margin-top: 10px;
      overflow: auto;
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .active {
        border: 1px solid #409eff !important;
        color: #409eff !important;
      }
      .capture-classify-item {
        width: 134px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: 4px 8px;
        border: 1px solid #d9dfe4;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        color: #262626;
        box-sizing: border-box;
        line-height: 1.2;
        &:hover {
          background-color: #f0f4f8;
        }
      }
    }

    .empty-classify {
      height: calc(100% - 70px);
    }
  }
}
</style>
