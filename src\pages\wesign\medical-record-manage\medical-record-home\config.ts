import type { TableColumnItem } from "@/types"
import type { FormRules } from "element-plus"
import { MenuOperationEnum } from "@/configs"

export const menuId = "/medical-record/list"

export const tabsConfig = [
  { label: "运行数据", path: "/medical-record/list/recently" },
  { label: "历史数据", path: "/medical-record/list/history" }
]

// 病案列表字段
export const recordTableColumns: Array<TableColumnItem> = [
  { type: "selection", width: 50, fixed: "left" },
  { prop: "statusEnumName", label: "病案状态", minWidth: 120 },
  { prop: "businessDetails", label: "业务详情", minWidth: 100 },
  { prop: "patientName", label: "姓名", minWidth: 260, must: true, showOverflowTooltip: false },
  { prop: "mrNo", label: "病案号", minWidth: 150, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 100, must: true },
  { prop: "visitId", label: "住院次数", minWidth: 90 },
  { prop: "patientSex", label: "性别", minWidth: 60 },
  { prop: "idCard", label: "身份证号", minWidth: 180 },
  { prop: "inHospitalDatetime", label: "入院日期", minWidth: 180, sortable: true },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 100 },
  { prop: "outHospitalDatetime", label: "出院日期", minWidth: 180, sortable: true },
  {
    prop: "outHospitalDeptName",
    label: "出院科室",
    tooltipContent: "在院患者入院科室与出院科室默认一致",
    minWidth: 100
  },
  { prop: "isSpecial", label: "特殊病案", minWidth: 90 },
  { prop: "deletionCount", label: "缺失记录", minWidth: 90 },
  { prop: "operation", label: "操作", width: 210, fixed: "right", must: true }
]

// 病案操作按钮配置
export const operationBtnConfig = [
  // { label: "同步", path: "", key: MenuOperationEnum.Sync },
  { label: "提交", path: "", key: MenuOperationEnum.Submit },
  { label: "查看", path: "/medical-record/detail", key: MenuOperationEnum.View },
  { label: "缺失登记", path: "", key: MenuOperationEnum.MissingReport },
  { label: "特殊病案", path: "", key: MenuOperationEnum.Special },
  { label: "示踪", path: "/medical-record/trace", key: MenuOperationEnum.Trace },
  { label: "收藏", path: "", key: MenuOperationEnum.Favorite },
  { label: "历史", path: "/medical-record/history", key: MenuOperationEnum.History },
  { label: "拍摄", path: "/scan-system", key: MenuOperationEnum.Capture }
]

// 历史数据列表
export const historyTableColumns: Array<TableColumnItem> = [
  { type: "selection", width: 50, fixed: "left" },
  { prop: "businessDetails", label: "业务详情", minWidth: 100 },
  { prop: "patientName", label: "姓名", minWidth: 260, must: true, showOverflowTooltip: false },
  { prop: "mrNo", label: "病案号", minWidth: 150, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 100, must: true },
  { prop: "visitId", label: "住院次数", minWidth: 90 },
  { prop: "patientSex", label: "性别", minWidth: 60 },
  { prop: "inHospitalDatetime", label: "入院日期", minWidth: 180, sortable: true },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 100 },
  { prop: "outHospitalDatetime", label: "出院日期", minWidth: 180, sortable: true },
  {
    prop: "outHospitalDeptName",
    label: "出院科室",
    tooltipContent: "在院患者入院科室与出院科室默认一致",
    minWidth: 100
  },
  { prop: "isSpecial", label: "特殊病案", minWidth: 90 },
  { prop: "deletionCount", label: "缺失记录", minWidth: 90 },
  { prop: "operation", label: "操作", width: 210, fixed: "right", must: true }
]

// 历史数据操作
export const historyBtnConfig = [
  { label: "查看", path: "/medical-record/detail", key: MenuOperationEnum.View },
  { label: "示踪", path: "/medical-record/trace", key: MenuOperationEnum.Trace },
  { label: "收藏", path: "", key: MenuOperationEnum.Favorite }
]

/* ================== 封存 =================== */

export const sealingStatusSelectOptions = [
  { label: "已封存", value: 3 },
  { label: "未封存", value: 0 }
]

export const sealingStatusOptions = [
  { label: "已封存", value: 3 },
  { label: "已申请解封", value: 2 },
  { label: "已申请封存", value: 1 },
  { label: "未封存", value: 0 }
]

/* ================== 缺失登记 =================== */

// 缺失登记表单校验
export const registrationFormRules: FormRules = {
  predictDatetime: [{ required: true, message: "请选择预计补交日期", trigger: "blur" }],
  type: [{ required: true, message: "请选择报告类型", trigger: "blur" }],
  mrClassCode: [{ required: true, message: "请选择文书分类", trigger: "blur" }],
  name: [{ required: true, message: "请填写报告名称", trigger: "blur" }]
}

// 缺失登记表格
export const missingReportTableColumns: Array<TableColumnItem> = [
  { type: "index", label: "序号", width: 60 },
  { prop: "status", label: "状态", width: 90 },
  { prop: "predictDatetime", label: "预计补交日期", minWidth: 110 },
  { prop: "mrClassCode", label: "文书分类", minWidth: 90 },
  { prop: "type", label: "报告类型", width: 90 },
  { prop: "name", label: "报告名称", width: 110 },
  { prop: "reason", label: "缺失原因", width: 110 },
  { prop: "operation", label: "操作", minWidth: 100, fixed: "right" }
]

export const missingReportTypeOptions = [
  { label: "电子", value: "ELECTRON" },
  { label: "纸质", value: "PAPER" }
]

export const inHospitalOptions = [
  { label: "是", value: true },
  { label: "否", value: false }
]

export const symbolOptions = [
  { label: "迟归标记", value: "isLateFiling" },
  { label: "返修标记", value: "isRepair" },
  { label: "封存标记", value: "isSealing" },
  { label: "特殊标记", value: "isSpecial" }
]
