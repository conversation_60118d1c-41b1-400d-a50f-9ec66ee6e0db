import type { SearchFormConfigItem, TableColumnItem } from "@/types"
import type { FormRules } from "element-plus"

export const tabsRouterList = [
  { label: "缺失登记", path: "/medical-record/registration/wait" },
  { label: "登记记录", path: "/medical-record/registration/record" }
]

// 查询表单
export const registrationSearchFormConfig: Array<SearchFormConfigItem> = [
  { type: "patient", label: "患者信息" },
  { type: "input", label: "主治医师", prop: "doctorInCharge" },
  { type: "daterange", label: "出院日期", prop: "outHospitalDatetime" }
]

// 缺失登记表格
export const registrationWaitTableColumns: Array<TableColumnItem> = [
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "patientSex", label: "性别", minWidth: 60 },
  { prop: "mrNo", label: "病案号", minWidth: 100, sortable: true, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 100, sortable: true, must: true },
  { prop: "visitId", label: "住院次数", minWidth: 100 },
  { prop: "inHospitalDatetime", label: "入院日期", minWidth: 180 },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 100 },
  { prop: "outHospitalDatetime", label: "出院日期", minWidth: 180 },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 100 },
  { prop: "doctorInCharge", label: "主治医师", minWidth: 120 },
  { prop: "operation", label: "操作", width: 100, fixed: "right", must: true }
]

// 缺失登记表单校验
export const registrationFormRules: FormRules = {
  predictDatetime: [{ required: true, message: "请选择预计补交日期", trigger: "blur" }],
  type: [{ required: true, message: "请选择报告类型", trigger: "blur" }],
  mrClassCode: [{ required: true, message: "请选择文书分类", trigger: "blur" }],
  name: [{ required: true, message: "请填写报告名称", trigger: "blur" }]
}

// 缺失登记表格
export const missingReportTableColumns: Array<TableColumnItem> = [
  { type: "index", label: "序号", width: 60 },
  { prop: "status", label: "状态", width: 90 },
  { prop: "predictDatetime", label: "预计补交日期", minWidth: 110 },
  { prop: "mrClassCode", label: "文书分类", minWidth: 90 },
  { prop: "type", label: "报告类型", width: 90 },
  { prop: "name", label: "报告名称", width: 110 },
  { prop: "reason", label: "缺失原因", width: 110 },
  { prop: "operation", label: "操作", minWidth: 70, fixed: "right" }
]

// 缺失登记记录详情表格
export const missingReportRecordTableColumns: Array<TableColumnItem> = [
  { type: "index", label: "序号", width: 60 },
  { prop: "predictDatetime", label: "预计补交日期", minWidth: 110 },
  { prop: "mrClassCode", label: "文书分类", minWidth: 90 },
  { prop: "type", label: "报告类型", width: 90 },
  { prop: "name", label: "报告名称", width: 110 },
  { prop: "reason", label: "缺失原因", width: 110 }
]

// 登记记录表格
export const registrationRecordTableColumns: Array<TableColumnItem> = [
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "patientSex", label: "性别", minWidth: 60 },
  { prop: "mrNo", label: "病案号", minWidth: 100, sortable: true, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 100, sortable: true, must: true },
  { prop: "visitId", label: "住院次数", minWidth: 100 },
  { prop: "inHospitalDatetime", label: "入院日期", minWidth: 180 },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 100 },
  { prop: "outHospitalDatetime", label: "出院日期", minWidth: 180 },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 100 },
  { prop: "doctorInCharge", label: "主治医师", minWidth: 120 },
  { prop: "creator", label: "登记人", minWidth: 120, must: true },
  { prop: "createdDatetime", label: "登记时间", minWidth: 120, must: true },
  { prop: "operation", label: "操作", width: 80, fixed: "right", must: true }
]
