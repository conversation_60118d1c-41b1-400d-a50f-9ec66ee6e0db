import axios from "@/interfaces/axios-instance"

/**
 * @method GET
 * @desc   ICD-范围层级查询(当前暂未使用)
 */
export function searchIcdCodeRangeApi(obj) {
  const { parentId, icdVersionId } = obj
  return axios({
    method: "get",
    url: `/api/hospital/icd/range/${parentId}`,
    params: {
      icdVersionId
    }
  })
}

interface DeleteICDParams {
  id: number
  icdVersionId: number
}

/* ======================== ICD9 ======================== */

/**
 * @method POST
 * @desc   ICD9-CM新增
 */
export type TypeAddIcdCmParams = {
  icdVersionId: number
  code: string
  name: string
  typeName: string
  entryOptions?: string
}
export function addIcdCmApi(obj: TypeAddIcdCmParams) {
  return axios({
    method: "post",
    url: "/api/hospital/icd-cm",
    data: obj
  })
}

/**
 * @method PUT
 * @desc   ICD9-CM编辑
 */
export type TypeEditIcdCmParams = {
  id: number
  icdVersionId: number
  code: string
  name: string
  typeName: string
  entryOptions?: string
}
export function editIcdCmApi(obj: TypeEditIcdCmParams) {
  return axios({
    method: "put",
    url: "/api/hospital/icd-cm",
    data: obj
  })
}

/**
 * @method DELETE
 * @desc   ICD9-CM删除
 */
export function deleteIcdCmApi(obj: DeleteICDParams) {
  return axios({
    method: "delete",
    url: "/api/hospital/icd-cm",
    data: obj
  })
}
/* ======================== ICD10 ======================== */

/**
 * @method POST
 * @desc   ICD10-新增
 */
export type TypeAddIcdParams = {
  icdVersionId: number
  code: string
  name: string
  additionalCode: string
}
export function addIcdApi(obj: TypeAddIcdParams) {
  return axios({
    method: "post",
    url: "/api/hospital/icd",
    data: obj
  })
}

/**
 * @method PUT
 * @desc   ICD10-编辑
 */
export type TypeEditIcdParams = {
  id: number
  code: string
  name: string
  additionalCode: string
  icdVersionId: number
}
export function editIcdApi(obj: TypeEditIcdParams) {
  return axios({
    method: "put",
    url: "/api/hospital/icd",
    data: obj
  })
}

/**
 * @method DELETE
 * @desc   ICD10-删除
 */
export function deleteIcdApi(obj: DeleteICDParams) {
  return axios({
    method: "delete",
    url: "/api/hospital/icd",
    data: obj
  })
}

/* ======================== 肿瘤形态学编码 ======================== */

/**
 * @method POST
 * @desc   肿瘤形态学编码-新增
 */
export type TypeAddTumourParams = {
  icdVersionId: number
  code: string
  name: string
}
export function addTumourApi(obj: TypeAddTumourParams) {
  return axios({
    method: "post",
    url: "/api/hospital/icd-o",
    data: obj
  })
}

/**
 * @method PUT
 * @desc   肿瘤形态学编码-编辑
 */
export type TypeEditTumourParams = {
  id: number
  code: string
  name: string
  icdVersionId: number
}
export function editTumourApi(obj: TypeEditTumourParams) {
  return axios({
    method: "put",
    url: "/api/hospital/icd-o",
    data: obj
  })
}

/**
 * @method DELETE
 * @desc   肿瘤形态学编码-删除
 */
export function deleteTumourApi(obj: DeleteICDParams) {
  return axios({
    method: "delete",
    url: "/api/hospital/icd-o",
    data: obj
  })
}

/*======================病种编码=======================*/

/**
 * @method DELETE
 * @desc   ICD-病种删除
 */
export function deleteIcdCodeApi(obj: DeleteICDParams) {
  return axios({
    method: "delete",
    url: "/api/hospital/icd-bz",
    data: obj
  })
}

/**
 * @method POST
 * @desc   ICD-病种新增
 */
export interface TypeAddIcdBzParams {
  icdVersionId: number
  code: string
  name: string
}

export function addIcdBzApi(obj: TypeAddIcdBzParams) {
  return axios({
    method: "post",
    url: "/api/hospital/icd-bz",
    data: obj
  })
}

/**
 * @method PUT
 * @desc   ICD-病种编辑
 */
export interface TypeEditIcdBzParams extends TypeAddIcdBzParams {
  id: number
}

export function editIcdBzApi(obj: TypeEditIcdBzParams) {
  return axios({
    method: "put",
    url: "/api/hospital/icd-bz",
    data: obj
  })
}

/**
 * @method GET
 * @desc   ICD-病种查询
 */
export function searchIcdBzApi(params) {
  return axios({
    method: "get",
    url: `/api/hospital/icd-bz`,
    params: params
  })
}

/*================================对照==========================*/
type IcdDzParams = IPaginationRequestParams & {
  code?: string
}

/**
 * @method GET
 * @desc   ICD-对照查询
 */
export function searchIcdDzApi(obj: IcdDzParams) {
  return axios({
    method: "get",
    url: `/api/hospital/icd-dm`,
    params: obj
  })
}

/**
 * @method DELETE
 * @desc   ICD-对照删除
 */
export function deleteIcdDzApi(obj: DeleteICDParams) {
  return axios({
    method: "delete",
    url: "/api/hospital/icd-dm",
    data: obj
  })
}

/**
 * @method POST
 * @desc   ICD-对照新增
 */
export type TypeAddIcdDzParams = {
  icdVersionId: number
  code: string
  name: string
  contrastCode: string // 医保版本编码
  contrastName: string
}

export function addIcdDzApi(obj: TypeAddIcdDzParams) {
  return axios({
    method: "post",
    url: "/api/hospital/icd-dm",
    data: obj
  })
}

/**
 * @method PUT
 * @desc   ICD-对照编辑
 */
export type TypeEditIcdDzParams = TypeAddIcdDzParams & {
  id: number
}

export function editIcdDzApi(obj: TypeEditIcdDzParams) {
  return axios({
    method: "put",
    url: "/api/hospital/icd-dm",
    data: obj
  })
}
