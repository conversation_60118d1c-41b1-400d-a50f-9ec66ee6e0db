<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <CommonInputFormItem v-model="searchFormState.name" label="接口名称" />
        <CommonSelectFormItem v-model="searchFormState.method" label="请求方式" :options="methodTypeOptions" />
        <CommonSelectFormItem v-model="searchFormState.triggerType" label="触发方式" :options="triggerTypeOptions" />
        <CommonSelectFormItem v-model="searchFormState.scene" label="业务类型" :options="sceneOptions" />
        <CommonSelectFormItem v-model="searchFormState.status" label="状态" :options="rowStatusOptions" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="commonTableRef"
        :table-columns="interfaceTableColumns"
        :request-api="getInterfaceList"
        :request-params="searchParams"
      >
        <template #header>
          <AddButton @click="showForm('add')">新增</AddButton>
        </template>
        <template #triggerType="{ row }">{{ getTriggerTypeDesc(row.triggerType) }}</template>
        <template #scene="{ row }">{{ getSceneDesc(row.scene) }}</template>
        <template #dataType="{ row }">{{ getDataTypeDesc(row.dataType) }}</template>
        <template #operation="{ row }">
          <TableButton @click="handleEdit(row)">编辑</TableButton>
          <TableButton v-if="row.triggerType !== 'BUSINESS'" @click="triggerJob(row)">执行一次</TableButton>
          <TableButton @click="showRecordDialog(row)">记录</TableButton>
          <TableButton @click="handleModifyStatus(row)">
            {{ row.status === "DISABLE" ? "启用" : "禁用" }}
          </TableButton>
          <TableButton @click="handleDelete(row)">删除</TableButton>
        </template>
      </CommonTable>
    </template>

    <DialogContainer
      v-model:visible="visible"
      :title="actionType === 'edit' ? '修改接口配置' : '添加接口配置'"
      :width="1000"
      :confirm-callback="save"
      :cancel-callback="closeForm"
    >
      <el-form
        ref="ruleFormRef"
        label-position="right"
        label-width="130px"
        label-suffix="："
        :model="formState"
        :rules="interfaceFormRules"
        style="max-height: 70vh; overflow: auto"
      >
        <CommonInputFormItem v-model="formState.name" label="接口名称" prop="name" placeholder="请输入接口名称" />

        <CommonRadioFormItem
          v-model="formState.triggerType"
          :options="triggerTypeOptions"
          label="触发方式"
          prop="triggerType"
          placeholder="请选择触发方式"
        />

        <CommonSelectFormItem
          v-model="formState.scene"
          label="业务类型"
          placeholder="请选择业务类型"
          :options="sceneOptions"
        />

        <CommonSelectFormItem
          v-model="formState.systemWsid"
          label="厂商系统"
          prop="systemWsid"
          :options="systemVendorOptions"
        />

        <CommonSelectFormItem
          v-model="formState.dataType"
          label="数据类型"
          prop="dataType"
          :options="dataTypeOptions"
        />

        <CommonSelectFormItem
          v-if="formState.dataType === 'MR_CONTENT'"
          v-model="formState.mrClassCode"
          label="文书分类"
          prop="mrClassCode"
          placeholder="请选择文书分类"
          :options="mrClassOptions"
        />

        <CommonInputFormItem
          v-model.trim="formState.authorization"
          label="认证信息"
          prop="authorization"
          placeholder="请输入认证信息"
        />

        <CommonInputFormItem v-model="formState.url" label="URL" prop="url" placeholder="请输入URL" />

        <CommonSelectFormItem
          v-model="formState.method"
          label="请求方式"
          prop="method"
          placeholder="请选择请求方式"
          :options="methodTypeOptions"
        />

        <CommonInputFormItem
          v-model.trim="formState.queryParams"
          label="请求参数"
          prop="queryParams"
          placeholder="请输入请求参数"
        />

        <el-form-item v-if="formState.method === 'POST'" label="请求体" prop="bodyParams">
          <el-input v-model.trim="formState.bodyParams" placeholder="请输入请求体" type="textarea" :rows="5" />
        </el-form-item>

        <CommonSelectFormItem
          v-model="formState.contentType"
          label="content-type"
          prop="contentType"
          placeholder="请选择content-type"
          :options="resultTypeOptions"
        />

        <CommonSelectFormItem
          v-model="formState.resultType"
          label="响应体"
          prop="resultType"
          placeholder="请选择响应体类型"
          :options="resultTypeOptions"
        />

        <CommonInputFormItem
          v-model.trim="formState.resultField"
          label="返回数据取值"
          prop="resultField"
          placeholder="请输入返回数据取值"
        />

        <el-form-item ref="largeScreen" label="数据清洗" prop="sourceCode" class="data-clean">
          <Codemirror
            v-model="formState.sourceCode"
            placeholder="请输入"
            :tab-size="10"
            :extensions="dataExtensions"
            :style="{
              maxHeight: isFullscreen ? '100vh' : '150px',
              height: isFullscreen ? '100vh' : 'auto',
              width: '100%'
            }"
          />
          <div ref="fullScreen" class="fullscreen-btn">
            <i v-if="!isFullscreen" title="全屏显示" class="ri-fullscreen-line" @click="toggle"></i>
            <i v-else title="退出全屏" class="ri-fullscreen-exit-line" @click="toggle"></i>
          </div>
        </el-form-item>

        <CommonRadioFormItem
          v-model="formState.resultDataType"
          :options="resultDataTypeOptions"
          prop="resultDataType"
          label="返回数据类型"
          placeholder="请选择返回数据类型"
        />

        <CommonSelectFormItem
          v-if="formState.resultDataType === 'DATA_KEY_VALUE'"
          v-model="formState.templateWsid"
          label="关联模板"
          prop="templateWsid"
          placeholder="请选择关联模板"
          :options="templateOptions"
        />

        <CommonSelectFormItem
          v-model="formState.charset"
          label="编码格式"
          prop="charset"
          placeholder="请选择编码格式"
          :options="charsetOptions"
        />

        <CommonRadioFormItem
          v-if="formState.triggerType === 'TIME'"
          v-model="formState.extractType"
          :options="extractTypeOptions"
          prop="extractType"
          label="采集模式"
          placeholder="请选择采集模式"
        />

        <template v-if="formState.extractType === 'INCREMENT'">
          <CommonInputFormItem
            v-model="formState.incrementFieldKey"
            label="自增key"
            prop="incrementFieldKey"
            placeholder="请输入自增key"
          />

          <CommonRadioFormItem
            v-model="formState.incrementFieldType"
            label="自增字段类型"
            prop="incrementFieldType"
            :options="incrementTypeOptions"
            @change="() => (formState.incrementLastValue = '')"
          />

          <CommonInputFormItem
            v-if="formState.incrementFieldType === 'NUMBER'"
            v-model="formState.incrementLastValue"
            label="最近自增值"
            prop="incrementLastValue"
            placeholder="请输入最近自增值"
          />

          <el-form-item v-if="formState.incrementFieldType === 'TIME'" label="最近自增值" prop="incrementLastValue">
            <el-date-picker
              v-model="formState.incrementLastValue"
              placeholder="请选择最近自增时间"
              type="datetime"
              value-format="x"
            />
          </el-form-item>

          <el-form-item label="增幅" prop="incrementDelta">
            <el-input v-model.trim="formState.incrementDelta" type="string" placeholder="请输入增幅">
              <template v-if="formState.incrementFieldType === 'TIME'" #append>秒</template>
            </el-input>
          </el-form-item>
        </template>

        <template v-if="formState.extractType === 'FIXED_TIME_RANGE'">
          <CommonInputFormItem
            v-model="formState.fixedTimeFieldKey"
            label="固定时间自增key"
            prop="fixedTimeFieldKey"
            placeholder="请输入自增key"
          />

          <CommonSelectFormItem
            v-model="formState.fixedTimeRange"
            :options="fixedTimeRangeOptions"
            label="时间范围"
            prop="fixedTimeRange"
            placeholder="请选择时间范围"
          />
        </template>

        <el-form-item v-if="formState.triggerType === 'TIME'" label="采集频率(cron)" prop="cron">
          <el-input v-model="formState.cron" placeholder="0 0/10 * * * ?" />
        </el-form-item>

        <el-form-item label="超时时间" prop="timeout">
          <el-input v-model.trim="formState.timeout" maxlength="5" type="string" placeholder="请输入超时时间">
            <template #append>秒</template>
          </el-input>
        </el-form-item>
      </el-form>
    </DialogContainer>

    <!-- 记录弹窗 -->
    <Record :record-visible-dialog="recordDialogVisible" :selected-row="selectedRow" @close="closeRecordDialog" />
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref, nextTick } from "vue"
import { java } from "@codemirror/lang-java"
import { useFullscreen } from "@vueuse/core"
import { Codemirror } from "vue-codemirror"
import {
  SearchContainer,
  DialogContainer,
  AddButton,
  CommonTable,
  TableButton,
  PageContainer,
  CommonSelectFormItem,
  CommonInputFormItem,
  CommonRadioFormItem
} from "@/base-components"
import { TabsRouter } from "@/page-components"
import { rowStatusOptions } from "@/configs"
import { useCommonOptions, useTableSearch } from "@/hooks"
import useFormSetting from "@/hooks/useFormSetting_v2"
import { getMrClassList, getAllTemplate } from "@/interfaces"
import { useSystemStore } from "@/stores"
import { SystemPrompt, Message, extractErrorMsg, toastError } from "@/utils/message-tool"
import Record from "./components/record.vue"
import {
  interfaceFormRules,
  interfaceTableColumns,
  charsetOptions,
  dataTypeOptions,
  methodTypeOptions,
  resultTypeOptions,
  tabsRouterList,
  triggerTypeOptions,
  sceneOptions,
  extractTypeOptions,
  myTheme,
  resultDataTypeOptions,
  getTriggerTypeDesc,
  getSceneDesc,
  getDataTypeDesc,
  fixedTimeRangeOptions,
  incrementTypeOptions
} from "./config"
import {
  modifyIntegrations,
  addIntegrations,
  editIntegrations,
  getCollectSystemList,
  triggerJobApi,
  getInterfaceList,
  type IntegrationsData
} from "./interface"
import type { FormInstance } from "element-plus"

const systemStore = useSystemStore()

/* ======================== 页面加载时获取选项 ======================== */

// 获取关联模板选项
const { options: templateOptions } = useCommonOptions({
  getOptionsApi: getAllTemplate,
  labelAlias: "templateName",
  valueAlias: "templateWsid"
})

// 获取厂商系统选项
const { options: systemVendorOptions } = useCommonOptions({
  getOptionsApi: getCollectSystemList,
  labelAlias: "systemName",
  valueAlias: "wsid"
})

// 获取所有文书分类选项
const { options: mrClassOptions } = useCommonOptions({
  getOptionsApi: getMrClassList,
  labelAlias: "mrClassName",
  valueAlias: "mrClassCode"
})

/* ======================== 表格内容 ======================== */

const commonTableRef = ref<InstanceType<typeof CommonTable>>()

const searchFormState = reactive({
  name: "", // 接口名称
  method: "", // 请求方式
  triggerType: "", // 触发方式
  scene: "", // 业务类型
  status: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/* ======================== 弹窗编辑 ======================== */

const formInitialValue = {
  targetWsid: "",
  name: "", // 接口名称
  triggerType: "TIME", // 触发方式
  scene: "", // 业务类型
  systemWsid: "", // 厂商系统
  dataType: "", // 数据类型
  mrClassCode: "", // 标准文书类型
  authorization: "", // 认证信息
  url: "", // URL
  method: "", // 请求方式
  queryParams: "", // 请求参数query
  bodyParams: "", // 请求参数body
  contentType: "JSON", // content-type
  resultType: "JSON", // 返回体类型
  resultField: "", // 返回数据取值
  resultDataType: "FILE", // 返回数据类型
  sourceCode: "", // 数据清洗源码
  templateWsid: "", // 关联模板
  charset: "", // 编码格式
  extractType: "ALL", // 采集模式
  incrementFieldKey: "", // 自增key
  fixedTimeFieldKey: "", // 固定时间自增key
  incrementFieldType: "TIME", // 自增key类型
  incrementLastValue: "", // 自增key值
  incrementDelta: "", // 自增步长
  fixedTimeRange: "", // 时间范围
  cron: "", // 采集频率
  timeout: "" // 超时时间
}

const { visible, showForm, closeForm, actionType, formState } = useFormSetting(formInitialValue)

const ruleFormRef = ref<FormInstance>()

// 编辑
const handleEdit = row => {
  showForm("edit", row)
  // @ts-ignore
  formState.incrementLastValue = Number(formState.incrementLastValue) // 自增值需要转换为数字（时间戳无法转换的bug）
}

const save = () => {
  ruleFormRef.value?.validate(async valid => {
    if (!valid) return
    if (!systemVendorOptions.value.find(option => option.value === formState.systemWsid)) {
      Message.error("该厂商系统已不存在或已被禁用，请重新选择")
      return false
    }
    try {
      systemStore.showLoading("正在提交")
      const requestParams: IntegrationsData = {
        integrationWsid: formState.targetWsid,
        systemWsid: formState.systemWsid,
        dataType: formState.dataType,
        mrClassCode: formState.dataType === "MR_CONTENT" ? formState.mrClassCode : "", // 数据类型为文书类型时，才传递mrClassCode参数
        cron: formState.cron,
        triggerType: formState.triggerType,
        scene: formState.scene,
        extractType: formState.extractType,
        sourceCode: formState.sourceCode,
        fixedTimeFieldKey: formState.fixedTimeFieldKey,
        incrementFieldKey: formState.incrementFieldKey,
        incrementFieldType: formState.incrementFieldType,
        incrementLastValue: formState.incrementLastValue,
        incrementDelta: formState.incrementDelta,
        fixedTimeRange: formState.fixedTimeRange,
        apiConfig: {
          name: formState.name,
          url: formState.url,
          method: formState.method,
          queryParams: formState.queryParams,
          bodyParams: formState.bodyParams,
          contentType: formState.contentType,
          resultType: formState.resultType,
          resultField: formState.resultField,
          resultDataType: formState.resultDataType,
          templateWsid: formState.templateWsid,
          charset: formState.charset,
          timeout: formState.timeout,
          authorization: formState.authorization
        }
      }
      if (actionType.value === "edit") {
        await editIntegrations(requestParams)
        Message.success("编辑成功！")
      } else {
        await addIntegrations(requestParams)
        Message.success("添加成功！")
      }
      systemStore.hideLoading()
      closeForm()
      commonTableRef.value?.refreshTableData()
    } catch (error: any) {
      systemStore.hideLoading()
      toastError(error, "操作失败")
    }
  })
}

/* ================= 行操作 ==================== */

// 执行一次
function triggerJob(row) {
  systemStore.showLoading()
  triggerJobApi(row.wsid)
    .then(res => {
      systemStore.hideLoading()
      commonTableRef.value?.refreshTableData()
      if (res.data?.message) Message.success(res.data.message)
    })
    .catch(err => {
      systemStore.hideLoading()
      toastError(err, "操作失败")
    })
}

// 切换启用/禁用状态
const handleModifyStatus = row => {
  const nextStatus = row.status === "ENABLE" ? "DISABLE" : "ENABLE"
  if (nextStatus === "ENABLE") {
    modifyStatusImplement(row.wsid, nextStatus)
  } else {
    SystemPrompt(`您确定要禁用${row.name} ，禁用后关联该接口所有配置将不能使用`).then(() => {
      modifyStatusImplement(row.wsid, nextStatus)
    })
  }
}

// 删除
function handleDelete(row) {
  SystemPrompt(`您确定要删除${row.name} ，删除后关联该接口所有配置将不能使用`, "error").then(() => {
    modifyStatusImplement(row.wsid, "DEL")
  })
}

// 执行数据源修改
const modifyStatusImplement = (targetWsid: string, status: "DEL" | "ENABLE" | "DISABLE") => {
  systemStore.showLoading("请稍候")
  modifyIntegrations({ targetWsid, status })
    .then(() => {
      systemStore.hideLoading()
      Message.success(status === "DEL" ? "删除数据成功" : "修改状态成功")
      commonTableRef.value?.refreshTableData()
    })
    .catch(err => {
      systemStore.hideLoading()
      toastError(err, "操作失败")
    })
}

/* ======================== 数据清洗 ======================== */

// 数据清洗
const dataExtensions = [java(), myTheme]

const largeScreen = ref(null)
const fullScreen = ref(null)
const { toggle, isFullscreen } = useFullscreen(largeScreen)

/* ======================== 记录弹窗 ======================== */

const recordDialogVisible = ref(false)
const selectedRow = ref()

const showRecordDialog = row => {
  selectedRow.value = row
  nextTick(() => {
    recordDialogVisible.value = true
  })
}

const closeRecordDialog = () => {
  recordDialogVisible.value = false
}
</script>

<style lang="less" scoped>
:deep(.el-form-item) {
  margin-bottom: 15px !important;
}

.cron {
  :deep(.el-form-item__label) {
    width: 132px !important;
  }
}

.data-clean {
  :deep(.el-form-item__content) {
    align-items: baseline;
    position: relative;
  }
}

.fullscreen-btn {
  position: absolute;
  cursor: pointer;
  right: 10px;
  top: 15px;
  line-height: 1;
  transform: translateY(-50%);
  i {
    opacity: 0.5;
    font-size: 20px;
    color: #333;
  }
  &:hover {
    i {
      opacity: 1;
    }
  }
}
.field-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 50px;
  flex-wrap: wrap;
  margin-bottom: 10px;
}

.operation-container {
  span {
    margin-right: 10px;
    color: var(--el-color-primary);
    cursor: pointer;
  }
}

.dialog-footer {
  text-align: right;
}

:deep(.cm-editor) {
  border: 1px solid #dcdfe6;
}

:deep(.cm-focused) {
  outline: none !important;
}

:deep(.cm-lineNumbers) {
  border-right: 1px solid #dcdfe6;
}
</style>
