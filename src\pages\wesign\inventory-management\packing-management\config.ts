import type { TableColumnItem, SearchFormConfigItem } from "@/types"

// 装箱管理表格配置
export const packingTableColumns: Array<TableColumnItem> = [
  { type: "selection", width: 50, fixed: "left" },
  { prop: "documentStorageBoxNumber", label: "箱号", minWidth: 100, must: true },
  { prop: "documentStorageBoxCount", label: "箱内数量", width: 120, must: true, sortable: true },
  { prop: "documentStorageBoxLocation", label: "存址", minWidth: 150, must: true },
  { prop: "documentStorageBoxShelveDatetime", label: "上架时间", minWidth: 120, must: true },
  { prop: "documentStorageBoxShelvePerson", label: "上架操作人", minWidth: 120, must: true },
  { prop: "operation", label: "操作", width: 120, fixed: "right", must: true }
]

// 装箱管理搜索表单配置
export const packingSearchFormConfig: Array<SearchFormConfigItem> = [
  { type: "input", label: "箱号", prop: "documentStorageBoxNumber" },
  { type: "input", label: "存址", prop: "documentStorageBoxLocation" }
]

// 装箱操作表格配置
export const packageOperationColumns: Array<TableColumnItem> = [
  { prop: "barCode", label: "条码", width: 120, must: true },
  { prop: "patientName", label: "患者姓名", minWidth: 120, must: true },
  { prop: "mrNo", label: "病案号", minWidth: 150, sortable: true, must: true },
  { prop: "operation", label: "操作", width: 75, fixed: "right", must: true }
]

// 装箱详情表格配置
export const packageDetailColumns: Array<TableColumnItem> = [
  { prop: "barCode", label: "条码", width: 120, must: true },
  { prop: "patientName", label: "患者姓名", minWidth: 120, must: true },
  { prop: "mrNo", label: "病案号", minWidth: 150, sortable: true, must: true },
  { prop: "operation", label: "操作", width: 75, fixed: "right", must: true }
]

export const storeLocationColumns: Array<TableColumnItem> = [
  { type: "selection", width: 50, fixed: "left" },
  { prop: "documentStorageRackName", label: "库位名称", minWidth: 120, must: true },
  { prop: "documentStorageRackNumber", label: "库位编码", width: 200, must: true }
]

//装箱表单校验规则
export const packingFormRules = {
  packingCode: [{ required: true, message: "请输入箱号", trigger: "change" }],
  packingBarCode: [{ required: true, message: "请输入明细条码", trigger: "change" }]
}
