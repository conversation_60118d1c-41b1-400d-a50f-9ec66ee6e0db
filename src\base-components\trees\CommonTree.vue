<template>
  <div class="common-tree">
    <el-tree
      ref="treeRef"
      :node-key="props.nodeKey"
      :indent="0"
      :data="props.data"
      :show-checkbox="props.showCheckbox"
      :default-expand-all="props.defaultExpandAll"
      :highlight-current="true"
      :props="props.treeProps"
      :class="`${styleType}-tree`"
      :filter-node-method="props.filterTreeNode || filterTreeNode"
      :default-checked-keys="props.checkedKeys"
      :draggable="props.draggable"
      :allow-drop="props.allowDrop"
      :allow-drag="props.allowDrag"
      @check="handleNodeCheck"
      @node-click="handleNodeClick"
    >
      <template #default="{ node, data }">
        <slot name="title" :node="node" :data="data" :has-children="hasChildren">
          <span class="custom-tree-node">
            <!-- 图标 -->
            <i v-if="data.type === 'FILE'" class="ri-file-line" style="margin-right: 4px"></i>
            <!-- 文本 -->
            <OverflowTooltip
              :content="getNodeTooltipContent(node, data, props.showFilePage)"
              :max-width="props.contentMaxWidth"
            />
          </span>
        </slot>
      </template>
    </el-tree>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue"
import { hasChildren, getNodeTooltipContent } from "@/utils"
import { OverflowTooltip } from ".."
import type { ElTree } from "element-plus"
import type { TreeOptionProps } from "element-plus/es/components/tree/src/tree.type"

// showCheckbox 和 showDelete请勿都传true
interface CommonTreeProps {
  nodeKey?: string
  styleType?: "native" | "custom" | "approval" | "table" // eltree的样式，默认使用 custom 样式
  data: any[] // eltree的数据
  showCheckbox?: boolean // eltree 原生属性，是否显示checkbox
  showDelete?: boolean // 是否展示删除按钮
  defaultExpandAll?: boolean // eltree 原生属性，	是否默认展开所有节点
  treeProps?: TreeOptionProps // eltree 原生属性，用于指定字段属性
  filterNodeKeyword?: string
  filterKeys?: any[] //复选框
  checkedKeys?: any[]
  showFilePage?: boolean // 是否展示文档页数
  contentMaxWidth?: string // 节点字符串的最大长度
  draggable?: boolean
  allowDrop?: (params: any) => any
  allowDrag?: (params: any) => any
  filterTreeNode?: (params: any) => any //过滤树节点方法
}

// props
const props = withDefaults(defineProps<CommonTreeProps>(), {
  nodeKey: "wsid",
  styleType: "custom",
  showCheckbox: false,
  showDelete: false,
  defaultExpandAll: true,
  treeProps: {
    // @ts-ignore 此处红线，未知是否是 element 或者 vue bug，无法指定type类型
    label: "name",
    children: "children",
    isLeaf: "leaf",
    disabled: "disable",
    class: treeNodeData => {
      // console.log(`output->treeNodeData`, treeNodeData)
      if (treeNodeData.type === "FILE" && treeNodeData?.disableCheckbox) {
        return "custom-tree-node--file checkbox-hidden"
      }
      if (treeNodeData.type === "FILE" || treeNodeData.catalogType === "FIELD") {
        return "custom-tree-node--file"
      } else if (treeNodeData.type === "PAGE" || treeNodeData.catalogType === "CATALOG") {
        return "custom-tree-node--page"
      } else {
        return "custom-tree-node--folder"
      }
    }
  },
  filterNodeKeyword: "",
  filterKeys: [] as any,
  checkedKeys: [] as any,
  showFilePage: false,
  contentMaxWidth: "85%",
  draggable: false,
  allowDrop: undefined,
  allowDrag: undefined,
  filterTreeNode: undefined
})

// ref
const treeRef = ref()
// emits
const emits = defineEmits(["checkNode", "clickNode", "deleteNode"])

/**
 * @description 切换勾选checkbox
 * @param data 当前点击的节点 - 原始prop传入的node节点的数据
 * @param checkedData 所有勾选的节点信息 checkedKeys: nodeKey[], checkedNodes: node[], halfCheckedKeys: [], halfCheckedNodes: []
 */
const handleNodeCheck = (node, checkedData) => {
  emits("checkNode", node, checkedData.checkedNodes)
}

// 点击节点 emits 当前点击的节点
const handleNodeClick = node => {
  emits("clickNode", node)
}

// 点击节点的删除按钮
const deleteNode = node => {
  emits("deleteNode", node)
}

// 获取选中的节点
const getCheckedNodes = () => {
  return treeRef.value?.getCheckedNodes(false, false)
}

// 设置选中的节点
const setCheckedNodes = (checkedNodes: any[]) => {
  const __checkedNodes: any[] = []
  checkedNodes.forEach(item => {
    __checkedNodes.push(item.mrClassCode)
  })
  treeRef.value.setCheckedKeys(__checkedNodes, false)
}

// 根据key选中节点
const setCheckedKeys = (keys: string[]) => {
  console.log(keys)
  treeRef.value.setCheckedKeys(keys, false)
  emits("checkNode", undefined, getCheckedNodes())
}

// 全选&取消全选所有节点
const checkAllNodes = (checkAll: boolean) => {
  if (checkAll) {
    const allRootNodeKeyList = props.data.map(node => node.wsid)
    setCheckedKeys(allRootNodeKeyList)
  } else {
    setCheckedKeys([])
  }
}

// 过滤树节点
const filterTreeNode = (value, data, node) => {
  console.log(value, data, node)

  const labelAlias = props.treeProps?.label as string
  if (
    data[labelAlias].includes(props.filterNodeKeyword) ||
    node.parent?.data[labelAlias]?.includes(props.filterNodeKeyword)
  ) {
    return true
  }
  return false

  // return props.checkedKeys.indexOf(data.wsid) !== -1
}

// 获取半勾选节点
const getHalfCheckedNodes = () => {
  return treeRef.value?.getHalfCheckedNodes(true)
}

// 设置节点被选中
const setCurrentKey = key => {
  if (!treeRef.value) return
  treeRef.value.setCurrentKey(key)
}

watch(
  () => props.filterNodeKeyword,
  val => {
    treeRef.value.filter(val)
  }
)

watch(
  () => props.filterKeys,
  val => {
    treeRef.value.filter(val)
  },
  {
    deep: true
  }
)

watch(
  () => props.checkedKeys,
  val => {
    if (val) {
      if (treeRef.value) {
        setCheckedKeys(val)
      }
    }
  },
  {
    immediate: true,
    deep: true
  }
)

defineExpose({
  setCheckedNodes,
  setCheckedKeys,
  getCheckedNodes,
  checkAllNodes,
  setCurrentKey,
  filterTreeNode,
  getHalfCheckedNodes
})
</script>

<style lang="less" scoped>
.common-tree {
  overflow-y: auto;
  height: 100%;

  .custom-tree-node {
    display: inline-block;
    overflow: hidden;
    width: calc(100% - 65px);
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  // 公共自定义tree
  :deep(.custom-tree.el-tree) {
    .el-tree-node {
      position: relative;
      color: #333;
      & > {
        .el-tree-node__children {
          overflow: initial;
        }
      }
    }

    .el-tree-node__children {
      padding-left: 26px;
    }

    .el-tree > .el-tree-node::before {
      border-left: none;
    }

    .el-tree > .el-tree-node::after {
      border-top: none;
    }

    .el-tree .el-tree-node__expand-icon.expanded {
      transform: rotate(0deg);
    }

    .el-tree-node__content > .el-tree-node__expand-icon {
      display: none;
      padding: 0;
    }

    .el-tree-node__content > .custom-tree-node {
      display: inline-block;
      overflow: hidden;
      width: calc(100% - 65px);
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .el-tree-node:not(.custom-tree-node--file) > .el-tree-node__content::before {
      height: 16px;
      margin-right: 10px;
      background-color: white;
      content: url(@/assets/svg/tree/open.svg);
    }

    .el-tree-node.is-expanded:not(.custom-tree-node--file) > .el-tree-node__content::before {
      height: 16px;
      margin-right: 10px;
      background-color: white;
      content: url(@/assets/svg/tree/close.svg);
    }

    .checkbox-hidden {
      .el-checkbox {
        display: none;
      }
    }
  }

  // 借阅tree
  :deep(.approval-tree.el-tree) {
    .el-tree-node__content {
      padding: 4px 0;
    }

    .el-tree-node__children {
      padding-left: 26px;
    }

    .el-tree > .el-tree-node::before {
      border-left: none;
    }

    .el-tree > .el-tree-node::after {
      border-top: none;
    }

    .el-tree .el-tree-node__expand-icon.expanded {
      transform: rotate(0deg);
    }

    .el-tree-node__content > .el-tree-node__expand-icon {
      display: none;
      padding: 0;
    }

    .el-tree-node:not(.custom-tree-node--file) > .el-tree-node__content::before {
      height: 16px;
      margin-right: 10px;
      background-color: white;
      content: url(@/assets/svg/tree/open.svg);
    }

    .el-tree-node.is-expanded:not(.custom-tree-node--file) > .el-tree-node__content::before {
      height: 16px;
      margin-right: 10px;
      background-color: white;
      content: url(@/assets/svg/tree/close.svg);
    }

    .el-tree-node.custom-tree-node--page.is-expanded > .el-tree-node__content::before {
      height: 16px;
      margin-right: 10px;
      background-color: white;
      content: none;
    }
  }

  // 表格tree
  :deep(.table-tree.el-tree) {
    .el-tree-node__children {
      padding-left: 15px;
    }

    .el-tree-node__content > .custom-tree-node {
      display: flex;
      justify-content: space-between;
      position: relative;
      width: calc(100% - 100px);
    }
  }
}

:deep(.el-tree) {
  background-color: transparent;

  .el-tree-node__content > .el-checkbox {
    position: absolute !important;
    right: 0 !important;
    margin-right: 16px !important;
  }

  .custom-tree {
    .el-tree-node__content > .el-checkbox {
      position: absolute !important;
      right: 0 !important;
      margin-right: 16px !important;
    }
  }
}
</style>
