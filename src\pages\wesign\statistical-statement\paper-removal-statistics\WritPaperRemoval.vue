<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList"></TabsRouter>
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <DepartmentFormItem v-model:model-value="writPaperRemovalParams.deptWsid" label="科室分类"></DepartmentFormItem>

        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
        />

        <StatisticalTypeFormItem
          v-model:filter-prop="searchFormState.statisticalTypeFilterProp"
          v-model:filter-value="searchFormState.statisticalTypeFilterValue"
          label="统计类型"
        ></StatisticalTypeFormItem>
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        :table-columns="writPaperRemovalColumns"
        :request-api="getWritPaperStatisticsApi"
        :request-params="searchParams.queryParams"
        :data-callback="formatTableData"
        :pagination="false"
      >
        <template #header>
          <ExportButton
            :export-api="exportWritPaperStatisticsApi"
            :export-params="exportParams"
            :file-name="`文书脱纸率报表_${getCurrentDate()}.xlsx`"
            file-type="application/vnd.ms-excel"
          >
            导出
          </ExportButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, computed, watch } from "vue"
import { cloneDeep } from "lodash-es"
import {
  PageContainer,
  SearchContainer,
  CommonTable,
  PatientFilterFormItem,
  DepartmentFormItem,
  ExportButton
} from "@/base-components"
import { TabsRouter } from "@/page-components"
import { useTableSearch } from "@/hooks"
import { getCurrentDate } from "../config"
import { getWritPaperStatisticsApi, exportWritPaperStatisticsApi } from "../interface"
import StatisticalTypeFormItem from "./components/StatisticalTypeFormItem.vue"
import { tabsRouterList, writPaperRemovalColumns, handleYear, handleMonth, getQuartersTimestamps } from "./config"

const archiveDateEnd = new Date(new Date().setHours(23, 59, 59, 999)).getTime()
const archiveDateStart = new Date(
  new Date(new Date().setMonth(new Date().getMonth() - 1)).setHours(0, 0, 0, 0)
).getTime()

/* ======================== 获取当前季度时间戳 ======================== */
const year = new Date().getFullYear() // 获取当前年份
const quartersTimestamps = getQuartersTimestamps(year)

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  deptWsid: "",
  patientFilterValue: "",
  patientFilterProp: "patientName",
  statisticalTypeFilterProp: "section",
  statisticalTypeFilterValue: [archiveDateStart, archiveDateEnd] as any
})

const writPaperRemovalParams = reactive({
  patientName: "",
  patientId: "",
  mrNo: "",
  deptWsid: "",
  archiveDateStart: archiveDateStart,
  archiveDateEnd: archiveDateEnd
})

const { searchParams, handleQuery, handleReset } = useTableSearch(
  {},
  "",
  { ...searchFormState, ...writPaperRemovalParams },
  writPaperRemovalParams,
  searchFormState
)

// 监听统计类型
watch(
  () => searchFormState.statisticalTypeFilterValue,
  val => {
    if (searchFormState.statisticalTypeFilterProp === "year") {
      val = handleYear(val)
    } else if (searchFormState.statisticalTypeFilterProp === "month") {
      val = handleMonth(val)
    } else if (searchFormState.statisticalTypeFilterProp === "quarter") {
      val = quartersTimestamps[val]
    }
    writPaperRemovalParams.archiveDateStart = val[0] || val.start
    writPaperRemovalParams.archiveDateEnd = val[1] || val.end
  }
)

// 监听患者信息
watch(
  () => searchFormState.patientFilterValue,
  val => {
    if (val) {
      writPaperRemovalParams.patientName = ""
      writPaperRemovalParams.patientId = ""
      writPaperRemovalParams.mrNo = ""
      writPaperRemovalParams[searchFormState.patientFilterProp] = val
    }
  }
)

const exportParams = computed(() => {
  return {
    archiveDateStart: searchFormState.statisticalTypeFilterValue[0],
    archiveDateEnd: searchFormState.statisticalTypeFilterValue[1]
  }
})

function formatTableData(resData) {
  return resData.data.data.rows
}
</script>

<style lang="less" scoped>
:deep(.el-form-item__content) {
  align-items: flex-start !important;
}
</style>
