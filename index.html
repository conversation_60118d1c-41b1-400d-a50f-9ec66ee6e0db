<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/src/assets//logomin.jpg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="pragma" content="no-cache" />
    <meta http-equiv="cache-control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="expires" content="0" />
    <link href="/src/styles/clear-default.css" type="text/css" rel="stylesheet" />
    <script src="/global-config.js"></script>
  </head>

  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
    <script>
      var ua = navigator.userAgent
      // 判断是否为IE
      var isIE = ua.indexOf("compatible") > -1 && ua.indexOf("MSIE") > -1 && ua.indexOf("Opera") === -1
      var isIE11 = ua.indexOf("Trident") > -1 && ua.indexOf("rv:11.0") > -1
      // 跳转到兼容提示页面
      if (isIE11 || isIE) {
        window.location.href = window.location.origin + "/browser-compatibility-hint.html"
      }

      // 是否为 Chrome
      var isChrome = ua.indexOf("Chrome") > -1 && ua.indexOf("Safari") > -1 && ua.indexOf("Edge") === -1
      if (isChrome) {
        var arr = navigator.userAgent.split(" ")
        var chromeVersion = ""
        for (var i = 0; i < arr.length; i++) {
          if (/chrome/i.test(arr[i])) chromeVersion = arr[i]
        }

        try {
          if (chromeVersion) {
            chromeVersion = Number(chromeVersion.split("/")[1].split(".")[0])
            // chrome版本判断
            if (chromeVersion < 49) {
              window.location.href = window.location.origin + "/browser-compatibility-hint.html"
            }
          }
        } catch (error) {
          console.log(error)
        }
      }
    </script>
  </body>
</html>
