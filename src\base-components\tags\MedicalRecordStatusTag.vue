<template>
  <!-- <el-tag
    :type="statusInfo?.color ? '' : statusInfo?.tagType || 'info'"
    :color="statusInfo?.color"
    effect="dark"
    :hit="false"
    @click.stop="handleClick"
  >
    {{ statusInfo?.label || "未知" }}
  </el-tag> -->
  <!-- 病案状态  statusEnumName-->
  <span class="status-tag" :style="{ color: statusInfo?.color, marginRight: '4px' }">
    {{ statusInfo?.label || "未知" }}
  </span>
  <!-- 病案提交状态  submitStatusEnum-->
  <!-- <span v-if="props.submitStatus" class="status-tag" :style="{ color: submitStatusInfo?.color }">
    {{ submitStatusInfo?.label || "未知" }}
  </span> -->
</template>

<script setup lang="ts">
import { computed } from "vue"
import { medicalRecordSubmitStatusOptions } from "@/configs"
import { useGlobalOptionsStore } from "@/stores"
import type { MedicalRecordStatus } from "@/types"
const globalOptionsStore = useGlobalOptionsStore()

const props = defineProps<{
  status: MedicalRecordStatus
  submitStatus?: MedicalRecordStatus
}>()

const emits = defineEmits(["click"])

const handleClick = () => {
  emits("click")
}

const submitStatusInfo = computed(() =>
  medicalRecordSubmitStatusOptions.find(option => option.value === props.submitStatus)
)

const statusInfo = computed(() =>
  globalOptionsStore.medicalRecordStatusOptions.find(option => option.statusEnumName === props.status)
)
</script>

<style lang="less" scoped>
.status-tag {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}
</style>
