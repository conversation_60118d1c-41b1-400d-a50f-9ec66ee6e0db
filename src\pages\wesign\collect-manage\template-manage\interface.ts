import { operationAxios } from "@/interfaces/axios-instance"

interface ITemplateData {
  templateWsid?: string
  fileName: string
  file: File
}

/**
 * @method POST
 * @desc   模板-新增
 */
export function addTemplate(data: ITemplateData) {
  const { fileName, file } = data
  const formData = new FormData()
  formData.append("file", file)
  return operationAxios({
    method: "post",
    url: `/api/collect/templates?fileName=${fileName}`,
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  })
}

/**
 * @method PUT
 * @desc   模板-改变状态
 */
export function changeTemplateStatus(obj) {
  const { templateWsid, status } = obj
  return operationAxios({
    method: "put",
    url: `/api/collect/templates/${templateWsid}/change-status?status=${status}`
  })
}

/**
 * @method PUT
 * @desc   模板-重新上传
 */
export function updateTemplate(data: ITemplateData) {
  const { templateWsid, fileName, file } = data
  const formData = new FormData()
  formData.append("file", file)
  return operationAxios({
    method: "put",
    url: `/api/collect/templates/${templateWsid}?fileName=${fileName}`,
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  })
}

/**
 * @method DELETE
 * @desc   模板-删除
 */
export function deleteTemplate(wsids: Array<string>) {
  return operationAxios({
    method: "delete",
    url: `/api/collect/templates`,
    data: {
      wsids
    }
  })
}
