<template>
  <div style="position: relative; height: 100%" :style="{ marginRight: state.isCollapse ? '0px' : '0px' }">
    <div class="tree-data" :style="{ width: state.isCollapse ? '0px' : '264px' }">
      <div class="card">
        <div class="name">{{ props.baseInfo.patientName || "暂无" }}</div>
        <div v-if="props.baseInfo.mrNo || props.baseInfo.inHospitalDeptName" class="info">
          {{ props.baseInfo.mrNo || "暂无" }}
          {{ props.baseInfo.inHospitalDeptName || "暂无" }}
        </div>
        <div v-if="props.baseInfo.checkupId" class="info">体检编号：{{ props.baseInfo.checkupId }}</div>
        <div v-if="props.baseInfo.inpatientArea || props.baseInfo.specialties" class="info">
          <span style="padding-right: 10px">{{ props.baseInfo.inpatientArea || "" }}</span>
          <span>{{ props.baseInfo.specialties || "" }}</span>
        </div>
        <div v-if="props.baseInfo.outHospitalDatetime" class="info">
          出院日期：{{ formatDatetime(props.baseInfo.outHospitalDatetime) }}
        </div>
        <div v-if="props.baseInfo.inHospitalDatetime" class="info">
          入院日期：{{ formatDatetime(props.baseInfo.inHospitalDatetime) }}
        </div>
        <div v-if="props.baseInfo.checkupDate" class="info">体检日期：{{ formatDate(props.baseInfo.checkupDate) }}</div>
        <div class="style_circle"></div>
      </div>
      <div class="tree">
        <div v-if="props.showMrSelect" class="mrClass-wrapper">
          <el-select v-model="selectedMrClass" multiple clearable :collapse-tags="true" :collapse-tags-tooltip="true">
            <el-option v-for="item in mrClassList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <el-tabs v-model="activeName">
          <el-tab-pane v-for="pane in tabPanes" :key="pane?.name" :name="pane?.name" :label="pane?.label" />
        </el-tabs>

        <CommonTree
          v-if="$props.treeInfo"
          ref="commonTreeRef"
          class="tree-content"
          :data="state.renderDocumentTree"
          :filter-tree-node="filterTreeNode"
          :filter-keys="selectedMrClass"
          @click-node="handleNodeClick"
        >
          <template #title="{ node, data }">
            <span v-if="data.type === 'FILE'" style="margin-right: 4px">
              <i v-if="!data.isSupplement" class="ri-file-text-line electric-icon"></i>
              <i v-else class="ri-camera-line paper-icon"></i>
            </span>
            <OverflowTooltip
              :content="getNodeTooltipContent(node, data, false) + getCatalogPage(data)"
              max-width="85%"
            />
          </template>
        </CommonTree>
        <el-empty v-else :image="NoDataImage" description="暂无数据" :image-size="100"></el-empty>

        <!-- <div class="mrClass-wrapper">
          <el-checkbox-group v-model="selectedMrClass">
            <el-checkbox v-for="item in mrClassList" :key="item.value" :label="item.label" :value="item.value" />
          </el-checkbox-group>
        </div> -->
      </div>
    </div>
    <!-- 展开/收缩 -->
    <div
      v-if="props.canCollapse"
      class="collapse-icon"
      :style="{
        boxShadow: state.isCollapse ? '0px 0px 3px 1px rgba(0, 35, 114, 0.1)' : '3px 0px 3px 1px rgba(0, 35, 114, 0.1)',
        borderRadius: state.isCollapse ? '5px' : '0px 5px 5px 0px'
      }"
      @click="() => (state.isCollapse = !state.isCollapse)"
    >
      <el-icon v-if="!state.isCollapse"><ArrowLeft /></el-icon>
      <el-icon v-if="state.isCollapse"><ArrowRight /></el-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, watch, nextTick, onMounted } from "vue"
import { ArrowLeft, ArrowRight } from "@element-plus/icons-vue"
import { CommonTree, OverflowTooltip } from "@/base-components"
import NoDataImage from "@/assets/png/nodata.png"
import { getApproveReasonApi } from "@/interfaces"
import {
  Message,
  formatDatetime,
  formatRecordTree,
  getFileNodeList,
  getNodeTooltipContent,
  getCatalogPage,
  formatDate,
  sessionLessAxios
} from "@/utils"

interface PropsType {
  baseInfo: Record<string, any>
  treeInfo: { mrClassTree: any[]; noCatalogDocuments: any[] } | null
  canCollapse?: boolean
  showMrSelect?: boolean
}

const props = withDefaults(defineProps<PropsType>(), {
  canCollapse: false, // 是否可收缩
  showMrSelect: true // 是否显示mr分类选择
})

const emits = defineEmits(["clickPdf"])
const commonTreeRef = ref()

const state = reactive({
  baseInfo: {},
  mrClassTree: [],
  initPdfConfig: {}, //初始化pdf
  documentTree: [] as any[], //全部的文档树
  renderDocumentTree: [] as any[], //根据tab切换的文档树
  isCollapse: false,
  documentNodeList: [] as any[] // 文件节点一维数组
})

// 点击树的节点
const handleNodeClick = node => {
  // 如果node没有数据，并且tree没有数据，需要清空缺省pdf
  if (
    node?.type === "FILE" ||
    JSON.stringify(node) === "{}" ||
    ((node?.length === 0 || !node || !node?.length) && state.renderDocumentTree?.length === 0)
  ) {
    emits("clickPdf", node)
  }
}

// 监听父组件document tree改变，格式化tree
watch(
  () => props.treeInfo,
  treeInfo => {
    if (!treeInfo?.mrClassTree && !treeInfo?.noCatalogDocuments) return
    state.documentTree = formatRecordTree(treeInfo)
    state.renderDocumentTree = state.documentTree
    const pdfNodeList = getFileNodeList(state.documentTree)
    handleNodeClick(pdfNodeList[0]) // 设置初始pdf
    nextTick(() => setCurrentKey(pdfNodeList[0]?.wsid))
    state.documentNodeList = pdfNodeList
  },
  {
    immediate: true,
    deep: true
  }
)

/*==================文件类型tab切换==================*/
const activeName = ref("all")
const tabPanes = [
  {
    name: "all",
    label: "全部"
  },
  {
    name: "electronic",
    label: "电子"
  },
  {
    name: "paper",
    label: "纸质"
  }
]

watch(
  () => activeName.value,
  () => {
    try {
      if (activeName.value === "all") {
        state.renderDocumentTree = state.documentTree
      } else if (activeName.value === "electronic") {
        //深层查找，将state.documentTree中最里层里isSupplement为0的数据筛选出来
        const electronicTree = state.documentTree.filter(item => {
          const find = (data: any) => {
            //如果有isSupplement字段并且isSupplement为0
            if (data?.isSupplement === 0) return true
            if (data?.children) {
              return data?.children?.some(find)
            }
            return false
          }
          return find(item)
        })
        state.renderDocumentTree = electronicTree
      } else if (activeName.value === "paper") {
        //深层查找，将state.documentTree中最里层里isSupplement为1的数据筛选出来
        const paperTree = state.documentTree.filter(item => {
          const find = (data: any) => {
            if (data?.isSupplement === 1) return true
            if (data?.children) {
              return data.children.some(find)
            }
            return false
          }
          return find(item)
        })
        state.renderDocumentTree = paperTree
      }
      const pdfNodeList = getFileNodeList(state.renderDocumentTree)
      handleNodeClick(pdfNodeList?.[0] || {}) // 设置初始pdf
      nextTick(() => setCurrentKey(pdfNodeList?.[0]?.wsid || ""))
      state.documentNodeList = pdfNodeList
    } catch (error) {
      console.log(error)
      Message.error("数据异常")
    }
  }
)

/*==================文书类型筛选文件==================*/
const mrClassList = ref<Array<Record<string, any>>>([])
const selectedMrClass = ref<Array<Record<string, any>>>([])
onMounted(async () => {
  if (!localStorage.getItem("SessionWsid")) {
    const params = {
      method: "get",
      params: { key: "MR_CLASS_LABEL" },
      url: `/api/system/dictionary/group-list`
    }
    mrClassList.value = (await sessionLessAxios(params))?.data?.data?.map(item => {
      return {
        label: item.value,
        value: item.key
      }
    })
  } else {
    mrClassList.value = (await getApproveReasonApi({ groupKey: "MR_CLASS_LABEL" }))?.map(item => {
      return {
        label: item.value,
        value: item.key
      }
    })
  }
})

//选择文书分类时触发筛选树节点
const filterTreeNode = (value, data, node) => {
  if (value.length) {
    if (data.type === "FILE") {
      if (value.some(item => item === data.mrClassLabelKey)) {
        handleNodeClick(data || {}) // 设置初始pdf
        nextTick(() => setCurrentKey(data.wsid || ""))
      } else {
        // 无数据 对pdf赋值空
        handleNodeClick({})
      }
      return value.some(item => item === data.mrClassLabelKey)
    }
  } else {
    return true
  }
}

// 设置当前选中节点
const setCurrentKey = (wsid: string) => {
  commonTreeRef.value?.setCurrentKey(wsid)
}

defineExpose({ state, setCurrentKey })
</script>

<style lang="less" scoped>
.tree-data {
  overflow: hidden;
  height: 100%;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgb(0 35 114 / 10%);
  position: relative;
  transition: width 0.5s;

  .card {
    position: relative;
    display: flex;
    justify-content: space-between;
    height: 100px;
    padding: 15px;
    font-size: 12px;
    color: white;
    background: linear-gradient(to right, rgb(88 136 255), rgb(45 105 255));
    flex-direction: column;

    .name {
      font-weight: 700;
      font-style: normal;
      font-size: 14px;
      color: #fff;
    }

    .style_circle {
      position: absolute;
      right: -45px;
      bottom: -45px;
      width: 108px;
      height: 108px;
      background-color: rgb(255 255 255 / 10%);
      border-radius: 50%;
    }
  }

  .tree {
    width: calc(100% - 40px);
    height: calc(100% - 130px - 40px);
    padding: 20px 0 20px 15px;
    :deep(.el-tabs__header) {
      margin: 0 0 10px 0;
    }
    :deep(.el-tabs__nav) {
      margin-left: 30px;
    }
    .tree-content {
      // height: calc(100% - 50px);
      height: calc(100% - 82px);
      .electric-icon {
        color: #04c3a1;
        font-size: 16px;
      }
      .paper-icon {
        color: #409eff;
        font-size: 16px;
      }
    }
  }

  .el-empty {
    width: 100%;
    height: 100%;
    .el-empty__description {
      white-space: nowrap;
    }
  }
}

.collapse-icon {
  width: 24px;
  height: 48px;
  background: #fff;
  right: -24px;
  padding: 16px 4px;
  box-sizing: border-box;
  position: absolute;
  top: 0px;
  bottom: 0px;
  margin: auto;
  z-index: 999;
  cursor: pointer;
  &:hover {
    color: #999;
  }
}
</style>
