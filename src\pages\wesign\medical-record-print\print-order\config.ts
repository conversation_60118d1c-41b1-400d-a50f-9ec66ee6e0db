import type { TableColumnItem, BaseOptionItem } from "@/types"

export const menuId = "/print/order"

export const orderColumns: Array<TableColumnItem> = [
  { type: "selection", width: 45, fixed: "left" },
  { prop: "orderCode", label: "订单单号", minWidth: 200, must: true },
  { prop: "printCount", label: "订单打印次数", minWidth: 200, must: true },
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 120, must: true },
  { prop: "patientIdCard", label: "身份证号", minWidth: 200 },
  { prop: "patientOutHospitalDatetime", label: "出院日期", minWidth: 180, sortable: true },
  { prop: "printTypeName", label: "打印类型", minWidth: 100 },
  { prop: "duplicateNum", label: "复印份数", minWidth: 120, sortable: true },
  { prop: "pageCount", label: "总页数", minWidth: 100, sortable: true },
  { prop: "orderAmount", label: "订单金额", minWidth: 120, sortable: true, must: true },
  { prop: "printInvoice", label: "是否开票", minWidth: 100 },
  { prop: "createdDatetime", label: "申请时间", minWidth: 180, sortable: true },
  { prop: "payDatetime", label: "支付时间", minWidth: 180, sortable: true },
  { prop: "status", label: "订单状态", minWidth: 100, must: true },
  { prop: "applySource", label: "申请来源", minWidth: 100 },
  { prop: "receiveType", label: "领取方式", minWidth: 100 },
  { prop: "mailName", label: "收件人", minWidth: 100, must: true },
  { prop: "mailPhone", label: "收件人手机", minWidth: 120 },
  { prop: "mailAddress", label: "邮寄地址", minWidth: 200 },
  { prop: "deliverGoodsDatetime", label: "发货时间", minWidth: 180, sortable: true },
  { prop: "receiveDatetime", label: "领取时间", minWidth: 180, sortable: true },
  { prop: "operation", label: "操作", width: 100, fixed: "right", must: true }
]

export const orderStatusOptions: Array<BaseOptionItem> = [
  { label: "待支付", value: "WAIT_PAYMENT" },
  { label: "已取消", value: "CANCELLATION_ORDER" },
  { label: "已退款", value: "REFUND_PAYMENT" },
  { label: "待打印", value: "WAIT_PRINT" },
  { label: "待邮寄", value: "WAITE_MAIL" },
  { label: "待取件", value: "WAITE_TAKE" },
  { label: "已完成", value: "COMPLETE" },
  // { label: "待审批", value: "APPLICATION" },
  // { label: "审批拒绝", value: "APPROVE_REFUSE" },
  { label: "退款申请中", value: "REFUND_APPLYING" },
  { label: "逾期未领取", value: "OVERDUE_CLAIM" }
]
