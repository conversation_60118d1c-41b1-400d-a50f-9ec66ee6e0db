import { Ref } from "vue"
import { CommonTable } from "@/base-components"
import { Message, toastError } from "@/utils"

interface ITableOperationApiGroup<T> {
  addApi: (data: T) => Promise<any>
  editApi: (data: T) => Promise<any>
  toggleApi: (wsid: string, nextStatus: "ENABLE" | "DISABLE") => Promise<any>
  deleteApi: (wsid: string) => Promise<any>
}

const useTableOperation = <T extends Record<string, any>>(
  formState: T,
  formVisible: Ref<boolean>,
  apiGroup: ITableOperationApiGroup<T>,
  tableRef: Ref<InstanceType<typeof CommonTable> | undefined>,
  actionLoading?: Ref<boolean>
) => {
  const { addApi, editApi, toggleApi, deleteApi } = apiGroup

  function confirmAdd() {
    if (actionLoading) actionLoading.value = true
    return addApi(formState)
      .then(() => {
        Message.success("添加成功")
        formVisible.value = false
        tableRef.value?.refreshTableData()
      })
      .catch(err => toastError(err, "添加失败"))
      .finally(() => {
        if (actionLoading) actionLoading.value = false
      })
  }

  function confirmEdit() {
    if (actionLoading) actionLoading.value = true
    return editApi(formState)
      .then(() => {
        Message.success("修改成功")
        formVisible.value = false
        tableRef.value?.refreshTableData()
      })
      .catch(err => toastError(err, "修改失败"))
      .finally(() => {
        if (actionLoading) actionLoading.value = false
      })
  }

  function confirmToggle(wsid: string, nextStatus: "ENABLE" | "DISABLE") {
    return toggleApi(wsid, nextStatus)
      .then(() => {
        Message.success("修改状态成功")
        tableRef.value?.refreshTableData()
      })
      .catch(err => toastError(err, "修改状态失败"))
  }

  function confirmDelete(wsid: string) {
    return deleteApi(wsid)
      .then(() => {
        Message.success("删除成功")
        tableRef.value?.refreshTableData()
      })
      .catch(err => toastError(err, "删除失败"))
  }

  return {
    confirmAdd,
    confirmEdit,
    confirmToggle,
    confirmDelete
  }
}

export default useTableOperation
