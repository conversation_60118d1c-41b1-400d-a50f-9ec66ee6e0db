<template>
  <div v-if="props.showTable" class="quality-table-container">
    <div class="base-table-header flex-between">
      <el-button :icon="Download" @click="handleExportQualityTable">导出</el-button>
      <div class="table-chart-tool">
        <el-tooltip content="表格展示">
          <i v-if="props.showTable"><img src="@/assets/svg/statistical-statement/table-active.svg" alt="" /></i>
          <i v-else>
            <img src="@/assets/svg/statistical-statement/table.svg" alt="" />
          </i>
        </el-tooltip>
        <el-tooltip content="图表展示">
          <i v-if="props.showTable" @click="emits('changeTab', false)">
            <img src="@/assets/svg/statistical-statement/chart.svg" alt="" />
          </i>
          <i v-else><img src="@/assets/svg/statistical-statement/chart-active.svg" alt="" /></i>
        </el-tooltip>
      </div>
    </div>
    <el-table
      class="base-table"
      show-summary
      :header-cell-style="{ background: 'rgb(248,249,252)', color: '#030814' }"
      :data="props.dataSource"
      border
      :summary-method="getSummaries"
    >
      <template v-for="item in medicalQualityReportColumn" :key="item.prop">
        <el-table-column
          :prop="item.prop"
          :label="item.label"
          :sortable="item.sortable"
          :width="item.width"
          :min-width="item.minWidth"
          :fixed="item.fixed"
          :show-overflow-tooltip="true"
        >
          <template #default="{ row, $index }">
            <slot :name="item.prop" :row="row" :index="$index">
              <span v-if="percentageDimensions.find(dimension => dimension === item.prop)">
                {{ item.prop ? `${row[item.prop]}%` : null }}
              </span>
              <span v-else>{{ item.prop ? row[item.prop] : null }}</span>
            </slot>
          </template>
        </el-table-column>
      </template>
      <template #empty>
        <EmptyContent />
      </template>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import ExcelJS from "exceljs"
import { saveAs } from "file-saver"
import { sumBy, map } from "lodash/fp"
import { Download } from "@element-plus/icons-vue"
import { EmptyContent } from "@/base-components"
import { Message } from "@/utils"
import { medicalQualityReportColumn } from "../config"

const props = defineProps({
  showTable: { type: Boolean, default: true },
  dataSource: { type: Array, default: () => [] },
  averageScore: { type: Number, default: 0 }
})

const emits = defineEmits(["changeTab"])

const percentageDimensions = ["AMedicalPercentage", "BMedicalPercentage", "CMedicalPercentage"]

const medicalMap = {
  AMedicalPercentage: "AMedical",
  BMedicalPercentage: "BMedical",
  CMedicalPercentage: "CMedical"
}
const getSummaries = () => {
  const summaries: (string | any)[] = []

  medicalQualityReportColumn.forEach((column: Record<string, any>, index) => {
    if (index === 0) {
      summaries[index] = "合计"
      return
    }
    if (index === 2) {
      summaries[index] = props.averageScore
      return
    }
    const values = map(column.prop)(props.dataSource)
    const totalMedicals = sumBy("medicalCount")(props.dataSource)
    if (percentageDimensions.find(dimension => dimension === column.prop)) {
      const totalMedicalsByProperty = sumBy(medicalMap[column?.prop])(props.dataSource)
      summaries[index] = `${Number(((totalMedicalsByProperty / totalMedicals) * 100).toFixed(2))}%`
      return
    }
    if (!values.every(value => Number.isNaN(value))) {
      summaries[index] = `${values.reduce((prev: any, curr) => {
        const value = Number(curr)
        if (!Number.isNaN(value)) {
          return prev + curr
        } else {
          return prev
        }
      }, 0)}`
    } else {
      summaries[index] = null
    }
  })

  return summaries
}
// 导出科室病案质量表格
const handleExportQualityTable = async () => {
  const workbook = new ExcelJS.Workbook()
  const worksheet = workbook.addWorksheet("Sheet1")

  // 添加数据到工作表
  const headers = medicalQualityReportColumn.map(item => item.label)
  // 添加表头
  worksheet.addRow(headers)

  // 添加数据
  const data = props.dataSource.map(item => {
    return medicalQualityReportColumn.map((column: Record<string, any>) => {
      return item?.[column.prop]
    })
  })
  data.forEach(row => {
    worksheet.addRow(row)
  })

  // 添加合计行
  const summaries = getSummaries()
  worksheet.addRow(summaries)

  // 生成并保存 ExcelJS 文件
  const buffer = await workbook.xlsx.writeBuffer()
  saveAs(new Blob([buffer]), "科室病案质量.xlsx")
}
</script>

<style lang="less" scoped>
.quality-table-container {
  border: 1px solid #d4d4d4;
  border-radius: 4px;
  padding: 20px;
  box-sizing: border-box;
  height: 400px;
  overflow: auto;
  width: 100%;
  display: flex;
  flex-direction: column;
  .table-chart-tool {
    .table-setting-btn {
      margin-right: 8px;
      padding: 4px 8px;
      cursor: pointer;
    }

    i {
      cursor: pointer;
      img {
        vertical-align: bottom;
      }
    }
  }
  .base-table {
    height: calc(100% - 40px);
  }

  .base-table-header {
    margin-bottom: 8px;
  }
}
</style>
