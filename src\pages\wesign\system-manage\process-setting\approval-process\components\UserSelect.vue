<template>
  <el-select
    v-model="members"
    value-key="wsid"
    :multiple="props.multiple"
    clearable
    collapse-tags
    collapse-tags-tooltip
    style="width: 400px"
    placeholder="请选择"
    :max-collapse-tags="3"
  >
    <template #header>
      <el-input v-model="userState.searchValue" placeholder="成员姓名/工号" :prefix-icon="Search" clearable></el-input>
    </template>
    <div v-infinite-scroll="scrollRenderUsers">
      <div v-for="item in userState.renderUsersData" :key="item.wsid">
        <el-option v-show="item.visible" :label="item.name" :value="item.wsid" />
      </div>
    </div>
  </el-select>
</template>

<script setup lang="ts">
import { ref, watch, reactive, onMounted, computed } from "vue"
import { Search } from "@element-plus/icons-vue"
import { getFuzzyUsersInDeptsApi } from "@/interfaces"

interface UserProps {
  members: Array<any> | string
  multiple?: boolean
}
const props = withDefaults(defineProps<UserProps>(), {
  multiple: true
})
const emits = defineEmits(["update:members"])
const members = computed({
  get: () => props.members,
  set: (val: any) => {
    emits("update:members", val)
  }
})

/*=============================用户搜索=============================*/

const userState = reactive({
  searchValue: "",
  // 所有用户
  usersDataSource: [] as Array<any>,
  // 渲染的数据
  renderUsersData: [] as Array<any>,
  // 通过搜索过滤的数据
  filterUsersData: [] as Array<any>
})

onMounted(() => {
  getFuzzyUsersInDeptsApi({
    deptWsid: "",
    item: userState.searchValue
  })
    .then(res => {
      const users = res.data?.map(item => ({
        ...item,
        wsid: item.userWsid,
        name: item.realName
      }))
      userState.usersDataSource = users
    })
    .catch(err => {
      userState.usersDataSource = []
    })
})
// 滚动的时候渲染数据
const scrollRenderUsers = () => {
  const SCROLL_SIZE = 20
  const renderUserCount = userState.renderUsersData.length
  // 判断renderUsersData的长度是否大于等于filterUsersData的长度，如果大于等于的话，不再渲染,否则每次渲染20条数据
  if (renderUserCount >= userState.filterUsersData.length) return
  let nextRenderUsersData = userState.filterUsersData.slice(0, renderUserCount + SCROLL_SIZE).map(item => ({
    wsid: item.wsid,
    name: item.realName,
    visible: true
  }))

  // 查找当前已选中的用户是否在列表中，如果不在则追加到最前面
  if (props.multiple && Array.isArray(members.value)) {
    members.value.forEach((member: any) => {
      if (!nextRenderUsersData.find((user: any) => user.wsid === member.wsid)) {
        nextRenderUsersData.unshift({ ...member, visible: false })
      }
    })
  } else {
    if (!nextRenderUsersData.find((user: any) => user.wsid === props.members) && props.members) {
      const member = userState.usersDataSource.find(item => item.wsid === props.members)
      nextRenderUsersData.unshift({ ...member, visible: false })
    }
  }
  userState.renderUsersData = nextRenderUsersData
}

watch(
  () => [userState.searchValue, userState.usersDataSource],
  val => {
    userState.filterUsersData = userState.usersDataSource.filter(
      item => item.jobId.includes(userState.searchValue) || item.realName.includes(userState.searchValue)
    )
    userState.renderUsersData = []
    scrollRenderUsers()
  },
  {
    immediate: true
  }
)

defineExpose({ userState })
</script>
