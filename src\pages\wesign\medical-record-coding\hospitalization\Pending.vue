<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
        />

        <el-form-item label="出院科室">
          <el-select v-model="searchFormState.outHospitalDeptWsid" style="width: 200px">
            <el-option
              v-for="item in globalOptionsStore.departmentOptions"
              :key="item.value as string"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <DaterangeFormItem v-model:model-value="searchFormState.outHospitalDatetime" label="出院时间" />

        <el-form-item label="主治医师">
          <el-input v-model="searchFormState.attendingDoctor" style="width: 240px" />
        </el-form-item>

        <PatientLabelFormItem v-model="searchFormState.patientLabel" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="waitingCatalogTableRef"
        table-id="waitingCatalogTableIdent"
        :table-columns="waitingCatalogControlColumns"
        :request-api="getCatalogLists"
        :request-params="waitingCatalogParams"
        :data-callback="dataCallback"
      >
        <!-- 患者姓名 -->
        <template #patientName="{ row }">
          <div style="display: flex; align-items: center; flex-wrap: wrap">
            <span style="margin: 0 16px">{{ row.patientName }}</span>
            <PatientLabelTag :row="row" />
          </div>
        </template>
        <template #patientPrintCount="{ row }">
          {{ row.patientPrintCount > 0 ? "已打印" : "未打印" }}
        </template>
        <template #operation="{ row }">
          <TableButton @click="catalogItem(row)">编目</TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref, computed } from "vue"
import { useRouter } from "vue-router"
import {
  SearchContainer,
  PatientFilterFormItem,
  CommonTable,
  TableButton,
  DaterangeFormItem,
  PageContainer,
  PatientLabelTag,
  PatientLabelFormItem
} from "@/base-components"
import { TabsRouter } from "@/page-components"
import { getCatalogLists } from "../interface"
import { waitingCatalogControlColumns, tabsRouterList } from "./config"
import { useTableSearch } from "@/hooks"
import { useGlobalOptionsStore } from "@/stores"
import { formatDatetime } from "@/utils"

const globalOptionsStore = useGlobalOptionsStore()

/* ======================== 搜索相关数据及方法 ======================== */
const searchFormState = reactive({
  patientFilterValue: "",
  patientFilterProp: "patientName",
  outHospitalDeptWsid: "",
  outHospitalDatetime: "",
  attendingDoctor: "",
  patientLabel: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

const waitingCatalogParams = computed(() => {
  return { ...searchParams, queryType: "wait_catalog" }
})

/* ======================== 表格相关方法 ======================== */

function dataCallback(data: Array<Record<string, any>>) {
  return data.map(item => ({
    ...item,
    inHospitalDatetime: formatDatetime(item.inHospitalDatetime),
    outHospitalDatetime: formatDatetime(item.outHospitalDatetime)
  }))
}

const waitingCatalogTableRef = ref()

const router = useRouter()

// 编目
const catalogItem = row => {
  router.push({
    path: "/catalog-preview",
    query: {
      appScenario: "HOME_PAGE_ENCODE",
      businessDataWsid: row.inpNo,
      returnUrl: "/coding/hospitalization/pending",
      actionType: "edit"
    }
  })
}

// 查看
const toDetail = row => {
  router.push({
    path: "/coding/hospitalization/detail",
    query: {
      inpNo: row.inpNo,
      type: "record"
    }
  })
}
</script>
