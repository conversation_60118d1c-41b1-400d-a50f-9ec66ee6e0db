<template>
  <div class="discharge-data">
    <div class="discharge-data-title"></div>
    <div class="discharge-data-status">
      <div class="yesterday-discharge-data data-status">
        <img src="@/assets/svg/monitor/Group-803.svg" />
        <span>昨日出院数据</span>
        <span class="actual-data">{{ dischargeNum }}</span>
      </div>
      <div class="complete-acquisition data-status">
        <img src="@/assets/svg/monitor/Group-804.svg" />
        <span>今日完成采集</span>
        <span class="actual-data">{{ collectDone }}</span>
      </div>
    </div>
    <div class="discharge-data-table">
      <v-chart class="broke-line-charts" :option="option" autoresize></v-chart>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, provide, watch } from "vue"
import { LineChart } from "echarts/charts"
import { TitleComponent, TooltipComponent, LegendComponent, GridComponent } from "echarts/components"
import { use, graphic } from "echarts/core"
import { UniversalTransition } from "echarts/features"
import { CanvasRenderer } from "echarts/renderers"
import VChart, { THEME_KEY } from "vue-echarts"
import { BusinessStatistics, BusinessStatisticsEnum } from "../config"

/* ===================== 数据处理操作 ===================== */
interface DischargeDataProp {
  dischargeData: Array<BusinessStatistics>
}
const props = defineProps<DischargeDataProp>()

watch(
  () => props.dischargeData,
  () => {
    formatData(props.dischargeData)
  }
)

// x轴数据
const xAxisData = ref([] as any)
// 采集病案数
const collectNumData = ref([] as any)
// 出院人数
const dischargeNumData = ref([] as any)
// 归档数
const archiveNumData = ref([] as any)
// 昨日出院人数
const dischargeNum = ref("")
// 今日完成采集数
const collectDone = ref("")

function formatData(dischargeData) {
  let tempCollectNumData: Array<number> = []
  let tempDischargeNumData: Array<number> = []
  let tempArchiveNumData: Array<number> = []
  let tempXAxisData: Array<any> = []
  dischargeData.map(dischargeItem => {
    if (dischargeItem.businessTypeStr === BusinessStatisticsEnum.COLLECT) {
      dischargeItem.items.forEach(item => {
        tempCollectNumData.push(item.count)
      })
    } else if (dischargeItem.businessTypeStr === BusinessStatisticsEnum.OUT_HOSPITAL) {
      dischargeItem.items.forEach(item => {
        tempDischargeNumData.push(item.count)
      })
    } else if (dischargeItem.businessTypeStr === BusinessStatisticsEnum.ARCHIVE) {
      dischargeItem.items.forEach(item => {
        tempArchiveNumData.push(item.count)
      })
    }
    if (dischargeItem.items && tempXAxisData.length === 0) {
      dischargeItem.items.forEach(item => {
        tempXAxisData.push(item.dateStr)
      })
    }
  })
  collectNumData.value = tempCollectNumData
  dischargeNumData.value = tempDischargeNumData
  archiveNumData.value = tempArchiveNumData
  xAxisData.value = tempXAxisData
  dischargeNum.value = dischargeNumData.value[dischargeNumData.value.length - 2] || 0
  collectDone.value = collectNumData.value[collectNumData.value.length - 1] || 0
}

/* ===================== 表格配置项 ===================== */
use([CanvasRenderer, LineChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent, UniversalTransition])

provide(THEME_KEY, "dark")

const option = ref({
  tooltip: {
    trigger: "axis"
  },
  legend: {
    orient: "horizontal",
    left: "right",
    data: ["出院人数", "归档数", "采集病案数"]
  },
  xAxis: {
    type: "category",
    boundaryGap: false,
    data: xAxisData
  },
  yAxis: {
    type: "value",
    name: "数量/个"
  },
  series: [
    {
      name: "出院人数",
      type: "line",
      smooth: true,
      showSymbol: true,
      symbolSize: 8,
      data: dischargeNumData,
      areaStyle: {
        color: new graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: "rgba(16,108,237,0.52)"
          },
          {
            offset: 1,
            color: "rgba(16,108,237,0)"
          }
        ])
      }
    },
    {
      name: "归档数",
      type: "line",
      smooth: true,
      showSymbol: true,
      symbolSize: 8,
      data: archiveNumData,
      areaStyle: {
        color: new graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: "rgba(55,206,86,0.4)"
          },
          {
            offset: 1,
            color: "rgba(55,206,86,0)"
          }
        ])
      }
    },
    {
      name: "采集病案数",
      type: "line",
      smooth: true,
      showSymbol: true,
      symbolSize: 8,
      data: collectNumData,
      areaStyle: {
        color: new graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: "rgba(242,201,56,0.71)"
          },
          {
            offset: 1,
            color: "rgba(242,201,56,0)"
          }
        ])
      }
    }
  ]
})
</script>

<style lang="less" scoped>
.discharge-data {
  min-width: 628px;
  width: 38.6%;
  margin-left: 20px;
  .discharge-data-title {
    width: 100%;
    height: 43px;
    background: url("@/assets/svg/monitor/discharge-data.svg") no-repeat;
    background-size: 100%;
  }
  .discharge-data-status {
    width: 100%;
    margin-top: 15px;
    display: flex;
    .data-status {
      width: 48%;
      height: 74px;
      background: linear-gradient(180deg, #061742 0%, rgba(6, 23, 66, 0) 100%);
      display: flex;
      img {
        margin-top: 10px;
        width: 80px;
        height: 44px;
      }
      span {
        padding-top: 16px;
        font-size: 16px;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.6);
      }
      .actual-data {
        font-size: 40px;
        font-weight: bold;
        color: #fff;
        margin-top: -18px;
        margin-left: 10px;
      }
    }
    .yesterday-discharge-data {
      margin-right: 32px;
    }
  }
  .discharge-data-table {
    .broke-line-charts {
      width: 100%;
      height: 300px;
    }
  }
}
</style>
