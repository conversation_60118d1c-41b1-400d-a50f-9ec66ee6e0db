<template>
  <PageContainer>
    <TabsRouter :tabs-config="tabsRouterList" />
    <!-- 全局设置 -->
    <div v-loading="uploadLoading" class="global-config-container">
      <div v-for="(config, index) in globalConfigs" :key="index" class="custom-item">
        <span class="custom-item-label">
          <span
            :style="{
              color: 'red'
            }"
          >
            *
          </span>
          {{ config?.formLabel }}:
        </span>
        <!-- 简单单选，无附加项 -->
        <template v-if="config.formType === 'RADIO'">
          <el-radio-group v-model="config.value">
            <el-radio v-for="item in JSON.parse(config.formOptions)" :key="item.value" :label="item.value">
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </template>

        <!-- 单选，有附加项 -->
        <template v-if="config.formType === 'RADIO_PLUS'">
          <el-radio-group v-model="config.value">
            <el-radio v-for="item in JSON.parse(config.formOptions)" :key="item.value" :label="item.value">
              {{ item.label }}
            </el-radio>
          </el-radio-group>
          <div v-if="getRadioPlusOptions(config.value, config?.extraConfig) > -1" class="extra-config">
            <!-- <div class="custom-item-label extra-label">
                  {{ JSON.parse(config.extraConfig)?.[getRadioPlusOptions(config.value, config?.extraConfig)]?.label }}
                </div> -->
            <div
              v-if="
                JSON.parse(config.extraConfig)?.[getRadioPlusOptions(config.value, config?.extraConfig)]?.formType ===
                'TEXTAREA'
              "
            >
              <el-link type="primary" :icon="Edit" @click.stop="openCodeDialog(config)">点击输入代码块</el-link>
            </div>

            <div
              v-if="
                JSON.parse(config.extraConfig)?.[getRadioPlusOptions(config.value, config?.extraConfig)]?.formType ===
                'UPLOAD_FILE'
              "
              class="upload-file"
            >
              <div v-if="/WSID_FILE/.test(config.extraValue)" class="uploaded-content">
                <OverflowTooltip :content="config?.extraName" max-width="70%" />
                <span @click="handleUploadFile(config, 'update')">重新上传</span>
              </div>
              <div v-else class="upload-desc" @click="handleUploadFile(config)">
                <i class="ri-upload-cloud-line"></i>
                <span>上传</span>
              </div>
            </div>
          </div>
        </template>

        <!-- 上传图片 -->
        <template v-if="config.formType === 'IMAGE'">
          <div class="upload-img">
            <div class="upload-tips">支持类型PNG，尺寸200*200</div>
            <div class="img-container" @click="handleUploadImage(config)">
              <img v-if="config?.imageSrc" class="seal-img" :src="config?.imageSrc" />
              <div v-else class="img-container-desc">
                <i class="ri-add-line add-btn"></i>
                <span>上传图片</span>
              </div>
            </div>
          </div>
        </template>

        <!-- 输入 -->
        <template v-if="config.formType === 'TEXT'">
          <el-input v-model="config.value" clearable :placeholder="`请输入${config?.formLabel}`" style="width: 70%" />
        </template>

        <!--  医保编码转换 -->
        <template v-if="config.formType === 'CODE_SOURCE'">
          <el-link type="primary" :icon="Edit" @click.stop="openCodeDialog(config)">点击输入代码块</el-link>
        </template>

        <div
          v-if="config?.errorMsg"
          :style="{
            color: 'red',
            fontSize: '12px',
            marginTop: '5px'
          }"
        >
          * {{ config?.errorMsg }}
        </div>
      </div>

      <el-button
        type="primary"
        :style="{
          marginLeft: '130px'
        }"
        @click="handleSaveGlobalConfig"
      >
        保存
      </el-button>
    </div>
  </PageContainer>

  <DialogContainer
    v-model:visible="openCodeDialogVisible"
    title="输入代码"
    :width="1000"
    :confirm-callback="confirmSetCode"
    @click.stop
  >
    <div class="code-container">
      <Codemirror
        v-model="extraValue"
        placeholder="请输入代码块"
        :tab-size="10"
        :extensions="extensions"
        :style="{ height: '100%', width: '100%' }"
      />
    </div>
  </DialogContainer>
</template>

<script setup lang="ts">
import { onMounted, ref, reactive } from "vue"
import { java } from "@codemirror/lang-java"
import { Codemirror } from "vue-codemirror"
import { Edit } from "@element-plus/icons-vue"
import { PageContainer, OverflowTooltip, DialogContainer } from "@/base-components"
import { TabsRouter } from "@/page-components"
import { uploadSystemFile } from "@/interfaces"
import { Message, toastError, checkBusinessProcessAndGlobalConfig, myTheme, selectFile, blobToBase64 } from "@/utils"
import { tabsRouterList } from "./config"
import { updateGlobalProcessConfigApi, getGlobalProcessConfigApi } from "./interface"

/*=======================================上传=======================================*/

const uploadLoading = ref(false)
//上传文件
async function handleUploadFile(config: Record<string, any>, uploadStatus?: string) {
  const type = ".jar,.zip"
  await selectFile(type, 1024 * 1024 * 30).then(async file => {
    uploadLoading.value = true

    await uploadSystemFile({ file: file })
      .then(res => {
        config.extraValue = res.data.data.fileWsid
        config.extraName = res.data.data.name
        Message.success("上传成功")
      })
      .catch(error => {
        toastError(error, "上传失败")
      })
      .finally(() => {
        uploadLoading.value = false
      })
  })
}

// 上传图片
async function handleUploadImage(config: Record<string, any>) {
  const type = ".png"
  await selectFile(type, 1024 * 1024 * 30).then(async file => {
    const reader = new FileReader()
    reader.readAsDataURL(file as Blob)
    reader.onload = function (e) {
      const img = new Image()
      img.src = e.target?.result as string
      img.onload = async function () {
        config.imageSrc = (await blobToBase64(file as Blob)) as string
        uploadLoading.value = true

        await uploadSystemFile({ file: file })
          .then(res => {
            config.value = res.data.data.fileWsid
            Message.success("上传图片成功")
          })
          .catch(error => {
            toastError(error, "上传图片失败")
          })
          .finally(() => {
            uploadLoading.value = false
          })
      }
    }
  })
}

/*==================================全局配置==================================*/
const globalConfigs = ref<Array<Record<string, any>>>([])

// 获取全局配置
const getGlobalConfig = () => {
  getGlobalProcessConfigApi().then(res => {
    const baseData = res.data.data
    globalConfigs.value = baseData.map(config => {
      if (config.formType === "RADIO_PLUS") {
        let extraMetadata = config.extraMetadata
        let extraName = ""
        if (extraMetadata) {
          extraMetadata = JSON.parse(extraMetadata)
        }
        if (config?.extraValue && config.extraValue?.includes("WSID_FILE")) {
          extraName = extraMetadata?.extraFileName || extraMetadata?.name || ""
        }
        return {
          ...config,
          extraName: extraName
        }
      }
      return config
    })
  })
}

onMounted(() => {
  getGlobalConfig()
})

// 保存全局配置
const handleSaveGlobalConfig = async () => {
  const { errorInfo: globalErrorInfo } = checkBusinessProcessAndGlobalConfig(globalConfigs.value, "全局配置")

  // 将所有的错误提示拼接然后弹出
  let MessageTexts = ""
  if (globalErrorInfo && Object.keys(globalErrorInfo).length > 0) {
    MessageTexts += "全局配置："
    for (let key in globalErrorInfo) {
      if (globalErrorInfo[key] instanceof Object) {
        for (let k in globalErrorInfo[key]) {
          MessageTexts += `${k}${globalErrorInfo[key][k]}.`
        }
      } else {
        MessageTexts += `${globalErrorInfo[key]}.`
      }
    }
    if (MessageTexts) {
      Message.error(MessageTexts)
      return
    }
  }
  const data = globalConfigs.value.map(config => {
    if (config.formType === "RADIO_PLUS" && config.extraValue?.includes("WSID_FILE")) {
      const extraMetadata = {
        extraFileName: config.extraName
      }
      return {
        key: config.key,
        value: config.value,
        extraMetadata: JSON.stringify(extraMetadata),
        extraValue: config.extraValue
      }
    }
    return {
      key: config.key,
      value: config.value,
      ...(config.extraValue?.length > 0
        ? {
            extraValue: config.extraValue
          }
        : {})
    }
  })

  await updateGlobalProcessConfigApi(data)
    .then(() => {
      Message.success("保存成功")
    })
    .catch(err => {
      toastError(err, "保存失败")
    })
}

//有额外配置的单选，通过选中的值，获取额外配置项
const getRadioPlusOptions = (value: string, extraConfig: string) => {
  if (!extraConfig) return
  const realExtraConfig = JSON.parse(extraConfig)
  const index = realExtraConfig?.findIndex((item: Record<string, any>) => item.radioValue === value)
  return index
}

/*=======================================代码输入=======================================*/

const openCodeDialogVisible = ref(false)
const extraValue = ref("")

const currentConfigKey = ref()

// 打开代码块输入
const openCodeDialog = config => {
  currentConfigKey.value = config.key
  openCodeDialogVisible.value = true
  extraValue.value = config?.extraValue
}

// 保存代码
const confirmSetCode = () => {
  if (globalConfigs.value.find((item: Record<string, any>) => item.key === currentConfigKey.value)) {
    globalConfigs.value.find((item: Record<string, any>) => item.key === currentConfigKey.value).extraValue =
      extraValue.value
  }
  openCodeDialogVisible.value = false
}

const extensions = [java(), myTheme]
</script>

<style lang="less" scoped>
.global-config-container {
  width: 100%;
  height: calc(100% - 34px);
  overflow: auto;
  padding: 20px;
  box-sizing: border-box;

  .custom-item {
    margin-bottom: 24px;
    .custom-item-label {
      font-size: 14px;
      padding-right: 20px;
      color: var(--el-text-color-regular);
      display: inline-block;
      width: 110px;
      text-align: right;
    }
    .extra-label {
      padding-bottom: 0;
    }
    .upload-desc {
      font-size: 12px;
      cursor: pointer;
      i {
        font-size: 14px;
        color: var(--el-color-primary);
      }
      span:hover {
        color: var(--el-color-primary);
      }
    }
    .extra-config {
      width: 70%;
      margin-left: 130px;
    }
  }
  .upload-img {
    :deep(.el-form-item__content) {
      flex-direction: column;
      align-items: start;
    }
    .upload-tips {
      color: #999;
    }
    .img-container {
      border: 1px dashed var(--el-border-color);
      padding: 5px;
      height: fit-content;
      line-height: 1;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 100px;
      min-height: 100px;
      box-sizing: border-box;

      .img-container-desc {
        display: flex;
        flex-direction: column;
        align-items: center;
        row-gap: 5px;
        color: #999;
        width: 200px;
      }

      .seal-img {
        width: 200px;
        height: 200px;
        object-fit: contain;
      }

      .add-btn {
        width: 24px;
        height: 24px;
        font-size: 24px;
      }
    }
  }
  .upload-file {
    background-color: #eff3f7;
    padding: 10px;
    border-radius: 4px;
    border: 1px dashed #409eff;
    width: 80%;
    .uploaded-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .uploaded-text {
        display: inline-block;
        width: 70%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      span {
        cursor: pointer;
        &:hover {
          color: var(--el-color-primary);
        }
      }
    }
  }
}

:deep(.cm-editor) {
  border: 1px solid #dcdfe6;
}

:deep(.cm-focused) {
  outline: none !important;
}

:deep(.cm-lineNumbers) {
  border-right: 1px solid #dcdfe6;
}

.code-container {
  height: 75vh;
}
</style>
