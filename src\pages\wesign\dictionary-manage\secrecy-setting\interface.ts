import axios from "@/interfaces/axios-instance"

interface ISecreciesData {
  secrecyValue: string
  secrecyName: string
  describe?: string
  secrecyId?: string
}

/**
 * @method POST
 * @desc   保密管理-新增
 */
export function addSecrecies(data: ISecreciesData) {
  return axios({
    method: "post",
    url: `/api/document/secrecies`,
    data
  })
}

/**
 * @method PUT
 * @desc   保密管理-更新
 */
export function updateSecrecies(data: ISecreciesData) {
  return axios({
    method: "put",
    url: `/api/document/secrecies/${data.secrecyId}`,
    data
  })
}

/**
 * @method DELETE
 * @desc   保密管理-删除
 */
export function deleteSecrecies(secrecyId: string) {
  return axios({
    method: "delete",
    url: `/api/document/secrecies/${secrecyId}`
  })
}
