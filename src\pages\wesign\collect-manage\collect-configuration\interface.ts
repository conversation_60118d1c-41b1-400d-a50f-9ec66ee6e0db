import axios, { operationAxios } from "@/interfaces/axios-instance"

/* ======================== 厂商系统配置 ======================== */

export interface ISystemProviderFormData {
  wsid: string
  provider: string
  systemName: string
}

/**
 * @method GET
 * @desc   采集配置-系统厂商分页列表
 */
export function getSystemList(params: IPaginationRequestParams) {
  return axios.request<IPaginationResponseData>({
    method: "get",
    url: `/api/collect/systems`,
    params
  })
}

/**
 * @method GET
 * @desc   采集配置-获取所有系统厂商
 */
export function getAllSystemVendor() {
  return axios
    .request<IPaginationResponseData>({
      method: "get",
      url: `/api/collect/systems`,
      params: {
        limit: 1000,
        offset: 0
      }
    })
    .then(res => res.data?.data?.rows ?? [])
}

/**
 * @method POST
 * @desc   采集配置-新增系统厂商
 */
export function addSystems(data: ISystemProviderFormData) {
  const { provider, systemName } = data
  return axios({
    method: "post",
    url: "/api/collect/systems",
    data: {
      provider,
      systemName
    }
  })
}

/**
 * @method PUT
 * @desc   系统厂商-修改
 */
export function modifySystems(data: ISystemProviderFormData) {
  const { wsid, provider, systemName } = data
  return axios({
    method: "put",
    url: `/api/collect/systems/${wsid}`,
    data: {
      provider,
      systemName
    }
  })
}

/**
 * @method PUT
 * @desc   系统厂商-修改
 */
export function modifySystemStatus(wsid: string, nextStatus: "ENABLE" | "DISABLE" | "DEL") {
  return axios({
    method: "put",
    url: `/api/collect/systems/${wsid}/${nextStatus}`
  })
}

/**
 * @method GET
 * @desc   系统厂商-检查是否在使用
 */
export function checkSystemUsed(wsid: string) {
  return axios({
    method: "get",
    url: `/api/collect/systems/${wsid}/check-used`
  }).then(res => (res.data?.data ?? true) as boolean)
}

/* ======================== 数据源配置 ======================== */

export interface IDataSourceFormData {
  wsid: string
  dbType: string
  name: string
  url: string
  username: string
  password: string
  jdbcUrlParams: string
  dbName: string
  timeout: string
}

/**
 * @method GET
 * @desc   采集配置-获取数据源列表（分页）
 */
export function getDataSourcesList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: "/api/collect/data-sources",
    params
  })
}

/**
 * @method POST
 * @desc   采集配置-数据源新增
 */
export function addDataSources(data: IDataSourceFormData) {
  return axios({
    method: "post",
    url: "/api/collect/data-sources",
    data
  })
}

/**
 * @method POST
 * @desc   采集配置-测试数据源连接
 */
export function dataSourcesConnectTest(data: IDataSourceFormData) {
  return axios({
    method: "post",
    url: "/api/collect/data-sources/connect-test",
    data
  })
}

/**
 * @method PUT
 * @desc   采集配置-修改数据源
 */
export function modifyDataSources(data: IDataSourceFormData) {
  return axios({
    method: "put",
    url: `/api/collect/data-sources/${data.wsid}`,
    data
  })
}

/**
 * @method PUT
 * @desc   采集配置-修改数据源启用状态
 */
export function modifyDataSourcesStatus(wsid: string, nextStatus: "ENABLE" | "DISABLE" | "DEL") {
  return axios({
    method: "put",
    url: `/api/collect/data-sources/${wsid}/${nextStatus}`
  })
}

/**
 * @method GET
 * @desc   采集配置-检查数据源是否在使用
 */
export function checkDataSourceUsed(wsid: string) {
  return axios({
    method: "get",
    url: `/api/collect/data-sources/${wsid}/check-used`
  }).then(res => (res?.data?.data ?? true) as boolean)
}

/* ======================== 字段映射配置 ======================== */

export interface IFieldMappingFormData {
  id: string | number
  collectSystemWsid: string
  systemElementCode: string
  documentElementCode: string
}

/**
 * @method POST
 * @desc   采集配置-导入字段映射列表
 */
export function systemTemplateImport({ file }: { file: File }) {
  const formData = new FormData()
  formData.append("file", file)
  return axios({
    method: "post",
    url: "/api/collect/system-element/template-import",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  })
}

/**
 * @method GET
 * @desc   采集配置-字段映射列表模板下载
 */
export function downloadSystemElementTemplate() {
  return axios({
    method: "get",
    url: "/api/collect/system-element/template-download",
    responseType: "arraybuffer"
  })
}

/**
 * @method GET
 * @desc   采集配置-获取系统厂商数据列表
 */
export function querySystemElementList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: "/api/collect/system-element",
    params
  })
}

/**
 * @method GET
 * @desc   采集配置-获取所有目标字段
 */
export function getAllDocumentElementCode() {
  return axios
    .request<IResponseData>({
      method: "get",
      url: "/api/collect/document-element"
    })
    .then(res => res.data?.data ?? [])
}

/**
 * @method POST
 * @desc   采集配置-新增字段映射
 */
export function addSystemElement(data: IFieldMappingFormData) {
  const { collectSystemWsid, systemElementCode, documentElementCode } = data
  return operationAxios({
    method: "post",
    url: `/api/collect/system-element/${collectSystemWsid}`,
    data: {
      systemElementCode,
      documentElementCode
    }
  })
}

/**
 * @method PUT
 * @desc   采集配置-修改字段映射
 */
export function editSystemElement(data: IFieldMappingFormData) {
  const { id, collectSystemWsid, systemElementCode, documentElementCode } = data
  return operationAxios({
    method: "put",
    url: `/api/collect/system-element/${collectSystemWsid}`,
    data: {
      systemElementCode,
      documentElementCode,
      id
    }
  })
}

/**
 * @method DELETE
 * @desc   采集配置-删除字段映射
 */
export function deleteSystemElement(data: IFieldMappingFormData) {
  const { collectSystemWsid, systemElementCode } = data
  return operationAxios({
    method: "delete",
    url: `/api/collect/system-element/${collectSystemWsid}/${systemElementCode}`
  })
}

/**
 * @method GET
 * @desc   采集配置-导出错误数据列表（暂未使用）
 */
export function exportTemplateError(key: string) {
  return axios({
    method: "get",
    url: `/api/collect/system-element/template-import/error?key=${key}`,
    responseType: "arraybuffer"
  })
}

/* ======================== ftp数据源配置 ======================== */

export interface IFtpDataOriginFormData {
  wsid?: string
  name: string // ftp名称
  ftpType: string // ftp类型
  systemWsid: string // 厂商系统wsid
  userName: string // ftp登录账号
  password: string // 密码
  hostName: string // 远程端口
  remoteDirectory?: string // 远程目录
}

/**
 * @method GET
 * @desc   获取ftp数据源分页列表
 */
export function getFtpDataOriginListApi(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/collect/data-ftp-sources`,
    params: params
  })
}

/**
 * @method POST
 * @desc   新增ftp数据源
 */
export function addFtpDataOriginApi(data: IFtpDataOriginFormData) {
  return axios({
    method: "post",
    url: `/api/collect/data-ftp-sources`,
    data: data
  })
}

/**
 * @method PUT
 * @desc   编辑ftp数据源
 */
export function editFtpDataOriginApi(data: IFtpDataOriginFormData) {
  return axios({
    method: "put",
    url: `/api/collect/data-ftp-sources/${data.wsid}`,
    data: data
  })
}

/**
 * @method DELETE
 * @desc   删除ftp数据源
 */
export function deleteFtpDataOriginApi(wsid: string) {
  return axios({
    method: "delete",
    url: `/api/collect/data-ftp-sources/${wsid}`
  })
}

/**
 * @method PUT
 * @desc   修改ftp数据源状态
 */
export function modifyFtpDataOriginStatusApi(wsid: string, status: string) {
  return axios({
    method: "put",
    url: `/api/collect/data-ftp-sources/${wsid}/${status}`
  })
}

/**
 * @method POST
 * @desc   修改ftp数据源状态
 */
export function testFtpDataOriginApi(data: IFtpDataOriginFormData) {
  return axios({
    method: "post",
    url: `/api/collect/data-ftp-sources/connect-test`,
    data: data
  })
}
