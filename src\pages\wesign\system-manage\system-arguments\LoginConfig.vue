<template>
  <PageContainer>
    <TabsRouter :tabs-config="tabsRouterList" />
    <div v-loading="state.loading" class="login-config">
      <div class="config-item">
        <el-form label-position="left">
          <el-form-item label="初始密码登录修改：">
            <el-radio v-model="formData.modifyIntiPassword" label="YES">是</el-radio>
            <el-radio v-model="formData.modifyIntiPassword" label="NO">否</el-radio>
          </el-form-item>

          <el-form-item label="密码位数：">
            <el-slider v-model="formData.passwordNumber" range show-stops :max="20" :min="6" :marks="marks" />
          </el-form-item>

          <el-form-item label="密码必须包含：">
            <el-checkbox-group v-model="formData.passwordInclude">
              <el-checkbox label="大小写字母"></el-checkbox>
              <el-checkbox label="数字"></el-checkbox>
              <el-checkbox label="特殊字符"></el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="密码定时过期：">
            <el-input v-model="formData.passwordExprieDay"><template #append>天</template></el-input>
            <div class="checkbox-container">
              <el-checkbox v-model="state.neverExpire">
                从不
                <span>（超过了密码有效期期，则在用户登录时强制用户修改密码 ）</span>
              </el-checkbox>
            </div>
          </el-form-item>
          <el-form-item label="密码错误次数限制：">
            <el-input v-model="formData.passwordErrorNumber"><template #append>次</template></el-input>
            <label style="margin-left: 20px">锁定：</label>
            <el-input v-model="formData.passwordErrorLock"><template #append>分钟</template></el-input>
          </el-form-item>

          <el-form-item label="历史密码校验：">
            <el-radio v-model="formData.passwordHistoryCheck" label="YES">是</el-radio>
            <el-radio v-model="formData.passwordHistoryCheck" label="NO">否</el-radio>
          </el-form-item>

          <el-form-item label="无操作登录保持时间：">
            <el-input v-model="formData.loginHoldTime"><template #append>分钟</template></el-input>
          </el-form-item>
          <el-form-item label=" ">
            <div class="btn-container">
              <el-button type="primary" @click="save">保存</el-button>
              <el-button @click="reset">重置</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, onMounted, watch } from "vue"
import { PageContainer } from "@/base-components"
import { TabsRouter } from "@/page-components"
import { querySystemConfig, updateSystemConfig } from "@/interfaces"
import { Message } from "@/utils"
import { tabsRouterList } from "./config"
import type { CSSProperties } from "vue"

interface FunctionConfigFormData {
  modifyIntiPassword: string
  passwordNumber: string
  passwordInclude: []
  passwordExprieDay: string
  passwordErrorNumber: number
  passwordErrorLock: number
  passwordHistoryCheck: string
  loginHoldTime: number
}

const formData = reactive<FunctionConfigFormData>({
  modifyIntiPassword: "",
  passwordNumber: "",
  passwordInclude: [],
  passwordExprieDay: "",
  passwordErrorNumber: "",
  passwordErrorLock: "",
  passwordHistoryCheck: "",
  loginHoldTime: ""
})

const state = reactive({
  loading: false,
  data: [],
  neverExpire: false
})

// 滑块刻度
interface Mark {
  style: CSSProperties
  label: string
}

type Marks = Record<number, Mark | string>

const marks = reactive<Marks>({})

for (let i = 0; i <= 20; i++) {
  marks[i] = i + ""
}

watch(
  () => state.neverExpire,
  (n, o) => {
    if (n) {
      formData.passwordExprieDay = null
    }
  },
  { deep: true }
)

watch(
  () => formData.passwordExprieDay,
  (n, o) => {
    if (n) {
      state.neverExpire = false
    }
  },
  { deep: true }
)

onMounted(() => {
  querySystemConfig({ type: "LOGIN" }).then(res => {
    res.data.data.forEach(item => {
      if (item.key === "passwordExprieDay" && !item.value) {
        state.neverExpire = true
      }
      if (item.value) {
        formData[item.key] = item.value
        if (item.key === "passwordNumber") {
          formData.passwordNumber = item.value.split("-")
        }
      }
    })
    // 转换为checkbox的label
    formData.passwordInclude = formData.passwordInclude
      .split(",")
      .map(value => {
        return transformValueToLabel(value)
      })
      .filter(value => value)
  })
})

const transformLabelToValue = (label: string) => {
  switch (label) {
    case "大小写字母":
      return "LETTER"
    case "数字":
      return "NUMBER"
    case "特殊字符":
      return "SPECIAL_CHARACTER"
    default:
      return ""
  }
}

const transformValueToLabel = (value: string) => {
  switch (value) {
    case "LETTER":
      return "大小写字母"
    case "NUMBER":
      return "数字"
    case "SPECIAL_CHARACTER":
      return "特殊字符"
    default:
      return ""
  }
}

const reset = () => {
  for (let key in formData) {
    formData[key] = ""
    state.data.push({ key: key, value: formData[key] })
  }
  state.neverExpire = false
}

const save = () => {
  if (state.loading) return
  state.loading = true
  for (let key in formData) {
    if (key === "passwordInclude") {
      const value = formData[key].map(label => transformLabelToValue(label)).join(",")
      state.data.push({ key: key, value: value })
    } else if (key === "passwordNumber") {
      const range = formData[key].join("-")
      state.data.push({ key: key, value: range })
    } else {
      state.data.push({ key: key, value: formData[key] })
    }
  }

  updateSystemConfig(state.data)
    .then(() => {
      Message.success("保存成功")
      state.loading = false
      state.data = []
    })
    .catch(error => {
      state.loading = false
      if (error.response.errMessage) {
        Message.error(`${error.response.errMessage}`)
      } else {
        Message.error("保存失败")
      }
      state.data = []
    })
}
</script>

<style lang="less" scoped>
.login-config {
  position: relative;
  height: 100%;

  :deep(.el-slider) {
    width: 50%;
  }
  :deep(.el-input) {
    width: 20%;
  }

  :deep(.el-form) {
    padding-left: 28px;
  }

  :deep(.el-form-item__label) {
    justify-content: flex-end;
    width: 152px;
  }

  :deep(.el-form-item) {
    margin-bottom: 25px;
  }
  :deep(.el-input-group__append) {
    width: 5%;
  }
  .config-item {
    width: 100%;
    // padding: 40px 0;
    padding-top: 40px;
    padding-bottom: 20px;
    // border-bottom: 1px solid #ebedf0;

    .checkbox-container {
      padding-left: 20px;
      span {
        font-size: 12px;
        color: #7f8997;
      }
    }
  }
}
</style>
