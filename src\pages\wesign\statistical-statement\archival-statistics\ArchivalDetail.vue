<template>
  <PageContainer separate>
    <!-- 头部搜索 -->
    <template #search>
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <DepartmentFormItem v-model:model-value="archivalDetailParams.deptWsid" label="出院科室"></DepartmentFormItem>

        <DaterangeFormItem
          v-model:model-value="searchFormState.outHospitalDatetime"
          :is-clearable="false"
          label="出院时间"
        />

        <el-form-item label="归档天数">
          <el-select v-model="archivalDetailParams.itemType">
            <el-option
              v-for="item in archivalDaysOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        :table-columns="archivalStatisticsDetailColumn"
        :request-api="getArchivalStatisticsDetailApi"
        :request-params="searchParams.queryParams"
      >
        <template #header>
          <ExportButton
            :export-api="exportArchiveStatisticsDetailApi"
            :export-params="exportParams"
            :file-name="`归档率统计明细表_${getCurrentDate()}.xlsx`"
            file-type="application/vnd.ms-excel"
          >
            导出
          </ExportButton>
        </template>
        <template #outHospitalDatetime="{ row }">
          <span>{{ formatDatetime(row.outHospitalDatetime) }}</span>
        </template>
        <template #archiveTime="{ row }">
          <span>{{ formatDatetime(row.archiveTime) }}</span>
        </template>
      </CommonTable>
    </template>
  </PageContainer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, onBeforeMount } from "vue"
import { useRoute } from "vue-router"
import {
  PageContainer,
  SearchContainer,
  DaterangeFormItem,
  DepartmentFormItem,
  CommonTable,
  ExportButton
} from "@/base-components"
import { useTableSearch } from "@/hooks"
import { formatDatetime, toastError } from "@/utils"
import { getCurrentDate } from "../config"
import { getArchivalStatisticsDetailApi, exportArchiveStatisticsDetailApi, getUserArchivalConfigApi } from "../interface"
import { archivalStatisticsDetailColumn, getCustomConfig } from "./config"

const route = useRoute()

const archiveDateEnd = new Date(new Date().setHours(23, 59, 59, 999)).getTime()
const archiveDateStart = new Date(new Date(new Date().setMonth(new Date().getMonth() - 1)).setHours(0, 0, 0, 0)).getTime()

/* ======================== 搜索相关数据及方法 ======================== */

// 归档天数options
const archivalDaysOptions = ref<Array<Record<string, any>>>([])

const searchFormState = reactive({
  deptWsid: "",
  outHospitalDatetime: [archiveDateStart, archiveDateEnd] as [number, number],
  itemType: ""
})

const archivalDetailParams = reactive({
  deptWsid: "",
  archiveDateStart: archiveDateStart,
  archiveDateEnd: archiveDateEnd,
  itemType: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(
  {},
  "",
  { ...searchFormState, ...archivalDetailParams },
  archivalDetailParams,
  searchFormState
)

watch(
  () => searchFormState.outHospitalDatetime,
  val => {
    (archivalDetailParams.archiveDateStart = val[0]), (archivalDetailParams.archiveDateEnd = val[1])
  }
)

const exportParams = computed(() => { 
  return {
    archiveDateStart: searchFormState.outHospitalDatetime[0],
    archiveDateEnd: searchFormState.outHospitalDatetime[1],
    itemType: searchFormState.itemType
  }
})

onBeforeMount(() => {
  getParamsFromQuery()
})

onMounted(() => {
  getUserArchivalConfig()
})

// 从query中提取携带的参数
function getParamsFromQuery() {
  const query = route.query as any
  archivalDetailParams.deptWsid = query.deptWsid
  archivalDetailParams.itemType = query.itemType
  searchFormState.outHospitalDatetime = [JSON.parse(query.archiveDateStart), JSON.parse(query.archiveDateEnd)]
  archivalDetailParams.archiveDateStart = JSON.parse(query.archiveDateStart)
  archivalDetailParams.archiveDateEnd = JSON.parse(query.archiveDateEnd)
  handleQuery()
}

// 获取自定义配置数据
function getUserArchivalConfig() {
  getUserArchivalConfigApi().then(res => {
    const userConfigData = res.data.data
    archivalDaysOptions.value = getCustomConfig(userConfigData)
  }).catch(err => {
    toastError(err, "获取自定义配置失败")
  })
}


</script>
