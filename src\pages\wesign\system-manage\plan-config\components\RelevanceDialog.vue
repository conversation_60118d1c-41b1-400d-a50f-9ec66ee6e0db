<template>
  <div class="relevance-wrapper">
    <div class="relevance-left">
      <el-input v-model="relevanceState.inputValue" placeholder="文档类型" :prefix-icon="Search" />
      <div class="relevance-left-content">
        <div v-for="item in renderList" :key="item.wsid" class="relevance-left-item">
          <span style="font-size: 12px">{{ item.mrClassName }}({{ item.mrClassCode }})</span>
          <i class="ri-arrow-right-circle-fill relevance-item-icon" @click="handleMoveCatalogues(item)"></i>
        </div>
      </div>
    </div>

    <div class="relevance-right-wrapper">
      <div v-if="props.allowAddDirectory" class="relevance-choice-catalogue">
        <el-button type="primary" link size="small" @click="clickAddCatalogue">新增目录</el-button>
      </div>
      <div class="relevance-right">
        <el-tree
          ref="elTreeRef"
          :data="renderTree"
          node-key="wsid"
          :props="treeProps"
          :expand-on-click-node="false"
          :highlight-current="true"
          :default-expanded-keys="expandNode"
          :default-expand-all="true"
          draggable
          :allow-drag="allowDrag"
          :allow-drop="allowDrop"
          class="relevance-el-tree"
          @node-click="handleClickNode"
          @node-expand="handleExpandNode"
          @node-collapse="handleCollapseNode"
        >
          <template #default="{ node, data }">
            <div class="custom-tree-node">
              <div class="custom-node-label">
                <div v-if="data.type !== 'CATALOGUE'" style="display: flex; align-items: center; margin-right: 8px">
                  <img style="width: 12px" src="~@/assets/svg/tree/file.svg" />
                </div>
                <span>{{ node.label }}</span>
              </div>
              <div class="relevance-icons-box">
                <!-- <div
                  v-if="!data.isLast && data.type !== 'CATALOGUE' && node.parent.childNodes.length > 1"
                  class="relevance-icon-wrapper"
                  @click.prevent="handleMove(node, data, 'down')"
                >
                  <el-tooltip class="relevance-icon-tooltip" effect="dark" content="下移" placement="top">
                    <img src="~@/assets/svg/tree/down.svg" />
                  </el-tooltip>
                </div>
                <div v-else class="relevance-icon-blank"></div>
                <div
                  v-if="!data.isFirst && data.type !== 'CATALOGUE' && node.parent.childNodes.length > 1"
                  class="relevance-icon-wrapper"
                  @click.prevent="handleMove(node, data, 'up')"
                >
                  <el-tooltip class="relevance-icon-tooltip" effect="dark" content="上移" placement="top">
                    <img src="~@/assets/svg/tree/up.svg" />
                  </el-tooltip>
                </div>
                <div v-else class="relevance-icon-blank"></div> -->
                <el-tooltip
                  v-if="data.type !== 'CATALOGUE'"
                  class="relevance-icon-tooltip"
                  effect="dark"
                  content="拖拽顺序"
                  placement="top-end"
                >
                  <div class="relevance-icon-drag"></div>
                </el-tooltip>
                <div
                  v-if="data.type !== 'CATALOGUE'"
                  class="relevance-icon-wrapper"
                  @click.prevent="handleDeleteNode(node, data)"
                >
                  <img style="height: 10px" src="~@/assets/svg/tree/delete.svg" />
                </div>
                <div v-if="data.type === 'CATALOGUE'">
                  <el-tooltip class="relevance-icon-tooltip" effect="dark" content="拖拽顺序" placement="top-end">
                    <div class="relevance-icon-drag"></div>
                  </el-tooltip>
                </div>
                <div v-else class="relevance-icon-blank"></div>
              </div>
            </div>
          </template>
        </el-tree>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, computed } from "vue"
import { cloneDeep } from "lodash-es"
import { Search } from "@element-plus/icons-vue"
import { Message, flattenTree } from "@/utils"
import type Node from "element-plus/es/components/tree/src/model/node"
import type { AllowDropType } from "element-plus/es/components/tree/src/tree.type"

const elTreeRef = ref()

const treeProps = {
  children: "children",
  label: "name",
  isLeaf: "leaf",
  class: function (data: any) {
    return data.type === "MR_CLASS" ? "is-file" : "custom-tree"
  }
}

type RelevanceDialogProps = {
  listData: any[]
  treeData: any[]
  id?: string
  allowAddDirectory: boolean
}

const props = withDefaults(defineProps<RelevanceDialogProps>(), {
  listData: () => [],
  treeData: () => [],
  id: ""
})

interface RelevanceDialogState {
  inputValue: string
  currentNodeKey: string
  oldRenderTree: any[]
}

const relevanceState = reactive<RelevanceDialogState>({
  inputValue: "",
  currentNodeKey: "",
  oldRenderTree: []
})

const filterList = computed(() => {
  let list = cloneDeep(props.listData)
  const _handleTreeSn = tree => {
    tree.forEach((node, index) => {
      if (node.type === "MR_CLASS") {
        list = list.filter(item => item.wsid !== node.wsid)
      }
      if (Object.hasOwnProperty.call(node, "children") && node.children) {
        _handleTreeSn(node.children)
      }
    })
  }
  _handleTreeSn(renderTree.value)
  return list
})

const renderList = computed(() => {
  if (!relevanceState.inputValue.trim()) {
    return filterList.value
  } else {
    return filterList.value.filter(item => item.mrClassName.indexOf(relevanceState.inputValue.trim()) !== -1)
  }
})

const renderTree = computed(() => {
  let catalogueTree = [...props.treeData]
  catalogueTree = compareTree(catalogueTree)
  const _handleTree = (catalogueTree, wsid?: string) => {
    for (let i = 0; i < catalogueTree.length; i++) {
      catalogueTree[i].isCatalogueChild = false
      catalogueTree[i].isFirst = false
      catalogueTree[i].isLast = false
      if (i === 0) {
        catalogueTree[i].isFirst = true
      }
      if (i === catalogueTree.length - 1) {
        catalogueTree[i].isLast = true
      }
      catalogueTree[i].parentWsid = wsid ?? ""
      if (Object.hasOwnProperty.call(catalogueTree[i], "children")) {
        if (catalogueTree[i]?.children[0]?.type === "CATALOGUE") catalogueTree[i].isCatalogueChild = true
        _handleTree(catalogueTree[i].children, catalogueTree[i].wsid)
      }
    }
  }
  _handleTree(catalogueTree)
  return catalogueTree
})

function compareTree(newTree: any[]): any[] {
  let resultArr = cloneDeep(relevanceState.oldRenderTree)
  if (relevanceState.oldRenderTree?.length) {
    const _compare = (newTrees, oldTrees, resTree) => {
      //新增处理
      newTrees.forEach(t => {
        let hasT = false,
          oldItemT
        oldTrees.forEach(oldT => {
          if (t.wsid === oldT.wsid) {
            hasT = true
            oldItemT = oldT
          }
        })
        if (hasT) {
          if (Object.hasOwnProperty.call(t, "children")) {
            const tree = resTree.filter(item => item.wsid === oldItemT.wsid)
            if (Array.isArray(t.children) && Array.isArray(oldItemT.children)) {
              _compare(t.children, oldItemT.children, tree[0].children)
            }
          }
          // if (Object.hasOwnProperty.call(t, "childrenMrClass")) {
          //   const tree = resTree.filter(item => item.wsid === oldItemT.wsid)
          //   if (Array.isArray(t.childrenMrClass) && Array.isArray(oldItemT.childrenMrClass)) {
          //     _compare(t.children, oldItemT.childrenMrClass, tree[0].childrenMrClass)
          //   }
          // }
        } else {
          const sn = oldTrees[oldTrees.length - 1].sn ?? 0
          t.sn = sn
          resTree.push(t)
        }
      })

      //删除处理
      oldTrees.forEach(oldT => {
        let hasOldT = false
        newTrees.forEach(t => {
          if (t.wsid === oldT.wsid) {
            hasOldT = true
          }
        })
        if (!hasOldT) {
          resTree = resTree.filter(item => item.wsid !== oldT.wsid)
        }
      })
      return resTree
    }

    resultArr = _compare(newTree, relevanceState.oldRenderTree, resultArr)
    return resultArr
  } else {
    return newTree
  }
}

//选择节点

type TypeChoiceNode = {
  isCatalogueChild?: boolean
  children?: any[]
  wsid?: string
  childrenMrClass?: any[]
}

const choiceNode = ref<TypeChoiceNode>({})

const handleClickNode = (data, node) => {
  if (data.type !== "CATALOGUE") {
    choiceNode.value = {}
    return
  }
  choiceNode.value = data
}

const expandNode = ref([])
const expandNodeLog = ref([])

const handleExpandNode = (data, node) => {
  if (!expandNodeLog.value.includes(data.wsid)) expandNodeLog.value.push(data.wsid)
}

const handleCollapseNode = (data, node) => {
  if (expandNodeLog.value.includes(data.wsid)) {
    expandNodeLog.value = expandNodeLog.value.filter(item => {
      return item !== data.wsid
    })
  }
}

//操作
const handleMove = (node, data, type: "down" | "up") => {
  let index = 0,
    moveIndex = 0
  let parent
  if (!Array.isArray(node.parent.data)) {
    parent = node.parent.data.children ?? []
  } else {
    parent = node.parent.data
  }
  parent.find((pNode, i) => {
    if (data.wsid === pNode.wsid) {
      index = i
      return true
    }
  })
  moveIndex = type === "down" ? index + 1 : index - 1

  const cloneTemp = cloneDeep(parent[index])
  const cloneOtherTemp = cloneDeep(parent[moveIndex])
  cloneTemp.sn = cloneOtherTemp.sn
  cloneTemp.isFirst = cloneOtherTemp.isFirst
  cloneTemp.isLast = cloneOtherTemp.isLast

  cloneOtherTemp.sn = data.sn
  cloneOtherTemp.isFirst = data.isFirst
  cloneOtherTemp.isLast = data.isLast
  if (type === "down") {
    let referNode = node.previousSibling
    if (referNode) {
      elTreeRef.value?.remove(node)
      elTreeRef.value?.remove(cloneOtherTemp)

      elTreeRef.value?.insertAfter(cloneTemp, referNode)
      elTreeRef.value?.insertAfter(cloneOtherTemp, referNode)
    } else {
      referNode = node.nextSibling.nextSibling
      if (referNode) {
        elTreeRef.value?.remove(node)
        elTreeRef.value?.remove(cloneOtherTemp)
        elTreeRef.value?.insertBefore(cloneOtherTemp, referNode)
        elTreeRef.value?.insertBefore(cloneTemp, referNode)
      } else {
        referNode = node.parent

        elTreeRef.value?.remove(node)
        elTreeRef.value?.remove(cloneOtherTemp)

        elTreeRef.value?.append(cloneOtherTemp, referNode)
        elTreeRef.value?.append(cloneTemp, referNode)
      }
    }
  } else {
    let referNode = node.nextSibling

    if (referNode) {
      elTreeRef.value?.remove(node)
      elTreeRef.value?.remove(cloneOtherTemp)

      elTreeRef.value?.insertBefore(cloneTemp, referNode)
      elTreeRef.value?.insertBefore(cloneOtherTemp, referNode)
    } else {
      referNode = node.previousSibling.previousSibling
      if (referNode) {
        elTreeRef.value?.remove(node)
        elTreeRef.value?.remove(cloneOtherTemp)
        elTreeRef.value?.insertAfter(cloneOtherTemp, referNode)
        elTreeRef.value?.insertAfter(cloneTemp, referNode)
      } else {
        referNode = node.parent
        elTreeRef.value?.remove(node)
        elTreeRef.value?.remove(cloneOtherTemp)

        elTreeRef.value?.append(cloneTemp, referNode)
        elTreeRef.value?.append(cloneOtherTemp, referNode)
      }
    }
  }
  expandNode.value = [...expandNodeLog.value]
}

// 判断是否可拖拽
const allowDrag = (draggingNode: Node) => {
  // return draggingNode.data.type === "CATALOGUE"
  return true
}

const allowDrop = (draggingNode: Node, dropNode: Node, type: AllowDropType) => {
  expandNode.value = [...expandNodeLog.value]
  // 拖拽文书
  if (draggingNode.data.type === "MR_CLASS") {
    // 目标节点所有的节点
    const flatTree = flattenTree(dropNode.data?.children || [])
    if (flatTree.some(item => item.wsid === draggingNode.data.wsid)) {
      // 只能移到最外层节点的内部
      if (dropNode.level === 1) {
        return dropNode.data.type === "CATALOGUE" && type === "inner"
      } else {
        // 其他目录节点
        return dropNode.data.type === "CATALOGUE"
      }
    } else {
      if (dropNode.data.type === "CATALOGUE") {
        return draggingNode.data.parentWsid === dropNode.data.parentWsid
      } else {
        return type !== "inner"
      }
      // return dropNode.data.type === "CATALOGUE" && draggingNode.data.parentWsid === dropNode.data.parentWsid
    }
  } else {
    return type !== "inner" && draggingNode.data.parentWsid === dropNode.data.parentWsid
  }
}

// 删除时将childrenMrClass和childrenCatalogues中的也删除
const handleDeleteNode = (node, data) => {
  let parent = node.parent.data
  if (data.type === "MR_CLASS") {
    parent.childrenMrClass = parent.childrenMrClass.filter(item => item.wsid !== data.wsid)
  } else {
    parent.childrenCatalogues = parent.childrenCatalogues.filter(item => item.wsid !== data.wsid)
  }

  if (!Array.isArray(parent)) parent = node.parent.data.children ?? []

  elTreeRef.value?.remove(data)
  expandNode.value = [...expandNodeLog.value]
}

type TypeMrClass = {
  code: string
  disable: boolean
  filePage?: number
  name: string
  sn: number
  type: string
  wsid: string
  isFirst: boolean
  isLast: boolean
}

const handleMoveCatalogues = (value: any) => {
  if (Object.keys(choiceNode.value).length === 0) {
    return Message.error("请先选择目录")
  }
  // if (choiceNode.value.isCatalogueChild) {
  //   return Message.error("该所属目录含有子目录，关联的该目录需删除子目录")
  // }

  const mrClassObj: TypeMrClass = {
    code: value.mrClassCode,
    name: value.mrClassName,
    wsid: value.wsid,
    disable: true,
    sn: 1,
    type: "MR_CLASS",
    isFirst: false,
    isLast: false
  }
  const isExist = hasExistNode(renderTree.value, mrClassObj.wsid)
  if (isExist) {
    return Message.warning("该文档类型已被添加")
  }

  if (Object.hasOwnProperty.call(choiceNode.value, "children") && choiceNode.value.children?.length) {
    if (!isExist) {
      choiceNode.value.children[choiceNode.value.children.length - 1].isLast = false
      const sn = choiceNode.value.children[choiceNode.value.children.length - 1].sn ?? 0
      mrClassObj.isLast = true
      mrClassObj.sn = sn + 1
      choiceNode.value.children.push(mrClassObj)
      choiceNode.value.childrenMrClass.push(mrClassObj)
    }
  } else {
    mrClassObj.isFirst = true
    mrClassObj.isLast = false
    choiceNode.value["children"] = [mrClassObj]
    choiceNode.value["childrenMrClass"] = [mrClassObj]
  }

  expandNode.value = [...expandNodeLog.value, choiceNode.value.wsid]

  //选中节点高亮展示
  setTimeout(() => {
    elTreeRef.value?.setCurrentKey(choiceNode.value.wsid, false)
  }, 100)
}

const emits = defineEmits(["addCatalogue"])

const clickAddCatalogue = () => {
  emits("addCatalogue")
}

const hasExistNode = (tree: any[], wsid: string) => {
  for (let i = 0; i < tree.length; i++) {
    const item = tree[i]
    if (item?.wsid === wsid) return true
    else {
      if (Object.hasOwnProperty.call(item, "children") && item.children) {
        if (hasExistNode(item.children, wsid)) return true
      }
    }
  }
  return false
}

//调整排序
const handleTreeSn = tree => {
  tree.forEach((node, index) => {
    node.sn = index
    if (Object.hasOwnProperty.call(node, "children") && node.children) {
      handleTreeSn(node.children)
    }
  })
}

//暴露
const getRenderTree = () => {
  handleTreeSn(renderTree.value)
  // console.log("renderTree.value:", renderTree.value)
  return renderTree.value
}

const cloneRenderTree = () => {
  relevanceState.oldRenderTree = cloneDeep(renderTree.value)
}

defineExpose({ getRenderTree, cloneRenderTree })
</script>

<style lang="less" scoped>
.relevance-wrapper {
  display: flex;
  justify-content: space-between;
  height: 330px;
  .relevance-left {
    display: flex;
    width: 406px;
    flex-direction: column;
    font-size: 12px;
    font-weight: 500;
    color: rgb(10 22 51 / 85%);

    .relevance-left-content {
      overflow-y: auto;
      height: 0;
      padding: 14px 27px 14px 13px;
      margin-top: 20px;
      border: 1px solid rgb(10 22 51 / 10%);
      border-radius: 3px;
      flex: 1;
      .relevance-left-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 30px;
        font-size: 12px;
        color: rgb(10 22 51 / 85%);
        line-height: 30px;
        font-weight: 500;
        .relevance-item-icon {
          font-size: 16px;
          color: var(--el-color-primary);
          cursor: pointer;
        }
      }
    }
  }
  .relevance-right {
    display: flex;
    flex-direction: column;
    // width: 505px;
    overflow-y: auto;
    flex: 1;
    padding: 14px 27px 14px 13px;
    border: 1px solid rgb(10 22 51 / 10%);
    border-radius: 3px;

    :deep(.relevance-el-tree) {
      .el-tree-node__content > .el-tree-node__expand-icon svg {
        display: none;
        padding: 0;
      }
      .el-tree-node__content > .el-tree-node__expand-icon {
        font-size: 16px;
      }
      .el-tree-node.is-file > .el-tree-node__content > .el-tree-node__expand-icon {
        display: none;
        padding: 0;
      }
      .el-tree-node__content > .el-tree-node__expand-icon.expanded {
        transform: rotate(0deg);
      }

      .el-tree-node:not(.is-file) > .el-tree-node__content > .el-tree-node__expand-icon::before {
        width: 15px;
        margin-right: 10px;
        background-color: white;
        content: url(@/assets/svg/tree/open.svg);
      }

      .el-tree-node.is-expanded:not(.is-file) > .el-tree-node__content > .el-tree-node__expand-icon::before {
        width: 15px;
        margin-right: 10px;
        background-color: white;
        content: url(@/assets/svg/tree/close.svg);
      }
      .is-file > .el-tree-node__content {
        margin-left: 4px;
      }

      .el-tree-node__content {
        .custom-tree-node {
          display: flex;
          justify-content: space-between;
          font-size: 12px;
          flex: 1;
          .custom-node-label {
            display: inline-flex;
            width: 280px;
          }
        }
      }
    }
  }
}
.relevance-icons-box {
  display: flex;
  align-items: center;
  height: 100%;

  .relevance-icon-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    height: 16px;
    margin-left: 16px;
  }
  .relevance-icon-drag::before {
    width: 12px;
    margin-left: 16px;
    background-color: white;
    content: url(@/assets/svg/tree/tree-icon.svg);
  }
  .relevance-icon-blank::before {
    display: inline-block;
    width: 12px;
    margin-left: 16px;
    background-color: red;
    content: "";
  }
  img {
    height: 12px;
  }
}

.relevance-right-wrapper {
  display: flex;
  flex-direction: column;
  width: 505px;
  .relevance-choice-catalogue {
    height: 32px;
    padding: 0 12px;
    text-align: right;
    border: 1px solid rgb(10 22 51 / 10%);
    border-bottom: none;
    line-height: 32px;
  }
}
</style>
