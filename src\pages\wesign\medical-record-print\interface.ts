import axios from "@/interfaces/axios-instance"
import { RelationshipType } from "@/types"

/* ======================== 打印审批 ======================== */

/**
 * @method GET
 * @desc   查询申请单列表
 */
export function getPrintApplyList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/print/apply/list`,
    params: { ...params, sorts: "-createdDatetime" }
  })
}

/**
 * @method GET
 * @desc   查询申请单详情
 */
export function getPrintApplyDetailApi(wsid: string) {
  return axios({
    method: "get",
    url: `/api/print/apply/${wsid}`
  })
}

interface IapprovePrintApplyApiData {
  wsid: string
  status: string
  reason: string
}

/**
 * @method POST
 * @desc   申请单审核
 */
export function approvePrintApplyApi(data: IapprovePrintApplyApiData) {
  return axios({
    method: "post",
    url: `/api/print/apply/approve`,
    data
  })
}

/**
 * @method GET
 * @desc   管理系统--病案文件处理失败列表
 */
export function getOrderFileFailList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/print/apply/orderFileFail/list`,
    params
  })
}

/**
 * @method POST
 * @desc   管理系统--文件重新处理
 */
export function reGenerateFileApi(data: Array<string>) {
  return axios({
    method: "post",
    url: `/api/print/apply/retry-orderFile`,
    data
  })
}

/* ======================== 打印订单 ======================== */

/**
 * @method GET
 * @desc   打印订单列表接口
 */
export function getPrintList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/print/list`,
    params: { ...params, sorts: "-createdDatetime" }
  })
}

/**
 * @method GET
 * @desc   打印订单列表导出接口
 */
export function exportPrintList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/print/list/export`,
    responseType: "arraybuffer",
    params
  })
}

/**
 * @method POST
 * @desc   打印订单打印完成
 */
export function printComplete(data: Array<string>) {
  return axios({
    method: "post",
    url: `/api/print/complete`,
    data
  })
}

/**
 * @method GET
 * @desc   查询申请单下的文件信息
 */
export function queryOrderFile(wsid: string) {
  return axios({
    method: "get",
    url: `/api/print/apply/${wsid}/print`
  })
}

/**
 * @method POST
 * @desc   打印订单完成邮寄
 */
export function mailComplete(data: Array<string>) {
  return axios({
    method: "post",
    url: `/api/print/mail`,
    data
  })
}

/**
 * @method GET
 * @desc   打印订单-查询打印申请单打印详情
 */
export function queryOrderDetail(params: Record<string, any>) {
  return axios({
    method: "get",
    url: `/api/print/record/order`,
    params
  })
}

/* ======================== 打印记录 ======================== */

/**
 * @method GET
 * @desc   查询打印记录列表
 */
export function getPrintRecordList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/print/record`,
    params: { ...params, sorts: "-createdDatetime" }
  })
}

/**
 * @method GET
 * @desc   打印记录列表导出接口
 */
export function exportPrintRecordList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/print/record/export`,
    responseType: "arraybuffer",
    params
  })
}

/**
 * @method GET
 * @desc   打印记录详情
 */
export function queryPrintRecordDetail(wsid: string) {
  return axios({
    method: "get",
    url: `/api/print/record/${wsid}`
  })
}

/* ======================== 自取核销 ======================== */

/**
 * @method GET
 * @desc   查询自取核销列表
 */
export function getOwnTakeList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/print/apply/own-take`,
    params
  })
}

interface IOwnTakeData {
  wsid?: string
  extractCode?: string
}

/**
 * @method POST
 * @desc   自取核销操作
 */
export function ownTakeData(data: IOwnTakeData) {
  return axios({
    method: "post",
    url: `/api/print/apply/own-take`,
    data
  })
}

/* ======================== 退款管理 ======================== */

/**
 * @method GET
 * @desc   管理系统--退款申请列表
 */
export function getRefundApplyList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/print/apply/refund-apply/list`,
    params
  })
}

interface IRefundApplyData {
  wsid: string
  status: boolean
  reason?: string
}
/**
 * @method POST
 * @desc   管理系统--退款申请审批
 */
export function refundApplyApprove(data: IRefundApplyData) {
  return axios({
    method: "post",
    url: `/api/print/apply/refund-apply/approve`,
    data
  })
}

/**
 * @method POST
 * @desc   管理系统--重新退款
 */
export function refundApplyApi(applyWsid: string) {
  return axios({
    method: "post",
    url: `/api/print/finance/refund/${applyWsid}`
  })
}

/* ======================== 对账管理 ======================== */

/**
 * @method GET
 * @desc   管理系统--T+1交易对账列表
 */
export function getTradeBillList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/print/finance/tradeBill/statistics`,
    params: {
      ...params,
      sorts: "-billDate"
    }
  })
}

/**
 * @method GET
 * @desc   管理系统--T+1交易对账明细
 */
export function getTradeBillDetail(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/print/finance/tradeBill`,
    params: {
      ...params,
      sorts: "-tradeDate"
    }
  })
}

/**
 * @method GET
 * @desc   管理系统--T+1交易对账列表导出
 */
export function exportTradeBill(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/print/finance/tradeBill/statistics/export`,
    responseType: "arraybuffer",
    params
  })
}

/**
 * @method GET
 * @desc   管理系统--T+1交易对账明细导出
 */
export function exportTradeBillDetail(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/print/finance/tradeBill/export`,
    responseType: "arraybuffer",
    params
  })
}

/**
 * @method GET
 * @desc   管理系统--资金对账列表
 */
export function getFundBillList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/print/finance/fundBill`,
    params: {
      ...params,
      sorts: "-billDate"
    }
  })
}

/**
 * @method GET
 * @desc   管理系统--资金对账列表导出
 */
export function exportFundBillDetail(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/print/finance/fundBill/export`,
    responseType: "arraybuffer",
    params
  })
}

/* ======================== 电子分发 ======================== */

/**
 * @method GET
 * @desc   管理系统--电子分发分页列表接口
 */
export function getPackageList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/print/apply/package`,
    params: { ...params, sorts: "-createdDatetime" }
  })
}

/**
 * @method GET
 * @desc   管理系统--电子分发分页列表导出
 */
export function exportPackage(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/print/apply/package/export`,
    responseType: "arraybuffer",
    params
  })
}

/**
 * @method GET
 * @desc   管理系统--电子分发详情及分发列表
 */
export function getPackageDetailList(params: Record<string, any>) {
  return axios({
    method: "get",
    url: `/api/print/apply/package/${params.wsid}`
  })
}

/**
 * @method POST
 * @desc   管理系统--电子分发重新分发
 */
export function retryPackage(data: Array<string>) {
  return axios({
    method: "post",
    url: `/api/print/apply/retry-package`,
    data
  })
}

/* ======================== 打印统计 ======================== */

interface IStatisticsOrderData {
  startDate?: number
  endDate?: number
  statisticsType?: "DAY" | "WEEK" | "MONTH"
}

/**
 * @method POST
 * @desc 订单统计
 */
export function getStatisticsOrder(data: IStatisticsOrderData) {
  return axios({
    method: "post",
    url: `/api/print/statistics/order`,
    data
  })
}

interface IStatisticsPurposeData extends IStatisticsOrderData {
  printPurposeCode: string
}

/**
 * @method POST
 * @desc 打印用途统计
 */
export function getStatisticsPurpose(data: IStatisticsPurposeData) {
  return axios({
    method: "post",
    url: `/api/print/statistics/purpose`,
    data
  })
}

interface IStatisticsWorkloadData {
  receiveType?: "ELECTRONIC_DISTRIBUTION" | "MAIL" | "OWN_TAKE "
  printPurposeCode?: string
  printInvoice?: "1" | "0"
  offset?: string
  limit?: string
  startDate?: number
  endDate?: number
}

/**
 * @method POST
 * @desc 打印工作量统计
 */
export function getStatisticsWorkload(data: IStatisticsWorkloadData) {
  return axios({
    method: "post",
    url: `/api/print/statistics/work-load`,
    data
  })
}

/**
 * @method POST
 * @desc 交易概况
 */
export function getStatisticsTrade(data: IStatisticsOrderData) {
  return axios({
    method: "post",
    url: `/api/print/statistics/transaction-overview`,
    data
  })
}

/**
 * @method GET
 * @desc 系统概况统计
 */
export function getStatisticsSystemOverview() {
  return axios({
    method: "get",
    url: `/api/print/statistics/order-user`
  })
}

/**
 * @method POST
 * @desc 订单是否开票统计
 */
export function getStatisticInvoicing(data: IStatisticsOrderData) {
  return axios({
    method: "post",
    url: `/api/print/statistics/invoke`,
    data
  })
}

/**
 * @method POST
 * @desc 打印工作量统计导出
 */

export function exportStatisticsWorkload(data: IStatisticsWorkloadData) {
  return axios({
    method: "post",
    url: `/api/print/statistics/work-load/export`,
    responseType: "arraybuffer",
    data
  })
}

interface IStatisticTypeData {
  startDate: string | number
  endDate: string | number
  statisticsType: string
  printTypeCode: string
}

// 获取打印方案统计数据
export const getStatisticsPrintTypeApi = (data: IStatisticTypeData) => {
  return axios({
    method: "post",
    url: `/api/print/statistics/type`,
    data: data
  })
}

/* ======================== 打印申请 ======================== */

interface IPrintFileData {
  documentBagWsids: Array<string>
  schemeWsid: string
  sceneApply?: boolean
}

/**
 * @method POST
 * @desc 根据打印类型获取可打印的病案文件
 */
export function getPrintFile(data: IPrintFileData) {
  return axios({
    method: "post",
    url: `/api/document/bag/print-file`,
    data
  })
}

interface IApplyForPrintingData {
  documentBagWsid: string
  inpNo: string
  patientName: string
  papersType: "ID_CARD" | "MEDICAL_CARD" | "HEALTH_CARD" | "PHONE" | "FINGERPRINT"
  idNumber: string
  relationship: RelationshipType
  agentName: string
  agentIdCard: string
  agentPhone: string
  printTypeCode: string
  printTypeName: string
  printPurposeCode: string
  printPurposeName: string
  duplicateNum: number
  documentLists: Array<string>
  printMail?: Record<string, any>
  authimageFile?: Record<string, any>
}

/**
 * @method POST
 * @desc 管理端申请打印
 */
export function applyForPrinting(data: IApplyForPrintingData) {
  return axios({
    method: "post",
    url: `/api/document/print/application`,
    data
  })
}

interface IPrintPatientInfoParam {
  patientName?: string
  patientId?: string
  mrNo?: string
}

/**
 * @method GET
 * @desc 打印查询患者信息-简单条件
 */
export function getPrintPatientInfoApi(params: IPrintPatientInfoParam) {
  return axios({
    method: "get",
    url: `/api/document/bag/query/simple/print-ready`,
    params
  })
}

/**
 * @method GET
 * @desc 打印申请-获取打印申请记录
 */
export function getApplyPrintRecordsApi(inpNo) {
  return axios({
    method: "get",
    url: `/api/print/record/byInpNo`,
    params: { inpNo }
  })
}

/**
 * @method GET
 * @desc 打印申请-获取打印详情
 */
export function getPrintDetailApi(wsid, inpNo) {
  return axios({
    method: "get",
    url: `/api/print/apply/${wsid}/emr/detail`,
    params: { inpNo }
  })
}
