<!-- 图片编辑器 -->
<template>
  <div class="image-editor">
    <!-- 工具栏 -->
    <div v-if="props.toolBar" class="toolbar">
      <div class="toolbar-group">
        <!-- 缩放工具 -->
        <el-button title="放大" @click="zoomIn">
          <el-icon><ZoomIn /></el-icon>
        </el-button>
        <el-button title="缩小" @click="zoomOut">
          <el-icon><ZoomOut /></el-icon>
        </el-button>
        <el-button title="实际大小" @click="actualSize">
          <el-icon><FullScreen /></el-icon>
        </el-button>
        <el-button title="适应宽度" @click="fitWidth">
          <el-icon><ScaleToOriginal /></el-icon>
        </el-button>
      </div>

      <div class="toolbar-group">
        <!-- 旋转工具 -->
        <el-button title="左旋转" @click="rotateLeft">
          <el-icon><RefreshLeft /></el-icon>
        </el-button>
        <el-button title="右旋转" @click="rotateRight">
          <el-icon><RefreshRight /></el-icon>
        </el-button>
      </div>

      <div class="toolbar-group">
        <!-- 裁剪工具 -->
        <el-button title="裁剪" :type="cropMode ? 'primary' : 'default'" @click="toggleCrop">
          <el-icon><Crop /></el-icon>
        </el-button>
      </div>

      <div class="toolbar-group">
        <!-- 调整工具 -->
        <div class="adjustment-controls">
          <div class="adjustment-item">
            <label>亮度</label>
            <el-slider
              v-model="brightness"
              :min="0"
              :max="200"
              :step="1"
              style="width: 120px"
              @change="onBrightnessChange"
            />
            <span class="value">{{ brightness }}%</span>
          </div>
          <div class="adjustment-item">
            <label>对比度</label>
            <el-slider
              v-model="contrast"
              :min="0"
              :max="200"
              :step="1"
              style="width: 120px"
              @change="onContrastChange"
            />
            <span class="value">{{ contrast }}%</span>
          </div>
        </div>
      </div>

      <div class="toolbar-group">
        <!-- 历史操作 -->
        <el-button title="撤销" :disabled="!canUndo" @click="undo">
          <el-icon><Back /></el-icon>
        </el-button>
        <el-button title="恢复" :disabled="!canRedo" @click="redo">
          <el-icon><Right /></el-icon>
        </el-button>
      </div>

      <div class="toolbar-group">
        <!-- 保存 -->
        <el-button type="primary" title="保存" @click="saveImage">
          <el-icon><Check /></el-icon>
          保存
        </el-button>
      </div>
    </div>

    <!-- Canvas显示区域 -->
    <div ref="canvasContainerRef" class="canvas-container">
      <canvas
        v-if="props.imageBase64"
        ref="canvasRef"
        @mousedown="onCanvasMouseDown"
        @mousemove="onCanvasMouseMove"
        @mouseup="onCanvasMouseUp"
        @wheel="onCanvasWheel"
      ></canvas>
      <EmptyContent v-else desc="请选择一张图片" />

      <!-- 裁剪操作按钮 -->
      <div v-if="cropMode" class="crop-actions">
        <el-button type="primary" @click="confirmCrop">确认裁剪</el-button>
        <el-button @click="cancelCrop">取消</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, onUnmounted, onBeforeUnmount, nextTick } from "vue"
import { ElButton, ElSlider, ElIcon, ElMessage } from "element-plus"
import {
  ZoomIn,
  ZoomOut,
  FullScreen,
  ScaleToOriginal,
  RefreshLeft,
  RefreshRight,
  Crop,
  Back,
  Right,
  Check
} from "@element-plus/icons-vue"
import { EmptyContent } from "@/base-components"
import { Message } from "@/utils"

// Props和Emits定义
const props = defineProps<{
  imageBase64: string
  toolBar?: boolean
}>()

const emits = defineEmits<{
  saveImage: [editedBase64: string]
}>()

// 响应式数据
const scale = ref(1)
const rotation = ref(0)
const brightness = ref(100)
const contrast = ref(100)
const cropMode = ref(false)
const isDragging = ref(false)
const isResizing = ref(false)
const resizeHandle = ref("")

// Canvas相关数据
const canvasWidth = ref(800)
const canvasHeight = ref(600)
const offsetX = ref(0)
const offsetY = ref(0)
const originalImage = ref<HTMLImageElement>()

// DOM引用
const canvasRef = ref<HTMLCanvasElement>()
const canvasContainerRef = ref<HTMLDivElement>()

// 裁剪框
const cropBox = reactive({
  x: 50,
  y: 50,
  width: 200,
  height: 200
})

// 历史记录接口
interface HistoryState {
  brightness: number
  contrast: number
  rotation: number
  scale: number
  offsetX: number
  offsetY: number
  canvasWidth: number
  canvasHeight: number
  originalImageSrc?: string // 保存原始图片的base64，用于裁剪撤销
}

// 历史记录
const history = ref<HistoryState[]>([])
const historyIndex = ref(-1)

// 计算属性
const canUndo = computed(() => historyIndex.value > 0)
const canRedo = computed(() => historyIndex.value < history.value.length - 1)

// 保存到历史记录
const saveToHistory = (includeOriginalImage = false) => {
  const state: HistoryState = {
    brightness: brightness.value,
    contrast: contrast.value,
    rotation: rotation.value,
    scale: scale.value,
    offsetX: offsetX.value,
    offsetY: offsetY.value,
    canvasWidth: canvasWidth.value,
    canvasHeight: canvasHeight.value
  }

  // 如果需要保存原始图片（用于裁剪撤销）
  if (includeOriginalImage && originalImage.value) {
    const canvas = document.createElement("canvas")
    const ctx = canvas.getContext("2d")
    if (ctx) {
      canvas.width = originalImage.value.width
      canvas.height = originalImage.value.height
      ctx.drawImage(originalImage.value, 0, 0)
      state.originalImageSrc = canvas.toDataURL("image/jpeg")
    }
  }

  // 删除当前位置之后的历史记录
  history.value = history.value.slice(0, historyIndex.value + 1)
  history.value.push(state)
  historyIndex.value = history.value.length - 1
}

// 重置编辑器状态
const resetEditor = () => {
  scale.value = 1
  rotation.value = 0
  brightness.value = 100
  contrast.value = 100
  cropMode.value = false
  offsetX.value = 0
  offsetY.value = 0
  history.value = []
  historyIndex.value = -1
}

// 初始化Canvas
const initCanvas = () => {
  if (!canvasRef.value || !canvasContainerRef.value) return

  const container = canvasContainerRef.value
  const canvas = canvasRef.value

  // 设置Canvas尺寸
  canvas.width = container.clientWidth
  canvas.height = container.clientHeight

  // 加载原始图片
  if (props.imageBase64) {
    loadImage()
  }
}

// 加载图片
const loadImage = () => {
  return new Promise((resolve, reject) => {
    if (!props.imageBase64) return

    const img = new Image()
    img.onload = () => {
      originalImage.value = img

      // 初始化时以图片较长边为准，适应视窗对应边的80%
      const containerWidth = canvasRef.value?.width || 800
      const containerHeight = canvasRef.value?.height || 600

      const imgWidth = img.width
      const imgHeight = img.height
      const imgRatio = imgWidth / imgHeight

      // 判断图片的较长边
      if (imgWidth >= imgHeight) {
        // 图片宽度较长，以宽度为准适应容器宽度的80%
        canvasWidth.value = containerWidth * 0.8
        canvasHeight.value = canvasWidth.value / imgRatio
      } else {
        // 图片高度较长，以高度为准适应容器高度的80%
        canvasHeight.value = containerHeight * 0.8
        canvasWidth.value = canvasHeight.value * imgRatio
      }

      // 居中显示
      offsetX.value = (containerWidth - canvasWidth.value) / 2
      offsetY.value = (containerHeight - canvasHeight.value) / 2

      drawCanvas()
      resolve(true)
    }
    img.src = props.imageBase64
  })
}

// Canvas绘制函数
const drawCanvas = () => {
  if (!canvasRef.value || !originalImage.value) return

  const canvas = canvasRef.value
  const ctx = canvas.getContext("2d")
  if (!ctx) return

  // 清空Canvas
  ctx.clearRect(0, 0, canvas.width, canvas.height)

  // 保存当前状态
  ctx.save()

  // 移动到图片中心
  const centerX = offsetX.value + canvasWidth.value / 2
  const centerY = offsetY.value + canvasHeight.value / 2
  ctx.translate(centerX, centerY)

  // 应用缩放
  ctx.scale(scale.value, scale.value)

  // 应用旋转
  ctx.rotate((rotation.value * Math.PI) / 180)

  // 应用滤镜
  ctx.filter = `brightness(${brightness.value}%) contrast(${contrast.value}%)`

  // 绘制图片
  ctx.drawImage(
    originalImage.value,
    -canvasWidth.value / 2,
    -canvasHeight.value / 2,
    canvasWidth.value,
    canvasHeight.value
  )

  // 恢复状态
  ctx.restore()

  // 绘制裁剪框
  if (cropMode.value) {
    drawCropBox(ctx)
  }
}

// 绘制裁剪框
const drawCropBox = (ctx: CanvasRenderingContext2D) => {
  ctx.save()

  // 绘制半透明遮罩
  ctx.fillStyle = "rgba(0, 0, 0, 0.5)"
  ctx.fillRect(0, 0, canvasRef.value!.width, canvasRef.value!.height)

  // 清除裁剪区域
  ctx.globalCompositeOperation = "destination-out"
  ctx.fillRect(cropBox.x, cropBox.y, cropBox.width, cropBox.height)

  // 绘制裁剪框边框
  ctx.globalCompositeOperation = "source-over"
  ctx.strokeStyle = "#409eff"
  ctx.lineWidth = 2
  ctx.setLineDash([5, 5])
  ctx.strokeRect(cropBox.x, cropBox.y, cropBox.width, cropBox.height)

  // 绘制调整手柄
  drawCropHandles(ctx)

  ctx.restore()
}

// 绘制裁剪框手柄
const drawCropHandles = (ctx: CanvasRenderingContext2D) => {
  const handleSize = 8
  const handles = [
    { x: cropBox.x - handleSize / 2, y: cropBox.y - handleSize / 2 }, // 左上
    { x: cropBox.x + cropBox.width - handleSize / 2, y: cropBox.y - handleSize / 2 }, // 右上
    { x: cropBox.x - handleSize / 2, y: cropBox.y + cropBox.height - handleSize / 2 }, // 左下
    { x: cropBox.x + cropBox.width - handleSize / 2, y: cropBox.y + cropBox.height - handleSize / 2 }, // 右下
    { x: cropBox.x + cropBox.width / 2 - handleSize / 2, y: cropBox.y - handleSize / 2 }, // 上中
    { x: cropBox.x + cropBox.width / 2 - handleSize / 2, y: cropBox.y + cropBox.height - handleSize / 2 }, // 下中
    { x: cropBox.x - handleSize / 2, y: cropBox.y + cropBox.height / 2 - handleSize / 2 }, // 左中
    { x: cropBox.x + cropBox.width - handleSize / 2, y: cropBox.y + cropBox.height / 2 - handleSize / 2 } // 右中
  ]

  ctx.fillStyle = "#409eff"
  ctx.strokeStyle = "#fff"
  ctx.lineWidth = 1
  ctx.setLineDash([])

  handles.forEach(handle => {
    ctx.fillRect(handle.x, handle.y, handleSize, handleSize)
    ctx.strokeRect(handle.x, handle.y, handleSize, handleSize)
  })
}

// 缩放功能
const zoomIn = () => {
  if (!props.imageBase64) return Message.warning("图片数据为空，操作失败")

  scale.value = Math.min(scale.value * 1.2, 5)
  drawCanvas()
  saveToHistory()
}

const zoomOut = () => {
  if (!props.imageBase64) return Message.warning("图片数据为空，操作失败")
  scale.value = Math.max(scale.value / 1.2, 0.1)
  drawCanvas()
  saveToHistory()
}

const actualSize = () => {
  if (!props.imageBase64) return Message.warning("图片数据为空，操作失败")

  if (!originalImage.value || !canvasRef.value) return

  // 显示图片实际大小，只改变预览显示，不改变保存的图片尺寸
  const containerWidth = canvasRef.value.width
  const containerHeight = canvasRef.value.height

  canvasWidth.value = originalImage.value.width
  canvasHeight.value = originalImage.value.height

  // 初始居中显示，但允许用户拖拽查看超出部分
  offsetX.value = (containerWidth - canvasWidth.value) / 2
  offsetY.value = (containerHeight - canvasHeight.value) / 2

  scale.value = 1
  drawCanvas()
  saveToHistory()
  ElMessage.success("已调整为实际大小预览")
}

const fitWidth = () => {
  if (!props.imageBase64) return Message.warning("图片数据为空，操作失败")

  if (!originalImage.value || !canvasRef.value) return

  // 适应宽度显示 - 只改变预览显示，不改变保存的图片尺寸
  const containerWidth = canvasRef.value.width
  const containerHeight = canvasRef.value.height

  // 根据旋转角度计算有效的宽高（旋转后的实际显示尺寸）
  const isRotated = rotation.value % 180 !== 0
  let effectiveWidth: number, effectiveHeight: number

  if (isRotated) {
    // 旋转90度或270度时，宽高互换
    effectiveWidth = originalImage.value.height
    effectiveHeight = originalImage.value.width
  } else {
    // 0度或180度时，保持原始宽高
    effectiveWidth = originalImage.value.width
    effectiveHeight = originalImage.value.height
  }

  // 使用旋转后的有效宽高比例来计算适应宽度（视窗宽度的80%）
  const imgRatio = effectiveWidth / effectiveHeight
  const targetWidth = containerWidth * 0.8 // 视窗宽度的80%
  const targetHeight = targetWidth / imgRatio

  // 更新显示尺寸（这里设置的是原始图片的显示尺寸，不是旋转后的）
  if (isRotated) {
    // 如果旋转了，需要将计算出的尺寸反向设置
    canvasWidth.value = targetHeight // 旋转后宽度对应原始高度
    canvasHeight.value = targetWidth // 旋转后高度对应原始宽度
  } else {
    canvasWidth.value = targetWidth
    canvasHeight.value = targetHeight
  }

  // 初始居中显示，但允许用户拖拽
  offsetX.value = (containerWidth - canvasWidth.value) / 2
  offsetY.value = (containerHeight - canvasHeight.value) / 2

  scale.value = 1
  drawCanvas()
  saveToHistory()
  ElMessage.success("已适应宽度预览")
}

// 旋转功能
const rotateLeft = () => {
  if (!props.imageBase64) return Message.warning("图片数据为空，操作失败")

  rotation.value = (rotation.value - 90) % 360
  drawCanvas()
  saveToHistory()
  autoSave()
}

const rotateRight = () => {
  if (!props.imageBase64) return Message.warning("图片数据为空，操作失败")

  rotation.value = (rotation.value + 90) % 360
  drawCanvas()
  saveToHistory()
  autoSave()
}

// 亮度和对比度调整
const onBrightnessChange = () => {
  if (!props.imageBase64) return Message.warning("图片数据为空，操作失败")

  drawCanvas()
  saveToHistory()
  autoSave()
}

function changeBrightness(bright) {
  brightness.value = bright
  onBrightnessChange()
}

const onContrastChange = () => {
  if (!props.imageBase64) return Message.warning("图片数据为空，操作失败")

  drawCanvas()
  saveToHistory()
  autoSave()
}

// 修改对比度
function changeContrast(con) {
  contrast.value = con
  onContrastChange()
}

// 撤销和恢复
const undo = () => {
  if (canUndo.value) {
    historyIndex.value--
    restoreFromHistory()
  }
}

const redo = () => {
  if (canRedo.value) {
    historyIndex.value++
    restoreFromHistory()
  }
}

const restoreFromHistory = () => {
  const state = history.value[historyIndex.value]
  if (state) {
    brightness.value = state.brightness
    contrast.value = state.contrast
    rotation.value = state.rotation
    scale.value = state.scale
    offsetX.value = state.offsetX
    offsetY.value = state.offsetY
    canvasWidth.value = state.canvasWidth
    canvasHeight.value = state.canvasHeight

    // 如果历史记录中包含原始图片，恢复它（用于裁剪撤销）
    if (state.originalImageSrc) {
      const img = new Image()
      img.onload = () => {
        originalImage.value = img
        drawCanvas()
        autoSave()
      }
      img.src = state.originalImageSrc
    } else {
      drawCanvas()
      autoSave()
    }
  }
}

// Canvas事件处理
let mouseStartX = 0
let mouseStartY = 0
let cropStartX = 0
let cropStartY = 0
let cropStartWidth = 0
let cropStartHeight = 0
let imageStartOffsetX = 0
let imageStartOffsetY = 0
const isDraggingImage = ref(false)

const onCanvasMouseDown = (event: MouseEvent) => {
  if (!canvasRef.value) return

  const rect = canvasRef.value.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  mouseStartX = x
  mouseStartY = y

  if (cropMode.value) {
    // 裁剪模式下的交互
    const handle = getResizeHandle(x, y)
    if (handle) {
      isResizing.value = true
      resizeHandle.value = handle
      cropStartX = cropBox.x
      cropStartY = cropBox.y
      cropStartWidth = cropBox.width
      cropStartHeight = cropBox.height
    } else if (x >= cropBox.x && x <= cropBox.x + cropBox.width && y >= cropBox.y && y <= cropBox.y + cropBox.height) {
      // 点击在裁剪框内，开始拖拽裁剪框
      isDragging.value = true
      cropStartX = cropBox.x
      cropStartY = cropBox.y
    }
    // 在裁剪模式下，点击裁剪框外的区域不允许拖拽图片
  } else {
    // 非裁剪模式下，直接拖拽图片
    isDraggingImage.value = true
    imageStartOffsetX = offsetX.value
    imageStartOffsetY = offsetY.value
  }
}

// 获取鼠标点击的调整手柄
const getResizeHandle = (x: number, y: number): string => {
  const handleSize = 8
  const tolerance = handleSize / 2

  const handles = [
    { name: "nw", x: cropBox.x - tolerance, y: cropBox.y - tolerance },
    { name: "ne", x: cropBox.x + cropBox.width - tolerance, y: cropBox.y - tolerance },
    { name: "sw", x: cropBox.x - tolerance, y: cropBox.y + cropBox.height - tolerance },
    { name: "se", x: cropBox.x + cropBox.width - tolerance, y: cropBox.y + cropBox.height - tolerance },
    { name: "n", x: cropBox.x + cropBox.width / 2 - tolerance, y: cropBox.y - tolerance },
    { name: "s", x: cropBox.x + cropBox.width / 2 - tolerance, y: cropBox.y + cropBox.height - tolerance },
    { name: "w", x: cropBox.x - tolerance, y: cropBox.y + cropBox.height / 2 - tolerance },
    { name: "e", x: cropBox.x + cropBox.width - tolerance, y: cropBox.y + cropBox.height / 2 - tolerance }
  ]

  for (const handle of handles) {
    if (x >= handle.x && x <= handle.x + handleSize && y >= handle.y && y <= handle.y + handleSize) {
      return handle.name
    }
  }

  return ""
}

const onCanvasMouseMove = (event: MouseEvent) => {
  if (!canvasRef.value) return

  const rect = canvasRef.value.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  if (isDraggingImage.value) {
    // 拖拽图片
    const deltaX = x - mouseStartX
    const deltaY = y - mouseStartY

    offsetX.value = imageStartOffsetX + deltaX
    offsetY.value = imageStartOffsetY + deltaY

    drawCanvas()
  } else if (cropMode.value) {
    if (isDragging.value) {
      // 拖拽裁剪框
      const deltaX = x - mouseStartX
      const deltaY = y - mouseStartY

      cropBox.x = cropStartX + deltaX
      cropBox.y = cropStartY + deltaY

      // 限制裁剪框在Canvas范围内
      cropBox.x = Math.max(0, Math.min(cropBox.x, canvasRef.value.width - cropBox.width))
      cropBox.y = Math.max(0, Math.min(cropBox.y, canvasRef.value.height - cropBox.height))

      drawCanvas()
    } else if (isResizing.value) {
      // 调整裁剪框大小
      const deltaX = x - mouseStartX
      const deltaY = y - mouseStartY

      const newCropBox = { ...cropBox }

      switch (resizeHandle.value) {
        case "nw":
          newCropBox.x = cropStartX + deltaX
          newCropBox.y = cropStartY + deltaY
          newCropBox.width = cropStartWidth - deltaX
          newCropBox.height = cropStartHeight - deltaY
          break
        case "ne":
          newCropBox.y = cropStartY + deltaY
          newCropBox.width = cropStartWidth + deltaX
          newCropBox.height = cropStartHeight - deltaY
          break
        case "sw":
          newCropBox.x = cropStartX + deltaX
          newCropBox.width = cropStartWidth - deltaX
          newCropBox.height = cropStartHeight + deltaY
          break
        case "se":
          newCropBox.width = cropStartWidth + deltaX
          newCropBox.height = cropStartHeight + deltaY
          break
        case "n":
          newCropBox.y = cropStartY + deltaY
          newCropBox.height = cropStartHeight - deltaY
          break
        case "s":
          newCropBox.height = cropStartHeight + deltaY
          break
        case "w":
          newCropBox.x = cropStartX + deltaX
          newCropBox.width = cropStartWidth - deltaX
          break
        case "e":
          newCropBox.width = cropStartWidth + deltaX
          break
      }

      // 确保裁剪框不会太小
      if (newCropBox.width >= 20 && newCropBox.height >= 20) {
        // 限制在Canvas范围内
        newCropBox.x = Math.max(0, Math.min(newCropBox.x, canvasRef.value.width - newCropBox.width))
        newCropBox.y = Math.max(0, Math.min(newCropBox.y, canvasRef.value.height - newCropBox.height))
        newCropBox.width = Math.min(newCropBox.width, canvasRef.value.width - newCropBox.x)
        newCropBox.height = Math.min(newCropBox.height, canvasRef.value.height - newCropBox.y)

        Object.assign(cropBox, newCropBox)
        drawCanvas()
      }
    }
  }
}

const onCanvasMouseUp = () => {
  // 如果刚才在拖拽图片，保存到历史记录
  if (isDraggingImage.value) {
    saveToHistory()
    autoSave()
  }

  // 如果刚才在拖拽或调整裁剪框，也保存到历史记录
  if (isDragging.value || isResizing.value) {
    saveToHistory()
  }

  isDragging.value = false
  isResizing.value = false
  isDraggingImage.value = false
  resizeHandle.value = ""
}

const onCanvasWheel = (event: WheelEvent) => {
  // 禁用滚轮缩放，只允许通过按钮缩放
  event.preventDefault()
}

// 裁剪功能
const toggleCrop = () => {
  if (!props.imageBase64) return Message.warning("图片数据为空，操作失败")

  cropMode.value = !cropMode.value
  if (cropMode.value && canvasRef.value && originalImage.value) {
    // 初始化裁剪框位置和大小
    const canvas = canvasRef.value

    // 计算当前显示的图片实际尺寸（考虑缩放和旋转）
    const scaledWidth = canvasWidth.value * scale.value
    const scaledHeight = canvasHeight.value * scale.value

    // 根据旋转角度决定实际显示尺寸
    const isRotated = rotation.value % 180 !== 0
    const displayWidth = isRotated ? scaledHeight : scaledWidth
    const displayHeight = isRotated ? scaledWidth : scaledHeight

    // 计算图片在Canvas中的位置
    const centerX = offsetX.value + canvasWidth.value / 2
    const centerY = offsetY.value + canvasHeight.value / 2
    const imageLeft = centerX - displayWidth / 2
    const imageTop = centerY - displayHeight / 2
    const imageRight = centerX + displayWidth / 2
    const imageBottom = centerY + displayHeight / 2

    // 计算Canvas的80%尺寸
    const maxCropWidth = canvas.width * 0.8
    const maxCropHeight = canvas.height * 0.8

    // 计算图片在Canvas中的可见区域
    const visibleLeft = Math.max(0, imageLeft)
    const visibleTop = Math.max(0, imageTop)
    const visibleRight = Math.min(canvas.width, imageRight)
    const visibleBottom = Math.min(canvas.height, imageBottom)
    const visibleWidth = visibleRight - visibleLeft
    const visibleHeight = visibleBottom - visibleTop

    // 确定裁剪框的尺寸：取Canvas 80%和图片可见区域的较小值
    const cropWidth = Math.min(maxCropWidth, visibleWidth)
    const cropHeight = Math.min(maxCropHeight, visibleHeight)

    // 确保裁剪框不会太小
    const finalCropWidth = Math.max(cropWidth, 50)
    const finalCropHeight = Math.max(cropHeight, 50)

    // 设置裁剪框位置（居中）
    cropBox.x = (canvas.width - finalCropWidth) / 2
    cropBox.y = (canvas.height - finalCropHeight) / 2
    cropBox.width = finalCropWidth
    cropBox.height = finalCropHeight

    drawCanvas()
  } else {
    drawCanvas()
  }
}

const cancelCrop = () => {
  cropMode.value = false
  isDragging.value = false
  isResizing.value = false
  isDraggingImage.value = false
  drawCanvas() // 重新绘制Canvas以移除裁剪框和蒙层
}

const confirmCrop = async () => {
  if (!props.imageBase64 || !originalImage.value || !canvasRef.value) return

  try {
    // 先保存当前状态到历史记录（裁剪前的状态，包含原始图片）
    saveToHistory(true)

    // 计算预览尺寸与实际尺寸的比例
    const previewToActualRatioX = originalImage.value.width / canvasWidth.value
    const previewToActualRatioY = originalImage.value.height / canvasHeight.value

    // 计算实际裁剪区域在原图中的位置和尺寸
    const centerX = offsetX.value + canvasWidth.value / 2
    const centerY = offsetY.value + canvasHeight.value / 2

    // 计算裁剪框相对于图片中心的位置（预览坐标）
    const cropCenterX = cropBox.x + cropBox.width / 2
    const cropCenterY = cropBox.y + cropBox.height / 2
    const cropOffsetX = cropCenterX - centerX
    const cropOffsetY = cropCenterY - centerY

    // 考虑缩放因子：裁剪框的偏移需要除以缩放因子来得到真实的图片坐标偏移
    const scaledCropOffsetX = cropOffsetX / scale.value
    const scaledCropOffsetY = cropOffsetY / scale.value

    // 转换为实际图片坐标
    const actualCropOffsetX = scaledCropOffsetX * previewToActualRatioX
    const actualCropOffsetY = scaledCropOffsetY * previewToActualRatioY
    const actualCropWidth = (cropBox.width / scale.value) * previewToActualRatioX
    const actualCropHeight = (cropBox.height / scale.value) * previewToActualRatioY

    // 计算实际裁剪区域在原图中的位置
    const actualCropX = originalImage.value.width / 2 + actualCropOffsetX - actualCropWidth / 2
    const actualCropY = originalImage.value.height / 2 + actualCropOffsetY - actualCropHeight / 2

    // 创建高分辨率的临时画布来渲染完整的变换后图片
    const tempCanvas = document.createElement("canvas")
    const tempCtx = tempCanvas.getContext("2d")
    if (!tempCtx) return

    // 根据旋转角度决定临时画布尺寸（使用实际尺寸）
    const isRotated = rotation.value % 180 !== 0
    const actualWidth = originalImage.value.width
    const actualHeight = originalImage.value.height
    const tempCanvasWidth = isRotated ? actualHeight : actualWidth
    const tempCanvasHeight = isRotated ? actualWidth : actualHeight

    tempCanvas.width = tempCanvasWidth
    tempCanvas.height = tempCanvasHeight

    // 应用滤镜效果到临时画布
    tempCtx.filter = `brightness(${brightness.value}%) contrast(${contrast.value}%)`

    // 在临时画布上绘制完整的变换后图片（使用实际尺寸）
    if (rotation.value !== 0) {
      tempCtx.save()
      tempCtx.translate(tempCanvasWidth / 2, tempCanvasHeight / 2)
      tempCtx.rotate((rotation.value * Math.PI) / 180)
      tempCtx.drawImage(originalImage.value, -actualWidth / 2, -actualHeight / 2, actualWidth, actualHeight)
      tempCtx.restore()
    } else {
      tempCtx.drawImage(originalImage.value, 0, 0, actualWidth, actualHeight)
    }

    // 创建最终裁剪画布（使用实际裁剪尺寸）
    const canvas = document.createElement("canvas")
    const ctx = canvas.getContext("2d")
    if (!ctx) return

    canvas.width = actualCropWidth
    canvas.height = actualCropHeight

    // 计算在变换后图片中的裁剪位置
    let cropSourceX: number, cropSourceY: number
    if (isRotated) {
      // 旋转后需要调整坐标
      cropSourceX = tempCanvasWidth / 2 + actualCropOffsetX - actualCropWidth / 2
      cropSourceY = tempCanvasHeight / 2 + actualCropOffsetY - actualCropHeight / 2
    } else {
      cropSourceX = actualCropX
      cropSourceY = actualCropY
    }

    // 从变换后的高分辨率图片中裁剪
    ctx.drawImage(
      tempCanvas,
      cropSourceX,
      cropSourceY,
      actualCropWidth,
      actualCropHeight,
      0,
      0,
      actualCropWidth,
      actualCropHeight
    )

    const croppedBase64 = canvas.toDataURL("image/jpeg")

    // 更新原图为裁剪后的图片
    const newImg = new Image()
    newImg.onload = () => {
      originalImage.value = newImg

      // 重新计算Canvas尺寸
      const containerWidth = canvasRef.value?.width || 800
      const containerHeight = canvasRef.value?.height || 600

      const imgRatio = newImg.width / newImg.height
      const containerRatio = containerWidth / containerHeight

      if (imgRatio > containerRatio) {
        canvasWidth.value = Math.min(containerWidth * 0.8, newImg.width)
        canvasHeight.value = canvasWidth.value / imgRatio
      } else {
        canvasHeight.value = Math.min(containerHeight * 0.8, newImg.height)
        canvasWidth.value = canvasHeight.value * imgRatio
      }

      offsetX.value = (containerWidth - canvasWidth.value) / 2
      offsetY.value = (containerHeight - canvasHeight.value) / 2

      // 重置编辑状态
      rotation.value = 0
      brightness.value = 100
      contrast.value = 100
      scale.value = 1
      cropMode.value = false

      drawCanvas()

      // 保存裁剪后的状态到历史记录
      saveToHistory()

      // 触发自动保存
      autoSave()

      emits("saveImage", croppedBase64)
      ElMessage.success("裁剪完成")
    }
    newImg.src = croppedBase64
  } catch (error) {
    ElMessage.error("裁剪失败")
  }
}

// 自动保存和手动保存功能
const autoSave = () => {
  if (!originalImage.value || !canvasRef.value) return Promise.resolve()

  return getCurrentImageData().then(finalBase64 => {
    emits("saveImage", finalBase64)
    return finalBase64
  })
}

const saveImage = () => {
  if (!props.imageBase64) return Message.error("无法保存，原始图片为空")
  if (!originalImage.value || !canvasRef.value) return

  getCurrentImageData().then(finalBase64 => {
    emits("saveImage", finalBase64)
    ElMessage.success("保存成功")
  })
}

// 获取当前编辑后的图片数据
const getCurrentImageData = (): Promise<string> => {
  return new Promise(resolve => {
    if (!originalImage.value || !canvasRef.value) {
      resolve(props.imageBase64 || "")
      return
    }

    const canvas = document.createElement("canvas")
    const ctx = canvas.getContext("2d")
    if (!ctx) {
      resolve(props.imageBase64 || "")
      return
    }

    // 使用图片实际大小，而不是预览大小
    const actualWidth = originalImage.value.width
    const actualHeight = originalImage.value.height

    // 根据旋转角度决定画布尺寸
    const isRotated = rotation.value % 180 !== 0
    const finalWidth = isRotated ? actualHeight : actualWidth
    const finalHeight = isRotated ? actualWidth : actualHeight

    canvas.width = finalWidth
    canvas.height = finalHeight

    // 应用滤镜效果
    ctx.filter = `brightness(${brightness.value}%) contrast(${contrast.value}%)`

    // 处理旋转
    if (rotation.value !== 0) {
      ctx.save()
      ctx.translate(finalWidth / 2, finalHeight / 2)
      ctx.rotate((rotation.value * Math.PI) / 180)
      ctx.drawImage(originalImage.value, -actualWidth / 2, -actualHeight / 2, actualWidth, actualHeight)
      ctx.restore()
    } else {
      ctx.drawImage(originalImage.value, 0, 0, actualWidth, actualHeight)
    }

    resolve(canvas.toDataURL("image/jpeg"))
  })
}

// 组件挂载时初始化
onMounted(() => {
  initCanvas()
  // 监听窗口大小变化
  window.addEventListener("resize", initCanvas)
})

onUnmounted(() => {
  window.removeEventListener("resize", initCanvas)
})

// 监听图片变化
watch(
  () => props.imageBase64,
  () => {
    if (props.imageBase64) {
      resetEditor()
      nextTick(() => {
        initCanvas()
        loadImage().then(() => {
          saveToHistory()
        })
      })
    }
  },
  { immediate: true }
)

// 暴露方法给父组件
defineExpose({
  undo,
  redo,
  zoomIn,
  zoomOut,
  actualSize,
  fitWidth,
  rotateLeft,
  rotateRight,
  toggleCrop,
  saveImage,
  changeBrightness,
  changeContrast,
  canUndo,
  canRedo
})
</script>

<style lang="less" scoped>
.image-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background: #f5f5f5;

  .toolbar {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 12px 16px;
    background: white;
    border-bottom: 1px solid #e0e0e0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    flex-wrap: wrap;

    .toolbar-group {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 0 8px;
      border-right: 1px solid #e0e0e0;

      &:last-child {
        border-right: none;
      }

      .adjustment-controls {
        display: flex;
        gap: 20px;

        .adjustment-item {
          display: flex;
          align-items: center;
          gap: 8px;
          min-width: 200px;

          label {
            font-size: 12px;
            color: #666;
            min-width: 40px;
          }

          .value {
            font-size: 12px;
            color: #333;
            min-width: 35px;
            text-align: right;
          }
        }
      }
    }
  }

  .canvas-container {
    flex: 1;
    overflow: hidden;
    position: relative;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;

    canvas {
      max-width: 100%;
      max-height: 100%;
      cursor: grab;

      &:active {
        cursor: grabbing;
      }
    }

    .crop-actions {
      position: absolute;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      gap: 8px;
      background: white;
      padding: 8px;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      z-index: 12;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .image-editor {
    .toolbar {
      flex-direction: column;
      align-items: stretch;
      gap: 8px;

      .toolbar-group {
        justify-content: center;
        border-right: none;
        border-bottom: 1px solid #e0e0e0;
        padding: 8px 0;

        &:last-child {
          border-bottom: none;
        }

        .adjustment-controls {
          flex-direction: column;
          gap: 12px;

          .adjustment-item {
            min-width: auto;
            justify-content: space-between;
          }
        }
      }
    }
  }
}
</style>
