<!-- 根据路由切换页面的标签页组件 -->
<template>
  <div class="custom-tabs-header">
    <RouterLink v-for="item in props.tabsConfig" :key="item.path" :to="item.path">{{ item.label }}</RouterLink>
  </div>
</template>

<script setup lang="ts">
type TabConfigItem = { label: string; path: string }

const props = defineProps<{
  tabsConfig: Array<TabConfigItem>
}>()
</script>

<style lang="less" scoped>
.custom-tabs-header {
  font-size: 14px;
  background-color: white;
  border-bottom: 1px solid #ebedf0;
  display: flex;

  a {
    height: 33px;
    line-height: 33px;
    box-sizing: border-box;
    margin-right: 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .router-link-active {
    font-size: 1rem;
    color: var(--el-color-primary);
  }
  .router-link-active:after {
    content: "";
    width: 50%;
    border: var(--el-color-primary) solid 1px;
  }
}
</style>
