<template>
  <PageContainer separate>
    <template #search>
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <CommonSelectFormItem
          v-model="unusedSearchState.systemWsid"
          label="厂商系统"
          :options="systemVendorOptions"
          @change="handleSystemNameChange"
        />
        <CommonSelectFormItem v-model="searchFormState.scene" label="业务类型" :options="sceneOptions" />
        <CommonSelectFormItem v-model="searchFormState.dataType" label="数据类型" :options="dataTypeOptions" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="tableRef"
        :table-columns="tableColumns"
        :request-api="getDataReceptionListApi"
        :request-params="searchParams"
      >
        <template #header>
          <AddButton @click="showAddForm">新增</AddButton>
        </template>
        <template #scene="{ row }">
          {{ sceneOptions.find(option => option.value === row.scene)?.label || "--" }}
        </template>
        <template #dataType="{ row }">
          {{ dataTypeOptions.find(option => option.value === row.dataType)?.label || "--" }}
        </template>
        <template #operation="{ row }">
          <TableButton @click="showEditForm(row)">编辑</TableButton>
          <TableButton @click="confirmDelete(row, row.id)">删除</TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>

  <DialogContainer
    v-model:visible="visible"
    :title="actionType === 'edit' ? '修改数据接收配置' : '添加数据接收配置'"
    :width="1000"
  >
    <el-form
      ref="formRef"
      label-position="right"
      label-width="145px"
      label-suffix="："
      :model="formState"
      :rules="formRules"
    >
      <CommonSelectFormItem
        v-model="formState.systemWsid"
        label="厂商系统"
        prop="systemWsid"
        :options="systemVendorOptions"
      />

      <CommonSelectFormItem v-model="formState.scene" label="业务类型" prop="scene" :options="sceneOptions" />

      <CommonSelectFormItem
        v-model="formState.dataType"
        label="数据类型"
        prop="dataType"
        :disabled="!formState.scene"
        :options="availableDataTypeOptions"
      />

      <el-form-item ref="largeScreen" label="数据清洗" prop="sourceCode" class="data-clean">
        <Codemirror
          v-model="formState.sourceCode"
          placeholder="请输入"
          :tab-size="10"
          :extensions="dataExtensions"
          :style="{
            maxHeight: isFullscreen ? '100vh' : '150px',
            height: isFullscreen ? '100vh' : 'auto',
            width: '100%'
          }"
        />
        <div ref="fullScreen" class="fullscreen-btn">
          <i v-if="!isFullscreen" title="全屏显示" class="ri-fullscreen-line" @click="toggle"></i>
          <i v-else title="退出全屏" class="ri-fullscreen-exit-line" @click="toggle"></i>
        </div>
      </el-form-item>

      <!-- 配置描述textarea -->
      <el-form-item label="配置描述" prop="remark">
        <el-input
          v-model="formState.remark"
          type="textarea"
          :rows="4"
          placeholder="请输入配置描述"
          :maxlength="200"
          show-word-limit
          clearable
        ></el-input>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button :loading="actionLoading" @click="closeFormDialog()">取消</el-button>
      <el-button :loading="actionLoading" type="primary" @click="handleSave">保存</el-button>
    </template>
  </DialogContainer>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from "vue"
import { java } from "@codemirror/lang-java"
import { useFullscreen } from "@vueuse/core"
import { Codemirror } from "vue-codemirror"
import {
  PageContainer,
  DialogContainer,
  CommonTable,
  AddButton,
  TableButton,
  CommonSelectFormItem,
  SearchContainer
} from "@/base-components"
import { useTableSearch, useCommonOptions } from "@/hooks"
import useFormSetting from "@/hooks/useFormSetting_v2"
import useTableOperation from "@/hooks/useTableOperation_v2"
import { SystemPrompt } from "@/utils"
import { getCollectSystemList } from "../collect-way/interface"
import { tableColumns, myTheme, dataTypeOptions, formRules, sceneOptions } from "./config"
import {
  getDataReceptionListApi,
  addDataReceptionApi,
  updateDataReceptionApi,
  deleteDataReceptionApi
} from "./interface"
import type { FormInstance } from "element-plus"

/* ======================== 页面加载时获取选项 ======================== */

// 获取所有厂商系统选项
const { options: systemVendorOptions } = useCommonOptions({
  getOptionsApi: getCollectSystemList,
  labelAlias: "systemName",
  valueAlias: "wsid"
})

// 获取可用的数据类型选项- 业务类型是住院全部展示,其他只展示患者和文书
const availableDataTypeOptions = computed(() => {
  if (formState.scene === "IN_HOSPITAL") {
    return dataTypeOptions
  } else {
    return dataTypeOptions.filter(option => option.value === "PATIENT" || option.value === "DOCUMENT")
  }
})

/* ======================== 搜索 ======================== */

const tableRef = ref<InstanceType<typeof CommonTable>>()

const unusedSearchState = reactive({
  systemWsid: ""
})

const searchFormState = reactive({
  systemName: "",
  scene: "",
  dataType: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

const handleSystemNameChange = (val: string) => {
  searchFormState.systemName = systemVendorOptions.value.find(item => item.value === val)?.label || ""
}

/* ======================== 行操作 ======================== */

const showAddForm = () => {
  // changeDataType()
  showForm("add")
}

const showEditForm = row => {
  // changeDataType()
  showForm("edit", row)
}

/* ======================== 表格 ======================== */

const formRef = ref<FormInstance>()
const testLoading = ref(false)

const formInitialValue = {
  id: "",
  scene: "", // 业务类型
  systemWsid: "", // 厂商系统
  dataType: "", // 数据类型
  sourceCode: "", // 数据清洗
  remark: "" // 备注
}

const { visible, actionType, formState, showForm, closeForm } = useFormSetting(formInitialValue)

const { confirmAdd, confirmEdit, confirmDelete, actionLoading } = useTableOperation(formState, {
  apiGroup: {
    addApi: addDataReceptionApi,
    editApi: updateDataReceptionApi,
    deleteApi: deleteDataReceptionApi
  },
  closeForm: closeFormDialog,
  deleteConfirmation: () => SystemPrompt(`您确定要删除该数据吗？`),
  callback: () => tableRef.value?.refreshTableData()
})

const handleSave = () => {
  formRef.value?.validate(valid => {
    if (!valid) return
    actionType.value === "edit" ? confirmEdit() : confirmAdd()
  })
}

// 关闭编辑弹窗
function closeFormDialog() {
  closeForm()
  testLoading.value = false
}

/* ======================== 代码块 ======================== */

// 数据清洗
const dataExtensions = [java(), myTheme]

const largeScreen = ref(null)
const fullScreen = ref(null)
const { toggle, isFullscreen } = useFullscreen(largeScreen)
</script>

<style lang="less" scoped>
:deep(.cm-editor) {
  border: 1px solid #dcdfe6;
}

:deep(.cm-focused) {
  outline: none !important;
}

:deep(.cm-lineNumbers) {
  border-right: 1px solid #dcdfe6;
}

.data-clean {
  :deep(.el-form-item__content) {
    align-items: baseline;
    position: relative;
  }
}

.fullscreen-btn {
  position: absolute;
  cursor: pointer;
  right: 10px;
  top: 15px;
  line-height: 1;
  transform: translateY(-50%);
  i {
    opacity: 0.5;
    font-size: 20px;
    color: #333;
  }
  &:hover {
    i {
      opacity: 1;
    }
  }
}
</style>
