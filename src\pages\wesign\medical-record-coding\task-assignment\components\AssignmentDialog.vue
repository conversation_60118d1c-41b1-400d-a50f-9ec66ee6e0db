<template>
  <!-- 分配编目任务/重新分配 -->
  <DialogContainer
    v-model:visible="visible"
    :title="assignmentTitle"
    :width="assignmentType === 'USER_DEPT' ? 1000 : 448"
    :close-on-click-modal="false"
    :cancel-callback="close"
    :confirm-callback="confirmAssign"
  >
    <!-- 手动分配 -->
    <template v-if="assignmentType === 'MANUAL'">
      <el-input
        v-model="filterTableKey"
        style="max-width: 600px; margin-bottom: 10px"
        placeholder="工号/姓名"
        class="input-with-select"
        :prefix-icon="Search"
        @input="filterTableData"
      />

      <el-table
        :data="manualTableData"
        style="width: 100%"
        max-height="350"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" :selectable="row => !row.holiday" />
        <el-table-column prop="jobId" label="工号" width="150">
          <template #default="scope">
            <span>{{ scope.row.jobId }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="catalogerName" label="编码员" width="200">
          <template #default="scope">
            <span style="margin-right: 10px">{{ scope.row.catalogerName }}</span>
            <el-tag v-if="scope.row.holiday" type="warning" effect="dark">已休假</el-tag>
          </template>
        </el-table-column>
      </el-table>
    </template>

    <!-- 按数量分配 -->
    <template v-if="assignmentType === 'AUTO_ASSIGNMENT'">
      <el-form-item class="assignment-item" label="数量超过：">
        <el-input v-model="autoAssignmentThreshold" type="number" min="0">
          <template #append>份</template>
        </el-input>
      </el-form-item>
    </template>

    <!-- 按科室分配 -->
    <template v-if="assignmentType === 'USER_DEPT'">
      <BaseTable :data="deptTableData" :columns="userDeptColumns" border style="width: 900px">
        <template #catalogerName="{ row }">
          <span style="margin-right: 10px">{{ row.catalogerName }}</span>
          <el-tag v-if="row.holiday" type="warning" effect="dark">已休假</el-tag>
        </template>
        <template #deptWsids="{ row }">
          <el-select
            v-model="row.deptWsids"
            multiple
            clearable
            filterable
            placeholder="请选择科室"
            style="width: 100%"
            @visible-change="value => handleSelect(value, row)"
          >
            <el-option
              v-for="item in row.departmentOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
      </BaseTable>
    </template>
  </DialogContainer>
</template>

<script setup lang="ts">
import { computed, ref } from "vue"
import { cloneDeep } from "lodash-es"
import { Search } from "@element-plus/icons-vue"
import { BaseTable, DialogContainer } from "@/base-components"
import { useSystemStore } from "@/stores"
import { Message, toastError } from "@/utils"
import {
  configureCatalogParameters,
  getCatalogConfigInfo,
  reassignCatalog,
  manualAssignOutpatientApi,
  type RangeType
} from "../../interface"
import { userDeptColumns } from "../config"

const systemStore = useSystemStore()

const props = defineProps<{ selectedRows: Record<string, any>[]; rangeType: RangeType }>()

const emits = defineEmits(["success"])

/* =================== 初始化和提交 ==================== */

type AssignmentType = "MANUAL" | "AUTO_ASSIGNMENT" | "USER_DEPT"

let originDepartmentOptions: Record<string, any>[] = [] // 原始科室数据

const assignmentType = ref<AssignmentType>("MANUAL") // 分配方式
const assignmentTitle = computed(() => {
  if (assignmentType.value === "USER_DEPT") {
    return "按科室分配"
  } else if (assignmentType.value === "AUTO_ASSIGNMENT") {
    return "按数量分配"
  } else {
    return "手动分配"
  }
})

// 初始化、获取配置信息
const init = async () => {
  try {
    systemStore.showLoading("请稍候")
    const res = (await getCatalogConfigInfo(props.rangeType)).data.data
    originDepartmentOptions = res.depts.map(dept => ({
      label: dept.deptName,
      value: dept.wsid
    }))
    // 生成表格数据
    deptTableData.value = res.cataloger.map(user => {
      const deptWsids: string[] = []
      for (let dept of res.userDepts) {
        if (user.userWsid === dept.userWsid) {
          deptWsids.push(dept.deptWsid)
        }
      }
      return {
        deptWsids: deptWsids, // 按科室分配的科室数据 el-select绑定值
        catalogerName: user.realName,
        userWsid: user.userWsid,
        jobId: user.jobId,
        holiday: user.holiday,
        realName: user.realName,
        departmentOptions: cloneDeep(originDepartmentOptions)
      }
    })
    autoAssignmentThreshold.value = Number(res.autoAssignmentThreshold) || 0 // 自动分配的阈值
    manualTableData.value = cloneDeep(deptTableData.value) // 手动分配展示的科室数据
    systemStore.hideLoading()
  } catch (error: any) {
    systemStore.hideLoading()
    toastError(error, "获取配置信息失败")
  }
}

// 确认分配
const confirmAssign = () => {
  // 手动分配
  if (assignmentType.value === "MANUAL") {
    handleManualAssign()
  } else {
    const userDepts: { userWsid: string; deptWsid: string }[] = []
    // 配置编目参数(分配编目任务)
    if (assignmentType.value === "USER_DEPT") {
      deptTableData.value.forEach(row => {
        row.deptWsids.forEach(deptWsid => {
          userDepts.push({ userWsid: row.userWsid, deptWsid: deptWsid })
        })
      })
    }
    systemStore.showLoading("请稍候")
    configureCatalogParameters({
      userDepts: userDepts,
      assignmentType: assignmentType.value,
      autoAssignmentThreshold: autoAssignmentThreshold.value,
      rangeType: props.rangeType
    })
      .then(() => {
        systemStore.hideLoading()
        Message.success("操作成功")
        emits("success", assignmentType.value)
        close()
      })
      .catch(error => {
        systemStore.hideLoading()
        toastError(error, "操作失败")
      })
  }
}

/* =================== 按数量分配 ================== */

const autoAssignmentThreshold = ref(0) // 自动分配的阈值

/* ======================== 按科室分配 ======================== */

const selectedDeptList = ref<string[]>([]) // 所有已勾选的科室
const deptTableData = ref<any[]>([]) // 按科室分配的表格数据

// 选择科室
const handleSelect = (val, row) => {
  row.departmentOptions = cloneDeep(originDepartmentOptions)
  selectedDeptList.value = [
    ...new Set<string>(
      deptTableData.value.reduce((result: string[], pre) => {
        return result.concat(pre.deptWsids)
      }, [])
    )
  ]
  row.departmentOptions = row.departmentOptions.filter(item => !selectedDeptList.value.includes(item.value))
}

/* ================== 手动分配 ==================== */

// 手动分配勾选的编码员数据
const manualSelectedRows = ref<Record<string, any>[]>([])

const handleSelectionChange = rows => {
  manualSelectedRows.value = rows
}

const manualTableData = ref<Record<string, any>[]>([])
const filterTableKey = ref("")

const filterTableData = val => {
  manualTableData.value = cloneDeep(
    deptTableData.value.filter(item => item.realName.includes(val) || item.jobId.includes(val))
  )
}

// const handleSelectable = row => {
//   return !row.holiday
// }

// 执行手动分配
const handleManualAssign = () => {
  systemStore.showLoading("请稍候")
  const userWsids = manualSelectedRows.value.map(item => item.userWsid)
  const assignApi = props.rangeType === "IN_HOSPITAL" ? reassignCatalog : manualAssignOutpatientApi
  const requestParams =
    props.rangeType === "IN_HOSPITAL"
      ? { inpNos: props.selectedRows.map(item => item.inpNo), userWsids }
      : { registerNo: props.selectedRows.map(item => item.registerNo), userWsids }
  assignApi(requestParams as any)
    .then(() => {
      systemStore.hideLoading()
      Message.success("操作成功")
      close()
      emits("success", assignmentType.value)
    })
    .catch(error => {
      systemStore.hideLoading()
      toastError(error, "操作失败")
    })
}

/* ================= visible ================== */

const visible = ref(false)

const show = async (type: AssignmentType) => {
  await init()
  assignmentType.value = type
  visible.value = true
}

const close = () => {
  visible.value = false
}

defineExpose({ show, close })
</script>
