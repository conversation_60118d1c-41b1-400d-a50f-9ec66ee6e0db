import type { TableColumnItem, BaseOptionItem } from "@/types"

export const menuId = "/statistical-statement/page-count-report"

export const pageCountReportColumn: Array<TableColumnItem> = [
  { type: "selection", width: 50, fixed: "left" },
  { prop: "patientName", label: "患者姓名", minWidth: 150 },
  { prop: "patientId", label: "患者编号", minWidth: 150 },
  { prop: "mrNo", label: "病案号", minWidth: 150 },
  { prop: "visitId", label: "住院次数", minWidth: 150 },
  { prop: "inHospitalDatetime", label: "入院时间", minWidth: 150 },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 150 },
  { prop: "outHospitalDatetime", label: "出院时间", minWidth: 150 },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 150 },
  { prop: "archiveTime", label: "归档时间", minWidth: 150 },
  { prop: "pageCount", label: "总页数", minWidth: 150 },
  { prop: "electronicPageCount", label: "采集页数", minWidth: 150 },
  { prop: "paperPageCount", label: "拍摄页数", minWidth: 150 }
]

export const filterPropOptions: Array<BaseOptionItem> = [
  { label: "姓名", value: "patientName" },
  { label: "患者编号", value: "patientId" },
  { label: "病案号", value: "mrNo" }
]
