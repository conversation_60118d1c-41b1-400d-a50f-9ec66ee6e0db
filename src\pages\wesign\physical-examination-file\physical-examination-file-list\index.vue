<template>
  <PageContainer separate>
    <template #search>
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <el-form-item label="体检者信息">
          <el-input v-model.trim="searchFormState.patientFilterValue" style="width: 300px" clearable>
            <template #prepend>
              <el-select v-model="searchFormState.patientFilterProp" @change="handleBaseInfoKeyChange">
                <el-option
                  v-for="option in baseInfoKeyOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                ></el-option>
              </el-select>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="体检日期">
          <el-date-picker
            v-model="searchFormState.checkupDate"
            type="daterange"
            unlink-panels
            :clearable="false"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :disabled-date="disabledDate"
            value-format="x"
            @calendar-change="handleCalendarChange"
          ></el-date-picker>
        </el-form-item>
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        :request-api="getPhysicalExaminationFileListApi"
        :table-columns="tableColumns"
        :request-params="searchParams"
      >
        <template #checkupDate="{ row }">
          {{ formatDate(row.checkupDate) }}
        </template>
        <template #operation="{ row }">
          <TableButton @click="toDetail(row)">查看</TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>
</template>

<script setup lang="ts">
import { ref } from "vue"
import { useRouter } from "vue-router"
import { CommonTable, PageContainer, SearchContainer, TableButton } from "@/base-components"
import useTableSearch from "@/hooks/useTableSearch_v2"
import { formatDate } from "@/utils"
import { getPhysicalExaminationFileListApi } from "../interface"
import { tableColumns } from "./config"
import type { DateModelType } from "element-plus"

const router = useRouter()

/* ================== 查询 ================= */

// 默认三个月前当日0点
const defaultTimeStart = new Date(new Date().setHours(0, 0, 0, 0) - 3 * 30 * 24 * 3600 * 1000).getTime()
const defaultTimeEnd = new Date(new Date().setHours(23, 59, 59, 999)).getTime()

const initialFormData = {
  patientFilterProp: "",
  patientFilterValue: "",
  checkupDate: [defaultTimeStart, defaultTimeEnd] as [DateModelType, DateModelType]
}

const handleBaseInfoKeyChange = e => {
  searchFormState.patientFilterProp = e
}

const { searchParams, handleQuery, handleReset, searchFormState } = useTableSearch({
  filtersInitialData: initialFormData
})

// 体检者基础选项
const baseInfoKeyOptions = [
  { label: "姓名", value: "name" },
  { label: "体检编号", value: "checkupId" },
  { label: "身份证号", value: "idCard" },
  { label: "电话号码", value: "phone" }
]

/* ======================== 时间筛选限制 ======================== */

const startDate = ref()
const days = 90 * 24 * 3600 * 1000

const handleCalendarChange = date => {
  const [minDate, maxDate] = date
  if (minDate && !maxDate) {
    startDate.value = minDate // 记录选中的首个日期
  } else {
    startDate.value = null
  }
}

const disabledDate = time => {
  // 如果已经选择了开始日期，则禁用所有超出30天范围的日期
  if (startDate.value) {
    return time.getTime() < startDate.value?.getTime() - days || time.getTime() > startDate.value?.getTime() + days
  } else {
    return false
  }
}

/* ==================== 操作 ==================== */

const toDetail = row => {
  router.push(`/physical-examination-file/detail?checkupId=${row.checkupId}&fileWsid=${row.checkupFileWsid}`)
}
</script>
