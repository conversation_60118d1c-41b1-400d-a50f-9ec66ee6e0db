<template>
  <div class="blob-image-container">
    <img
      ref="ImgRef"
      :src="blobUrl || props.default"
      :style="{ visibility: complete ? 'visible' : 'hidden' }"
      @load="onLoad"
    />
    <el-icon v-if="!complete" class="is-loading"><Loading /></el-icon>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue"
import { Loading } from "@element-plus/icons-vue"
import axios from "@/interfaces/axios-instance"

const props = withDefaults(
  defineProps<{
    fileWsid: string
    urlPrefix?: string
    default?: string
  }>(),
  {
    default: "",
    urlPrefix: "/api/files"
  }
)

const ImgRef = ref()

function onLoad() {
  complete.value = true
}

const complete = ref(false)

const blobUrl = ref()

watch(
  () => props.fileWsid,
  val => {
    if (!val) return
    if (localStorage.getItem("SessionWsid") && localStorage.getItem("SESSION_EXPIRE") !== "YES") {
      axios({
        method: "get",
        url: `${props.urlPrefix}/${props.fileWsid}`,
        responseType: "blob"
      }).then(res => {
        blobUrl.value = URL.createObjectURL(res.data)
      })
    }
  },
  { immediate: true }
)
</script>

<style lang="less" scoped>
.blob-image-container {
  width: 100%;
  height: 100%;
  position: relative;
  .is-loading {
    position: absolute;
    top: 0px;
    bottom: 0px;
    left: 0px;
    right: 0px;
    margin: auto;
  }
}
img {
  width: 100%;
  height: 100%;
}
</style>
