<template>
  <div v-loading="loading" class="catalog-container">
    <!-- 顶部返回及操作按钮 -->
    <div class="header">
      <div class="return" @click="goBack">
        <el-icon style="margin-right: 4px"><ArrowLeft /></el-icon>
        返回
      </div>
      <!-- 操作按钮 -->
      <div v-if="actionType !== 'record'" class="btns-container">
        <el-button
          v-for="item in actionButtons"
          v-show="item.checked"
          :key="item.key"
          :color="catalogOperationOptions.find(option => option.value === item.key)?.color"
          :type="catalogOperationOptions.find(option => option.value === item.key)?.tagType"
          @click="handleCatalogOperation(item.key)"
        >
          <!-- <i :class="catalogOperationOptions.find(option => option.value === item.key)?.icon"></i> -->
          <img :src="catalogOperationOptions.find(option => option.value === item.key)?.icon" />
          <span :class="catalogOperationOptions.find(option => option.value === item.key)?.tagType">
            {{ item.label }}
          </span>
        </el-button>
      </div>
    </div>

    <div class="body">
      <div class="main">
        <!-- tab标题 -->
        <div class="catalog-title-container">
          <!-- 锚点标题导航 -->
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane
              v-for="item in configData"
              :key="item.prop"
              :label="item.name"
              :name="`_${item.prop}`"
            ></el-tab-pane>
          </el-tabs>
        </div>

        <!-- 编目表单数据 -->
        <div class="catalog-form-container" @scroll="scroll">
          <!-- 顶部固定内容 -->
          <el-affix v-if="scrolled && formConfig.filter(item => item.fixed === true).length" :offset="169">
            <div class="fixed-wrapper">
              <div
                v-for="item in formConfig"
                v-show="item.fixed === true"
                :key="item.groupName"
                class="form-item"
                :style="{ width: item.width }"
              >
                <!-- 标题 -->
                <div v-if="item.type === FormItemType.GroupHeadline" class="title-content">
                  {{ item.name }}
                </div>

                <!-- 锚点标题 -->
                <FormGroupHeader
                  v-else-if="item.type === FormItemType.AnchorPoint"
                  :title="item.name"
                ></FormGroupHeader>

                <!-- 其余类型表单 -->
                <el-form-item v-else :required="item.required" class="catalog-form-item">
                  <!-- 表单名称 -->
                  <template v-if="item.type !== FormItemType.IdCardGroup && item.name" #label>
                    <span
                      v-if="item.type === FormItemType.Table && item.default"
                      :style="{
                        visibility: item.name ? 'visible' : 'hidden',
                        fontSize: '14px'
                      }"
                    >
                      {{ item.name }}
                      <el-tooltip v-if="item.desc" placement="top" :content="item.desc">
                        <el-icon><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </span>

                    <span
                      v-if="item.type !== FormItemType.Table"
                      :style="{
                        visibility: item.name ? 'visible' : 'hidden',
                        fontSize: '14px'
                      }"
                    >
                      {{ item.name }}
                      <el-tooltip v-if="item.desc" placement="top" :content="item.desc">
                        <el-icon><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </span>
                  </template>

                  <!-- 单行文本 -->
                  <el-input
                    v-if="item.type === FormItemType.BaseInput"
                    v-model="item.default"
                    :disabled="item.fieldEditable === 'readOnly' || actionType === 'record'"
                    size="large"
                    :type="item.lineType === 'single' ? 'text' : 'textarea'"
                    :placeholder="item.placeholder ? item.placeholder : `请输入${item.name}`"
                    :style="{ width: item.width }"
                    @keydown.enter="pressEnter($event)"
                  />

                  <!-- 数字输入 -->
                  <NumberInput
                    v-if="item.type === FormItemType.BaseNumber"
                    v-model:value="item.default"
                    :disabled="item.fieldEditable === 'readOnly' || actionType === 'record'"
                    size="large"
                    :placeholder="item.placeholder ? item.placeholder : `请输入${item.name}`"
                    :unit="item.unit"
                    :precision="item?.allowDecimal ? item.precision : undefined"
                    @keydown.enter="pressEnter($event)"
                  ></NumberInput>

                  <!-- 多行文本域 -->
                  <el-input
                    v-if="item.type === FormItemType.BaseTextarea"
                    v-model="item.default"
                    size="large"
                    :disabled="item.fieldEditable === 'readOnly' || actionType === 'record'"
                    type="textarea"
                    :placeholder="item.placeholder ? item.placeholder : `请输入${item.name}`"
                    @keydown.enter="pressEnter($event)"
                  />

                  <!-- 日期选择 -->
                  <el-date-picker
                    v-else-if="item.type === FormItemType.BaseDate"
                    v-model="item.default"
                    size="large"
                    :disabled="item.fieldEditable === 'readOnly' || actionType === 'record'"
                    :type="getDateType(item.dateFormat)"
                    :format="item.dateFormat"
                    :value-format="item.dateFormat"
                    :placeholder="item.placeholder ? item.placeholder : `请选择${item.name}`"
                    @keydown.enter="pressEnter($event)"
                  />

                  <!-- 单选框 -->
                  <el-radio-group
                    v-else-if="[FormItemType.BaseRadio].includes(item.type)"
                    v-model="item.default"
                    :disabled="item.fieldEditable === 'readOnly' || actionType === 'record'"
                    size="large"
                  >
                    <el-radio v-for="option in item.options" :key="option.value" :label="option.value">
                      {{ option.label }}
                    </el-radio>
                  </el-radio-group>

                  <!-- 多选框 -->
                  <el-checkbox-group
                    v-else-if="item.type === FormItemType.BaseCheckbox"
                    v-model="item.default"
                    :disabled="item.fieldEditable === 'readOnly' || actionType === 'record'"
                    size="large"
                    :style="choiceFormItemStyle"
                    :value="[]"
                  >
                    <el-checkbox v-for="option in item.options" :key="option.value" :label="option.label"></el-checkbox>
                  </el-checkbox-group>

                  <!-- 下拉选择 -->
                  <div
                    v-else-if="[FormItemType.BaseSelect].includes(item.type) && !item.isLogicForm"
                    class="select-output"
                  >
                    <el-select
                      v-model="item.default"
                      :class="{ change: differenceData?.[item.prop] && differenceData?.[item.prop] !== '' }"
                      :disabled="item.fieldEditable === 'readOnly' || actionType === 'record'"
                      size="large"
                      fit-input-width
                      filterable
                      :remote="item.collectValueSource !== ''"
                      reserve-keyword
                      clearable
                      :remote-method="val => handleOptionsChange(val, item)"
                      @keydown.enter="pressEnter($event)"
                      @change="$event => handleChange($event, item)"
                      @click="getDefaultOption(item)"
                    >
                      <template v-if="item.fieldLinkage" #header>
                        <div class="code-header">
                          <span>名称</span>
                          <span>值</span>
                        </div>
                      </template>
                      <el-option
                        v-for="option in item.options"
                        :key="option.value"
                        :label="option.label"
                        :value="item.fieldLinkage ? option : option.value"
                      >
                        <div v-if="item.fieldLinkage" class="code-content">
                          <span style="color: #aaa" :title="option.label">{{ option.label }}</span>
                          <span :title="option.value">{{ option.value }}</span>
                        </div>
                      </el-option>
                    </el-select>
                  </div>

                  <!-- 地址 -->
                  <div v-else-if="[FormItemType.BaseAddress].includes(item.type)" class="select-output">
                    <el-cascader
                      v-model="item.addressSelect"
                      size="large"
                      :disabled="item.fieldEditable === 'readOnly' || actionType === 'record'"
                      style="width: 100%; margin-bottom: 8px"
                      placeholder="请选择"
                      :options="regionData"
                      :props="{ value: 'label' }"
                      @change="$event => changeValue($event, item.prop)"
                    />
                    <el-input
                      v-model="item.default"
                      size="large"
                      type="textarea"
                      :placeholder="item.placeholder ? item.placeholder : `请输入${item.name}`"
                    />
                  </div>

                  <!-- 表格数据 -->
                  <CatalogTable
                    v-else-if="item.type === FormItemType.Table"
                    :table-id="item.prop"
                    :table-data="item.default"
                    :table-columns="item.tableColumn"
                    :add-able="true"
                    :select-options="selectOptions"
                    :form-config="formConfig"
                    :handle-operation="handleOperation"
                    :handle-move="handleMove"
                    :update-column="handleUpdateColumn"
                  ></CatalogTable>
                </el-form-item>
              </div>
            </div>
          </el-affix>

          <!-- 正常展示内容 -->
          <div
            v-for="item in formConfig"
            v-show="item.type !== FormItemType.Table || (item.type === FormItemType.Table && item.default)"
            :id="`_${item.prop}`"
            :key="item.groupName"
            class="form-item"
            :style="{ width: item.width }"
          >
            <!-- 标题 -->
            <div v-if="item.type === FormItemType.GroupHeadline" class="title-content">
              {{ item.name }}
            </div>

            <!-- 锚点标题 -->
            <FormGroupHeader v-else-if="item.type === FormItemType.AnchorPoint" :title="item.name"></FormGroupHeader>

            <!-- 其余类型表单 -->
            <el-form-item
              v-else
              :disabled="item.fieldEditable === 'readOnly' || actionType === 'record'"
              :required="item.required"
              class="catalog-form-item"
              :class="{
                error: errorInfo[item.prop]
              }"
            >
              <!-- 表单名称 -->
              <template #label>
                <div v-if="item.type !== FormItemType.IdCardGroup && item.name && !item.isLogicForm">
                  <span
                    v-if="item.type === FormItemType.Table && item.default"
                    :style="{
                      visibility: item.name ? 'visible' : 'hidden',
                      fontSize: '14px'
                    }"
                  >
                    {{ item.name }}
                    <el-tooltip v-if="item.desc" placement="top" :content="item.desc">
                      <el-icon><QuestionFilled /></el-icon>
                    </el-tooltip>
                  </span>

                  <span
                    v-if="item.type !== FormItemType.Table"
                    :style="{
                      visibility: item.name ? 'visible' : 'hidden',
                      fontSize: '14px'
                    }"
                  >
                    {{ item.name }}
                    <el-tooltip v-if="item.desc" placement="top" :content="item.desc">
                      <el-icon><QuestionFilled /></el-icon>
                    </el-tooltip>
                  </span>
                </div>
              </template>

              <!-- 单行文本 -->
              <el-input
                v-if="item.type === FormItemType.BaseInput && !item.isLogicForm"
                v-model="item.default"
                :class="{ change: differenceData?.[item.prop] && differenceData?.[item.prop] !== '' }"
                :disabled="item.fieldEditable === 'readOnly' || actionType === 'record'"
                size="large"
                :type="item.lineType === 'single' ? 'text' : 'textarea'"
                :placeholder="item.placeholder ? item.placeholder : `请输入${item.name}`"
                :style="{ width: item.width }"
                @keydown.enter="pressEnter($event)"
              />

              <!-- 数字输入 -->
              <NumberInput
                v-if="item.type === FormItemType.BaseNumber && !item.isLogicForm"
                v-model:value="item.default"
                :class="{ change: differenceData?.[item.prop] && differenceData?.[item.prop] !== '' }"
                :disabled="item.fieldEditable === 'readOnly' || actionType === 'record'"
                size="large"
                :placeholder="item.placeholder ? item.placeholder : `请输入${item.name}`"
                :unit="item.unit"
                :precision="item?.allowDecimal ? item.precision : undefined"
                @keydown.enter="pressEnter($event)"
              ></NumberInput>

              <!-- 多行文本域 -->
              <el-input
                v-if="item.type === FormItemType.BaseTextarea && !item.isLogicForm"
                v-model="item.default"
                :class="{ change: differenceData?.[item.prop] && differenceData?.[item.prop] !== '' }"
                :disabled="item.fieldEditable === 'readOnly' || actionType === 'record'"
                size="large"
                type="textarea"
                :placeholder="item.placeholder ? item.placeholder : `请输入${item.name}`"
                @keydown.enter="pressEnter($event)"
              />

              <!-- 日期选择 -->
              <el-date-picker
                v-else-if="item.type === FormItemType.BaseDate && !item.isLogicForm"
                v-model="item.default"
                :class="{ change: differenceData?.[item.prop] && differenceData?.[item.prop] !== '' }"
                :disabled="item.fieldEditable === 'readOnly' || actionType === 'record'"
                size="large"
                :type="getDateType(item.dateFormat)"
                :format="item.dateFormat"
                :value-format="item.dateFormat"
                :placeholder="item.placeholder ? item.placeholder : `请选择${item.name}`"
                @keydown.enter="pressEnter($event)"
              />

              <!-- 单选框 -->
              <el-radio-group
                v-else-if="[FormItemType.BaseRadio].includes(item.type) && !item.isLogicForm"
                v-model="item.default"
                :class="{ change: differenceData?.[item.prop] && differenceData?.[item.prop] !== '' }"
                :disabled="item.fieldEditable === 'readOnly' || actionType === 'record'"
                size="large"
              >
                <el-radio v-for="option in item.options" :key="option.value" :label="option.value">
                  {{ option.label }}
                </el-radio>
              </el-radio-group>

              <!-- 多选框 -->
              <el-checkbox-group
                v-else-if="item.type === FormItemType.BaseCheckbox && !item.isLogicForm"
                v-model="item.default"
                :class="{ change: differenceData?.[item.prop] && differenceData?.[item.prop] !== '' }"
                :disabled="item.fieldEditable === 'readOnly' || actionType === 'record'"
                size="large"
                :style="choiceFormItemStyle"
                :value="[]"
              >
                <el-checkbox v-for="option in item.options" :key="option.value" :label="option.label"></el-checkbox>
              </el-checkbox-group>

              <!-- 下拉选择 -->
              <div v-else-if="[FormItemType.BaseSelect].includes(item.type) && !item.isLogicForm" class="select-output">
                <el-select
                  v-model="item.default"
                  :class="{ change: differenceData?.[item.prop] && differenceData?.[item.prop] !== '' }"
                  :disabled="item.fieldEditable === 'readOnly' || actionType === 'record'"
                  size="large"
                  fit-input-width
                  filterable
                  :remote="item.collectValueSource !== ''"
                  reserve-keyword
                  clearable
                  :remote-method="val => handleOptionsChange(val, item)"
                  @keydown.enter="pressEnter($event)"
                  @change="$event => handleChange($event, item)"
                  @click="getDefaultOption(item)"
                >
                  <template v-if="item.fieldLinkage" #header>
                    <div class="code-header">
                      <span>名称</span>
                      <span>值</span>
                    </div>
                  </template>
                  <el-option
                    v-for="option in item.options"
                    :key="option.value"
                    :label="option.label"
                    :value="item.fieldLinkage ? option : option.value"
                  >
                    <div v-if="item.fieldLinkage" class="code-content">
                      <span style="color: #aaa" :title="option.label">{{ option.label }}</span>
                      <span :title="option.value">{{ option.value }}</span>
                    </div>
                  </el-option>
                </el-select>
              </div>

              <!-- 地址 -->
              <div
                v-else-if="[FormItemType.BaseAddress].includes(item.type) && !item.isLogicForm"
                class="select-output"
              >
                <el-cascader
                  v-model="item.addressSelect"
                  :class="{ change: differenceData?.[item.prop] && differenceData?.[item.prop] !== '' }"
                  size="large"
                  :disabled="item.fieldEditable === 'readOnly' || actionType === 'record'"
                  style="width: 100%; margin-bottom: 8px"
                  placeholder="请选择"
                  :options="regionData"
                  :props="{ value: 'label' }"
                  @change="$event => changeValue($event, item.prop)"
                />
                <el-input
                  v-model="item.default"
                  size="large"
                  type="textarea"
                  :placeholder="item.placeholder ? item.placeholder : `请输入${item.name}`"
                />
              </div>

              <!-- 说明文本 -->
              <div
                v-if="[FormItemType.DescText].includes(item.type) && !item.isLogicForm"
                class="description-text"
                v-html="item.content"
              ></div>

              <!-- 表格数据 -->
              <CatalogTable
                v-else-if="item.type === FormItemType.Table && !item.isLogicForm"
                :table-id="item.prop"
                :table-data="item.default"
                :table-columns="item.tableColumn"
                :add-able="true"
                :select-options="selectOptions"
                :form-config="formConfig"
                :config="item"
                :handle-operation="handleOperation"
                :handle-move="handleMove"
                :update-column="handleUpdateColumn"
                :difference-data="differenceData"
                :error-info="errorInfo"
              ></CatalogTable>

              <!-- 错误信息 -->
              <div class="error-info">
                {{ errorInfo[item.prop] }}
              </div>
            </el-form-item>
          </div>
        </div>
      </div>

      <!-- 质控结果 -->
      <ScoreDrawer
        title="质控结果"
        :drawer-visible="scoreDrawerVisible"
        :close="closeScoreDrawer"
        :drawer-data="drawerData"
        :score-drawer-data="scoreDrawerData"
        :inp-no="businessDataWsid"
        :total-score="totalScore"
        :pdf-key="pdfKey"
        :update-drawer-data="updateDrawerData"
        :drawer-able="false"
        @locate-to="locateTo"
      />

      <!-- 编码-版本记录 -->

      <div v-if="actionType === 'record'" class="version">
        <el-timeline style="max-width: 600px">
          <el-timeline-item
            v-for="(item, index) in versionList"
            :key="index"
            placement="top"
            size="large"
            :type="item.id === currentId ? 'primary' : undefined"
            :hollow="false"
            :timestamp="item.createTime"
            @click="getVersionDetail(item)"
          >
            {{ item.catalogerName ? `编码员：${item.catalogerName}` : "采集数据" }}
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>

    <!-- 病案退回 -->
    <DialogContainer
      v-model:visible="returnDialogVisible"
      title="退回"
      :width="600"
      :confirm-callback="handleConfirmReturn"
      :confirm-loading="returnDialogLoading"
    >
      <el-form
        ref="returnCatalogForm"
        :model="returnCatalogFormData"
        label-position="right"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item label="限时修改时间：" prop="limitedModifyTime">
          <el-date-picker
            v-model="returnCatalogFormData.limitedModifyTime"
            size="large"
            type="date"
            format="YYYY/MM/DD"
            value-format="YYYY-MM-DD"
            placeholder="请选择限时修改时间"
          />
        </el-form-item>
        <el-form-item label="退回原因：" prop="desc">
          <el-input v-model="returnCatalogFormData.desc" placeholder="请输入退回原因" type="textarea" :rows="5" />
        </el-form-item>
        <el-form-item></el-form-item>
      </el-form>
    </DialogContainer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from "vue"
import { useRoute, useRouter } from "vue-router"
import { regionData } from "element-china-area-data"
import { cloneDeep } from "lodash-es"
import { CircleCheck, CircleClose, Check, QuestionFilled } from "@element-plus/icons-vue"
import { FormGroupHeader, NumberInput, DialogContainer } from "@/base-components"
import { ScoreDrawer } from "@/page-components"
import CatalogTable from "./CatalogTable.vue"
import type { TabsPaneContext, FormInstance, FormRules } from "element-plus"
import { FormItemType, FormItemConfig, catalogOperationOptions } from "@/configs"
import { useCatalogTableOperation } from "@/hooks"
import {
  getFormConfigsDetailApi,
  getDynamicFormCatalogApi,
  confirmDynamicFormCatalogApi,
  checkDynamicFormCatalogApi,
  searchMetaCodeTableFieldApi,
  temporaryDynamicFormCatalogApi,
  cancelDynamicFormCatalogApi,
  catalogSendBack,
  getCatalogVersionRecord,
  getDifferenceDynamicFormCatalogApi,
  getCatalogRecordByinpNoApi
} from "@/interfaces"
import axios from "@/interfaces/axios-instance"
import { useSystemStore } from "@/stores"
import { Message, toastError, formatDatetime, getDateType, SystemPrompt, getOptions } from "@/utils"

const route = useRoute()
const router = useRouter()
const systemStore = useSystemStore()

/* ======================== 页面配置和数据 ======================== */

const activeName = ref("")
const configData = ref<Array<Record<string, any>>>([])
const selectOptions = ref<Record<string, any>>({})
const formData = ref<Record<string, any>>({})
const tables = ref<Record<string, any>>({})
const tablesConfig = ref<Record<string, any>>([])
const errorInfo = ref<Record<string, any>>({})
const actionButtons = ref<Record<string, any>>([])

const formConfig = ref<Array<Record<string, any>>>([])
const initFormConfig = ref<Array<Record<string, any>>>([])

const versionList = ref<Array<Record<string, any>>>([]) // 版本列表

const differenceData = ref<Record<string, any>>({})

onMounted(async () => {
  await initData("")
  if (actionType.value === "record") {
    // 版本列表
    await getVersionData()
  }
  if (actionType.value === "edit" || actionType.value === "finished") {
    checkData()
  }
})

const initData = async id => {
  try {
    systemStore.showLoading("加载中")

    // 三种场景 待编目编辑(getDynamicFormCatalogApi) 、已编目修改(getCatalogRecordByinpNoApi)、已编目编目记录(getDifferenceDynamicFormCatalogApi)
    const handle = id
      ? getDifferenceDynamicFormCatalogApi
      : actionType.value === "finished"
      ? getCatalogRecordByinpNoApi
      : getDynamicFormCatalogApi

    const resData = (
      await handle({
        businessDataWsid: id || (route.query?.businessDataWsid as string),
        appScenario: route.query?.appScenario as string
      })
    )?.data?.data
    formConfig.value = JSON.parse(resData.formContent || "[]")?.formConfig
    actionButtons.value = JSON.parse(resData.formContent || "[]")?.actionButtons
    differenceData.value = resData?.differenceData
    console.log(`output->differenceData.value`, differenceData.value)

    // 初始化 数据源的options(只加载valueRangeKey：元数据取值的options, 接口取值collectValueSource的options点击时才获取)
    formConfig.value.forEach(async (item: any) => {
      if (item.valueRangeKey || item?.collectValueSource?.includes("/hospital/departments/fuzzy-query")) {
        item.options = await getOptions(item, "")
      }

      // table类型
      if (item.type === FormItemType.Table) {
        item.tableColumn = []
        item.tableFormConfig.forEach((config: any) => {
          item.tableColumn.push({
            ...config,
            id: config.prop,
            label: config.name,
            width: null
          })
        })
        if (item.operate) {
          item.tableColumn.push({
            prop: "pageOptionProp",
            label: "操作",
            width: 300,
            fixed: "right"
            // minWidth: 260
          })
        }
        if (item.tableIndex) {
          item.tableColumn.unshift({
            type: "index",
            width: 140,
            fixed: "left"
          })
        }
      }
    })

    //  添加到tableColumn内
    formConfig.value.forEach(async (item: any) => {
      if (item.tableColumn) {
        item.tableColumn.forEach(async (config: any) => {
          // if (config.valueRangeKey || config.collectValueSource) {
          if (config.valueRangeKey) {
            config.options = (await getOptions(config, "")).length ? await getOptions(config, "") : config.options
          }
        })
      }
    })

    formData.value = resData?.formData
    // 初始化formData，将null值转成空字符串
    Object.keys(formData.value).forEach(key => {
      if (typeof formData.value[key] === "object") {
        formData.value[key] = formData.value[key].map(item => {
          // return JSON.parse(JSON.stringify(item).replaceAll("null", `""`))
          return JSON.parse(JSON.stringify(item).replace(/null/g, `""`))
        })
      }
    })

    console.log(`output->formData.value`, formData.value)
    Object.keys(formData.value).forEach(key => {
      if (formConfig.value.find(item => item.prop === key)) {
        formConfig.value.find(item => item.prop === key).default = formData.value[key]
        // 采集的数据可能有数字 转换成字符串
        if (typeof formConfig.value.find(item => item.prop === key).default === "number") {
          formConfig.value.find(item => item.prop === key).default = formConfig.value
            .find(item => item.prop === key)
            .default.toString()
        }
      }
      if (typeof formData.value[key] === "object") {
        tables.value[key] = formData.value[key]
      }
    })

    console.log(`output->formConfig.value`, formConfig.value)
    console.log(`output->tables.value`, tables.value)
    formConfig.value.forEach(config => {
      if (config.tableDataCountField)
        formConfig.value.find(item => item.prop === config.tableDataCountField).default = 0
    })
    // 初始化逻辑表单
    let prop = ""
    formConfig.value.forEach(config => {
      if (config.type === "base_select") {
        prop = config?.logicFormConfig?.[config?.default] || []
        formConfig.value.forEach(item => {
          if (prop.includes(item.prop)) {
            item.isLogicForm = false
          }
        })
      } else if (config.tableDataCountField) {
        // 计数表单
        formConfig.value.find(item => item.prop === config.tableDataCountField).default += config.default.length
      }
      // 编辑时编码员使用后端返回字段初始化
      if (config.prop === "B29" && actionType.value === "edit") {
        config.default = formData.value?.["currentCoder"]
      }
    })
    // 锚点标题数据
    configData.value = formConfig.value.filter(item => item.type === FormItemType.AnchorPoint)
    activeName.value = `_${configData.value[0]?.prop}`
    // 原始配置数据
    initFormConfig.value = cloneDeep(formConfig.value)
    systemStore.hideLoading()
  } catch (err: any) {
    console.log(`output->err`, err)
    systemStore.hideLoading()
    toastError(err)
  }
}

const getVersionData = async () => {
  //  版本列表
  versionList.value = (await getCatalogVersionRecord(businessDataWsid.value))?.data?.data.map(item => {
    return {
      ...item,
      createTime: formatDatetime(item.createTime)
    }
  })
}

const currentId = ref("")
// 获取记录id
const getVersionDetail = data => {
  if (data.id === currentId.value) return
  currentId.value = data.id
  initData(data.id)
}

/* ======================== 顶部按钮相关操作 ======================== */
const actionType = computed(() => {
  return route.query.actionType as string
})

const loading = ref(false)
const inpNo = computed(() => {
  return route.query.inpNo as string
})

const businessDataWsid = computed(() => {
  return route.query.businessDataWsid as string
})

const returnUrl = computed(() => {
  return route.query.returnUrl as string
})

const toBackEndData = computed(() => {
  return formConfig.value
    .map(item => {
      return { [item.prop]: item.default }
    })
    .reduce((result, pre) => {
      return Object.assign(result, pre)
    }, {})
})

const { handleMove, handleUpdateColumn, handleOperation, save, cancelSave, check, confirm } = useCatalogTableOperation(
  tables,
  toBackEndData,
  (route.query?.appScenario as string) || "OUTPATIENT_SERVICE_HQMS",
  businessDataWsid,
  {
    saveApi: temporaryDynamicFormCatalogApi,
    cancelSaveApi: cancelDynamicFormCatalogApi,
    checkApi: checkDynamicFormCatalogApi,
    confirmApi: confirmDynamicFormCatalogApi
  },
  loading
)

const checkData = async () => {
  scoreDrawerVisible.value = true

  errorInfo.value = {}
  await check()
    .then(res => {
      drawerData.value = res.data.data.deducts
      scoreDrawerData.value = drawerData.value
      totalScore.value = res.data.data.totalScore
      pdfKey.value = res.data.data.pdfKey || ""
      loading.value = false
      drawerData.value
        .filter(item => item.controlLevelEnum === "ERROR")
        .forEach(data => {
          errorInfo.value[data.fieldName] = data.promptInfo
        })

      console.log(`output->errorInfo.value`, errorInfo.value)
    })
    .catch(err => {
      console.log(`output->err`, err)
      toastError(err, "操作失败")
      loading.value = false
    })
}

const saveData = async () => {
  await save()
  await initData("")
}

const cancelSaveData = async () => {
  SystemPrompt("您确定要取消已编码的病案内容？").then(async () => {
    await cancelSave()
    await initData("")
  })
}

const confirmCatalog = async () => {
  await checkData()
  if (Object.keys(errorInfo.value).length !== 0) return
  else {
    SystemPrompt("就诊已编码，确定提交吗？").then(async () => {
      confirm()
        .then(res => {
          Message.success("保存成功")
          setTimeout(() => {
            router.push({
              path: returnUrl.value
            })
            loading.value = false
          }, 500)
        })
        .catch(err => {
          toastError(err, "保存失败")
          loading.value = false
        })
    })
  }
}

/* ======================== 评分详情 ======================== */

const scoreDrawerVisible = ref(false)
const drawerData = ref<Array<Record<string, any>>>([])
const scoreDrawerData = ref<Array<Record<string, any>>>([])
const pdfKey = ref()
const totalScore = ref()

const closeScoreDrawer = () => {
  scoreDrawerVisible.value = false
}

const updateDrawerData = (val: string) => {
  scoreDrawerData.value = drawerData.value.filter(item => val.includes(item.controlLevelEnum))
}

/* ======================== 表单相关操作 ======================== */

// 省市级联
const changeValue = (value, prop) => {
  formConfig.value.find(item => item.prop === prop).default = value
  // formData.value[prop] = value.join("")
}

// 按下回车键光标移动到下一个表单
const pressEnter = e => {
  // 选择框未选中值时，不移动
  if (!e.target.value && e.target.role === "combobox") return
  let inputIndex
  Array.from(document.querySelectorAll(".el-input__inner")).forEach((item, index) => {
    if (item.id === e.target.id) {
      inputIndex = index
      return
    }
  })
  document.querySelectorAll(".el-input__inner")[inputIndex + 1].focus()
}

// 定位标题锚点
const handleClick = (tab: TabsPaneContext, event: Event) => {
  document.querySelector(`#${tab.props.name}`)?.scrollIntoView(true)
  document.querySelector(`#${tab.props.name}`)?.scrollIntoView({
    behavior: "smooth",
    block: "start",
    inline: "center"
  })
}
const scrolled = ref(false)
// 页面滚动字段切换tab(activeName)
const scroll = e => {
  // 滚动一段距离就展示固定内容
  scrolled.value = document.querySelector(".catalog-form-container")?.scrollTop > 200
  configData.value.forEach(item => {
    if (
      document.querySelector(".catalog-form-container")?.scrollTop >=
      document.querySelector(`#_${item.prop}`)?.offsetTop - 300
    ) {
      activeName.value = `_${item.prop}`
    }
  })
}

const handleOptionsChange = async (val, item) => {
  if (item.collectValueSource) {
    if (val) item.options = await getOptions(item, val)
  }
}

const getDefaultOption = async item => {
  if (item.collectValueSource) {
    if (!item.options.length) item.options = await getOptions(item, "")
  }
}

/* ======================== 编目顶部操作 ======================== */
const handleCatalogOperation = (key: string) => {
  switch (key) {
    case "check":
      return checkData()
    case "confirm":
      return confirmCatalog()
    case "save":
      return saveData()
    case "cancel":
      return cancelSaveData()
    case "return":
      return returnCatalog()
    case "view":
      return window.open(
        `/coding/medical-insurance/detail?inpNo=${route.query.businessDataWsid}&type=${route.query.actionType}`,
        "_blank"
      )
  }
}

const goBack = () => {
  router.push({
    path: returnUrl.value
  })
}

// 病案退回
const returnDialogVisible = ref(false)
const returnDialogLoading = ref(false)

const returnCatalogFormData = reactive({
  desc: "",
  limitedModifyTime: ""
})

const returnCatalog = async () => {
  returnDialogVisible.value = true
}

const returnCatalogForm = ref<FormInstance>()
const rules: FormRules = {
  desc: [
    { required: true, message: "请输入退回原因", trigger: "blur" },
    { min: 1, max: 200, message: "退回原因长度在 1 到 200 个字符", trigger: "blur" }
  ],
  limitedModifyTime: [{ required: true, message: "请选择限时修改时间", trigger: "blur" }]
}
const handleConfirmReturn = async () => {
  returnCatalogForm.value?.validate(async valid => {
    if (!valid) return
    try {
      returnDialogLoading.value = true
      await catalogSendBack({
        ...returnCatalogFormData,
        inpNo: route.query?.businessDataWsid
      })
      returnDialogVisible.value = false
      returnDialogLoading.value = false
      Message.success("退回成功")
      setTimeout(() => {
        goBack()
      }, 500)
    } catch (error: any) {
      returnDialogLoading.value = false
      // Message.error("退回失败")
      toastError(error, "退回失败")
    }
  })
}

/* ======================== 下拉框表单选择激活逻辑表单 ======================== */
const handleChange = (e, item) => {
  console.log(`output->e`, e)
  console.log(`output->item`, item)
  if (JSON.stringify(item.logicFormConfig) !== "{}") {
    nextTick(() => {
      const prop = item.logicFormConfig[e] || ""
      console.log(`output->prop`, prop)
      formConfig.value.forEach(config => {
        if (prop.includes(config.prop)) {
          config.isLogicForm = false
        } else {
          if (JSON.stringify(item.logicFormConfig).includes(config.prop)) {
            config.isLogicForm = true
            config.default = ""
          }
        }
      })
    })
  }
  if (item.fieldLinkage) {
    item.default = e[item.optionKey]
    const linkProp = formConfig.value?.find(config => config.prop === item.fieldLinkage)
    linkProp.options = cloneDeep(item?.options || [])
    linkProp.default = e[linkProp?.optionKey]
  }
}

/* ======================== 编目错误信息定位到页面元素 ======================== */
const locateTo = item => {
  if (item.controlLevelEnum === "ERROR") {
    // 非table类型表单
    const dom = document.querySelector(`#_${item.fieldName}`)
    if (dom) {
      // 自动focus对应输入框
      Array.from(dom.querySelectorAll("*"))
        .find(item => item?.localName === "input")
        ?.focus()
      // 滚动到对应位置
      dom?.scrollIntoView(true)
      dom?.scrollIntoView({
        behavior: "smooth",
        block: "center",
        inline: "center"
      })
    } else {
      // table类型form, error字段在table内
      const id = formConfig.value.find(
        config => config.type === FormItemType.Table && config?.tableFormConfig?.some(t => t.prop === item.fieldName)
      )?.prop
      document.querySelector(`#_${id}`)?.scrollIntoView(true)
      document.querySelector(`#_${id}`)?.scrollIntoView({
        behavior: "smooth",
        block: "center",
        inline: "center"
      })
    }
  }
}
</script>

<style lang="less" scoped>
.catalog-title-container {
  padding: 12px 20px;
  margin-bottom: 16px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  :deep(.el-tabs) {
    // float: left;
    // width: 40%;
  }
  .btns-container {
    height: 54px;
    // width: 30%;
    min-width: 30%;
    display: flex;
    align-items: center;
    justify-content: space-evenly;

    .btn {
      cursor: pointer;
    }
  }
}

// .catalog-title-container::after {
//   content: "";
//   display: block;
//   height: 0;
//   clear: both;
//   visibility: hidden;
// }

.organization-info::after {
  content: "";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

.catalog-container {
  min-width: 980px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  .catalog-form-container {
    height: calc(100% - 132px);
    overflow: auto;
    background-color: #fff;
    display: flex;
    flex-wrap: wrap;
    // justify-content: space-between;
    padding: 19px 12px;
    width: calc(100% - 24px);

    .form-title {
      padding: 24px 20px;

      .organization-info {
        width: 100%;
        font-size: 14px;
        div {
          float: right;
          margin-right: 30px;
        }
        label {
          color: rgba(10, 22, 51, 0.85);
        }
        span {
          color: rgba(10, 22, 51, 0.6);
        }
      }
    }

    .form-item {
      padding: 0px 10px;
      box-sizing: border-box;

      .form-container {
        padding: 20px 0;
      }

      .options {
        margin-bottom: 0;
        display: flex;
        align-items: center;

        .options-container {
          width: 100%;
          padding-top: 8px;
          span {
            margin-right: 16px;
            color: #0a1633;
          }
        }
      }
    }
  }
}

.error-info {
  display: none;
}

.error {
  :deep(.el-input) {
    border: 1px solid #f56c6c;
    border-radius: 4px;
  }
}

:deep(.el-form-item) {
  position: relative;
  width: 100%;
  &:hover {
    .error-info {
      display: block;
      position: absolute;
      bottom: -80%;
      color: #f56c6c;
    }
  }
}

:deep(.el-textarea) {
  position: relative;
  width: 100% !important;
  &:hover {
    .error-info {
      display: block;
      position: absolute;
      bottom: -80%;
      color: #f56c6c;
    }
  }
}

.common-table-container {
  margin-bottom: 10px;
}

.el-form {
  // display: flex;
  // flex-wrap: wrap;
  // justify-content: space-between;
  overflow: hidden;
}

.el-form-item {
  // width: 21%;
  // float: left;
  // padding-bottom: 10px;
}

.unit {
  width: 10%;
  text-align: center;
  // font-weight: bold;
}
.prefix {
  width: 16%;
  text-align: center;
}

:deep(.el-form-item__content) {
  flex-wrap: nowrap;
  width: 100%;

  .el-input {
    width: 100% !important;
  }
}

.form-group-header {
  margin-bottom: 20px;
  width: 100%;
}

.width-20 {
  width: 20%;
  margin-right: 5%;
}

.width-30 {
  width: 25%;
  margin-right: 5%;
}

.width-40 {
  width: 45%;
  margin-right: 5%;
}

.width-100 {
  width: 100%;
}

.title-content {
  text-align: center;
  color: #000;
  font-size: 26px;
  margin: 44px 0;
}

.catalog-form-item {
  flex-direction: column;
}
:deep(.select-output) {
  width: 100% !important;
}

:deep(.el-select) {
  width: 100% !important;
}

:deep(.el-form-item__label) {
  // text-align: left !important;
  // display: block !important;
  justify-content: flex-start !important;
}

:deep(.el-affix) {
  width: 100% !important;
}
:deep(.el-affix--fixed) {
  // width: 100% !important;
}

.fixed-wrapper {
  width: 100%;
  height: 10vh;
  overflow-y: auto;
  background: #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
  // font-size: 32px;

  // .el-form-item__label {
  //   fontsize: 14px !important;
  // }
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 14px 25px;
  background: #fff;
  margin-bottom: 16px;

  .return {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 500;
    font-size: 14px;
    color: #666666;
  }
}

.body {
  display: flex;
  // flex-wrap: wrap;
  height: calc(100vh - 76px);

  .main {
    height: 100%;
    flex: 1;
    min-width: 70%;
  }
}
.version {
  padding: 50px;
  border-left: 1px solid #e1e2e6;
  height: calc(100% - 100px);
  overflow: auto;
}

.change {
  color: red !important;
  :deep(.el-input__inner) {
    color: red !important;
    -webkit-text-fill-color: red !important;
  }
}
.text {
  color: #6c7385 !important;
  margin-left: 4px;
}

:deep(.el-button--text) {
  &:hover {
    background: #eff2f7 !important;
  }
}
.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  font-size: 14px;
  width: calc(100% - 32px);
  min-width: 20vw;
  span {
    width: 33%;
    text-align: left;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
.code-content {
  display: flex;
  justify-content: space-between;
  align-items: center;

  span {
    width: 33%;
    text-align: left;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>
