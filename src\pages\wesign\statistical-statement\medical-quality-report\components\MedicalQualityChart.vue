<template>
  <div v-if="!props.showTable" class="medical-chart">
    <div class="header-container flex-between">
      <div class="select-wrapper">
        <div class="title-text">科室病案质量</div>
        <el-select v-model="selectedDemension">
          <el-option
            v-for="(item, index) in deptMedicalOptions"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div>

      <div class="table-chart-tool">
        <el-tooltip content="表格展示">
          <i v-if="props.showTable"><img src="@/assets/svg/statistical-statement/table-active.svg" alt="" /></i>
          <i v-else @click="emits('changeTab', true)">
            <img src="@/assets/svg/statistical-statement/table.svg" alt="" />
          </i>
        </el-tooltip>
        <el-tooltip content="图表展示">
          <i v-if="props.showTable">
            <img src="@/assets/svg/statistical-statement/chart.svg" alt="" />
          </i>
          <i v-else><img src="@/assets/svg/statistical-statement/chart-active.svg" alt="" /></i>
        </el-tooltip>
      </div>
    </div>

    <v-chart class="line-chart" :option="options" autoresize></v-chart>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue"
import { LineChart } from "echarts/charts"
import { GridComponent } from "echarts/components"
import { use } from "echarts/core"
import { UniversalTransition } from "echarts/features"
import { CanvasRenderer } from "echarts/renderers"
import { max } from "lodash/fp"
import VChart from "vue-echarts"
import { deptMedicalOptions } from "../config"

const emits = defineEmits(["changeTab"])

const props = defineProps({
  showTable: { type: Boolean, default: true },
  dataSource: { type: Array, default: () => [] },
  xAxisData: { type: Array, default: () => [] }
})

const selectedDemension = ref(deptMedicalOptions[0]?.value)

use([LineChart, GridComponent, UniversalTransition, CanvasRenderer])

const options = computed(() => {
  const seriesData = props.dataSource.map((item: any) => item?.[selectedDemension.value as string])
  const percentageDimensions = ["AMedicalPercentage", "BMedicalPercentage", "CMedicalPercentage"]
  return {
    grid: {
      left: "1%",
      right: "2%",
      bottom: "3%",
      containLabel: true
    },
    tooltip: {
      trigger: "axis"
    },
    xAxis: {
      type: "category",
      data: props.xAxisData
    },
    yAxis: {
      type: "value",
      //如果是百分比的维度，设置单位为%
      axisLabel: {
        formatter: percentageDimensions.find(item => item === selectedDemension.value) ? "{value}%" : "{value}"
      },
      // 如果是百分比的维度，那么设置最大值为100%
      max: percentageDimensions.find(item => item === selectedDemension.value) ? 100 : null
    },
    series: [
      {
        data: seriesData,
        type: "line",
        color: "#1BA1FC",
        smooth: false,
        //格式化数据，后面加上百分号
        label: {
          show: true,
          formatter: percentageDimensions.find(item => item === selectedDemension.value) ? "{c}%" : "{c}"
        }
      }
    ]
  }
})
</script>

<style lang="less" scoped>
.medical-chart {
  width: 100%;
  border: 1px solid #d4d4d4;
  border-radius: 4px;
  padding: 20px;
  box-sizing: border-box;
  height: 400px;
  color: #333;

  .header-container {
    width: 100%;

    .select-wrapper {
      .title-text {
        padding-bottom: 20px;
        font-weight: bold;
      }
    }
    .table-chart-tool {
      .table-setting-btn {
        margin-right: 8px;
        padding: 4px 8px;
        cursor: pointer;
      }

      i {
        cursor: pointer;
        img {
          vertical-align: bottom;
        }
      }
    }
  }

  .line-chart {
    width: 100%;
    height: calc(100% - 72px);
  }
}
</style>
