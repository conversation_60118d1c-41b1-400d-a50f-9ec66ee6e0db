<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          :filter-prop-options="outpatientFilterFormOptions"
          label="患者信息"
        />
        <DepartmentFormItem v-model="searchFormState.deptCode" label="就诊科室" />
        <DaterangeFormItem v-model="searchFormState.consultationTime" label="就诊时间" />
        <CommonInputFormItem v-model="searchFormState.doctorName" label="就诊医师" />
        <CommonInputFormItem v-model="queryParams.mainDiagnostic" label="主要诊断" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="tableRef"
        table-id="outpatientWaitingCatalogedTableIdent"
        :table-columns="pendingColumns"
        :request-api="getOutpatientCatalogListApi"
        :request-params="waitingCatalogParams"
        :data-callback="dataCallback"
      >
        <!-- 患者姓名 -->
        <template #patientName="{ row }">
          <div style="display: flex; align-items: center; flex-wrap: wrap">
            <span style="margin: 0 16px">{{ row.patientName }}</span>
            <PatientLabelTag :row="row" />
          </div>
        </template>
        <template #consultationTime="{ row }">
          {{ formatDatetime(row.consultationTime, "YYYY-MM-DD HH:mm:ss") }}
        </template>

        <template #patientPrintCount="{ row }">
          {{ row.patientPrintCount > 0 ? "已打印" : "未打印" }}
        </template>
        <template #operation="{ row }">
          <TableButton @click="toEdit(row)">编辑</TableButton>
          <TableButton @click="toDetail(row)">查看</TableButton>
          <TableButton @click="downloadPdf(row)">下载PDF</TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref, computed, watch } from "vue"
import { useRouter } from "vue-router"
import {
  SearchContainer,
  PatientFilterFormItem,
  CommonTable,
  TableButton,
  DaterangeFormItem,
  DepartmentFormItem,
  PageContainer,
  PatientLabelTag,
  CommonInputFormItem
} from "@/base-components"
import { TabsRouter } from "@/page-components"
import { getOutpatientCatalogListApi } from "../interface"
import { pendingColumns, tabsRouterList, outpatientFilterFormOptions } from "./config"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { exportDynamicFormCatalogApi } from "@/interfaces"
import { useUserStore, useSystemStore } from "@/stores"
import { formatDatetime, formatDate, Message, downloadFile, toastError } from "@/utils"

const router = useRouter()
const systemStore = useSystemStore()

const { hasOperationPermission } = useUserStore()

/* ======================== 搜索相关数据及方法 ======================== */
const searchFormState = reactive({
  patientFilterProp: "",
  patientFilterValue: "",
  deptCode: "",
  consultationTime: "",
  doctorName: ""
})

const queryParams = reactive({
  mainDiagnostic: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState, undefined, undefined, queryParams)

const waitingCatalogParams = computed(() => {
  return { ...searchParams, queryType: "wait_catalog" }
})

/* ======================== 表格相关方法 ======================== */

function dataCallback(data: Array<Record<string, any>>) {
  return data.map(item => ({
    ...item,
    inHospitalDatetime: formatDatetime(item.inHospitalDatetime),
    outHospitalDatetime: formatDatetime(item.outHospitalDatetime)
  }))
}

const tableRef = ref()

/* ==================== 操作 ======================= */

// 编辑
const toEdit = (row: Record<string, any>) => {
  router.push({
    path: "/catalog-preview",
    query: {
      appScenario: "OUTPATIENT_SERVICE_ENCODE",
      businessDataWsid: row.registerNo,
      returnUrl: "/coding/outpatient-and-emergency/pending",
      actionType: "edit"
    }
  })
}

// 查看
const toDetail = (row: Record<string, any>) => {
  router.push(`/coding/outpatient-and-emergency/detail?registerNo=${row.registerNo}`) // TODO
}

// 下载pdf
const downloadPdf = async (row: Record<string, any>) => {
  // TODO
  systemStore.showLoading("正在下载，请稍候")
  exportDynamicFormCatalogApi(row.registerNo)
    .then(res => {
      systemStore.hideLoading()
      Message.success("下载文件成功")

      downloadFile({
        fileData: res.data,
        fileType: "application/pdf",
        fileName: `${decodeURIComponent(res.headers["content-disposition"].split("filename=")[1].replaceAll('"', ""))}`
      })
    })
    .catch(error => {
      systemStore.hideLoading()
      toastError(error, "下载文件失败")
    })
}
</script>
