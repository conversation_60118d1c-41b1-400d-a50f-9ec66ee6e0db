<template>
  <div class="popover-button-container">
    <el-button
      class="popover-button"
      :class="$props.center && 'popover-button--center'"
      link
      type="primary"
      :disabled="$props.disabled"
      @click.stop="$emit('click')"
    >
      <slot></slot>
      <span v-if="$props.tip" class="popover-button-tip">{{ $props.tip }}</span>
    </el-button>
  </div>
</template>

<script setup lang="ts">
interface PopoverButtonProps {
  disabled?: boolean
  tip?: string
  center?: boolean
}

withDefaults(defineProps<PopoverButtonProps>(), {
  disabled: false,
  tip: "",
  center: true
})

defineEmits(["click"])
</script>

<style lang="less" scoped>
.popover-button-container {
  &:hover {
    background: #ecf5ff;

    .popover-button-tip {
      color: #ccc;
    }
  }

  :deep(.is-disabled, .is-disabled:hover) {
    color: #aaa !important;
    background: #fff !important;
  }

  .popover-button {
    color: #666;
    padding: 6px 10px !important;
    display: flex;
    align-items: center;
    justify-content: flex-start;

    &--center {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
    }
  }

  .popover-button-tip {
    margin-left: 2px;
    font-size: 12px;
    color: #aaa;
  }
}
</style>
