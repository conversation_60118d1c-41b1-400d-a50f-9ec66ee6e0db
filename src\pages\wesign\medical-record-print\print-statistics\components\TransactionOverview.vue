<template>
  <div class="transaction-overview-list">
    <div class="transaction-overview-item">
      <img :src="TradeIcon" alt="" />
      <div>
        <span class="transaction-overview-item-title">销售金额(元)</span>
        <div class="transaction-overview-item-content">{{ props.orderData?.orderAmount }}</div>
      </div>
    </div>
    <div class="transaction-overview-item">
      <img :src="TradeIcon" alt="" />

      <div class="transaction-overview-item-title">
        <span class="transaction-overview-item-title">支付订单数(个)</span>
        <div class="transaction-overview-item-content">{{ props.orderData?.orderPayNum }}</div>
      </div>
    </div>
    <div class="transaction-overview-item">
      <img :src="TradeIcon" alt="" />
      <div>
        <span class="transaction-overview-item-title">新增用户数(个)</span>
        <div class="transaction-overview-item-content">{{ props.orderData?.addUserNum }}</div>
      </div>
    </div>
    <div class="transaction-overview-item">
      <img :src="TradeIcon" alt="" />
      <div>
        <span class="transaction-overview-item-title">下单用户数(个)</span>
        <div class="transaction-overview-item-content">{{ props.orderData?.orderUserNum }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import TradeIcon from "@/assets/svg/medical-reacord-trace/trade.svg"
import { TransactionOverviewProps } from "../types"

const props = defineProps<{
  orderData: TransactionOverviewProps
}>()
</script>

<style lang="less" scoped>
.transaction-overview-list {
  margin-top: 24px;
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  :nth-child(odd) {
    margin-right: 20px;
  }
  .transaction-overview-item {
    width: 48%;
    padding-top: 34px;
    padding-bottom: 30px;
    background: linear-gradient(180deg, #f7f9ff 0%, #ffffff 100%);
    border-radius: 4px 4px 4px 4px;
    margin-bottom: 20px;
    display: flex;
    align-items: baseline;
    justify-content: center;
    img {
      margin-right: 10px;
    }

    .transaction-overview-item-title {
      font-size: 14px;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: rgba(10, 22, 51, 0.6);
      line-height: 24px;
    }
    .transaction-overview-item-content {
      font-size: 28px;
      font-family: DIN-Bold, DIN;
      font-weight: bold;
      color: rgba(10, 22, 51, 0.85);
    }
  }
}
</style>
