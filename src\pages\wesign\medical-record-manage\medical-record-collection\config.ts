import type { TableColumnItem } from "@/types"

// 病案查询表格columns
export const medicalRecordCollectionColumns: Array<TableColumnItem> = [
  { type: "selection", width: 50, fixed: "left" },
  { prop: "labelName", label: "个人标签", minWidth: 100, must: true },
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "mrNo", label: "病案号", minWidth: 150, sortable: true, must: true },
  { prop: "visitId", label: "住院次数", minWidth: 90 },
  { prop: "patientSex", label: "性别", minWidth: 60 },
  { prop: "inHospitalDatetime", label: "入院时间", minWidth: 170, sortable: true },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 100 },
  { prop: "outHospitalDatetime", label: "出院时间", minWidth: 170, sortable: true },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 100 },
  { prop: "operation", label: "操作", width: 140, fixed: "right", must: true }
]
