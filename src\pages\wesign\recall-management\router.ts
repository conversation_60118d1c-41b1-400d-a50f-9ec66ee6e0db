import { MenuOperationEnum } from "@/configs"
import type { RouteRecordRaw } from "vue-router"

const recallRouter: RouteRecordRaw = {
  path: "/recall",
  name: "RecallManagement",
  redirect: "/recall/apply",
  meta: {
    title: "召回管理",
    icon: "ri-share-forward-box-line",
    type: "multiple",
    showBreadcrumb: true
  },
  children: [
    {
      path: "/recall/apply",
      component: () => import("./recall-apply/index.vue"),
      meta: { title: "召回申请", operations: [{ id: MenuOperationEnum.Recall, name: "召回" }] }
    },
    {
      path: "/recall/approval",
      meta: { title: "召回审批", operations: [{ id: MenuOperationEnum.View, name: "详情" }] },
      redirect: "/recall/approval/pending",
      children: [
        {
          path: "/recall/approval/pending",
          component: () => import("./recall-approval/ApprovalPending.vue"),
          meta: { title: "待审批" }
        },
        {
          path: "/recall/approval/finished",
          component: () => import("./recall-approval/ApprovalFinished.vue"),
          meta: { title: "已审批" }
        }
      ]
    },
    {
      path: "/recall/history",
      meta: { title: "召回记录", operations: [{ id: MenuOperationEnum.View, name: "查看" }] },
      redirect: "/recall/history/list",
      children: [
        {
          path: "/recall/history/list",
          meta: { title: "列表" },
          component: () => import("./recall-history/index.vue")
        },
        {
          path: "/recall/history/compare",
          component: () => import("@/pages/wesign/recall-management/recall-history/sub-pages/record-compare.vue"),
          meta: { title: "召回比对", hideLayout: true }
        }
      ]
    },
    {
      path: "/recall/history/detail",
      meta: { title: "召回详情", hideMenu: true },
      component: () => import("./recall-history/sub-pages/record-detail.vue")
    }
  ]
}

export default recallRouter
