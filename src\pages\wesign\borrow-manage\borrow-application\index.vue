<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="borrowTab" />
      <SearchForm
        :form-config="borrowApplicationFormConfig"
        :form-state="searchFormState"
        @query-btn-click="handleQuery"
        @reset-btn-click="handleReset"
      >
        <template #after-action>
          <el-badge style="margin-left: auto" :is-dot="advancedOptions ? true : false">
            <el-button
              style="margin-left: auto"
              :type="advancedOptions ? 'primary' : 'default'"
              @click="ComplexSearchRef?.openDrawer()"
            >
              高级搜索
            </el-button>
          </el-badge>
        </template>
      </SearchForm>
    </template>

    <template #table>
      <CommonTable
        ref="borrowApplicationTableRef"
        table-id="borrowApplicationTable"
        :table-columns="borrowApplicationColumns"
        :request-api="getBorrowApplicationListApi"
        :request-params="searchParams"
      >
        <template #header>
          <BatchOperationButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Borrow)"
            type="primary"
            :disabled="!selectedRows.length"
            :icon="Tickets"
            @click="handleBorrowMulti"
          >
            批量借阅
          </BatchOperationButton>
        </template>

        <!-- 自定义字段 -->
        <template #applyDatetime="{ row }">
          {{ formatDatetime(row.applyDatetime) }}
        </template>
        <template #outHospitalDatetime="{ row }">
          {{ formatDatetime(row.outHospitalDatetime) }}
        </template>
        <template #inHospitalDatetime="{ row }">
          {{ formatDatetime(row.inHospitalDatetime) }}
        </template>

        <template #borrowStatus="{ row }">
          <el-tag :type="borrowApplicationOptions[row.borrowStatus].tagType || 'info'">
            {{ borrowApplicationOptions[row.borrowStatus]?.label || "未知" }}
          </el-tag>
        </template>

        <template #operation="{ row }">
          <TableButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Borrow)"
            :tooltip="row.notBorrowReason"
            :disabled="row.isBorrow === 'NO'"
            @click="handleBorrowSingle(row)"
          >
            借阅
          </TableButton>
        </template>
      </CommonTable>
    </template>

    <BorrowDialog
      ref="borrowDialogRef"
      :table-ref="borrowApplicationTableRef"
      :borrow-tree="borrowState.borrowTree"
      :packet-list="borrowState.packetList"
    />

    <!-- 高级搜索 -->
    <ComplexSearch
      ref="ComplexSearchRef"
      filter-type="BORROW_APPLY"
      :disabled-options-prop="disabledOptions"
      :default-active-template-id="defaultActiveTemplateID"
      :default-advanced-options="defaultAdvancedOptions"
      @search="handleQuery"
      @reset="handleReset"
    />
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref, computed, onBeforeMount } from "vue"
import { Tickets } from "@element-plus/icons-vue"
import { CommonTable, TableButton, PageContainer, BatchOperationButton } from "@/base-components"
import { ComplexSearch, SearchForm, TabsRouter } from "@/page-components"
import { MenuOperationEnum } from "@/configs"
import { useSystemStore, useUserStore } from "@/stores"
import { Message, formatDatetime, formatRecordTree } from "@/utils"
import { BorrowDialog } from "./components"
import {
  borrowApplicationColumns,
  menuId,
  borrowApplicationFormConfig,
  borrowTab,
  BorrowState,
  getBorrowInfo,
  borrowApplicationOptions
} from "./config"
import { getBorrowApplicationListApi } from "./interface"
const { hasOperationPermission } = useUserStore()
const systemStore = useSystemStore()

const ComplexSearchRef = ref()

const disabledOptions = ["a11", "a48", "b15", "b16c"]

const defaultActiveTemplateID = ref("")
const defaultAdvancedOptions = ref([])

onBeforeMount(() => {
  const pageParams = systemStore.pageParams.find(item => item.id === location.pathname)
  for (const key in pageParams?.searchFilterForm) {
    searchFormState[key] = pageParams?.searchFilterForm[key]
  }
  for (const key in pageParams?.searchParams) {
    searchParams[key] = pageParams?.searchParams[key]
  }
  defaultActiveTemplateID.value = pageParams?.activeTemplateID || ""
  defaultAdvancedOptions.value = pageParams?.advancedOptions || []
})

const isInit = ref(true)
const advancedOptions = computed(() => {
  let haveFilter = false
  if (isInit.value && (defaultActiveTemplateID.value || defaultAdvancedOptions.value.length > 0)) {
    haveFilter = true
  }
  if (ComplexSearchRef.value?.advancedFilters.filters) {
    haveFilter = true
  }
  return haveFilter
})

/* ======================== 搜索相关数据及方法 ======================== */
const searchFormState = reactive({
  patientFilterProp: "patientName",
  patientFilterValue: "",
  outHospitalDeptWsid: "",
  outHospitalDatetime: ""
})

const searchParams = reactive({
  filters: "",
  orFieldName: "",
  timestamp: 0
})

// 搜索
function handleQuery() {
  const filters: Array<string> = []
  if (searchFormState.patientFilterProp && searchFormState.patientFilterValue) {
    filters.push(`${searchFormState.patientFilterProp}=${searchFormState.patientFilterValue}`)
  }
  if (searchFormState.outHospitalDatetime && searchFormState.outHospitalDatetime.length) {
    filters.push(
      `outHospitalDatetime>=${searchFormState.outHospitalDatetime[0]}`,
      `outHospitalDatetime<=${searchFormState.outHospitalDatetime[1] + (1000 * 60 * 60 * 24 - 1)}`
    )
  }
  if (searchFormState.outHospitalDeptWsid) filters.push(`outHospitalDeptWsid=${searchFormState.outHospitalDeptWsid}`)
  // 获取复杂查询的筛选值
  ComplexSearchRef.value?.getAdvancedFilters()
  if (ComplexSearchRef.value?.advancedFilters.filters)
    filters.push(`${ComplexSearchRef.value?.advancedFilters.filters}`)
  searchParams.filters = filters.join(",")
  searchParams.orFieldName = ComplexSearchRef.value?.advancedFilters.orFieldName || ""
  searchParams.timestamp = new Date().getTime()
  saveParams()
}

// 重置
function handleReset() {
  for (let key in searchFormState) {
    if (key === "patientFilterProp") searchFormState[key] = "patientName"
    else searchFormState[key] = ""
  }
  searchParams.filters = ""
  searchParams.orFieldName = ""
  searchParams.timestamp = new Date().getTime()
  // 清除复杂查询的筛选值
  ComplexSearchRef.value?.resetValue()
  saveParams()
}

function saveParams() {
  isInit.value = false
  systemStore.setPageParams({
    id: location.pathname,
    searchParams: searchParams,
    searchFilterForm: searchFormState,
    activeTemplateID: ComplexSearchRef.value?.activeTemplateID || "",
    advancedOptions: ComplexSearchRef.value?.advancedOptions || []
  })
}

/*===================== 借阅 =====================*/

const borrowApplicationTableRef = ref<InstanceType<typeof CommonTable>>()

// 当前选中表格项
const selectedRows = computed(() => borrowApplicationTableRef.value?.tableState?.selectedRows ?? [])

const borrowDialogRef = ref()
const borrowState = reactive<BorrowState>({
  dialogVisible: false,
  borrowTree: [],
  packetList: [],
  borrowType: "single"
})

// 借阅单条数据
const handleBorrowSingle = (row: any) => {
  if (row.borrowStatus === "CAN_NOT_BORROW") {
    Message.error("存在未归档的病案，不能借阅")
    return
  }
  borrowState.borrowType = "single"

  return getBorrowInfo([row.wsid], "single", setBorrowInfo)
}

// 借阅多条数据
const handleBorrowMulti = () => {
  for (let record of selectedRows.value) {
    if (record.borrowStatus === "CAN_NOT_BORROW") {
      Message.error("存在未归档的病案，不能借阅")
      return
    }
    if (record.isBorrow === "NO") {
      Message.error("所选数据存在已借阅的病案或无法借阅的特殊病案")
      return
    }
  }
  const recordWsidList = selectedRows.value.map(record => record.wsid)
  const borrowType = recordWsidList.length > 1 ? "multi" : "single"
  borrowState.borrowType = borrowType
  return getBorrowInfo(recordWsidList, borrowType, setBorrowInfo)
}

// 设置可借阅信息
const setBorrowInfo = (borrowTree, packetList) => {
  borrowDialogRef.value.open()
  borrowState.borrowTree = formatRecordTree(borrowTree)
  borrowState.packetList = packetList.map(packet => {
    return `${packet.mrNo}(${packet.inpNo})，${packet.patientName}`
  })
}
</script>

<style lang="less" scoped>
.print {
  color: #0a1633;
}

.print-catalogue-list {
  height: 300px;
  font-weight: bold;
  padding: 12px 20px;
  background-color: rgb(250 250 250);
  overflow-y: auto;
  margin-top: 20px;
}

.print-catalogue {
  height: 32px;
  line-height: 32px;
}

.print__footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
}
</style>
