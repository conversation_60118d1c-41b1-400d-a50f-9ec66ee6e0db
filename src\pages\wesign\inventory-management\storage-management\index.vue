<template>
  <div class="store-management-wrapper">
    <div class="store-management-container">
      <!-- 库房列表 -->
      <PageSideList
        ref="pageSideListRef"
        title="库房列表"
        :list-data="storeroomState.list"
        :loading="storeroomState.listLoading"
        :props="storeroomConfigProps"
        @click-add="createstoreroom"
        @click-edit="editStoreRoom"
        @click-delete="deleteStoreRoom"
        @change-active="changeStoreroom"
      />

      <!-- 库房详情 -->
      <div v-if="storeroomState.choiceStoreroomDetail" class="store-detail">
        <BaseTable
          ref="storeLocationTableRef"
          :columns="storeLocationTableColumns"
          border
          class="store-location-table"
          :data="storeroomState.storeLocationList"
        >
          <template #header>
            <div>
              <el-button type="primary" size="mini" @click="addStoreLocation">添加库位</el-button>
            </div>
          </template>
          <template #operation="{ row }">
            <TableButton @click="editStoreLocation(row)">编辑</TableButton>
            <TableButton @click="deleteStoreLocation(row)">删除</TableButton>
          </template>
        </BaseTable>
        <el-pagination
          v-model:page-size="paginationConfig.pageSize"
          v-model:current-page="paginationConfig.currentPage"
          :pager-count="5"
          size="small"
          background
          class="store-location-pagination"
          :page-sizes="[10, 25, 50, 100]"
          layout="sizes, prev, pager, next, jumper, total"
          :total="storeroomState.totalStoreLocation"
          @update:page-size="refreshStoreLocationList"
        />
      </div>
      <el-empty v-else description="请在左侧选择库房" />
    </div>
  </div>

  <!-- 创建/编辑库房/库位 -->
  <EditStoreroomDialog ref="editstoreDialogRef" :confirm-callback="editStoreConfirmCallback" />
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, watch } from "vue"
import { PageSideList } from "@signit/vue3-components"
import { CommonTable, TableButton, BaseTable } from "@/base-components"
import { Message, SystemPrompt, toastError } from "@/utils"
import {
  deleteStoreroomApi,
  checkStorageDeleteApi,
  deleteStoreLocationApi,
  getStoreLocationListApi,
  getStoreroomListApi
} from "../interface"
import EditStoreroomDialog from "./components/edit-storeroom-dialog.vue"
import { storeLocationTableColumns } from "./config"

interface StoreroomType {
  name: string
  wsid: string
  code: string
  address: string
}
const storeroomState = reactive({
  list: [] as Array<StoreroomType>,
  choiceStoreroomDetail: null as Record<string, any> | null,
  listLoading: false,
  detailLoading: false,
  storeLocationList: [] as Array<Record<string, any>>, // 库位列表
  totalStoreLocation: 0 // 库位总数
})

/*=================================库房操作=================================*/
onMounted(() => {
  getStoreroomList()
})

const storeroomConfigProps = { id: "wsid", name: "renderName" }

//获取库房列表
const getStoreroomList = () => {
  storeroomState.listLoading = true
  getStoreroomListApi()
    .then(res => {
      storeroomState.list = res.data.data.documentStorageRooms.map((item: any) => {
        return {
          name: item.documentStorageRoomName,
          renderName: `${item.documentStorageRoomName}(${item.documentStorageRoomNumber})`,
          wsid: item.documentStorageRoomWsid,
          code: item.documentStorageRoomNumber,
          address: item.documentStorageRoomAddress
        }
      })
    })
    .finally(() => {
      storeroomState.listLoading = false
    })
}

//删除库房
const deleteStoreRoom = (data: Record<string, any>) => {
  SystemPrompt("确定要删除该库房吗？").then(() => {
    checkStorageDeleteApi(data.wsid)
      .then((res: any) => {
        if (!res.data.data) {
          return Message.warning("当前库房已保存了病案，不支持删除")
        }
        deleteStoreroomApi(data.wsid)
          .then(() => {
            Message.success("删除库房成功")
            if (storeroomState.choiceStoreroomDetail?.wsid === data.wsid) {
              storeroomState.choiceStoreroomDetail = null
            }
            getStoreroomList()
          })
          .catch(() => {
            Message.error("删除库房失败")
          })
      })
      .catch(err => {
        toastError(err)
      })
  })
}

// 切换库房
const changeStoreroom = (_, data: Record<string, any>) => {
  storeroomState.choiceStoreroomDetail = data
  getStoreLocationList()
}

/*==============================库位详情==============================*/
const storeLocationTableRef = ref<InstanceType<typeof CommonTable>>()

// 分页配置
const paginationConfig = reactive({
  currentPage: 1,
  pageSize: 25
})

// 翻页时更新table数据
watch(
  () => paginationConfig,
  val => {
    getStoreLocationList()
  },
  {
    deep: true
  }
)

// 获取库位列表
const getStoreLocationList = () => {
  if (!storeroomState.choiceStoreroomDetail) return
  getStoreLocationListApi({
    documentStorageRoomWsid: storeroomState.choiceStoreroomDetail?.wsid,
    offset: paginationConfig.pageSize * (paginationConfig.currentPage - 1),
    limit: paginationConfig.pageSize
  }).then((res: any) => {
    const data = res.data.data
    storeroomState.storeLocationList = data.documentStorageRacks.map(item => ({
      ...item,
      name: item.documentStorageRackName,
      wsid: item.documentStorageRackWsid,
      code: item.documentStorageRackNumber,
      documentStorageRackCount: item.documentStorageRackCount
    }))
    storeroomState.totalStoreLocation = data.pageMetadata?.totalElements
  })
}

//刷新库位列表
const refreshStoreLocationList = () => {
  // 如果当前就是第一页则强制刷新
  if (paginationConfig.currentPage === 1) getStoreLocationList()
  else paginationConfig.currentPage = 1
}

// 添加库位
const addStoreLocation = () => {
  editstoreDialogRef.value?.openDialog("create", "storeLocation", {
    roomWsid: storeroomState.choiceStoreroomDetail?.wsid
  })
}

// 编辑库位
const editStoreLocation = (data: Record<string, any>) => {
  editstoreDialogRef.value?.openDialog("edit", "storeLocation", {
    ...data,
    roomWsid: storeroomState.choiceStoreroomDetail?.wsid
  })
}

// 删除库位
const deleteStoreLocation = (data: Record<string, any>) => {
  SystemPrompt("确定要删除该库位吗？").then(() => {
    checkStorageDeleteApi(data.wsid)
      .then((res: any) => {
        if (!res.data.data) {
          return Message.warning("当前库位已保存了病案，不支持删除")
        }
        deleteStoreLocationApi(data.wsid)
          .then(() => {
            Message.success("删除库位成功")
            refreshStoreLocationList()
          })
          .catch(() => {
            Message.error("删除库位失败")
          })
      })
      .catch(err => {
        toastError(err)
      })
  })
}

/*===============================编辑库房/库位弹窗===============================*/
const editstoreDialogRef = ref<InstanceType<typeof EditStoreroomDialog>>()
// 新建库房
const createstoreroom = () => {
  editstoreDialogRef.value?.openDialog("create", "storeroom", null)
}
//编辑库房
const editStoreRoom = (data: Record<string, any>) => {
  editstoreDialogRef.value?.openDialog("edit", "storeroom", data)
}

// 刷新库房/位列表
const editStoreConfirmCallback = () => {
  console.log("刷新库房列表")
  if (editstoreDialogRef.value?.businessType === "storeroom") {
    getStoreroomList()
  } else {
    refreshStoreLocationList()
  }
}
</script>

<style lang="less" scoped>
.store-management-wrapper {
  min-width: 980px;
  height: 100%;

  .store-management-container {
    height: 100%;
    display: flex;
    gap: 20px;
    > div {
      box-shadow: 0 2px 8px rgba(0, 35, 114, 0.1);
      background-color: #fff;
      border-radius: 4px;
      padding: 20px;
    }
    .store-list {
      width: 300px;
      .store-list-header {
        display: flex;
        justify-content: space-between;
        padding-bottom: 20px;
        span {
          font-size: 16px;
          font-weight: 500;
          color: #333;
        }
      }
      .store-item {
        display: flex;
        justify-content: space-between;
        padding: 10px;
        cursor: pointer;
        &:hover {
          background-color: #f5f7fa;
        }
      }
    }
    .store-detail {
      flex: 1;
      .store-location-table {
        height: calc(100% - 60px);
      }
      .store-location-pagination {
        display: flex;
        justify-content: flex-end;
        margin-top: 20px;
      }
    }
    :deep(.el-empty) {
      flex: 1;
    }
  }
}
</style>
