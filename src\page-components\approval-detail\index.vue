<template>
  <div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="审批记录" name="approvalRecord">
        <ApprovalRecord :business-key="props.businessKey" />
      </el-tab-pane>
      <el-tab-pane label="流程图" name="processGraph">
        <ProcessGraph :business-key="props.businessKey" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue"
import ApprovalRecord from "./ApprovalRecord.vue"
import ProcessGraph from "./ProcessGraph.vue"
interface Props {
  businessKey: string
}

const props = defineProps<Props>()
const activeName = ref("approvalRecord")

onMounted(() => {
  activeName.value = "approvalRecord"
})
</script>
