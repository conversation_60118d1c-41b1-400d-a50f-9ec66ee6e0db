<template>
  <div id="catalogue-dialog" v-loading="loading">
    <SelectTree ref="selectTreeRef" :tree-data="treeData" :default-checked-keys="defaultCheckedKeys" />
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from "vue"
import { SelectTree } from "@/base-components"
import { getSchemesCataloguesApi } from "../interface"

const props = defineProps({
  hasChoiceData: {
    type: Array,
    default: () => []
  }
})

const treeData = ref([])
const selectTreeRef = ref()
const loading = ref(false)
const defaultCheckedKeys = ref([])
onMounted(() => {
  getSchemesCatalogues()
})

watch(
  () => props.hasChoiceData,
  n => {
    defaultCheckedKeys.value = getTreeKey(n)
    console.log("n:", n)
  },
  {
    deep: true,
    immediate: true
  }
)

function getTreeKey(tree) {
  let keyArr = []
  if (!tree) return []
  tree.forEach(item => {
    if (item.type === "CATALOGUE" && !item.isCatalogueChild) {
      keyArr.push(item.wsid)
    }
    if (Object.hasOwnProperty.call(item, "children")) {
      const arr = getTreeKey(item.children)
      keyArr = [...keyArr, ...arr]
    }
  })
  return keyArr
}

const getSchemesCatalogues = async () => {
  loading.value = true
  try {
    const res = await getSchemesCataloguesApi()
    const defaultCatalogues = res.data?.data?.defaultCatalogues ?? []
    handleTreeData(defaultCatalogues)
    treeData.value = defaultCatalogues
  } catch (error) {
    console.log("error:", error)
  }
  loading.value = false
}

const handleTreeData = tree => {
  const _handleTree = tree => {
    for (let i = 0; i < tree.length; i++) {
      tree[i].label = tree[i].name
      tree[i].treeId = tree[i].wsid
      // tree[i].parent = tree
      if (Object.hasOwnProperty.call(tree[i], "children")) {
        _handleTree(tree[i].children)
      }
    }
  }
  _handleTree(tree)
}

//暴露
const getCheckedKeys = (flag = false) => {
  console.log(selectTreeRef.value?.getCheckedKeys(flag))
  return selectTreeRef.value?.getCheckedKeys(flag)
}

const changeLoading = (flag = false) => {
  loading.value = flag
}

defineExpose({ getCheckedKeys, changeLoading })
</script>
<style lang="less" scoped>
#catalogue-dialog {
  margin-top: -10px;
  max-height: 60vh;
  overflow-y: scroll;
  padding-right: 10px;
}
</style>
