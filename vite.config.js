import { resolve } from "path"
import legacy from "@vitejs/plugin-legacy"
import vue from "@vitejs/plugin-vue"
import vueJsx from "@vitejs/plugin-vue-jsx"
import autoprefixer from "autoprefixer"
import postcssPresetEnv from "postcss-preset-env"
import AutoImport from "unplugin-auto-import/vite"
import ElementPlus from "unplugin-element-plus/vite"
import { ElementPlusResolver } from "unplugin-vue-components/resolvers"
import Components from "unplugin-vue-components/vite"
import { defineConfig, loadEnv, normalizePath } from "vite"
import { viteStaticCopy } from "vite-plugin-static-copy"

export default defineConfig(({ mode }) => {
  return {
    plugins: [
      vue(),
      viteStaticCopy({
        targets: [
          {
            src: normalizePath(resolve(__dirname, "./src/plugins")),
            dest: "src"
          },
          {
            src: normalizePath(resolve(__dirname, "./src/libs")),
            dest: "src"
          }
        ]
      }),
      vueJsx(),
      ElementPlus({
        useSource: true
      }),
      AutoImport({
        resolvers: [ElementPlusResolver({ importStyle: "sass" })]
      }),
      Components({
        resolvers: [ElementPlusResolver({ importStyle: "sass" })]
      }),
      legacy({
        targets: ["chrome >= 49"]
      })
    ],
    base: "/",
    publicPath: "./",
    publicDir: resolve(__dirname, "public"),
    define: {
      "process.env": loadEnv(mode, process.cwd())
    },
    build: {
      target: "modules",
      sourcemap: false,
      outDir: "dist",
      emptyOutDir: true,
      assetsDir: "static",
      chunkSizeWarningLimit: 1500,
      cssCodeSplit: true
    },
    resolve: {
      alias: {
        "@": resolve(__dirname, "src")
      }
    },
    css: {
      postcss: {
        plugins: [postcssPresetEnv({ browsers: "Chrome >= 49" }), autoprefixer()]
      }
    },
    server: {
      hmr: true,
      useLocalIp: true,
      disableHostCheck: true,
      host: "0.0.0.0",
      port: 8080, // 服务器端口号
      open: "/login", // 自动打开浏览器
      proxy: {
        "/api": {
          // target: "http://**********:16153",
          // target: "http://**************:18456",
          target: "http://***********:16153",
          // target: "http://**************:16153",
          // target: "https://wesign-emrdev-web.signit.vip:10443/api",
          changeOrigin: true,
          rewrite: path => path.replace(/^\/api/, "")
        },
        "/emr-scan": {
          target: "https://wesign-hospital-web.signit.vip:10443",
          changeOrigin: true,
          secure: true
        },
        "/umiocr": {
          target: "http://**********:1224",
          changeOrigin: true,
          rewrite: path => path.replace(/^\/umiocr/, "")
        }
      }
    }
  }
})
