import type { TableColumnItem, DialogFormConfigItem } from "@/types"

export const tabsRouterList = [
  { label: "ICD-10", path: "/dictionary-manage/icd/icd10" },
  { label: "ICD-9-CM-3", path: "/dictionary-manage/icd/icd9cm3" },
  { label: "肿瘤形态学编码(M码)", path: "/dictionary-manage/icd/tumour" },
  { label: "门诊慢特病病种", path: "/dictionary-manage/icd/outpatient-disease" },
  { label: "日间手术病种", path: "/dictionary-manage/icd/day-surgery-disease" },
  { label: "中医疾病诊断", path: "/dictionary-manage/icd/chinese-medicine-disease" },
  { label: "病种对照", path: "/dictionary-manage/icd/disease-control" },
  { label: "手术对照", path: "/dictionary-manage/icd/surgical-control" }
]

export enum ICDTypeEnum {
  ICD_10 = "ICD-10", // ICD-10
  ICD_9_CM_3 = "ICD-9-CM-3", // ICD-9-CM-3
  ICD_O = "ICD-O", // 肿瘤形态学编码
  ICD_MZ = "ICD-MZ", // 门诊慢特病病种
  ICD_RJ = "ICD-RJ", // 日间手术病种
  ICD_ZY = "ICD-ZY", // 中医疾病诊断
  ICD_BZ_DM = "ICD-BZ-DM", // 病种对照
  ICD_SS_DM = "ICD-SS-DM" // 手术对照
}

export const menuId = "/icd"

const icdCodeValidator = (_rule: any, value: string, callback: (err?: Error) => void) => {
  if (value === "") {
    callback(new Error("请输入"))
  } else {
    // 先判断是否超过40个字符
    if (value.length > 40) {
      callback(new Error("长度不能超过40字符"))
    } else {
      const reg = /^[^\u4e00-\u9fa5]{0,40}$/
      if (reg.test(value)) {
        callback()
      } else {
        callback(new Error("请输入数字、字母或特殊字符"))
      }
    }
  }
}

// 校验名称，长度100字符
const icdNameValidator = (_rule: any, value: string, callback: (err?: Error) => void) => {
  if (value === "") {
    callback(new Error("请输入名称"))
  } else {
    // 先判断是否超过100个字符
    if (value.length > 100) {
      callback(new Error("长度不能超过100字符"))
    } else {
      callback()
    }
  }
}

/* ======================== ICD10 ======================== */

export const icd10TableColumns: Array<TableColumnItem> = [
  { prop: "code", label: "编码", minWidth: 150 },
  { prop: "name", minWidth: 200, label: "名称" },
  { prop: "additionalCode", label: "附加编码", minWidth: 150 },
  { prop: "greyCode", label: "医保灰码", minWidth: 150 },
  { prop: "operation", label: "操作", width: 150, fixed: "right" }
]

export const icd10FormRules = {
  code: [
    { required: true, message: "请输入编码", trigger: "blur" },
    { validator: icdCodeValidator, trigger: "blur" }
  ],
  name: [
    { required: true, message: "请输入名称", trigger: "blur" },
    { validator: icdNameValidator, trigger: "blur" }
  ],
  additionalCode: [
    {
      validator: (_rule: any, value: string, callback: (err?: Error) => void) => {
        if (value.length > 40) {
          callback(new Error("长度不能超过40字符"))
        } else {
          const reg = /^[a-zA-Z0-9#@!~%^&*]{0,40}$/
          if (reg.test(value)) {
            callback()
          } else {
            callback(new Error("请输入数字、字母或特殊字符"))
          }
        }
      },
      trigger: "blur"
    }
  ]
}

/* ======================== ICD9 ======================== */

export const icd9TableColumns: Array<TableColumnItem> = [
  { prop: "code", label: "编码", minWidth: 150 },
  { prop: "name", label: "名称", minWidth: 150 },
  { prop: "typeName", minWidth: 200, label: "操作类别" },
  { prop: "entryOptions", minWidth: 200, label: "录入选项" },
  { prop: "greyCode", label: "医保灰码", minWidth: 150 },
  { prop: "operation", label: "操作", width: 150, fixed: "right" }
]

// 操作类别选项列表
export const typeNameOptions = [
  { label: "介入治疗", value: "介入治疗" },
  { label: "手术", value: "手术" },
  { label: "诊断性操作", value: "诊断性操作" },
  { label: "治疗性操作", value: "治疗性操作" }
]

// 录入选项列表
export const entryOptionsList = [
  { label: "必选", value: "必选" },
  { label: "选择性", value: "选择性" },
  { label: "中医必选", value: "中医必选" }
]

export const icdCommonFormRules = {
  code: [
    { required: true, message: "请输入编码", trigger: "blur" },
    { validator: icdCodeValidator, trigger: "blur" }
  ],
  name: [
    { required: true, message: "请输入名称", trigger: "blur" },
    { validator: icdNameValidator, trigger: "blur" }
  ]
}

/* ======================== 肿瘤形态学编码 ======================== */

export const tumourTableColumns: Array<TableColumnItem> = [
  { prop: "code", label: "编码", minWidth: 150 },
  { prop: "name", minWidth: 200, label: "名称" },
  { prop: "operation", label: "操作", width: 150, fixed: "right" }
]

/*========================门诊慢特病病种=======================*/
export const outpatientDiseaseTableColumns: Array<TableColumnItem> = [
  { prop: "code", label: "编码", minWidth: 150 },
  { prop: "name", minWidth: 200, label: "名称" },
  { prop: "operation", label: "操作", width: 150, fixed: "right" }
]

/*=================================手术对照和病种对照=================================*/
export const diseaseControlTableColumns: Array<TableColumnItem> = [
  { prop: "code", label: "国临2.0疾病编码", minWidth: 150 },
  { prop: "name", minWidth: 200, label: "国临2.0疾病名称" },
  { prop: "contrastCode", label: "医保2.0疾病编码", minWidth: 150 },
  { prop: "contrastName", minWidth: 200, label: "医保2.0疾病名称" },
  { prop: "operation", label: "操作", width: 150, fixed: "right" }
]

export const controlTableColumns: Array<TableColumnItem> = [
  { prop: "code", label: "国临3.0手术代码", minWidth: 150 },
  { prop: "name", minWidth: 200, label: "国临3.0手术名称" },
  { prop: "contrastCode", label: "医保2.0手术代码", minWidth: 150 },
  { prop: "contrastName", minWidth: 200, label: "医保2.0手术名称" },
  { prop: "operation", label: "操作", width: 150, fixed: "right" }
]
