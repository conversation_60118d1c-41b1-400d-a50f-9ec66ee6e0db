import type { RouteRecordRaw } from "vue-router"
import UserSetting from "./index.vue"
import BasicInfo from "./components/BasicInfo.vue"
import SecurityCentre from "./components/SecurityCentre.vue"

const userSettingRouter: RouteRecordRaw = {
  path: "/user-setting",
  name: "UserSetting",
  component: UserSetting,
  redirect: "/user-setting/basic-info",
  meta: { title: "账户设置", showBreadcrumb: false },
  children: [
    { path: "basic-info", component: BasicInfo },
    { path: "security-center", component: SecurityCentre }
  ]
}

export default userSettingRouter
