import { DateModelType } from "element-plus"
import axios from "@/interfaces/axios-instance"

/* ==================== 上报文书 ======================= */

// 死亡/非医嘱离院--上报列表查询
export function getSubmitDocumentList(params) {
  const { offset, limit, filters, queryParams } = params
  return axios({
    method: "get",
    url: `/api/document/bag/query/death`,
    params: {
      offset: offset,
      limit: limit,
      filters: filters,
      ...queryParams
    }
  })
}

// 死亡/非医嘱离院--上报数据导出
export function downloadSubmitDocumentPdf(params: Record<string, any>) {
  return axios({
    method: "get",
    url: `/api/document/bags/death/export?${params.data}`,
    responseType: "blob"
  })
}

// 死亡/非医嘱离院--上报数据生成
export function generateHqmsDataApi(documentBagWsid: string) {
  return axios({
    method: "post",
    url: `/api/document/bags/death/generate?documentBagWsid=${documentBagWsid}`
  })
}

/* ==================== 门急诊上报 ======================= */

// 获取门急诊上报列表
export const getOutpatientListApi = (data: {
  medicalNos: string
  names: string
  deptName: string[]
  consultationTimeStart: string
  consultationTimeEnd: string
  reviewStatus: string
  sorts?: string
  limit?: number
  offset?: number
}) => {
  return axios({
    method: "post",
    url: `/api/catalog/outpatient-hqms/page-query`,
    data: data
  })
}

// 导出cvs文件
export const exportToCvsApi = data => {
  return axios({
    method: "post",
    url: `/api/catalog/outpatient-hqms/export`,
    data: data,
    timeout: 60000,
    responseType: "blob"
  })
}

// 导出至远程目录
export const exportOutpatientDataToRemoteApi = data => {
  return axios({
    method: "post",
    url: `/api/catalog/outpatient-hqms/export-share-directory`,
    data: data
  })
}

// 审核门急诊上报
export const auditOutpatientApi = (wsids: string[]) => {
  return axios({
    method: "post",
    url: `/api/catalog/outpatient-hqms/review`,
    data: wsids
  })
}

// 检查数据源门诊数量
export const checkOutpatientDataSourceApi = (data: {
  registerNos: string
  medicalNos: string
  patientNames: string
  startTime: DateModelType
  endTime: DateModelType
}) => {
  return axios({
    method: "post",
    url: `/api/outpatient/sync/count`,
    data: data
  })
}

// 同步门急诊数据
export const syncOutpatientDataApi = (data: {
  registerNos: string
  patientNames: string
  startTime: string
  endTime: string
}) => {
  return axios({
    method: "post",
    url: `/api/outpatient/sync`,
    data: data,
    timeout: 60000
  })
}

// 同步门急诊数据
export const syncOutpatientDataSubApi = (data: {
  registerNos: string
  patientNames: string
  startTime: string
  endTime: string
}) => {
  return axios({
    method: "post",
    url: `/api/outpatient/sync/sub`,
    data: data,
    timeout: 60000
  })
}

// 门急诊-下载 差集
export const diffDownloadOutpatientDataSubApi = (data: {
  registerNos: string
  patientNames: string
  medicalNos: string
  startTime: DateModelType
  endTime: any
}) => {
  return axios({
    method: "post",
    url: `/api/outpatient/sync/diff-download`,
    data: data,
    timeout: 60000,
    responseType: "blob"
  })
}

// 获取门急诊导出记录条数
export const getOutpatientExportLimitApi = () => {
  return axios({
    method: "get",
    url: `/api/catalog/outpatient-hqms/document-max-number`
  })
}

// 门急诊列表导出记录
export const getOutpatientExportRecordApi = (params: IPaginationRequestParams) => {
  params.sorts = "-createdDatetime"
  return axios({
    method: "get",
    url: `/api/catalog/outpatient-hqms/export-record`,
    params: params
  })
}

// 导出记录中下载
export const downloadOutpatientFileApi = (wsids: string[]) => {
  return axios({
    method: "post",
    url: `/api/catalog/outpatient-hqms/file-download`,
    data: wsids,
    timeout: 60000,
    responseType: "blob"
  })
}

/* ======================= 住院数据上报 ====================== */

export interface IInHospitalListParams {
  mrNos: string
  names: string
  outHospitalDeptWsid: string
  outHospitalTimeStart: DateModelType | number
  outHospitalTimeEnd: DateModelType | number
  reviewStatus: string
  registerNos?: string[]
  inpNos?: string[] // 导出需要的住院号
}

// 获取住院上报数据列表
export const getInHospitalListApi = (data: IInHospitalListParams) => {
  return axios({
    method: "post",
    url: `/api/catalog/inpatient-hqms/page-query`,
    data: data
  })
}

// 导出住院数据到本地
export const exportInHospitalDataToLocalApi = (data: IInHospitalListParams) => {
  return axios({
    method: "post",
    url: `/api/catalog/inpatient-hqms/export`,
    data: data,
    timeout: 60000,
    responseType: "blob"
  })
}

// 导出住院数据到远程目录
export const exportInHospitalDataToRemoteApi = (data: IInHospitalListParams) => {
  return axios({
    method: "post",
    url: `/api/catalog/inpatient-hqms/export-share-directory`,
    data: data
  })
}

// 下载住院数据上报文件
export const downloadInHospitalFileApi = (wsids: string[]) => {
  return axios({
    method: "post",
    url: `/api/catalog/inpatient-hqms/file-download`,
    data: wsids,
    timeout: 60000,
    responseType: "blob"
  })
}

// 获取住院数据上报文件最大限制
export const getInHospitalExportLimitApi = () => {
  return axios({
    method: "get",
    url: `/api/catalog/inpatient-hqms/document-max-number`
  })
}

// 住院数据导出记录列表
export const getInHospitalExportRecordApi = (params: IPaginationRequestParams) => {
  params.sorts = "-createdDatetime"
  return axios({
    method: "get",
    url: `/api/catalog/inpatient-hqms/export-record`,
    params: params
  })
}

// 审核住院数据
export const auditInHospitalApi = (wsids: string[]) => {
  return axios({
    method: "post",
    url: `/api/catalog/inpatient-hqms/review`,
    data: wsids
  })
}
