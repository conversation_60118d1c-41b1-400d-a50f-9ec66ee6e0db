import type { SearchFormConfigItem, TableColumnItem } from "@/types"

export const tabsRouterList = [
  { label: "待审批", path: "/recall/approval/pending" },
  { label: "已审批", path: "/recall/approval/finished" }
]

export const getApprovalStatus = value => {
  if (value === "PASS") return "已同意"
  if (value === "DENY") return "已驳回"
  if (value === "ROLLBACK") return "回退"
}

export const menuId = "/recall/approval"

const recallApprovalStatusOptions = [
  { value: "PASS", label: "已同意", color: "success" },
  { value: "DENY", label: "已驳回", color: "danger" },
  { value: "ROLLBACK", label: "回退", color: "warning" }
]

// 召回审批公共表格配置
const recallApprovalCommonTableColumns: Array<TableColumnItem> = [
  { prop: "applicantName", label: "申请人", minWidth: 100, must: true, fixed: "left" },
  { prop: "createdDatetime", label: "申请时间", minWidth: 180, sortable: true },
  { prop: "recallReason", label: "召回原因", minWidth: 200, must: true },
  { prop: "recallTimesDescribe", label: "召回时长", minWidth: 100, must: true },
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 120, must: true, sortable: true },
  { prop: "mrNo", label: "病案号", minWidth: 100, must: true, sortable: true },
  { prop: "visitId", label: "住院次数", sortable: true, minWidth: 120 },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 120, must: true },
  { prop: "inHospitalDatetime", label: "入院时间", minWidth: 180, sortable: true },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 120, must: true },
  { prop: "outHospitalDatetime", label: "出院时间", minWidth: 180, sortable: true },
  { prop: "outDischargeDiagnosisName", label: "出院主诊断", minWidth: 200, must: true },
  { prop: "recallCount", label: "召回次数", minWidth: 120, sortable: true, must: true },
  { prop: "archivistName", label: "归档人", minWidth: 100 }
]

/* ======================== 召回待审批 ======================== */

// 待审批搜索表单配置
export const approvalPendingSearchFormConfig: Array<SearchFormConfigItem> = [
  { type: "patient", label: "患者信息" },
  { type: "daterange", label: "入院时间", prop: "inHospitalDatetime" },
  { type: "daterange", label: "出院时间", prop: "outHospitalDatetime" }
]

// 待审批表格配置
export const recallApprovalPendingTableColumns: Array<TableColumnItem> = [
  ...recallApprovalCommonTableColumns,
  { prop: "operation", label: "操作", width: 75, fixed: "right", must: true }
]

/* ======================== 召回已审批 ======================== */

// 已审批搜索表单配置
export const approvalFinishedSearchFormConfig: Array<SearchFormConfigItem> = [
  { type: "patient", label: "患者信息" },
  { type: "select", label: "审批状态", prop: "approveStatus", options: recallApprovalStatusOptions },
  { type: "daterange", label: "入院时间", prop: "inHospitalDatetime" },
  { type: "daterange", label: "出院时间", prop: "outHospitalDatetime" },
  { type: "daterange", label: "审批时间", prop: "approverDatetime" }
]

// 已审批表格配置
export const recallApprovalFinishedTableColumns: Array<TableColumnItem> = [
  ...recallApprovalCommonTableColumns,
  { prop: "approverName", label: "审批人", minWidth: 150, must: true },
  { prop: "approveStatus", label: "审批状态", minWidth: 120, must: true },
  { prop: "approverDatetime", label: "审批时间", minWidth: 180, sortable: true },
  { prop: "operation", label: "操作", width: 75, fixed: "right", must: true }
]
