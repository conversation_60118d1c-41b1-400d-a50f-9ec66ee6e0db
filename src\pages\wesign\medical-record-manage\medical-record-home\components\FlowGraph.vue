<template>
  <div class="process-graph-wrapper">
    <div class="process-graph">
      <div class="start-node">
        <span>开始</span>
      </div>
      <div class="process-edge"></div>

      <!-- 循环生成审批节点 -->
      <div v-for="(node, index) in processNodes" :key="index" class="process-nodes">
        <!-- 分组的节点（hide的不显示-即所有child为DISABLE的group节点） -->
        <template v-if="node.groupName && !node.hide">
          <div class="grouped-node">
            <div class="grouped-node-header">
              <img :src="businessProcessIcons.find(icon => icon.label === node?.groupName)?.img" />
              <div class="grouped-node-name">{{ node.groupName }}</div>
            </div>
            <div class="grouped-node-content">
              <template v-for="(child, childIndex) in node.children" :key="child.businessNodeCode">
                <!-- ENABLE的节点才显示 -->
                <template v-if="child.status !== 'DISABLE'">
                  <div class="process-node" :style="{ borderColor: getFlowStatusInfo(child?.status)?.borderColor }">
                    <div class="process-node-name">
                      <img :src="businessProcessIcons.find(icon => icon.label === child?.businessNodeName)?.img" />
                      {{ child?.businessNodeName }}
                    </div>
                    <span :style="{ color: getFlowStatusInfo(child?.status)?.color }">
                      {{ getFlowStatusInfo(child?.status)?.text }}
                    </span>
                  </div>
                  <div v-if="childIndex !== node.children.length - 1" class="process-edge"></div>
                </template>
              </template>
            </div>
          </div>
          <div class="process-edge"></div>
        </template>

        <!-- 未分组的节点 -->
        <template v-if="!node.groupName && node.status !== 'DISABLE'">
          <div class="process-node" :style="{ borderColor: getFlowStatusInfo(node?.status)?.borderColor }">
            <div class="process-node-name">
              <img :src="businessProcessIcons.find(icon => icon.label === node?.businessNodeName)?.img" />
              {{ node?.businessNodeName }}
            </div>
            <span :style="{ color: getFlowStatusInfo(node?.status)?.color }">
              {{ getFlowStatusInfo(node?.status)?.text }}
            </span>
          </div>
          <div class="process-edge"></div>
        </template>
      </div>
      <!-- 结束节点 -->
      <div class="end-node">
        <span>结束</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from "vue"
import { cloneDeep } from "lodash/fp"
import { businessProcessIcons } from "@/utils"

interface Props {
  data: Array<Record<string, any>>
}
const props = defineProps<Props>()

const processNodes = ref<Array<Record<string, any>>>([])

const getFlowStatusInfo = (status?: string) => {
  switch (status) {
    case "ing":
      return {
        text: "处理中",
        color: "#FF9D03",
        borderColor: "#FF9D03"
      }
    case "notStart":
      return {
        text: "待处理",
        color: "#999999",
        borderColor: "#DCDFE6"
      }
    case "complete":
      return {
        text: "已完成",
        color: "#42B625",
        borderColor: "#42B625"
      }
    default:
      return {
        text: "处理中",
        color: "#DADFE7",
        borderColor: "#DADFE7"
      }
  }
}

interface ProcessNode {
  businessNodeCode: string
  businessNodeName: string
  groupName: string
  nextNodeCode: string
  status: string
}

interface GroupedProcessNode {
  groupName: string
  hide?: boolean
  children: ProcessNode[]
}

const formatData = () => {
  // 然后找某个节点的nextNodeCode是这个节点的，就是这个节点的上一个节点
  const findPrevNode = (targetNode: any) => {
    const prevNode = props.data.find(node => node.nextNodeCode === targetNode.businessNodeCode)
    if (prevNode) {
      processData.unshift(prevNode)
      findPrevNode(prevNode)
    }
  }

  // 找到质控节点并合并
  const mergeGroupedNodes = (nodes: any[]) => {
    // 根据groupName进行分组，相同的放在一组下，并生成一个新的父节点，使用groupName作为新节点的名称
    let groupedNodes: GroupedProcessNode[] = []
    for (let i = 0; i < nodes.length; i++) {
      const groupName = nodes[i].groupName
      if (!groupName) continue
      const existedGroup = groupedNodes.find(node => node.groupName === groupName)
      if (existedGroup) {
        existedGroup.children.push(nodes[i])
      } else {
        groupedNodes.push({
          groupName: groupName,
          children: [nodes[i]]
        })
      }
    }

    // 遍历已分组，如果所有child的status都为disable，则设置为hide为true
    for (let groupedNode of groupedNodes) {
      if (groupedNode.children.every(child => child.status === "DISABLE")) {
        groupedNode.hide = true
      }
    }

    // 将分组后的节点插入到原节点中
    nodes = nodes.filter(node => !node.groupName)
    for (let groupedNode of groupedNodes) {
      const prevNodeIndex = nodes.findIndex(node => node.nextNodeCode === groupedNode.children[0].businessNodeCode)
      if (prevNodeIndex > -1) {
        nodes.splice(prevNodeIndex + 1, 0, groupedNode)
      }
    }
    return nodes
  }

  let processData: any[] = []
  // 找到结束节点。也就是没有nextNodeCode的节点，这个是结束节点
  const lastNode = props.data.find(node => !node.nextNodeCode)
  processData.unshift(lastNode)
  findPrevNode(lastNode)
  processData = mergeGroupedNodes(processData)
  processNodes.value = cloneDeep(processData)
  console.log(processNodes.value)
}

watch(
  () => props.data,
  () => {
    formatData()
  },
  { deep: true }
)

onMounted(() => {
  formatData()
})
</script>

<style lang="scss" scoped>
.process-graph-wrapper {
  display: flex;
  justify-content: center;

  .process-graph {
    width: 100%;
    max-width: 350px;
  }

  .start-node {
    width: 116px;
    height: 32px;
    background: #42b625;
    box-shadow: 0px 2px 8px 0px rgba(51, 51, 51, 0.1);
    border-radius: 30px 30px 30px 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: auto;
    span {
      color: #fff;
      font-size: 14px;
      font-weight: 600;
      line-height: 16px;
    }
  }
  .end-node {
    width: 116px;
    height: 32px;
    background: #333333;
    box-shadow: 0px 2px 8px 0px rgba(51, 51, 51, 0.1);
    border-radius: 34px 34px 34px 34px;
    border: 1px solid #dcdfe6;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: auto;
    span {
      color: #fff;
      font-size: 14px;
      font-weight: 600;
      line-height: 16px;
    }
  }

  .process-nodes {
    display: flex;
    flex-direction: column;
    width: 100%;

    .grouped-node {
      width: 100%;
      border-radius: 8px;

      .grouped-node-header {
        font-size: 14px;
        padding: 10px 0;
        width: 100%;
        background: #f0f4fb;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;

        .grouped-node-name {
          margin-left: 8px;
        }
      }

      .grouped-node-content {
        padding: 20px;
        background: #e4eaf2;
      }
    }

    .process-node {
      padding: 12px 16px;
      width: calc(100% - 34px);
      box-shadow: 0px 2px 8px 0px rgba(51, 51, 51, 0.1);
      border-radius: 4px;
      border: 1px solid #dcdfe6;
      background: #fff;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .process-node-name {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }

  .process-edge {
    width: 1px;
    height: 34px;
    background-color: rgba(193, 197, 205, 1);
    margin: auto;
    margin-top: 4px;
    margin-bottom: 10px;
    position: relative;
    &::before {
      // 添加一个等边三角形
      content: "";
      position: absolute;
      bottom: -6px;
      left: -6px;
      border-left: 6px solid transparent;
      border-right: 6px solid transparent;
      border-top: 6px solid rgba(193, 197, 205, 1);
    }
  }
}
</style>
