<template>
  <div class="widget-select-panel">
    <el-tabs v-model="activeTab" class="form-edit-tabs">
      <el-tab-pane label="基础控件" :name="TabEnum.Basic"></el-tab-pane>
      <el-tab-pane label="数据规范" :name="TabEnum.DataStand"></el-tab-pane>
    </el-tabs>
    <!-- 基础控件 -->
    <template v-if="activeTab === TabEnum.Basic">
      <VueDraggable
        v-model="baseList"
        class="group-wrapper"
        filter=".title-group-item"
        :sort="false"
        :group="{ name: 'people', pull: 'clone', put: false }"
        :clone="clone"
        @move="isMoving"
        @end="isMovingEnd"
        @clone="toClone"
      >
        <div
          v-for="item in baseList"
          :key="item.type"
          class="group-item"
          :class="item.type === 'title' ? 'title-group-item' : 'form-item'"
        >
          <div v-if="item.type === 'title'" class="group-item-title">{{ item.name }}</div>

          <FormItemWidget v-else :type="item.type" :icon="item.icon" :name="item.name" />
        </div>
      </VueDraggable>
    </template>
    <!-- 数据规范分类字段 -->
    <template v-else>
      <!-- <VueDraggable
        v-model="standardList"
        class="group-wrapper"
        filter=".title-group-item"
        :sort="false"
        :group="{ name: 'people', pull: 'clone', put: false }"
        :clone="clone"
        handle=".content"
        @move="isMoving"
        @end="isMovingEnd"
        @clone="toClone"
      > -->
      <div class="standard-wrapper">
        <el-input v-model="filterText" style="margin-bottom: 10px" :prefix-icon="Search" placeholder="字段名称" />

        <CommonTree
          ref="standardTreeRef"
          v-loading="standardLoading"
          node-key="id"
          :data="standardList"
          :filter-tree-node="filterNode"
          :filter-keys="filterText"
          @click-node="selectStandard"
        >
          <template #title="{ node, data }">
            <div class="content">
              <span style="margin-right: 4px">{{ data.fileName }}</span>
            </div>
          </template>
        </CommonTree>
      </div>

      <!-- </VueDraggable> -->
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue"
import { VueDraggable } from "vue-draggable-plus"
import { Search } from "@element-plus/icons-vue"
import { CommonTree } from "@/base-components"
import { FormItemType, StandardFormItemType } from "@/configs"
import { getCatalogStandardList, getHomeMetaListApi, getStandardsFieldApi } from "@/interfaces"
import { Message, toastError } from "@/utils"
import FormItemWidget from "./components/form-item-widget.vue"
import { createFormItem, reportFormItemType } from "./config"

const emits = defineEmits(["isMoving", "addFormItem"])

enum TabEnum {
  Basic = "Basic",
  DataStand = "DataStand"
}

const activeTab = ref(TabEnum.Basic)

// 基础字段
const baseList = ref([
  { type: "title", icon: "", name: "布局控件" },
  { type: FormItemType.GroupHeadline, icon: "ri-heading", name: "分组标题" },
  { type: FormItemType.AnchorPoint, icon: "ri-heading", name: "锚点标题" },

  { type: "title", icon: "", name: "基础控件" },
  { type: FormItemType.BaseInput, icon: "ri-edit-line", name: "文本" },
  { type: FormItemType.BaseAddress, icon: "ri-map-pin-line", name: "地址" },
  { type: FormItemType.BaseNumber, icon: "ri-number-0", name: "数字" },
  { type: FormItemType.BaseDate, icon: "ri-calendar-line", name: "日期" },
  // { type: FormItemType.BaseTextarea, icon: "ri-edit-box-line", name: "多行文本" },
  { type: FormItemType.BaseSelect, icon: "ri-arrow-drop-down-line", name: "下拉选择" },
  { type: FormItemType.DescText, icon: "ri-text", name: "说明文本" },

  { type: FormItemType.BaseCheckbox, icon: "ri-checkbox-line", name: "多项选择" },
  { type: FormItemType.BaseRadio, icon: "ri-radio-button-line", name: "单项选择" },

  // { type: FormItemType.UserSign, icon: "ri-calendar-line", name: "献血者签名" },
  // { type: FormItemType.StaffSign, icon: "ri-calendar-line", name: "工作人员签名" },
  // { type: FormItemType.InformedConsentFormSign, icon: "ri-calendar-line", name: "知情同意书签名" },
  { type: FormItemType.Table, icon: "ri-table-line", name: "表格" }

  // { type: "title", icon: "", name: "业务控件" },
  // { type: FormItemType.BloodDonationBarcode, icon: "ri-heading", name: "献血条码" },

  // { type: "title", icon: "", name: "控件组" },
  // { type: FormItemType.IdCardGroup, icon: "ri-profile-line", name: "身份证识别" }
])

const props = defineProps<{
  formConfig: any
  appTypeCode: any
}>()

function clone(element) {
  const hasIdCardGroup = props.formConfig.some(item => item.type === FormItemType.IdCardGroup)
  if (hasIdCardGroup && element.type === FormItemType.IdCardGroup) {
    return Message.warning("身份证识别控件只能添加一个")
  }

  const hasBarCode = props.formConfig.some(item => item.type === FormItemType.BloodDonationBarcode)

  if (hasBarCode && element.type === FormItemType.BloodDonationBarcode) {
    return Message.warning("献血条码控件只能添加一个")
  }
  const newFormItem = createFormItem(element.type, element.name)
  console.log(`output->props.formConfig`, props.formConfig)
  return newFormItem
}

function isMoving(e) {
  // 将其他表单移动（单行文本、下拉、数字、日期）到表格内
  // if (!e.to.className.includes("table-form-edit-area")) return

  emits("isMoving", true)
}

function isMovingEnd(e) {
  if (
    e.data.type === FormItemType.BaseInput ||
    e.data.type === FormItemType.BaseDate ||
    e.data.type === FormItemType.BaseSelect ||
    e.data.type === FormItemType.BaseNumber
  ) {
    console.log(`output->左侧控件栏`, e)
  }
  emits("isMoving", false)
}
const toClone = e => {
  console.log(`output->toClonee`, e)
}

/*================================= 数据规范数据 =================================*/
const standardLoading = ref(false)

const standardList = ref<Array<Record<string, any>>>([])
const getStandardList = async () => {
  standardLoading.value = true
  try {
    standardList.value = (
      await getCatalogStandardList({
        catalogType: "FIELD",
        catalogFileType: "SYSTEM",
        filters: `scenario_type=${props.appTypeCode}`
      })
    )?.data?.data
    standardLoading.value = false
  } catch (err: any) {
    toastError(err)
    standardLoading.value = false
  }
}

watch(
  () => props.appTypeCode,
  async () => {
    await getStandardList()
  }
)

// 点击分类  添加表单
const selectStandard = node => {
  if (node.catalogType === "FIELD") {
    const newFormItem = createFormItem(StandardFormItemType[node.widgetType], node.fileName, "", node)
    emits("addFormItem", newFormItem)
  }
}
const filterText = ref("")

const filterNode = (value: string, data: any) => {
  if (!value) return true
  return data.fileName.includes(value)
}
</script>

<style lang="less" scoped>
.widget-select-panel {
  width: 280px;
  // margin-right: 10px;
  height: 100%;
  // padding: 0 20px;

  // border-top: 1px solid #ccc;

  .group-wrapper {
    padding: 0 20px;
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    // height: calc(100% - 30px);
    min-height: 0px;
    align-content: flex-start;
    .group-item {
      width: calc(50% - 5px);
      border: none;
      .group-item-title {
        font-weight: 500;
        font-size: 14px;
        color: #81878e;
      }
    }
    .title-group-item {
      width: 100%;
    }
    .title-group-item:not(:first-child) {
      margin-top: 20px;
    }
  }
}

:deep(.el-tabs__nav) {
  width: 100% !important;
  justify-content: space-evenly !important;
  margin-bottom: 3px;
}

.common-tree {
  height: calc(100% - 112px);
}

.standard-wrapper {
  // padding-right: 20px;
  overflow-y: auto;
  height: calc(100% - 53px);
  padding: 0 20px;
  padding-top: 33px;
}
.form-edit-tabs {
  background: #f8f8fa;
}

:deep(.el-tabs__item.is-active) {
  color: #0a1633;
  font-weight: bold;
}
:deep(.el-tabs__active-bar) {
  // width: 24px !important;
}
:deep(.el-tabs__nav-wrap::after) {
  height: 0px !important;
}

:deep(.el-tabs__header) {
  margin-bottom: 0;
}
</style>
