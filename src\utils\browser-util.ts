const browserList = [
  "Chrome",
  "Firefox",
  "Safari",
  "Opera",
  "Edge",
  "IE",
  "Chrome",
  "Firefox",
  "Safari",
  "Opera",
  "Edge",
  "IE"
]

const chromeVersionMap = {
  low: 49,
  mid: 49,
  high: 90
}

type BrowserVersion = "LOW" | "MID" | "HIGH" | undefined

// 检查浏览器版本
export const checkBrowserVersion = (): BrowserVersion => {
  const ua = navigator.userAgent
  // 判断是否为IE
  const isIE = ua.indexOf("compatible") > -1 && ua.indexOf("MSIE") > -1 && ua.indexOf("Opera") === -1
  const isIE11 = ua.indexOf("Trident") > -1 && ua.indexOf("rv:11.0") > -1
  // 跳转到兼容提示页面
  if (isIE11 || isIE) {
    window.location.href = window.location.origin + "/browser-compatibility-hint.html"
    return "LOW"
  }

  // 是否为 Chrome
  const isChrome = ua.indexOf("Chrome") > -1 && ua.indexOf("Safari") > -1 && ua.indexOf("Edge") === -1
  if (isChrome) {
    const arr = navigator.userAgent.split(" ")
    let chromeVersion
    for (let i = 0; i < arr.length; i++) {
      if (/chrome/i.test(arr[i])) chromeVersion = arr[i]
    }

    try {
      if (chromeVersion) {
        chromeVersion = Number(chromeVersion.split("/")[1].split(".")[0])
        // chrome版本判断
        if (chromeVersion < chromeVersionMap.mid) {
          window.location.href = window.location.origin + "/browser-compatibility-hint.html"
          return "LOW"
        } else if (chromeVersion < chromeVersionMap.high) {
          return "MID"
        } else {
          return "HIGH"
        }
      }
    } catch (error) {
      console.log(error)
    }
  }
}
