<template>
  <div class="image-editor-root">
    <!-- 工具栏 -->
    <div class="tool-container">
      <IconContainer
        name="返回拍摄"
        desc="返回拍摄页面"
        size="medium"
        icon="ri-camera-switch-line"
        @click="$router.back()"
      />
      <IconContainer
        name="保存"
        desc="保存编辑图像"
        size="medium"
        icon="ri-save-line"
        :disabled="!activeNode.activeBase64"
        @click="ImageEditorRef?.saveImage"
      />
      <IconContainer
        name="撤销"
        desc="撤销操作"
        size="medium"
        icon="ri-arrow-go-back-line"
        :disabled="!ImageEditorRef?.canUndo || !activeNode.activeBase64"
        @click="ImageEditorRef?.undo"
      />
      <IconContainer
        name="恢复"
        desc="恢复撤销"
        size="medium"
        icon="ri-arrow-go-forward-line"
        :disabled="!ImageEditorRef?.canRedo || !activeNode.activeBase64"
        @click="ImageEditorRef?.redo"
      />
      <IconContainer
        name="放大"
        desc="放大图片"
        size="medium"
        icon="ri-zoom-in-line"
        :disabled="!activeNode.activeBase64"
        @click="ImageEditorRef?.zoomIn"
      />
      <IconContainer
        name="缩小"
        desc="缩小图片"
        size="medium"
        icon="ri-zoom-out-line"
        :disabled="!activeNode.activeBase64"
        @click="ImageEditorRef?.zoomOut"
      />
      <IconContainer
        name="实际大小"
        desc="图片实际大小"
        size="medium"
        icon="ri-image-2-line"
        :disabled="!activeNode.activeBase64"
        @click="ImageEditorRef?.actualSize"
      />
      <IconContainer
        name="适应宽度"
        desc="图片宽度自适应"
        size="medium"
        icon="ri-aspect-ratio-line"
        :disabled="!activeNode.activeBase64"
        @click="ImageEditorRef?.fitWidth"
      />
      <IconContainer
        name="左旋转"
        desc="图片左旋转90度"
        size="medium"
        icon="ri-anticlockwise-2-fill"
        :disabled="!activeNode.activeBase64"
        @click="ImageEditorRef?.rotateLeft"
      />
      <IconContainer
        name="右旋转"
        desc="图片右旋转90度"
        size="medium"
        icon="ri-clockwise-2-fill"
        :disabled="!activeNode.activeBase64"
        @click="ImageEditorRef?.rotateRight"
      />
      <IconContainer
        name="裁剪"
        desc="图片裁剪"
        size="medium"
        icon="ri-scissors-2-line"
        :disabled="!activeNode.activeBase64"
        @click="ImageEditorRef?.toggleCrop"
      />
      <IconContainer
        name="亮度"
        desc="设置图片亮度"
        size="medium"
        icon="ri-sun-line"
        :disabled="!activeNode.activeBase64"
        @click="brightnessVisible = true"
      />
      <IconContainer
        name="对比度"
        desc="设置图片对比度"
        size="medium"
        icon="ri-contrast-line"
        :disabled="!activeNode.activeBase64"
        @click="contractVisible = true"
      />
    </div>
    <div class="editor-container">
      <div class="info-container">
        <div class="patient-info">{{ `${patientInfo?.patientName}（${patientInfo?.inpNo}）` }}</div>
        <el-tree
          class="image-tree"
          default-expand-all
          highlight-current
          node-key="fileName"
          :data="imageTree"
          :props="{
            children: 'imageFiles',
            label: 'fileName'
          }"
          :expand-on-click-node="false"
          @node-click="handleNodeClick"
        ></el-tree>
      </div>
      <div class="editor">
        <div v-if="brightnessVisible" class="brightness-container">
          <div class="brightness-container-text">{{ `亮度(${brightness})：` }}</div>
          <el-slider
            v-model="brightness"
            :min="0"
            :max="200"
            :step="1"
            style="width: 240px"
            @change="e => ImageEditorRef?.changeBrightness(brightness)"
          />
          <i class="ri-close-line" @click="brightnessVisible = false"></i>
        </div>
        <div v-if="contractVisible" class="contract-container">
          <div class="contract-container-text">{{ `对比度(${contract})：` }}</div>
          <el-slider
            v-model="contract"
            :min="0"
            :max="200"
            :step="1"
            style="width: 240px"
            @change="e => ImageEditorRef?.changeContrast(contract)"
          />
          <i class="ri-close-line" @click="contractVisible = false"></i>
        </div>
        <ImageEditor ref="ImageEditorRef" :image-base64="activeNode.activeBase64" @save-image="handleSaveImage" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, reactive } from "vue"
import localforage from "localforage"
import { cloneDeep } from "lodash-es"
import { IconContainer } from "@/base-components"
import { ImageEditor } from "@/page-components"

const ImageEditorRef = ref()
const activeNode = reactive({
  mrClassCode: "",
  imageId: "",
  activeBase64: ""
})

const imageTree = ref([])
const patientInfo = ref()

const brightnessVisible = ref(false)
const brightness = ref(100)

const contractVisible = ref(false)
const contract = ref(100)

onMounted(() => {
  let patient = localStorage.getItem("PATIENT_INFO")
  if (patient) {
    patientInfo.value = JSON.parse(patient)
    if (patientInfo.value?.patientWsid)
      localforage.getItem(patientInfo.value?.patientWsid).then((data: any) => {
        const captureClassify = data?.captureClassify ?? []
        captureClassify.forEach(item => {
          item.fileName = item.mrClassName
        })
        imageTree.value = captureClassify
      })
  }
})

function handleNodeClick(data, node) {
  if (data.fileBase64) {
    activeNode.activeBase64 = data.fileBase64
    activeNode.imageId = data.id
    activeNode.mrClassCode = node.parent?.data?.mrClassCode
    brightness.value = 100
    contract.value = 100
  }
}

function handleSaveImage(base) {
  imageTree.value.forEach((item: any) => {
    if (item.mrClassCode === activeNode.mrClassCode) {
      item.imageFiles.forEach((img: any) => {
        if (img.id === activeNode.imageId) {
          img.fileBase64 = base
        }
      })
    }
    return item
  })

  const data = cloneDeep(imageTree.value).map((item: any) => {
    delete item.fileName
    return item
  })

  localforage.setItem(
    patientInfo.value?.patientWsid,
    cloneDeep({
      patientInfo: patientInfo.value,
      captureClassify: data
    })
  )
}
</script>

<style lang="less" scoped>
.image-editor-root {
  background: #f5f7f9;
  border-radius: 4px;
  height: calc(100% - 2px);
  overflow: auto;
  border: 1px solid #cdd4da;
  .tool-container {
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    border-bottom: 1px solid #d9dfe4;
    > div:first-child {
      margin-left: 4px;
    }
  }
  .editor-container {
    height: calc(100% - 65px);
    display: flex;
    .info-container {
      display: flex;
      flex-direction: column;
      .patient-info {
        background: #409eff;
        padding: 10px;
        color: #fff;
        flex-shrink: 0;
      }
      .image-tree {
        flex: 1;
        width: 240px;
        min-height: 0px;
        overflow: auto;
      }
    }

    .editor {
      flex: 1;
      min-width: 0px;
      position: relative;

      .brightness-container {
        position: absolute;
        width: 424px;
        box-sizing: border-box;
        top: 20px;
        right: 20px;
        z-index: 2;
        background: #fff;
        padding: 10px 20px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        border-radius: 5px;
        line-height: 32px;

        .ri-close-line {
          font-size: 20px;
          color: #aaa;
          margin-left: 20px;
          cursor: pointer;
          &:hover {
            color: #ccc;
          }
        }
      }
      .contract-container {
        position: absolute;
        width: 424px;
        box-sizing: border-box;
        top: 100px;
        right: 20px;
        z-index: 2;
        background: #fff;
        padding: 10px 20px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        border-radius: 5px;
        line-height: 32px;
        .ri-close-line {
          font-size: 20px;
          color: #aaa;
          margin-left: 20px;
          cursor: pointer;
          &:hover {
            color: #ccc;
          }
        }
      }
    }
  }
}
</style>
