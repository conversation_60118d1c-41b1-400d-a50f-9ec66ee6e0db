<template>
  <PageContainer>
    <SearchForm
      :form-config="sealingHistorySearchFormConfig"
      :form-state="searchFilterForm"
      @query-btn-click="handleQuery"
      @reset-btn-click="handleReset"
    />

    <CommonTable
      ref="sealingHistoryTableRef"
      table-id="sealingHistoryTable"
      :table-columns="sealingHistoryTableColumns"
      :request-api="getSealingHistoryListApi"
      :request-params="searchParams"
    >
      <template v-if="allowPrint === 'YES'" #header>
        <el-button :disabled="!selectedRows.length" @click="handleBulkPrintSealHistory">
          <i class="ri-printer-cloud-line"></i>
          &nbsp; 打印
        </el-button>
      </template>
      <template #inHospitalDatetime="{ row }">
        <span>{{ formatDatetime(row.inHospitalDatetime) }}</span>
      </template>
      <template #outHospitalDatetime="{ row }">
        <span>{{ formatDatetime(row.outHospitalDatetime) }}</span>
      </template>
      <template #lockDatetime="{ row }">
        <span>{{ formatDatetime(row.lockDatetime) }}</span>
      </template>
      <template #unlockDatetime="{ row }">
        <span>{{ formatDatetime(row.unlockDatetime) }}</span>
      </template>
      <template #decryptionKey="{ row }">
        <span v-if="row.isShowKey && row.decryptionKey">{{ row.decryptionKey }}</span>
        <span v-else>******</span>
      </template>
      <template #operation="{ row }">
        <TableButton
          v-if="hasOperationPermission(menuId, MenuOperationEnum.View)"
          @click="$router.push({ path: '/sealing/history/detail', query: { id: row.id } })"
        >
          查看
        </TableButton>
        <TableButton
          v-if="hasOperationPermission(menuId, MenuOperationEnum.View) && allowPrint === 'YES'"
          @click="handlePrintSealHistory(row)"
        >
          打印
        </TableButton>
        <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.View)" @click="openExportDialog(row)">
          导出PDF
        </TableButton>
        <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.Cipher)" @click="showKey(row)">
          {{ row.isShowKey ? "隐藏密钥" : "查看密钥" }}
        </TableButton>
      </template>
    </CommonTable>

    <!-- 下载弹窗 -->
    <DialogContainer
      v-model:visible="exportState.dialogVisible"
      title="设置"
      :loading="exportState.loading"
      :confirm-callback="handleExportSealHistory"
      :cancel-callback="closeExportDialog"
      :close-callback="closeExportDialog"
    >
      <div class="export-content">
        <div>导出样式</div>
        <el-select v-model="exportState.exportType" style="width: 285px" placeholder="请选择下载样式">
          <el-option
            v-for="item in exportOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div>
    </DialogContainer>

    <!-- 打印密码校验弹窗 -->
    <PrintPasswordDialog ref="printPasswordDialogRef" @confirm="confirm" />
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref, computed } from "vue"
import printJS from "print-js"
import { CommonTable, TableButton, PageContainer, DialogContainer } from "@/base-components"
import { SearchForm, PrintPasswordDialog } from "@/page-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { getPrintPasswordStatus } from "@/interfaces"
import { useSystemStore, useUserStore } from "@/stores"
import { formatDatetime, Message, toastError, downloadFile } from "@/utils"
import { getSealingHistoryListApi, getSealingKetApi, exportSealHistoryApi, printSealHistoryApi } from "../interface"
import { useSealingConfig } from "../useSealingConfig"
import { sealingHistoryTableColumns, sealingHistorySearchFormConfig } from "./config"

const { hasOperationPermission } = useUserStore()
let menuId = "/sealing/history"
const systemStore = useSystemStore()

const { allowPrint } = useSealingConfig()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFilterForm = reactive({
  patientFilterProp: "patientName",
  patientFilterValue: "",
  secrecyGrade: "",
  unlockDatetime: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFilterForm)

/* ======================== 表格操作 ======================== */

const sealingHistoryTableRef = ref<InstanceType<typeof CommonTable>>()
// 表格中被选中项
const selectedRows = computed(() => sealingHistoryTableRef.value?.tableState?.selectedRows ?? [])

function showKey(row) {
  if (row.isShowKey) {
    row.isShowKey = false
  } else {
    row.isShowKey = true
    getSealingKetApi({ id: row.id }).then(res => {
      row.isShowKey = true
      row.decryptionKey = res?.data?.data ?? ""
    })
  }
  Message.success("操作成功")
}

/*===================================下载===================================*/
// 下载弹窗
const exportState = reactive({
  dialogVisible: false,
  loading: false,
  exportId: "",
  patientName: "", // 患者姓名
  version: "", //版本号
  exportType: "COMPOSE"
})

// 下载可选类型
const exportOptions = [
  {
    label: "整体一个PDF",
    value: "COMPOSE"
  },
  {
    label: "一个文书一个PDF",
    value: "ZIP"
  }
]

// 打开下载弹窗
const openExportDialog = row => {
  exportState.dialogVisible = true
  exportState.exportId = row.id
  exportState.version = row.version
  exportState.patientName = row.patientName
}

//关闭下载弹窗
const closeExportDialog = () => {
  exportState.dialogVisible = false
  exportState.exportId = ""
  exportState.patientName = ""
  exportState.version = ""
  exportState.exportType = "COMPOSE"
}

// 下载提交
const handleExportSealHistory = () => {
  const requestParams = {
    id: exportState.exportId,
    type: exportState.exportType
  }
  systemStore.showLoading("正在下载，请稍候")
  exportSealHistoryApi(requestParams)
    .then(res => {
      systemStore.hideLoading()
      Message.success("下载文件成功")
      const exportFileName = `${exportState.patientName}_${exportState.version}`
      if (exportState.exportType === "ZIP") {
        // 下载的是一个zip
        downloadFile({ fileData: res.data, fileType: "application/zip", fileName: `${exportFileName}.zip` })
      } else {
        // 下载的是一个pdf
        downloadFile({ fileData: res.data, fileType: "application/pdf", fileName: `${exportFileName}.pdf` })
      }
      closeExportDialog()
    })
    .catch(error => {
      systemStore.hideLoading()
      toastError(error, "下载文件失败")
    })
}

/*===================================打印===================================*/
interface PrintState {
  dialogVisible: boolean
  fileList: any[] // 打印文件的列表
  loading: boolean
  printId: string
}

const printState = reactive<PrintState>({
  dialogVisible: false,
  fileList: [],
  loading: false,
  printId: ""
})

const wsid = ref()
const printPasswordDialogRef = ref()
const batch = ref(false)

// 打印封存历史
const handlePrintSealHistory = async row => {
  systemStore.showLoading("请稍候")
  wsid.value = row.id
  try {
    const status = (await getPrintPasswordStatus())?.data?.data?.status
    systemStore.hideLoading()
    if (status === "ENABLE") {
      printPasswordDialogRef.value?.openDialog()
      batch.value = false
    } else {
      printHandle()
    }
  } catch (err: any) {
    toastError(err)
  }
}

const confirm = data => {
  if (data) {
    printPasswordDialogRef.value?.close()
    if (batch.value) {
      batchHandle()
    } else {
      printHandle()
    }
  } else {
    Message.error("密码错误，请重试")
  }
}

const printHandle = () => {
  systemStore.showLoading("处理中")
  printState.dialogVisible = false
  printSealHistoryApi(wsid.value)
    .then(res => {
      const data = URL.createObjectURL(new Blob([res.data], { type: "application/pdf" }))
      systemStore.hideLoading()

      printJS(data)
    })
    .catch(err => {
      systemStore.hideLoading()
    })
}

// 打印单文件
function printFile(id) {
  return new Promise((resolve, reject) => {
    systemStore.showLoading("请稍候")
    printSealHistoryApi(id)
      .then(res => {
        const data = URL.createObjectURL(new Blob([res.data], { type: "application/pdf" }))
        systemStore.hideLoading()

        printJS({
          printable: data,
          type: "pdf",
          showModal: false,
          // 关闭打印弹窗
          onPrintDialogClose: () => {
            URL.revokeObjectURL(id)
            resolve("")
          },
          onError(err) {
            reject(err)
          }
        })
      })
      .catch(err => {
        reject(err)
        systemStore.hideLoading()
      })
  })
}

// 批量打印封存历史
const handleBulkPrintSealHistory = async () => {
  if (!selectedRows.value || selectedRows.value?.length === 0) {
    Message.warning("请先选择需要打印的数据")
    return
  }

  systemStore.showLoading("请稍候")
  try {
    const status = (await getPrintPasswordStatus())?.data?.data?.status
    systemStore.hideLoading()
    if (status === "ENABLE") {
      printPasswordDialogRef.value?.openDialog()
      batch.value = true
    } else {
      batchHandle()
    }
  } catch (err: any) {
    toastError(err)
  }
}

// 批量打印
const batchHandle = async () => {
  systemStore.showLoading("处理中")
  for (let index = 0; index < selectedRows.value.length; index++) {
    await printFile(selectedRows.value[index].id)
  }
  systemStore.hideLoading()
}
</script>

<style scoped lang="less">
.export-content {
  display: flex;
  flex-wrap: nowrap;
  gap: 10px;
  align-items: center;
}
</style>
