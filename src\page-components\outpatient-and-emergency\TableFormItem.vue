<template>
  <div
    v-if="props.config.prop !== 'pageOptionProp' && props.config.type !== 'index'"
    class="table-item"
    :style="{
      backgroundColor: isMouseOver ? 'rgba(56,96,244,0.08)' : '',
      width: props.tableConfig?.tableArrangement === 'list' ? `${Math.floor(100 / props.tableConfig.column) - 1}%` : '',
      flex: props.tableConfig?.tableArrangement === 'list' ? 'auto' : 1
    }"
    @mouseover="handleMouseOver"
    @mouseleave="handleMouseLeave"
  >
    <div class="table-header">{{ props.config.name }}</div>
    <el-tooltip
      class="box-item"
      effect="dark"
      :content="props.tableData[props.index][props.config.prop]"
      placement="bottom-start"
      :disabled="!props.tableData[props.index][props.config.prop]"
    >
      <div class="table-body">
        <el-input
          v-if="props.config.type === FormItemType.BaseInput"
          v-model="props.tableData[props.index][props.config.prop]"
          :disabled="props.config.fieldEditable === 'readOnly' || actionType === 'record'"
          size="large"
          :placeholder="props.config.placeholder ? props.config.placeholder : `请输入${props.config.name}`"
        />

        <el-input
          v-if="props.config.type === FormItemType.BloodDonationBarcode"
          v-model="props.tableData[props.index][props.config.prop]"
          :disabled="props.config.fieldEditable === 'readOnly' || actionType === 'record'"
          size="large"
          :placeholder="props.config.placeholder ? props.config.placeholder : `请输入${props.config.name}`"
        />

        <!-- 数字输入框 -->

        <NumberInput
          v-if="props.config.type === FormItemType.BaseNumber"
          v-model:value="props.tableData[props.index][props.config.prop]"
          :disabled="props.config.fieldEditable === 'readOnly' || actionType === 'record'"
          size="large"
          :placeholder="props.config.placeholder ? props.config.placeholder : `请输入${props.config.name}`"
          :unit="props.config.unit"
          :precision="props.config?.allowDecimal ? props.config.precision : undefined"
        >
          <!-- <span v-if="props.config.unit && props.config.unit?.length > 0">{{ props.config.unit }}</span> -->
        </NumberInput>

        <!-- 多行文本域 -->
        <el-input
          v-if="props.config.type === FormItemType.BaseTextarea"
          v-model="props.tableData[props.index][props.config.prop]"
          :disabled="props.config.fieldEditable === 'readOnly' || actionType === 'record'"
          size="large"
          type="textarea"
          :placeholder="props.config.placeholder ? props.config.placeholder : `请输入${props.config.name}`"
        />

        <!-- 说明文本 -->

        <div v-if="props.config.type === FormItemType.DescText" class="output" v-html="props.config.content"></div>

        <!-- 日期选择 -->
        <el-date-picker
          v-else-if="props.config.type === FormItemType.BaseDate"
          v-model="props.tableData[props.index][props.config.prop]"
          :disabled="props.config.fieldEditable === 'readOnly' || actionType === 'record'"
          size="large"
          :type="getDateType(props.config.dateFormat)"
          :format="props.config.dateFormat"
          :value-format="props.config.dateFormat"
          :placeholder="props.config.placeholder ? props.config.placeholder : `请选择${props.config.name}`"
        />

        <!-- 单选框 -->
        <el-radio-group
          v-else-if="[FormItemType.BaseRadio].includes(props.config.type)"
          v-model="props.tableData[props.index][props.config.prop]"
          :disabled="props.config.fieldEditable === 'readOnly' || actionType === 'record'"
          size="large"
          :style="choiceFormItemStyle"
        >
          <el-radio v-for="option in props.config.options" :key="option.value" :label="option.value">
            {{ option.label }}
          </el-radio>
        </el-radio-group>

        <!-- 下拉选择 -->
        <div v-else-if="[FormItemType.BaseSelect].includes(props.config.type)" class="select-output">
          <el-select
            v-if="props.config.fieldLinkage"
            v-model="props.tableData[props.index][props.config.prop]"
            :disabled="props.config.fieldEditable === 'readOnly' || actionType === 'record'"
            size="large"
            fit-input-width
            filterable
            remote
            clearable
            reserve-keyword
            style="width: 100%"
            :remote-method="val => handleOptionsChange(val, props.config.prop)"
            @change="handleCodeChange($event, props.config)"
            @click="getDefaultOption(props.config.prop)"
          >
            <template #header>
              <div class="code-header">
                <span>编码</span>
                <span>名称</span>
                <span>医保灰码</span>
              </div>
            </template>
            <el-option
              v-for="option in props.config.options"
              :key="option.code"
              style="padding: 0 20px"
              :label="option.code"
              :value="option"
            >
              <div class="code-content">
                <span>{{ option.value }}</span>
                <span style="color: #aaa">{{ option.label }}</span>
                <span style="color: #aaa">{{ option?.greyCode ? "是" : "否" }}</span>
              </div>
            </el-option>
          </el-select>

          <el-select
            v-else
            v-model="props.tableData[props.index][props.config.prop]"
            :disabled="props.config.fieldEditable === 'readOnly' || actionType === 'record'"
            size="large"
            filterable
            clearable
            fit-input-width
            style="width: 100%"
          >
            <el-option
              v-for="option in props.config.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            ></el-option>
          </el-select>
        </div>

        <!-- 多选框 -->
        <el-checkbox-group
          v-else-if="props.config.type === FormItemType.BaseCheckbox"
          :disabled="props.config.fieldEditable === 'readOnly' || actionType === 'record'"
          size="large"
          :style="choiceFormItemStyle"
          :value="[]"
          :model-value="props.config.default"
        >
          <el-checkbox v-for="option in props.config.options" :key="option.value" :label="option.label"></el-checkbox>
        </el-checkbox-group>

        <!-- 地址 -->
        <div
          v-if="props.config.type === FormItemType.BaseAddress"
          :style="{
            width: '100%',
            height: '100%'
          }"
        >
          <el-cascader
            v-model="props.config.addressSelect"
            :disabled="props.config.fieldEditable === 'readOnly' || actionType === 'record'"
            size="large"
            style="width: 100%; margin-bottom: 8px"
            placeholder="请选择"
            :options="regionData"
            :props="{ value: 'label' }"
            @change="$event => changeValue($event, props.config.prop)"
          />
          <el-input
            v-model="props.tableData[props.index][props.config.prop]"
            :disabled="props.config.fieldEditable === 'readOnly' || actionType === 'record'"
            size="large"
            type="textarea"
            :placeholder="props.config.placeholder ? props.config.placeholder : `请选择${props.config.name}`"
          />
        </div>
      </div>
    </el-tooltip>
  </div>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from "vue"
import { useRoute, useRouter } from "vue-router"
import { regionData } from "element-china-area-data"
import { NumberInput } from "@/base-components"
import { FormItemType, FormItemConfig } from "@/configs"
import { getIcdCode, searchMetaCodeTableFieldApi } from "@/interfaces"
import axios from "@/interfaces/axios-instance"
import { TableColumnItem } from "@/types"
import { formatDatetime, getDateType, getOptions } from "@/utils"
const route = useRoute()

// interface PropsType {
//   config: FormItemConfig
// }
// const props = withDefaults(defineProps<PropsType>(), {})

const props = defineProps<{
  prop: string
  config: FormItemConfig
  index: number
  activeTableConfigIndex: number
  tableConfig: Record<string, any> | undefined
  activeTableConfig: FormItemConfig
  tableData: Record<string, any>
  editCard?: (prop: any) => void
  copyCard?: (config: FormItemConfig) => void
  deleteCard?: (prop: string) => void
}>()

// 单选和多选框样式
const choiceFormItemStyle = computed(() => {
  const arrangement = props.config.arrangement
  if (arrangement === "vertical") {
    return {
      flexDirection: "column",
      alignItems: "flex-start",
      display: "flex",
      overflowX: "hidden"
    }
  }
  return {
    flexDirection: "row",
    alignItems: "center",
    display: "flex",
    overflowX: "hidden"
  }
})

const actionType = computed(() => {
  return route.query.actionType as string
})

/* ==================== 鼠标悬停处理 ====================== */

const isMouseOver = ref(false)

const handleMouseOver = () => {
  isMouseOver.value = true
}

const handleMouseLeave = () => {
  isMouseOver.value = false
}
/* ==================== 表单操作 ====================== */

// 省市级联
const changeValue = (value, prop) => {
  props.tableData[prop] = value
}

const emits = defineEmits(["handleChange"])

const handleCodeChange = (val, config) => {
  const scope = {
    $index: props.index,
    row: props.tableData[props.index]
  }
  emits("handleChange", {
    val: val,
    scope: scope,
    item: config
  })
}

/**
 * 更新option
 * @param val
 * @param queryParam
 */
const handleOptionsChange = async (val, prop) => {
  if (val) {
    const config = props?.tableConfig?.tableColumn?.find(item => item.id === prop)
    props.tableConfig.tableColumn.find(item => item.id === prop).options = await getOptions(config, val)
  }
}

// 加载初始option
const getDefaultOption = async prop => {
  const config = props?.tableConfig?.tableColumn?.find(item => item.id === prop)
  if (!config.options.length && config.collectValueSource) {
    props.tableConfig.tableColumn.find(item => item.id === prop).options = await getOptions(config, "")
  }
}
</script>

<style lang="less" scoped>
.select-output {
  width: calc(100% - 16px);
  // border: 1px solid var(--el-border-color);
  color: #606266;
  font-size: var(--custom-larger-font-size);
  border-radius: 4px;
  font-size: var(--custom-small-font-size);
  padding-left: 4px;
  display: flex;
  // height: 38px;
  // line-height: 38px;
  // padding-right: 12px;
  div {
    display: inline-block;
    width: calc(100% - 20px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.table-item {
  position: relative;
  border: 1px dashed transparent;
  border-right: none;
  height: 100%;
  display: flex;
  align-items: center;
  flex: 1;
  // margin-right: 1px;
  flex-direction: column;

  // &:last-child {
  //   border-right: 1px solid #ccc;
  // }

  // &:hover {
  //   border: 1px solid #3860f4;
  // }

  .table-header {
    background: #fcfcfd;
    box-shadow: inset 0px -1px 0px 0px #f5f7fa;
    padding: 0 8px;
    width: calc(100% - 16px);
    color: rgba(10, 22, 51, 0.85) !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .table-body {
    padding: 8px;
    width: calc(100% - 16px);
  }
  .operation-buttons {
    position: absolute;
    right: 0;
    top: -10px;
    display: flex;
    .operation-icon {
      color: #aaa;
      cursor: pointer;
      margin-left: 6px;
      background: #fff;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0px 4px 8px 0px rgba(51, 51, 51, 0.15);
      border-radius: 4px;
    }
  }
}
:deep(.el-textarea__inner) {
  height: 40px;
}

:deep(.el-radio-group) {
  flex-wrap: nowrap !important;
}
</style>
