<template>
  <PageContainer separate>
    <!-- 搜索表单 -->
    <template #search>
      <SearchContainer @reset-btn-click="handleReset" @query-btn-click="handleQuery">
        <el-form-item label="名称">
          <el-select
            v-model="searchFormState.name"
            placeholder="请输入名称"
            fit-input-width
            filterable
            remote
            reserve-keyword
            :remote-method="handleOptionsChange"
          >
            <el-option
              v-for="item in nameOptions"
              :key="item.value"
              :value="item.label"
              :label="item.label"
            ></el-option>
          </el-select>
        </el-form-item>
        <CommonSelectFormItem v-model="searchFormState.status" label="状态" :options="statusOptions" />
      </SearchContainer>
    </template>

    <!-- 表格数据 -->
    <template #table>
      <CommonTable
        ref="formConfigTableRef"
        :table-columns="formConfigTableColumns"
        :request-api="getFormConfigsApi"
        :request-params="searchParams"
      >
        <template #header>
          <AddButton @click="handleAdd">新增</AddButton>
        </template>

        <template #appScenario="{ row }">
          <span>{{ appScenarioOptions.find(item => item.value === row.appScenario)?.label }}</span>
        </template>

        <template #status="{ row }">
          <el-tag :type="row.status === 'ENABLE' ? 'success' : 'danger'">
            {{ row.status === "ENABLE" ? "已启用" : "已禁用" }}
          </el-tag>
        </template>
        <template #operation="{ row }">
          <TableButton @click="handleEdit(row)">编辑</TableButton>
          <TableButton @click="handleCopy(row)">复制</TableButton>
          <TableButton @click="handleDelete(row)">删除</TableButton>
          <TableButton @click="modifyStatus(row)">
            {{ row.status === "ENABLE" ? "禁用" : "启用" }}
          </TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>

  <!-- 新增、编辑弹窗 -->
  <DialogContainer
    v-model:visible="messagePushDialogVisible"
    :title="messagePushDialogTitle"
    :width="500"
    :confirm-callback="handleConfirm"
    :confirm-loading="submitLoading"
  >
    <el-form
      ref="ruleFormRef"
      label-suffix="："
      label-width="auto"
      :rules="formConfigFormRules"
      :model="formConfigFormState"
    >
      <el-form-item label="名称" prop="name">
        <el-input v-model="formConfigFormState.name" placeholder="请输入名称" clearable />
      </el-form-item>
      <el-form-item label="应用场景" prop="appScenario">
        <el-select v-model="formConfigFormState.appScenario" placeholder="请选择应用场景" clearable>
          <el-option
            v-for="item in appScenarioOptions"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="数据规范" prop="appTypeName">
        <el-tree-select
          v-model="formConfigFormState.appTypeName"
          class="tree-select"
          placeholder="请选择"
          fit-input-width
          check-strictly
          default-expand-all
          node-key="fileName"
          filterable
          :data="standardOptions"
          @change="handleStandardChange"
        ></el-tree-select>
      </el-form-item>
    </el-form>
  </DialogContainer>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from "vue"
import { debounce } from "lodash-es"
import {
  PageContainer,
  CommonTable,
  DialogContainer,
  AddButton,
  TableButton,
  SearchContainer,
  CommonSelectFormItem
} from "@/base-components"
import { useTableSearch } from "@/hooks"
import { getCatalogStandardList, getFormConfigsDetailApi } from "@/interfaces"
import { Message, SystemPrompt, toastError } from "@/utils"
import {
  getFormConfigsApi,
  addFormConfigsApi,
  copyFormConfigsApi,
  deleteFormConfigsApi,
  modifyFormConfigsApi
} from "../interface"
import { formConfigTableColumns, appScenarioOptions, nameValidator, statusOptions } from "./config"

const formConfigTableRef = ref<InstanceType<typeof CommonTable>>()

/* ======================== 搜索 ======================== */

const searchFormState = reactive({
  name: "",
  status: ""
})
const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

const standardOptions = ref<Record<string, any>[]>([])
onMounted(async () => {
  // 初始化数据规范数据
  standardOptions.value = (
    await getCatalogStandardList({
      catalogType: "CATALOG",
      catalogFileType: "SYSTEM"
    })
  )?.data?.data?.[0]?.children.filter(item => item.parentId === 0)
  standardOptions.value.forEach(item => {
    item.children = []
  })
})

// 选择数据规范
const handleStandardChange = data => {
  formConfigFormState.appTypeCode = standardOptions.value?.find(item => item.fileName === data)?.code
}

// 名称模糊搜索
const nameOptions = ref<Record<string, any>[]>([])
const handleOptionsChange = debounce(async (query: string) => {
  nameOptions.value = (
    await getFormConfigsApi({
      offset: 0,
      limit: 1000,
      filters: `queryName=${query}`
    })
  )?.data?.data?.rows.map(item => ({
    label: item.name,
    value: item.catalogFormConfigWsid
  }))
}, 400)
/* ======================== 新增 ======================== */
const ruleFormRef = ref()
const messagePushDialogVisible = ref(false)
const messagePushDialogTitle = ref("")
const submitLoading = ref(false)

const formConfigFormState = reactive({
  name: "",
  appScenario: "",
  appTypeCode: "",
  appTypeName: "",
  catalogFormConfigWsid: ""
})

const formConfigFormRules = {
  name: [{ required: true, message: "请输入名称", trigger: "blur" }, { validator: nameValidator }],
  appScenario: [{ required: true, message: "请选取应用场景", trigger: "blur" }],
  appTypeName: [{ required: true, message: "请选择数据规范", trigger: "blur" }]
}

const handleAdd = () => {
  formConfigFormState.name = ""
  formConfigFormState.appScenario = ""
  formConfigFormState.appTypeCode = ""
  formConfigFormState.appTypeName = ""
  formConfigFormState.catalogFormConfigWsid = ""
  messagePushDialogVisible.value = true
  messagePushDialogTitle.value = "新增"
}

// 跳转编辑详情页
const handleEdit = (row: any) => {
  location.href = `/form-edit?formWsid=${row.catalogFormConfigWsid}`
}

// 复制
const handleCopy = (row: any) => {
  formConfigFormState.catalogFormConfigWsid = row.catalogFormConfigWsid
  getFormConfigsDetailApi(row.catalogFormConfigWsid).then(res => {
    formConfigFormState.name = res.data.data.name
    formConfigFormState.appScenario = res.data.data.appScenario
    formConfigFormState.appTypeCode = res.data.data.appTypeCode
    formConfigFormState.appTypeName = res.data.data.appTypeName

    messagePushDialogVisible.value = true
    messagePushDialogTitle.value = "复制"
  })
}

const handleConfirm = async () => {
  ruleFormRef.value?.validate(valid => {
    if (!valid) return
    if (submitLoading.value) return
    submitLoading.value = true
    const handler =
      messagePushDialogTitle.value === "新增"
        ? addFormConfigsApi(formConfigFormState)
        : copyFormConfigsApi(formConfigFormState)
    handler
      .then(() => {
        messagePushDialogVisible.value = false
        Message.success(`操作成功`)
        formConfigTableRef.value?.refreshTableData()
        submitLoading.value = false
      })
      .catch(err => {
        toastError(err)
        submitLoading.value = false
      })
  })
}

// 删除
const handleDelete = row => {
  SystemPrompt(`是否删除${row.name}`).then(() => {
    deleteFormConfigsApi(row.catalogFormConfigWsid)
      .then(() => {
        Message.success("删除成功")
        formConfigTableRef.value?.refreshTableData()
      })
      .catch(err => toastError(err))
  })
}

// 切换启用&禁用状态
const modifyStatus = row => {
  const nextStatus = row.status === "ENABLE" ? "DISABLE" : "ENABLE"
  SystemPrompt(`是否确认${row.status === "ENABLE" ? "禁用" : "启用"}表单"${row.name}"`).then(async () => {
    try {
      await modifyFormConfigsApi(row.catalogFormConfigWsid, nextStatus)
      Message.success("修改状态成功")
      formConfigTableRef.value?.refreshTableData()
    } catch (err: any) {
      toastError(err, "操作失败")
    }
  })
}
</script>

<style lang="less" scoped>
.param-item {
  display: flex;
  justify-content: space-between;
  width: 100%;
  gap: 15px;
  margin-bottom: 10px;
}
</style>
