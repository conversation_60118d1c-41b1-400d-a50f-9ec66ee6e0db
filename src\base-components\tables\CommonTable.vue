<template>
  <div class="common-table-container">
    <div class="common-table-header">
      <div>
        <slot name="header"></slot>
      </div>
      <div v-if="tableId" class="setting" style="align-self: flex-end">
        <el-popover
          placement="bottom"
          :width="240"
          trigger="click"
          :visible="columnConfigVisible"
          :append-to-body="false"
          popper-class="common-table-setting-popper"
        >
          <template #reference>
            <div class="table-setting-btn" @click="columnConfigVisible = !columnConfigVisible">
              <span class="ri-settings-4-line setting-icon"></span>
              <span class="table-setting-text">自定义配置</span>
            </div>
          </template>
          <template #default>
            <div class="popover-ref">
              <div class="table-setting-placeholder">请选择您需要显示的列字段</div>
              <div class="common-table-setting-check-wrapper">
                <el-checkbox :model-value="isCheckAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">
                  全选
                </el-checkbox>
                <DndList v-model:colSetting="customTableColumns" />
              </div>
              <div style="padding: 12px">
                <el-button @click="cancelTableSetting()">取消</el-button>
                <el-button type="primary" @click="saveTableSetting()">保存</el-button>
                <el-button type="primary" plain @click="resetTableSetting()">重置</el-button>
              </div>
            </div>
          </template>
        </el-popover>
      </div>
    </div>

    <el-table
      ref="tableRef"
      v-loading="state.configLoading || state.dataLoading"
      class="common-table"
      scrollbar-always-on
      :data="state.tableData"
      :border="border"
      :header-cell-class-name="headerCellClass"
      :header-cell-style="{ background: 'rgb(248,249,252)', color: '#030814' }"
      :element-loading-spinner="tableLoadingSvg"
      element-loading-svg-view-box="-10, -10, 50, 50"
      :row-key="props.rowKey"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <template v-for="item in customTableColumns" :key="item">
        <!-- 表格左侧选择栏或索引栏 -->
        <el-table-column
          v-if="item.type === 'selection' || item.type === 'index'"
          :type="item.type"
          :width="50"
          fixed="left"
          :selectable="handleSelectable"
        ></el-table-column>
        <!-- 可展开行 -->
        <el-table-column v-else-if="item.type === 'expand'" :type="item.type" :width="50" fixed="left">
          <template #default="{ row }">
            <slot name="expand" :row="row"></slot>
          </template>
        </el-table-column>
        <!-- 表格数据栏 -->
        <el-table-column
          v-if="!item.type && item.prop && item.checked"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
          :min-width="item.minWidth"
          :sortable="item.sortable"
          :show-overflow-tooltip="item.prop === 'operation' || item.showOverflowTooltip === false ? false : true"
          :resizable="true"
          :fixed="item.fixed"
          :align="item.align"
        >
          <template #header>
            <span>{{ item.label }}</span>
            <!-- 提示内容 -->
            <el-tooltip placement="top" width="auto" :content="item.tooltipContent">
              <i v-if="item.tooltipContent" class="ri-question-fill" style="font-size: 18px"></i>
            </el-tooltip>
          </template>
          <template #default="{ row }">
            <slot :name="item.prop" :row="row">
              <el-tag v-if="item.prop === 'statusText'" :type="row.statusText.includes('启用') ? 'success' : 'danger'">
                {{ row.statusText }}
              </el-tag>
              <el-tag
                v-else-if="item.prop === 'status' && [0, 1].includes(row.status)"
                :type="row.status ? 'success' : 'danger'"
              >
                {{ row.status ? "已启用" : "已禁用" }}
              </el-tag>
              <el-tag
                v-else-if="item.prop === 'status' && ['ENABLE', 'DISABLE'].includes(row.status)"
                :type="row.status === 'ENABLE' ? 'success' : 'danger'"
              >
                {{ row.status === "ENABLE" ? "已启用" : "已禁用" }}
              </el-tag>
              <span v-else>{{ row[item.prop] ?? "--" }}</span>
            </slot>
          </template>
        </el-table-column>
      </template>
      <template #empty>
        <EmptyContent />
      </template>
    </el-table>

    <!-- 表格数据分页 -->
    <div v-if="pagination" class="table-pagination">
      <el-pagination
        v-model:current-page="paginationConfig.currentPage"
        v-model:page-size="paginationConfig.pageSize"
        :page-sizes="[10, 25, 50, 100]"
        :background="true"
        layout="sizes, prev, pager, next, jumper, total"
        :total="state.total"
        :disabled="state.configLoading || state.dataLoading"
        @update:page-size="resetCurrentPage"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, computed, toRaw } from "vue"
import { cloneDeep, last } from "lodash-es"
import { tableLoadingSvg } from "@/configs"
import { getTableConfigApi, setTableConfigApi } from "@/interfaces"
import { Message, toastError } from "@/utils"
import EmptyContent from "../EmptyContent.vue"
import DndList from "./DndList.vue"
import type { TableColumnItem, TableColSettingItem } from "@/types"

interface CommonTableProps {
  tableId?: string // 用于标识表格自定义列配置,只有表格列数在10以上时才需要传
  tableColumns: Array<TableColumnItem>
  requestApi: (params: any) => any
  requestParams?: Record<string, any>
  dataCallback?: (data: Array<any>) => Array<any>
  border?: boolean
  pagination?: boolean
  multiple?: boolean // 是否多选
  selectable?: (row: any, index: number) => boolean
  rowKey?: string
}

const props = withDefaults(defineProps<CommonTableProps>(), {
  tableId: "",
  border: true,
  pagination: true,
  multiple: true,
  requestParams: () => ({}),
  dataCallback: undefined,
  selectable: undefined,
  rowKey: ""
})

const emits = defineEmits(["rowClick"])

/* ======================== 表格自定义配置 ======================== */

let initColumnsConfig = toRaw(props).tableColumns.map(item => ({
  ...item,
  id: item.prop || item.type || "",
  checked: true
}))

//如果有多选这一列同时只支持单选那么需要隐藏表头的全选框
const headerCellClass = ({ column }) => {
  return column.type === "selection" && !props.multiple ? "hidden-check" : ""
}

const customTableColumns = ref<Array<TableColSettingItem>>(initColumnsConfig)
const columnConfigVisible = ref(false)

// 是否勾选全部
const isCheckAll = computed(() => customTableColumns.value.every(item => item.checked))
// 是否部分勾选
const isIndeterminate = computed(() => customTableColumns.value.some(item => item.checked) && !isCheckAll.value)

// 是否可勾选
function handleSelectable(row, index) {
  if (props.selectable) {
    return props.selectable(row, index)
  } else return true
}

// 取消配置
function cancelTableSetting() {
  columnConfigVisible.value = false
  customTableColumns.value = cloneDeep(initColumnsConfig)
}

// 保存配置
function saveTableSetting() {
  setTableConfigApi({ columnConfig: JSON.stringify(customTableColumns.value), mark: props.tableId })
    .then(() => {
      Message.success("配置成功")
      columnConfigVisible.value = false
      initColumnsConfig = cloneDeep(customTableColumns.value)
    })
    .catch(err => toastError(err, "配置失败"))
}

// 重置表格列配置
function resetTableSetting() {
  const resetColumnsConfig = toRaw(props).tableColumns.map(item => ({
    ...item,
    id: item.prop || item.type || "",
    checked: true
  }))
  customTableColumns.value = cloneDeep(resetColumnsConfig)
  initColumnsConfig = cloneDeep(resetColumnsConfig)
  setTableConfigApi({ columnConfig: JSON.stringify(customTableColumns.value), mark: props.tableId })
    .then(() => Message.success("重置成功"))
    .catch(err => toastError(err, "重置失败"))
}

// 监听tableColumns改变，动态修改表格列
watch(
  () => props.tableColumns,
  val => {
    if (!val) return
    customTableColumns.value = val.map(item => ({
      ...item,
      id: item.prop || item.type || "",
      checked: true
    }))
  },
  { deep: true }
)

// 全选状态切换
function handleCheckAllChange() {
  if (isCheckAll.value) {
    customTableColumns.value.forEach(item => {
      if (!item.must) item.checked = false
    })
  } else {
    customTableColumns.value.forEach(item => {
      item.checked = true
    })
  }
}

// 设置选择项
function toggleRowSelection(row, isSelect) {
  if (tableRef.value) tableRef.value.toggleRowSelection(row, isSelect)
}

// 加载时获取自定义表格配置
onMounted(() => {
  if (!props.tableId) return
  state.configLoading = true
  getTableConfigApi({ mark: props.tableId })
    .then(res => {
      const remoteConfig = JSON.parse(res.data?.data?.columnConfig ?? null)
      if (remoteConfig && compareConfigData(props.tableColumns, remoteConfig)) {
        customTableColumns.value = remoteConfig
        initColumnsConfig = cloneDeep(remoteConfig)
      }
    })
    .finally(() => {
      state.configLoading = false
    })
})

// 判断本地表格列配置相比线上存储配置是否有更新
function compareConfigData(localConfig: Array<TableColumnItem>, remoteConfig: Array<TableColSettingItem>) {
  for (let i in localConfig) {
    const localItem = localConfig[i]
    const prop = localItem.prop
    if (!prop) continue
    const remoteItem = remoteConfig.find(item => item.id === prop)
    if (localItem?.label !== remoteItem?.label) return false
    if (localItem?.width !== remoteItem?.width) return false
    if (localItem?.minWidth !== remoteItem?.minWidth) return false
    if (localItem?.must !== remoteItem?.must) return false
  }
  return true
}

/* ======================== 表格数据&分页 ======================== */

const state = reactive({
  configLoading: false, // 表格配置加载状态
  dataLoading: false, // 表格数据加载状态
  tableData: [] as Array<any>, // 表格数据
  selectedRows: [] as Array<any>, // 选中的行
  total: 0 // 总数据量
})

// 表格分页配置
const paginationConfig = reactive({
  currentPage: 1,
  pageSize: 25
})
const tableRef = ref()
// 获取表格数据
function refreshTableData() {
  state.dataLoading = true
  let params = cloneDeep(props.requestParams)
  if (props.pagination) {
    props
      .requestApi({
        offset: paginationConfig.pageSize * (paginationConfig.currentPage - 1),
        limit: paginationConfig.pageSize,
        ...params
      })
      .then(res => {
        const resData = res.data?.data?.rows ?? []
        if (props.dataCallback) state.tableData = props.dataCallback(resData)
        else state.tableData = resData
        state.total = res.data?.data?.page?.totalElements ?? 0
      })
      .finally(() => {
        state.dataLoading = false
      })
  } else {
    props
      .requestApi({ ...params })
      .then(resData => {
        if (props.dataCallback) state.tableData = props.dataCallback(resData)
        else state.tableData = resData
      })
      .finally(() => {
        state.dataLoading = false
      })
  }
}

// 重置页码为首页
function resetCurrentPage() {
  paginationConfig.currentPage = 1
}

// 请求参数变化后重置页码
watch(() => props.requestParams, resetCurrentPage, { immediate: true, deep: true })

// 分页配置或请求参数变化后自动重新获取数据
watch([paginationConfig, () => props.requestParams], refreshTableData, { immediate: true, deep: true })

function handleSelectionChange(rows: Array<any>) {
  if (props.multiple || rows.length === 1) {
    state.selectedRows = rows
    return
  }

  if (rows.length > 1) {
    state.selectedRows = last(rows)
    toggleRowSelection(rows[0], false)
  }
}

// el-table row-click事件
const handleRowClick = (row, column) => {
  emits("rowClick", row)
}

defineExpose({
  refreshTableData,
  toggleRowSelection,
  tableState: state
})
</script>

<style lang="less" scoped>
.common-table-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  .common-table-header {
    display: flex;
    justify-content: space-between;

    .table-setting-btn {
      display: flex;
      align-items: center;
      color: #666;
      cursor: pointer;

      .table-setting-text {
        margin-left: 4px;
        font-size: 14px;
      }
    }
  }

  .common-table {
    flex: 1;
    margin-top: 8px;
    :deep(.hidden-check) {
      .el-checkbox {
        display: none;
      }
    }

    :deep(.el-scrollbar__view) {
      height: 100%;
    }

    :deep(.el-empty__description) {
      margin-top: 0;
    }
  }

  .table-pagination {
    margin-top: 20px;
    align-self: flex-end;
  }
}
</style>

<style lang="less">
// 表格设置popover样式
.common-table-setting-popper {
  padding: 0px !important;
  .table-setting-placeholder {
    padding: 10px;
    font-size: 14px;
    color: rgb(3 8 20 / 65.5%);
    font-style: normal;
    border-bottom: 1px solid rgb(225 226 230);
  }

  .common-table-setting-check-wrapper {
    max-height: 240px;
    overflow-y: auto;
    padding: 0px 10px;
  }
}
</style>
