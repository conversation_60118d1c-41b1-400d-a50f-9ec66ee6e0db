import axios from "@/interfaces/axios-instance"

// 获取待审批的借阅列表
export const getWaitApprovalListApi = (params: IPaginationRequestParams) => {
  return axios({
    method: "get",
    url: "/api/document/borrow/examine-wait",
    params
  })
}

// 获取已审批的借阅列表
export const getFinishedApprovalListApi = (params: IPaginationRequestParams) => {
  return axios({
    method: "get",
    url: "/api/document/borrow/examine",
    params
  })
}

/**
 * @method POST
 * @desc   借阅审批-获取借阅审批信息
 */
export function getBorrowApprovalInfoApi(obj) {
  const { paramsId } = obj
  return axios({
    method: "post",
    url: `/api/document/borrow/examine-info`,
    data: {
      paramsId
    }
  })
}

/**
 * @method POST
 * @desc   借阅审批-处理审批
 */
export function handleBorrowApprovalApi(data) {
  return axios({
    method: "post",
    url: `/api/document/borrow/examine/do`,
    data: data
  })
}

// 退回
export const sendBackApi = (taskId: string, targetNodeId: string, desc: string) => {
  return axios({
    method: "post",
    url: "/api/workflow/processTask/rollback",
    data: {
      taskId: taskId,
      tarActivityId: targetNodeId,
      desc: desc
    }
  })
}
