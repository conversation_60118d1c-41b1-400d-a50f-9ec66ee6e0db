import axios from "@/interfaces/axios-instance"

/**
 * @method GET
 * @desc   借阅类型下拉选项
 */
export function getBorrowTypeOptionsApi() {
  return axios
    .request<IResponseData>({
      method: "get",
      url: `/api/document/borrow/apply-type/list`
    })
    .then(res => res.data?.data ?? [])
}

/**
 * @method POST
 * @desc   获取病案借阅信息
 */
export const getBorrowApplyInfoApi = (recordWsidList: string[]) => {
  return axios({
    method: "post",
    url: `/api/document/borrow/apply-info`,
    data: {
      documentBagWsids: recordWsidList
    }
  })
}

// 获取病案流程信息
export const getMedicalRecordProcessApi = (inpNo: string) => {
  return axios({
    method: "get",
    url: `/api/document/bags/${inpNo}/business-flow`
  })
}
