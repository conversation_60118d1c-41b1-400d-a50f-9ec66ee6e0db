import axios from "@/interfaces/axios-instance"

/**
 * @method GET
 * @desc   终末质控任务分配
 */
export function getDistributeQcFinalList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/document/bag/qc/wait-final-qc`,
    params
  })
}

/**
 * @method GET
 * @desc   终末质控任务分配
 */
export function getThirdConfigApi() {
  return axios({
    method: "get",
    url: `/api/qc/third/config`
  })
}
