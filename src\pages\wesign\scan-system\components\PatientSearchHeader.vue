<template>
  <div class="capture-container-left-header">
    <!-- 搜索框 -->
    <SearchContainer :search-loading="searchLoading" @query-btn-click="getRecordList">
      <el-form-item label="患者信息">
        <el-input v-model.trim="searchFormState.value" style="width: 300px; margin-right: 20px" clearable>
          <template #prepend>
            <el-select v-model="searchFormState.prop" style="width: 100px; line-height: 1" @change="changeProp">
              <el-option
                v-for="option in filterPropOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              ></el-option>
            </el-select>
          </template>
        </el-input>
      </el-form-item>
    </SearchContainer>

    <!-- 用户信息展示 -->
    <el-form :inline="true">
      <el-form-item label="姓名:">
        {{ props.patientInfo?.patientName || "--" }}
      </el-form-item>
      <el-form-item label="出院科室:">
        {{ props.patientInfo?.outHospitalDeptName || "--" }}
      </el-form-item>
      <el-form-item label="入院日期:">
        {{ formatDate(props.patientInfo?.inHospitalDatetime) || "--" }}
      </el-form-item>
      <el-form-item label="出院日期:">
        {{ formatDate(props.patientInfo?.outHospitalDatetime) || "--" }}
      </el-form-item>
      <el-form-item label="病案条形码:">
        <el-input v-if="patientInfoForm" v-model="patientInfoForm.barCode" placeholder="请输入病案条形码"></el-input>
        <template v-else>--</template>
      </el-form-item>
    </el-form>

    <!-- 选择弹窗 -->
    <DialogContainer v-model:visible="selectVisible" title="选择患者" no-footer>
      <div class="patient-list">
        <div v-for="item in allPatientList" :key="item.wsid" class="patient-item" @click="e => handleSelect(item)">
          <div>{{ `患者姓名：${item.patientName}` }}</div>
          <div style="display: flex; align-items: center; flex-wrap: wrap; gap: 12px; color: #888">
            <span>{{ `住院号：${item.inpNo}` }}</span>
            <span>{{ `病案号：${item.mrNo}` }}</span>
          </div>
        </div>
      </div>
    </DialogContainer>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, computed, ref } from "vue"
import { useRoute } from "vue-router"
import { SearchContainer, DialogContainer, CommonTable } from "@/base-components"
import { getRecordListApi } from "@/interfaces"
import { formatDate, Message, SystemAlert, toastError } from "@/utils"

const route = useRoute()

interface PropsType {
  patientInfo: Record<string, any> | null
}

const props = defineProps<PropsType>()

const filterPropOptions = [
  { label: "住院号", value: "inpNo" },
  { label: "姓名", value: "patientName" },
  { label: "病案号", value: "mrNo" }
]

const searchFormState = reactive({
  prop: "inpNo",
  value: ""
})

const patientInfoForm = computed({
  get: () => props.patientInfo,
  set: val => emits("update:patientInfo", val)
})

const emits = defineEmits(["update:patientInfo"])

onMounted(() => {
  const patientInfoLocal = localStorage.getItem("PATIENT_INFO")
  const info = patientInfoLocal ? JSON.parse(patientInfoLocal) : ""
  if (patientInfoLocal) emits("update:patientInfo", JSON.parse(patientInfoLocal))
})

// 根据搜索值获取病案列表
const searchLoading = ref(false)
const getRecordList = () => {
  let filters = ""
  if (searchFormState.value) filters = `${searchFormState.prop}=${searchFormState.value}`

  // 如果没有输入住院号和病案号，则不进行搜索，如果输入了，则进行搜索
  if (!filters) return Message.warning(`请输入需要搜索的患者信息`)
  searchLoading.value = true
  getRecordListApi({ offset: 0, limit: 1000, filters })
    .then(resData => {
      const data = resData.data?.data?.rows || []
      if (data?.length > 0) {
        allPatientList.value = data
        selectVisible.value = true
      } else SystemAlert("没有查找到该患者信息，请检查输入数据！")
      searchLoading.value = false
    })
    .catch(err => {
      toastError(err)
      searchLoading.value = false
    })
}

function changeProp(value) {
  searchFormState.prop = value
  searchFormState.value = ""
}

/* ================================== 多选弹窗 ================================== */
const selectVisible = ref(false)
const allPatientList = ref<Array<any>>([])

function handleSelect(item) {
  localStorage.setItem("PATIENT_INFO", JSON.stringify(item))
  emits("update:patientInfo", item)
  selectVisible.value = false
}
</script>

<style lang="less" scoped>
.capture-container-left-header {
  padding: 0px 15px;
  background: #f5f7f9;
  border-bottom: 1px solid #d9dfe4;
  :deep(.el-form-item) {
    margin-bottom: 8px;
  }
}

.patient-list {
  height: 300px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  .patient-item {
    padding: 5px 5px;
    box-sizing: border-box;
    cursor: pointer;
    color: #333;
    &:hover {
      background: rgba(56, 127, 250, 0.1);
    }
  }
}
</style>
