import axios from "@/interfaces/axios-instance"

/**
 * @method GET
 * @desc   场景编目-页面配置-获取配置
 */
export function getCatalogPageConfigs(scenarioType: string) {
  return axios({
    method: "get",
    url: `/api/catalog/page-configs/${scenarioType}`
  })
}

/**
 * @method GET
 * @desc   编目-获取编目页面数据
 */
export function getCatalogPageData(params) {
  const { scenarioType, inpNo } = params
  return axios({
    method: "get",
    url: `/api/catalog/scenario/${scenarioType}/${inpNo}`
  })
}

interface ICatalogListData extends IPaginationRequestParams {
  scenarioType: string
  likeFieldName: string
  likeTitle: string
}

/**
 * @method GET
 * @desc   编目-分页数据
 */
export function getCatalogListData(params: ICatalogListData) {
  const { scenarioType } = params
  return axios({
    method: "get",
    url: `/api/catalog/scenario/${scenarioType}`,
    params
  })
}

/**
 * @method PUT
 * @desc   编目-完成编目
 */
export function confirmCatalogApi(data: Record<string, any>) {
  const { scenarioType, inpNo, bodyData } = data
  return axios({
    method: "put",
    url: `/api/catalog/scenario/${scenarioType}/${inpNo}/confirm`,
    data: bodyData
  })
}

/**
 * @method PUT
 * @desc   编目-临时保存
 */
export function saveCatalogTemporaryApi(data: Record<string, any>) {
  const { scenarioType, inpNo, bodyData } = data
  return axios({
    method: "put",
    url: `/api/catalog/scenario/${scenarioType}/${inpNo}/temporary`,
    data: bodyData
  })
}

/**
 * @method PUT
 * @desc   编目-取消暂存
 */
export function cancelSaveCatalogApi(data: Record<string, any>) {
  const { scenarioType, inpNo } = data
  return axios({
    method: "put",
    url: `/api/catalog/scenario/${scenarioType}/${inpNo}/cancel`
  })
}

/**
 * @method POST
 * @desc   编目-数据校验
 */
export function checkCatalogDataApi(data: Record<string, any>) {
  const { scenarioType, inpNo, bodyData } = data
  return axios({
    method: "post",
    url: `/api/catalog/scenario/${scenarioType}/${inpNo}/check-data`,
    data: bodyData
  })
}

/**
 * @method GET
 * @desc   编目-获取评分数据（编目后）
 */
export function getGradeData(params: Record<string, any>) {
  const { scenarioType, inpNo } = params
  return axios({
    method: "get",
    url: `/api/catalog/scenario/${scenarioType}/${inpNo}/grade-data`,
    params
  })
}
