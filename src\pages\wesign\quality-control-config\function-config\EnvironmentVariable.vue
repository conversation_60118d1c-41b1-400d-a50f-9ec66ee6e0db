<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <CommonInputFormItem v-model="formData.elementName" label="环境变量名" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="environmentVariableTableRef"
        :table-columns="environmentVariableColumns"
        :request-api="getElementMetadataList"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <template #header>
          <AddButton @click="handleAdd">新增</AddButton>
        </template>
        <template #operation="{ row }">
          <TableButton @click="handleEdit(row)">编辑</TableButton>
          <TableButton @click="handleDelete(row)">删除</TableButton>
        </template>
      </CommonTable>
    </template>

    <DialogForm
      v-model:visible="dialogVisible"
      :title="isEdit ? '修改环境变量' : '添加环境变量'"
      :form-config="dialogFormConfig"
      :form-state="ruleData"
      :confirm-callback="handleConfirm"
    />
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, computed } from "vue"
import {
  PageContainer,
  SearchContainer,
  CommonTable,
  AddButton,
  TableButton,
  CommonInputFormItem
} from "@/base-components"
import { TabsRouter, DialogForm } from "@/page-components"
import { useTableSearch } from "@/hooks"
import { getHomeMetaListApi } from "@/interfaces"
import { SystemPrompt, Message, toastError } from "@/utils"
import { getElementMetadataList, addElementMetadata, editElementMetadata, deleteElementMetadata } from "../interface"
import {
  environmentVariableColumns,
  getQcPointDesc,
  environmentVariableFormConfig,
  getElementTypeDesc,
  tabsRouterList
} from "./config"

/* ======================== 搜索相关数据及方法 ======================== */

const formData = reactive({
  elementName: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(formData)

/* ======================== 页面首次加载获取标准字段列表 ======================== */

// 标准字段list
const standardKeyOptions = ref()

const dialogFormConfig = computed(() =>
  environmentVariableFormConfig.map(item => {
    if (item.prop === "standardKey") {
      return { ...item, options: standardKeyOptions.value }
    } else {
      return { ...item }
    }
  })
)

onMounted(() => {
  getHomeMetaListApi({
    filters: "",
    offset: 0,
    limit: 1000
  }).then(res => {
    const homeMetaData = res.data.data?.rows ?? []
    standardKeyOptions.value = homeMetaData.map(item => ({
      label: item.collectItem,
      value: item.collectFieldName
    }))
  })
})

/* ======================== 表格相关方法 ======================== */

const environmentVariableTableRef = ref<InstanceType<typeof CommonTable>>()

function dataCallback(data: Array<Record<string, any>>) {
  return data.map(item => ({
    qcPointEnum: getQcPointDesc(item.qcPoint),
    elementTypeEnum: getElementTypeDesc(item.elementType),
    ...item
  }))
}

// 新增环境变量
function handleAdd() {
  isEdit.value = false
  for (let prop in ruleData) {
    ruleData[prop] = ""
  }
  dialogVisible.value = true
}

// 编辑环境变量
function handleEdit(row) {
  isEdit.value = true
  for (let prop in ruleData) {
    ruleData[prop] = row[prop]
  }
  dialogVisible.value = true
}

// 删除环境变量
function handleDelete(row) {
  SystemPrompt(`您确定要删除环境变量《${row.elementName} 》?`).then(() =>
    deleteElementMetadata(row.wsid)
      .then(() => {
        Message.success("删除成功")
        environmentVariableTableRef.value?.refreshTableData()
      })
      .catch(err => {
        toastError(err, "删除失败")
      })
  )
}

/* ======================== 弹窗相关数据及方法 ======================== */

const isEdit = ref(false)
const dialogVisible = ref(false)

const ruleData = reactive({
  elementName: "", //变量名称
  elementCode: "", //变量代码
  elementType: "", //变量类型
  standardKey: "", //关联的首页标准字段
  describe: "", // 描述
  wsid: "" // 文档元素全局唯一ID
})

// 保存数据
function handleConfirm() {
  const handler = isEdit.value ? editElementMetadata(ruleData) : addElementMetadata(ruleData)
  return handler
    .then(() => {
      Message.success(isEdit.value ? "编辑环境变量成功" : "添加环境变量成功")
      dialogVisible.value = false
      environmentVariableTableRef.value?.refreshTableData()
    })
    .catch(err => {
      toastError(err)
    })
}
</script>
