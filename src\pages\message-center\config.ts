// 左侧菜单栏
export enum MenuEnum {
  ANNOUNCEMENT = "ANNOUNCEMENT",
  WAIT_DISPOSE = "WAIT_DISPOSE",
  MESSAGE = "MESSAGE"
}
export const menuConfig = [
  { title: "待处理", key: MenuEnum.WAIT_DISPOSE },
  { title: "系统通知", key: MenuEnum.MESSAGE },
  { title: "系统公告", key: MenuEnum.ANNOUNCEMENT }
]

// 消息结构
export interface MessageItemType {
  title: string
  content: string
  contentHtml?: string
  dialogType: string
  createDateTimeCh: string
  createDateTime: string
  id: number
  isRead: string
}

// 已读/未读枚举
export enum ReadEnum {
  NOT_READ = "NOT_READ",
  READ = "READ"
}
