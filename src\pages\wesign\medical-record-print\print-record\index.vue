<template>
  <PageContainer separate>
    <template #search>
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <el-form-item label="患者信息">
          <el-input v-model="searchFormState.patientFilterValue">
            <template #prepend>
              <el-select v-model="searchFormState.patientFilterProp" style="width: 100px">
                <el-option label="姓名" value="patientName" />
                <el-option label="患者编号" value="patientId" />
              </el-select>
            </template>
          </el-input>
        </el-form-item>

        <DaterangeFormItem v-model:model-value="searchFormState.createdDatetime" label="打印时间" />

        <DaterangeFormItem v-model:model-value="searchFormState.outHospitalDatetime" label="出院日期" />

        <el-form-item label="操作人">
          <el-input v-model="searchFormState.operator" placeholder="请输入操作人" />
        </el-form-item>
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="recordTableRef"
        table-id="printRecordTableIdent"
        :table-columns="recordColumns"
        :request-api="getPrintRecordList"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <template #header>
          <ComplexExportButton
            :table-info="recordTableRef?.tableState"
            :export-api="exportPrintRecordList"
            filters-prefix="printRecordWsids"
            :search-filters="searchParams.filters"
          />
        </template>
        <template #operation="{ row }">
          <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.View)" @click="viewDetail(row)">
            详情
          </TableButton>
        </template>
      </CommonTable>
    </template>

    <DialogContainer v-model:visible="printForm.printVisible" :title="'打印详情'" :width="650" :no-footer="true">
      <div class="header">
        <p>具体打印内容如下：</p>
        <p>
          共计
          <span style="color: var(--el-color-primary); font-size: 16px">{{ printForm.totalPages }}</span>
          页
        </p>
      </div>
      <ul class="detail-container">
        <li v-for="(item, index) in printForm.printDetailData" :key="index">
          <span>{{ index + 1 }}. &nbsp;{{ item.title }}&nbsp;({{ item.filePage }}页)</span>
        </li>
      </ul>
    </DialogContainer>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref, computed } from "vue"
import {
  PageContainer,
  SearchContainer,
  CommonTable,
  TableButton,
  DaterangeFormItem,
  ComplexExportButton,
  DialogContainer
} from "@/base-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { useUserStore } from "@/stores"
import { formatDatetime } from "@/utils"
import { getPrintRecordList, exportPrintRecordList, queryPrintRecordDetail } from "../interface"
import { recordColumns, menuId } from "./config"

const { hasOperationPermission } = useUserStore()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  patientFilterValue: "",
  patientFilterProp: "patientName",
  operator: "",
  outHospitalDatetime: "",
  createdDatetime: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/* ======================== 表格相关方法 ======================== */

const recordTableRef = ref<InstanceType<typeof CommonTable>>()

// 表格中被选中项
const selectedRows = computed(() => recordTableRef.value?.tableState?.selectedRows ?? [])

const dataCallback = (data: Array<Record<string, any>>) => {
  return data.map(item => ({
    ...item,
    createdDatetime: formatDatetime(item.createdDatetime),
    outHospitalDatetime: formatDatetime(item.outHospitalDatetime)
  }))
}

const printForm = reactive({
  printVisible: false,
  printDetailData: [] as Array<any>,
  totalPages: 0
})

//查看详情
const viewDetail = row => {
  queryPrintRecordDetail(row.wsid)
    .then(res => {
      printForm.printVisible = true
      printForm.printDetailData = res.data.data
      printForm.printDetailData.forEach(item => {
        item.title = item.title.slice(0, item.title.length - 4)
      })
      printForm.totalPages = res.data.data.reduce((sum, pre) => {
        return sum + pre.filePage
      }, 0)
      console.log(`output->printForm.totalPages`, printForm.totalPages)
    })
    .catch(err => {})
}
</script>

<style lang="less" scoped>
.header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.detail-container {
  padding: 10px 20px;
  background-color: rgba(225, 226, 230, 0.15);
  height: 200px;
  overflow-y: auto;

  li {
    margin-bottom: 10px;
    font-size: 16px;
    color: #000;
    font-weight: bold;
  }
}
</style>
