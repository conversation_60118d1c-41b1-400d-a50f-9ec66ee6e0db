import { secrecyLevelOptions } from "@/configs"
import { phoneNumberValidator, IdNumberValidator } from "@/utils"
import type { SearchFormConfigItem, TableColumnItem } from "@/types"
import type { FormRules } from "element-plus"

export const tabsRouterList = [
  { label: "封存申请", path: "/sealing/apply/seal" },
  { label: "解封申请", path: "/sealing/apply/unlock" }
]

export const sealingTableColumns: Array<TableColumnItem> = [
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "mrNo", label: "病案号", minWidth: 100, must: true, sortable: true },
  { prop: "patientId", label: "患者编号", minWidth: 120, must: true, sortable: true },
  { prop: "visitId", label: "住院次数", minWidth: 150 },
  { prop: "patientSex", label: "性别", minWidth: 100 },
  { prop: "inHospitalDatetime", label: "入院时间", minWidth: 180, sortable: true },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 120 },
  { prop: "outHospitalDatetime", label: "出院时间", minWidth: 180, sortable: true },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 120 },
  { prop: "dischargeDiagnosisName", label: "出院主诊断", minWidth: 100 },
  { prop: "secrecyGrade", label: "保密等级", minWidth: 100 },
  { prop: "sealingStatus", label: "封存状态", minWidth: 100 },
  { prop: "operation", label: "操作", width: 75, fixed: "right" }
]

export const getSealingStatusDesc = status => {
  switch (status) {
    case "UNSEALED":
      return "未封存"
    case "SEALED":
      return "已封存"
    default:
      return "未知"
  }
}

// 判断大于今天的日期
export const disabledDate = time => {
  const OneDay = 24 * 3600 * 1000
  const today = Date.now() - (Date.now() % OneDay) - 8 * 3600 * 1000
  return time.getTime() < today
}

// 解封表单规则
export const unblockFormRules: FormRules = {
  applicant: [
    { required: true, message: "请输入申请人", trigger: "blur" },
    { min: 1, max: 50, message: "输入长度应为1到50之间", trigger: "blur" }
  ],
  applicantCertificatesNo: [
    { required: true, message: "请输入身份证号", trigger: "blur" },
    { validator: IdNumberValidator, trigger: "blur" }
  ],
  applicantContent: [
    { required: true, message: "请输入联系电话", trigger: "blur" },
    { validator: phoneNumberValidator, trigger: "blur" }
  ],
  unlockReason: [
    { required: true, message: "请输入解封原因", trigger: "blur" },
    { min: 1, max: 250, message: "输入长度应为1到250之间", trigger: "blur" }
  ],
  relationship: [{ required: true, message: "请选择与患者的关系", trigger: "blur" }]
}

// 封存表单规则
export const sealingFormRules: FormRules = {
  applicant: [
    { required: true, message: "请输入申请人", trigger: "blur" },
    { min: 1, max: 50, message: "输入长度应为1到50之间", trigger: "blur" }
  ],
  applicantCertificatesNo: [
    { required: true, message: "请输入身份证号", trigger: "blur" },
    { validator: IdNumberValidator, trigger: "blur" }
  ],
  applicantContent: [
    { required: true, message: "请输入联系电话", trigger: "blur" },
    { validator: phoneNumberValidator, trigger: "blur" }
  ],
  unlockDatetime: [{ required: true, message: "请选择封存截止时间", trigger: "blur" }],
  lockReason: [
    { required: true, message: "请输入封存原因", trigger: "blur" },
    { min: 1, max: 250, message: "输入长度应为1到250之间", trigger: "blur" }
  ],
  relationship: [{ required: true, message: "请选择与患者的关系", trigger: "blur" }],
  unlockType: [{ required: true, message: "请选择封存时间", trigger: "blur" }],
  additionalCases: [{ required: true, message: "请选择是否允许追加病历", trigger: "blur" }]
}

export const sealingApplySearchFormConfig: Array<SearchFormConfigItem> = [
  { type: "patient", label: "患者信息" },
  { type: "daterange", label: "出院时间", prop: "outHospitalDatetime" },
  { type: "select", label: "保密等级", prop: "secrecyGrade", options: secrecyLevelOptions }
]
