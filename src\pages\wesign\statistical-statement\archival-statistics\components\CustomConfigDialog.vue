<template>
  <DialogContainer
    v-model:visible="customConfigDialogVisible"
    title="自定义指标"
    :confirm-callback="handleConfirm"
    :width="468"
    :confirm-loading="confirmLoading"
  >
    <el-form>
      <el-form-item label="归档天数">
        <el-select v-model="archiveItemOperators" style="margin-right: 8px; width: 200px">
          <el-option
            v-for="item in archiveItemOperatorsOptions"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          ></el-option>
        </el-select>
        <el-input v-model.number="archivalDay" style="width: 200px; margin-right: 8px" @input="handleInput">
          <template #append>天</template>
        </el-input>
        <el-button type="primary" @click="appendCustomConfig">添加</el-button>
      </el-form-item>
    </el-form>

    <div v-loading="configDataLoading" class="custom-config-container">
      <div class="custom-config-list" v-for="item in customConfigData">
        <div class="archive-title">{{ item.archiveItemTitle }}</div>
        <div class="archive-count-title">{{ item.archiveCountTitle }}</div>
        <div class="archive-percentage-title">{{ item.archivePercentageTitle }}</div>
        <i @click="handleDeleteConfig(item)">
          <img src="@/assets/svg/statistical-statement/delete.svg" />
        </i>
      </div>
    </div>
  </DialogContainer>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue"
import { DialogContainer } from "@/base-components"
import { updateCustomArchivalConfigApi, getCustomArchivalConfigApi } from "../../interface"
import { archiveItemOperatorsOptions, ArchiveItemOperatorsEnum } from "../config"
import { Message, toastError } from "@/utils"

const customConfigDialogVisible = ref(false)

const archiveItemOperators = ref("")

const archivalDay = ref()

const confirmLoading = ref(false)

const configDataLoading = ref(false)

// 自定义配置数据
const customConfigData = ref<Array<Record<string, any>>>([])


function handleInput(val) {
  const reg = /^[1-9]\d*$/
  if (!reg.test(val)) {
    archivalDay.value = ""
  } else {
    archivalDay.value = Number(val)
  }
}

// 添加自定义配置
function appendCustomConfig() {
  if (!archiveItemOperators.value && !archivalDay.value) return
  let archiveItemTitle = ""
  let archiveCountTitle = ""
  let archivePercentageTitle = ""
  if (archiveItemOperators.value === ArchiveItemOperatorsEnum.GT) {
    archiveItemTitle = `超出${archivalDay.value}日归档指标`
    archiveCountTitle = `超出${archivalDay.value}日归档数`
    archivePercentageTitle = `超出${archivalDay.value}日归档率`
  } else {
    archiveItemTitle = `${archivalDay.value}日归档指标`
    archiveCountTitle = `${archivalDay.value}日归档数`
    archivePercentageTitle = `${archivalDay.value}日归档率`
  }
  const configObj = {
    archiveItemDays: archivalDay.value,
    archiveItemOperators: archiveItemOperators.value,
    archiveItemTitle: archiveItemTitle,
    archiveCountTitle: archiveCountTitle,
    archivePercentageTitle: archivePercentageTitle
  }
  customConfigData.value.push(configObj)
}

// 删除自定义配置
function handleDeleteConfig(removeItem) {
  const removeItemIndex = customConfigData.value.findIndex(item => {
    return item.archiveItemDays === removeItem.archiveItemDays
  })
  console.log(removeItemIndex)
  customConfigData.value.splice(removeItemIndex, 1)
}

// 确认操作
function handleConfirm() {
  const data = [] as any
  customConfigData.value.map(item => {
    const obj = {
      archiveItemDays: item.archiveItemDays,
      archiveItemOperators: item.archiveItemOperators
    }
    data.push(obj)
  })
  confirmLoading.value = true
  updateCustomArchivalConfigApi(data)
    .then(() => {
      Message.success("添加自定义指标成功")
      customConfigDialogVisible.value = false
      confirmLoading.value = false
    })
    .catch(err => {
      toastError(err, "添加自定义指标失败")
      getCustomArchivalConfig()
      confirmLoading.value = false
    })
}

// 获取自定义配置
function getCustomArchivalConfig() {
  configDataLoading.value = true
  getCustomArchivalConfigApi().then(res => {
    customConfigData.value = res.data.data
  }).catch(err => {
    toastError(err)
  }).finally(() => {
    configDataLoading.value = false
  })
}

// 重置数据
function resetData() {
  customConfigDialogVisible.value = true
  archiveItemOperators.value = ""
  archivalDay.value = ""
}

defineExpose({
  showCustomConfigDialog: () => {
    resetData()
    getCustomArchivalConfig()
  }
})
</script>

<style lang="less" scoped>
:deep(.el-form) {
  .el-form-item__content {
    flex-wrap: nowrap;
  }
}

.custom-config-container {
  font-size: 14px;
  font-weight: 500;
  margin-top: 27px;
  max-height: 450px;
  overflow: auto;
  .custom-config-list {
    padding: 6px 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .archive-title {
      color: rgba(10, 22, 51, 0.6);
      margin-right: 40px;
    }
    .archive-count-title,
    .archive-percentage-title {
      color: #0a1633;
    }
    img {
      vertical-align: bottom;
      cursor: pointer;
    }
    &:hover {
      background: #f0f4fb;
    }
  }
}
</style>
