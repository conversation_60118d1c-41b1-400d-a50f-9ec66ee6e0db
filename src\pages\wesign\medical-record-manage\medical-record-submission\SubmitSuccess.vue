<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />

      <SearchForm
        :form-config="submissionSearchFormConfig"
        :form-state="searchFormState"
        @query-btn-click="handleQuery"
        @reset-btn-click="handleReset"
      />
    </template>

    <template #table>
      <CommonTable
        ref="commonTableRef"
        table-id="medicalRecordSubmissionTable"
        :table-columns="submissionColumns"
        :request-api="getSubmissionListApi"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <template #submitStatusEnum="{ row }">
          <el-tag :type="row.statusTagType">{{ getStatusLabel(row.submitStatusEnum) }}</el-tag>
        </template>
        <template #operation="{ row }">
          <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.Submit)" @click="handleCheck(row)">
            查看
          </TableButton>
        </template>
      </CommonTable>
    </template>

    <SealDialog ref="sealDialogRef" :selected-row="medicalRecord" :confirm-callback="toDetail"></SealDialog>
  </PageContainer>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue"
import { useRouter } from "vue-router"
import { CommonTable, TableButton, PageContainer } from "@/base-components"
import { SealDialog, SearchForm, TabsRouter } from "@/page-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { useUserStore } from "@/stores"
import { encryptStr, formatDatetime } from "@/utils"
import { submissionSearchFormConfig, submissionColumns, SUBMISSION_STATUS_MAP, tabsRouterList } from "./config"
import { getSubmissionListApi } from "./interface"

const router = useRouter()

const menuId = "/medical-record/submission"

const { hasOperationPermission } = useUserStore()

/* ======================== 搜索 ======================== */

const searchFormState = reactive({
  patientFilterProp: "patientName",
  patientFilterValue: "",
  outHospitalDatetime: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState, "submitStatus=SUBMIT_SUCCESS", {
  patientFilterProp: "patientName"
})

/* ======================== 表格操作 ======================== */

const commonTableRef = ref<InstanceType<typeof CommonTable>>()

function dataCallback(data: Array<Record<string, any>>) {
  return data.map(item => ({
    ...item,
    inHospitalDatetime: formatDatetime(item.inHospitalDatetime),
    outHospitalDatetime: formatDatetime(item.outHospitalDatetime),
    submitterTime: formatDatetime(item.submitterTime),
    statusTagType: SUBMISSION_STATUS_MAP.get(item.submitStatusEnum)?.tag
  }))
}

// 获取状态文案
const getStatusLabel = statusEnum => {
  return SUBMISSION_STATUS_MAP.get(statusEnum)?.label
}

/* ========================  ======================== */

const medicalRecord = ref<Record<string, any> | null>(null) // 用户点击提交的病案信息

const sealDialogRef = ref<InstanceType<typeof SealDialog>>() // SealDialog组件ref

const handleCheck = row => {
  medicalRecord.value = row
  sealDialogRef.value?.checkSealed()
}

const toDetail = () => {
  const query = { inpNo: medicalRecord.value?.inpNo }
  if (sealDialogRef.value?.secretKey) {
    query["secretKey"] = encryptStr(sealDialogRef.value.secretKey)
  }
  router.push({
    path: "/medical-record/detail",
    query: query
  })
}
</script>
