<template>
  <PageContainer separate>
    <template #search>
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
          :filter-prop-options="filterPropOptions"
        />

        <DaterangeFormItem v-model:model-value="searchFormState.createdDatetime" label="申请时间" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="writeoffTableRef"
        table-id="writeoffTableIdent"
        :table-columns="writeoffColumns"
        :request-api="getOwnTakeList"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <template #operation="{ row }">
          <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.Take)" @click="receiveItem(row)">
            领取
          </TableButton>
        </template>
      </CommonTable>
    </template>
    <DialogContainer
      v-model:visible="visible"
      :title="'自取核销'"
      :width="450"
      :cancel-callback="resetWriteoffDialog"
      :confirm-callback="writeoffDialogConfirm"
    >
      <el-form-item label="订单取货码：">
        <el-input v-model="dialogData.extractCode" class="input-with-select" />
      </el-form-item>
    </DialogContainer>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import {
  SearchContainer,
  CommonTable,
  DialogContainer,
  TableButton,
  DaterangeFormItem,
  PatientFilterFormItem,
  PageContainer
} from "@/base-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { useUserStore } from "@/stores"
import { Message, formatDatetime, toastError } from "@/utils"

import { getOwnTakeList, ownTakeData } from "../interface"
import { filterPropOptions } from "../print-config"
import { writeoffColumns, menuId } from "./config"

const { hasOperationPermission } = useUserStore()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  patientFilterValue: "",
  patientFilterProp: "patientName",
  createdDatetime: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/* ======================== 表格相关方法 ======================== */

const dataCallback = (data: Array<Record<string, any>>) => {
  return data.map(item => ({
    ...item,
    createdDatetime: formatDatetime(item.createdDatetime)
  }))
}

const visible = ref(false)
const dialogData = reactive({
  extractCode: "",
  wsid: ""
})

// 接收
const receiveItem = row => {
  visible.value = true
  dialogData.wsid = row.wsid
}

const resetWriteoffDialog = () => {
  dialogData.extractCode = ""
  visible.value = false
}

const writeoffTableRef = ref()

// 确认
const writeoffDialogConfirm = () => {
  if (!dialogData.extractCode) return Message.warning("请输入订单取货码")
  ownTakeData({
    extractCode: dialogData.extractCode,
    wsid: dialogData.wsid
  })
    .then(() => {
      visible.value = false
      writeoffTableRef.value?.refreshTableData()
      Message.success("操作成功")
    })
    .catch(err => toastError(err))
}
</script>

<style lang="less" scoped>
:deep(.el-dialog__body) {
  .el-form-item {
    width: 100%;
  }
}
</style>
