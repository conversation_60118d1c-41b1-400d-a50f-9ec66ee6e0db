<template>
  <div class="pdf-preview-wrapper">
    <div class="pdf-preview-main" :style="{ background: $props.background }">
      <!-- 工具栏 -->
      <div v-if="showControl && !switchPdf" class="pdf-preview-header" :style="$props.headerStyle">
        <div class="left-header">
          <!-- 左侧header区域 -->
          <div class="change-box">
            <slot name="header"></slot>
          </div>

          <!-- 缩放操作 -->
          <div class="change-box">
            <el-tooltip content="缩小" placement="top">
              <i class="ri-zoom-out-line action" @click="scalePdf('decrease')"></i>
            </el-tooltip>

            <el-select v-model="pdfState.scale" placeholder="请选择" size="small" style="width: 100px">
              <el-option
                v-for="scale in scaleOptions"
                :key="scale.value"
                :label="scale.label"
                :value="scale.value"
              ></el-option>
            </el-select>
            <el-tooltip content="放大" placement="top">
              <i class="ri-zoom-in-line action" @click="scalePdf('increase')"></i>
            </el-tooltip>
          </div>

          <!-- 翻页操作 -->
          <div class="change-box">
            <div class="page-input-container">
              <el-tooltip content="上一页" placement="top">
                <i class="ri-arrow-left-s-line action" @click="turnPage('pre')"></i>
              </el-tooltip>
              <div class="page-input">
                <el-input-number
                  v-model="pdfState.inputPage"
                  class="input-container"
                  size="small"
                  :controls="false"
                  step-strictly
                  @keyup.enter="handleInputChange"
                ></el-input-number>
                <div class="page-hint number-slice">/</div>
                <div class="page-hint">{{ pdfState.total }}</div>
              </div>
              <el-tooltip content="下一页" placement="top">
                <i class="ri-arrow-right-s-line action" @click="turnPage('next')"></i>
              </el-tooltip>
            </div>
          </div>

          <!-- 旋转操作 -->
          <div class="change-box">
            <el-tooltip content="逆时针旋转" placement="top">
              <i class="ri-anticlockwise-2-line action" @click="rotatePdf()"></i>
            </el-tooltip>
          </div>

          <!-- 右边header区域 -->
          <div class="change-box right-header">
            <slot name="right-header"></slot>
          </div>
        </div>

        <!-- 兼容性提示 -->
        <div v-if="!switchPdf" class="tips-view">
          <!-- <i class="ri-error-warning-line"></i> -->
          <img :src="PDFViewSvg" />
          <div style="width: 70px">无法查看？</div>
          <el-button color="#fb7d0f" size="small" style="color: #fff" @click="switchPdfVersion">请点击此处</el-button>
        </div>
      </div>

      <!-- 兼容性返回 -->
      <div v-if="switchPdf" class="tips-return" @click="switchPdfVersion">
        <el-icon><ArrowLeft /></el-icon>
        <el-button v-if="switchPdf" color="#fb7d0f" size="small" style="color: #fff">返回</el-button>
      </div>

      <!-- pdf渲染区域 -->
      <div
        v-show="!switchPdf"
        ref="pdfAreaRef"
        v-loading="fullLoading"
        class="pdf-preview-content"
        :style="{
          background: $props.background,
          width: pdfState.showThumbnail ? 'calc(100% - 160px)' : '100%',
          height: $props.medicalLocation ? 'calc(100% - 150px)' : 'calc(100% - 100px)%'
        }"
        @scroll="handleScroll"
      >
        <div class="pdf-preview-content-area">
          <div
            ref="pdfRef"
            :style="{
              height: `${pdfAreaHeight}px`,
              width: `${maxPreviewWidth}px`
            }"
          >
            <div v-for="(pageNum, index) in pdfState.total" :key="index">
              <VuePdfEmbed
                v-if="pdfState.pdfRenderPages.includes(pageNum)"
                v-loading="pdfsLoading[pageNum]"
                :width="previewWidth"
                :scale="pdfState.scale"
                :source="pdfState.source"
                :rotation="pdfState.rotation"
                :style="{
                  width: Math.floor(rawsSize[pageNum]?.width * Number(ratio)) + 'px',
                  height: `${Math.floor(rawsSize[pageNum]?.height * Number(ratio)) + PDF_PAGE_PAGE_HEIGHT + 2}px`
                }"
                :page="pageNum"
                @rendered="(pdf, rawsSize) => extractPdfInfo(pdf, rawsSize, pageNum)"
              >
                <template #after-page="{ page }">
                  <div class="pdf-page-num">第&nbsp;{{ page }}/{{ pdfState.total }}&nbsp;页</div>
                </template>
              </VuePdfEmbed>
              <div
                v-else-if="!pdfState.pdfRenderPages.includes(pageNum)"
                :style="{
                  width: Math.floor(rawsSize[pageNum]?.width * Number(ratio)) + 'px',
                  backgroundColor: '#fff',
                  border: '1px solid #ccc',
                  margin: 'auto',
                  marginBottom: `${PDF_PAGE_PAGE_HEIGHT}px`,
                  height: `${Math.floor(rawsSize[pageNum]?.height * Number(ratio))}px`
                }"
              ></div>
            </div>
          </div>

          <div class="sign-layer">
            <slot name="sign"></slot>
          </div>

          <div v-if="props.showWatermark" class="watermark-layer">
            <div
              ref="watermarkRef"
              :style="{ margin: 'auto', width: previewWidth + 'px', height: previewHeight + 'px' }"
            ></div>
          </div>
        </div>
      </div>

      <!-- PdfObject -->
      <PdfObjectPreview
        v-if="props.src && switchPdf"
        ref="pdfRef"
        :src="props.src"
        :medical-location="props.medicalLocation"
        :session-less="props.sessionLess"
      />

      <!-- 病案位置 -->
      <div v-if="props.medicalLocation && !switchPdf" class="pdf-preview-location">
        病案位置：{{ props.medicalLocation }}
      </div>
    </div>

    <!-- 右侧pdf编辑操作区域 -->
    <div>
      <slot name="right-aside"></slot>
    </div>

    <!-- 缩略图预览区域 -->
    <div
      v-if="!$props.disableThumbnail"
      class="pdf-preview-thumbnail-wrapper"
      :style="{
        width: pdfState.showThumbnail ? '160px' : '0px'
      }"
    >
      <!-- 缩略图控制按钮 -->
      <div
        class="switch"
        :class="getDynamicSwitchClass"
        @click="changeThumbnailBoxStatus"
        @mouseenter="changeSwitchIcon"
        @mouseleave="changeSwitchIcon"
      >
        <i class="ri-arrow-right-s-line"></i>
      </div>

      <!-- 缩略图显示 -->
      <div v-if="pdfState.showThumbnail" class="pdf-preview-thumbnail">
        <div class="pdf-preview-thumbnail-title">缩略图</div>
        <div
          ref="pdfThumbnailRef"
          v-loading="fullLoading || !isRenderSuccess"
          class="pdf-preview-thumbnail-content"
          @scroll="handleThumbnailScroll"
        >
          <div v-for="pageNum in pdfState.total" :key="pageNum" @click="handleJump(pageNum, 'thumbnail')">
            <VuePdfEmbed
              v-if="pdfState.thumbnailRenderPages.includes(pageNum)"
              key="pdf-thumbnail"
              v-loading="thumbnailsLoading[pageNum]"
              :class="{ active: pageNum === pdfState.inputPage }"
              class="pdf-preview-thumbnail-item"
              :width="120"
              :style="{
                height: `${
                  Math.floor((rawsSize[pageNum]?.height * 120) / rawsSize[pageNum]?.width) + THUMBNAIL_PAGE_HEIGHT + 2
                }px`,
                width: '122px'
              }"
              :source="pdfState.source"
              :page="pageNum"
              :is-thumbnail="true"
              @rendered="() => (thumbnailsLoading[pageNum] = false)"
            >
              <template #after-page="{ page }">
                <div
                  class="pdf-preview-thumbnail-page"
                  :style="{
                    height: `${THUMBNAIL_PAGE_HEIGHT}px`
                  }"
                >
                  {{ `${page}/${pdfState.total}` }}
                </div>
              </template>
            </VuePdfEmbed>
            <div
              v-else
              v-loading="true"
              :style="{
                backgroundColor: '#fff',
                border: '1px solid #ccc',
                marginBottom: `${THUMBNAIL_PAGE_HEIGHT}px`,
                height: `${Math.floor((rawsSize[pageNum]?.height * 120) / rawsSize[pageNum]?.width) + 2}px`,
                width: '122px'
              }"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, nextTick, ref, watch, computed } from "vue"
import { useRoute } from "vue-router"
import { debounce, forEach, last } from "lodash"
import { flow, map, values, minBy, prop } from "lodash/fp"
import { toNumber } from "lodash-es"
import { PdfObjectPreview } from "@/base-components"
import VuePdfEmbed from "./pdf-embed.vue"
import PDFViewSvg from "@/assets/svg/other/pdf-view.svg"
import { getWatermarkApi } from "@/interfaces"
import axios from "@/interfaces/axios-instance"
import { toastError, Message, PDF_PAGE_PAGE_HEIGHT, sessionLessAxios } from "@/utils"

const route = useRoute()

const THUMBNAIL_PAGE_HEIGHT = 44 // 缩略图每页显示页码的高度

const RENDER_PAGES = 6 //第一次渲染的页数
const props = defineProps({
  src: { type: String, required: true },
  defaultWidth: { type: Number, default: 800 },
  showControl: { type: Boolean, default: true }, // 是否显示控制栏
  background: { type: String, default: "#f1f1f1" }, // 预览区域背景
  headerStyle: { type: Object, default: () => ({}) }, // pdf顶部控制栏样式
  disableThumbnail: { type: Boolean, default: true }, // 是否禁用缩略图
  bodyData: { type: Object, default: () => ({}) },
  method: { type: String, default: "get" },
  showWatermark: { type: Boolean, default: true, require: false },
  medicalLocation: { type: String, default: "", require: false }, // 病案位置
  sessionLess: { type: Boolean, default: false, require: false }, //是否需要sessionWsid
  enableCache: { type: Boolean, default: true } // 是否启用缓存
})

const scaleOptions = [
  { label: "75%", value: 0.75 },
  { label: "100%", value: 1.0 },
  { label: "125%", value: 1.25 },
  { label: "150%", value: 1.5 }
]

const isRenderSuccess = ref(false) // 用于标识是否已执行extractPdfInfo

const fullLoading = ref(false) //全局loading

const pdfRef = ref() // pdf容器

const pdfThumbnailRef = ref() // 缩略图容器

//缩略图每页的loading
const thumbnailsLoading = ref({})

// pdf预览区域每页的loading
const pdfsLoading = ref({})

// 监听页码变化，让pdf滚动到指定页
const pdfAreaRef = ref()

const pdfState = reactive({
  source: "",
  rotation: 0, // 旋转角度
  scale: 1.0, // 预览缩放比例
  inputPage: 1, // 页码输入框值
  current: 1, // 当前所在页
  total: 1, // 总页数
  thumbnailRenderPages: [] as Array<number>, // 缩略图渲染的页数
  pdfRenderPages: [] as Array<number>, // pdf渲染的页数
  showThumbnail: false // 是否显示缩略图
})

//计算pdf容器整体高度
const pdfAreaHeight = computed(() => {
  //根据每一页的高度和缩放比例计算出整体高度
  let height = 0
  if (!rawsSize.value) return height

  Object.values(rawsSize.value)?.forEach((item: any) => {
    height += Math.floor(Number(item.height * Number(ratio.value)) + PDF_PAGE_PAGE_HEIGHT) + 2 //2是因为每个pdf有上下两个边框线
  })
  return height
})

// pdf原始尺寸,
const rawSize = reactive({
  width: 800,
  height: 1132
})

// 存储每一页pdf的原始尺寸
const rawsSize = ref({})

// 真实预览宽度
const previewWidth = computed(() => Math.floor(pdfState.scale * props.defaultWidth))

const previewHeight = computed(() => {
  if (ratio.value <= 0) return 0
  // 分页模式
  // else if (pdfState.enablePaging) return Math.ceil(rawSize.height * ratio.value * 1)
  else return Math.ceil(rawSize.height * ratio.value * pdfState.total)
})

// pdf预览宽度与真实宽度比
const ratio = computed(() => (rawSize.width ? (previewWidth.value / rawSize.width).toFixed(3) : -1) as number)

// pdf最大一页的预览宽度
const maxPreviewWidth = computed(() => {
  const maxRaw = Math.max(...Object.values(rawsSize.value).map((item: any) => item.width))
  return Math.floor(maxRaw * Number(ratio.value))
})

// 文件地址变化时发起新请求
watch(
  () => props.src,
  () => {
    if (!props.src) return
    fullLoading.value = true
    isRenderSuccess.value = false
    pdfState.total = 1
    pdfState.pdfRenderPages = []
    pdfState.thumbnailRenderPages = []
    thumbnailsLoading.value = {}
    pdfsLoading.value = {}
    rawsSize.value = {}
    pdfState.source = ""
    if (pdfAreaRef.value) {
      pdfAreaRef.value.scrollTop = 0
      pdfAreaRef.value.scrollLeft = 0
    }

    if (pdfThumbnailRef.value) pdfThumbnailRef.value.scrollTop = 0
    const requestUrl = props.enableCache ? props.src : props.src + `?t=${new Date().getTime()}`
    if (props.src.includes("undefined")) {
      fullLoading.value = false
      return
    }
    // axios({
    //   method: props.method,
    //   url: props.src,
    //   responseType: "blob",
    //   // 只在发起合同填完预览时传data
    //   data: {
    //     templateWsid: props.bodyData.templateWsid,
    //     replaceData: props.bodyData.tags
    //   }
    // })
    const handle = props.sessionLess
      ? sessionLessAxios({
          method: "get",
          url: requestUrl,
          responseType: "blob",
          jobId: route.query.jobId as string,
          inpNo: route.query.inpNo as string
        })
      : axios({
          method: "get",
          url: requestUrl,
          responseType: "blob"
        })
    handle
      .then(res => {
        pdfState.current = 1
        pdfState.inputPage = 1
        pdfState.rotation = 0
        pdfState.scale = 1.0
        pdfState.source = URL.createObjectURL(res.data)
        pdfState.pdfRenderPages = [1]
        pdfState.thumbnailRenderPages = [1]

        fullLoading.value = false
        rawsSize.value = {}
      })
      .catch(error => {
        toastError(error, "文件获取失败")
        pdfState.source = "error"
      })
  },
  {
    immediate: true
  }
)

// 缩放操作
function scalePdf(type: "increase" | "decrease") {
  const currentScaleIndex = scaleOptions.findIndex(item => item.value === pdfState.scale)
  const nextScaleIndex = type === "increase" ? currentScaleIndex + 1 : currentScaleIndex - 1
  if (scaleOptions[nextScaleIndex]) pdfState.scale = scaleOptions[nextScaleIndex].value
}

// 翻页操作
function turnPage(type: "pre" | "next") {
  const nextPage = type === "pre" ? pdfState.current - 1 : pdfState.current + 1
  if (nextPage > 0 && nextPage < pdfState.total + 1) {
    pdfState.inputPage = nextPage
    pdfState.current = nextPage
    inputJump.value = true
    clickJumpType.value = ""
  }
}

// 旋转操作
const rotatePdf = () => {
  pdfState.rotation -= 90
}

const inputJump = ref(false) // 输入页码跳转标识

const clickJumpType = ref<string | undefined>(undefined) // 点击跳转类型

// 跳转到指定页
function handleInputChange() {
  if (pdfState.inputPage < 1 || pdfState.inputPage > pdfState.total) {
    pdfState.inputPage = pdfState.current
    return Message.warning("请输入有效的页码")
  }
  pdfState.current = pdfState.inputPage
  inputJump.value = true
  clickJumpType.value = ""
}

// 点击缩略图跳转到指定页
function handleJump(pageNum: number, clickType?: string) {
  if (pageNum) {
    pdfState.current = pageNum
    pdfState.inputPage = pageNum
  }
  inputJump.value = true
  clickJumpType.value = clickType
}

watch(
  () => pdfState.current,
  () => {
    if (!inputJump.value) return
    if (pdfState.current > 0 && pdfState.current <= pdfState.total) {
      //将这页前面的页数，当前页，后面的页数都渲染出来
      pdfState.pdfRenderPages = [1]
      pdfState.thumbnailRenderPages = []
      pdfsLoading.value = {}
      thumbnailsLoading.value = {}
      for (let i = pdfState.current - 2; i < pdfState.current + 4; i++) {
        if (i > 0 && i <= pdfState.total && !pdfState.pdfRenderPages.find(item => item === i)) {
          pdfState.pdfRenderPages.push(i)
          if (pdfsLoading.value[i]?.length === 0) {
            pdfsLoading.value[i] = true
          }
        }
      }
      // 如果不是点击缩略图跳转，将缩略图渲染出来
      // if (clickJumpType.value !== "thumbnail") {
      //将当前页和后面的4页缩略图渲染出来
      for (let i = pdfState.current - 3; i < pdfState.current + 6; i++) {
        if (i > 0 && i <= pdfState.total) {
          pdfState.thumbnailRenderPages.push(i)
          if (thumbnailsLoading.value[i]?.length === 0) thumbnailsLoading.value[i] = true
        }
      }
      // }
      let pdfTop = 0
      let pdfThumbnailTop = 0
      for (let i = 1; i < pdfState.current; i++) {
        pdfThumbnailTop +=
          Math.floor(Number((rawsSize.value?.[i]?.height * 120) / rawsSize.value?.[i]?.width)) +
          THUMBNAIL_PAGE_HEIGHT +
          2
        pdfTop += Math.floor(Number(rawsSize.value?.[i]?.height * Number(ratio.value)) + PDF_PAGE_PAGE_HEIGHT) + 2 //2是因为每个pdf有上下两个边框线
      }
      pdfAreaRef.value.scrollTop = pdfTop
      if (pdfThumbnailRef.value && clickJumpType.value !== "thumbnail") {
        pdfThumbnailRef.value.scrollTop = pdfThumbnailTop
      }
    }
  }
)

watch(
  () => pdfState.scale,
  () => {
    if (pdfState.current > 0 && pdfState.current <= pdfState.total) {
      let pdfTop = 0
      for (let i = 1; i < pdfState.current; i++) {
        pdfTop += Math.floor(Number(rawsSize.value?.[i]?.height * Number(ratio.value)) + PDF_PAGE_PAGE_HEIGHT) + 2 //2是因为每个pdf有上下两个边框线
      }
      pdfAreaRef.value.scrollTop = pdfTop
    }
  }
)

// pdf滚动事件
const handleScroll = debounce(e => {
  //说明是跳转到指定页，此时不需要再次触发滚动事件
  if (inputJump.value) {
    inputJump.value = false
    return
  }
  const pdfPreviewContent = pdfAreaRef.value
  if (pdfPreviewContent) {
    let page = 1
    let pdfHeightTotal = 0
    let top = pdfPreviewContent.scrollTop

    for (let i = 1; i <= Object.values(rawsSize.value).length; i++) {
      pdfHeightTotal += Math.floor(rawsSize.value[i]?.height * Number(ratio.value) + PDF_PAGE_PAGE_HEIGHT) + 2
      // 找到当前页
      if (top < pdfHeightTotal) {
        page = i
        break
      }
    }
    pdfState.inputPage = Math.max(page, 1)
    pdfState.current = Math.max(page, 1)
    for (let i = pdfState.current - 2; i < pdfState.current + 4; i++) {
      if (i > 0 && i <= pdfState.total && !pdfState.pdfRenderPages.includes(i)) {
        pdfState.pdfRenderPages.push(i)
        if (pdfsLoading.value[i]?.length === 0) pdfsLoading.value[i] = true
      }
    }

    pdfState.thumbnailRenderPages = []
    thumbnailsLoading.value = {}
    //将当前页和后面的4页缩略图渲染出来
    for (let i = pdfState.current - 2; i < pdfState.current + 6; i++) {
      if (i > 0 && i <= pdfState.total && !pdfState.thumbnailRenderPages.includes(i)) {
        pdfState.thumbnailRenderPages.push(i)
        if (thumbnailsLoading.value[i]?.length === 0) thumbnailsLoading.value[i] = true
      }
    }
  }
  let thumbnailPdfTop = 0
  for (let i = 1; i < pdfState.current; i++) {
    thumbnailPdfTop +=
      Math.floor(Number((rawsSize.value?.[i]?.height * 120) / rawsSize.value?.[i]?.width)) + THUMBNAIL_PAGE_HEIGHT + 2
  }
  nextTick(() => {
    if (pdfThumbnailRef.value) pdfThumbnailRef.value.scrollTop = thumbnailPdfTop
  })
}, 200)

// pdf滚动
const scrollPdf = top => {
  nextTick(() => {
    if (top > 0) {
      pdfAreaRef.value.scrollTop = top
    }
  })
}

// 缩略图滚动事件
const handleThumbnailScroll = debounce(e => {
  const pdfPreviewContent = pdfThumbnailRef.value
  if (pdfPreviewContent) {
    let page = 1
    let pdfHeightTotal = 0
    let top = pdfPreviewContent.scrollTop
    for (let i = 1; i <= Object.values(rawsSize.value).length; i++) {
      pdfHeightTotal +=
        Math.floor((rawsSize.value[i]?.height * 120) / rawsSize.value[i]?.width) + 2 + THUMBNAIL_PAGE_HEIGHT
      // 找到当前页
      if (top < pdfHeightTotal) {
        page = i
        break
      }
    }

    //将当前页和后面的4页缩略图渲染出来
    for (let i = page - 2; i < page + 5; i++) {
      if (i > 0 && i <= pdfState.total && !pdfState.thumbnailRenderPages.includes(i)) {
        pdfState.thumbnailRenderPages.push(i)
        if (thumbnailsLoading.value[i]?.length === 0) thumbnailsLoading.value[i] = true
      }
    }
  }
}, 100)

//初次加载可视区的page
const initPdfPages = () => {
  pdfState.thumbnailRenderPages = []
  const thumbnailTotal = Math.min(pdfState.total, RENDER_PAGES)
  for (let i = 1; i <= thumbnailTotal; i++) {
    pdfState.thumbnailRenderPages.push(i)
    if (pdfsLoading.value[i]?.length === 0) pdfsLoading.value[i] = true
  }

  const pdfTotal = Math.min(pdfState.total, RENDER_PAGES)
  pdfState.pdfRenderPages = []
  for (let i = 1; i <= pdfTotal; i++) {
    pdfState.pdfRenderPages.push(i)
    if (!pdfsLoading.value[i] && pdfsLoading.value[i] !== false) pdfsLoading.value[i] = true
  }

  // 让pdf滚动到中间
  let scrollLeft = 0
  if (
    pdfsLoading.value[1] === false &&
    Math.floor(rawsSize.value[1]?.width * Number(ratio.value)) < maxPreviewWidth.value
  ) {
    scrollLeft = (maxPreviewWidth.value - rawsSize.value[1]?.width * Number(ratio.value)) / 2
  }

  nextTick(() => {
    pdfAreaRef.value.scrollLeft = (maxPreviewWidth.value - rawsSize.value[1]?.width * Number(ratio.value)) / 4
  })
}

// pdf解析后获取页码和旋转
async function extractPdfInfo(pdf, pagesSize, pageIndex) {
  if (!pdfRef.value) return
  pdfsLoading.value[pageIndex] = false
  // 如果已经赋值过，不再重复赋值
  if (Object.keys(rawsSize.value).length > 0) return
  pdfState.total = pdf.numPages
  rawsSize.value = pagesSize
  isRenderSuccess.value = true
  const minRaw = flow(values, minBy("width"))(rawsSize.value)
  rawSize.width = Number(minRaw?.width)
  rawSize.height = Number(minRaw?.height)
  initPdfPages()
}

defineExpose({
  ratio,
  rawSize,
  rawsSize,
  pdfState,
  handleJump,
  isRenderSuccess,
  scrollPdf,
  pdfAreaRef
})

/* ========================修改查看缩略图启动开关图标===================================== */
const isActiveSwitch = ref(false)
const changeSwitchIcon = () => {
  isActiveSwitch.value = !isActiveSwitch.value
}

const changeThumbnailBoxStatus = () => {
  pdfState.showThumbnail = !pdfState.showThumbnail
  nextTick(() => {
    let pdfThumbnailTop = 0
    for (let i = 1; i < pdfState.current; i++) {
      pdfThumbnailTop +=
        Math.floor(Number((rawsSize.value?.[i]?.height * 120) / rawsSize.value?.[i]?.width)) + THUMBNAIL_PAGE_HEIGHT + 2
    }
    if (pdfThumbnailRef.value) {
      pdfThumbnailRef.value.scrollTop = pdfThumbnailTop
    }
  })
}

// 查看缩览图开关样式
const getDynamicSwitchClass = computed(() => {
  if (!pdfState.showThumbnail) return "default-switch"
  return ""
})

/* ====================pdf版本切换==========================*/
const switchPdf = ref(false)
const switchPdfVersion = () => {
  switchPdf.value = !switchPdf.value
}

/* ======================== 水印绘制 ======================== */

watch([() => previewWidth.value, () => previewHeight.value], val => {
  if (props.showWatermark) getWatermark()
})

const watermarkRef = ref()
const watermarkState = reactive({
  waterMark: "",
  width: 0,
  height: 0
})

function getTextActualSize(text) {
  const canvas = document.createElement("canvas")
  canvas.width = 400
  canvas.height = 400
  const ctx = canvas.getContext("2d") as CanvasRenderingContext2D
  const { actualBoundingBoxAscent, actualBoundingBoxDescent, actualBoundingBoxLeft, actualBoundingBoxRight } =
    ctx.measureText(text)
  return {
    width: actualBoundingBoxLeft + actualBoundingBoxRight,
    height: actualBoundingBoxAscent + actualBoundingBoxDescent
  }
}

async function getWatermark() {
  try {
    const res = props.sessionLess
      ? await sessionLessAxios({
          method: "get",
          url: "/api/system/system-config/watermark",
          jobId: route.query.jobId as string,
          inpNo: route.query.inpNo as string
        })
      : await getWatermarkApi()

    const resData = res.data.data
    // 获取绘制文本的尺寸
    const spacing = resData.rowSize
    const textSize = getTextActualSize(resData.waterMark)
    const fontSize = resData.fontSize * ratio.value
    const rotatePI = (-toNumber(resData.radians) / 180) * Math.PI
    const canvas = document.createElement("canvas")
    canvas.width = (textSize.width * 4 + 20 + spacing) / 2
    canvas.height = (textSize.width * 4 + 20 + spacing) / 2
    const ctx = canvas.getContext("2d")
    if (!ctx) return
    // 绘制之前清空画布
    ctx.clearRect(0, 0, watermarkState.width, watermarkState.height)
    // 设置画笔属性
    ctx.font = `${fontSize}px serif`
    ctx.fillStyle = `rgba(${resData.colorRgb})`
    ctx.globalAlpha = resData.transparency
    // 平移画布后，左上角不再是0，0, 而是（-canvas.width / 2， -canvas.height / 2）
    ctx.translate(canvas.width / 2, canvas.height / 2)
    // 旋转水印方向
    ctx.rotate(rotatePI)
    // 文字宽度
    const stringWidth = ctx.measureText(resData.waterMark || "无纸化病案系统").width

    ctx.fillText(resData.waterMark || "无纸化病案系统", -stringWidth / 2, fontSize / 4)
    if (watermarkRef.value) {
      watermarkRef.value.style.backgroundImage = `url(${canvas.toDataURL()})`
      watermarkRef.value.style.backgroundRepeat = "repeat"
      watermarkRef.value.style.backgroundPosition = `top left`
    }
  } catch (err: any) {
    toastError(err, "获取水印失败")
  }
  // getWatermarkApi()
  //   .then(res => {

  //   })
  //   .catch(err => {
  //     toastError(err, "获取水印失败")
  //   })
}
</script>

<style lang="less" scoped>
.pdf-preview-wrapper {
  display: flex;
  height: 100%;
  .pdf-preview-main {
    flex: 1;
    height: 100%;
    width: 100%;
    position: relative;
    .pdf-preview-header {
      border-left: 1px solid #f3f3f3;
      height: 40px;
      align-items: center;
      justify-content: center;
      // gap: 20px;
      background-color: #fff;
      display: flex;
      color: #fff;
      font-size: 14px;
      line-height: 24px;
      padding: 0px 32px;
      margin-bottom: 30px;
      overflow: auto;

      i {
        font-size: 18px;
      }

      .change-box {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        column-gap: 10px;
        &:first-child {
          justify-content: flex-start;
        }
        &:last-child {
          justify-content: flex-end;
        }

        .action {
          cursor: pointer;
          color: #666;
        }
        .all-total {
          text-align: right;
          padding-right: 12px;
        }
        .page-input-container {
          display: flex;
          align-items: center;
          justify-content: flex-end;
        }
        .page-input {
          display: flex;
          align-items: center;
          white-space: nowrap;
          :deep(.el-input-number) {
            width: 45px;
            border: none;
            .el-input__wrapper {
              padding: 0px 5px;
              .el-input__inner {
                font-size: 14px;
                color: #666;
                text-align: center;
                line-height: 18px;
                height: 20px;
              }
            }
          }
        }

        .page-hint {
          cursor: default;
          color: #666;
        }
        .number-slice {
          padding: 0px 10px;
        }
        :deep(.el-checkbox) {
          color: #fff;
          --el-checkbox-checked-icon-color: #666;
          .el-checkbox__label {
            color: #fff;
          }
        }
      }

      .right-header {
        flex: 0;
      }
    }
    .pdf-preview-content {
      position: relative;
      overflow-y: auto;
      // padding-top: 30px;
      height: calc(100% - 100px);
      .pdf-preview-content-area {
        position: relative;
        z-index: 100;
        width: fit-content;
        margin: 0 auto;
        // overflow: auto;
      }
      .pdf-page-num {
        text-align: center;
        color: #666;
        font-size: 14px;
        height: 30px;
        line-height: 28px;
      }
      :deep(.vue-pdf-embed) {
        width: 100%;
        margin: auto;
        display: flex;
        flex-direction: column;
        align-items: center;
        .vue-pdf-embed__page {
          border: 1px solid #ccc;
          background-color: #fff;
        }
      }

      .pdf-thumbnail-container {
        width: 200px;
        display: flex;
        flex-direction: column;
        row-gap: 20px;
      }
    }
  }

  .pdf-preview-thumbnail-wrapper {
    transition: width 0.3s;
    position: absolute;
    right: 0;
    z-index: 99;
    // height: 100%;
    height: calc(100% - 72px);
    .switch {
      position: absolute;
      top: 50%;
      cursor: pointer;
      z-index: 99;
      height: 60px;
      width: 20px;
      background: rgba(0, 0, 0, 0.1);
      border-top-right-radius: 5px;
      border-bottom-right-radius: 5px;
      display: flex;
      align-items: center;
      i {
        font-size: 20px;
        color: #81878e;
      }
      &:hover {
        background-color: #0c7ffc;
        i {
          color: #fff;
        }
      }
    }
    &-content {
      max-height: calc(100% - 80px);
      // overflow: auto;
    }
    .default-switch {
      right: 0;
      transform: rotate(180deg);
    }
    .pdf-preview-thumbnail {
      padding: 0 0px 12px 0px;
      box-sizing: border-box;
      flex-direction: column;
      display: flex;
      align-items: center;
      height: 100%;
      background-color: #fff;
      box-shadow: -5px 0 10px -5px #ddd;
      min-height: 200px;
      &-title {
        padding: 10px 0;
        font-size: 14px;
        text-align: center;
        color: #666;
        font-weight: 500;
        background-color: #fff;
        width: 100%;
      }
      &-content {
        min-height: 220px;
        overflow-y: auto;
        padding: 0 10px;
        overflow-x: hidden;
      }
      &-item {
        cursor: pointer;
        width: 120px;
        min-width: 120px;
        height: auto;
        :deep(.vue-pdf-embed__page) {
          border: 1px solid #ccc;
          background-color: #fff;
        }
      }
      .active {
        :deep(.vue-pdf-embed__page) {
          border: 1px solid #0c7ffc;
        }
      }
      &-page {
        text-align: center;
        margin: auto;
        font-size: 14px;
        color: #81878e;
      }
    }
  }
}

.tips {
  &-return {
    i {
      font-size: 16px;
      font-weight: 500;
      margin-right: 5px;
    }
    width: calc(100% - 20px);
    background: #fff;
    color: #fb7d0f;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 10px;
    line-height: 1;
    cursor: pointer;
  }

  &-view {
    color: #666;
    display: flex;
    align-items: center;
    img {
      margin-right: 5px;
    }
  }
}

.watermark-layer {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
}
.pdf-preview-location {
  height: 30px;
  line-height: 30px;
  padding: 10px;
  width: calc(100% - 20px);
  background: #fff;
  position: absolute;
  bottom: 0;
  z-index: 100;
}

.left-header {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 175px;
  gap: 20px;
}
</style>
