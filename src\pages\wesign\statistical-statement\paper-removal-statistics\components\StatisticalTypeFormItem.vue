<template>
  <el-form-item :label="props.label">
    <el-select v-model="statisticalTypeFilterProp" style="margin-bottom: 5px" @change="handleSelectChange">
      <el-option
        v-for="item in filterPropOptions"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      ></el-option>
    </el-select>

    <DaterangeFormItem
      v-if="statisticalTypeFilterProp === 'section'"
      v-model:model-value="statisticalTypeFilterValue"
      label=""
      :is-clearable="false"
    ></DaterangeFormItem>

    <el-date-picker
      v-else-if="statisticalTypeFilterProp === 'month'"
      v-model="statisticalTypeFilterValue"
      type="month"
      value-format="YYYY-MM"
      placeholder="请选择时间"
      :clearable="false"
    />

    <el-date-picker
      v-else-if="statisticalTypeFilterProp === 'year'"
      v-model="statisticalTypeFilterValue"
      type="year"
      value-format="YYYY"
      placeholder="请选择时间"
      :clearable="false"
    />

    <el-select v-else-if="statisticalTypeFilterProp === 'quarter'" v-model="statisticalTypeFilterValue">
      <el-option v-for="item in quarterOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
    </el-select>
  </el-form-item>
</template>

<script setup lang="ts">
import { computed } from "vue"
import { DaterangeFormItem } from "@/base-components"

const props = defineProps<{
  label: string
  filterProp: string
  filterValue: string | [number, number]
}>()

const filterPropOptions = [
  { label: "按区间", value: "section" },
  { label: "按年", value: "year" },
  { label: "按季度", value: "quarter" },
  { label: "按月份", value: "month" }
]

const quarterOptions = [
  { label: "第一季度", value: "Q1" },
  { label: "第二季度", value: "Q2" },
  { label: "第三季度", value: "Q3" },
  { label: "第四季度", value: "Q4" }
]

function handleSelectChange(val) {
  if (val === "year") {
    const now = new Date()
    emits("update:filterValue", `${now.getFullYear()}`)
  } else if (val === "month") {
    emits("update:filterValue", getYearMonth(new Date()))
  } else if (val === "quarter") {
    emits("update:filterValue", "Q1")
  } else {
    emits("update:filterValue", getDate())
  }
}

function getYearMonth(date) {
  let year = date.getFullYear()
  let month = date.getMonth() + 1 // 月份是从0开始的，所以需要加1
  month = month < 10 ? "0" + month : month // 如果月份小于10，前面添加0
  return year + "-" + month
}

// 获取当前日期(完整时间)
function getDate() {
  const archiveDateEnd = new Date().getTime()
  const archiveDateStart = new Date(new Date().setMonth(new Date().getMonth() - 1)).getTime()
  return [archiveDateStart, archiveDateEnd]
}

const emits = defineEmits(["update:filterProp", "update:filterValue"])

const statisticalTypeFilterProp = computed({
  get: () => props.filterProp,
  set: val => emits("update:filterProp", val)
})

const statisticalTypeFilterValue = computed({
  get: () => props.filterValue,
  set: val => emits("update:filterValue", val)
})
</script>
