<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
        />

        <DepartmentFormItem v-model:model-value="searchFormState.outHospitalDeptWsid" label="出院科室" />

        <DaterangeFormItem v-model:model-value="searchFormState.outHospitalDatetime" label="出院时间" />

        <CommonInputFormItem v-model="searchFormState.attendingDoctor" label="主治医师" />

        <el-form-item label="编码员">
          <el-select v-model="searchFormState.catalogerUserWsid" style="width: 100%">
            <el-option v-for="item in coders" :key="item.userWsid" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>

        <PatientLabelFormItem v-model="searchFormState.patientLabel" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="tableRef"
        table-id="catalogAssignmentTableIdent"
        :table-columns="hospitalizationColumns"
        :request-api="getCatalogLists"
        :request-params="catalogAssignmentParams"
        :data-callback="dataCallback"
      >
        <!-- 患者姓名 -->
        <template #patientName="{ row }">
          <div style="display: flex; align-items: center; flex-wrap: wrap">
            <span style="margin: 0 16px">{{ row.patientName }}</span>
            <PatientLabelTag :row="row" />
          </div>
        </template>
        <template #header>
          <div style="margin-bottom: 12px">
            <el-button
              :type="assignmentType === 'AUTO_ASSIGNMENT' ? 'primary' : ''"
              @click="handleAssignment('AUTO_ASSIGNMENT')"
            >
              按数量分配
            </el-button>
            <el-button :type="assignmentType === 'USER_DEPT' ? 'primary' : ''" @click="handleAssignment('USER_DEPT')">
              按科室分配
            </el-button>
            <BatchOperationButton type="primary" :disabled="!selectedRows.length" @click="handleAssignment('MANUAL')">
              手动分配
            </BatchOperationButton>

            <el-button
              :icon="h('i', { class: 'ri-menu-search-line', style: { 'font-size': '18px' } })"
              @click="processDialogRef?.show()"
            >
              编目进度查询
            </el-button>
          </div>
        </template>
      </CommonTable>
    </template>

    <!-- 编码进度弹窗 -->
    <CodingProcessDialog ref="processDialogRef" range-type="IN_HOSPITAL" />

    <!-- 分配编目任务/重新分配 -->
    <AssignmentDialog
      ref="assignmentDialogRef"
      range-type="IN_HOSPITAL"
      :selected-rows="selectedRows"
      @success="handleAssignSuccess"
    />
  </PageContainer>
</template>

<script setup lang="ts">
import { h, reactive, ref, computed, onMounted } from "vue"
import {
  SearchContainer,
  CommonTable,
  PatientFilterFormItem,
  DepartmentFormItem,
  DaterangeFormItem,
  BatchOperationButton,
  PageContainer,
  CommonInputFormItem,
  PatientLabelTag,
  PatientLabelFormItem
} from "@/base-components"
import { TabsRouter } from "@/page-components"

import { getCatalogConfigInfo, getCatalogLists } from "../interface"
import AssignmentDialog from "./components/AssignmentDialog.vue"
import CodingProcessDialog from "./components/CodingProcessDialog.vue"
import { hospitalizationColumns, catalogStatusEnum, tabsRouterList } from "./config"
import { useTableSearch } from "@/hooks"
import { formatDatetime } from "@/utils"

const coders = ref<any[]>([]) // 编码员数据
const assignmentType = ref("") // 分配类型

onMounted(async () => {
  const res = (await getCatalogConfigInfo("IN_HOSPITAL")).data.data
  coders.value = res.cataloger.map(item => ({
    label: item.realName,
    value: item.userWsid
  }))
  assignmentType.value = res.assignmentType
})

/* ======================== 表格查询 ======================== */

const searchFormState = reactive({
  patientFilterValue: "",
  patientFilterProp: "patientName",
  outHospitalDeptWsid: "",
  outHospitalDatetime: "",
  attendingDoctor: "",
  catalogerUserWsid: "",
  patientLabel: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

const catalogAssignmentParams = computed(() => {
  return { ...searchParams, queryType: "catalog_assignment" }
})

/* ======================== 表格相关方法 ======================== */

const tableRef = ref<InstanceType<typeof CommonTable>>()

// 当前选中表格
const selectedRows = computed(() => tableRef.value?.tableState?.selectedRows ?? [])

const dataCallback = (data: Array<Record<string, any>>) => {
  data.forEach(item => {
    item.inHospitalDatetime = formatDatetime(item.inHospitalDatetime)
    item.outHospitalDatetime = formatDatetime(item.outHospitalDatetime)
    item.assignmentTime = formatDatetime(item.assignmentTime)
    item.catalogTime = formatDatetime(item.catalogTime)
    item.catalogStatusEnum = catalogStatusEnum[item.catalogStatusEnum]
  })
  return data
}

/* ======================== 进度弹窗 ======================== */

const processDialogRef = ref()

/* ======================== 分配弹窗 ======================== */

const assignmentDialogRef = ref()

const handleAssignment = type => {
  assignmentDialogRef.value.show(type)
}

const handleAssignSuccess = type => {
  assignmentType.value = type
  tableRef.value?.refreshTableData()
}
</script>
