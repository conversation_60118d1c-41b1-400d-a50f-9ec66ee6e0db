import { reactive, ref } from "vue"

type ActionType = "add" | "edit"

const useFormSetting = <T extends Record<string, any>>(formInitialValue: T) => {
  // 表单弹窗是否可见
  const visible = ref(false)
  // 表单数据
  const formState = reactive({ ...formInitialValue })
  // 表单操作类型
  const actionType = ref<ActionType>("add")

  // 打开form
  const showForm = (type: ActionType, row: T = formInitialValue) => {
    resetFormState(row)
    visible.value = true
    actionType.value = type
  }

  // 关闭form
  const closeForm = () => {
    visible.value = false
  }

  // 重置formState
  const resetFormState = (row: T) => {
    for (const key in formState) {
      formState[key] = row[key]
    }
  }

  return { showForm, closeForm, actionType, visible, formState }
}

export default useFormSetting
