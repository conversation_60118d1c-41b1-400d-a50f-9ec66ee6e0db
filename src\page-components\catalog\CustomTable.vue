<template>
  <div class="common-table-container">
    <div class="common-table-header">
      <AddButton @click="handleOperation('', 'add')">新增</AddButton>
    </div>
    <el-table
      class="common-table"
      :data="props.tableData"
      :border="border"
      :row-key="mode === 'tree' ? 'wsid' : undefined"
      :header-cell-style="{ background: 'rgb(248,249,252)', color: '#030814' }"
    >
      <template v-for="item in customTableColumns" :key="item">
        <el-table-column
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
          :min-width="item.minWidth"
          :sortable="item.sortable"
          :show-overflow-tooltip="false"
          :resizable="true"
          :fixed="item.fixed"
          :align="item.align"
        >
          <template #default="scope">
            <slot :name="item.prop" :row="scope.row">
              <el-input v-if="item.type === 'input'" v-model="scope.row[item.prop]"></el-input>

              <!-- 下拉选择 -->
              <template v-else-if="item.type === 'select'">
                <!-- 可查询的下拉框 -->
                <el-select
                  v-if="item.queryParam"
                  v-model="scope.row[item.prop]"
                  fit-input-width
                  filterable
                  remote
                  reserve-keyword
                  style="width: 100%"
                  :remote-method="val => handleOptionsChange(val, item.queryParam)"
                  @change="handleCodeChange($event, scope, item)"
                  @focus="$event => getOption($event, item)"
                >
                  <el-option
                    v-for="option in options[item.queryParam]"
                    :key="option.code"
                    :label="option.code"
                    :value="option"
                  >
                    <div>
                      <span>{{ option.code }}</span>
                      <span style="color: #aaa">{{ `(${option.name})` }}</span>
                    </div>
                  </el-option>
                </el-select>

                <el-select
                  v-else
                  v-model="scope.row[item.prop]"
                  fit-input-width
                  style="width: 100%"
                  @change="handleCodeChange($event, scope, item)"
                >
                  <el-option
                    v-for="option in props.selectOptions[item.optionKey]"
                    :key="option.key"
                    :label="option.value"
                    :value="option.key"
                  ></el-option>
                </el-select>
              </template>

              <!-- 日期选择 -->
              <el-date-picker
                v-else-if="item.type === 'date'"
                v-model="scope.row[item.prop]"
                type="datetime"
                placeholder="请选择日期"
              />

              <!-- 操作。 获取row 的index -->
              <div v-else-if="item.prop === 'pageOptionProp'" style="display: flex">
                <el-button v-if="scope.$index !== 0" type="text" @click="handleMove(scope, 'up')">上移</el-button>
                <el-button
                  v-if="scope.$index !== props.tableData.length - 1"
                  type="text"
                  @click="handleMove(scope, 'down')"
                >
                  下移
                </el-button>
                <el-button type="text" @click="handleOperation(scope, 'delete')">删除</el-button>
                <!-- <el-button v-if="props.addAble" type="text" @click="handleOperation(scope, 'add')">添加</el-button> -->
              </div>

              <span v-else>{{ scope.row[item.prop] }}</span>
            </slot>
          </template>
        </el-table-column>
      </template>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue"
import { AddButton } from "@/base-components"
// import { getOperationIcdInfo, getIcdInfo, getPathologyIcdInfo } from "../../interface"
import { getIcdCode } from "@/interfaces"
import type { TableColumnItem, TableColSettingItem } from "@/types"

interface CommonTableProps {
  tableId?: string
  tableColumns: Array<TableColumnItem>

  border?: boolean
  pagination?: boolean
  mode?: "basic" | "tree"

  tableData?: Array<any>
  addAble?: boolean
  selectOptions: Record<string, any>

  handleOperation: (params: any) => any
  handleMove: (params: any) => any
  updateColumn: (params: any) => any
}

const props = withDefaults(defineProps<CommonTableProps>(), {
  addAble: false,
  border: true,
  pagination: true,
  mode: "basic"
})

const options = reactive({
  ICD: [],
  ICD_CM: [],
  ICD_O: [],
  ICD_DM: []
})

let initColumnsConfig = props.tableColumns.map(item => ({ ...item, id: item.prop || item.type || "", checked: true }))
const customTableColumns = ref<Array<TableColSettingItem>>(initColumnsConfig)

const getOption = async (event, item) => {
  if (item.queryParam) {
    await getIcdCode({
      item: "",
      type: item.queryParam
    }).then(res => (options[item.queryParam] = res))
  }
}

// 表格行 上、下移
const handleMove = (scope, type: "up" | "down") => {
  props.handleMove(props.tableData, scope, type)
}

// 删除、添加
const handleOperation = (scope, type: "add" | "delete") => {
  props.handleOperation(props.tableData, scope, type, props.tableId)
}

// 列修改数据（icd10code、icd10name）
const handleCodeChange = (val, scope, item) => {
  props.updateColumn(props.tableData, scope, val, item)
}

/**
 * 更新option
 * @param val
 * @param queryParam
 */
const handleOptionsChange = (val, queryParam) => {
  if (val) {
    getIcdCode({
      item: val,
      type: queryParam
    }).then(res => (options[queryParam] = res))
  }
}
</script>

<style lang="less" scoped>
.common-table-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  //   overflow-y: auto;

  .common-table-header {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;

    .table-setting-btn {
      display: flex;
      align-items: center;
      color: #666;
      .table-setting-text {
        margin-left: 4px;
        font-size: 14px;
      }
    }
  }
  .common-table {
    flex: 1;
    overflow-y: hidden;
    :deep(.el-scrollbar__view) {
      height: 100%;
    }
  }

  .table-pagination {
    margin-top: 20px;
    align-self: flex-end;
  }
}

:deep(.el-table) tr {
  //   background-color: #dadfe7;
}

:deep(.el-table) th {
  background-color: #dadfe7 !important;
}
</style>
