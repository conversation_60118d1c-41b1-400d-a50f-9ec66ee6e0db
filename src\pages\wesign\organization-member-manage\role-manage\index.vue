<template>
  <CardContainer>
    <template #header>
      <span class="tab-btn active-btn">角色列表</span>
    </template>
    <CommonTable ref="roleTableRef" :table-columns="roleTableColumns" :request-api="getRolesList">
      <template #header>
        <AddButton @click="clickAddRole">新增</AddButton>
      </template>
      <template #operation="{ row }">
        <TableButton @click="clickEditRole(row)">编辑</TableButton>
        <TableButton @click="clickAddUser(row)">设置关联用户</TableButton>
        <TableButton @click="clickUserList(row)">用户列表</TableButton>
        <TableButton :disabled="row.fixRoleFlag === 1" tooltip="系统内置角色不可删除" @click="clickDeleteRole(row)">
          删除
        </TableButton>
      </template>
    </CommonTable>
  </CardContainer>

  <!-- 新增/编辑弹窗 -->
  <DialogForm
    v-model:visible="isVisibleDialog"
    :title="dialogType === 'add' ? '新增' : '编辑'"
    :form-config="roleFormConfig"
    :form-state="dialogForm"
    :confirm-callback="handleConfirm"
  />

  <!-- 关联用户弹窗 -->
  <AuthorizedDialog
    v-model:visible="authorizedDialogVisible"
    :role-wsid="dialogForm.roleWsid"
    :role-name="dialogForm.roleName"
  />

  <!-- 关联用户抽屉 -->
  <el-drawer v-model="showDrawer" title="关联用户" :size="680" destroy-on-close>
    <CommonTable
      ref="drawerTableRef"
      :pagination="false"
      :border="false"
      :table-columns="drawerTableColumns"
      :request-api="getUserInRolesApi"
      :request-params="drawerTableSearchParams"
    >
      <template #operation="{ row }">
        <el-button link type="primary" @click="removeUserInRole(row)">移除</el-button>
      </template>
    </CommonTable>
  </el-drawer>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import { cloneDeep } from "lodash-es"
import { CommonTable, CardContainer, AddButton, TableButton } from "@/base-components"
import { DialogForm } from "@/page-components"
import { useFormSetting } from "@/hooks"
import { getRolesList, getUserInRolesApi } from "@/interfaces"
import { SystemPrompt, Message, toastError } from "@/utils/message-tool"
import { addRoleApi, editRoleApi, removeUserInRoleApi, editRoleStatusApi } from "../interface"
import AuthorizedDialog from "./AuthorizedDialog.vue"
import { roleTableColumns, drawerTableColumns, roleFormConfig } from "./config"

const authorizedDialogVisible = ref(false)

/* ======================== 表格角色操作 ======================== */

const roleTableRef = ref<InstanceType<typeof CommonTable>>()
const isVisibleDialog = ref(false)
const dialogType = ref<"add" | "edit">("add")

const initFormData = {
  roleName: "",
  remark: "",
  roleWsid: ""
}

const dialogForm = reactive(cloneDeep(initFormData))

const { showAddForm, showEditForm } = useFormSetting(dialogForm, initFormData, dialogType, isVisibleDialog)

// 新增角色
function clickAddRole() {
  showAddForm()
}

// 编辑角色
function clickEditRole(row) {
  showEditForm(row)
  dialogForm.roleWsid = row.wsid
}

// 添加关联用户
function clickAddUser(row) {
  dialogForm.roleWsid = row.wsid
  dialogForm.roleName = row.roleName
  authorizedDialogVisible.value = true
}

// 确认新增&修改
function handleConfirm() {
  if (dialogType.value === "add") {
    return addRoleApi({ ...dialogForm })
      .then(() => {
        Message.success("添加成功")
        roleTableRef.value?.refreshTableData()
        isVisibleDialog.value = false
      })
      .catch(err => toastError(err))
  } else {
    return editRoleApi({ ...dialogForm })
      .then(() => {
        Message.success("修改成功")
        roleTableRef.value?.refreshTableData()
        isVisibleDialog.value = false
      })
      .catch(err => toastError(err))
  }
}

// 展示用户列表
function clickUserList(row) {
  drawerTableSearchParams.roleWsid = row.wsid
  showDrawer.value = true
  dialogForm.roleWsid = row.wsid
}

// 删除角色
const clickDeleteRole = row => {
  SystemPrompt("删除角色后，角色包含成员不会被删除！").then(() => {
    editRoleStatusApi({ roleWsid: row.wsid, status: "DEL" })
      .then(() => {
        Message.success("删除角色成功")
        roleTableRef.value?.refreshTableData()
      })
      .catch(err => toastError(err))
  })
}

/* ======================== 抽屉详情 ======================== */

const showDrawer = ref(false)
const drawerTableRef = ref<InstanceType<typeof CommonTable>>()
const drawerTableSearchParams = reactive({ roleWsid: "" })

// 移除角色内用户
function removeUserInRole(row) {
  SystemPrompt("您确定要移除成员吗？").then(() => {
    removeUserInRoleApi({ userWsid: row.userWsid, roleWsid: dialogForm.roleWsid })
      .then(() => {
        Message.success("操作成功")
        drawerTableRef.value?.refreshTableData()
      })
      .catch(err => toastError(err))
  })
}
</script>

<style lang="less" scoped>
.tab-btn {
  display: inline-block;
  height: 100%;
  box-sizing: border-box;
  margin-right: 50px;
  cursor: pointer;
  &.active-btn {
    border-bottom: solid 2px var(--el-color-primary);
  }
}
</style>
