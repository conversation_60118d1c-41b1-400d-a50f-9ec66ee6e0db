<template>
  <CardContainer
    v-loading="props.loading"
    :title="props.title"
    :style="{ marginRight: '10px', width: props.width + 'px' }"
  >
    <template #header>
      <el-button
        v-if="!props.hideAdd"
        text
        type="primary"
        :icon="h('i', { class: 'ri-add-line', style: { 'font-size': '18px' } })"
        @click="$emit('clickAdd')"
      >
        新增
      </el-button>
    </template>

    <div
      v-for="item in props.listData"
      :key="item.id"
      class="page-side-item"
      :class="{ 'classify-active': item.id === activeId }"
      @click="changeActive(item.id, item.oriCode, item)"
    >
      <OverflowTooltip class="item-text" :content="item.name" max-width="200px" />
      <el-popover
        v-if="!props.hideEdit"
        popper-class="page-side-popover"
        placement="bottom"
        :width="90"
        trigger="click"
        :teleported="false"
      >
        <template #reference>
          <i class="ri-more-fill" style="cursor: pointer"></i>
        </template>
        <div class="item-more-btn" @click="$emit('clickEdit', item)">编辑</div>
        <div v-if="!props.hideAdd" class="item-more-btn" @click="$emit('clickDelete', item)">删除</div>
      </el-popover>
    </div>
  </CardContainer>
</template>

<script lang="ts" setup>
import { ref, h } from "vue"
import { CardContainer, OverflowTooltip } from "@/base-components"
import { CollectTypeItem } from "@/types"

const props = withDefaults(
  defineProps<{
    listData: Array<CollectTypeItem>
    loading: boolean
    title: string
    width?: number
    hideAdd?: boolean
    hideEdit?: boolean
  }>(),
  {
    width: 300
  }
)

const activeId = ref("")

const emits = defineEmits(["clickAdd", "clickEdit", "clickDelete", "changeActive"])

const changeActive = (id: string, code: string, item: Record<string, any>) => {
  activeId.value = id
  emits("changeActive", id, code, item)
}

const reset = () => {
  activeId.value = ""
}

defineExpose({ reset, changeActive })
</script>

<style lang="less" scoped>
.page-side-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 40px;
  line-height: 40px;
  padding: 0 10px;
  &.classify-active {
    background-color: rgb(44 104 255 / 9.8%);
  }
  .item-text {
    font-size: 14px;
    font-weight: 500;
    color: #0a1633;
    cursor: pointer;
  }
  :deep(.page-side-popover) {
    min-width: 20px;
    padding: 4px 0;
  }
  .item-more-btn {
    height: 40px;
    font-size: 14px;
    text-align: center;
    color: #0a1633;
    font-weight: 500;
    line-height: 40px;
    cursor: pointer;
    &:hover {
      background-color: rgb(231 242 255);
    }
  }
}
</style>
