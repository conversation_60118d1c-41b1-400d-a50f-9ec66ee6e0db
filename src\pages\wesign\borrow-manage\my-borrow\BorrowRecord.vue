<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchForm
        :form-config="borrowRecordSearchFormConfig"
        :form-state="searchFormState"
        @query-btn-click="handleQuery"
        @reset-btn-click="handleReset"
      />
    </template>

    <template #table>
      <CommonTable
        table-id="borrowRecordTable"
        :table-columns="borrowRecordColumns"
        :request-api="getBorrowListApi"
        :request-params="searchParams"
      >
        <template #applyDatetime="{ row }">
          {{ formatDatetime(row.applyDatetime) }}
        </template>
        <template #outHospitalDatetime="{ row }">
          {{ formatDatetime(row.outHospitalDatetime) }}
        </template>
        <template #approveLatestTime="{ row }">
          {{ formatDatetime(row.approveLatestTime) }}
        </template>
        <template #overdueDatetime="{ row }">
          {{ formatDatetime(row.overdueDatetime) }}
        </template>
        <template #approverDatetime="{ row }">
          {{ formatDatetime(row.approverDatetime) }}
        </template>
        <template #statusEnumName="{ row }">
          <el-tag :type="(getBorrowStatus(row.statusEnumName)?.color as any)">
            {{ getBorrowStatus(row.statusEnumName)?.label }}
          </el-tag>
        </template>
        <template #timeLimitCh="{ row }">
          {{
            row.approveTimeLimitUnitEnumName === "FOREVER"
              ? "永久"
              : row.approveTimeLimitCh + unitOptions[row.approveTimeLimitUnitEnumName]
          }}
        </template>
        <template #operation="{ row }">
          <TableButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.View)"
            :disabled="row.statusEnumName === 'TIME_OUT_AUDIT'"
            tooltip="审批已超时"
            @click="showBorrowDetailDialog(row)"
          >
            详情
          </TableButton>
        </template>
      </CommonTable>
    </template>

    <BorrowDetailDrawer ref="BorrowDetailDrawerRef" />
  </PageContainer>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue"
import { TableButton, CommonTable, PageContainer } from "@/base-components"
import { TabsRouter, SearchForm } from "@/page-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { useUserStore } from "@/stores"
import { formatDatetime } from "@/utils"

import BorrowDetailDrawer from "../module-components/BorrowDetailDrawer.vue"
import { borrowRecordSearchFormConfig, borrowRecordColumns, tabsRouterList, menuId, unitOptions } from "./config"
import { getBorrowListApi } from "./interface"
import { getBorrowStatus } from "./utils"

const { hasOperationPermission } = useUserStore()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  patientFilterProp: "",
  patientFilterValue: "",
  outHospitalDeptWsid: "",
  status: "",
  applyDatetime: "",
  outHospitalDatetime: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/* ================= 详情弹窗 ================== */
const BorrowDetailDrawerRef = ref()

// 打开详情弹窗
const showBorrowDetailDialog = row => {
  BorrowDetailDrawerRef.value.open(row)
}
</script>

<style lang="less" scoped>
.print {
  color: #0a1633;
}

.print-catalogue-list {
  height: 300px;
  font-weight: bold;
  padding: 12px 20px;
  background-color: rgb(250 250 250);
  overflow-y: auto;
  margin-top: 20px;
}

.print-catalogue {
  height: 32px;
  line-height: 32px;
}

.print__footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
}
</style>
