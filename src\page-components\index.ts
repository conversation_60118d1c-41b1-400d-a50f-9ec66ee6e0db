import BatchImport from "./BatchImport.vue"
import ImageEditor from "./ImageEditor.vue"
import MedicalRecordTree from "./MedicalRecordTree.vue"
import PageSideList from "./PageSideList.vue"
import ProgressDialog from "./ProgressDialog.vue"
import TabsRouter from "./TabsRouter.vue"
import ApprovalDetail from "./approval-detail/index.vue"
import ComplexSearch from "./complex-search/ComplexSearch.vue"
import DocumentMeta from "./document-meta/index.vue"

export * from "./business-dialogs" // 业务弹窗
export * from "./business-forms" // 业务动态表单
export * from "./business-drawer" //业务抽屉
export * from "./catalog" //编目相关组件
export * from "./outpatient-and-emergency" //门急诊编目

export {
  PageSideList,
  TabsRouter,
  BatchImport,
  MedicalRecordTree,
  DocumentMeta,
  ApprovalDetail,
  ProgressDialog,
  ComplexSearch,
  ImageEditor
}
