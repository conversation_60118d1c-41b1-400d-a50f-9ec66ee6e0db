import { variableCodeValidator } from "@/utils"
import type { TableColumnItem, DialogFormConfigItem, BaseOptionItem } from "@/types"
import type { FormRules } from "element-plus"

export const tabsRouterList = [
  { label: "首页元数据", path: "/system-manage/data-standard/home-meta" },
  { label: "医保结算元数据", path: "/system-manage/data-standard/medical-insurance-meta" }
  // { label: "数据元值域代码表", path: "/system-manage/data-standard/code-table" }
]

/* ======================== 数据元值域代码表 ======================== */

export const codeTableColumns: Array<TableColumnItem> = [
  { prop: "code", label: "值域代码", minWidth: 100 },
  { prop: "key", label: "值", minWidth: 100 },
  { prop: "value", minWidth: 150, label: "名称" },
  { prop: "remark", minWidth: 150, label: "说明" },
  { prop: "hospitalKey", minWidth: 150, label: "对照值" },
  { prop: "hospitalValue", minWidth: 150, label: "对照名称" },
  { prop: "operation", label: "操作", width: 160, fixed: "right" }
]

// 值映射表单配置
export const metaCodeDialogFormConfig: Array<DialogFormConfigItem> = [
  {
    type: "input",
    label: "值",
    prop: "key",
    rules: [{ required: true, message: "请输入值", trigger: "blur" }]
  },
  {
    type: "input",
    label: "名称",
    prop: "value",
    rules: [{ required: true, message: "请输入名称", trigger: "blur" }]
  },
  {
    type: "input",
    label: "说明",
    prop: "remark"
  },
  {
    type: "input",
    label: "对照值",
    prop: "hospitalKey"
  },
  {
    type: "input",
    label: "对照名称",
    prop: "hospitalValue"
  }
]

// 值域代码表单配置
export const classifyDialogConfig: Array<DialogFormConfigItem> = [
  {
    type: "input",
    label: "值域名称",
    prop: "name",
    rules: [{ required: true, message: "请输入值域名称", trigger: "blur" }]
  },
  {
    type: "input",
    label: "值域代码",
    prop: "code",
    rules: [{ required: true, validator: variableCodeValidator, trigger: "blur" }]
  }
]

/* ======================== 首页元数据 ======================== */

export const homeMetaTableColumns: Array<TableColumnItem> = [
  { prop: "checked", type: "selection", width: 50, must: true },
  { prop: "collectFieldName", label: "字段名", minWidth: 100, must: true },
  { prop: "collectItem", minWidth: 120, label: "数据项" },
  { prop: "collectValueTypeDesc", minWidth: 100, label: "数据类型" },
  { prop: "collectLength", minWidth: 100, label: "长度" },
  { prop: "requiredDesc", label: "是否必填", minWidth: 120, sortable: true },
  { prop: "remark", label: "备注", minWidth: 180, sortable: true },
  { prop: "collectValueFormat", label: "取值范围", minWidth: 120, sortable: true },
  { prop: "status", label: "是否启用", minWidth: 100 },
  { prop: "operation", label: "操作", width: 200, fixed: "right" }
]

export const fieldRangeColumns: Array<TableColumnItem> = [
  { prop: "key", label: "值", minWidth: 100, sortable: false },
  { prop: "value", minWidth: 200, label: "值含义", width: 200 },
  { prop: "description", minWidth: 200, label: "说明" }
]

export const transformValueType = (valueType: string) => {
  switch (valueType) {
    case "STRING":
      return "字符"
    case "NUMBER":
      return "数字"
    case "DATA":
    case "DATE":
      return "日期"
    case "DATA_TIME":
    case "DATE_TIME":
      return "日期时间"
    case "COLLECTIONS":
      return "集合"
    default:
      return "--"
  }
}

export const homeMetaFormConfig: Array<DialogFormConfigItem> = [
  {
    type: "input",
    label: "字段名",
    prop: "collectFieldName",
    rules: [{ required: true, message: "请输入字段名", trigger: "blur" }]
  },
  {
    type: "input",
    label: "数据项",
    prop: "collectItem",
    rules: [{ required: true, message: "请输入数据项", trigger: "blur" }]
  },
  {
    type: "select",
    label: "数据类型",
    prop: "collectValueType",
    options: [
      { value: "STRING", label: "字符" },
      { value: "NUMBER", label: "数字" },
      { value: "DATE", label: "日期" },
      { value: "DATE_TIME", label: "日期时间" },
      { value: "COLLECTIONS", label: "集合" }
    ],
    rules: [{ required: true, message: "请选择数据类型", trigger: "blur" }]
  },
  {
    type: "select",
    label: "字段类型",
    prop: "standardType",
    options: [
      { value: "FIRST_PAGE", label: "首页字段" },
      { value: "HQMS", label: "HQMS字段" }
    ],
    rules: [{ required: true, message: "请选择字段类型", trigger: "blur" }]
  },
  {
    type: "number",
    label: "长度",
    prop: "collectLength",
    rules: [{ type: "number", max: 99, min: 1, message: "字段长度应在1到99之间" }]
  },
  {
    type: "radio",
    label: "是否必填",
    prop: "required",
    options: [
      { value: "YES", label: "是" },
      { value: "NO", label: "否" }
    ]
  },
  {
    type: "select",
    label: "取值范围",
    prop: "collectValueFormat",
    options: []
  },
  {
    type: "input",
    label: "备注",
    prop: "remark"
  }
]

/* ======================== 医保结算元数据 ======================== */
export const MedicalInsuranceMetaColumns: Array<TableColumnItem> = [
  { prop: "checked", type: "selection", width: 50, must: true },
  { prop: "collectItem", label: "字段名称", minWidth: 100, must: true },
  { prop: "collectFieldName", minWidth: 120, label: "字段标识" },
  { prop: "valueTypeDesc", minWidth: 100, label: "数据类型" },
  { prop: "widgetTypeDesc", minWidth: 100, label: "控件类型" },
  // { prop: "collectLength", minWidth: 100, label: "长度" },
  // { prop: "requiredDesc", label: "是否必填", minWidth: 120, sortable: true },
  { prop: "valueRangeKey", label: "取值范围", minWidth: 120, sortable: true },
  { prop: "remark", label: "备注", minWidth: 180, sortable: true },
  // { prop: "status", label: "是否启用", minWidth: 100 },
  { prop: "operation", label: "操作", width: 200, fixed: "right" }
]

export const standardFormConfig: Array<DialogFormConfigItem> = [
  {
    type: "input",
    label: "分类名",
    prop: "fileName",
    rules: [{ required: true, message: "请输入分类名称", trigger: "blur" }]
  }
]

export const widgetTypeOptions: Array<Record<string, any>> = [
  { key: "1", label: "文本", value: "STRING" },
  { key: "2", label: "数字", value: "NUMBER" },
  { key: "3", label: "日期", value: "DATE" },
  { key: "4", label: "电话", value: "PHONE" },
  { key: "5", label: "下拉单选", value: "SINGLE_CHOICES" },
  { key: "6", label: "下拉多选", value: "MULTIPLE_CHOICES" },
  { key: "7", label: "地址", value: "ADDRESS" }
]
export const valueTypeOptions: Array<Record<string, any>> = [
  { value: "STRING", label: "字符" },
  { value: "NUMBER", label: "数字" },
  { value: "DATE", label: "日期" },
  { value: "DATE_TIME", label: "日期时间" },
  { value: "COLLECTIONS", label: "集合" }
]
export const collectSourceTypeOptions: Array<Record<string, any>> = [
  { key: "0", value: "METADATA", label: "元数据取值" },
  { key: "1", value: "INTERFACE", label: "接口取值" }
]

export const medicalMetaFormConfig: Array<Record<string, any>> = [
  {
    type: "input",
    label: "字段标识",
    prop: "name",
    rules: [{ required: true, message: "请输入字段名", trigger: "blur" }]
  },
  {
    type: "input",
    label: "字段名称",
    prop: "title",
    rules: [{ required: true, message: "请输入数据项", trigger: "blur" }]
  },
  {
    type: "select",
    label: "数据类型",
    prop: "valueType",
    options: [
      { value: "STRING", label: "字符" },
      { value: "NUMBER", label: "数字" },
      { value: "DATE", label: "日期" },
      { value: "DATE_TIME", label: "日期时间" },
      { value: "COLLECTIONS", label: "集合" }
    ],
    rules: [{ required: true, message: "请选择数据类型", trigger: "blur" }]
  },
  {
    type: "select",
    label: "控件类型",
    prop: "widgetType",
    options: widgetTypeOptions,
    rules: [{ required: false, message: "请选择控件类型", trigger: "blur" }]
  },
  {
    type: "select",
    label: "取值来源",
    prop: "collectSourceType",
    options: [
      { value: "METADATA", label: "元数据取值" },
      { value: "INTERFACE", label: "接口取值" }
    ],
    rules: [{ required: true, message: "请选择取值来源", trigger: "blur" }]
  },
  {
    type: "",
    label: "接口地址",
    prop: "collectValueSource",
    rules: [{ required: false, message: "请输入接口地址", trigger: "blur" }]
  },
  {
    type: "number",
    label: "长度",
    prop: "length",
    rules: [{ type: "number", max: 99, min: 1, message: "字段长度应在1到99之间" }]
  },
  {
    type: "select-api",
    label: "字段对照",
    prop: "refFirstPageName",
    options: [],
    rules: [{ required: false, message: "请选择", trigger: "blur" }]
  },
  {
    type: "select",
    label: "取值范围",
    prop: "valueRangeKey",
    options: [],
    rules: [{ required: true, message: "请选择", trigger: "blur" }]
  },
  {
    type: "input",
    label: "备注",
    prop: "remark",
    rules: [{ required: true, message: "请输入备注", trigger: "blur" }]
  }
]

export const nameValidator = (_rule: any, value: string, callback: (err?: Error) => void) => {
  if (!value) callback()
  // else if (/^[\u3400-\u4dbf\u4e00-\u9fffA-Za-z0-9\-_.!@#$%^&*()，。！？、【】；《》：“”]{1,50}$/.test(value)) callback()
  else if (value.length <= 50) callback()
  else callback(new Error(" 限制50字符"))
}

export const remarkValidator = (_rule: any, value: string, callback: (err?: Error) => void) => {
  console.log(`output->value`, value)
  if (!value) callback()
  else if (value.length <= 200) callback()
  else callback(new Error(" 限制200字符"))
}

// // 院区名称校验
// export const titleValidator = (_rule: any, value: string, callback: (err?: Error) => void) => {
//   if (!value) callback()
//   else if (value.length <= 50) callback()
//   else callback(new Error("不能超过50个字符"))
// }

export const formRules: FormRules = {
  name: [{ required: true, message: "请输入字段标识", trigger: "blur" }],
  title: [{ required: true, message: "请输入字段名称", trigger: "blur" }, { validator: nameValidator }],
  parentId: [{ required: true, message: "请选择字段分类", trigger: "blur" }],
  valueType: [{ required: true, message: "请选择数据类型", trigger: "blur" }],
  widgetType: [{ required: true, message: "请选择控件类型", trigger: "blur" }],
  // collectSourceType: [{ required: false, message: "请选择取值来源", trigger: "blur" }],
  // length: [{ required: true, type: "number", max: 99, min: 1, message: "字段长度应在1到99之间" }],
  refFirstPageName: [{ required: true, message: "请选择对照字段", trigger: "blur" }],
  // valueRangeKey: [{ required: true, message: "请选择", trigger: "blur" }],
  remark: [{ required: false, message: "请输入备注", trigger: "blur" }, { validator: remarkValidator }]
}

export const transWidgetType = (value: any) => {
  return widgetTypeOptions.find(item => item.key === value || item.value === value)?.label
}
