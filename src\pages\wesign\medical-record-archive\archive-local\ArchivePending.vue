<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchForm
        :form-config="archivePendingSearchFormConfig"
        :form-state="searchFormState"
        @query-btn-click="handleQuery"
        @reset-btn-click="handleReset"
      />
    </template>

    <template #table>
      <CommonTable
        ref="waitingArchivedTableRef"
        table-id="waitingArchiveTableIdent"
        :table-columns="archivePendingTableColumns"
        :request-api="getWaitingArchiveListApi"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <template #archiveStatus="{ row }">
          <el-tag v-if="row?.archiveStatusEnum" :type="getArchiveStatus(row.archiveStatusEnum)?.type">
            {{ getArchiveStatus(row.archiveStatusEnum)?.label }}
          </el-tag>
          <span v-else>--</span>
        </template>
        <template #operation="{ row }">
          <TableButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Archive)"
            :disabled="
              loading ||
              row.archiveStatusEnum === ArchiveStatusEnum.ARCHIVING ||
              row.archiveStatusEnum === ArchiveStatusEnum.ARCHIVE_SUCCESS
            "
            :tooltip="
              row.archiveStatusEnum === ArchiveStatusEnum.ARCHIVING
                ? '此病案正在归档，无法再次归档'
                : '此病案已经归档，无法再次归档'
            "
            @click="archiveItem(row)"
          >
            <span>归档审批</span>
          </TableButton>
          <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.View)" @click="toArchiveDetail(row)">
            查看
          </TableButton>
        </template>
      </CommonTable>
    </template>

    <SealDialog ref="sealDialogRef" :selected-row="medicalRecord" :confirm-callback="handleOperation"></SealDialog>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import { useRouter } from "vue-router"
import { PageContainer, CommonTable, TableButton } from "@/base-components"
import { SearchForm, TabsRouter, SealDialog } from "@/page-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { useUserStore } from "@/stores"
import { formatDatetime, encryptStr } from "@/utils"
import {
  archivePendingTableColumns,
  tabsRouterList,
  menuId,
  getArchiveStatus,
  archivePendingSearchFormConfig,
  ArchiveStatusEnum
} from "./config"
import { getWaitingArchiveListApi } from "./interface"

const router = useRouter()

const { hasOperationPermission } = useUserStore()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  patientFilterProp: "",
  patientFilterValue: "",
  outHospitalDeptWsid: "",
  outHospitalDatetime: "",
  visitIdSymbol: "",
  visitIdCount: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/* ======================== 表格相关方法 ======================== */

const waitingArchivedTableRef = ref<InstanceType<typeof CommonTable>>()

function dataCallback(data: Array<Record<string, any>>) {
  return data.map(item => ({
    ...item,
    inHospitalDatetime: formatDatetime(item.inHospitalDatetime),
    outHospitalDatetime: formatDatetime(item.outHospitalDatetime),
    archiveDatetime: formatDatetime(item.archiveDatetime),
    ifOverdue: item.ifOverdue === "ENABLE" ? "是" : "否"
  }))
}

// 操作类型,区分查看和归档审批
const operation = ref<"archiveApproval" | "approvalDetail" | null>(null)

// 防止多次点击归档按钮
const loading = ref(false)
// 病案归档
const archiveItem = row => {
  handleRowClick(row)
  console.log(row)
  operation.value = "archiveApproval"
}

/* ======================== 检查封存 ======================== */

const medicalRecord = ref<Record<string, any> | null>(null) // 用户点击提交的病案信息
const sealDialogRef = ref() // SealDialog组件ref

// 检查是否封存
const handleRowClick = (row: any) => {
  medicalRecord.value = row
  sealDialogRef.value.checkSealed()
}

// 查看详情
const toArchiveDetail = row => {
  handleRowClick(row)
  operation.value = "approvalDetail"
}

// 查看详情
const handleOperation = () => {
  console.log(medicalRecord.value)
  if (operation.value === "archiveApproval") {
    const query = {
      inpNo: medicalRecord.value?.inpNo,
      wsid: medicalRecord.value?.wsid,
      isControl: "false",
      urlType: "FINAL_QC"
    }
    if (sealDialogRef.value.secretKey) {
      query["secretKey"] = encryptStr(sealDialogRef.value.secretKey)
    }
    router.push({ path: "/archive/local/approval-view", query })
  } else {
    router.push({ path: "/archive/local/view", query: { inpNo: medicalRecord.value?.inpNo } })
  }
}
</script>
