<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />

      <SearchForm
        :form-config="submissionSearchFormConfig"
        :form-state="searchFormState"
        @query-btn-click="handleQuery"
        @reset-btn-click="handleReset"
      />
    </template>

    <template #table>
      <CommonTable
        ref="commonTableRef"
        table-id="medicalRecordSubmissionTable"
        :table-columns="submissionColumns"
        :request-api="getSubmissionListApi"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <template #submitStatusEnum="{ row }">
          <el-tag :type="row.statusTagType">{{ getStatusLabel(row.submitStatusEnum) }}</el-tag>
        </template>
        <template #operation="{ row }">
          <TableButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Submit)"
            :disabled="row.submitStatusEnum !== 'NOT_SUBMIT' && row.submitStatusEnum !== 'SUBMIT_FAIL'"
            tooltip="该病案正在提交中"
            @click="confirmSubmit(row)"
          >
            查看
          </TableButton>
        </template>
      </CommonTable>
    </template>

    <!-- 检查封存 -->
    <SealDialog ref="sealDialogRef" :selected-row="medicalRecord" :confirm-callback="toSubmit" />

    <!-- 提交进度 -->
    <SubmitProgressDialog ref="progressDialogRef" :inp-no="medicalRecord?.inpNo" />
  </PageContainer>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue"
import { useRouter } from "vue-router"
import { CommonTable, TableButton, PageContainer } from "@/base-components"
import { SealDialog, SearchForm, TabsRouter } from "@/page-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { useUserStore, useSystemStore } from "@/stores"
import { encryptStr, formatDatetime, SystemAlert, toastError } from "@/utils"
import SubmitProgressDialog from "../components/SubmitProgressDialog.vue"
import { getSubmitProgressApi } from "../interface"
import { submissionSearchFormConfig, submissionColumns, SUBMISSION_STATUS_MAP, tabsRouterList } from "./config"
import { getSubmissionListApi, getSubmitConfigApi } from "./interface"

const systemStore = useSystemStore()
const router = useRouter()

const menuId = "/medical-record/submission"

const { hasOperationPermission } = useUserStore()

/* ======================== 搜索 ======================== */

const searchFormState = reactive({
  patientFilterProp: "patientName",
  patientFilterValue: "",
  outHospitalDatetime: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState, "submitStatus!=SUBMIT_SUCCESS", {
  patientFilterProp: "patientName"
})

/* ======================== 表格操作 ======================== */

const commonTableRef = ref<InstanceType<typeof CommonTable>>()

function dataCallback(data: Array<Record<string, any>>) {
  return data.map(item => ({
    ...item,
    inHospitalDatetime: formatDatetime(item.inHospitalDatetime),
    outHospitalDatetime: formatDatetime(item.outHospitalDatetime),
    submitterTime: formatDatetime(item.submitterTime),
    statusTagType: SUBMISSION_STATUS_MAP.get(item.submitStatusEnum)?.tag
  }))
}

// 获取状态文案
const getStatusLabel = statusEnum => {
  return SUBMISSION_STATUS_MAP.get(statusEnum)?.label
}

/* ======================== 检查封存 ======================== */

const sealDialogRef = ref<InstanceType<typeof SealDialog>>() // SealDialog组件ref

// 检查是否封存
const confirmSubmit = row => {
  medicalRecord.value = row
  sealDialogRef.value?.checkSealed()
}

/* ======================== 提交 ======================== */

const medicalRecord = ref<Record<string, any> | null>(null) // 用户点击提交的病案信息
const progressDialogRef = ref<InstanceType<typeof SubmitProgressDialog>>()

let query = "" // 跳转到提交详情的参数

// 生成跳转参数
const generateQuery = (deletionSubmit: "YES" | "NO") => {
  const _query = `inpNo=${medicalRecord.value?.inpNo}&patientName=${medicalRecord.value?.patientName}&lastSyncTime=${medicalRecord.value?.syncDatetime}&allowMissing=${deletionSubmit}`
  if (sealDialogRef.value?.secretKey) {
    query + `&secretKey=${encryptStr(sealDialogRef.value.secretKey)}`
  }
  query = _query
}

// 检查病案是否提交
const toSubmit = async () => {
  try {
    systemStore.showLoading()
    const { needSign, deletionSubmit } = (await getSubmitConfigApi(medicalRecord.value?.wsid)).data.data
    console.log(needSign, deletionSubmit)
    generateQuery(deletionSubmit)
    systemStore.hideLoading()
    // 根据配置跳转不同的页面 - 是否需要签名
    if (needSign === "YES") {
      checkSubmissionProgress()
    } else {
      query += "&needSign=NO"
      router.push(`/medical-record/submit-sign?${query}`)
    }
  } catch (error) {
    console.log(error)
    systemStore.hideLoading()
  }
}

// 检查病案是否提交
const checkSubmissionProgress = () => {
  getSubmitProgressApi(medicalRecord.value?.inpNo)
    .then(res => {
      const { completeFlag } = res.data.data
      // completeFlag 1未提交；2提交中；3提交完成

      // 提交成功
      if (completeFlag === 2) {
        SystemAlert("该病案已提交成功，无需再次提交")
      }
      // 已经提交但在提交中
      else if (completeFlag === 1) {
        progressDialogRef.value?.init(res.data.data.submitCount, res.data.data.total)
      }
      // 正常提交流程
      else {
        query += "&needSign=YES"
        router.push(`/medical-record/submit-sign?${query}`)
      }
    })
    .catch(error => {
      toastError(error)
    })
}
</script>
