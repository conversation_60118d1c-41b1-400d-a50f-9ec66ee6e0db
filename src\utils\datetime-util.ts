import moment from "moment"

// 格式化完整时间
export const formatDatetime = (datetime: number | Date, format?: string) => {
  if (datetime) return moment(datetime).format(format || "YYYY-MM-DD HH:mm:ss")
  else return "--"
}

// 格式化日期
export const formatDate = (datetime: number | Date) => {
  if (datetime) return moment(Number(datetime)).format("YYYY-MM-DD HH:mm:ss")
  else return "--"
}

// 返回距离现在时间的时间差，返回的时间差为附带单位的字符串
export function getTimeDifference(time) {
  const nowTime = new Date()
  const prevTime = new Date(time)
  const timestamp = nowTime.getTime() - prevTime.getTime()
  const diffSecond = timestamp / 1000
  const diffMinute = diffSecond / 60
  const diffHour = diffMinute / 60
  const diffDay = diffHour / 24
  if (diffSecond < 61) return `${Math.floor(diffSecond)}秒前`
  if (diffMinute < 61) return `${Math.floor(diffMinute)}分钟前`
  if (diffHour < 25) return `${Math.floor(diffHour)}小时前`
  if (diffDay < 31) return `${Math.floor(diffDay)}天前`
  return time
  // return `${prevTime.getFullYear()}-${prevTime.getMonth() + 1}-${prevTime.getDate()}`
}
