import { useRoute } from "vue-router"
import { AxiosRequestConfig } from "axios"
import CryptoJs from "crypto-js"
import { isNumber } from "lodash"
import SparkMD5 from "spark-md5"
import { FormItemConfig, FormItemType, DateFormatEnum } from "@/configs"
import { searchMetaCodeTableFieldApi } from "@/interfaces"
import axios from "@/interfaces/axios-instance"
export * from "./datetime-util"
export * from "./message-tool"
export * from "./form-validator"
export * from "./tree-util"
export * from "./file-util"
export * from "./quality-control-util"
export * from "./flow-util"

// 判断数据是否有children
export const hasChildren = (data: Record<string, any>) => {
  return data.children && data.children.length > 0
}

/* ================================  加密解密 =================================== */

const key = CryptoJs.enc.Utf8.parse("1234567887654321") //十六位十六进制数作为密钥
const iv = CryptoJs.enc.Utf8.parse("8765432112345678") //十六位十六进制数作为密钥偏移量

// 加密字符串
export function encryptStr(str: string) {
  const src = CryptoJs.enc.Utf8.parse(str)
  const encrypted = CryptoJs.AES.encrypt(src, key, { iv: iv, mode: CryptoJs.mode.CBC, padding: CryptoJs.pad.Pkcs7 })
  return encrypted.ciphertext.toString().toUpperCase()
}

// 解密字符串
export function decryptStr(str: string) {
  const encryptedHexStr = CryptoJs.enc.Hex.parse(str)
  const src = CryptoJs.enc.Base64.stringify(encryptedHexStr)
  const decrypt = CryptoJs.AES.decrypt(src, key, { iv: iv, mode: CryptoJs.mode.CBC, padding: CryptoJs.pad.Pkcs7 })
  const decryptedStr = decrypt.toString(CryptoJs.enc.Utf8)
  return decryptedStr.toString()
}

/* ================================ 无需session的axios请求 =================================== */

export function sessionLessAxios(config: AxiosRequestConfig) {
  const route = useRoute()
  const jobId = route?.query?.jobId as string

  const timeStamp = new Date().getTime()
  const requestData = objectToQueryString({ ...config.data })

  return axios({
    baseURL: config.url,
    method: config.method,
    timeout: config.timeout || 20000,
    params: config.params,
    data: config?.data,
    responseType: config?.responseType,
    headers: {
      "X-Requested-Session": "sessionless",
      timeStamp: timeStamp,
      params: JSON.stringify(requestData),
      signature: SparkMD5.hash(`jobId=${jobId}&params=${JSON.stringify(requestData)}&timeStamp=${timeStamp}`),
      jobId: jobId
    }
  })
}

// 动态生成正则表达式和错误提示语
export function getCustomRule(config: FormItemConfig) {
  let reg = ""
  let ruleMessage = ""
  // 献血条码格式校验，长度为13的数字
  if (config.type === FormItemType.BloodDonationBarcode) {
    reg = "\\d{13}"
    ruleMessage = "长度为13位的数字"
  }
  // 日期格式校验
  if (config.type === FormItemType.BaseDate && config.dateFormat) {
    if (config.dateFormat === DateFormatEnum.Year) {
      reg += `\\d{4}`
      ruleMessage = "日期格式为YYYY"
    } else if (config.dateFormat === DateFormatEnum.Month) {
      reg += `\\d{4}-\\d{2}`
      ruleMessage = "日期格式为YYYY-MM"
    } else if (config.dateFormat === DateFormatEnum.Date) {
      reg += `\\d{4}-\\d{2}-\\d{2}`
      ruleMessage = "日期格式为YYYY-MM-DD"
    } else if (config.dateFormat === DateFormatEnum.Minutes) {
      reg += `\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}`
      ruleMessage = "日期格式为YYYY-MM-DD mm:hh"
    } else if (config.dateFormat === DateFormatEnum.Seconds) {
      reg += `\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}`
      ruleMessage = "日期格式为YYYY-MM-DD mm:hh:ss"
    }
  }

  // 长度
  if ([FormItemType.BaseInput, FormItemType.BaseTextarea].includes(config.type)) {
    // 特殊字符检查

    if (config.minLength || config.maxLength) {
      reg = `.{${config.minLength ? config.minLength : "0"},${config.maxLength ? config.maxLength : "1000"}}`
      ruleMessage = `长度为${config.minLength ? config.minLength : "0"}-${config.maxLength ? config.maxLength : "0"}位`
    }
  }

  // 精度设置
  if (config.type === FormItemType.BaseNumber) {
    if (config.precision !== undefined) {
      if (config.precision === 0) {
        reg = `(\\-|\\+)?[0-9]{1,9}`
        ruleMessage = `应为整数`
      } else {
        reg = `(\\-|\\+)?[0-9]{1,9}(\\.\\d{0,${config.precision}})?`
        ruleMessage = `精确到小数点后${config.precision}位`
      }
    }
  }

  // 值域判断
  if (config.options) {
    const tempOptionsValue = config.options.map(item => item.value)
    reg = `(${tempOptionsValue.join("|")})(,(${tempOptionsValue.join("|")}))*`
    ruleMessage = "该值不在规定的值域中"
  }

  return {
    rule: reg,
    ruleMessage: ruleMessage
  }
}

export const fixedNumberWithCommas = (number, options: any = {}) => {
  const { digit = 2, defaultDisplay = "--" } = options
  if (isNumber(number) && !isNaN(number)) {
    const parts = number.toFixed(digit).toString().split(".")
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",")
    return parts.join(".")
  }
  return defaultDisplay
}

export function getDateType(format) {
  switch (format) {
    case DateFormatEnum.Year:
      return "year"
    case DateFormatEnum.Month:
      return "month"
    case DateFormatEnum.Date:
      return "date"
    case DateFormatEnum.Seconds:
    case DateFormatEnum.Minutes:
      return "datetime"
    default:
      return "datetime"
  }
}

// 字体大小，默认/大/小
export const fontSizeOptions = [
  { label: "默认", value: "default", size: 18 },
  { label: "大", value: "large", size: 24 },
  { label: "小", value: "small", size: 16 }
]

export const textAlignContentTypes = [
  {
    value: "ALIGN_LEFT",
    label: "水平对齐，左对齐",
    icon: "ri-align-left",
    style: "left"
  },
  {
    value: "ALIGN_CENTER",
    label: "水平对齐，居中对齐",
    icon: "ri-align-center",
    style: "center"
  },
  {
    value: "ALIGN_RIGHT",
    label: "水平对齐，右对齐",
    icon: "ri-align-right",
    style: "right"
  },
  {
    value: "ALIGN_JUSTIFIED",
    label: "水平对齐，自适应",
    icon: "ri-align-justify",
    style: "justify"
  }
]

//表单样式
export const getFormCustomStyle = () => {
  // 标题控件样式
  const groupHeadlineStyle = (config: FormItemConfig) => {
    const fontSizeType = fontSizeOptions.find(item => item.value === config.size) as Record<string, any>
    const textAlignType = textAlignContentTypes.find(item => item.value === config.align) as Record<string, any>
    return {
      color: config.fontColor,
      fontSize: `${fontSizeType.size}px`,
      textAlign: textAlignType.style
    }
  }

  // 控件值和标题在一行
  const formContentClass = (config: FormItemConfig) => {
    if (config.titleAndOptionsInRow) {
      return "form-content-row"
    }
    return ""
  }
  return {
    groupHeadlineStyle,
    formContentClass
  }
}

// 获取表单的自定义字段值(表单值转后端形式)
export function getCustomFormValue(value, config: FormItemConfig) {
  if (config.type === FormItemType.BaseCheckbox || (config.type === FormItemType.BaseSelect && config.multiple)) {
    return { value: Array.isArray(value) ? value?.join(",") : value }
  }
  return { value: value }
}

// 动态校验
export function checkRuleWithDefaultValue(value, config: FormItemConfig) {
  value = getCustomFormValue(value, config).value

  // if (!value && config.required) return "该项为必填值"
  // else if (!value && !config.required) return ""
  if (!value) return ""

  // 日期格式不用校验规则
  if ([FormItemType.BaseDate].includes(config.type)) return ""

  // 最大值校验
  if ((config.max || config.max === 0) && value > config.max) {
    return `该值的最大值为${config.max}`
  }
  // 最小值校验
  if ((config.min || config.min === 0) && value < config.min) {
    return `该值的最小值为${config.min}`
  }

  // 存在校验规则
  if (config.rule) {
    const reg = new RegExp(`^${config.rule}$`)
    // 存入config.error，提交的时候检查是否全部校验通过
    if (reg.test(value)) {
      return ""
    } else {
      return config.ruleMessage
    }
  }

  return ""
}

// 数据规范tree-select 结构数据
export const formatTreeInfo = (tree: Array<Record<string, any>>) => {
  tree.forEach(item => {
    item.disabled = false
    item.label = item.fileName
    item.value = item.code || item.fileName
    if (!item.code) item.disabled = true
    if (item.children.length) {
      return formatTreeInfo(item.children)
    }
  })
  return tree
}

// 上下页pdf直接的间隙
export const PDF_PAGE_PAGE_HEIGHT = 30

export const getOptions = async (config: Record<string, any>, val: string) => {
  if (config.valueRangeKey) {
    return (
      await searchMetaCodeTableFieldApi({
        offset: 0,
        limit: 1000,
        filters: `code=${config.valueRangeKey}`
      })
    )?.data?.data?.rows.map(item => {
      return {
        label: item.value,
        value: item.key
      }
    })
  } else {
    const res = await axios({
      url: `api${config.collectValueSource}&item=${val}`
    })
    return res?.data?.data?.map(item => {
      return {
        label: item.name || item.realName || item.value,
        value: item.code || item.wsid || item.key || item.jobId,
        greyCode: item?.greyCode
      }
    })
  }
}

export function createSSEStream(url) {
  return new ReadableStream({
    start(controller) {
      const eventSource = new EventSource(url)
      eventSource.addEventListener("message", event => {
        console.log(`output->event`, event)
        controller.enqueue({ data: event.data, event: "message" })
      })
      eventSource.addEventListener("update", event => {
        controller.enqueue({ data: event.data, event: "update" })
      })
      eventSource.addEventListener("ping", event => {
        controller.enqueue({ data: event.data, event: "ping" })
      })
      eventSource.addEventListener("close", event => {
        controller.enqueue({ data: event.data, event: "close" })
        eventSource.close()
        controller.close()
        // 关闭流
      })
      eventSource.onerror = err => {
        console.error("SSE Error:", err)
        eventSource.close()
        controller.error(err) // 推送错误
      }
    }
  })
}

// 将对象转换为按照ascii码排序的查询字符串
const objectToQueryString = (obj: Record<string, any>) => {
  const sortedKeys = Object.keys(obj).sort()
  const queryParams = sortedKeys.map(key => {
    return `${encodeURIComponent(key)}=${encodeURIComponent(obj[key])}`
  })
  return queryParams.join("&")
}
