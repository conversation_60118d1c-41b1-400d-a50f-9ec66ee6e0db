<template>
  <el-tooltip :disabled="!props.desc" :content="props.desc" placement="top" :open-delay="200">
    <div :class="{ 'is-disabled': $props.disabled }" class="icon-wrapper" @click="click">
      <i :class="{ [props.icon]: true }" :style="getIconSize()"></i>
      <div class="icon-name">{{ $props.name }}</div>
      <i class="ri-prohibited-line prohibit-icon"></i>
    </div>
  </el-tooltip>
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    icon: string
    name: string
    size?: "small" | "default" | "medium" | "large"
    disabled?: boolean
    desc?: string
  }>(),
  {
    icon: "",
    name: "",
    size: "default",
    disabled: false,
    desc: ""
  }
)

const emits = defineEmits(["click"])

const click = () => {
  if (props.disabled) return
  emits("click")
}

const getIconSize = () => {
  switch (props.size) {
    case "small":
      return {
        fontSize: "12px"
      }
    case "default":
      return {
        fontSize: "14px"
      }
    case "medium":
      return {
        fontSize: "18px"
      }
    case "large":
      return {
        fontSize: "24px"
      }
    default:
      return {
        fontSize: "14px"
      }
  }
}
</script>

<style lang="less" scoped>
.icon-wrapper {
  border-radius: 4px;
  color: #717579;
  font-size: 14px;
  cursor: pointer;
  padding: 6px 10px;
  position: relative;
  text-align: center;
  .icon-name {
    margin-top: 4px;
  }
  &:hover {
    background-color: #edf0f2;
    border-radius: 4px;
  }
  .prohibit-icon {
    position: absolute;
    top: calc(50% - 8px);
    right: calc(50% - 8px);
    color: red;
    display: none;
  }
}
.is-disabled {
  cursor: not-allowed;
  // 添加遮罩
  &:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 4px;
  }
  .prohibit-icon {
    display: block;
  }
}
</style>
