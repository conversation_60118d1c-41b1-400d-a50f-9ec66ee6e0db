<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />

      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <CommonInputFormItem v-model="searchFormState.name" label="接口名称" />
        <CommonSelectFormItem v-model="searchFormState.dataType" label="数据类型" :options="dataTypeOptions" />
        <CommonSelectFormItem v-model="searchFormState.method" label="请求方式" :options="methodTypeOptions" />
        <CommonSelectFormItem v-model="searchFormState.status" label="状态" :options="rowStatusOptions" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="tableRef"
        :table-columns="apiTableColumns"
        :request-api="getInterfaceHistoryApi"
        :request-params="searchParams"
      >
        <template #header>
          <AddButton @click="showAddForm">新增</AddButton>
        </template>
        <template #dataSourceWsid="{ row }">
          {{ dataSourceTypeOptions.find(option => option.value === row.dataSourceWsid)?.label || "--" }}
        </template>
        <template #dataType="{ row }">
          {{ getDataTypeDesc(row.dataType) }}
        </template>
        <template #mrClassCode="{ row }">
          {{ mrClassOptions.find(option => option.value === row.mrClassCode)?.label || "--" }}
        </template>
        <template #extractType="{ row }">
          {{ getExtractTypeDesc(row.extractType) }}
        </template>
        <template #operation="{ row }">
          <TableButton @click="showEditForm(row)">编辑</TableButton>
          <TableButton @click="confirmToggle(row, row.wsid, row.status === 'ENABLE' ? 'DISABLE' : 'ENABLE')">
            {{ row.status === "ENABLE" ? "禁用" : "启用" }}
          </TableButton>
          <TableButton v-if="row.status === 'ENABLE'" @click="triggerJob(row)">执行一次</TableButton>
          <TableButton @click="confirmDelete(row, row.wsid)">删除</TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>

  <DialogContainer
    v-model:visible="visible"
    :title="actionType === 'edit' ? '修改接口配置' : '添加接口配置'"
    :width="800"
    class="api-form-dialog"
  >
    <el-form
      ref="formRef"
      label-position="right"
      label-width="130px"
      label-suffix="："
      :model="formState"
      :rules="apiFormRules"
    >
      <CommonInputFormItem v-model="formState.name" label="接口名称" prop="name" placeholder="请输入接口名称" />

      <CommonSelectFormItem
        v-model="formState.systemWsid"
        label="厂商系统"
        prop="systemWsid"
        :options="systemVendorOptions"
      />

      <CommonSelectFormItem v-model="formState.dataType" label="数据类型" prop="dataType" :options="dataTypeOptions" />

      <CommonSelectFormItem
        v-if="formState.dataType === 'MR_CONTENT'"
        v-model="formState.mrClassCode"
        label="文书分类"
        prop="mrClassCode"
        placeholder="请选择文书分类"
        :options="mrClassOptions"
      />

      <CommonInputFormItem
        v-model.trim="formState.authorization"
        label="认证信息"
        prop="authorization"
        placeholder="请输入认证信息"
      />

      <CommonInputFormItem v-model="formState.url" label="URL" prop="url" placeholder="请输入URL" />

      <CommonSelectFormItem
        v-model="formState.method"
        label="请求方式"
        prop="method"
        placeholder="请选择请求方式"
        :options="methodTypeOptions"
      />

      <CommonInputFormItem
        v-model.trim="formState.queryParams"
        label="请求参数"
        prop="queryParams"
        placeholder="请输入请求参数"
      />

      <el-form-item v-if="formState.method === 'POST'" label="请求体" prop="bodyParams">
        <el-input v-model.trim="formState.bodyParams" placeholder="请输入请求体" type="textarea" :rows="5" />
      </el-form-item>

      <CommonSelectFormItem
        v-model="formState.contentType"
        label="content-type"
        prop="contentType"
        placeholder="请选择content-type"
        :options="resultTypeOptions"
      />

      <CommonSelectFormItem
        v-model="formState.resultType"
        label="响应体"
        prop="resultType"
        placeholder="请选择响应体类型"
        :options="resultTypeOptions"
      />

      <CommonInputFormItem
        v-model.trim="formState.resultField"
        label="返回数据取值"
        prop="resultField"
        placeholder="请输入返回数据取值"
      />

      <el-form-item ref="largeScreen" label="数据清洗" prop="sourceCode" class="data-clean">
        <Codemirror
          v-model="formState.sourceCode"
          placeholder="请输入"
          :tab-size="10"
          :extensions="dataExtensions"
          :style="{ maxHeight: '150px', width: '100%' }"
        />
        <div ref="fullScreen" class="fullscreen-btn">
          <i v-if="!isFullscreen" title="全屏显示" class="ri-fullscreen-line" @click="toggle"></i>
          <i v-else title="退出全屏" class="ri-fullscreen-exit-line" @click="toggle"></i>
        </div>
      </el-form-item>

      <CommonRadioFormItem
        v-model="formState.resultDataType"
        :options="resultDataTypeOptions"
        prop="resultDataType"
        label="返回数据类型"
        placeholder="请选择返回数据类型"
      />

      <CommonSelectFormItem
        v-model="formState.charset"
        label="编码格式"
        prop="charset"
        placeholder="请选择编码格式"
        :options="charsetOptions"
      />

      <CommonRadioFormItem
        v-model="formState.extractType"
        :options="extractTypeOptions"
        prop="extractType"
        label="采集模式"
        placeholder="请选择采集模式"
      />

      <template v-if="formState.extractType === 'INCREMENT'">
        <CommonInputFormItem
          v-model="formState.incrementFieldKey"
          label="自增key"
          prop="incrementFieldKey"
          placeholder="请输入自增key"
        />

        <CommonRadioFormItem
          v-model="formState.incrementFieldType"
          label="自增字段类型"
          prop="incrementFieldType"
          :options="incrementTypeOptions"
          @change="() => (formState.incrementLastValue = '')"
        />

        <CommonInputFormItem
          v-if="formState.incrementFieldType === 'NUMBER'"
          v-model="formState.incrementLastValue"
          label="最近自增值"
          prop="incrementLastValue"
          placeholder="请输入最近自增值"
        />

        <el-form-item v-if="formState.incrementFieldType === 'TIME'" label="最近自增值" prop="incrementLastValue">
          <el-date-picker
            v-model="formState.incrementLastValue"
            placeholder="请选择最近自增时间"
            type="datetime"
            value-format="x"
          />
        </el-form-item>

        <CommonInputFormItem
          v-model="formState.incrementDelta"
          label="增幅"
          prop="incrementDelta"
          placeholder="请输入增幅"
        />
      </template>

      <el-form-item label="采集频率(cron)" prop="cron">
        <el-input v-model="formState.cron" placeholder="0 0/10 * * * ?" />
      </el-form-item>

      <el-form-item label="超时时间" prop="timeout">
        <el-input v-model.trim="formState.timeout" maxlength="5" type="string" placeholder="请输入超时时间">
          <template #append>秒</template>
        </el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :loading="actionLoading" @click="closeFormDialog()">取消</el-button>
      <el-button :loading="actionLoading" type="primary" @click="handleSave">保存</el-button>
    </template>
  </DialogContainer>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import { java } from "@codemirror/lang-java"
import { useFullscreen } from "@vueuse/core"
import { Codemirror } from "vue-codemirror"
import {
  PageContainer,
  DialogContainer,
  CommonTable,
  AddButton,
  TableButton,
  CommonSelectFormItem,
  CommonInputFormItem,
  CommonRadioFormItem,
  SearchContainer
} from "@/base-components"
import { TabsRouter } from "@/page-components"
import { rowStatusOptions } from "@/configs"
import { useTableSearch, useCommonOptions } from "@/hooks"
import useFormSetting from "@/hooks/useFormSetting_v2"
import useTableOperation from "@/hooks/useTableOperation_v2"
import { getMrClassList } from "@/interfaces"
import { SystemPrompt, Message, toastError } from "@/utils"
import { getAllDataSources, getCollectSystemList, getFieldDicts, triggerJobApi } from "../collect-way/interface"
import {
  tabsRouterList,
  dataTypeOptions,
  myTheme,
  extractTypeOptions,
  getDataTypeDesc,
  getExtractTypeDesc,
  resultDataTypeOptions,
  apiFormRules,
  resultTypeOptions,
  methodTypeOptions,
  apiTableColumns,
  incrementTypeOptions,
  charsetOptions
} from "./config"
import {
  addInterfaceHistoryApi,
  changeInterfaceHistoryStatusApi,
  deleteInterfaceHistoryApi,
  getInterfaceHistoryApi,
  updateInterfaceHistoryApi
} from "./interface"
import type { FormInstance } from "element-plus"

/* ======================== 页面加载时获取选项 ======================== */

// 获取所有厂商系统选项
const { options: systemVendorOptions } = useCommonOptions({
  getOptionsApi: getCollectSystemList,
  labelAlias: "systemName",
  valueAlias: "wsid"
})

// 获取所有数据源选项
const { options: dataSourceTypeOptions } = useCommonOptions({
  getOptionsApi: getAllDataSources,
  labelAlias: "name",
  valueAlias: "wsid"
})

// 获取所有文书分类选项
const { options: mrClassOptions } = useCommonOptions({
  getOptionsApi: getMrClassList,
  labelAlias: "mrClassName",
  valueAlias: "mrClassCode"
})

/* ======================== 搜索 ======================== */

const tableRef = ref<InstanceType<typeof CommonTable>>()

const searchFormState = reactive({
  name: "",
  dataType: "",
  method: "",
  status: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/* ======================== 行操作 ======================== */

const showAddForm = () => {
  changeDataType()
  showForm("add")
}

const showEditForm = row => {
  changeDataType()
  showForm("edit", row)
}

// 执行一次
function triggerJob(row) {
  triggerJobApi(row.wsid)
    .then(res => {
      if (res.data?.message) {
        Message.success(res.data.message)
      }
    })
    .catch(err => toastError(err, "操作失败"))
}

/* ======================== 表格 ======================== */

const formRef = ref<FormInstance>()
const testLoading = ref(false)

const formInitialValue = {
  wsid: "",
  name: "", // 数据库名称
  systemWsid: "", // 厂商系统
  dataSourceWsid: "", // 数据源id
  dataType: "", // 数据类型
  mrClassCode: "", // 文书分类
  authorization: "", // 认证信息
  url: "", // URL
  method: "", // 请求方式
  queryParams: "", // 请求参数query
  bodyParams: "", // 请求参数body
  contentType: "JSON", // content-type
  resultType: "JSON", // 返回体类型
  resultField: "", // 返回数据取值
  resultDataType: "FILE", // 返回数据类型
  sourceCode: "", // 数据清洗
  charset: "",
  extractType: "ALL", // 采集模式
  cron: "", // 采集频率
  incrementFieldKey: "", // 自增key
  incrementFieldType: "TIME", // 自增key类型
  incrementLastValue: "", // 自增key值
  incrementDelta: "", // 自增步长
  timeout: "" // 超时时间
}

const { visible, actionType, formState, showForm, closeForm } = useFormSetting(formInitialValue)

const { confirmAdd, confirmEdit, confirmToggle, confirmDelete, actionLoading } = useTableOperation(formState, {
  apiGroup: {
    addApi: addInterfaceHistoryApi,
    editApi: updateInterfaceHistoryApi,
    toggleApi: changeInterfaceHistoryStatusApi,
    deleteApi: deleteInterfaceHistoryApi
  },
  closeForm: closeFormDialog,
  deleteConfirmation: row => SystemPrompt(`您确定要删除“${row?.name}”吗？`),
  toggleConfirmation: row => {
    const action = row?.status === "DISABLE" ? "启用" : "禁用"
    return SystemPrompt(`您确定要${action}“${row?.name}”吗？`)
  },
  callback: () => tableRef.value?.refreshTableData()
})

const handleSave = () => {
  formRef.value?.validate(valid => {
    console.log(valid)
    if (!valid) return
    actionType.value === "edit" ? confirmEdit() : confirmAdd()
  })
}

// 关闭编辑弹窗
function closeFormDialog() {
  closeForm()
  testLoading.value = false
}

/* ================= 数据类型 ==================== */

const dataTypeTableData = ref()
const dataTypeTableLoading = ref(false)

const changeDataType = () => {
  const type = formState.dataType || null
  dataTypeTableLoading.value = true
  getFieldDicts({
    filters: `type=${type}`
  })
    .then(res => {
      dataTypeTableData.value = res.data.data
      dataTypeTableLoading.value = false
    })
    .catch(() => {
      dataTypeTableLoading.value = false
    })
}

/* ======================== 代码块 ======================== */

// 数据清洗
const dataExtensions = [java(), myTheme]

const largeScreen = ref(null)
const fullScreen = ref(null)
const { toggle, isFullscreen } = useFullscreen(largeScreen)
</script>

<style lang="less" scoped>
:deep(.cm-editor) {
  border: 1px solid #dcdfe6;
}

:deep(.cm-focused) {
  outline: none !important;
}

:deep(.cm-lineNumbers) {
  border-right: 1px solid #dcdfe6;
}

.api-form-dialog {
  :deep(.el-dialog__body) {
    max-height: 75vh;
    overflow-y: auto;
  }
}

.data-clean {
  :deep(.el-form-item__content) {
    align-items: baseline;
    position: relative;
  }
}

.fullscreen-btn {
  position: absolute;
  cursor: pointer;
  right: 10px;
  top: 15px;
  line-height: 1;
  transform: translateY(-50%);
  i {
    opacity: 0.5;
    font-size: 20px;
    color: #333;
  }
  &:hover {
    i {
      opacity: 1;
    }
  }
}
</style>
