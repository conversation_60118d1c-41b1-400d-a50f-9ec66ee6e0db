import { reactive } from "vue"
import { cloneDeep } from "lodash-es"
import { useSystemStore } from "@/stores"

/**
 * v2版本的useTableSearch
 * 直接传入一个初始化的formData数据，生成一个reactive的表单对象用于绑定页面表单
 * 你可以直接使用内置的searchParams作为查询参数，也可以直接传入一个formatter函数改造searchParams和做页面上的其他操作
 * 如果需要传递除了filters以外的属性，传递queryInitialData即可
 * searchFormState和queryState用于你在页面上值的绑定
 */
const useTableSearch = <
  T extends Record<string, any>,
  Q extends Record<string, any>,
  M extends Record<string, any>
>(options: {
  filtersInitialData: T // 默认参数
  queryInitialData?: Q // query参数
  advanceData?: M // 高级查询参数或者其他需要储存但不放入查询条件数据
  formatter?: () => void // 格式化搜索参数，直接暴露searchParams到formatter函数修改
  defaultFilters?: string // 必须传递的filters
}) => {
  const { filtersInitialData, queryInitialData, advanceData, formatter, defaultFilters } = options

  const systemStore = useSystemStore()
  const pageParams = systemStore.pageParams.find(page => page.id === location.pathname)

  // 暴露给页面的表单值
  const searchFormState = reactive(cloneDeep(filtersInitialData))
  const queryState = reactive(cloneDeep(queryInitialData || ({} as Q)))
  const advanceState = reactive(cloneDeep(advanceData || ({} as M)))

  // 根据缓存数据初始化生成query参数
  if (pageParams?.queryState) {
    for (const key in pageParams.queryState) {
      queryState[key] = pageParams.queryState[key]
    }
  }
  if (pageParams?.searchFormState) {
    for (const key in pageParams.searchFormState) {
      searchFormState[key] = pageParams.searchFormState[key]
    }
  }

  // 暴露给页面的查询参数
  const searchParams = reactive<Record<string, any>>({
    filters: pageParams?.searchParams?.filters || generateFilters(),
    timestamp: new Date().getTime()
  })

  // 生成初始过滤参数
  function generateFilters() {
    const filterConditions: Array<string> = defaultFilters ? [defaultFilters] : []
    const complexFields = [
      "patientFilterProp",
      "patientFilterValue",
      "visitIdSymbol",
      "visitIdCount",
      "inHospitalDaysFrom",
      "inHospitalDaysTo"
    ]

    for (const key in searchFormState) {
      const value = searchFormState[key]
      // 复合字段跳过
      if (complexFields.includes(key)) {
        continue
      }
      // 日期范围字段 - 增加就诊时间 consultationTime
      else if (
        Array.isArray(value) &&
        value.length === 2 &&
        (key.includes("Datetime") ||
          key.includes("consultationTime") ||
          key.includes("assignmentTime") ||
          key.includes("catalogTime") ||
          key.includes("returnTime") ||
          key.includes("checkupDate"))
      ) {
        filterConditions.push(`${key}>=${value[0]}`, `${key}<=${value[1] + (1000 * 60 * 60 * 24 - 1)}`)
      }
      // 病案标记等布尔值参数
      else if (typeof value === "boolean" && value) {
        filterConditions.push(`${key}=YES`)
      }
      // 患者标签
      else if (key === "patientLabel") {
        if (!value?.length) continue
        if (typeof value === "string") {
          filterConditions.push(`${key}=${value}`)
        } else {
          value.length && filterConditions.push(`${key}=${value.join("|")}`)
        }
      }
      // 其他基础字段
      else if (value || value === 0) {
        if (Array.isArray(value)) {
          filterConditions.push(`${key}=${value.join("#")}`)
        } else {
          filterConditions.push(`${key}=${value}`)
        }
      }
    }
    // 患者信息复合字段
    const { patientFilterProp, patientFilterValue } = searchFormState
    if (patientFilterProp && patientFilterValue) filterConditions.push(`${patientFilterProp}=${patientFilterValue}`)

    // 住院次数复合字段
    const { visitIdSymbol, visitIdCount } = searchFormState
    if (visitIdSymbol && visitIdCount) filterConditions.push(`visitId${visitIdSymbol}${visitIdCount}`)

    // 住院天数范围字段
    const { inHospitalDaysFrom, inHospitalDaysTo } = searchFormState
    if (inHospitalDaysTo) filterConditions.push(`inHospitalDays<=${inHospitalDaysTo}`)
    if (inHospitalDaysFrom) filterConditions.push(`inHospitalDays>=${inHospitalDaysFrom}`)
    return filterConditions.join(",")
  }

  // 将query参数设置到searchParams中
  const setQuery = () => {
    if (!queryInitialData) return
    for (const key in queryState) {
      searchParams[key] = queryState[key]
    }
  }

  // 重置表单
  const resetState = () => {
    for (const key in filtersInitialData) {
      searchFormState[key] = filtersInitialData[key] as any
    }
    if (queryInitialData) {
      for (const key in queryInitialData) {
        queryState[key] = queryInitialData[key] as any
      }
    }
    if (advanceData) {
      for (const key in advanceData) {
        advanceState[key] = advanceData[key] as any
      }
    }
  }

  // 触发搜索
  const handleQuery = () => {
    searchParams.filters = generateFilters()
    searchParams.timestamp = new Date().getTime()
    formatter && formatter()
    setQuery()
    systemStore.setPageParams({
      id: location.pathname,
      searchParams: searchParams,
      searchFormState: searchFormState,
      queryState: queryState,
      advanceState: advanceState
    })
  }

  // 重置搜索
  const handleReset = () => {
    resetState()
    searchParams.filters = generateFilters()
    searchParams.timestamp = new Date().getTime()
    setQuery()
    formatter && formatter()
    systemStore.setPageParams({
      id: location.pathname,
      searchParams: searchParams,
      searchFormState: searchFormState,
      queryState: queryState,
      advanceState: advanceState
    })
  }

  return {
    searchFormState,
    queryState,
    advanceState,
    searchParams,
    handleQuery,
    handleReset
  }
}

export default useTableSearch
