import type { SearchFormConfigItem, TableColumnItem } from "@/types"

export const recallRecordSearchFormConfig: Array<SearchFormConfigItem> = [
  { type: "patient", label: "患者信息" },
  { type: "daterange", label: "入院时间", prop: "inHospitalDatetime" },
  { type: "daterange", label: "出院时间", prop: "outHospitalDatetime" },
  { type: "daterange", label: "审批时间", prop: "approverDatetime" }
]

// 审批记录表头
export const recallApprovalRecordTableColumns: Array<TableColumnItem> = [
  { prop: "version", label: "召回版本", minWidth: 180, must: true, fixed: "left" },
  { prop: "patientName", label: "姓名", minWidth: 100, must: true, fixed: "left" },
  { prop: "patientId", label: "患者编号", minWidth: 120, must: true, sortable: true },
  { prop: "mrNo", label: "病案号", minWidth: 100, must: true, sortable: true },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 120, must: true },
  { prop: "inHospitalDatetime", label: "入院时间", minWidth: 180, sortable: true },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 120, must: true },
  { prop: "outHospitalDatetime", label: "出院时间", minWidth: 180, sortable: true },
  { prop: "outDischargeDiagnosisName", label: "出院主诊断", minWidth: 200, must: true },
  { prop: "recallCount", label: "修改次数", minWidth: 120, sortable: true },
  { prop: "archivistName", label: "归档人", minWidth: 100 },
  { prop: "applicantName", label: "申请人", minWidth: 100, must: true },
  { prop: "createdDatetime", label: "申请时间", minWidth: 180, sortable: true },
  { prop: "recallReason", label: "召回原因", minWidth: 200, must: true },
  { prop: "operation", label: "操作", width: 75, fixed: "right", must: true }
]
