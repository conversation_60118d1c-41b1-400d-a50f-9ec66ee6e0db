<!-- 基于pdf-object的预览组件 -->
<template>
  <div class="pdf-preview-container common-box-shadow">
    <!-- 渲染区域 -->
    <el-skeleton style="width: 100%; height: 100%" animated :loading="skeletonLoading">
      <template #template>
        <el-skeleton-item variant="image" style="width: 100%; height: 100%" />
      </template>
    </el-skeleton>
    <div
      ref="pdfContentRef"
      v-loading="pdfState.status === 'loading'"
      class="pdf-preview-content"
      :style="{ paddingBottom: props.medicalLocation ? '44px' : '0' }"
    >
      <div v-if="pdfState.status === 'error'" class="pdf-error">文件加载失败</div>
      <div v-else-if="!pdfState.source" class="pdf-error">暂无数据</div>
      <div id="pdf-container" style="width: 100%; height: 100%; min-height: 700px; position: relative"></div>
      <div ref="hide" class="hide"></div>
    </div>
    <!-- 病案位置 -->
    <div v-if="props.medicalLocation" class="pdf-preview-location">病案位置：{{ props.medicalLocation }}</div>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch, ref, onMounted, nextTick } from "vue"
import { useRoute } from "vue-router"
import { toNumber } from "lodash-es"
import PDFObject from "pdfobject"
import { getWatermarkApi, fragmentingFileApi } from "@/interfaces"
import axios from "@/interfaces/axios-instance"
import { toastError, sessionLessAxios } from "@/utils"
import { checkBrowserVersion } from "@/utils/browser-util"

const route = useRoute()

const pdfContentRef = ref()

const skeletonLoading = ref(true)

const props = defineProps({
  enableCache: { type: Boolean, default: true }, // 是否启用缓存
  src: { type: String, required: true },
  fileWsid: { type: String, default: "", required: false },
  showWatermark: { type: Boolean, default: true, require: false },
  medicalLocation: { type: String, default: "", require: false }, // 病案位置
  sessionLess: { type: Boolean, default: false, require: false } //是否需要sessionWsid
})

// empty为空，loading为加载中，success为加载成功，error为加载失败
type PdfStatusType = "empty" | "loading" | "success" | "error"

// vertical为常规竖屏，horizontal为横屏
type PdfFileType = "vertical" | "horizontal"

const pdfState = reactive({
  type: "vertical" as PdfFileType, // pdf文件类型
  status: "empty" as PdfStatusType, // pdf文件加载状态
  source: ""
})

// 文件地址变化时发起新请求
watch(
  () => props.src,
  async () => {
    // 每次切换要删除上一次生成的水印图片
    const watermarkDoms = [...document.getElementsByClassName("watermark")]
    watermarkDoms.forEach(item => {
      item.parentElement?.removeChild(item)
    })
    console.log(props.src)
    if (!props.src || props.src.includes("undefined")) {
      // 清空已生成的pdf和水印
      pdfState.status = "empty"
      pdfState.source = ""
      const pdfContainerDom = document.getElementById("pdf-container")
      if (pdfContainerDom) {
        pdfContainerDom.innerHTML = ""
        pdfContainerDom.style.height = "0"
        skeletonLoading.value = false
      }
      return
    }
    if (props.src.includes("data")) {
      pdfState.source = props.src
      pdfState.status = "success"
      skeletonLoading.value = false
      return
    }
    pdfState.status = "loading"

    const requestUrl = props.enableCache ? props.src : props.src + `?t=${new Date().getTime()}`
    const handle = props.sessionLess
      ? sessionLessAxios({
          method: "get",
          url: requestUrl,
          responseType: "blob"
        })
      : axios({
          method: "get",
          url: requestUrl,
          responseType: "blob"
        })
    handle
      .then(async res => {
        pdfState.source = URL.createObjectURL(res.data)
        pdfState.status = "success"
        const pdfContainerDom = document.getElementById("pdf-container")
        // 恢复容器高度
        if (pdfContainerDom) {
          pdfContainerDom.style.height = "100%"
        }
        const browserVersion = checkBrowserVersion()
        console.log(browserVersion)
        if (browserVersion === "MID") {
          PDFObject.embed(URL.createObjectURL(new Blob([res.data], { type: "application/pdf" })), "#pdf-container", {
            height: "800px",
            pdfOpenParams: {
              toolbar: 0
            }
          })
        } else {
          PDFObject.embed(URL.createObjectURL(new Blob([res.data], { type: "application/pdf" })), "#pdf-container", {
            pdfOpenParams: {
              toolbar: 0
            }
          })
        }
        if (props.showWatermark) {
          await getWatermark()
          setWatermark()
        }
        skeletonLoading.value = false
      })
      .catch(error => {
        toastError(error, "文件获取失败")
        pdfState.status = "error"
        pdfState.source = ""
        skeletonLoading.value = false
      })
  },
  { immediate: true }
)

/* ===================================== 文件水印 ===================================== */
const watermarkState = reactive({
  waterMark: "",
  width: 0,
  height: 0,
  backgroundImage: "",
  backgroundRepeat: "",
  backgroundPosition: ""
})

function getTextActualSize(text) {
  const canvas = document.createElement("canvas")
  canvas.width = 400
  canvas.height = 400
  const ctx = canvas.getContext("2d") as CanvasRenderingContext2D
  const { actualBoundingBoxAscent, actualBoundingBoxDescent, actualBoundingBoxLeft, actualBoundingBoxRight } =
    ctx.measureText(text)
  return {
    width: actualBoundingBoxLeft + actualBoundingBoxRight,
    height: actualBoundingBoxAscent + actualBoundingBoxDescent
  }
}

async function getWatermark() {
  try {
    const res = props.sessionLess
      ? // await sessionLessAxios({
        //     method: "get",
        //     url: "/api/system/system-config/watermark"
        //   })
        {
          data: {
            data: {
              fontSize: 30,
              radians: 45,
              rowSize: 75,
              transparency: 0.2,
              waterMark: "无纸化病案系统"
            }
          }
        }
      : await getWatermarkApi()
    const resData = res.data.data
    // 获取绘制文本的尺寸
    const spacing = resData.rowSize
    const textSize = getTextActualSize(resData.waterMark || "无纸化病案系统")
    const fontSize = resData.fontSize
    const rotatePI = (-toNumber(resData.radians) / 180) * Math.PI
    const canvas = document.createElement("canvas")
    canvas.width = (textSize.width * 4 + 20 + spacing) / 2
    canvas.height = (textSize.width * 4 + 20 + spacing) / 2
    const ctx = canvas.getContext("2d")
    if (!ctx) return
    // 绘制之前清空画布
    ctx.clearRect(0, 0, watermarkState.width, watermarkState.height)
    // 设置画笔属性
    ctx.font = `${fontSize}px serif`
    ctx.fillStyle = `rgba(${resData.colorRgb})`
    ctx.globalAlpha = resData.transparency
    // 平移画布后，左上角不再是0，0, 而是（-canvas.width / 2， -canvas.height / 2）
    ctx.translate(canvas.width / 2, canvas.height / 2)
    // 旋转水印方向
    ctx.rotate(rotatePI)
    // 文字宽度
    const stringWidth = ctx.measureText(resData.waterMark || "无纸化病案系统").width

    ctx.fillText(resData.waterMark || "无纸化病案系统", -stringWidth / 2, fontSize / 4)
    watermarkState.backgroundImage = `url(${canvas.toDataURL()})`
    watermarkState.backgroundRepeat = "repeat"
    watermarkState.backgroundPosition = `top left`
  } catch (err: any) {
    toastError(err, "获取水印失败")
  }
}

// 设置水印
function setWatermark() {
  const dom = document.createElement("div")
  // 设置水印样式
  dom.className = "watermark"
  dom.style.position = "absolute"
  dom.style.width = "100%"
  dom.style.height = "100%"
  dom.style.top = "0px"
  dom.style.left = "0px"
  dom.style.pointerEvents = "none"
  dom.style.backgroundImage = watermarkState.backgroundImage
  dom.style.backgroundPosition = watermarkState.backgroundPosition
  dom.style.backgroundRepeat = watermarkState.backgroundRepeat
  setPdfWatermark(dom)
}

// 设置pdf预览水印
function setPdfWatermark(dom) {
  const grandDom = pdfContentRef.value
  const parentDoms = grandDom?.children
  // 如果没有获取到父节点，则再次触发设置水印（渲染存在时差）
  if (!parentDoms) setTimeout(() => setPdfWatermark(dom), 500)
  else {
    const parents = Array.from(parentDoms)
    if (parents.length === 0) return
    const canvasDom = parents[0] as HTMLElement
    // pdf水印样式
    dom.style.width = canvasDom.style.width
    dom.style.height = "calc(100% - 60px)"
    dom.style.top = "30px"
    dom.style.right = "0px"
    dom.style.margin = "auto"
    grandDom.appendChild(dom)
  }
}
const hide = ref()
</script>

<style lang="less" scoped>
.pdf-preview-container {
  display: flex;
  flex-direction: column;
  height: calc(100% - 44px);
  width: 100%;
  position: relative;

  .pdf-preview-content {
    flex: 1;
    position: relative;
    width: 100%;
    height: 100%;

    .pdf-error {
      color: #999999;
      font-size: 16px;
      letter-spacing: 8px;
      text-align: center;
      padding-top: 50px;
      width: 100%;
      height: calc(100% - 50px);
    }
  }

  .pdf-preview-location {
    height: 30px;
    line-height: 30px;
    padding: 10px;
    position: absolute;
    background: #fff;
    width: calc(100% - 20px);
    bottom: 0;
  }
}
.hide {
  width: 30%;
  background: rgb(50, 54, 57);
  position: absolute;
  right: 0;
  top: 0;
  height: 6vh;
  display: none;
}
</style>
