import axios from "@/interfaces/axios-instance"

/**
 * @method GET
 * @desc   我的借阅-导出
 */
export function exportBorrowFilesApi(obj) {
  const { paramsId, sealKey = "", type } = obj
  return axios({
    method: "get",
    url: `/api/document/borrow/export`,
    params: {
      paramsId,
      sealKey,
      type
    },
    responseType: "blob"
  })
}

/**
 * @method POST
 * @desc   我的借阅-打印分类信息
 */
export function getBorrowPrintInfoApi(paramsId: string) {
  return axios({
    method: "post",
    url: `/api/document/borrow/print/mrclass`,
    data: {
      paramsId: paramsId
    }
  })
}

/**
 * @method GET
 * @desc   我的借阅-打印
 */
export function printBorrowFilesApi(obj) {
  const { paramsId, sealKey = "" } = obj

  return axios({
    method: "get",
    url: `/api/document/borrow/print`,
    params: {
      paramsId,
      sealKey
    },
    responseType: "blob"
  })
}

/**
 * @method GET
 * @desc   我的借阅-分页列表
 */
export function getBorrowListApi(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/document/borrow/my-borrow`,
    params
  })
}
