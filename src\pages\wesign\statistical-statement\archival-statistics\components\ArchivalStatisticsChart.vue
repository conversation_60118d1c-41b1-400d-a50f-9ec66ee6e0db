<template>
  <div class="chart-container">
    <div class="chart-header flex-between">
      <div class="date-picker">
        <el-radio-group v-model="itemType">
          <el-radio-button v-for="item in archivalDaysOptions" :key="item.value" :label="item.value">
            {{ item.label }}
          </el-radio-button>
        </el-radio-group>
      </div>
      <div class="table-chart-tool">
        <el-tooltip content="表格展示">
          <i v-if="isDisplayTable"><img src="@/assets/svg/statistical-statement/table-active.svg" alt="" /></i>
          <i v-else @click="() => emits('update:showChart', !isDisplayTable)">
            <img src="@/assets/svg/statistical-statement/table.svg" alt="" />
          </i>
        </el-tooltip>
        <el-tooltip content="图表展示">
          <i v-if="isDisplayTable" @click="() => emits('update:showChart', !isDisplayTable)">
            <img src="@/assets/svg/statistical-statement/chart.svg" alt="" />
          </i>
          <i v-else><img src="@/assets/svg/statistical-statement/chart-active.svg" alt="" /></i>
        </el-tooltip>
      </div>
    </div>

    <v-chart v-loading="chartTableDataLoading" :option="options" autoresize style="min-height: 400px"></v-chart>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue"
import { BarChart } from "echarts/charts"
import { GridComponent, TooltipComponent } from "echarts/components"
import { use } from "echarts/core"
import { CanvasRenderer } from "echarts/renderers"
import VChart from "vue-echarts"
import { toastError } from "@/utils"
import { getUserArchivalConfigApi, getArchivalStatisticsChartApi } from "../../interface"
import { sortDynamicColumn } from "../config"

interface PropsType {
  showChart: boolean
  requestParams: any
}

const emits = defineEmits(["update:showChart"])

const props = defineProps<PropsType>()

const isDisplayTable = computed({
  get: () => props.showChart,
  set: val => emits("update:showChart", val)
})

const chartTableDataLoading = ref(false)

// 统计图所需指标
const archivalDaysOptions = ref<Array<Record<string, any>>>([])

const itemType = ref("")

const isShowGraphic = ref(true)

const xAxisData = ref<Array<string>>([])
const seriesData = ref<Array<string>>([])

const chartSearchParams = computed(() => {
  return { ...props.requestParams, itemCode: itemType.value }
})

onMounted(() => {
  getUserArchivalConfig()
})

watch(() => props.requestParams, getArchivalStatisticsChartData)

watch(
  () => itemType.value,
  () => {
    getArchivalStatisticsChartData()
  }
)

// 获取自定义配置数据
function getUserArchivalConfig() {
  getUserArchivalConfigApi()
    .then(res => {
      const userConfigData = res.data.data
      getChartItemCode(userConfigData)
    })
    .catch(err => {
      toastError(err, "获取自定义配置失败")
    })
}

// 获取统计图需要的指标
function getChartItemCode(configData) {
  const resultCode: Array<Record<string, any>> = []
  configData.forEach(item => {
    if (item.archiveCountTitle !== "平均归档天数") {
      if (item.archivePercentageCheck) {
        const codeObj = {
          label: item.archivePercentageTitle,
          value: item.archivePercentageCode
        }
        resultCode.push(codeObj)
      }
    }
  })
  sortDynamicColumn(resultCode)
  archivalDaysOptions.value = resultCode
  itemType.value = resultCode[0].value
}

// 获取柱状图数据
function getArchivalStatisticsChartData() {
  chartTableDataLoading.value = true
  getArchivalStatisticsChartApi(chartSearchParams.value)
    .then(res => {
      formatChartData(res.data.data.rows)
      chartTableDataLoading.value = false
    })
    .catch(err => {
      toastError(err)
      chartTableDataLoading.value = false
    })
}

// 格式化统计图数据
function formatChartData(data) {
  if (data.length === 0) {
    isShowGraphic.value = false
  } else {
    isShowGraphic.value = true
  }
  const tempXAxisData = [] as Array<string>
  const tempSeriesData = [] as Array<string>
  data.map(item => {
    item.deptName ? tempXAxisData.push(item.deptName) : tempXAxisData.push("")
    tempSeriesData.push(item.count)
  })
  xAxisData.value = tempXAxisData
  seriesData.value = tempSeriesData
}

use([TooltipComponent, GridComponent, BarChart, CanvasRenderer])

const options = ref({
  grid: {
    left: "4%",
    right: "4%"
  },
  graphic: {
    type: "text", // 类型：文本
    left: "center",
    top: "middle",
    silent: true, // 不响应事件
    invisible: isShowGraphic, // 有数据就隐藏
    style: {
      fill: "#9d9d9d",
      fontWeight: "bold",
      text: "暂无数据",
      fontFamily: "Microsoft YaHei",
      fontSize: "25px"
    }
  },
  tooltip: {},
  xAxis: {
    type: "category",
    data: xAxisData
  },
  yAxis: {
    type: "value"
  },
  series: [
    {
      data: seriesData,
      type: "bar",
      barWidth: "10%",
      color: "#3860F4",
      itemStyle: {
        borderRadius: [20, 20, 0, 0]
      }
    }
  ]
})
</script>

<style lang="less" scoped>
.chart-container {
  height: calc(100% - 20px);

  .date-picker {
    :deep(.el-radio-group) {
      max-width: 1300px;
      overflow: auto;
      margin-bottom: 0 !important;
      margin-right: 10px;
      flex-wrap: nowrap;
      padding: 5px 10px;
      background-color: #eff2f7;
      border-radius: 4px;
    }

    :deep(.el-radio-button__inner) {
      border: none;
      background-color: transparent;
      box-shadow: none;
      &:hover {
        color: #3860f4;
      }
    }
    :deep(.el-radio-group) {
      .is-active {
        background-color: #3860f4;
        border-radius: 4px;
        color: #fff;
        &:hover {
          .el-radio-button__inner {
            color: #fff;
          }
        }
      }
    }
  }
}
</style>
