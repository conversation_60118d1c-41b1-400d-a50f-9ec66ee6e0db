import axios from "@/interfaces/axios-instance"

/**
 * @method GET
 * @desc   我的借阅-查看
 */
export function getMyBorrowViewApi(obj) {
  const { paramsId = "", sealKey = "" } = obj
  return axios({
    method: "get",
    url: `/api/document/borrow/view`,
    params: {
      paramsId,
      sealKey
    }
  })
}

/**
 * @method GET
 * @desc   我的借阅-
 */
export function getBorrowMrnoViewApi(obj) {
  const { documentBagWsid = "", sealKey = "" } = obj
  return axios({
    method: "get",
    url: `/api/document/authorization/bags/${documentBagWsid}`,
    params: {
      sealKey
    }
  })
}

/**
 * @method GET
 * @desc   查看借阅病案详情数据
 */
export function getBorrowDeptViewApi(obj) {
  const { documentBagWsid = "", sealKey = "" } = obj
  return axios({
    method: "get",
    url: `/api/document/authorization/dept/${documentBagWsid}`,
    params: {
      sealKey
    }
  })
}
