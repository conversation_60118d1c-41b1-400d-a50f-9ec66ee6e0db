<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="outpatientTabsConfig" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <CommonInputFormItem
          v-model="searchFormState.medicalNos"
          label="就诊卡号"
          placeholder="多个就诊卡号用逗号分隔"
          style="align-items: center"
        />

        <CommonInputFormItem
          v-model="searchFormState.names"
          label="患者姓名"
          placeholder="多个患者姓名用逗号分隔"
          style="align-items: center"
        />

        <el-form-item label="就诊科室">
          <el-select
            v-model="searchFormState.deptName"
            filterable
            multiple
            collapse-tags
            clearable
            :filter-method="filterDeptOptions"
          >
            <el-option
              v-for="option in filteredDeptOptions"
              :key="option.value as string"
              :label="option.label"
              :value="option.label"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="就诊时间">
          <el-date-picker
            v-model="searchFormState.consultationTime"
            type="daterange"
            unlink-panels
            :clearable="false"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :disabled-date="disabledDate"
            value-format="x"
            @calendar-change="handleCalendarChange"
          ></el-date-picker>
        </el-form-item>

        <CommonSelectFormItem v-model="searchFormState.reviewStatus" label="审核状态" :options="reviewStatusOptions" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="tableRef"
        table-id="outpatientListTableIdent"
        :table-columns="outpatientAndEmergencyTableColumns"
        :request-api="getOutpatientListApi"
        :request-params="actualSearchParams"
        :data-callback="dataCallback"
      >
        <template #header>
          <div class="flex-start">
            <BatchOperationButton
              :disabled="!auditRows.length"
              type="primary"
              :plain="false"
              tooltip="请至少选择一条待审核的数据"
              @click="handleAudit"
            >
              审核
            </BatchOperationButton>
            <BatchOperationButton :disabled="!selectedRows.length" tooltip="请至少选择一条的数据" @click="handleSync">
              同步
            </BatchOperationButton>
            <el-button @click="handleCompare">数据对比</el-button>
            <ManualExportButton
              ref="manualExportRef"
              button-text="导出csv"
              :selected-count="selectedRows.length"
              :total="tableRef?.tableState?.total"
              :max="10000"
              :tooltip-disabled="true"
              :error-message="exportErrorMessage"
              @export="handleExport('selected')"
              @export-all="handleExport('all')"
            />
            <div style="margin-left: 12px"></div>
            <ManualExportButton
              ref="manualExportRef"
              button-text="导出至前置机"
              :selected-count="selectedRows.length"
              :total="tableRef?.tableState?.total"
              :max="10000"
              :show-icon="false"
              :error-message="exportErrorMessage"
              @export="handleExportToRemote('selected')"
              @export-all="handleExportToRemote('all')"
            />
            <div style="margin-left: 12px"></div>
            <BatchOperationButton @click="autoExportConfigDialogRef?.show">自动导出配置</BatchOperationButton>
          </div>
        </template>

        <template #syncDatetime="{ row }">
          {{ formatDatetime(row.syncDatetime) }}
        </template>
        <template #reviewStatus="{ row }">
          <el-tag :type="getReviewsStatusTagType(row.reviewStatus)">
            {{ reviewStatusOptions.find(item => item.value === row.reviewStatus)?.label }}
          </el-tag>
        </template>
        <template #operation="{ row }">
          <TableButton @click="toEdit(row)">详情</TableButton>
          <TableButton @click="toDetail(row)">查看</TableButton>
          <TableButton @click="downloadPdf(row)">下载PDF</TableButton>
        </template>
      </CommonTable>
    </template>

    <CompareResultDialog
      ref="compareResultDialogRef"
      :search-form-state="searchFormState"
      @sync="handleSync"
      @download-diff="handleDownloadDiff"
    />

    <AutoExportConfigDialog ref="autoExportConfigDialogRef" />

    <SyncLoadingDialog ref="syncLoadingDialogRef" />
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref, computed, onMounted, h, watch } from "vue"
import { useRouter } from "vue-router"
import { type DateModelType } from "element-plus"
import { cloneDeep } from "lodash-es"
import {
  PageContainer,
  SearchContainer,
  CommonTable,
  CommonInputFormItem,
  CommonSelectFormItem,
  TableButton,
  BatchOperationButton,
  ManualExportButton
} from "@/base-components"
import { TabsRouter } from "@/page-components"
import {
  outpatientAndEmergencyTableColumns,
  reviewStatusOptions,
  getReviewsStatusTagType,
  outpatientTabsConfig
} from "../config"
import {
  getOutpatientListApi,
  exportToCvsApi,
  auditOutpatientApi,
  exportOutpatientDataToRemoteApi,
  checkOutpatientDataSourceApi,
  syncOutpatientDataApi,
  getOutpatientExportLimitApi,
  syncOutpatientDataSubApi,
  diffDownloadOutpatientDataSubApi
} from "../interface"
import AutoExportConfigDialog from "./components/AutoExportConfigDialog.vue"
import CompareResultDialog from "./components/CompareResultDialog.vue"
import SyncLoadingDialog from "./components/SyncLoadingDialog.vue"
import { useTableSearch, useCommonOptions } from "@/hooks"
import { getHQMSDepartmentApi, exportDynamicFormCatalogApi } from "@/interfaces"
import { useSystemStore } from "@/stores"
import { formatDatetime, Message, toastError, downloadFile, formatDate, extractErrorMsg, SystemAlert } from "@/utils"

const router = useRouter()

const systemStore = useSystemStore()

/* ======================== 科室搜索 ======================== */

const { options: outDeptOptions } = useCommonOptions({
  getOptionsApi: getHQMSDepartmentApi,
  labelAlias: "value",
  valueAlias: "key"
})

const filteredDeptOptions = ref<Record<string, any>[]>([])

// el-select自带筛选超过100条会报错，需要自定义filter-method
const filterDeptOptions = query => {
  if (!query) {
    filteredDeptOptions.value = outDeptOptions.value
    return
  }
  filteredDeptOptions.value = outDeptOptions.value.filter(option => option.label.includes(query))
}

// 初始化更新 filteredDeptOptions
watch(
  () => outDeptOptions.value,
  newVal => {
    if (newVal) {
      filteredDeptOptions.value = cloneDeep(newVal)
    }
  }
)

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  medicalNos: "", // 就诊卡号
  names: "", // 患者姓名
  deptName: [] as string[], // 就诊科室
  consultationTime: ["", ""] as [DateModelType, DateModelType], // 就诊日期
  reviewStatus: "" // 审核状态
})

// 默认三个月前当日0点
const defaultTimeStart = new Date(new Date().setHours(0, 0, 0, 0) - 1 * 30 * 24 * 3600 * 1000).getTime()
const defaultTimeEnd = new Date(new Date().setHours(23, 59, 59, 999)).getTime()

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState, undefined, {
  consultationTime: [defaultTimeStart, defaultTimeEnd]
})

// 实际请求参数 -----------------------------

const actualSearchParams = reactive({
  medicalNos: "", // 就诊卡号
  names: "", // 患者姓名
  deptName: "", // 就诊科室
  consultationTimeStart: "" as DateModelType, // 就诊日期
  consultationTimeEnd: "" as DateModelType, // 就诊日期
  reviewStatus: "" // 审核状态
})

watch(
  () => searchParams,
  () => {
    actualSearchParams.medicalNos = searchFormState.medicalNos
    actualSearchParams.names = searchFormState.names
    actualSearchParams.deptName = searchFormState.deptName ? searchFormState.deptName?.join(",") : ""
    actualSearchParams.consultationTimeStart = searchFormState.consultationTime[0]
    actualSearchParams.consultationTimeEnd = new Date(searchFormState.consultationTime[1]).setHours(23, 59, 59, 999)
    actualSearchParams.reviewStatus = searchFormState.reviewStatus
  },
  { deep: true, immediate: true }
)

/* ======================== 表格数据 ======================== */

const tableRef = ref<InstanceType<typeof CommonTable>>()

// 当前选中表格项
const selectedRows = computed(() => tableRef.value?.tableState?.selectedRows ?? [])

// 可生成上报pdf的选中项
const auditRows = computed(() => selectedRows.value.filter(item => item.reviewStatus === "REVIEW_WAIT"))

const dataCallback = (data: Array<Record<string, any>>) => {
  return data.map(item => ({
    ...item,
    outHospitalDatetime: formatDatetime(item.outHospitalDatetime),
    inHospitalDatetime: formatDatetime(item.inHospitalDatetime),
    consultationTime: formatDate(item.consultationTime)
  }))
}

/* ======================== 导出 ======================== */

const manualExportRef = ref<InstanceType<typeof ManualExportButton>>()
const exportErrorMessage = ref("")
let exportLimit = 10000 // 导出数据的上限：如果超出上限则调用接口完成显示提示语，否则执行下载到本地

onMounted(async () => {
  exportLimit = (await getOutpatientExportLimitApi()).data.data
})

const checkExportable = (type: "all" | "selected") => {
  if (!searchFormState.consultationTime[1] || !searchFormState.consultationTime[0]) {
    Message.error("请选择就诊时间")
    return false
  }
  if (type === "all") {
    if (
      searchFormState.consultationTime[1] &&
      searchFormState.consultationTime[0] &&
      // @ts-ignore
      Math.abs(searchFormState.consultationTime[1] - searchFormState.consultationTime[0]) > 31 * 24 * 3600 * 1000
    ) {
      Message.error("导出时间范围不能超过30天")
      return false
    } else {
      return true
    }
  }
  return true
}

const handleExport = async (type: "all" | "selected") => {
  if (!checkExportable(type)) return
  const totalExportCount = type === "all" ? tableRef.value?.tableState?.total || 0 : selectedRows.value.length // 当前导出总数
  const registerNos = type === "selected" ? selectedRows.value.map(row => row.registerNo) : undefined
  // 如果导出数量大于后台配置的导出上限，调用接口后就提示用户稍后查看（异步任务）
  if (totalExportCount > exportLimit) {
    try {
      systemStore.showLoading()
      await exportToCvsApi({ registerNos: registerNos, ...actualSearchParams })
      systemStore.hideLoading()
      SystemAlert("导出任务处理中，请您前往导出记录查看任务进度", "success")
    } catch (error: any) {
      systemStore.hideLoading()
      toastError(error, "导出失败")
    }
  }
  // 否则下载到本地
  else {
    try {
      manualExportRef.value?.handleExportStart()
      const res = await exportToCvsApi({ registerNos: registerNos, ...actualSearchParams })
      const fileName = res.headers["content-disposition"].split("filename")[1].split("=")[1].replace(/"/g, "")
      let fileType = fileName.split(".")[1].toLowerCase()
      if (fileType === "zip") fileType = "application/zip"
      downloadFile({ fileData: res.data, fileType: fileType, fileName })
      manualExportRef.value?.handleExportDone()
      exportErrorMessage.value = ""
    } catch (error: any) {
      console.log(error)
      let errorMessage = ""
      if (error?.response?.data instanceof Blob) {
        const blob = new Blob([error.response.data])
        const blobJson = JSON.parse(await blob.text())
        errorMessage = blobJson.userMessage
      } else {
        errorMessage = extractErrorMsg(error)
      }
      manualExportRef.value?.handleExportError()
      exportErrorMessage.value = errorMessage
      Message.error(errorMessage || "导出失败")
    }
  }
}

/* ======================== 导出到前置机 ======================== */

const exportRemoteLoading = ref(false)

// 导出至远程目录
const handleExportToRemote = async (type: "all" | "selected") => {
  if (!checkExportable(type)) return
  try {
    exportRemoteLoading.value = true
    const registerNos = type === "selected" ? selectedRows.value.map(row => row.registerNo) : undefined
    await exportOutpatientDataToRemoteApi({ registerNos: registerNos, ...actualSearchParams })
    exportRemoteLoading.value = false
    Message.success("导出至前置机成功")
  } catch (error: any) {
    exportRemoteLoading.value = false
    toastError(error, "导出执行失败")
  }
}

/* ======================== 同步 ======================== */

const syncLoadingDialogRef = ref<InstanceType<typeof SyncLoadingDialog>>()

// 同步已勾选的
const handleSync = () => {
  const requestData = {
    registerNos: selectedRows.value.length
      ? selectedRows.value.map(row => row.registerNo).join(",")
      : searchFormState.medicalNos,
    patientNames: searchFormState.names,
    startTime: searchFormState.consultationTime[0],
    endTime: new Date(searchFormState.consultationTime[1]).setHours(23, 59, 59, 59)
  }
  if (selectedRows.value.length) {
    handleSyncSelected(requestData)
  } else {
    handleSyncAll(requestData)
  }
}

// 下载异常数据
const handleDownloadDiff = async () => {
  try {
    systemStore.showLoading("下载中")
    const requestData = {
      registerNos: selectedRows.value.length ? selectedRows.value.map(row => row.registerNo).join(",") : "",
      medicalNos: selectedRows.value.length ? "" : searchFormState.medicalNos,
      patientNames: selectedRows.value.length ? "" : searchFormState.names,
      startTime: selectedRows.value.length ? "" : searchFormState.consultationTime[0],
      endTime: selectedRows.value.length ? "" : new Date(searchFormState.consultationTime[1]).setHours(23, 59, 59, 59)
    }
    const res = await diffDownloadOutpatientDataSubApi(requestData)
    systemStore.hideLoading()
    downloadFile({
      fileData: res.data,
      fileType: "application/vnd.ms-excel",
      fileName: decodeURIComponent(
        res.headers["content-disposition"].split("filename")[1].split("=")[1].replaceAll(`"`, "")
      )
    })
    Message.success("下载文件成功")
  } catch (err: any) {
    const reader = new FileReader()
    reader.onload = e => {
      const result = JSON.parse((e.target?.result as string) ?? {})
      const msg = result?.userMessage || result?.developerMessage
      systemStore.hideLoading()
      Message.error(msg || "下载失败")
    }
    reader.readAsText(err.response?.data)
  }
}

// 同步已勾选的
const handleSyncSelected = requestData => {
  systemStore.showLoading("正在同步")
  syncOutpatientDataApi(requestData)
    .then(res => {
      systemStore.hideLoading()
      SystemAlert(`同步成功，共同步${res.data.data}条数据`, "success")
      tableRef.value?.refreshTableData()
    })
    .catch(error => {
      systemStore.hideLoading()
      toastError(error, "同步失败")
    })
}

// 同步全部
const handleSyncAll = requestData => {
  syncLoadingDialogRef.value?.show()
  syncOutpatientDataSubApi(requestData)
    .then(res => {
      syncLoadingDialogRef.value?.finish(res.data.data)
      tableRef.value?.refreshTableData()
    })
    .catch(error => {
      if (error.code !== "ECONNABORTED") {
        syncLoadingDialogRef.value?.close()
        SystemAlert(extractErrorMsg(error, "数据同步失败"))
      }
    })
}

/* ======================== 数据对比 ======================== */

const compareResultDialogRef = ref<InstanceType<typeof CompareResultDialog>>()

// 数据对比
const handleCompare = async () => {
  if (tableRef.value?.tableState.dataLoading) return
  systemStore.showLoading("请稍候")
  const requestData = {
    registerNos: selectedRows.value.length ? selectedRows.value.map(row => row.registerNo).join(",") : "",
    medicalNos: selectedRows.value.length ? "" : searchFormState.medicalNos,
    patientNames: selectedRows.value.length ? "" : searchFormState.names,
    startTime: selectedRows.value.length ? "" : searchFormState.consultationTime[0],
    endTime: selectedRows.value.length ? "" : new Date(searchFormState.consultationTime[1]).setHours(23, 59, 59, 59)
  }
  const res = (await checkOutpatientDataSourceApi(requestData))?.data?.data
  const hisCount = selectedRows.value.length ? selectedRows.value.length : tableRef.value?.tableState?.total ?? 0
  const { compareSourceCount, businessCount, pct } = res
  compareResultDialogRef.value?.show(hisCount, compareSourceCount, businessCount, pct)
  systemStore.hideLoading()
}

/* ======================== 时间筛选限制 ======================== */

const startDate = ref()
const days = 31 * 24 * 3600 * 1000

const handleCalendarChange = date => {
  const [minDate, maxDate] = date
  if (minDate && !maxDate) {
    startDate.value = minDate // 记录选中的首个日期
  } else {
    startDate.value = null
  }
}

const disabledDate = time => {
  // 如果已经选择了开始日期，则禁用所有超出30天范围的日期
  if (startDate.value) {
    return time.getTime() < startDate.value?.getTime() - days || time.getTime() > startDate.value?.getTime() + days
  } else {
    return false
  }
}

/* ======================== 表格操作 ======================== */

// 审核
const handleAudit = async () => {
  try {
    systemStore.showLoading("审核中")
    const wsids = auditRows.value.map(item => item.registerNo)
    await auditOutpatientApi(wsids)
    systemStore.hideLoading()
    Message.success("审核成功")
    tableRef.value?.refreshTableData()
  } catch (error: any) {
    console.log(error)
    systemStore.hideLoading()
    toastError(error, "操作失败")
  }
}

// 详情
const toEdit = row => {
  router.push({
    path: "/data-report/outpatient-and-emergency/edit",
    query: {
      appScenario: "OUTPATIENT_SERVICE_HQMS",
      businessDataWsid: row.registerNo
    }
  })
}

// 查看
const toDetail = (row: Record<string, any>) => {
  router.push(`/data-report/outpatient-and-emergency/detail?registerNo=${row.registerNo}`) // TODO
}

// 下载pdf
const downloadPdf = async (row: Record<string, any>) => {
  systemStore.showLoading("正在下载，请稍候")
  exportDynamicFormCatalogApi(row.registerNo)
    .then(res => {
      systemStore.hideLoading()
      downloadFile({
        fileData: res.data,
        fileType: "application/pdf",
        fileName: `${decodeURIComponent(res.headers["content-disposition"].split("filename=")[1].replace(/"/g, ""))}`
      })
      Message.success("下载文件成功")
    })
    .catch(error => {
      console.log(error)
      systemStore.hideLoading()
      toastError(error, "下载文件失败")
    })
}

/* ====================== 自动导出配置 ======================= */

const autoExportConfigDialogRef = ref<InstanceType<typeof AutoExportConfigDialog>>()
</script>
