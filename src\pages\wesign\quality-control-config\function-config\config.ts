import { EditorView } from "@codemirror/view"
import type { TableColumnItem, BaseOptionItem, DialogFormConfigItem } from "@/types"
import type { FormRules } from "element-plus"
import { variableCodeValidator } from "@/utils"

export const menuId = "/quality-control-config/function-config"

export const environmentVariableColumns: Array<TableColumnItem> = [
  { prop: "elementName", label: "环境变量名", minWidth: 100 },
  { prop: "elementCode", label: "环境变量代码", minWidth: 200 },
  { prop: "describe", label: "变量描述", minWidth: 150 },
  { prop: "standardKey", label: "关联字段", minWidth: 150 },
  { prop: "elementTypeEnum", label: "类型", minWidth: 100 },
  { prop: "operation", label: "操作", width: 100, fixed: "right" }
]

export const expressionFunctionColumns: Array<TableColumnItem> = [
  { prop: "funName", label: "函数名", minWidth: 200 },
  { prop: "code", label: "函数代码", minWidth: 100 },
  { prop: "funSource", label: "代码块", minWidth: 200 },
  { prop: "status", label: "状态", minWidth: 200 },
  { prop: "operation", label: "操作", width: 200, fixed: "right" }
]

export const tabsRouterList = [
  // {
  //   label: "环境变量",
  //   path: "/quality-control-config/function-config/environment-variable",
  //   name: "qualityControlConfig_functionConfig_environmentVariable"
  // },
  {
    label: "函数表达式",
    path: "/quality-control-config/function-config/expression-function",
    name: "qualityControlConfig_functionConfig_expressionFunction"
  }
]

export const qcPointOptions: Array<BaseOptionItem> = [{ value: "HOME_PAGE_CATALOG_COMPLETE", label: "首页编目完成" }]

// 获取质控点描述
export const getQcPointDesc = value => {
  switch (value) {
    case "HOME_PAGE_CATALOG_COMPLETE":
      return "首页编目完成"
  }
}

// 获取变量所属类型描述
export const getElementTypeDesc = value => {
  switch (value) {
    case "HOME_PAGE":
      return "首页"
    case "FILE":
      return "文件"
    default:
      break
  }
}

export const environmentVariableFormConfig: Array<DialogFormConfigItem> = [
  {
    type: "input",
    label: "变量名",
    prop: "elementName",
    rules: [
      { required: true, message: "请输入变量名称", trigger: "blur" },
      { max: 30, message: "变量名最多不超过30位" }
    ]
  },
  {
    type: "input",
    label: "变量代码",
    prop: "elementCode",
    rules: [{ required: true, validator: variableCodeValidator, trigger: "blur" }]
  },
  {
    type: "select",
    label: "变量类型",
    prop: "elementType",
    options: [
      { value: "HOME_PAGE", label: "首页" },
      { value: "FILE", label: "文件" }
    ],
    rules: [{ required: true, message: "请选择变量类型", trigger: "blur" }]
  },
  {
    type: "select",
    label: "关联字段",
    prop: "standardKey",
    options: [],
    rules: [{ required: true, message: "请选择关联的首页标准字段", trigger: "blur" }]
  },
  {
    type: "textarea",
    label: "变量描述",
    prop: "describe"
  }
]

// 函数名校验
const funCodeValidator = (_rule: any, value: string, callback: (err?: Error) => void) => {
  if (value === "") {
    callback(new Error("请输入函数代码"))
  } else {
    const reg = /^[a-zA-Z][a-zA-Z0-9]*$/
    if (reg.test(value)) {
      callback()
    } else {
      if (!/^[a-zA-Z]*$/.test(value[0])) {
        callback(new Error("函数名称必需以字母开头"))
      } else {
        callback(new Error("函数名称不合法"))
      }
    }
  }
}

export const ruleConfigFormRules: FormRules = {
  funName: [{ required: true, message: "请输入函数名（60字符以内）", trigger: "blur" }],
  code: [{ required: true, validator: funCodeValidator, trigger: "blur" }],
  funSource: [{ required: true, message: "请输入", trigger: "blur" }],
  funMethodName: [{ required: false, message: "请选择质控类型", trigger: "blur" }]
}

export const myTheme = EditorView.theme(
  {
    // 输入的字体颜色
    "&": {
      color: "#0052D9",
      backgroundColor: "#FFFFFF"
    },
    ".cm-content": {
      caretColor: "#0052D9"
    },
    // 激活背景色
    ".cm-activeLine": {
      backgroundColor: "#FFFFDD"
    },
    // 激活序列的背景色
    ".cm-activeLineGutter": {
      backgroundColor: "#FAFAFA"
    },
    //光标的颜色
    "&.cm-focused .cm-cursor": {
      borderLeftColor: "#0052D9"
    },
    // 选中的状态
    "&.cm-focused .cm-selectionBackground, ::selection": {
      backgroundColor: "#0052D9",
      color: "#dcdfe6"
    },
    // 左侧侧边栏的颜色
    ".cm-gutters": {
      backgroundColor: "#FFFFFF",
      color: "#ddd", //侧边栏文字颜色
      border: "none"
    }
  },
  { dark: true }
)
