import type { TableColumnItem, SearchFormConfigItem } from "@/types"

export const menuId = "/borrow/my-authorized"

export const tabsRouterList = [
  { label: "授权(病案号)", path: "/borrow/my-authorized/auth-mrno" },
  { label: "授权(科室)", path: "/borrow/my-authorized/auth-dept" }
]

export const authorizedSearchFormConfig: Array<SearchFormConfigItem> = [
  { type: "patient", label: "患者信息" },
  { type: "department", label: "出院科室", prop: "outHospitalDeptWsid" },
  { type: "daterange", label: "出院日期", prop: "outHospitalDatetime" }
]

export const authorizedByMrnoColumns: Array<TableColumnItem> = [
  { prop: "mrNo", label: "病案号", minWidth: 100, sortable: true, must: true },
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 100, sortable: true, must: true },
  { prop: "visitId", label: "住院次数", minWidth: 100 },
  { prop: "outHospitalDatetime", label: "出院时间", minWidth: 180, sortable: true },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 120, sortable: true },
  { prop: "statusEnumName", label: "病案状态", minWidth: 120 },
  { prop: "authorizerName", label: "授权人", minWidth: 120 },
  { prop: "operationTime", label: "授权时间", minWidth: 180 },
  { prop: "operation", label: "操作", width: 75, fixed: "right", must: true }
]

export const authorizedByDeptColumns: Array<TableColumnItem> = [
  { prop: "mrNo", label: "病案号", minWidth: 100, sortable: true, must: true },
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 100, sortable: true, must: true },
  { prop: "visitId", label: "住院次数", minWidth: 100 },
  { prop: "outHospitalDatetime", label: "出院时间", minWidth: 170, sortable: true },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 120, sortable: true },
  { prop: "statusEnumName", label: "病案状态", minWidth: 120 },
  { prop: "outHospitalDeptName", label: "授权科室", minWidth: 120, sortable: true },
  { prop: "authorizerName", label: "授权人", minWidth: 120 },
  { prop: "operationTime", label: "授权时间", minWidth: 170 },
  { prop: "operation", label: "操作", width: 75, fixed: "right", must: true }
]
