<template>
  <SearchContainer v-if="props.queryTerm.length" @query-btn-click="querySearch" @reset-btn-click="queryReset">
    <div v-for="item in props.queryTerm" :key="item.prop" class="query-from-item-container">
      <DaterangeFormItem
        v-if="item.type === QueryTermTypeEnum.Daterange"
        v-model:model-value="formState[item.prop]"
        :label="item.label"
      />
      <RangeSelect
        v-else-if="item.type === QueryTermTypeEnum.RangeSelect"
        v-model:model-value="formState[item.prop]"
        :label="item.label"
        :rc-code="item.rcCode"
      />
      <DepartmentFormItem
        v-else-if="item.type === QueryTermTypeEnum.DeptSelect"
        v-model:model-value="formState[item.prop]"
        :label="item.label"
      />
      <CountRangeFormItem
        v-else-if="item.type === QueryTermTypeEnum.CountRange"
        v-model:from="formState[`${item.prop}From`]"
        v-model:to="formState[`${item.prop}To`]"
        :label="item.label"
        :min="0"
      />

      <IcdSelect
        v-else-if="item.type === QueryTermTypeEnum.NameSelect || item.type === QueryTermTypeEnum.CodeSelect"
        v-model:model-value="formState[item.prop]"
        :version-type="item.versionType"
        :label="item.label"
        :type="item.type === QueryTermTypeEnum.NameSelect ? 'name' : 'code'"
      />
      <el-form-item v-else :label="item.label">
        <el-input
          v-if="item.type === QueryTermTypeEnum.Input"
          v-model="formState[item.prop]"
          :placeholder="`请输入${item.label}`"
        />
        <div v-else>该搜索项失踪了</div>
      </el-form-item>

      <el-icon class="delete-btn" @click="deleteQueryTerm(item.prop)"><Delete /></el-icon>
    </div>
  </SearchContainer>
</template>

<script setup lang="ts">
import { ref, watch } from "vue"
import { noop } from "lodash-es"
import {
  CountRangeFormItem,
  DaterangeFormItem,
  DepartmentFormItem,
  SearchContainer,
  RangeSelect,
  IcdSelect
} from "@/base-components"
import { QueryTermTypeEnum } from "@/configs"

interface PropsType {
  queryFilters: string
  queryTerm: Array<Record<string, any>>
  deleteTerm: (prop: string) => void
}

const props = withDefaults(defineProps<PropsType>(), {
  queryFilters: "",
  queryTerm: () => [],
  deleteTerm: noop
})

const emits = defineEmits(["update:queryFilters"])

const formState = ref<Record<string, any>>({})

watch(
  () => props.queryTerm,
  val => {
    val.forEach(item => {
      if (!(item.prop in formState.value)) {
        if (item.type === QueryTermTypeEnum.CountRange) {
          formState.value[`${item.prop}From`] = ""
          formState.value[`${item.prop}To`] = ""
        }
        formState.value[item.prop] = ""
      }
    })
  },
  {
    immediate: true,
    deep: true
  }
)

function getFilters() {
  const filters = [] as Array<string>
  props.queryTerm.forEach(item => {
    if (item.type === QueryTermTypeEnum.CountRange) {
      if (formState.value[`${item.prop}From`]) filters.push(`${item.prop}>=${formState.value[item.prop + "From"]}`)
      if (formState.value[`${item.prop}To`]) filters.push(`${item.prop}<=${formState.value[item.prop + "To"]}`)
    }
    // 日期范围字段
    if (Array.isArray(formState.value[item.prop]) && formState.value[item.prop].length === 2) {
      filters.push(
        `${item.prop}>=${formState.value[item.prop][0]}`,
        `${item.prop}<=${formState.value[item.prop][1] + (1000 * 60 * 60 * 24 - 1)}`
      )
      // 其他基础字段
    } else if (formState.value[item.prop]) {
      filters.push(`${item.prop}=${formState.value[item.prop]}`)
    }
  })
  return filters.join(",")
}

// 搜索
function querySearch() {
  const filter = getFilters()
  emits("update:queryFilters", filter)
}
// 重置
function queryReset() {
  for (let key in formState.value) {
    formState.value[key] = ""
  }
  emits("update:queryFilters", "")
}

// 删除搜索项
function deleteQueryTerm(prop) {
  if (props.deleteTerm) {
    delete formState.value[prop]
    const filter = getFilters()
    emits("update:queryFilters", filter)

    props.deleteTerm(prop)
  }
}
</script>

<style lang="less" scoped>
.query-from-item-container {
  display: flex;
  .delete-btn {
    width: 22px;
    height: 22px;
    margin-top: 5px;
    margin-left: 8px;
    font-size: 14px;
    color: #fff;
    background-color: #f56c6c;
    border-radius: 50%;
    padding: 3px;
    box-sizing: border-box;
    cursor: pointer;
  }
}
</style>
