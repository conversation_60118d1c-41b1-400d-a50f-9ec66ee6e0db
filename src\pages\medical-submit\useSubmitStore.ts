import { defineStore } from "pinia"
import { MedicalRecordStatus } from "@/types"

interface SubmitStoreState {
  activeStep: number // 1 - 选择归档文件 2 - 提交
  checkedArchiveDocumentWsidList: string[] // 用户选择的要归档的文件wsid列表
  homePageFlag: boolean // 文档树是否有首页
  status: MedicalRecordStatus | ""
  submitRemark: string // 病案提交备注
}

export const useSubmitStore = defineStore("SUBMIT_STORE", {
  state: (): SubmitStoreState => {
    return {
      activeStep: 1,
      checkedArchiveDocumentWsidList: [],
      homePageFlag: true,
      status: "",
      submitRemark: ""
    }
  }
})
