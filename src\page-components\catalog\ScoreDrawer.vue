<template>
  <!-- 编目评分 -->
  <el-drawer
    v-if="props.drawerAble"
    v-model="drawerVisible"
    direction="rtl"
    :before-close="beforeClose"
    close-on-press-escape
  >
    <template #header="{ close, titleId, titleClass }">
      <div class="header" style="margin-bottom: 0px">
        <div class="score-icon">
          <img :src="QCSvg" />
        </div>
        <div class="qc-result">
          <div :id="titleId" :class="titleClass" class="score-title">{{ props.title }}</div>
          <div class="score-number">
            自动评分：
            <span>{{ $props.totalScore }}</span>
          </div>
        </div>
      </div>
    </template>

    <div v-loading="!$props.totalScore" class="score-content-container">
      <el-tabs v-model="activeName" class="demo-tabs">
        <el-tab-pane :label="'全部（' + nums.all + '）'" name="ERROR_WARN_REMIND"></el-tab-pane>
        <el-tab-pane :label="'错误（' + nums.error + '）'" name="ERROR"></el-tab-pane>
        <el-tab-pane :label="'警告（' + nums.warn + '）'" name="WARN"></el-tab-pane>
        <el-tab-pane :label="'提示（' + nums.remind + '）'" name="REMIND"></el-tab-pane>
      </el-tabs>
      <!-- 总分及查看详情 -->
      <!-- <div class="score-detail">
        <div class="score">
          <span>{{ $props.totalScore }}&nbsp;分</span>
          <el-button v-if="props.pdfKey" type="primary" @click="viewScorePdf">详情</el-button>
        </div>
        <div>修改建议</div>
      </div> -->

      <!-- 接口数据遍历展示 -->
      <div v-for="item in $props.scoreDrawerData" :key="item" class="score-item" :class="item.controlLevelEnum">
        <div class="left">
          <div v-if="item.deductScore" class="score-number">{{ item.deductScore }}</div>
        </div>
        <div class="right">
          <ul>
            <li v-if="item.ruleName">
              <span>{{ item.ruleName }}</span>
            </li>
            <li v-if="item.promptInfo">
              <label>提示：</label>
              <span>{{ item.promptInfo }}</span>
            </li>
            <!-- <li v-if="item.referenceInfo">
              <label>参考：</label>
              <span>{{ item.referenceInfo }}</span>
            </li> -->
          </ul>
        </div>
      </div>
    </div>
  </el-drawer>

  <div
    v-if="!props.drawerAble"
    class="score-container"
    :style="{
      width: drawerVisible ? 'calc(30% - 41px)' : '0px',
      padding: drawerVisible ? '20px' : '0',
      height: drawerVisible ? 'calc(100% - 40px)' : '0'
    }"
  >
    <div class="header">
      <div class="score-icon">
        <img :src="QCSvg" />
      </div>
      <div class="qc-result">
        <div class="score-title">{{ props.title }}</div>
        <div class="score-number">
          自动评分：
          <span>{{ $props.totalScore }}</span>
        </div>
      </div>

      <el-icon style="cursor: pointer" @click="beforeClose"><Close /></el-icon>
    </div>
    <div v-loading="!$props.totalScore" class="score-content-container">
      <el-tabs v-model="activeName" class="demo-tabs">
        <el-tab-pane :label="'全部（' + nums.all + '）'" name="ERROR_WARN_REMIND"></el-tab-pane>
        <el-tab-pane :label="'错误（' + nums.error + '）'" name="ERROR"></el-tab-pane>
        <el-tab-pane :label="'警告（' + nums.warn + '）'" name="WARN"></el-tab-pane>
        <el-tab-pane :label="'提示（' + nums.remind + '）'" name="REMIND"></el-tab-pane>
      </el-tabs>
      <!-- 总分及查看详情 -->
      <!-- <div class="score-detail">
        <div class="score">
          <span>{{ $props.totalScore }}&nbsp;分</span>
          <el-button v-if="props.pdfKey" type="primary" @click="viewScorePdf">详情</el-button>
        </div>
        <div>修改建议</div>
      </div> -->

      <!-- 接口数据遍历展示 -->
      <div
        v-for="item in $props.scoreDrawerData"
        :key="item"
        class="score-item"
        :class="item.controlLevelEnum"
        @click="locateTo(item)"
      >
        <div class="left">
          <div v-if="item.deductScore" class="score-number">{{ item.deductScore }}</div>
        </div>
        <div class="right">
          <ul>
            <li v-if="item.ruleName">
              <span style="font-size: 16px; color: #000; font-weight: 500">{{ item.ruleName }}</span>
            </li>
            <li v-if="item.promptInfo">
              <span>提示：</span>
              <span>{{ item.promptInfo }}</span>
            </li>
            <!-- <li v-if="item.referenceInfo">
              <label>参考：</label>
              <span>{{ item.referenceInfo }}</span>
            </li> -->
          </ul>
        </div>
      </div>
    </div>
  </div>

  <!-- 查看pdf -->
  <el-dialog v-model="state.dialogVisible" width="80%" top="50px" destroy-on-close>
    <div style="height: 600px; overflow-y: auto">
      <PdfPreviewComponent :src="state.documentSrc" :default-width="750" />
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from "vue"
import { PdfPreviewComponent } from "@/base-components"
import QCSvg from "@/assets/svg/other/qc-icon.svg"
const props = defineProps<{
  title: string
  drawerVisible: boolean
  drawerData: Array<any>
  scoreDrawerData: Array<any>
  close: () => void
  pdfKey?: string
  inpNo: string
  totalScore: string | number
  updateDrawerData?: (params: any) => any
  drawerAble?: boolean //抽屉或者侧边固定展示
}>()

const nums = computed(() => {
  return {
    all: props.drawerData.length,
    error: props.drawerData.filter(item => item.controlLevelEnum === "ERROR").length,
    warn: props.drawerData.filter(item => item.controlLevelEnum === "WARN").length,
    remind: props.drawerData.filter(item => item.controlLevelEnum === "REMIND").length
  }
})

const activeName = ref("ERROR_WARN_REMIND")

watch(
  () => activeName.value,
  val => {
    if (val) {
      props.updateDrawerData(val)
    }
  }
)

const emits = defineEmits(["update:drawerVisible", "updateDrawerData", "locateTo"])

const drawerVisible = computed({
  get: () => props.drawerVisible,
  set: val => emits("update:drawerVisible", val)
})

const state = reactive({
  dialogVisible: false,
  documentSrc: ""
})

const beforeClose = () => {
  emits("update:drawerVisible", false)
  if (props.close) props.close()
}

// 查看pdf
const viewScorePdf = () => {
  state.dialogVisible = true
  state.documentSrc = `/api/catalog/catalogs/${props.inpNo}/grade-pdf?key=${props.pdfKey}`
}

const locateTo = item => {
  emits("locateTo", item)
}
</script>

<style lang="less" scoped>
.score-content-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;

  .score-detail {
    width: 100%;
    border-bottom: 1px solid #ddd;
    margin-bottom: 16px;
    padding-bottom: 8px;

    .score {
      display: flex;
      justify-content: space-between;
      color: rgba(10, 22, 51, 1);
      font-size: 16px;
      span {
        font-weight: bold;
      }
    }
  }

  .score-item {
    display: flex;
    margin-bottom: 16px;
    cursor: pointer;

    .left {
      width: 20%;
      display: flex;
      align-items: center;
      justify-content: center;

      .score-number {
        // background-color: red;
        width: 61px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        border-radius: 4px;
        color: #fff;
      }
    }

    .right {
      width: 70%;
      border-left: 3px solid;
      padding: 10px;
      font-size: 14px;

      ul {
        display: flex;
        flex-direction: column;

        li {
          width: 100%;
          label {
            color: #0a1633;
            font-weight: bold;
          }
          span {
            color: rgba(10, 22, 51, 0.7);
          }
        }
      }
    }
  }
}

.REMIND {
  .score-number {
    background: #2bb353;
  }
  .right {
    border-color: #2bb353 !important;
    background: #f4fbf6;
  }
}

.ERROR {
  .score-number {
    background: #f52f3e;
  }
  .right {
    border-color: #f52f3e !important;
    background: #fef8f9;
  }
}

.WARN {
  .score-number {
    background: #ffb540;
  }
  .right {
    border-color: #ffb540 !important;
    background: #fffbf5;
  }
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 28px;
}

.score-container {
  // width: calc(30% - 41px);
  height: calc(100% - 40px);
  overflow-y: auto;
  background-color: #fff;
  border-left: 1px solid #f8f9fc;
  // transition: width 0.3s;
}
.score-title {
  font-weight: bold;
  font-size: 20px;
  color: #0a1633;
  margin-bottom: 11px;
}
.qc-result {
  flex: 1;
}

.score-icon {
  width: 74px;
  height: 74px;
  margin-right: 8px;
}

.score-number {
  font-weight: 500;
  font-size: 14px;
  color: #6c7385;
  span {
    font-weight: bold;
    font-size: 20px;
    color: #ff9d03;
  }
}
</style>
