<template>
  <div :class="$props.separate ? 'common-search-container' : 'common-page-container'">
    <slot></slot>

    <div class="common-search-header">
      <slot name="search"></slot>
    </div>

    <div class="common-search-content">
      <slot name="table"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  separate: { type: Boolean, default: false }
})
</script>
