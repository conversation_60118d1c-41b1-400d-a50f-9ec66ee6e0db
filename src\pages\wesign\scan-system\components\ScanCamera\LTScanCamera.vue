<!-- 良田高拍仪 -->
<template>
  <div style="height: 100%">
    <img v-if="cameraImage" :src="cameraImage" alt="Camera Feed" width="100%" />
    <EmptyContent v-else desc="暂无数据" style="height: 100%" />

    <!-- 高拍仪连接提示弹窗 -->
    <el-dialog
      v-model="wsHintVisible"
      title="高拍仪"
      align-center
      :close-on-click-modal="false"
      :show-close="false"
      :close-on-press-escape="false"
    >
      <el-result v-if="isConnectLoading" title="连接中" sub-title="高拍仪正在连接中，请稍候...">
        <template #icon>
          <el-icon class="is-loading" size="36" color="#409eff"><Loading /></el-icon>
        </template>
      </el-result>
      <el-result
        v-else
        :icon="isExpired || isError ? 'error' : 'success'"
        :title="isExpired ? '响应超时' : isError ? '连接失败' : '连接成功'"
        :sub-title="hintContext"
      >
        <template #extra>
          <el-button v-if="isError || isExpired" type="primary" @click="connectWebSocket">重新连接</el-button>
          <el-button v-else type="primary" @click="wsHintVisible = false">关闭弹窗</el-button>
        </template>
      </el-result>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from "vue"
import { debounce } from "lodash"
import { Loading } from "@element-plus/icons-vue"
import { EmptyContent } from "@/base-components"

const emits = defineEmits(["handleMessage", "updateBarCode"])

const cameraImage = ref("")
const ws = ref<WebSocket | null>(null)

onMounted(() => {
  connectWebSocket()
})

function connectWebSocket() {
  if (!ws.value) {
    isConnectLoading.value = true
    wsHintVisible.value = true
    ws.value = new WebSocket("ws://127.0.0.1:9000")
  }
  ws.value.onopen = function (event) {
    isError.value = false
    isConnectLoading.value = false
    setTimeout(() => {
      wsHintVisible.value = false
    }, 3000)
    isConnecting()
    if (ws.value) {
      // 初始化设备
      ws.value.send(JSON.stringify({ function: "InitDevs", device: 0, resolution: 0, datacallback: true }))
    }
  }
  ws.value.onerror = function (event) {
    if (ws.value) ws.value.close()
  }
  ws.value.onclose = function (event) {
    clearWebsocket()
  }
  ws.value.onmessage = function (event) {
    handleWebSocketMessage(event)
  }
}

// 清除ws
function clearWebsocket(expired = false) {
  if (expired) isExpired.value = true
  else {
    isExpired.value = false
    isError.value = true
  }
  isConnectLoading.value = false
  if (ws.value) {
    ws.value.close()
    ws.value = null
  }
}

// ws信息处理
function handleWebSocketMessage(event) {
  // 只处理JSON格式的消息
  let data
  try {
    data = JSON.parse(event.data)
  } catch {
    // 兼容老协议或非JSON消息
    return
  }
  isConnecting()
  // 拍照指令
  if (data.function === "InitDevs" && data.datacallback) {
    ws.value && ws.value.send(JSON.stringify({ function: "OpenCamera", device: 0, resolution: 0, datacallback: true }))
  } else if (data.function === "ScanImage" && data.value) {
    let imageBase64 = data.value
    if (isReShoot.value) {
      imageBase64 = "recover," + imageBase64
      isReShoot.value = false
    }
    emits("handleMessage", imageBase64)
  }
  if (data.function === "ImageCallback") {
    cameraImage.value = "data:image/jpeg;base64," + data.value
  }
  // 可扩展其它指令
}

// 判断高拍仪设备是否断开（如果3s内没有数据传输，则视为断开连接）
const isConnecting = debounce(() => disconnect(), 3000)
function disconnect() {
  wsHintVisible.value = true
  clearWebsocket(true)
}

// 拍摄图片
function shootImage() {
  ws.value?.send(JSON.stringify({ function: "ScanImage", imagepath: "", colorize: 0, type: true }))
}

// 重拍
const isReShoot = ref(false)
function reShootImage() {
  isReShoot.value = true
  ws.value?.send(JSON.stringify({ function: "ScanImage", imagepath: "", colorize: 0, type: true }))
}

/* ============================= websocket连接失败弹窗 ============================= */
const isConnectLoading = ref(false)
const wsHintVisible = ref(false)
const isError = ref(false)
const isExpired = ref(false)

const hintContext = computed(() => {
  return isExpired.value
    ? "高拍仪响应超时，请检查高拍仪并重新连接"
    : isError.value
    ? "高拍仪连接失败，请接入高拍仪设备，并启动高拍仪插件后再点击重新连接"
    : "高拍仪连接成功，弹窗将会在3秒后自动关闭或手动点击关闭弹窗"
})

defineExpose({ shootImage, reShootImage })
</script>

<style lang="less" scoped>
/* 样式可以根据需要进行调整 */
img {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 5px;
  box-sizing: border-box;
}
</style>
