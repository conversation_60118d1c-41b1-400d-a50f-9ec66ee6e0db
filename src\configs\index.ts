import type { FormItemType } from "@/configs"
export * from "./enums"
export * from "./options"

export const tableLoadingSvg = `
<path class="path" d="
  M 30 15
  L 28 17
  M 25.61 25.61
  A 15 15, 0, 0, 1, 15 30
  A 15 15, 0, 1, 1, 27.99 7.5
  L 15 15
" style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
`

export interface FormItemConfig {
  type: FormItemType
  prop: string
  name: string
  placeholder: string
  desc: string
  width: string
  default: any // 默认值
  customValue?: any // 默认值选了自定义后的值
  required: boolean
  canSearch: boolean // 是否可以作为搜索条件
  canShowColumn: boolean // 是否显示在表格列中
  canShowStatistic: boolean // 是否显示在统计中
  filterRegex?: Array<Record<string, string>> // 文本控件自定义校验规则
  canFilterRegex?: boolean // 是否启用自定义校验规则
  value?: any
  fixed?: boolean //是否固定在顶部

  options?: Array<IBaseOptionItem>

  // 数字输入
  precision?: number //精度
  allowDecimal?: boolean //允许小数
  min?: number
  max?: number
  // 单选/多选（下拉选择）
  multiple?: boolean
  // 日期显示格式
  dateFormat?: string
  // 文本长度限制
  minLength?: number
  maxLength?: number
  // 表单项
  tableFormItem?: boolean //是否为表单项
  tableColumns?: Array<FormItemConfig>
  tableEditData?: Record<string, any>
  tableData?: Array<Record<string, any>>
  //标题
  size?: string //  'small' | 'medium' | 'mini'
  align?: string // 对齐方式
  fontColor?: string // 字体颜色
  content?: string //说明文本的内容
  titleAndOptionsInRow?: boolean // 标题和选项是否在同一行
  showDivider?: boolean //是否显示分割线

  arrangement?: string // 排列方式 'horizontal' | 'vertical' 单选和多选控件才有

  unit?: string //单位,只有数字输入框才有
  canShowTitle?: boolean //是否显示标题
  // 正则（校验）,普通文本框有自定义的正则数组，其他控件正则是字符串
  rule?: string | Array<Record<string, any>>
  ruleMessage?: string
  error?: string
  canEdit?: boolean
  canView?: boolean
  disabled?: boolean

  tableFormConfig?: Array<FormItemConfig>
  addressSelect?: Array<string> // 地址选择器的值
  addressAccuracy?: string //地址精度
  addressAccuracyOption?: Array<Record<string, any>> //地址精度选项

  fieldLinkage?: string //字段联动
  collectSourceType?: "METADATA" | "INTERFACE" | "" //数据来源类别
  collectValueSource?: string //接口地址
  valueRangeKey?: string

  association?: string //诊断关联(表格)

  lineType?: string // 单行、多行

  operate?: boolean // 显示表格操作项

  optionKey?: string //option默认的取值字段

  tableFieldLinkage?: string //表格字段联动

  tableArrangement?: string // 表格排列方式 'list' | 'table'

  column?: number // table平铺时，一行的数量

  tableOperationIcon?: boolean //table操作是否显示图标

  tableOperationConfig?: Array<Record<string, any>> //table操作配置

  tableGlobalConfiguration?: Array<Record<string, any>> //子表单全局配置

  fieldEditable?: string //表单是否可编辑

  tableIndex?: boolean //table是否显示序号

  medicalInsuranceTable?: string // 关联医保表格

  medicalInsuranceCode?: string // 医保编码

  medicalInsuranceName?: string // 医保编码名称

  icdType?: string // ICD编码类型

  pathologicalDiagnosisTable?: string // 关联病理诊断表格

  isLogicForm?: boolean

  logicFormConfig?: Record<string, any> // 逻辑配置表单

  tableDataCountField?: string // table数据计数对应的表单

  mapField?: string //映射字段

  tableDataCountField?: string
}
