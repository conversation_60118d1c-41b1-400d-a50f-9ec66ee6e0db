import axios from "@/interfaces/axios-instance"

// 数据库分页数据
export const getDatabaseHistoryListApi = (params: IPaginationRequestParams) => {
  return axios({
    method: "get",
    url: "/api/collect/his-integration-dbs",
    params: params
  })
}

// 数据库-状态更改
export const changeDatabaseHistoryStatusApi = (wsid: string, status: string) => {
  return axios({
    method: "put",
    url: `/api/collect/his-integration-dbs/${wsid}/${status}`
  })
}

// 数据库-删除
export const deleteDatabaseHistoryApi = (wsid: string) => {
  return axios({
    method: "delete",
    url: `/api/collect/his-integration-dbs/${wsid}`
  })
}

// 数据库-新增
export const addDatabaseHistoryApi = (data: any) => {
  return axios({
    method: "post",
    url: "/api/collect/his-integration-dbs",
    data
  })
}

// 数据库-修改
export const updateDatabaseHistoryApi = (data: any) => {
  return axios({
    method: "put",
    url: `/api/collect/his-integration-dbs/${data.wsid}`,
    data
  })
}

// 接口-分页列表
export const getInterfaceHistoryApi = (params: IPaginationRequestParams) => {
  return axios({
    method: "get",
    url: "/api/collect/his-integration-interfaces",
    params
  })
}

// 接口-更改状态
export const changeInterfaceHistoryStatusApi = (wsid: string, status: string) => {
  return axios({
    method: "put",
    url: `/api/collect/his-integration-interfaces/${wsid}/${status}`
  })
}

// 接口-新增
export const addInterfaceHistoryApi = (data: any) => {
  return axios({
    method: "post",
    url: "/api/collect/his-integration-interfaces",
    data
  })
}

// 接口-编辑
export const updateInterfaceHistoryApi = (data: any) => {
  return axios({
    method: "put",
    url: `/api/collect/his-integration-interfaces/${data.wsid}`,
    data
  })
}

// 接口-删除
export const deleteInterfaceHistoryApi = (wsid: string) => {
  return axios({
    method: "delete",
    url: `/api/collect/his-integration-interfaces/${wsid}`
  })
}

// 接收配置-分页数据
export const getReceptionHistoryApi = (params: IPaginationRequestParams) => {
  return axios({
    method: "get",
    url: "/api/collect/his-push-configs",
    params
  })
}

// 接收配置-新增
export const addReceptionHistoryApi = data => {
  return axios({
    method: "post",
    url: "/api/collect/his-push-configs",
    data: data
  })
}

// 接收配置-编辑
export const updateReceptionHistoryApi = data => {
  return axios({
    method: "put",
    url: `/api/collect/his-push-configs/${data.id}`,
    data: data
  })
}

// 接收配置-删除
export const deleteReceptionHistoryApi = (id: string) => {
  return axios({
    method: "delete",
    url: `/api/collect/his-push-configs/${id}`
  })
}
