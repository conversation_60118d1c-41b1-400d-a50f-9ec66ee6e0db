<template>
  <DialogContainer
    title="提示"
    :width="450"
    :visible="state.visible"
    :confirm-callback="confirm"
    :cancel-callback="closeSealDialog"
    :close-callback="closeSealDialog"
    :confirm-loading="state.confirmLoading"
  >
    <template #default>
      <el-form label-position="top" @submit.enter.prevent="confirm">
        <el-form-item required>
          <template #label>病案已封存，请输入封存密钥</template>
          <el-input v-model="state.secretKey" placeholder="请输入密钥" type="password"></el-input>
        </el-form-item>
      </el-form>
    </template>
  </DialogContainer>
</template>

<script setup lang="ts">
import { reactive, nextTick, toRef } from "vue"
import { DialogContainer } from "@/base-components"
import { checkSealedApi, getCheckKeyApi } from "@/interfaces"
import { useSystemStore } from "@/stores"
import { Message, toastError } from "@/utils"

const systemStore = useSystemStore()

interface SealDialogProps {
  confirmCallback: () => void
  selectedRow: Record<string, any> | null // 用户所点击的病案
}

const props = defineProps<SealDialogProps>()

const state = reactive({
  visible: false, // 弹窗显示
  secretKey: "", // 用户输入的密钥
  confirmLoading: false // 点击确认时的loading
})

// 检查病案是否封存
const checkSealed = () => {
  nextTick(() => {
    if (!props.selectedRow) return
    systemStore.showLoading()
    checkSealedApi(props.selectedRow?.inpNo)
      .then(isSealed => {
        systemStore.hideLoading()
        // 已封存则打开弹窗让用户输入密码
        if (isSealed) {
          state.visible = true
        } else {
          props.confirmCallback()
          closeSealDialog()
        }
      })
      .catch(error => {
        systemStore.hideLoading()
        toastError(error)
      })
  })
}

// 用户提交密钥
const confirm = () => {
  if (!state.secretKey) {
    Message.error("请输入密钥！")
    return
  }
  state.confirmLoading = true
  getCheckKeyApi({ inpNo: props.selectedRow?.inpNo, sealKey: state.secretKey })
    .then(isCorrect => {
      state.confirmLoading = false
      if (isCorrect) {
        props.confirmCallback()
        closeSealDialog()
      } else {
        Message.error("密钥错误，请重新输入")
      }
    })
    .catch(error => {
      toastError(error)
      state.confirmLoading = false
    })
}

// 关闭弹窗
const closeSealDialog = () => {
  state.visible = false
  state.secretKey = ""
}

defineExpose({ checkSealed, secretKey: toRef(state, "secretKey") })
</script>
