// 中文姓名校验
export const realNameValidator = (_rule: any, value: string, callback: (err?: Error | string) => void) => {
  const realNameReg = /^[\u4e00-\u9fa5]{2,20}$/
  if (!value) callback()
  else if (realNameReg.test(value)) callback()
  else callback(new Error("姓名格式不正确"))
}

// 工号校验
export const jobIdValidator = (_rule: any, value: string, callback: (err?: Error) => void) => {
  // const jobIdReg = /^[0-9]{2,20}$/
  const jobIdReg = /^[A-Za-z0-9]{2,20}$/
  if (!value) callback()
  else if (jobIdReg.test(value)) callback()
  else callback(new Error("工号格式不正确，请输入2-20位纯数字"))
}

// 电话号码校验
export const phoneNumberValidator = (_rule: any, value: string, callback: (err?: Error) => void) => {
  const mobilePhoneReg = /^1[3-9][0-9]{9}$/ // 手机号正则
  const telephoneReg = /[0-9]{3,4}[-][0-9]{8}/ // 固定电话正则
  if (!value) callback()
  else if (mobilePhoneReg.test(value) || telephoneReg.test(value)) callback()
  else callback(new Error("电话号码格式不正确"))
}

// 身份证号校验
export const IdNumberValidator = (_rule: any, value: string, callback: (err?: Error) => void) => {
  const id18Reg = /^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/ // 18位身份证
  const id15Reg = /^([1-6][1-9]|50)\d{4}\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}$/ // 15位身份证
  if (!value) callback()
  else if (id18Reg.test(value) || id15Reg.test(value)) callback()
  else callback(new Error("身份证号格式不正确"))
}

// 邮箱校验
export const emailValidator = (_rule: any, value: string, callback: (err?: Error) => void) => {
  const emailReg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(.[a-zA-Z0-9_-]+)+$/
  if (!value) callback()
  else if (emailReg.test(value)) callback()
  else callback(new Error("邮箱格式不正确"))
}

// 校验编码
export const variableCodeValidator = (_rule: any, value: string, callback: (err?: Error) => void) => {
  if (value === "") {
    callback(new Error("请输入变量代码"))
  } else {
    const reg = /(?=.*[a-z])|(?=.*\d)|(?=.*[#@!~%^&*])[a-z\d#@!~%^&*]{0,30}/i
    const regText = /^[^\u4e00-\u9fa5]{0,}$/
    if (reg.test(value) && regText.test(value)) {
      callback()
    } else {
      callback(new Error("请输入最多30位含字母、数字或符号的变量代码"))
    }
  }
}

// 校验密码
export const passwordValidator = (_rule: any, value: string, callback: (err?: Error) => void) => {
  if (value === "") {
    callback(new Error("请输入密码"))
  } else {
    const reg =
      /((^(?=.*[a-z])(?=.*[A-Z])(?=.*[^A-Za-z0-9])[\da-zA-Z_\W]{6,16}$)|(^(?=.*\d)(?=.*[A-Z])(?=.*[^A-Za-z0-9])[\da-zA-Z_\W]{6,16}$)|(^(?=.*\d)(?=.*[a-z])(?=.*[^A-Za-z0-9])[\da-zA-Z_\W]{6,16}$)|(^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])[\da-zA-Z_\W]{6,16}$))/
    if (reg.test(value)) {
      callback()
    } else {
      callback(new Error("请输入至少6位数字、大小写字母和字符的组合"))
    }
  }
}

// 域名校验
export const ipValidator = (_rule: any, value: string, callback: (err?: Error | string) => void) => {
  const ipReg = /^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]{1,63}\.)+[a-zA-Z]{2,6}(\/)?$/
  if (!value) callback()
  else if (ipReg.test(value)) callback()
  else callback(new Error("域名格式不正确"))
}

// 域名校验
export const directoryValidator = (_rule: any, value: string, callback: (err?: Error | string) => void) => {
  const directoryReg = /^[\\\/]?([a-zA-Z0-9\u4e00-\u9fa5_\-]+[\\\/]?)+$/
  if (!value) callback()
  else if (directoryReg.test(value)) callback()
  else callback(new Error("保存目录地址格式不正确"))
}
