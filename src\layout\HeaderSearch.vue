<template>
  <div class="header-search-wrapper" @keydown.enter="handleContainerEnter">
    <el-autocomplete
      ref="autoCompleteRef"
      v-model="menuSearchState.searchValue"
      class="header-search-container"
      :fetch-suggestions="querySearch"
      placeholder="请输入功能关键词进行搜索"
      :debounce="300"
      :teleported="false"
      clearable
      @select="handleJump"
      @keydown.enter="handleEnter"
      @blur="handleBlur"
    >
      <template #prefix>
        <el-icon>
          <Search />
        </el-icon>
      </template>
      <template #default="{ item }">
        <el-empty v-if="item?.empty" :image="NoDataImage" :description="item?.name" :image-size="60" />
        <span
          v-else
          class="header-search__content"
          @mouseenter="handleActiveSearchMenu(item)"
          @mouseleave="handleUnActiveSearchMenu()"
        >
          <span>
            <i class="search-icon" :class="getSearchedMenuInfo(item.path)?.icon"></i>
            <span class="search-title">{{ getSearchedMenuInfo(item.path)?.label }}：</span>
            <HighlightText :value="item.name" :keyword="menuSearchState.searchValue" />
          </span>

          <i v-if="menuSearchState.activeSearchMenu === item?.path" class="ri-corner-down-left-line"></i>
        </span>
      </template>
    </el-autocomplete>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import { useRouter } from "vue-router"
import { Search } from "@element-plus/icons-vue"
import { HighlightText } from "@/base-components"
import NoDataImage from "@/assets/png/nodata.png"
import { useSystemStore } from "@/stores"

const systemStore = useSystemStore()
const router = useRouter()

interface MenuType {
  name: string
  path: string
  empty?: boolean
}
/*==================顶部搜素===================*/
const menuSearchState = reactive({
  searchValue: "",
  activeSearchMenu: "",
  searchMenus: [] as Array<MenuType>
})

const handleActiveSearchMenu = (item: Record<string, string>) => {
  menuSearchState.activeSearchMenu = item.path
}

const handleUnActiveSearchMenu = () => {
  menuSearchState.activeSearchMenu = ""
}

// 组件 enter ,通过 activeSearchMenu 拿到当前高亮的菜单，然后跳转
const handleContainerEnter = () => {
  const hightLightMenu = menuSearchState.searchMenus.find(item => item.path === menuSearchState.activeSearchMenu)
  if (hightLightMenu) {
    autoCompleteRef.value.handleSelect(hightLightMenu)
  }
}

// 根据搜索值查询菜单
const querySearch = (queryString: string, cb: any) => {
  const allMenus = systemStore.availablePages

  const results = queryString
    ? allMenus.filter((item: Record<string, string>) => {
        return item.name?.includes(queryString) || item.path?.includes(queryString)
      })
    : []
  menuSearchState.searchMenus = [...results]
  if (results.length === 0) {
    cb([{ name: "暂无匹配结果", path: "", empty: true }])
    return
  }
  cb(results)
}

const autoCompleteRef = ref()

// 搜索菜单回车
const handleEnter = () => {
  if (autoCompleteRef.value.suggestions.length > 0) {
    autoCompleteRef.value.handleSelect(autoCompleteRef.value.suggestions[0])
    menuSearchState.searchValue = ""
  }
}

// 处理失去焦点
const handleBlur = () => {
  autoCompleteRef.value.handleSelect({})
}

// 路由跳转
const handleJump = (item: Record<string, string>) => {
  if (item?.path) {
    router.push({ path: item?.path })
    menuSearchState.searchValue = ""
  }
}

// 通过path确定是几级菜单
const getSearchedMenuInfo = (path: string) => {
  const pathArr = path?.split("/")?.filter(item => item?.length > 0)
  if (!pathArr || pathArr?.length === 0) return null
  if (pathArr?.length === 1) {
    return { label: "一级菜单", icon: "ri-price-tag-fill" }
  } else if (pathArr?.length === 2) {
    return { label: "二级子菜单", icon: "ri-list-unordered" }
  } else {
    return { label: "模块功能项", icon: "ri-function-fill" }
  }
}
</script>

<style scoped lang="less">
.header-search-wrapper {
  flex: 1;
  margin-left: 24px;
  .header-search-container {
    width: 350px;
  }
  :deep(.el-input) {
    width: 350px;
  }
  .header-search__content {
    display: flex;
    justify-content: space-between;
    .search-icon {
      padding-right: 10px;
    }
    .search-title {
      font-weight: 500;
    }
  }
}
</style>
