import axios from "@/interfaces/axios-instance"

/**
 * @method GET
 * @desc   保密管理-保密数据列表
 */
export function getSecrecyListApi(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: "/api/document/bag/secrecy/query",
    params
  })
}

/**
 * @method GET
 * @desc   保密管理-保密下拉选项
 */
export function getSecrecyOptionsApi() {
  return axios({
    method: "get",
    url: "/api/catalog/choice/option"
  })
}

/**
 * @method GET
 * @desc   保密管理-模糊查询诊断名称（暂未使用）
 */
export function searchKeywordDiagnosisApi(keyword: string) {
  return axios({
    method: "get",
    url: `/api/hospital/icd/fuzzy-query?name=${keyword}`
  }).then(res => (res.data?.data ?? []) as CommonObjectList)
}

/**
 * @method GET
 * @desc   保密管理-模糊查询手术名称
 */
export function searchKeywordOperationApi(keyword: string) {
  return axios({
    method: "get",
    url: `/api/hospital/icd-cm/fuzzy-query?name=${keyword}`
  }).then(res => (res.data?.data ?? []) as CommonObjectList)
}

/**
 * @method POST
 * @desc   保密管理-保密等级设置
 */
export function setSecrecyConfigApi(obj) {
  const { documentBagWsids = "", secrecyScope = "", secrecyGrade = "", documentWsids = [] } = obj
  return axios({
    method: "post",
    url: "/api/document/bag/secrecy",
    data: {
      documentBagWsids,
      secrecyScope,
      secrecyGrade,
      documentWsids
    }
  })
}

/**
 * @method GET
 * @desc   保密管理-保密详情查询
 */
export function getSecrecyDetailApi(inpNo: string) {
  return axios({
    method: "get",
    url: `/api/document/document-bags/class/${inpNo}`
  })
}

/**
 * @method GET
 * @desc   病案文档-批量保密详情查询
 */
export function getMultipleSecrecyDetailApi(obj) {
  const { documentBagWsids = [] } = obj
  return axios({
    method: "post",
    url: "/api/document/bag/get-batch",
    data: {
      documentBagWsids
    }
  })
}
