import type { SearchFormConfigItem, TableColumnItem } from "@/types"

export enum PushStatusTypeEnum {
  ENABLE = "ENABLE", // 启用
  DISABLE = "DISABLE" // 禁用
}

export enum PushMethodEnum {
  GET = "GET", // 立即发送
  POST = "POST" // 指定时间
}

// 消息推送搜索表单配置
export const messagePushSearchFormConfig: Array<SearchFormConfigItem> = [
  { type: "select", label: "厂商系统", prop: "systemWsid", options: [] },
  {
    type: "select",
    label: "状态",
    prop: "status",
    options: [
      { label: "启用", value: PushStatusTypeEnum.ENABLE },
      { label: "禁用", value: PushStatusTypeEnum.DISABLE }
    ]
  }
]

// 消息推送表格项
export const messagePushTableColumns: Array<TableColumnItem> = [
  { prop: "systemWsid", label: "厂商系统", minWidth: 250 },
  { prop: "url", label: "URL", minWidth: 180 },
  { prop: "pushMethod", label: "推送方式", minWidth: 100 },
  { prop: "status", label: "状态", minWidth: 100 },
  { prop: "operation", label: "操作", width: 150, fixed: "right" }
]

