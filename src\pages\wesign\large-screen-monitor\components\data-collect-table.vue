<template>
  <div class="data-collect">
    <div class="data-collect-title"></div>
    <div class="data-collect-table">
      <el-table
        v-el-table-infinite-scroll="infiniteLoad"
        v-loading="tableLoading"
        :data="collectTaskRecords"
        :header-cell-style="{ background: 'rgba(12,67,132,0.8)', color: '#fff' }"
        :row-class-name="tableRowClassName"
        :row-style="TableRowStyle"
        :infinite-scroll-disabled="isDisableScroll"
        :infinite-scroll-immediate="false"
        height="360"
        max-height="360"
      >
        <template v-for="item in dataCollectColumn" :key="item">
          <el-table-column :prop="item.prop" :label="item.label" :min-width="item.minWidth" />
        </template>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, onBeforeUnmount } from "vue"
import { default as vElTableInfiniteScroll } from "el-table-infinite-scroll"
import { formatDate, toastError } from "@/utils"
import { dataCollectColumn, CollectRecords, CollectStatusEnum } from "../config"
import { getCollectRecordsApi } from "../interface"

let collectTaskRecords = reactive<Array<CollectRecords>>([])

const tableLoading = ref(false)

// 总页数
const totalPage = ref(0)

// 是否禁止表格滚动
const isDisableScroll = ref(false)

const pageConfig = reactive({
  currentPage: 0,
  pageSize: 10
})
const requestParams = reactive({
  params: { offset: pageConfig.pageSize * pageConfig.currentPage, limit: pageConfig.pageSize }
})

// 今日数据采集刷新间隔
const intervalId = ref()

onMounted(() => {
  getCollectRecords()
  intervalId.value = setInterval(getCollectRecords, 300000)
})

onBeforeUnmount(() => {
  clearInterval(intervalId.value)
})

// 获取数据采集记录
function getCollectRecords() {
  tableLoading.value = true
  getCollectRecordsApi(requestParams.params).then(res => {
    collectTaskRecords = formatData(res.data.data.rows)
    totalPage.value = res.data.data.page.totalPages
    tableLoading.value = false
  }).catch(err => {
    toastError(err)
    tableLoading.value = false
  })
}

// 实现懒加载
function infiniteLoad() {
  if (isDisableScroll.value) return
  pageConfig.currentPage++
  if (pageConfig.currentPage <= totalPage.value) {
    requestParams.params.offset = pageConfig.pageSize * pageConfig.currentPage
    requestParams.params.limit = pageConfig.pageSize
    getCollectRecordsApi(requestParams.params).then( res => {
      const addData = formatData(res.data.data.rows)
      collectTaskRecords = collectTaskRecords.concat(addData)
    } )
  }
  // 如果当前页码大于总页数，则禁止滚动
  if (pageConfig.currentPage > totalPage.value) {
    isDisableScroll.value = true
  }
}

function formatData(data) {
  return data.map(item => {
    if (item.collectStatusEnum === CollectStatusEnum.COLLECT_SUCCESS) {
      item.collectStatus = "成功"
    } else if (item.collectStatusEnum === CollectStatusEnum.COLLECT_FAIL) {
      item.collectStatus = "失败"
    } else {
      item.collectStatus = "待采集"
    }
    item.collectDateTime = formatDate(item.collectDateTime)
    return item
  })
}
// 更改table样式
function tableRowClassName({ row }) {
  if (row.collectStatus === "失败") {
    return "fail-row"
  } else {
    return ""
  }
}

function TableRowStyle({ rowIndex }) {
  if (rowIndex % 2 === 1) {
    return { background: "rgb(7,31,72)" }
  }
}
</script>

<style lang="less" scoped>
.data-collect {
  width: 28%;
  min-width: 458px;
  .data-collect-title {
    width: 100%;
    height: 43px;
    background: url("@/assets/svg/monitor/data-collection.svg") no-repeat;
    background-size: 100%;
  }
  .data-collect-table {
    margin-top: 15px;
    :deep(.el-table) {
      color: rgba(255, 255, 255, 0.7);
      font-weight: 500;
      border-bottom: 1px solid #051335;
      .el-table__body-wrapper {
        background: #051335;
      }
      .el-table__row {
        background: #051335;
      }
      .el-table__row.fail-row {
        font-weight: 500;
        color: #ff5059;
      }
      tr:hover > td {
        background-color: transparent !important;
      }
      td.el-table__cell, th.el-table__cell.is-leaf {
        border-bottom: 1px solid #051335;
      }
    }
    :deep(.el-loading-mask) {
      background: #051335;
    }
  }
}
</style>
