<template>
  <el-form-item :label="props.label" :prop="props.prop">
    <el-input
      v-model.trim="commonInputValue"
      :placeholder="props.placeholder ? props.placeholder : `请输入${props.label}`"
      :disabled="props.disabled"
      :clearable="props.clearable"
      :style="{ width: props.width }"
    />
  </el-form-item>
</template>

<script setup lang="ts">
import { computed } from "vue"

const props = withDefaults(
  defineProps<{
    label: string
    modelValue: string
    prop?: string
    width?: string
    placeholder?: string
    disabled?: boolean
    clearable?: boolean
  }>(),
  { prop: "", width: "100%", placeholder: "", disabled: false, clearable: false, trim: true }
)

const emits = defineEmits(["update:modelValue"])

const commonInputValue = computed({
  get: () => props.modelValue,
  set: val => emits("update:modelValue", val)
})
</script>
