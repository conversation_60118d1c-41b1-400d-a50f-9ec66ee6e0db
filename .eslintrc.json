{
  "root": true,
  "env": {
    "browser": true,
    "node": true
  },
  "extends": [
    "eslint:recommended",
    "plugin:import/recommended",
    "plugin:import/typescript",
    "plugin:vue/vue3-recommended",
    "plugin:@typescript-eslint/recommended"
  ],
  "parser": "vue-eslint-parser",
  "parserOptions": {
    "ecmaVersion": 12,
    "parser": "@typescript-eslint/parser",
    "sourceType": "module"
  },
  "plugins": ["vue", "@typescript-eslint"],
  "settings": {
    "import/resolver": {
      "typescript": true,
      "node": true
    }
  },
  "rules": {
    // common
    "no-unused-vars": "off",
    "no-var": "error",
    "no-console": "warn",
    "eqeqeq": "error",

    // ts
    "@typescript-eslint/no-unused-vars": "warn",
    "@typescript-eslint/no-empty-function": "warn",
    "@typescript-eslint/ban-ts-comment": "warn",

    // vue
    "vue/multi-word-component-names": "off",
    "vue/eqeqeq": "error",
    "vue/html-self-closing": "off",
    "vue/singleline-html-element-content-newline": "off",
    "vue/no-unused-vars": "warn",
    "vue/max-attributes-per-line": [
      "warn",
      {
        "singleline": {
          "max": 10
        },
        "multiline": {
          "max": 1
        }
      }
    ],

    // import
    "import/first": "error",
    "import/no-duplicates": "error",
    "import/order": [
      "warn",
      {
        "groups": ["builtin", "external", "internal", "parent", "sibling", "index", "type", "unknown"],
        "pathGroups": [
          {
            "pattern": "vue",
            "group": "external",
            "position": "before"
          },
          {
            "pattern": "vue-router",
            "group": "external",
            "position": "before"
          },
          {
            "pattern": "element-plus",
            "group": "external",
            "position": "before"
          },
          {
            "pattern": "@element-plus/icons-vue",
            "group": "external",
            "position": "after"
          },
          {
            "pattern": "@/base-components",
            "group": "internal",
            "position": "before"
          },
          {
            "pattern": "@/page-components",
            "group": "internal",
            "position": "before"
          }
        ],
        "alphabetize": {
          "order": "asc",
          "caseInsensitive": false
        },
        "pathGroupsExcludedImportTypes": ["type"]
      }
    ]
  }
}
