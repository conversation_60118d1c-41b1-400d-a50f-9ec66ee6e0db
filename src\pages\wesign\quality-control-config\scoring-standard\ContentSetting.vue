<template>
  <div v-loading="loading" class="home-page-container common-search-container">
    <div class="common-search-header">
      <TabsRouter :tabs-config="contentTabsRouterList" />
    </div>

    <div class="header">
      <el-button type="primary" :icon="Plus" @click="addClass('PROBLEM_CATEGORIES', treeData)">问题大类</el-button>
      <el-button type="primary" :icon="Check" @click="save">保存</el-button>
    </div>

    <div class="body">
      <!-- level-1数据 （问题大类） -->
      <div v-for="(item, index) in treeData" :key="item.wsid" class="level">
        <div class="content level-1-content">
          <el-button
            v-if="item.children && item.children.length > 0"
            link
            :icon="item.expand ? Minus : Plus"
            @click="item.expand = !item.expand"
          ></el-button>

          <el-form :rules="categoryRule" :model="item">
            <el-form-item prop="name">
              <div class="label-name form-item">
                <el-input v-model="item.name" placeholder="问题大类" />
              </div>
            </el-form-item>

            <el-form-item prop="documentType">
              <div class="document-type form-item">
                <el-select v-model="item.documentType" placeholder="文档类型">
                  <el-option
                    v-for="option in documentTypeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  ></el-option>
                </el-select>
              </div>
            </el-form-item>
            <el-form-item prop="totalScore">
              <div class="totalScore form-item">
                <el-input
                  v-model="item.totalScore"
                  placeholder="分值"
                  type="number"
                  min="0"
                  @input="scanScore($event, item, 'totalScore')"
                />
              </div>
            </el-form-item>
          </el-form>

          <div class="operation-container">
            <el-button link type="primary" :icon="Plus" @click="addClass('PROBLEM_SUBCLASS', item)">问题子类</el-button>
            <el-button link type="primary" :icon="Plus" @click="addClass('SCORE_ITEM', item)">评分项</el-button>
            <el-button link type="primary" :icon="Delete" @click="deleteClass(item, item.id, index)">删除</el-button>
          </div>
        </div>

        <!-- level-2数据 （问题子类） -->
        <div v-show="item.expand" class="children-container">
          <div v-for="(data, dataIndex) in item.children" :key="data.wsid" class="level">
            <div class="content level-2-content">
              <el-button
                v-if="data.children && data.children.length > 0"
                link
                :icon="data.expand ? Minus : Plus"
                @click="data.expand = !data.expand"
              ></el-button>

              <el-form :model="data" :rules="subclassRule">
                <el-form-item prop="name">
                  <div class="label-name form-item">
                    <el-input v-model="data.name" :placeholder="data.type === 'SCORE_ITEM' ? '评分项' : '问题子类'" />
                  </div>
                </el-form-item>

                <!-- 问题大类可直接添加评分项 -->
                <template v-if="data.type === 'SCORE_ITEM'">
                  <el-form-item prop="singleReject">
                    <div class="single-reject form-item">
                      <el-select v-model="data.singleReject" placeholder="单项否决">
                        <el-option
                          v-for="option in singleRejectOptions"
                          :key="option.value"
                          :label="option.label"
                          :value="option.value"
                        ></el-option>
                      </el-select>
                    </div>
                  </el-form-item>

                  <el-form-item prop="rejectLevel">
                    <div v-if="data.singleReject === 'YES'" class="reject-level form-item">
                      <el-select v-model="data.rejectLevel" placeholder="否决评分级别">
                        <el-option
                          v-for="option in rejectLevelOptions"
                          :key="option.value"
                          :label="option.label"
                          :value="option.value"
                        ></el-option>
                      </el-select>
                    </div>
                  </el-form-item>

                  <el-form-item prop="totalScore">
                    <div class="totalScore form-item">
                      <el-input
                        v-model="data.totalScore"
                        :placeholder="'单项最高可扣分'"
                        type="number"
                        min="0"
                        @input="scanScore($event, data, 'totalScore')"
                      />
                    </div>
                  </el-form-item>

                  <el-form-item prop="score">
                    <div class="score form-item">
                      <el-input
                        v-model="data.score"
                        placeholder="扣分"
                        type="number"
                        min="0"
                        @input="scanScore($event, data, 'score')"
                      />
                    </div>
                  </el-form-item>
                </template>

                <el-form-item v-if="data.type !== 'SCORE_ITEM'" prop="totalScore">
                  <div class="totalScore form-item">
                    <el-input
                      v-model="data.totalScore"
                      :placeholder="'分值'"
                      type="number"
                      min="0"
                      @input="scanScore($event, data, 'totalScore')"
                    />
                  </div>
                </el-form-item>
              </el-form>

              <div class="operation-container">
                <el-button
                  v-if="data.type === 'PROBLEM_SUBCLASS'"
                  link
                  type="primary"
                  :icon="Plus"
                  @click="addClass('SCORE_ITEM', data)"
                >
                  评分项
                </el-button>
                <el-button link type="primary" :icon="Delete" @click="deleteClass(data, data.id, dataIndex)">
                  删除
                </el-button>
              </div>
            </div>

            <!-- level-3数据 （评分项） -->
            <div v-show="data.expand" class="children-container">
              <div v-for="(node, nodeIndex) in data.children" :key="node.wsid" class="level">
                <div class="content level-3-content">
                  <el-form :model="node" :rules="subclassRule">
                    <el-form-item prop="name">
                      <div class="label-name form-item">
                        <el-input v-model="node.name" placeholder="评分项" />
                      </div>
                    </el-form-item>

                    <el-form-item prop="singleReject">
                      <div class="single-reject form-item">
                        <el-select v-model="node.singleReject" placeholder="单项否决">
                          <el-option
                            v-for="option in singleRejectOptions"
                            :key="option.value"
                            :label="option.label"
                            :value="option.value"
                          ></el-option>
                        </el-select>
                      </div>
                    </el-form-item>

                    <el-form-item prop="rejectLevel">
                      <div v-if="node.singleReject === 'YES'" class="reject-level form-item">
                        <el-select v-model="node.rejectLevel" placeholder="否决评分级别">
                          <el-option
                            v-for="option in rejectLevelOptions"
                            :key="option.value"
                            :label="option.label"
                            :value="option.value"
                          ></el-option>
                        </el-select>
                      </div>
                    </el-form-item>

                    <el-form-item prop="totalScore">
                      <div class="totalScore form-item">
                        <el-input
                          v-model="node.totalScore"
                          placeholder="单项最高可扣分"
                          type="number"
                          min="0"
                          @input="scanScore($event, node, 'totalScore')"
                        />
                      </div>
                    </el-form-item>

                    <el-form-item prop="score">
                      <div class="score form-item">
                        <el-input
                          v-model="node.score"
                          placeholder="扣分"
                          type="number"
                          min="0"
                          @input="scanScore($event, node, 'score')"
                        />
                      </div>
                    </el-form-item>
                  </el-form>

                  <div class="operation-container">
                    <el-button link type="primary" :icon="Delete" @click="deleteClass(node, node.id, nodeIndex)">
                      删除
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue"
import { useRoute, useRouter } from "vue-router"
import { Delete, Plus, Minus, Check } from "@element-plus/icons-vue"
import { TabsRouter } from "@/page-components"
import { queryScoreStandard } from "@/interfaces"
import { SystemPrompt, Message, extractErrorMsg } from "@/utils"
import { batchUpdateGivenScoreStandard, deleteGivenScoreStandard } from "../interface"
import {
  contentTabsRouterList,
  categoryRule,
  subclassRule,
  documentTypeOptions,
  singleRejectOptions,
  rejectLevelOptions
} from "./config"

/* ======================== 编辑评分标准相关数据及方法 ======================== */

interface Level {
  type: "PROBLEM_CATEGORIES" | "PROBLEM_SUBCLASS" | "SCORE_ITEM" //数据类型
  wsid: string
  parentWsid: string
  documentType: "FIRST_HOME_PAGE" //文档类型
  label: string //
  level: number //层级
  name: string
  score?: string | number //扣分
  totalScore?: string | number //单项总分
  singleReject?: "YES" | "NO" | "" //单项否决
  rejectLevel?: "LEVE_ONE" | "LEVE_TWO" | "LEVE_THREE" | "" //否决评分级别
  status?: "ENABLE" | "DISABLE"
  children?: []
  id?: number
}

const treeData = ref<Array<Record<string, any>>>([])

interface DeleteGivenScoreStandardParam {
  wsid: string
  reconfirm: boolean
}
// 记录删除的数据
const deleteData = ref<DeleteGivenScoreStandardParam>({ wsid: "", reconfirm: false })
const loading = ref(false)
const route = useRoute()
const router = useRouter()
// 加载评分项
const getData = scoreType => {
  if (loading.value) return
  loading.value = true
  queryScoreStandard({ documentType: "", scoreType: scoreType })
    .then(resData => {
      treeData.value = resData
      if (localStorage.getItem("importData")) {
        const importData =
          localStorage.getItem("importData")[0] === "[" ? JSON.parse(localStorage.getItem("importData")) : []
        treeData.value = resData.concat(importData)
      }
      treeData.value.forEach(item => {
        item.expand = true
        item.id = Math.floor(Math.random() * 1000000)
        if (item.children) {
          item.children.forEach(data => {
            data.expand = true
            data.id = Math.floor(Math.random() * 1000000)
            if (data.type === "SCORE_ITEM") data.rejectLevel = data.rejectLevel === "UNKNOWN" ? "" : data.rejectLevel
            if (data.children) {
              data.children.forEach(node => {
                node.id = Math.floor(Math.random() * 1000000)
                node.rejectLevel = node.rejectLevel === "UNKNOWN" ? "" : node.rejectLevel
              })
            }
          })
        }
      })
    })
    .finally(() => {
      loading.value = false
    })
}

// 页面初始化
onMounted(() => {
  getData(route.query.scoreType)
})

const levelType = {
  PROBLEM_CATEGORIES: 1,
  PROBLEM_SUBCLASS: 2,
  SCORE_ITEM: 3
}

// 添加子类
const addClass = (type: "PROBLEM_CATEGORIES" | "PROBLEM_SUBCLASS" | "SCORE_ITEM", node) => {
  // 问题大类添加问题子类、评分项提示
  if (node.type === "PROBLEM_CATEGORIES") {
    if (node.children && node.children.some(child => child.type !== type)) {
      Message.warning(`问题子类与评分项不能在同一层级，请重新选择。`)
      return
    }
  }
  node.expand = true
  // 问题大类添加的评分项 level为2
  const level = type === "SCORE_ITEM" && node.type === "PROBLEM_CATEGORIES" ? 2 : levelType[type]
  const data: Level = {
    type: type,
    wsid: "",
    parentWsid: node.length ? "" : node.wsid,
    documentType: "FIRST_HOME_PAGE",
    level: level,
    name: "",
    label: "",
    score: "",
    totalScore: "",
    status: "ENABLE",
    children: [],
    singleReject: "",
    rejectLevel: "",
    id: Math.floor(Math.random() * 1000000) // 记录新添加的子类
  }
  if (node.children) {
    node.children.push(data)
  } else {
    node.push(data)
  }
}

//删除子类
const deleteClass = (node, id, nodeIndex) => {
  // 调用接口删除
  if (node.wsid) {
    deleteData.value = { wsid: node.wsid, reconfirm: false }
    deleteGivenScoreStandard(deleteData.value)
      .then(res => {
        // result值为false表示有关联评分规则，需要进行二次确认
        if (!res.data.data.result) {
          SystemPrompt(`您确定要删除${node.name}`).then(() => {
            deleteGivenScoreStandard(deleteData.value)
          })
        }
        getData(route.query.scoreType)
      })
      .catch(err => {
        Message.error(extractErrorMsg(err, "删除评分标准失败"))
      })
  } else {
    // 问题大类
    if (node.type === "PROBLEM_CATEGORIES") {
      treeData.value.splice(nodeIndex, 1)
    } else {
      treeData.value.forEach(item => {
        if (item.children) {
          item.children.forEach(data => {
            // 删除level=2的数据
            if (data.parentWsid === node.parentWsid) {
              item.children = item.children.filter(child => child?.id !== id)
            }

            if (data.children) {
              data.children.forEach(child => {
                // 删除level=3的数据
                if (child.parentWsid === node.parentWsid) {
                  data.children = data.children.filter(child => child?.id !== id)
                }
              })
            }
          })
        }
      })
    }
  }
}

// 保存数据
const save = async () => {
  if (loading.value) return
  loading.value = true
  treeData.value.forEach(category => {
    category.score = category.totalScore
    category.singleReject = "NO"
    category.scoreType = route.query.scoreType
    if (category.children) {
      category.children.forEach(subclass => {
        subclass.scoreType = route.query.scoreType
        if (subclass.type === "PROBLEM_SUBCLASS") {
          subclass.score = subclass.totalScore
          subclass.singleReject = "NO"
          if (subclass.children) {
            subclass.children.forEach(item => {
              item.scoreType = route.query.scoreType
            })
          }
        }
      })
    }
  })

  await batchUpdateGivenScoreStandard(treeData.value)
    .then(() => {
      loading.value = false
      Message.success("保存成功")
      localStorage.removeItem("importData")
      getData(route.query.scoreType)
      // router.push({
      //   path: route.path,
      //   query: {
      //     scoreType: route.query.scoreType
      //   }
      // })
    })
    .catch(err => {
      loading.value = false
      Message.error(extractErrorMsg(err, "保存失败"))
    })
}

/* ======================== 数据校验 ======================== */

/**
 * @val 输入值
 * @node 当前输入的节点
 * @key 当前节点在数组中对应的key值
 */
const scanScore = (val, node, key) => {
  let newVal = Math.abs(Number(val))

  //问题大类分值 <= 100
  let categoryScore = treeData.value.reduce((totalScore, next) => {
    return totalScore + Number(next.totalScore)
  }, 0)

  treeData.value.forEach(category => {
    /* ======================== level-1 ======================== */

    category[key] = category.id === node.id ? newVal : category[key]
    if (categoryScore > 100 && category.id === node.id) {
      Message.warning(`问题大类《${node.name}》项分值总和超过最大分值100，请重新输入`)
      category[key] = 0
      return
    }

    //问题大类分值 >= 其下所有评分项（问题子类）分值总和
    let subclassScore = category.children.reduce((totalScore, next) => {
      return totalScore + Number(next.totalScore)
    }, 0)

    /* ======================== level-2 ======================== */
    if (category.children) {
      category.children.forEach(subclass => {
        subclass[key] = subclass.id === node.id ? newVal : subclass[key]
        if (subclass.type === "PROBLEM_SUBCLASS") {
          if (subclassScore > category.totalScore && subclass.id === node.id) {
            Message.warning(`问题大类《${category.name}》下问题子类分值总和超过该大类的分值，请重新输入`)
            subclass[key] = 0
            return
          }
        } else {
          if (subclass.totalScore > category.totalScore) {
            Message.warning(`问题大类《${category.name}》下评分项分值超过该大类的分值，请重新输入`)
            subclass[key] = 0
            return
          }
        }

        // 问题子类分值 >= 其下所有评分项分值总和
        // if (subclass.type === "PROBLEM_SUBCLASS") {
        // // 计算当前子类下的所有评分项的分值总和
        // let itemTotalScore = subclass.children.reduce((totalScore, next) => {
        //   return totalScore + Number(next.totalScore)
        // }, 0)
        // if (itemTotalScore > subclass.totalScore) {
        //   Message.warning(`问题子类《${subclass.name}》下评分项分值总和超过该子类的分值，请重新输入`)
        //   newVal = 0
        // }
        // }

        //评分项 扣分值<=单项最高可扣分
        // else {
        //   if (subclass.score > subclass.totalScore) subclass.score = subclass.totalScore
        // }

        // if (subclass.totalScore > category.totalScore) {
        //   Message.warning(`问题大类《${category.name}》下评分项分值不可超过该大类的分值`)
        //   subclass.totalScore = category.totalScore
        //   if (subclass.score > subclass.totalScore) subclass.score = subclass.totalScore
        //   return
        // }
        if (subclass.score > subclass.totalScore) subclass.score = subclass.totalScore

        /* ======================== level-3 ======================== */
        if (subclass.children) {
          subclass.children.forEach(item => {
            item[key] = item.id === node.id ? newVal : item[key]

            // 扣分值<=单项最高可扣分
            if (item.totalScore > subclass.totalScore) item.totalScore = subclass.totalScore
            if (item.score > item.totalScore) item.score = item.totalScore
          })
        }
      })
    }
  })
}
</script>

<style lang="less" scoped>
.home-page-container {
  height: 100%;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 35, 114, 0.1);

  .header {
    height: 40px;
    padding: 10px 20px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #fff;
  }
  .body {
    height: calc(100% - 60px);
    overflow: auto;
    padding: 0 20px;
  }
}

.level {
  display: flex;
  flex-wrap: wrap;
}

.content {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 10px;

  .form-item {
    margin-right: 15px;
  }
}

.level-1-content {
  background: #e1e2e6;
}

.operation-container {
  display: flex;
  align-items: center;
  float: right;
}

.children-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding-left: 30px;
}

.common-search-header {
  margin-bottom: 0;
  border-radius: 0;
}

:deep(.el-form) {
  display: flex;
}
:deep(.el-form-item) {
  margin-bottom: 0;
}
</style>
