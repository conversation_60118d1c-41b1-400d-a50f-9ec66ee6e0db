<template>
  <DialogContainer
    :close-callback="closeViewPackageDialog"
    no-footer
    :width="650"
    :visible="packageDetailState.visible"
    title="查看"
  >
    <div class="view-package">
      <div>
        <span>箱号:</span>
        <span>{{ packageDetailState.documentStorageBoxNumber }}</span>
      </div>
      <div>
        <span>存址:</span>
        <span>{{ packageDetailState.documentStorageBoxLocation }}</span>
      </div>
      <div>
        <span>明细:</span>
        <BaseTable :data="packageDetailState.storedDocumentList" :columns="packageDetailColumns" border>
          <template #operation="{ row }">
            <i class="ri-delete-bin-6-line" @click="deleteMedical(row)"></i>
          </template>
        </BaseTable>
      </div>
      <div>
        <span></span>
        <el-pagination
          v-model:page-size="packageDetailPageConfig.pageSize"
          v-model:current-page="packageDetailPageConfig.currentPage"
          :pager-count="5"
          size="small"
          background
          :page-sizes="[10, 25, 50, 100]"
          layout="sizes, prev, pager, next, jumper, total"
          :total="packageDetailState.totalStoredDocument"
          @update:page-size="refreshPackageDetailList"
          @current-change="getPackingDetailList"
        />
      </div>
    </div>
  </DialogContainer>
</template>

<script lang="ts" setup>
import { reactive } from "vue"
import { DialogContainer, BaseTable } from "@/base-components"
import { Message, SystemPrompt } from "@/utils"
import { getPackingDetailApi, deletePackingDocumentApi } from "../../interface"
import { packageDetailColumns } from "../config"

const props = defineProps<{
  confirmCallback?: () => void
}>()
/*======================================查看弹窗======================================*/

const packageDetailState = reactive({
  visible: false,
  documentStorageBoxNumber: "", // 箱号
  documentStorageBoxLocation: "", // 箱子存放的位置
  documentStorageBoxWsid: "", // 箱子id
  storedDocumentList: [] as Array<Record<string, any>>, // 箱子里存放的病案
  totalStoredDocument: 0 // 病案箱存放的病案总数
})

const packageDetailPageConfig = reactive({
  currentPage: 1,
  pageSize: 25
})

const refreshPackageDetailList = () => {
  // 如果当前就是第一页则强制刷新
  if (packageDetailPageConfig.currentPage === 1) getPackingDetailList()
  else packageDetailPageConfig.currentPage = 1
}

//打开弹窗，查看病案箱
const viewPackage = row => {
  packageDetailState.documentStorageBoxWsid = row.documentStorageBoxWsid
  packageDetailState.documentStorageBoxLocation = row.documentStorageBoxLocation
  packageDetailState.documentStorageBoxNumber = row.documentStorageBoxNumber

  packageDetailState.visible = true
  getPackingDetailList()
}

// 关闭查看弹窗
const closeViewPackageDialog = () => {
  packageDetailState.visible = false
  props.confirmCallback && props.confirmCallback()
}

// 获取箱子里存放的病案
const getPackingDetailList = () => {
  getPackingDetailApi({
    documentStorageBoxWsid: packageDetailState.documentStorageBoxWsid,
    offset: packageDetailPageConfig.pageSize * (packageDetailPageConfig.currentPage - 1),
    limit: packageDetailPageConfig.pageSize
  }).then(res => {
    const data = res.data.data
    packageDetailState.storedDocumentList = data.storedDocumentDetails.map(item => ({
      ...item,
      mrNo: item.electronicMedicalRecordNo
    }))
    packageDetailState.totalStoredDocument = data.pageMetadata.totalElements
  })
}

// 删除病案
const deleteMedical = row => {
  SystemPrompt("是否删除该病案？").then(() => {
    // 删除病案
    console.log(row)
    deletePackingDocumentApi(packageDetailState.documentStorageBoxWsid, row.documentBagWsid)
      .then(() => {
        Message.success("删除病案成功")
        getPackingDetailList()
      })
      .catch(() => {
        Message.error("删除病案失败")
      })
  })
}

defineExpose({
  viewPackage
})
</script>

<style lang="less" scoped>
.view-package {
  > div {
    display: flex;
    margin-bottom: 10px;
    gap: 6px;
    > span {
      display: inline-block;
      min-width: 40px;
    }
  }
}
</style>
