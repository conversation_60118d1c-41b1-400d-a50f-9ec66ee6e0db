<template>
  <PageContainer separate>
    <template #search>
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <el-form-item label="文档类型" prop="documentMrClassName">
          <el-select v-model="formData.documentMrClassName" placeholder="请选择文档类型">
            <el-option
              v-for="item in documentTypeOptions"
              :key="item.mrClassCode"
              :label="item.mrClassName"
              :value="item.mrClassName"
            />
          </el-select>
        </el-form-item>
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="classificationConfigTableRef"
        :table-columns="classificationConfigColumns"
        :request-api="getClassRule"
        :request-params="searchParams"
        :data-callback="dataCallback"
        :pagination="false"
      >
        <template #header>
          <AddButton @click="onClickAdd">新增</AddButton>
        </template>
        <template #operation="{ row }">
          <TableButton @click="onClickEdit(row)">编辑</TableButton>
          <TableButton @click="modifyStatus(row)">
            {{ row.status === "ENABLE" ? "禁用" : "启用" }}
          </TableButton>
          <TableButton @click="handleDelete(row)">删除</TableButton>
        </template>
        <template #status="{ row }">
          <el-tag :type="row.status === 'ENABLE' ? 'success' : 'danger'">
            {{ row.status === "ENABLE" ? "已启用" : "已禁用" }}
          </el-tag>
        </template>
      </CommonTable>
    </template>
  </PageContainer>

  <DialogContainer
    v-model:visible="dialogVisible"
    :title="actionType === 'edit' ? '编辑' : '新增'"
    :width="500"
    :confirm-callback="handleConfirm"
  >
    <el-form
      ref="ruleFormRef"
      label-position="right"
      label-width="100px"
      label-suffix="："
      :model="classificationData"
      :rules="classificationConfigFormRules"
    >
      <el-form-item label="文档类型" prop="documentMrClassName">
        <el-select
          v-model="classificationData.documentMrClassName"
          placeholder="请选择文档类型"
          @change="selectDocumentType"
        >
          <el-option
            v-for="item in documentTypeOptions"
            :key="item.mrClassCode"
            :label="item.mrClassName"
            :value="item.mrClassName"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="应用科室" prop="classRuleDetails" :rules="classRuleDetailsRules">
        <el-select v-model="classRuleDetails" placeholder="请选择应用科室" :multiple="true" @change="selectDepartments">
          <el-option v-for="item in departmentOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
    </el-form>
  </DialogContainer>
</template>

<script setup lang="ts">
import { reactive, ref, watch, computed } from "vue"
import { cloneDeep } from "lodash-es"
import { AddButton, TableButton, CommonTable, DialogContainer, PageContainer, SearchContainer } from "@/base-components"
import { useTableSearch, useFormSetting, useTableOperation, useRequestData } from "@/hooks"
import { getAllDocTypes } from "@/interfaces"
import { useGlobalOptionsStore } from "@/stores"
import { SystemPrompt } from "@/utils"
import { getClassRule, editClassRule, addClassRule, modifyClassRuleStatus } from "../interface"
import { classificationConfigColumns, classificationConfigFormRules, menuId } from "./config"
import type { FormInstance } from "element-plus"

// 应用科室选项
const globalOptionsStore = useGlobalOptionsStore()
const departmentOptions = computed(() => [{ label: "全部", value: "all" }, ...globalOptionsStore.departmentOptions])

// 获取所有文档类型选项
const documentTypeOptions = useRequestData(getAllDocTypes)

/* ======================== 搜索相关数据及方法 ======================== */

const formData = reactive({
  documentMrClassName: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(formData)

/* ======================== 表格相关方法 ======================== */

const classificationConfigTableRef = ref<InstanceType<typeof CommonTable>>()

const dataCallback = (data: Array<Record<string, any>>) => {
  return data.map(item => ({
    ...item,
    applyRoom:
      item.hospitalDeptScope === "ALL" ? "全院" : item.classRuleDetails.map(detail => detail.hospitalDeptName).join(",")
  }))
}

// 切换启用&禁用状态
const modifyStatus = row => {
  const nextStatus = row.status === "ENABLE" ? "DISABLE" : "ENABLE"
  SystemPrompt(`是否确定${row.status === "ENABLE" ? "禁用" : "启用"}文档${row.documentMrClassName}`).then(() => {
    confirmToggle(row.wsid, nextStatus)
  })
}

// 删除
const handleDelete = row => {
  SystemPrompt(`您确定要删除文档${row.documentMrClassName} ?`).then(() => confirmDelete(row.wsid))
}

/* ======================== 弹窗相关数据及方法 ======================== */

const ruleFormRef = ref<FormInstance>()

interface ClassRuleDetailsItem {
  hospitalDepartmentWsid: string
  hospitalDeptName: string
}
const initFormValues = {
  wsid: "",
  documentMrClassWsid: "",
  hospitalDeptScope: "PART",
  classRuleDetails: [] as Array<ClassRuleDetailsItem>,
  documentMrClassName: "",
  mrClassCode: ""
}
const classificationData = reactive({ ...initFormValues })
const actionType = ref<"add" | "edit">("add")
const dialogVisible = ref(false)

const { showAddForm, showEditForm } = useFormSetting(classificationData, initFormValues, actionType, dialogVisible)

const { confirmAdd, confirmEdit, confirmToggle, confirmDelete } = useTableOperation(
  classificationData,
  dialogVisible,
  {
    addApi: addClassRule,
    editApi: editClassRule,
    toggleApi: modifyClassRuleStatus,
    deleteApi: (wsid: string) => modifyClassRuleStatus(wsid, "DEL")
  },
  classificationConfigTableRef
)

const classRuleDetails = ref<Array<any>>([])
const classRuleDetailsRules = ref<Array<any>>([{ required: true, message: "请选择应用科室", trigger: "blur" }])

// 选择全部科室时，修改校验规则
const selectDepartments = val => {
  if (val.indexOf("all") !== -1) {
    classRuleDetails.value = ["all"]
  }
}

const selectExistData = ref(false)
// 选择文档类型
const selectDocumentType = val => {
  // 新增时，若选中已有文档类型，则加载出该文档类型已有的应用科室
  if (actionType.value === "add") {
    getClassRule({
      offset: 0,
      limit: 1000,
      filters: `documentMrClassName=${val}`
    }).then(resData => {
      handleClassRuleDetails(resData[0])
      classificationData.wsid = resData[0].wsid
      selectExistData.value = true
    })
  }
}

// 选择全部时，classificationData.classRuleDetails传[]给后台，表单校验设置为非必填才可提交
watch(
  () => classRuleDetails.value,
  val => {
    classRuleDetailsRules.value[0].required = val.indexOf("all") === -1
  }
)

// 处理所选科室
const handleClassRuleDetails = (data: any) => {
  classRuleDetails.value = data.classRuleDetails || []
  classRuleDetails.value = classRuleDetails.value.map(item => item.hospitalDepartmentWsid || item)
  // 应用科室为全部时
  if (data.hospitalDeptScope === "ALL") {
    classRuleDetails.value = ["all"]
  }
}

// 新增
const onClickAdd = () => {
  showAddForm()
}

// 编辑
const onClickEdit = (data: any) => {
  showEditForm(data)
  handleClassRuleDetails(data)
}

// 保存数据
const handleConfirm = () => {
  // 构建选择科室数据
  classificationData.classRuleDetails = cloneDeep(classRuleDetails.value)
  globalOptionsStore.departmentOptions.forEach(item => {
    classificationData.classRuleDetails.forEach((data, index) => {
      if (item.value === data) {
        classificationData.classRuleDetails[index] = {
          hospitalDepartmentWsid: item.value,
          hospitalDeptName: item.label
        }
      }
    })
  })
  if (classRuleDetails.value.indexOf("all") !== -1) {
    classificationData.classRuleDetails = []
  }

  ruleFormRef.value?.validate(valid => {
    if (!valid) return
    const targetDocumentType = documentTypeOptions.value.find(
      item => item.mrClassName === classificationData.documentMrClassName
    )
    classificationData.documentMrClassWsid = targetDocumentType?.wsid ?? ""
    classificationData.mrClassCode = targetDocumentType?.mrClassCode ?? ""
    classificationData.hospitalDeptScope = classRuleDetails.value.indexOf("all") !== -1 ? "ALL" : "PART"
    // 确认提交
    const handle = actionType.value === "edit" || selectExistData.value ? confirmEdit() : confirmAdd()
    handle.then(() => {
      classRuleDetails.value = []
      selectExistData.value = false
    })
  })
}
</script>
