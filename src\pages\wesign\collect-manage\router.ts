import type { RouteRecordRaw } from "vue-router"

const collectManageRouter: RouteRecordRaw = {
  path: "/collect",
  name: "CollectManage",
  redirect: "/collect/configuration",
  meta: {
    title: "采集管理",
    icon: "ri-sound-module-line",
    type: "multiple",
    showBreadcrumb: true
  },
  children: [
    {
      path: "/collect/configuration",
      redirect: "/collect/configuration/vendor-system",
      meta: { title: "采集配置" },
      children: [
        {
          path: "/collect/configuration/vendor-system",
          component: () => import("./collect-configuration/VendorSystem.vue"),
          meta: { title: "厂商系统" }
        },
        {
          path: "/collect/configuration/data-origin",
          component: () => import("./collect-configuration/DataOrigin.vue"),
          meta: { title: "数据源" }
        },
        {
          path: "/collect/configuration/ftp-data-origin",
          component: () => import("./collect-configuration/FTPDataOrigin.vue"),
          meta: { title: "FTP数据源" }
        },
        {
          path: "/collect/configuration/field-mapping",
          component: () => import("./collect-configuration/FieldMapping.vue"),
          meta: { title: "字段映射" }
        }
      ]
    },
    {
      path: "/collect/way",
      redirect: "/collect/way/database",
      meta: { title: "采集方式" },
      children: [
        {
          path: "/collect/way/database",
          component: () => import("./collect-way/DatabaseConfig.vue"),
          meta: { title: "数据库" }
        },
        {
          path: "/collect/way/ftp",
          component: () => import("./collect-way/FtpConfig.vue"),
          meta: { title: "FTP" }
        },
        {
          path: "/collect/way/interface",
          component: () => import("./collect-way/InterfaceConfig.vue"),
          meta: { title: "接口配置" }
        }
      ]
    },
    {
      path: "/collect/monitor",
      redirect: "/collect/monitor/index",
      meta: { title: "采集记录" },
      children: [
        {
          path: "/collect/monitor/index",
          component: () => import("./collect-monitor/index.vue"),
          meta: { title: "采集监控" }
        },
        {
          path: "/collect/monitor/third-party-push",
          component: () => import("./collect-monitor/ThirdPartyPush.vue"),
          meta: { title: "第三方推送" }
        },
        {
          path: "/collect/monitor/medical-record-remake",
          component: () => import("./collect-monitor/MedicalRecordRemake.vue"),
          meta: { title: "病案翻拍" }
        }
      ]
    },
    {
      path: "/collect/template",
      redirect: "/collect/template/list",
      meta: { title: "文书模板" },
      children: [
        {
          path: "/collect/template/list",
          component: () => import("./template-manage/TemplateList.vue"),
          meta: { title: "模板列表" }
        },
        {
          path: "/collect/template/view",
          component: () => import("./template-manage/TemplateView.vue"),
          meta: { title: "模板预览" }
        }
      ]
    },
    // {
    //   path: "/collect/collect-history-data",
    //   redirect: "/collect/collect-history-data/database",
    //   meta: { title: "历史数据采集" },
    //   children: [
    //     {
    //       path: "/collect/collect-history-data/database",
    //       component: () => import("./collect-history-data/Database.vue"),
    //       meta: { title: "数据库" }
    //     },
    //     {
    //       path: "/collect/collect-history-data/api",
    //       component: () => import("./collect-history-data/Api.vue"),
    //       meta: { title: "接口配置" }
    //     },
    //     {
    //       path: "/collect/collect-history-data/reception",
    //       component: () => import("./collect-history-data/Reception.vue"),
    //       meta: { title: "接收配置" }
    //     }
    //   ]
    // },
    {
      path: "/collect/data-reception-config",
      meta: { title: "数据接收配置" },
      component: () => import("./data-reception-config/index.vue")
    }
  ]
}

export default collectManageRouter
