import { QualityControlTypeEnum, QcStatusEnum, QualityControlOperatorTypeEnum } from "@/configs"

/* ========================= 科室质控/交叉质控 ========================= */
// 获取质控标签类型
export function getQcStatusTagType(status: QcStatusEnum) {
  switch (status) {
    case QcStatusEnum.WAIT:
    case QcStatusEnum.UNDISTRIBUTED:
      return "warning"
    case QcStatusEnum.DEPT_FAIL:
    case QcStatusEnum.CROSS_FAIL:
    case QcStatusEnum.FAIL:
    case QcStatusEnum.DELETE:
    case QcStatusEnum.NOT_PASS:
      return "danger"
    case QcStatusEnum.MEDICAL_PASS:
    case QcStatusEnum.NURSE_PASS:
    case QcStatusEnum.DEPT_PASS:
    case QcStatusEnum.CROSS_PASS:
    case QcStatusEnum.PASS:
    case QcStatusEnum.DEPT_FINAL_PASS:
      return "success"
  }
}

// 获取质控状态
export function getQcStatus(status: QcStatusEnum, qcType = "") {
  switch (status) {
    case QcStatusEnum.WAIT:
      if (qcType) return `${getSimpleQcTaskType(qcType)}未质控`
      else return "未质控"
    case QcStatusEnum.UNDISTRIBUTED:
      if (qcType) return `${getSimpleQcTaskType(qcType)}未分配`
      else return "未分配"
    case QcStatusEnum.DEPT_FAIL:
      return "科室质控未通过"
    case QcStatusEnum.CROSS_FAIL:
      return "交叉质控未通过"
    case QcStatusEnum.NOT_PASS:
    case QcStatusEnum.FAIL:
      if (qcType) return `${getSimpleQcTaskType(qcType)}未通过`
      else return "质控未通过"
    case QcStatusEnum.DELETE:
      return "已删除"
    case QcStatusEnum.MEDICAL_PASS:
      return "医疗质控已通过"
    case QcStatusEnum.NURSE_PASS:
      return "护理质控已通过"
    case QcStatusEnum.DEPT_PASS:
      return "科室质控已通过"
    case QcStatusEnum.CROSS_PASS:
      return "交叉质控已通过"
    case QcStatusEnum.DEPT_FINAL_PASS:
      return "医护质控通过"
    case QcStatusEnum.PASS:
      if (qcType) return `${getSimpleQcTaskType(qcType)}已通过`
      else return "质控已通过"
    default:
      return ""
  }
}

// 获取质控类型
export function getQcTaskType(qcType) {
  switch (qcType) {
    case QualityControlTypeEnum.DEPT_QC_MEDICAL:
      return "科室医疗质控"
    case QualityControlTypeEnum.DEPT_QC_NURSE:
      return "科室护理质控"
    case QualityControlTypeEnum.CROSS_QC_MEDICAL:
      return "交叉医疗质控"
    case QualityControlTypeEnum.CROSS_QC_NURSE:
      return "交叉护理质控"
    case QualityControlTypeEnum.FINAL_QC:
      return "终末质控"
    default:
      return ""
  }
}
// 获取质控类型（不区分交叉/科室）
export function getSimpleQcTaskType(qcType) {
  switch (qcType) {
    case QualityControlTypeEnum.DEPT_QC_MEDICAL:
    case QualityControlTypeEnum.CROSS_QC_MEDICAL:
      return "医疗质控"
    case QualityControlTypeEnum.DEPT_QC_NURSE:
    case QualityControlTypeEnum.CROSS_QC_NURSE:
      return "护理质控"
    case QualityControlTypeEnum.FINAL_QC:
      return "终末质控"
    case QualityControlTypeEnum.DEPT_QC:
      return "医护质控"
    default:
      return ""
  }
}

// 获取质控操作类型
export function getQcOperateType(operateType) {
  switch (operateType) {
    case QualityControlOperatorTypeEnum.QC_FINAL_SCORE:
    case QualityControlOperatorTypeEnum.QC_NURSE_SCORE:
    case QualityControlOperatorTypeEnum.QC_MEDICAL_SCORE:
    case QualityControlOperatorTypeEnum.QC_DEPT_SCORE:
      return "科室评分"
    case QualityControlOperatorTypeEnum.QC_FINAL_COMMENT:
    case QualityControlOperatorTypeEnum.QC_NURSE_COMMENT:
    case QualityControlOperatorTypeEnum.QC_MEDICAL_COMMENT:
    case QualityControlOperatorTypeEnum.QC_DEPT_COMMENT:
      return "添加质控记录"
    case QualityControlOperatorTypeEnum.QC_MEDICAL_PASS:
      return "医疗质控通过"
    case QualityControlOperatorTypeEnum.QC_MEDICAL_FAIL:
      return "医疗质控不通过"
    case QualityControlOperatorTypeEnum.QC_NURSE_PASS:
      return "护理质控通过"
    case QualityControlOperatorTypeEnum.QC_NURSE_FAIL:
      return "护理质控不通过"
    case QualityControlOperatorTypeEnum.QC_FINAL_PASS:
      return "终末质控通过"
    case QualityControlOperatorTypeEnum.QC_FINAL_FAIL:
      return "终末质控不通过"
    case QualityControlOperatorTypeEnum.QC_DEPT_PASS:
      return "医护质控通过"
    case QualityControlOperatorTypeEnum.QC_DEPT_FAIL:
      return "医护质控不通过"

    default:
      return ""
  }
}
