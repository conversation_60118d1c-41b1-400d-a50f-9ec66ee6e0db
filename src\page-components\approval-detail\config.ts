import { flow, map, compact } from "lodash/fp"

// 获取流程处理状态
export const getWorkflowStatus = (status?: string) => {
  if (!status)
    return {
      text: "处理中",
      class: "warning-status",
      icon: "ri-hourglass-fill"
    }
  switch (status) {
    case "PASS":
      return {
        text: "通过",
        class: "success-status",
        icon: "ri-checkbox-circle-fill"
      }
    case "ROLLBACK":
      return {
        text: "回退",
        class: "error-status",
        icon: "ri-close-circle-fill"
      }
    case "DENY":
      return {
        text: "已驳回",
        class: "error-status",
        icon: "ri-close-circle-fill"
      }
    case "UNKNOWN":
      return {
        text: "未知",
        class: "info-status",
        icon: "ri-hourglass-fill"
      }
    case "APPLY":
      return {
        text: "提交申请",
        class: "success-status",
        icon: "ri-checkbox-circle-fill"
      }
    default:
      return {
        text: "处理中",
        class: "info-status",
        icon: "ri-hourglass-fill"
      }
  }
}
