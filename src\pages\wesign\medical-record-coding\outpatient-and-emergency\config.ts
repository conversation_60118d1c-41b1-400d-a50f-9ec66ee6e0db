import type { TableColumnItem } from "@/types"

export const menuId = "/catalog/catalog-list"

export const tabsRouterList = [
  { label: "待编码", path: "/coding/outpatient-and-emergency/pending", name: "waitingCatalog" },
  { label: "已编码", path: "/coding/outpatient-and-emergency/finished", name: "cataloged" }
]

const tableColumns: Array<TableColumnItem> = [
  { prop: "patientName", label: "姓名", minWidth: 120, must: true },
  { prop: "patientSex", label: "性别", minWidth: 80 },
  // { prop: "patientAge", label: "年龄", minWidth: 100, sortable: true },
  { prop: "registerNo", label: "门诊号", minWidth: 100 },
  { prop: "", label: "急诊号", minWidth: 100 },
  { prop: "medicalNo", label: "就诊卡号", minWidth: 100 },
  { prop: "mainDiagnostic", label: "主要诊断", minWidth: 200 },
  { prop: "deptName", label: "就诊科室", minWidth: 100 },
  { prop: "consultationTime", label: "就诊时间", minWidth: 160, sortable: true },
  { prop: "doctorN<PERSON>", label: "就诊医师", minWidth: 100 }
]

export const pendingColumns: Array<TableColumnItem> = [
  ...tableColumns,
  { prop: "operation", label: "操作", width: 200, fixed: "right", must: true }
]

export const finishedColumns: Array<TableColumnItem> = [
  ...tableColumns,
  { prop: "catalogerName", label: "编码员", minWidth: 120 },
  { prop: "catalogTime", label: "编码时间", minWidth: 180, sortable: true },
  { prop: "operation", label: "操作", width: 300, fixed: "right", must: true }
]

// 门诊（急）诊断

export const oldDiagnosisColumns: Array<TableColumnItem> = [
  { prop: "oldCode", label: "疾病编码", minWidth: 80, must: true },
  { prop: "oldName", label: "门诊(急)诊断", minWidth: 170, must: true }
]

export const diagnosisColumns: Array<TableColumnItem> = [
  // { prop: "oldCode", label: "疾病编码", minWidth: 80, must: true },
  // { prop: "oldName", label: "门诊(急)诊断", minWidth: 170, must: true },
  { type: "select", prop: "code", label: "ICD10编码", minWidth: 100, optionName: "icd10" },
  { type: "select", prop: "name", label: "ICD10诊断名称", minWidth: 120, optionName: "icd10" },
  { prop: "operation", label: "操作", width: 150, fixed: "right" }
]

// 出院诊断

export const oldOutHospitalColumns: Array<TableColumnItem> = [
  { prop: "oldCode", label: "疾病编码", minWidth: 80, must: true },
  { prop: "oldName", label: "出院诊断", minWidth: 170, must: true },
  { prop: "oldDischargeDiagnosisDisease", label: "入院病情", minWidth: 100 }
]

export const outHospitalColumns: Array<TableColumnItem> = [
  // { prop: "oldCode", label: "疾病编码", minWidth: 80, must: true },
  // { prop: "oldName", label: "出院诊断", minWidth: 170, must: true },
  // { prop: "oldDischargeDiagnosisDisease", label: "入院病情", minWidth: 100 },
  { type: "select", prop: "code", label: "ICD10编码", minWidth: 100, optionName: "icd10" },
  { type: "select", prop: "name", label: "ICD10诊断名称", minWidth: 120, optionName: "icd10" },
  { type: "select", prop: "dischargeDiagnosisDisease", label: "入院病情", minWidth: 100, optionName: "rc027" },
  { prop: "operation", label: "操作", width: 150, fixed: "right" }
]

// 中毒损伤
export const oldPoisoningDamageColumns: Array<TableColumnItem> = [
  { prop: "oldCode", label: "疾病编码", minWidth: 80, must: true },
  { prop: "oldName", label: "诊断名称", minWidth: 170, must: true }
]
export const poisoningDamageColumns: Array<TableColumnItem> = [
  // { prop: "oldCode", label: "疾病编码", minWidth: 80, must: true },
  // { prop: "oldName", label: "诊断名称", minWidth: 170, must: true },
  { type: "select", prop: "code", label: "ICD10编码", minWidth: 100, optionName: "icd10" },
  { type: "select", prop: "name", label: "ICD10诊断名称", minWidth: 120, optionName: "icd10" },
  { prop: "operation", label: "操作", width: 150, fixed: "right" }
]

// 病理诊断
export const oldPathologyDiagnosisColumns: Array<TableColumnItem> = [
  { prop: "oldCode", label: "疾病编码", minWidth: 80, must: true },
  { prop: "oldName", label: "诊断名称", minWidth: 170, must: true }
]

export const pathologyDiagnosisColumns: Array<TableColumnItem> = [
  // { prop: "oldCode", label: "疾病编码", minWidth: 80, must: true },
  // { prop: "oldName", label: "诊断名称", minWidth: 170, must: true },
  { type: "select", prop: "code", label: "ICD-O编码", minWidth: 100, optionName: "icdO" },
  { type: "select", prop: "name", label: "ICD-O诊断名称", minWidth: 120, optionName: "icdO" },
  { prop: "operation", label: "操作", width: 150, fixed: "right" }
]

// 手术信息

export const oldOperationInfoColumns: Array<TableColumnItem> = [
  { type: "date", prop: "date", label: "手术及操作日期", minWidth: 80, must: true },
  { prop: "oldCode", label: "手术及操作编码", minWidth: 120, must: true },
  { prop: "oldName", label: "手术及操作名称", minWidth: 80 },
  { type: "select", prop: "operationLevel", label: "手术级别", minWidth: 100, optionName: "rc029" },
  { type: "input", prop: "operationLevel", label: "手术者", minWidth: 100 },
  { type: "input", prop: "operationCollaboratorOne", label: "一助", minWidth: 100 },
  { type: "input", prop: "operationCollaboratorTwo", label: "二助", minWidth: 100 },
  { type: "input", prop: "healingLevel", label: "切口愈合等级", minWidth: 100 },
  { type: "input", prop: "operationAnesthesiaDoctor", label: "麻醉医师", minWidth: 100 },
  { type: "select", prop: "anesthesiaType", label: "麻醉方式", minWidth: 100, optionName: "rc013" }
]

export const operationInfoColumns: Array<TableColumnItem> = [
  // ...oldOperationInfoColumns,
  { type: "date", prop: "date", label: "手术及操作日期", minWidth: 80, must: true },
  { type: "select", prop: "code", label: "ICD9编码", minWidth: 100, optionName: "icd9" },
  { type: "select", prop: "name", label: "ICD9诊断名称", minWidth: 120, optionName: "icd9" },
  { type: "select", prop: "operationLevel", label: "手术级别", minWidth: 100, optionName: "rc029" },
  { type: "input", prop: "operationDoctor", label: "手术者", minWidth: 100 },
  { type: "input", prop: "operationCollaboratorOne", label: "一助", minWidth: 100 },
  { type: "input", prop: "operationCollaboratorTwo", label: "二助", minWidth: 100 },
  { type: "select", prop: "healingLevel", label: "切口愈合等级", minWidth: 100, optionName: "rc014" },
  { type: "input", prop: "operationAnesthesiaDoctor", label: "麻醉医师", minWidth: 100 },
  { type: "select", prop: "anesthesiaType", label: "麻醉方式", minWidth: 100, optionName: "rc013" },
  { prop: "operation", label: "操作", width: 150, fixed: "right" }
]

export const insuranceOperationColumns: Array<TableColumnItem> = [
  { type: "select", prop: "code", label: "诊断编码", minWidth: 100, optionName: "icd9" },
  { type: "select", prop: "name", label: "诊断名称", minWidth: 120, optionName: "icd9" },
  { type: "select", prop: "anesthesiaType", label: "麻醉方式", minWidth: 100, optionName: "yb019" },
  { type: "input", prop: "operationDoctor", label: "术者医师姓名", minWidth: 100 },
  { type: "input", prop: "operationDoctorCode", label: "术者医师代码", minWidth: 100 },
  { type: "input", prop: "operationAnesthesiaDoctor", label: "麻醉医师姓名", minWidth: 100 },
  { type: "input", prop: "operationAnesthesiaDoctorCode", label: "麻醉医师代码", minWidth: 100 },
  { type: "date", timeType: "date", prop: "operationDateStart", label: "手术及操作开始时间", minWidth: 80, must: true },
  { type: "date", timeType: "date", prop: "operationDateEnd", label: "手术及操作结束时间", minWidth: 80, must: true },
  { type: "date", timeType: "date", prop: "anesthesiaDateStart", label: "麻醉开始时间", minWidth: 80, must: true },
  { type: "date", timeType: "date", prop: "anesthesiaDateEnd", label: "麻醉结束时间", minWidth: 80, must: true },
  { prop: "operation", label: "操作", width: 150, fixed: "right" }
]

export const anchorArr = ["first", "second", "third", "fourth", "fifth"]

export const outpatientFilterFormOptions = [
  { label: "姓名", value: "patientName" },
  { label: "就诊卡号", value: "medicalNo" },
  { label: "门诊号", value: "registerNo" }
]
