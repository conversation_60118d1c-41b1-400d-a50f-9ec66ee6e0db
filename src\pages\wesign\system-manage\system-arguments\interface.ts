import axios from "@/interfaces/axios-instance"

/**
 * @method GET
 * @desc   查询系统参数--指定key
 */
export function querySystemConfigByKey(obj) {
  const { key } = obj
  return axios({
    method: "get",
    url: `/api/system/system-config/by-key?type=${key}`
  })
}

interface GlobalProcessConfigData {
  key: string
  value: string
  extraValue?: string
}

// 更新全局流程配置
export function updateGlobalProcessConfigApi(data: Array<GlobalProcessConfigData>) {
  return axios({
    method: "post",
    url: "/api/system/system-flow-bns/config",
    data
  })
}

//获取全局流程配置
export function getGlobalProcessConfigApi() {
  return axios({
    method: "get",
    url: "/api/system/system-flow-bns/config"
  })
}
