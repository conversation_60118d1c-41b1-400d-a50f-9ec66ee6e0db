<template>
  <div v-loading="loading">
    <el-timeline class="approval-record">
      <el-timeline-item
        v-for="item in props.data"
        :key="item.createdDatetime"
        :timestamp="item?.operDescription"
        placement="top"
        :type="!item?.isFailOper ? 'success' : 'danger'"
      >
        <el-card class="approval-card">
          <div class="approval-card-content">
            <template v-if="!item.group?.length">
              <BlobImage class="approval-user-avatar" :file-wsid="item?.avatar" :default="defaultAvatar" />
              <div class="approval-user-content">
                <div>
                  <span v-if="item?.userName" class="approval-name">
                    {{ item?.userName }}
                  </span>
                  <span v-if="item?.createdDatetime" class="approval-date">
                    {{ item?.createdDatetime }}
                  </span>
                </div>
                <div
                  class="approval-status"
                  :style="{
                    color: !item?.isFailOper ? '#67c23a' : '#ff4d4f'
                  }"
                >
                  <span v-if="item?.isProcessNode">{{ !item?.isFailOper ? "通过" : "不通过" }}</span>
                </div>
                <div class="approval-remark" v-html="item?.remark"></div>
              </div>
            </template>
            <template v-else>
              <el-timeline class="approval-record" style="width: 100%">
                <el-timeline-item
                  v-for="group in item.group"
                  :key="group.createdDatetime"
                  :timestamp="group?.operDescription"
                  placement="top"
                  :type="!group?.isFailOper ? 'success' : 'danger'"
                >
                  <el-card class="approval-card">
                    <div class="approval-card-content">
                      <BlobImage class="approval-user-avatar" :file-wsid="group?.avatar" :default="defaultAvatar" />
                      <div class="approval-user-content">
                        <div>
                          <span v-if="group?.userName" class="approval-name">
                            {{ group?.userName }}
                          </span>
                          <span v-if="group?.createdDatetime" class="approval-date">
                            {{ group?.createdDatetime }}
                          </span>
                        </div>
                        <div
                          class="approval-status"
                          :style="{
                            color: !group?.isFailOper ? '#67c23a' : '#ff4d4f'
                          }"
                        >
                          <span v-if="group?.isProcessNode">{{ !group?.isFailOper ? "通过" : "不通过" }}</span>
                        </div>
                        <div class="approval-remark" v-html="group?.remark"></div>
                      </div>
                    </div>
                  </el-card>
                </el-timeline-item>
              </el-timeline>
            </template>
          </div>
        </el-card>
      </el-timeline-item>
    </el-timeline>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue"
import { BlobImage } from "@/base-components"
import defaultAvatar from "@/assets/png/avatar2.png"

// 获取流程处理状态
const getWorkflowStatus = (status?: number) => {
  // if (!status)
  //   return {
  //     text: "处理中",
  //     class: "warning-status",
  //     icon: "ri-hourglass-fill"
  //   }
  switch (status) {
    case 0:
      return {
        text: "通过",
        class: "success-status",
        icon: "ri-checkbox-circle-fill"
      }
    case 1:
      return {
        text: "回退",
        class: "error-status",
        icon: "ri-close-circle-fill"
      }
    case 2:
      return {
        text: "已驳回",
        class: "error-status",
        icon: "ri-close-circle-fill"
      }
    case 3:
      return {
        text: "未知",
        class: "info-status",
        icon: "ri-hourglass-fill"
      }
    case 4:
      return {
        text: "提交申请",
        class: "success-status",
        icon: "ri-checkbox-circle-fill"
      }
    default:
      return {
        text: "处理中",
        class: "info-status",
        icon: "ri-hourglass-fill"
      }
  }
}

interface Props {
  data: Array<Record<string, any>>
}

const props = defineProps<Props>()

const loading = ref(false)

onMounted(() => {
  props?.data?.forEach(item => {
    if (item.remark) item.remark = item.remark.replace(/\n/g, "<br/>")
    if (item.group?.length) {
      item.group.forEach(group => {
        if (group.remark) group.remark = group.remark.replace(/\n/g, "<br/>")
      })
    }
  })
})
</script>

<style lang="less" scoped>
.approval-record {
  margin-left: 1px;

  :deep(.el-timeline-item__timestamp) {
    font-weight: bold;
    color: #0a1633;
    font-size: 14px;
  }
  :deep(.el-timeline-item) {
    padding-bottom: 16px;
  }
  .approval-card {
    background: #f6f7f9;

    :deep(.el-card__body) {
      padding: 16px;
    }
    .approval-card-content {
      display: flex;
      align-items: center;
      gap: 8px;
      &:not(:last-child) {
        margin-bottom: 16px;
      }
      .approval-user-avatar {
        width: 46px;
        height: 46px;
        min-width: 46px;

        :deep(img) {
          border-radius: 50%;
        }
      }
      .approval-user-content {
        // width: 100%;
      }
      .approval-name {
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: #0a1633;
        line-height: 20px;
        padding-right: 8px;
      }
      .approval-date {
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: rgba(10, 22, 51, 0.6);
        line-height: 20px;
      }
      .approval-status {
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 14px;
        line-height: 20px;
        padding-top: 4px;
        display: flex;
        align-items: center;
        gap: 8px;
        .approval-desc {
          flex: 1;
        }
      }
      .approval-remark {
        color: rgba(10, 22, 51, 0.6);
        font-size: 12px;
      }
      .error-status {
        color: #ff4d4f;
      }
      .success-status {
        color: #67c23a;
      }
      .warning-status {
        color: #e6a23c;
      }
      .info-status {
        color: #909399;
      }
    }
  }
}
</style>
