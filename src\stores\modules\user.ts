import { defineStore } from "pinia"
import type { MenuConfigItem } from "@/types"
import { MenuOperationEnum, UserTypeEnum } from "@/configs"
import { adminMenuData } from "@/pages/wesign/sub-routers"

/* ======================== 登录用户信息 ======================== */

interface IUserHospitalInfo {
  avatar: string
  realName: string
  phone: string
  jobId: string
  jobTitleName: string
  deptName: string
  deptWsid: string
  wsid: string
}

interface IUserStore {
  rememberAccount: boolean
  account: string
  userWsid: string
  expiresIn: number
  passwordExpire: boolean // 用于标识是否为新用户
  roles: Array<string>
  userMenus: Array<MenuConfigItem>
  userHospitalInfo: IUserHospitalInfo
  loginName: string
  fullTextHistory: Array<string>
}

const useUserStore = defineStore("USER_INFO", {
  state: (): IUserStore => {
    return {
      userWsid: "",
      account: "",
      rememberAccount: true,
      expiresIn: 1800,
      passwordExpire: false,
      roles: [],
      userMenus: [],
      userHospitalInfo: {
        avatar: "",
        realName: "",
        phone: "",
        jobId: "",
        jobTitleName: "",
        deptName: "",
        deptWsid: "",
        wsid: ""
      },
      // 单点登录接口字段
      loginName: "",
      // 全文检索历史记录
      fullTextHistory: []
    }
  },
  actions: {
    setUserInfo(loginInfo: IUserStore) {
      this.account = loginInfo.account || loginInfo?.loginName
      this.rememberAccount = loginInfo?.rememberAccount
      this.expiresIn = loginInfo.expiresIn || 1800
      this.roles = loginInfo.roles || []
      this.userWsid = loginInfo.userWsid
    },
    setUserHospitalInfo(userHospitalInfo: IUserHospitalInfo) {
      for (const prop in this.userHospitalInfo) {
        // if (userHospitalInfo[prop])
        this.userHospitalInfo[prop] = userHospitalInfo[prop] ?? ""
      }
    },
    clearUserStore() {
      localStorage.removeItem("SessionWsid")
      localStorage.removeItem("activeTime")
      localStorage.removeItem("activeMenuTab")
      localStorage.removeItem("formClientId")
      const preAccount = this.rememberAccount ? this.account : "" // 重置前记录当前账号
      this.$reset()
      this.account = preAccount
    },
    // 检测是否有指定菜单操作权限
    hasOperationPermission(menuId: string, operationId: MenuOperationEnum) {
      return this.userAvailableOperations.includes(`${menuId}__${operationId}`)
    },
    setFullTextHistory(history) {
      this.fullTextHistory = history
    }
  },
  getters: {
    // 用户当前可用的路径列表
    userAvailablePaths: state => {
      const temp: Array<string> = []
      console.log(`output->state.userMenus`, state.userMenus)
      state.userMenus.forEach(menu => {
        temp.push(menu.path, ...menu.functions)
        menu.children.forEach(subMenu => {
          temp.push(subMenu.path, ...subMenu.functions)
          subMenu.children.forEach(level2Menu => {
            temp.push(level2Menu.path, ...level2Menu.functions)
          })
        })
      })
      return temp
    },
    // 用户当前可用的操作列表
    userAvailableOperations: state => {
      const temp: Array<string> = []
      state.userMenus.forEach(menu => {
        temp.push(...menu.operations.map(operation => operation.id))
        menu.children.forEach(subMenu => {
          temp.push(...subMenu.operations.map(operation => operation.id))
          subMenu.children.forEach(level2Menu => {
            temp.push(...level2Menu.operations.map(operation => operation.id))
          })
        })
      })
      return temp
    },
    // 判断当前登录是否为管理员
    isAdmin: state => state.roles.includes(UserTypeEnum.ADMIN) || state.roles.includes(UserTypeEnum.SUPER_ADMIN),
    // 若为管理员则需要额外加上仅管理员可见的菜单
    appMenus: state => {
      if (state.roles.includes(UserTypeEnum.ADMIN) || state.roles.includes(UserTypeEnum.SUPER_ADMIN)) {
        return [...state.userMenus, ...adminMenuData]
      } else {
        return state.userMenus
      }
    }
  },
  persist: true
})

export default useUserStore
