import { MenuOperationEnum } from "@/configs"
import type { RouteRecordRaw } from "vue-router"

const borrowManageRouter: RouteRecordRaw = {
  path: "/borrow",
  name: "BorrowManage",
  redirect: "/borrow/my-borrow",
  meta: {
    title: "借阅管理",
    icon: "ri-book-read-line",
    type: "multiple",
    showBreadcrumb: true
  },
  children: [
    // 借阅申请
    {
      path: "/borrow/borrow-application",
      meta: {
        title: "借阅申请",
        operations: [{ id: MenuOperationEnum.Borrow, name: "借阅" }]
      },
      redirect: "/borrow/borrow-application/list",
      children: [
        {
          path: "/borrow/borrow-application/list",
          meta: {
            title: "列表"
          },
          component: () => import("./borrow-application/index.vue")
        },
        {
          path: "/borrow/borrow-application/full-text-search",
          meta: {
            title: "全文检索"
          },
          component: () => import("./borrow-application/FullTextSearch.vue")
        }
      ]
    },

    // 我的借阅
    {
      path: "/borrow/my-borrow",
      redirect: "/borrow/my-borrow/approved",
      meta: {
        title: "我的借阅",
        operations: [
          { id: MenuOperationEnum.Export, name: "导出" },
          { id: MenuOperationEnum.View, name: "查看" },
          { id: MenuOperationEnum.Print, name: "打印" }
        ]
      },
      children: [
        {
          path: "/borrow/my-borrow/approved",
          component: () => import("./my-borrow/BorrowApproved.vue"),
          meta: { title: "借阅查看" }
        },
        {
          path: "/borrow/my-borrow/record",
          component: () => import("./my-borrow/BorrowRecord.vue"),
          meta: { title: "借阅记录" }
        }
      ]
    },
    // 已授权给我的病案
    {
      path: "/borrow/my-authorized",
      redirect: "/borrow/my-authorized/auth-mrno",
      meta: {
        title: "已授权病案",
        operations: [{ id: MenuOperationEnum.View, name: "查看" }]
      },
      children: [
        {
          path: "/borrow/my-authorized/auth-mrno",
          component: () => import("./my-authorized/AuthorizedByMrno.vue"),
          meta: { title: "授权(病案号)" }
        },
        {
          path: "/borrow/my-authorized/auth-dept",
          component: () => import("./my-authorized/AuthorizedByDept.vue"),
          meta: { title: "授权(科室)" }
        }
      ]
    },
    // 借阅审批
    {
      path: "/borrow/approval",
      redirect: "/borrow/approval/wait",
      meta: {
        title: "借阅审批",
        operations: [
          { id: MenuOperationEnum.Approve, name: "审批" },
          { id: MenuOperationEnum.View, name: "详情" }
        ]
      },
      children: [
        {
          path: "/borrow/approval/wait",
          component: () => import("./borrow-approval/ApprovalWait.vue"),
          meta: { title: "待审批" }
        },
        {
          path: "/borrow/approval/finished",
          component: () => import("./borrow-approval/ApprovalFinished.vue"),
          meta: { title: "已审批" }
        }
      ]
    },
    // 借阅授权
    {
      path: "/borrow/authorize",
      redirect: "/borrow/authorize/mrno",
      meta: {
        title: "借阅授权",
        operations: [
          { id: MenuOperationEnum.Authorize, name: "授权" },
          { id: MenuOperationEnum.View, name: "详情" }
        ]
      },
      children: [
        {
          path: "/borrow/authorize/mrno",
          component: () => import("./borrow-authorize/authorize-by-mrno/index.vue"),
          meta: { title: "授权(病案号)" }
        },
        {
          path: "/borrow/authorize/dept",
          component: () => import("./borrow-authorize/authorize-by-dept/index.vue"),
          meta: { title: "授权(科室)" }
        }
      ]
    },
    {
      path: "/borrow/detail",
      component: () => import("./sub-pages/detail.vue"),
      meta: { title: "查看", hideMenu: true }
    }
  ]
}
export default borrowManageRouter
