<template>
  <div class="progress-dialog-root">
    <DialogContainer
      v-model:visible="dialogVisible"
      :title="props.title"
      :cancel-callback="closeDialog"
      :no-footer="true"
    >
      <el-result v-if="props.error" icon="error" :title="`${$props.title}失败`" :sub-title="props.error" />
      <div v-else-if="props.progress < 100" class="batch-export">
        <div class="loading-hint">{{ props.detail }}</div>
        <div class="close-hint">关闭当前窗口不会影响导出操作。</div>
        <el-progress :percentage="Math.floor(props.progress)" />
      </div>
      <el-result
        v-else
        icon="success"
        :title="`${$props.title}成功`"
        sub-title="待浏览器下载完成后可前往本机文件夹中查看"
      />
    </DialogContainer>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue"
import { DialogContainer } from "@/base-components"
const props = defineProps({
  visible: { type: Boolean, default: true },
  title: { type: String, default: "导出" },
  detail: { type: String, default: "文件正在导出中，请稍候..." },
  progress: { type: Number, default: 0 },
  error: { type: String, default: "" }
})

const emits = defineEmits(["update:visible"])

const dialogVisible = computed({
  get: () => props.visible,
  set: val => emits("update:visible", val)
})

function closeDialog() {
  emits("update:visible", false)
}
</script>

<style lang="less" scoped>
.batch-export {
  width: 100%;
  height: 143px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .loading-hint {
    font-size: 20px;
  }

  .close-hint {
    font-size: 14px;
    color: #bbb;
    margin: 20px 0px;
  }
  :deep(.el-progress) {
    width: 100%;
  }
}

:deep(.el-result) {
  padding: 0px;
}
</style>
