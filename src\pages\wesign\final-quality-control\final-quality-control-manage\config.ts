import type { TableColumnItem } from "@/types"

const finalQualityControlColumns: Array<TableColumnItem> = [
  { prop: "patientName", label: "姓名", minWidth: 200, must: true, showOverflowTooltip: false },
  { prop: "mrNo", label: "病案号", minWidth: 150, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 120, must: true },
  { prop: "visitId", label: "住院次数", minWidth: 120, sortable: true },
  { prop: "patientSex", label: "性别", minWidth: 120 },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 120 },
  { prop: "inHospitalDatetime", label: "入院时间", minWidth: 180, sortable: true },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 120 },
  { prop: "outHospitalDatetime", label: "出院时间", minWidth: 180, sortable: true },
  { prop: "doctorName", label: "主治医师", minWidth: 100 },
  { prop: "deptActualScore", label: "科室评分", minWidth: 100 },
  { prop: "homePageActualScore", label: "首页评分", minWidth: 100 },
  { prop: "patientPrintCount", label: "患者打印", minWidth: 100 },
  { prop: "outClinicalDeptOperator", label: "出科操作人", minWidth: 120 },
  { prop: "outClinicalDeptDatetime", label: "出科时间", minWidth: 180, sortable: true, must: true }
]

export const finalWaitingQualityControlColumns: Array<TableColumnItem> = [
  ...finalQualityControlColumns,
  { prop: "operation", label: "操作", minWidth: 200, fixed: "right", must: true }
]

export const finalQualityControlledColumns: Array<TableColumnItem> = [
  ...finalQualityControlColumns,
  { prop: "finalActualScore", label: "终末评分", minWidth: 100 },
  { prop: "qcFinalStatusEnum", label: "终末质控", minWidth: 110 },
  { prop: "operation", label: "操作", width: 140, fixed: "right", must: true }
]

export const tabsRouterList = [
  {
    label: "待质控",
    path: "/final-quality-control/manage/waiting-control"
  },
  {
    label: "已质控",
    path: "/final-quality-control/manage/controlled"
  }
]
