import { MenuOperationEnum } from "@/configs"
import type { RouteRecordRaw } from "vue-router"

const DataReportRouter: RouteRecordRaw = {
  path: "/data-report",
  name: "DataReport",
  redirect: "/data-report/hospitalization",
  meta: {
    title: "HQMS上报",
    icon: "ri-chat-upload-line",
    type: "multiple",
    showBreadcrumb: true
  },
  children: [
    {
      path: "/data-report/hospitalization",
      redirect: "/data-report/hospitalization/list",
      meta: { title: "住院上报" },
      children: [
        {
          path: "/data-report/hospitalization/list",
          component: () => import("./hospitalization-report/List.vue"),
          meta: { title: "住院上报" }
        },
        {
          path: "/data-report/hospitalization/export-record",
          meta: { title: "导出记录" },
          component: () => import("./hospitalization-report/ExportRecord.vue")
        }
      ]
    },
    {
      path: "/data-report/outpatient-and-emergency",
      meta: { title: "门急诊上报" },
      redirect: "/data-report/outpatient-and-emergency/list",
      children: [
        {
          path: "/data-report/outpatient-and-emergency/list",
          component: () => import("./outpatient-report/List.vue"),
          meta: { title: "门急诊上报" }
        },
        {
          path: "/data-report/outpatient-and-emergency/export-record",
          component: () => import("./outpatient-report/ExportRecord.vue"),
          meta: { title: "导出记录" }
        }
      ]
    },
    {
      path: "/data-report/document",
      component: () => import("./document-report/index.vue"),
      meta: { title: "上报文书" }
    },
    {
      path: "/data-report/outpatient-and-emergency",
      meta: { title: "门急诊上报" },
      redirect: "/data-report/outpatient-and-emergency/list",
      children: [
        {
          path: "/data-report/outpatient-and-emergency/list",
          component: () => import("./outpatient-report/List.vue"),
          meta: { title: "门急诊上报" }
        },
        {
          path: "/data-report/outpatient-and-emergency/export-record",
          component: () => import("./outpatient-report/ExportRecord.vue"),
          meta: { title: "导出记录" }
        }
      ]
    },
    {
      path: "/data-report/outpatient-and-emergency/edit",
      meta: { title: "编目操作", hideMenu: true },
      component: () => import("./outpatient-report/Edit.vue")
    },
    {
      path: "/data-report/outpatient-and-emergency/detail",
      meta: { title: "病案详情", hideMenu: true },
      component: () => import("./outpatient-report/Detail.vue")
    }
  ]
}

export default DataReportRouter
