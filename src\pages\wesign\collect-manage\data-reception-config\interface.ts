import axios from "@/interfaces/axios-instance"

// 数据接收-分页类表
export const getDataReceptionListApi = (params: IPaginationRequestParams) => {
  return axios({
    method: "get",
    url: "/api/collect/push-configs",
    params: params
  })
}

// 数据接收-编辑
export const updateDataReceptionApi = data => {
  return axios({
    method: "put",
    url: `/api/collect/push-configs/${data.id}`,
    data: data
  })
}

// 数据接收-新增
export const addDataReceptionApi = data => {
  return axios({
    method: "post",
    url: "/api/collect/push-configs",
    data: data
  })
}

// 数据接收-删除
export const deleteDataReceptionApi = (id: string) => {
  return axios({
    method: "delete",
    url: `/api/collect/push-configs/${id}`
  })
}
