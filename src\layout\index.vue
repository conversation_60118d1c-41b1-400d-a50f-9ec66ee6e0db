<template>
  <Spin :spinning="systemStore.pageLoading" :tip="loadingText">
    <el-container class="common-layout">
      <el-header>
        <a v-if="!isCollapse" class="aside-wrapper">
          <img :src="resourceStore.logo" class="aside-logo" />
        </a>
        <div class="header-wrapper" @click="toggleCollapseStatus">
          <span class="line"></span>
          <i style="cursor: pointer" :class="isCollapse ? 'ri-menu-unfold-fill' : 'ri-menu-fold-fill'"></i>
          <span class="line"></span>
        </div>

        <!-- 顶部搜索 -->
        <!-- <HeaderSearch /> -->

        <div class="menu-type-nav">
          <div class="menu-type-nav">
            <el-tabs v-model="activeMenuType" class="menu-tabs" @tab-change="switchMenuType">
              <el-tab-pane
                v-for="item in menuGroupList"
                :key="item.id"
                :label="item.name"
                :name="item.id"
              ></el-tab-pane>
            </el-tabs>
          </div>
        </div>

        <!-- 顶部右侧消息和个人信息 -->
        <div class="header-right-wrapper">
          <MessagePopover />
          <el-popover
            placement="bottom"
            popper-class="avatar-popover"
            :width="120"
            trigger="click"
            :teleported="false"
            :hide-after="0"
          >
            <template #reference>
              <div class="avatar-wrapper">
                <div class="avatar-img">
                  <BlobImage
                    style="border-radius: 50%"
                    :file-wsid="userStore.userHospitalInfo.avatar"
                    :default="defaultSrc"
                  />
                </div>
                <div class="avatar-name">
                  {{ userStore.userHospitalInfo.realName || "超级管理员" }}
                </div>
                <i class="ri-arrow-down-s-fill" style="font-size: 14px; color: #fff"></i>
              </div>
            </template>
            <div class="avatar-btn" @click="$router.push({ path: '/user-setting' })">账户设置</div>
            <div class="avatar-btn" @click="handleLogout">退出登录</div>
          </el-popover>
        </div>
      </el-header>

      <el-container style="height: calc(100% - 60px)">
        <el-aside :class="!isCollapse ? 'normal-width' : 'mini-width'" style="padding-top: 17px">
          <LayoutMenu :is-collapse="isCollapse" :menu-type="menuType" :active-menu-type="activeMenuType" />
        </el-aside>
        <el-main style="background: #eff2f7">
          <DndProvider :backend="HTML5Backend">
            <div :class="{ container: true, 'container-no-breadcrumb': !$route.meta.showBreadcrumb, 'tab-mode': true }">
              <!-- 面包屑模式 -->
              <div v-if="$route.meta.showBreadcrumb && menuNavigation === '0'" class="breadcrumb">
                <el-breadcrumb separator="/">
                  <el-breadcrumb-item v-for="item in routeNodeList" :key="item.path" :to="{ path: item.path }">
                    {{ item.title }}
                  </el-breadcrumb-item>
                </el-breadcrumb>
              </div>
              <!-- 多窗口模式 -->
              <el-tabs
                v-show="menuNavigation === '1'"
                v-model="activeTab"
                type="card"
                :closable="pageTabs.length > 1"
                class="navigation-tabs"
                @tab-remove="removeTab"
                @tab-change="selectTab"
              >
                <el-tab-pane v-for="item in pageTabs" :key="item.name" :label="item.title" :name="item.name">
                  <template #label>
                    <div
                      class="tab-title"
                      :style="{ paddingRight: pageTabs.length > 1 ? '0' : '20px' }"
                      @contextmenu.prevent="e => openMenu(e, item)"
                    >
                      {{ item.title }}
                    </div>
                  </template>
                </el-tab-pane>
              </el-tabs>
              <RouterView v-if="systemStore.hasPermission"></RouterView>
              <NoPermissionPage v-else />
            </div>
          </DndProvider>
        </el-main>
      </el-container>
    </el-container>
    <!-- tab菜单 -->
    <div v-if="tabMenuVisible" class="tab-menu" @contextmenu.prevent>
      <div class="menu-item" @click="closeTab('current')">关闭</div>
      <div class="menu-item" @click="closeTab('other')">关闭其他标签页</div>
      <div class="menu-item" @click="closeTab('left')">关闭左侧标签页</div>
      <div class="menu-item" @click="closeTab('right')">关闭右侧标签页</div>
      <div class="menu-item" @click="closeTab('all')">关闭全部标签页</div>
    </div>
  </Spin>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watchEffect, computed, watch, nextTick } from "vue"
import { useRouter, useRoute, RouteRecordRaw } from "vue-router"
import { useNetwork } from "@vueuse/core"
import { Spin } from "ant-design-vue"
import { cloneDeep } from "lodash-es"
import { HTML5Backend } from "react-dnd-html5-backend"
import Sortable from "sortablejs"
import { DndProvider } from "vue3-dnd"
import { BlobImage } from "@/base-components"
import LayoutMenu from "./LayoutMenu.vue"
import MessagePopover from "./MessagePopover.vue"
import type { MenuType, TabState } from "@/types"
import type { TabPaneName } from "element-plus"
import {
  getAllDeptApi,
  getBorrowTypeOptionsApi,
  getSystemResourceConfig,
  updateSystemResourceConfig,
  logoutApi,
  getBagsStatusListApi,
  querySystemConfig,
  getPatientLabelOptionsApi
} from "@/interfaces"
import { NoPermissionPage } from "@/pages/exception"
import subRouters, {
  initMenuData,
  adminModulePathList,
  physicalExaminationModulePathList,
  codingModulePathList
} from "@/pages/wesign/sub-routers"
import { useUserStore, useSystemStore, useGlobalOptionsStore, useResourceStore } from "@/stores"
import { Message, SystemAlert } from "@/utils"

const { isOnline } = useNetwork()
const router = useRouter()
const currentRoute = useRoute()
const userStore = useUserStore()
const systemStore = useSystemStore()
const resourceStore = useResourceStore()
const globalOptionsStore = useGlobalOptionsStore()

const defaultSrc = new URL(`/src/assets/png/avatar2.png`, import.meta.url).href

/* ======================== 右侧顶部tab栏操作 ======================== */

// 系统配置窗口导航模式  0: 面包屑  1：多窗口（tab模式）
const menuNavigation = ref("0")

const activeTab = ref("")
const pageTabs = ref<Array<TabState>>([])

// 切换tab
const selectTab = val => {
  systemStore.setPageTabsData(pageTabs.value, activeTab.value)
  router.push(val)
}

onMounted(async () => {
  if (!localStorage.getItem("SessionWsid")) return
  menuNavigation.value = (await querySystemConfig({ type: "MENU_NAVIGATION" })).data?.data?.find(
    item => item.key === "menuNavigation"
  )?.value
  // tab模式
  if (menuNavigation.value === "1") {
    pageTabs.value = systemStore.pageTabs
    // tab模式下有值才跳转， 首页(/home)、运行监控(/large-screen-monitor)刷新时停留
    // if (activeTab.value && currentRoute.path !== "/home" && currentRoute.path !== "/large-screen-monitor") {
    //   router.push(activeTab.value)
    //   activeTab.value = systemStore.activeTab
    // }
  }

  // 初始化tab栏拖动
  const sortableList = document.getElementsByClassName("el-tabs__nav")[1]
  // const sortableList =
  //   document.getElementsByClassName("navigation-tabs")[0].childNodes[0]?.childNodes[1]?.childNodes[1]?.childNodes[0]
  if (sortableList) {
    Sortable.create(sortableList as HTMLElement, {
      animation: 150,
      onEnd: evt => {
        // 修改数组以反映拖动后的顺序
        const item = pageTabs.value.splice(evt.oldIndex as number, 1)[0]
        pageTabs.value.splice(evt.newIndex as number, 0, item)
        activeTab.value = item.name
        systemStore.setPageTabsData(pageTabs.value, activeTab.value)
        router.push(activeTab.value)
      }
    })
  }
})

// 右键选中的tab
const activeMenuTab = ref<TabState>({
  name: "",
  title: ""
})

// 关闭tab
const closeTab = (type: "current" | "other" | "all" | "left" | "right") => {
  const tabs = pageTabs.value
  let activeName = activeTab.value
  // 删除的是当前选中标签时，tab左移或右移
  if (activeName === activeMenuTab.value.name) {
    tabs.forEach((tab, index) => {
      if (tab.name === activeMenuTab.value.name) {
        const nextTab = tabs[index + 1] || tabs[index - 1]
        if (nextTab) {
          activeName = nextTab.name
        }
      }
    })
  }
  if (type === "current") {
    pageTabs.value = pageTabs.value.filter(item => item.name !== activeMenuTab.value.name)
    activeTab.value = activeName
  } else if (type === "other") {
    pageTabs.value = pageTabs.value.filter(item => item.name === activeMenuTab.value.name)
    activeTab.value = activeMenuTab.value.name
  } else if (type === "left") {
    pageTabs.value = pageTabs.value.slice(pageTabs.value.indexOf(activeMenuTab.value), pageTabs.value.length)
    activeTab.value = activeMenuTab.value.name
  } else if (type === "right") {
    pageTabs.value = pageTabs.value.slice(0, pageTabs.value.indexOf(activeMenuTab.value) + 1)
    activeTab.value = activeMenuTab.value.name
  } else {
    pageTabs.value = [
      {
        title: "首页",
        name: "/home"
      }
    ]
    activeTab.value = "/home"
  }
  systemStore.setPageTabsData(pageTabs.value, activeTab.value)
  // 已关闭所有tab，跳转回首页
  if (pageTabs.value.length === 0) {
    router.push({ path: "/home" })
    return
  }

  router.push(activeTab.value)
  tabMenuVisible.value = false
}

// 菜单展示
const tabMenuVisible = ref(false)

/**
 *
 * @param e
 * @param item 当前选中的tab
 */
const openMenu = (e, item) => {
  // 每次右键都触发动画效果
  tabMenuVisible.value = false
  activeMenuTab.value = item
  setTimeout(() => {
    tabMenuVisible.value = true
    nextTick(() => {
      const tabMenu = document.getElementsByClassName("tab-menu")[0] as HTMLElement
      tabMenu.style.top = `${e.y}px`
      tabMenu.style.left = `${e.x}px`
    })
  }, 1)
}

watch(
  () => currentRoute,
  val => {
    console.log(`output->val`, val)
    // 切换路由时，自动切换顶部模块
    menuGroupList.value.forEach(group => {
      group?.children?.forEach(level1 => {
        if (level1.path === val.path || level1?.functions?.includes(val.path)) {
          activeMenuType.value = group.id
          return
        }
        level1?.children?.forEach(level2 => {
          if (level2.path === val.path || level2?.functions?.includes(val.path)) {
            activeMenuType.value = group.id
            return
          }
        })
      })
    })

    // 初始化已保存的pageTabs
    pageTabs.value = systemStore.pageTabs.length ? systemStore.pageTabs : pageTabs.value

    // 点击tab时有重复的path不再新添加到tab内。
    if (
      val &&
      !pageTabs.value?.some(item => item.name === val?.fullPath)
      // &&val.meta.title !== "首页" &&
      // val.meta.title !== "运行监控"
    ) {
      // 如果是三级菜单页面
      if (val.matched.length === 3) {
        // 如果是从左侧点击二级菜单重定向时，才新增tab
        if (!pageTabs.value?.some(item => item.parentRoute === val.matched[val.matched.length - 2].path)) {
          pageTabs.value.push({
            title: (val?.redirectedFrom?.meta?.title || val?.meta?.title) as string,
            name: `${val?.fullPath}`,
            parentRoute: val.matched ? val.matched[val.matched.length - 2].path : ""
          })
          // 获取当前点击的页面tab
          activeTab.value = pageTabs.value[pageTabs.value.length - 1].name
        }
      } else {
        // 消息中心
        if (val.path === "/message-center") {
          pageTabs.value.push({
            title: (val?.redirectedFrom?.meta?.title || val?.meta?.title) as string,
            name: `${val?.fullPath}`,
            parentRoute: ""
          })
        } else {
          pageTabs.value.push({
            title: (val?.redirectedFrom?.meta?.title as string) || (val?.meta?.title as string),
            name: `${val?.fullPath}`,
            parentRoute: val.matched ? val.matched[val.matched.length - 2]?.path : ""
          })
        }
        // 获取当前点击的页面tab
        activeTab.value = pageTabs.value[pageTabs.value.length - 1].name
      }
      systemStore.setPageTabsData(pageTabs.value, activeTab.value)
    } else {
      // 跳转到已打开的tab
      activeTab.value = val.fullPath
    }
  },
  {
    deep: true
  }
)

// 删除tab时，自动切换路由
const removeTab = (targetName: TabPaneName) => {
  const tabs = pageTabs.value
  let activeName = activeTab.value
  // 删除的是当前选中标签时，tab左移或右移
  if (activeName === targetName) {
    tabs.forEach((tab, index) => {
      if (tab.name === targetName) {
        const nextTab = tabs[index + 1] || tabs[index - 1]
        if (nextTab) {
          activeName = nextTab.name
        }
      }
    })
  }
  activeTab.value = activeName
  pageTabs.value = tabs.filter(tab => tab.name !== targetName)
  systemStore.setPageTabsData(pageTabs.value, activeTab.value)
  router.push(activeTab.value)
}

/* ======================== 跳转翻拍系统 ======================== */
function jumpScanSystem() {
  window.open(`/scan-system`)
}

/* ======================== 菜单类型切换 ======================== */

const menuType = ref<MenuType>("user") // 菜单模式，分为病案管理用户端和管理员配置后台

const activeMenuType = ref("")
const menuGroupList = ref<Array<Record<string, any>>>([])
onMounted(async () => {
  menuNavigation.value = (await querySystemConfig({ type: "MENU_NAVIGATION" })).data?.data?.find(
    item => item.key === "menuNavigation"
  )?.value
  // tab模式
  if (menuNavigation.value === "1") {
    pageTabs.value = systemStore.pageTabs
    // 全局点击事件，关闭tab菜单
    const handleClick = () => {
      tabMenuVisible.value = false
    }
    document.addEventListener("click", handleClick)
  }

  // 当前所有模块
  menuGroupList.value = userStore.userMenus.filter(item => item.level === 0 && item.status && item.children.length)

  // 兼容从hideLayout: true页面返回时，切换路由
  activeTab.value = location.pathname
  menuGroupList.value.forEach(group => {
    group?.children?.forEach(level1 => {
      if (level1.path === activeTab.value || level1.functions.includes(activeTab.value)) {
        activeMenuType.value = group.id
        return
      }
      level1?.children?.forEach(level2 => {
        if (level2.path === activeTab.value || level2.functions.includes(activeTab.value)) {
          activeMenuType.value = group.id
          return
        }
      })
    })
  })

  // 打开的是targetPage
  if (!pageTabs.value.length) {
    nextTick(() => {
      pageTabs.value = [
        {
          title:
            currentRoute?.matched?.find(item => item?.children.some(child => child.path === currentRoute.path))?.meta
              ?.title || currentRoute?.meta?.title,
          name: currentRoute.path
        }
      ]
    })
  }
})

const switchMenuType = val => {
  localStorage.setItem("activeMenuTab", activeMenuType.value)
  if (val === "/scan-system/capture") {
    window.open(`/scan-system/capture`)
  } else {
    // 默认打开模块下的第一个路由
    const selectedMenu = menuGroupList.value?.find(item => item.id === activeMenuType.value)?.children
    router.push({ path: selectedMenu[0]?.path })
  }
}

// 路由切换后判断当前所属菜单类型(hardcode)
watchEffect(() => {
  if (adminModulePathList.find(item => currentRoute.path.startsWith(item))) {
    menuType.value = "admin"
  } else if (codingModulePathList.find(item => currentRoute.path.startsWith(item))) {
    menuType.value = "coding"
  } else if (physicalExaminationModulePathList.find(item => currentRoute.path.startsWith(item))) {
    menuType.value = "physical-examination"
  } else {
    menuType.value = "user"
  }
})

/* ======================== 页面加载配置 ======================== */

let loadingTimer

const loadingText = ref("")

watch(
  () => systemStore.pageLoading,
  val => {
    if (!val) {
      clearInterval(loadingTimer)
      return
    }
    loadingText.value = systemStore.loadingText
    loadingTimer = setInterval(() => {
      loadingText.value = loadingText.value.endsWith("...") ? loadingText.value.slice(0, -3) : loadingText.value + "."
    }, 500)
  }
)

onBeforeUnmount(() => clearInterval(loadingTimer))

/* ======================== 面包屑：动态响应路由 ======================== */

const isCollapse = ref(false)

type RouteNodeItem = { path: string; title: string }

const routeNodeList = computed(() => {
  let arr: Array<RouteNodeItem> = []
  function parseRouters(routers: Array<RouteRecordRaw>) {
    if (!routers.length) return
    const matchedSubRouter = routers.find(item => currentRoute.fullPath.startsWith(item.path))
    if (matchedSubRouter) {
      arr.push({ title: matchedSubRouter.meta?.title as string, path: matchedSubRouter.path })
    }
    parseRouters(matchedSubRouter?.children || [])
  }
  parseRouters(subRouters)
  return arr
})

// 切换左侧菜单伸缩状态
const toggleCollapseStatus = () => {
  isCollapse.value = !isCollapse.value
}

/* ======================== 初始化菜单配置 ======================== */

onMounted(() => {
  if (!localStorage.getItem("SessionWsid")) return
  // 加载系统配置菜单树
  getSystemResourceConfig().then(res => {
    const jsonData = res.data?.data ?? ""
    if (jsonData) {
      const resourceConfig = JSON.parse(jsonData)
      systemStore.$patch({ menus: resourceConfig })
    } else {
      updateSystemResourceConfig(JSON.stringify(initMenuData))

      systemStore.$patch({ menus: cloneDeep(initMenuData) })
    }
  })
})

/* ======================== 超时重新登录设置 ======================== */

let undoTimer

// 主动退出登录
const handleLogout = () => {
  clearInterval(undoTimer)
  logoutApi().finally(() => {
    userStore.clearUserStore()
    systemStore.clearPageParams()
    systemStore.clearPageTabsData()
    router.push({ path: "/login" })
  })
}

// 超时自动退出登录
const forceLogout = () => {
  clearInterval(undoTimer)
  SystemAlert("长时间未操作，为了您的账号安全请重新登录!", "warning")
    .finally(() => logoutApi())
    .finally(() => {
      userStore.clearUserStore()
      router.push({
        path: "/login",
        query: {
          // targetPage: `${window.location.pathname}${window.location.search}`
        }
      })
    })
}

// 30分钟无操作需要重新登录
onMounted(() => {
  const UNDO_TIME = 30 * 60 * 1000 // 暂时写死
  undoTimer = setInterval(() => {
    const activeTime = localStorage.getItem("activeTime") || "0"
    if (parseInt(activeTime) + UNDO_TIME < Date.now()) {
      clearInterval(undoTimer)
      localStorage.removeItem("activeTime")
      forceLogout()
    }
  }, 60 * 1000)
})

onBeforeUnmount(() => clearInterval(undoTimer))

// 网络状态监测
watchEffect(() => {
  if (!isOnline.value) {
    Message.warning("您的网络存在异常，请检查网络是否正常连接")
  }
})

/* ======================== 应用加载时获取常用选项 ======================== */

onMounted(() => {
  if (!localStorage.getItem("SessionWsid")) return
  getAllDeptApi().then(resData => {
    const departmentOptions = resData.map(item => ({ label: item.deptName, value: item.wsid }))
    globalOptionsStore.$patch({ departmentOptions: departmentOptions })
  })
  getBorrowTypeOptionsApi().then(resData => {
    const borrowTypeOptions = resData.map(item => ({ label: item.value, value: item.key }))
    globalOptionsStore.$patch({ borrowTypeOptions: borrowTypeOptions })
  })
  getBagsStatusListApi().then(resData => {
    const medicalRecordStatusOptions = resData.map(item => ({
      label: item.des,
      value: item.status,
      statusEnumName: item.statusEnumName,
      color: item.color
    }))
    globalOptionsStore.$patch({ medicalRecordStatusOptions: medicalRecordStatusOptions })
  })
  // 患者标签
  getPatientLabelOptionsApi().then(resData => {
    const patientLabelOptions = resData.map(item => ({ label: item.value, value: item.key }))
    globalOptionsStore.$patch({ patientLabelOptions: patientLabelOptions })
  })
})

/* ======================== 更新系统配置信息 ======================== */

watch(
  () => resourceStore.favicon,
  val => {
    if (val) {
      let link = document.querySelector("link[rel*='icon']") as HTMLLinkElement
      if (!link) return
      link.href = resourceStore.favicon
    }
  },
  {
    immediate: true,
    deep: true
  }
)
</script>

<style lang="less" scoped>
.common-layout {
  width: 100vw;
  height: 100vh;
  .el-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
    background-color: white;
    border-bottom: solid 1px rgb(225 226 230);
    background-color: #3860f4;
    .header-wrapper {
      display: flex;
      align-items: center;
      height: 100%;
      padding: 0 35px;
      .line {
        width: 0px;
        height: 16px;
        border: 1px solid rgba(255, 255, 255, 0.4);
      }
      i {
        color: #fff;
        font-size: 20px;
        padding: 0 24px;
      }
    }

    .menu-type-nav {
      // margin-right: 315px;
      flex: 1;
      .menu-tabs {
        :deep(.el-tabs__header) {
          margin-bottom: 0 !important;
        }

        :deep(.el-tabs__item) {
          font-size: 16px !important;
          color: rgba(255, 255, 255, 0.8) !important;
        }
        :deep(.is-active) {
          color: #fff !important;
        }

        :deep(.el-tabs__nav-wrap::after) {
          width: 0 !important;
        }
        :deep(.el-tabs__active-bar) {
          background-color: #fff;
        }
      }
    }

    .header-right-wrapper {
      display: flex;
      align-items: flex-end;

      :deep(.avatar-popover) {
        padding: 0;
        .el-popper__arrow {
          display: none;
        }
        .avatar-btn {
          height: 36px;
          font-size: 14px;
          text-align: center;
          color: rgb(3 8 20 / 85.1%);
          line-height: 36px;
          cursor: pointer;
          &:hover {
            background-color: rgb(231 242 255);
          }
        }
      }
      .avatar-wrapper {
        display: inline-flex;
        height: 32px;
        line-height: 32px;
        font-size: 14px;
        cursor: pointer;
        .avatar-name {
          margin-left: 5px;
          color: #fff;
          margin-right: 8px;
        }

        .avatar-img {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          overflow: hidden;
        }
      }
    }
  }
  .el-aside {
    background: #f2f6ff;
    transition: width 0.3s;
    // /* 设置滚动条滑块样式 */
    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2) !important; /* 设置滚动条滑块的背景颜色 */
      box-shadow: none;
    }
    &::-webkit-scrollbar-track {
      background: #f2f6ff !important; /* 设置滚动条轨道的背景颜色 */
      box-shadow: none;
    }
  }
  .normal-width {
    width: 220px;
    overflow-y: scroll;
    img {
      width: 100px;
    }
  }

  .mini-width {
    width: 64px;
    overflow-y: scroll;
    img {
      width: 50px;
    }
  }

  .aside-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0;

    .aside-logo {
      width: 106px;
    }
  }

  .container {
    height: calc(100% - 30px);
    min-width: 980px;
    min-height: 500px;
    .breadcrumb {
      margin-bottom: 20px;
    }
    :deep(.el-breadcrumb) {
      .el-breadcrumb__item:not(:last-child) {
        .el-breadcrumb__inner a,
        .el-breadcrumb__inner.is-link {
          font-weight: 400;
          color: #03081474;
        }
      }

      .el-breadcrumb__item {
        .el-breadcrumb__inner.is-link {
          font-weight: 400;
          color: #030814d9;
        }
      }
    }
  }

  .container-no-breadcrumb {
    height: 100%;
  }
}

/* 定义动画 */
@keyframes menu-in {
  from {
    opacity: 0;
    transform: scale(1);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.tab-menu {
  animation: menu-in 0.2s linear forwards;
  position: fixed;
  z-index: 999;
  // width: 10vw;
  // height: 10vh;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  box-shadow: 0px 2px 8px 0px rgba(51, 51, 51, 0.1);
  background-color: #fff;
  align-items: flex-start;
  justify-content: space-between;
  padding: 10px 0;
  font-size: 14px;

  .menu-item {
    width: calc(100% - 40px);
    padding: 5px 20px;
    cursor: pointer;

    &:hover {
      background: #ecf5ff;
    }
  }
}

.tab-title {
  // width: 100%;
  height: 100%;
  text-align: center;
  display: flex;
  align-items: center;
  padding-left: 20px;
  cursor: default;
}
.navigation-tabs {
  :deep(.el-tabs__item) {
    padding: 0 !important;
  }
  :deep(.is-active) {
    border-bottom: none !important;
    background-color: #fff !important;
  }
  :deep(.is-icon-close) {
    margin-right: 20px !important;
  }
}

.tab-mode {
  height: calc(100% - 60px) !important;
}
</style>
