<template>
  <!-- 登记弹窗 -->
  <DialogContainer v-model:visible="visible" title="缺失报告登记" no-footer :width="850" :close-callback="close">
    <div v-loading="loading" style="max-height: 70vh; overflow-y: auto">
      <el-form ref="registrationFormRef" :model="registrationInfo" :rules="registrationFormRules" label-width="120px">
        <!-- 补交日期 -->
        <el-form-item prop="predictDatetime" label="预计补交日期">
          <el-date-picker
            v-model="registrationInfo.predictDatetime"
            type="date"
            placeholder="补交日期"
            value-format="x"
            style="width: 100%"
          />
        </el-form-item>

        <!-- 病案类型 -->
        <CommonSelectFormItem
          v-model:model-value="registrationInfo.mrClassCode"
          label="文书分类"
          prop="mrClassCode"
          filterable
          :options="curDeptRecordTypeOptions"
        />

        <!-- 报告类型 -->
        <CommonSelectFormItem
          v-model:model-value="registrationInfo.type"
          label="报告类型"
          prop="type"
          :options="missingReportTypeOptions"
        />

        <!-- 报告名称 -->
        <CommonInputFormItem
          v-model:model-value="registrationInfo.name"
          prop="name"
          label="报告名称"
        ></CommonInputFormItem>

        <!-- 缺失原因 -->
        <el-form-item prop="reason" label="缺失原因">
          <el-input
            v-model="registrationInfo.reason"
            show-word-limit
            type="textarea"
            maxlength="100"
            placeholder="请输入缺失原因"
          />
        </el-form-item>

        <div class="flex-end" style="margin-bottom: 20px">
          <el-button type="primary" plain @click="handleModifyReport">
            {{ formType === "add" ? "添加" : "确定" }}
          </el-button>
        </div>
      </el-form>

      <!-- 缺失登记表格 -->
      <BaseTable :data="registrationTableData" :columns="missingReportTableColumns" border>
        <template #status="{ row }">
          <el-tag v-if="row.status === MissingReportStatusEnum.WAIT_SUBMIT" type="warning">待补交</el-tag>
          <el-tag v-else-if="row.status === MissingReportStatusEnum.COMPLETE_SUBMIT" type="success">已补交</el-tag>
        </template>
        <template #predictDatetime="{ row }">
          {{ formatDate(row.predictDatetime) }}
        </template>
        <template #mrClassCode="{ row }">
          {{ $props.getRecordType(row.mrClassCode) }}
        </template>
        <template #type="{ row }">
          <span v-if="row.type === MissingReportTypeEnum.ELECTRON">电子</span>
          <span v-else-if="row.type === MissingReportTypeEnum.PAPER">纸质</span>
          <span v-else>未知</span>
        </template>
        <template #operation="{ row }">
          <TableButton
            tooltip="已完成补交无法修改"
            :disabled="row.status === MissingReportStatusEnum.COMPLETE_SUBMIT"
            @click="handleEditReport(row)"
          >
            修改
          </TableButton>
          <TableButton
            tooltip="已完成补交无法删除"
            :disabled="row.status === MissingReportStatusEnum.COMPLETE_SUBMIT"
            @click="handleDeleteReport(row)"
          >
            删除
          </TableButton>
        </template>
      </BaseTable>
    </div>
  </DialogContainer>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import { BaseTable, DialogContainer, CommonSelectFormItem, CommonInputFormItem, TableButton } from "@/base-components"
import { MissingReportStatusEnum, MissingReportTypeEnum } from "@/configs"
import { useCommonOptions } from "@/hooks"
import {
  deleteMissingReportApi,
  getMissingReportListApi,
  getRecordTypeByDeptApi,
  submitMissingReportApi,
  getAllDocTypes
} from "@/interfaces"
import { BaseOptionItem } from "@/types"
import { formatDate, Message, SystemPrompt, toastError } from "@/utils"
import { missingReportTableColumns, missingReportTypeOptions, registrationFormRules } from "../config"

const props = defineProps<{ selectedRow: Record<string, any>; getRecordType: any }>()
const emits = defineEmits(["success"])

const modified = ref(false) // 是否已修改登记表格
const loading = ref(false)

/* =============== 表单 ============== */

const registrationFormRef = ref()
const formType = ref<"add" | "edit">("add")

const registrationInfo = reactive({
  predictDatetime: "",
  mrClassCode: "",
  type: "",
  name: "",
  reason: "",
  wsid: ""
})

// 点击添加缺失登记报告
const handleModifyReport = async (row: any) => {
  if (formType.value === "add") {
    for (let report of registrationTableData.value) {
      if (report.name === registrationInfo.name) {
        Message.error("重复的报告名称！")
        return
      }
    }
  }
  registrationFormRef.value?.validate(async (valid: boolean) => {
    if (!valid) return
    loading.value = true
    try {
      await submitMissingReportApi(props.selectedRow?.wsid, props.selectedRow?.inpNo, registrationInfo)
      reset()
      modified.value = true
      registrationTableData.value = (await getMissingReportListApi(props.selectedRow.wsid)).data.data ?? []
      Message.success(formType.value === "add" ? "添加成功！" : "修改成功！")
      loading.value = false
    } catch (error: any) {
      loading.value = false
      toastError(error)
    }
  })
}

// 删除报告
const handleDeleteReport = async row => {
  try {
    await SystemPrompt("确认删除该报告吗？")
  } catch {
    return
  }
  try {
    loading.value = true
    await deleteMissingReportApi(row.wsid)
    modified.value = true
    registrationTableData.value = (await getMissingReportListApi(props.selectedRow.wsid)).data.data ?? []
    Message.success("删除成功！")
    loading.value = false
  } catch (error: any) {
    toastError(error)
    loading.value = false
  }
}

// 编辑单条报告
const handleEditReport = row => {
  registrationInfo.wsid = row.wsid
  registrationInfo.predictDatetime = row.predictDatetime
  registrationInfo.mrClassCode = row.mrClassCode
  registrationInfo.type = row.type
  registrationInfo.name = row.name
  registrationInfo.reason = row.reason
  formType.value = "edit"
}

/* =============== 表格 ============== */

// 当前科室的病案数据类型
// let curDeptRecordTypeOptions = ref<BaseOptionItem[]>([])
const { options: curDeptRecordTypeOptions } = useCommonOptions({
  getOptionsApi: getAllDocTypes,
  labelAlias: "mrClassName",
  valueAlias: "mrClassCode"
})
// 已经提交的缺失登记表格数据
const registrationTableData = ref<Record<string, any>[]>([])

// 打开登记弹窗
const init = async () => {
  try {
    loading.value = true
    console.log(`output->props.selectedRow`, props.selectedRow)
    registrationInfo.mrClassCode = props.selectedRow.mrClassCode
    registrationInfo.type = props.selectedRow.mrDataSourceType === "ANY" ? "" : props.selectedRow.mrDataSourceType
    const res = await Promise.all([
      getRecordTypeByDeptApi(props.selectedRow.outHospitalDeptWsid),
      getMissingReportListApi(props.selectedRow.wsid)
    ])
    // 根据科室信息获取文书分类
    // const { options } = useCommonOptions({
    //   optionsDataSource: res[0].data,
    //   labelAlias: "mrClassName",
    //   valueAlias: "mrClassCode"
    // })
    // curDeptRecordTypeOptions.value = options.value
    registrationTableData.value = res[1].data.data ?? []
    loading.value = false
  } catch (error: any) {
    toastError(error)
    loading.value = false
  }
}

/* =========== dialog =========== */

const visible = ref(false)

const show = () => {
  visible.value = true
  init()
}

const close = () => {
  if (modified.value) emits("success")
  reset()
  visible.value = false
  modified.value = false
}

const reset = () => {
  registrationFormRef.value?.resetFields()
  registrationInfo.wsid = ""
  registrationTableData.value = []
  formType.value = "add"
}

defineExpose({
  show,
  close
})
</script>
