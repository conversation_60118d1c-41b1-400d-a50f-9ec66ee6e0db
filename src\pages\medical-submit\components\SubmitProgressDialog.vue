<template>
  <DialogContainer
    title="提交进度"
    :visible="state.visible"
    no-footer
    :width="600"
    :confirm-callback="close"
    :close-callback="close"
  >
    <div class="progress">
      <div style="margin-bottom: 10px">
        <span class="progress__text">文件处理中 {{ state.completedFileCount }}/{{ state.totalFileCount }}</span>
        <span class="progress__tip">关闭窗口后不会终止，提交过程将会继续运行</span>
      </div>
      <el-progress
        :text-inside="true"
        :percentage="percentage"
        :stroke-width="18"
        striped
        striped-flow
        :duration="10"
      ></el-progress>
    </div>

    <div class="footer flex-center">
      <el-button type="primary" @click="close">确定</el-button>
    </div>
  </DialogContainer>
</template>

<script setup lang="ts">
import { computed, reactive } from "vue"
import { DialogContainer } from "@/base-components"
import { getSubmitProgressApi } from "@/interfaces"
import { toastError } from "@/utils"

const props = defineProps<{ inpNo: string }>()

const emits = defineEmits(["success", "fail"])

/* ============== 进度 ============== */

const state = reactive({
  visible: false,
  totalFileCount: 0,
  completedFileCount: 0,
  requestCount: 0
})

// 处理进度的进度条百分比
const percentage = computed(() => {
  return Math.round((state.completedFileCount / state.totalFileCount) * 100) || 10
})

// 初始化进度条数据
const init = (completedFileCount: number, totalFileCount: number) => {
  state.completedFileCount = completedFileCount
  state.totalFileCount = totalFileCount
  open()
  setProgressInterval()
}

/* ============== 轮询进度 ============== */

let progressInterval // 轮询定时器

// 设置轮询定时器
const setProgressInterval = () => {
  progressInterval = setInterval(() => {
    // 已经有请求则return
    if (state.requestCount >= 1) return
    state.requestCount += 1
    getSubmitProgressApi(props.inpNo)
      .then(res => {
        state.requestCount -= 1
        const { completeFlag, errorMsg } = res.data.data
        if (errorMsg?.length) {
          close()
          emits("fail", errorMsg)
        } else if (completeFlag === 2) {
          state.completedFileCount = res.data.data.submitCount
          state.totalFileCount = res.data.data.total
          // 提交成功 延时完成让用户看到效果
          setTimeout(() => {
            close()
            emits("success")
          }, 1000)
        } else if (completeFlag === 1) {
          // 已经提交但在提交中
          state.completedFileCount = res.data.data.submitCount
          state.totalFileCount = res.data.data.total
        } else {
          // 未提交，正常提交流程-正常情况不会出现在此组件中
        }
      })
      .catch(error => {
        state.requestCount -= 1
        toastError(error)
      })
  }, 3000)
}

/* ============== 其他 ============== */

const open = () => {
  state.visible = true
}

const close = () => {
  if (progressInterval) clearInterval(progressInterval)
  state.visible = false
}

defineExpose({ init, open, close })
</script>

<style lang="less" scoped>
.progress {
  &__text {
    font-size: 14px;
    margin-bottom: 6px;
  }

  &__tip {
    font-size: 12px;
    color: #f56c6c;
    margin-left: 10px;
  }
}

.footer {
  margin-top: 20px;
}
</style>
