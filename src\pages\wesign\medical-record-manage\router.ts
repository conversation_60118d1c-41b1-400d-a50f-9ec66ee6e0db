import { RouteRecordRaw } from "vue-router"
import { MenuOperationEnum } from "@/configs"

const medicalRecordRouter: RouteRecordRaw = {
  path: "/medical-record",
  name: "MedicalRecordManage",
  redirect: "/medical-record/list",
  meta: {
    title: "病案管理",
    icon: "ri-folder-open-line",
    type: "multiple",
    showBreadcrumb: true
  },
  children: [
    // 病案查询
    {
      path: "/medical-record/list",
      meta: {
        title: "病案查询",
        operations: [
          { id: MenuOperationEnum.Export, name: "导出" },
          { id: MenuOperationEnum.View, name: "查看" },
          { id: MenuOperationEnum.Submit, name: "提交" },
          { id: MenuOperationEnum.MissingReport, name: "缺失登记" },
          { id: MenuOperationEnum.Special, name: "特殊病案" },
          { id: MenuOperationEnum.Trace, name: "示踪" },
          { id: MenuOperationEnum.Favorite, name: "收藏" },
          { id: MenuOperationEnum.Sync, name: "同步" },
          { id: MenuOperationEnum.History, name: "历史" },
          { id: MenuOperationEnum.Capture, name: "拍摄" }
        ]
      },
      redirect: "/medical-record/list/recently",
      children: [
        {
          path: "/medical-record/list/recently",
          meta: { title: "运行数据", hideMenu: true },
          component: () => import("./medical-record-home/recently/index.vue")
        },
        {
          path: "/medical-record/list/history",
          meta: { title: "历史数据", hideMenu: true },
          component: () => import("./medical-record-home/history/index.vue")
        }
      ]
    },
    // 收藏
    {
      path: "/medical-record/collection",
      meta: {
        title: "我的收藏",
        operations: [
          { id: MenuOperationEnum.Borrow, name: "借阅" },
          { id: MenuOperationEnum.Favorite, name: "收藏" }
        ]
      },
      component: () => import("./medical-record-collection/index.vue")
    },
    // 签收
    {
      path: "/medical-record/sign-for",
      meta: {
        title: "病案签收",
        operations: [
          { id: MenuOperationEnum.View, name: "查看" },
          { id: MenuOperationEnum.SignFor, name: "签收" }
        ]
      },
      redirect: "/medical-record/sign-for/waiting",
      children: [
        {
          path: "/medical-record/sign-for/waiting",
          component: () => import("./medical-record-sign-for/Waiting.vue"),
          meta: { title: "待签收" }
        },
        {
          path: "/medical-record/sign-for/signed",
          component: () => import("./medical-record-sign-for/Signed.vue"),
          meta: { title: "已签收" }
        },
        {
          path: "/medical-record/sign-for/returned",
          component: () => import("./medical-record-sign-for/Returned.vue"),
          meta: { title: "已退回" }
        },
        {
          path: "/medical-record/sign-for/detail",
          component: () => import("./medical-record-sign-for/Detail.vue"),
          meta: { title: "签收" }
        }
      ]
    },
    // 退回
    {
      path: "/medical-record/returned",
      meta: {
        title: "病案退回",
        operations: [{ id: MenuOperationEnum.Submit, name: "提交" }]
      },
      component: () => import("./medical-record-returned/index.vue")
    },
    // 二级页面
    {
      path: "/medical-record/detail",
      meta: { title: "病案详情", hideMenu: true },
      component: () => import("./sub-pages/detail.vue")
    },
    {
      path: "/medical-record/trace",
      meta: { title: "病案示踪", hideMenu: true },
      component: () => import("./sub-pages/trace.vue")
    },
    {
      path: "/medical-record/history",
      meta: { title: "病案历史", hideMenu: true },
      component: () => import("./sub-pages/history.vue")
    }
  ]
}

export default medicalRecordRouter
