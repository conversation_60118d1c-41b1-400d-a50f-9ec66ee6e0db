<template>
  <div class="common-search-container">
    <!-- 头部搜素栏 -->
    <div class="common-search-header">
      <!-- <TabsRouter :tabs-config="tabsRouterList" /> -->
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <el-form-item>
          <el-input v-model="searchFormState.inputValue">
            <template #prepend>
              <el-select v-model="searchFormState.inputField" style="width: 100px" @change="changeSelectValue">
                <el-option label="值" value="key" />
                <el-option label="名称" value="value" />
              </el-select>
            </template>
          </el-input>
        </el-form-item>
      </SearchContainer>
    </div>

    <div class="code-table-content">
      <!-- 左侧值域列表 -->
      <PageSideList
        ref="pageSideListRef"
        title="值域列表"
        :list-data="dictionaryNameState.list"
        :loading="dictionaryNameState.loading"
        @click-add="clickAddDictionaryName"
        @click-edit="clickEditDictionaryName"
        @click-delete="clickDeleteDictionaryName"
        @change-active="clickDictionaryName"
      />

      <!-- 值域表格 -->
      <div style="flex: 1; padding: 20px" class="common-box-shadow">
        <CommonTable
          ref="metaCodeTableRef"
          :table-columns="codeTableColumns"
          :request-api="searchMetaCodeTableFieldApi"
          :request-params="searchParams"
          :data-callback="dataCallback"
        >
          <template #header>
            <div style="display: flex; align-items: center">
              <AddButton @click="handleAdd()">新增</AddButton>
              <ExportButton
                :export-api="metaCodeExportApi"
                file-name="元数据值域代码表.xlsx"
                file-type="application/vnd.ms-excel"
              >
                批量导出
              </ExportButton>
              <ImportButton @click="importDialogRef?.openUserImportDialog()">批量导入</ImportButton>
            </div>
          </template>
          <template #enableDefaultDesc="{ row }">
            <el-switch v-model="row.enableDefaultDesc" @change="handleEnableDefault(row)" />
          </template>
          <template #operation="{ row }">
            <TableButton @click="handleEditBtn(row)">编辑</TableButton>
            <TableButton @click="handleDelete(row)">删除</TableButton>
          </template>
        </CommonTable>
      </div>
    </div>

    <!-- 新增/编辑值映射弹窗 -->
    <!-- <DialogForm
      v-model:visible="dialogVisible"
      :title="`${isEdit ? '编辑' : '新增'}值映射`"
      :form-config="metaCodeDialogFormConfig"
      :form-state="rangeState.dialogFormData"
      :confirm-callback="rangeConfirmBack"
      :disabled-fields="isEdit ? ['key'] : []"
    /> -->
    <DialogContainer
      v-model:visible="dialogVisible"
      :title="`${isEdit ? '编辑' : '新增'}值映射`"
      :width="470"
      :confirm-callback="rangeConfirmBack"
      :confirm-loading="mappingFormLoading"
    >
      <el-form ref="mappingFormRef" :model="rangeState.dialogFormData" label-width="120px" :rules="rules">
        <CommonInputFormItem
          v-model="rangeState.dialogFormData.key"
          :disabled="isEdit"
          label="值："
          placeholder="请输入值"
          prop="key"
        />
        <CommonInputFormItem
          v-model="rangeState.dialogFormData.value"
          label="名称："
          placeholder="请输入名称"
          prop="value"
        />

        <el-form-item label="对照：" style="display: flex; align-items: flex-start">
          <div class="compare">
            <div class="compare-container">
              <div v-for="(item, index) in mappingList" :key="index" class="compare-item">
                <el-input v-model="item.hospitalKey" placeholder="请输入对照值" />
                <el-input v-model="item.hospitalValue" placeholder="请输入对照名称" />
                <el-button type="danger" :icon="Delete" text @click="removeCompareItem(index)" />
              </div>
            </div>

            <div class="add-btn">
              <el-button @click="addCompareItem">添加</el-button>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </DialogContainer>

    <!-- 新增/编辑值域配置项弹窗 -->
    <DialogForm
      v-model:visible="dictionaryNameState.dialogVisible"
      :title="`${isEdit ? '编辑' : '新增'}值域配置项`"
      :form-config="classifyDialogConfig"
      :form-state="dictionaryNameState.dialogFormData"
      :disabled-fields="isEdit ? ['code'] : []"
      :confirm-callback="dictionaryConfirmCallback"
    />

    <BatchImport
      ref="importDialogRef"
      :download-template-api="downloadMetaCodeTemplateApi"
      :import-file-api="metaCodeImportApi"
      :confirm-callback="refresh"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue"
import { cloneDeep } from "lodash-es"
import { Delete } from "@element-plus/icons-vue"
import {
  SearchContainer,
  CommonTable,
  AddButton,
  TableButton,
  ImportButton,
  ExportButton,
  DialogContainer,
  CommonInputFormItem
} from "@/base-components"
import { DialogForm, PageSideList, BatchImport } from "@/page-components"
import { codeTableColumns, metaCodeDialogFormConfig, classifyDialogConfig, rules } from "./config"
import {
  searchStandardRangeClassifyApi,
  addStandardRangeClassifyApi,
  editStandardRangeClassifyApi,
  deleteStandardRangeClassifyApi,
  searchMetaCodeTableFieldApi,
  addStandardRangeApi,
  editStandardRangeApi,
  deleteStandardRangeApi,
  downloadMetaCodeTemplateApi,
  metaCodeImportApi,
  metaCodeExportApi,
  setStandardOptionsDefaultApi
} from "./interface"
import { useSystemStore } from "@/stores"
import { Message, SystemPrompt, toastError } from "@/utils/message-tool"

const systemStore = useSystemStore()
const isEdit = ref(false)
const importDialogRef = ref<InstanceType<typeof BatchImport>>()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  inputField: "key",
  inputValue: ""
})

const searchParams = reactive({
  filters: ""
})

// 切换过滤属性
function changeSelectValue(nextField: string) {
  searchFormState.inputField = nextField
  searchFormState.inputValue = ""
}

function handleReset() {
  searchFormState.inputField = "key"
  searchFormState.inputValue = ""
  searchParams.filters = ""
  dictionaryNameState.choiceCode = ""
  pageSideListRef.value?.reset()
}

function handleQuery() {
  if (searchFormState.inputValue) {
    searchParams.filters = dictionaryNameState.choiceCode
      ? `${searchFormState.inputField}=${searchFormState.inputValue},code=${dictionaryNameState.choiceCode}`
      : `${searchFormState.inputField}=${searchFormState.inputValue}`
  }
}

function dataCallback(data: Array<any>) {
  return data.map(item => ({
    ...item,
    enableDefaultDesc: item.enableDefault === 1
  }))
}

/* ======================== 左侧值域列表&编辑弹窗 ======================== */

const pageSideListRef = ref<InstanceType<typeof PageSideList>>()

const dictionaryNameState = reactive({
  list: [],
  dialogVisible: false,
  dialogFormData: {
    name: "",
    code: ""
  },
  loading: false,
  choiceCode: ""
})

onMounted(() => {
  getMetaCodeTableRange()
})

// 获取值域列表
const getMetaCodeTableRange = async () => {
  dictionaryNameState.loading = true
  try {
    const res = await searchStandardRangeClassifyApi()
    const rows = res.data?.data ?? []
    dictionaryNameState.list = rows.map(item => {
      return {
        name: `${item.name}(${item.code})`,
        id: item.id,
        oriName: item.name,
        oriCode: item.code
      }
    })
  } catch (error) {
    toastError(error, "获取值域列表失败")
  }
  dictionaryNameState.loading = false
}

// 切换当前选中值域项
const clickDictionaryName = (id, code) => {
  searchParams.filters = searchFormState.inputValue
    ? `${searchFormState.inputField}=${searchFormState.inputValue},code=${code}`
    : `code=${code}`
  dictionaryNameState.choiceCode = code
}

// 新增值域项
const clickAddDictionaryName = () => {
  isEdit.value = false
  dictionaryNameState.dialogFormData = {
    name: "",
    code: ""
  }
  dictionaryNameState.dialogVisible = true
}

// 编辑值域项
const clickEditDictionaryName = item => {
  isEdit.value = true
  dictionaryNameState.dialogVisible = true
  dictionaryNameState.dialogFormData = {
    name: item.oriName,
    code: item.oriCode
  }
}

// 删除值域项
const clickDeleteDictionaryName = item => {
  SystemPrompt(`您正在删除名称为“${item.name}”的值域代码配置，确定删除？`, "error").then(() => {
    deleteStandardRangeClassifyApi(item.id)
      .then(() => {
        Message.success("操作成功")
        handleReset()
        getMetaCodeTableRange()
      })
      .catch(error => toastError(error))
  })
}

// 值域新增&编辑确认
const dictionaryConfirmCallback = async () => {
  if (!isEdit.value) {
    return addStandardRangeClassifyApi({ ...dictionaryNameState.dialogFormData })
      .then(() => {
        Message.success("添加成功")
        dictionaryNameState.dialogVisible = false
        getMetaCodeTableRange()
      })
      .catch(err => {
        toastError(err, "添加失败")
      })
  } else {
    return editStandardRangeClassifyApi({ ...dictionaryNameState.dialogFormData })
      .then(() => {
        Message.success("修改成功")
        dictionaryNameState.dialogVisible = false
        getMetaCodeTableRange()
      })
      .catch(err => {
        toastError(err, "修改失败")
      })
  }
}

/* ======================== 值域代码表格&编辑弹窗 ======================== */

const metaCodeTableRef = ref<InstanceType<typeof CommonTable>>()

// 值域弹窗visible
const dialogVisible = ref(false)

const initRangeData = { key: "", value: "", remark: "", hospitalKey: "", hospitalValue: "" }

// 值域数据状态
const rangeState = reactive({
  dialogFormData: cloneDeep(initRangeData),
  editData: null as null | Record<string, any>
})

// 新增值映射
function handleAdd() {
  rangeState.dialogFormData = cloneDeep(initRangeData)
  dialogVisible.value = true
  isEdit.value = false
  mappingList.value = [{ hospitalValue: "", hospitalKey: "" }]
}

// 修改值映射
const handleEditBtn = async row => {
  isEdit.value = true
  mappingList.value = [{ hospitalValue: "", hospitalKey: "" }]
  rangeState.dialogFormData.key = row.key
  rangeState.dialogFormData.value = row.value
  rangeState.dialogFormData.remark = row.remark
  rangeState.dialogFormData.hospitalKey = row.hospitalKey
  rangeState.dialogFormData.hospitalValue = row.hospitalValue
  row?.hospitalKey?.split(",")?.forEach((item, index) => {
    mappingList.value[index] = { hospitalKey: item, hospitalValue: "" }
  })
  row?.hospitalValue?.split(",")?.forEach((item, index) => {
    if (!mappingList.value[index]) mappingList.value[index] = { hospitalKey: "", hospitalValue: "" }
    mappingList.value[index].hospitalValue = item
  })
  rangeState.editData = row
  dialogVisible.value = true
}

// 删除值映射
function handleDelete(row) {
  SystemPrompt(`您正在删除名称为“${row.value}”的值映射，是否确定删除？`, "error").then(() => {
    deleteStandardRangeApi(row.id)
      .then(() => {
        metaCodeTableRef.value?.refreshTableData()
        Message.success("删除成功")
      })
      .catch(error => toastError(error, "删除失败"))
  })
}

const mappingFormRef = ref()
const mappingFormLoading = ref(false)
const mappingList = ref<Array<Record<string, any>>>([])

const removeCompareItem = index => {
  mappingList.value.splice(index, 1)
}
const addCompareItem = () => {
  mappingList.value.push({ hospitalValue: "", hospitalKey: "" })
}

// 修改值域
const rangeConfirmBack = async () => {
  if (!dictionaryNameState.choiceCode) {
    Message.warning("请先选择左侧值域")
    return
  }
  mappingFormRef.value?.validate(async valid => {
    if (!valid) return
    mappingFormLoading.value = true
    try {
      rangeState.dialogFormData.hospitalKey = mappingList.value.map(item => item.hospitalKey).join(",")
      rangeState.dialogFormData.hospitalValue = mappingList.value.map(item => item.hospitalValue).join(",")
      const params = isEdit.value
        ? { id: rangeState?.editData?.id, code: rangeState?.editData?.code, ...rangeState.dialogFormData }
        : { ...rangeState.dialogFormData, code: dictionaryNameState.choiceCode }
      const handle = isEdit.value ? editStandardRangeApi : addStandardRangeApi
      await handle(params)
      metaCodeTableRef.value?.refreshTableData()
      dialogVisible.value = false
      mappingFormLoading.value = false
      Message.success(isEdit.value ? "修改成功" : "新增成功")
    } catch (error: any) {
      mappingFormLoading.value = false
      toastError(error, isEdit.value ? "修改失败" : "新增失败")
    }
  })
}

const refresh = () => {
  metaCodeTableRef.value?.refreshTableData()
}

// 设置默认值
const handleEnableDefault = async row => {
  try {
    systemStore.showLoading("加载中")
    await setStandardOptionsDefaultApi({
      code: row.code,
      key: row.key,
      enabledDefault: row.enableDefaultDesc ? "ENABLE" : "DISABLE"
    })
    metaCodeTableRef.value?.refreshTableData()
    systemStore.hideLoading()
  } catch (error: any) {
    toastError(error, "修改失败")
    systemStore.hideLoading()
  }
}
</script>

<style lang="less" scoped>
.code-table-content {
  display: flex;
  height: 0;
  flex: 1;
}
.compare {
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  &-container {
    max-height: 200px;
    overflow-y: auto;
  }

  &-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }

  .el-input {
    width: 40%;
  }

  .add-btn {
    width: 100%;
    margin-top: 10px;
  }
}
</style>
