<template>
  <PageContainer separate>
    <!-- 头部搜索 -->
    <template #search>
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <DepartmentFormItem
          v-model:model-value="archivalStatisticParams.deptWsid"
          label="出院科室"
        ></DepartmentFormItem>

        <DaterangeFormItem
          v-model:model-value="searchFormState.outHospitalDatetime"
          :is-clearable="false"
          label="出院时间"
        />
      </SearchContainer>
    </template>

    <template #table>
      <div v-if="!archivalItemCode.length" v-loading="userTableDataLoading" style="height: 100%"></div>
      <!-- 表格展示 -->
      <CommonTable
        v-if="isDisplayTable && archivalItemCode.length"
        class="archival-statistics-table"
        :table-columns="newArchivalStatisticsColumn"
        :request-api="getArchivalPercentageTableApi"
        :request-params="tableSearchParams"
        :data-callback="formatArchivalTableData"
        :pagination="false"
      >
        <template #header>
          <div class="header-container flex-between">
            <div class="flex">
              <ExportButton
                :export-api="exportArchiveStatisticsApi"
                :export-params="exportParams"
                :file-name="`归档率统计表_${getCurrentDate()}.xlsx`"
                file-type="application/vnd.ms-excel"
              >
                导出
              </ExportButton>
              <el-button
                type="primary"
                style="margin-left: 10px"
                :icon="h('i', { class: 'ri-menu-search-line' })"
                @click="() => customConfigDialogRef.showCustomConfigDialog()"
              >
                自定义指标
              </el-button>
            </div>

            <div class="table-chart-tool flex-center">
              <div class="table-setting-btn" @click="() => userConfigDialogRef.showUserConfigDialog()">
                <span class="ri-settings-4-line setting-icon"></span>
                <span class="table-setting-text">自定义配置</span>
              </div>
              <el-tooltip content="表格展示">
                <i v-if="isDisplayTable"><img src="@/assets/svg/statistical-statement/table-active.svg" alt="" /></i>
                <i v-else @click="changeTableChart">
                  <img src="@/assets/svg/statistical-statement/table.svg" alt="" />
                </i>
              </el-tooltip>
              <el-tooltip content="图表展示">
                <i v-if="isDisplayTable" @click="changeTableChart">
                  <img src="@/assets/svg/statistical-statement/chart.svg" alt="" />
                </i>
                <i v-else><img src="@/assets/svg/statistical-statement/chart-active.svg" alt="" /></i>
              </el-tooltip>
            </div>
          </div>
        </template>

        <template #totalArchiveCount="{ row }">
          <span
            style="display: inline-block; width: 100%; cursor: pointer; color: #409eff"
            @click="handleClick(row, '')"
          >
            {{ row.totalArchiveCount }}
          </span>
        </template>
        <template v-for="item in dynamicProp" :key="item" #[`${item}`]="{ row }">
          <span
            style="display: inline-block; width: 100%; cursor: pointer; color: #409eff"
            @click="handleClick(row, item)"
          >
            {{ row[item] }}
          </span>
        </template>
      </CommonTable>

      <!-- 图表展示 -->
      <ArchivalStatisticsChart
        v-else-if="!isDisplayTable"
        v-model:show-chart="isDisplayTable"
        :request-params="searchParams.queryParams"
      ></ArchivalStatisticsChart>

      <CustomConfigDialog ref="customConfigDialogRef"></CustomConfigDialog>

      <UserConfigDialog ref="userConfigDialogRef" @update:config="handleUpdateConfig"></UserConfigDialog>
    </template>
  </PageContainer>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h, watch, computed } from "vue"
import { useRouter } from "vue-router"
import {
  PageContainer,
  SearchContainer,
  DaterangeFormItem,
  DepartmentFormItem,
  ExportButton,
  CommonTable
} from "@/base-components"
import { useTableSearch } from "@/hooks"
import { toastError } from "@/utils"
import { getCurrentDate } from "../config"
import { getArchivalPercentageTableApi, getUserArchivalConfigApi, exportArchiveStatisticsApi } from "../interface"
import { CustomConfigDialog, UserConfigDialog, ArchivalStatisticsChart } from "./components"
import { archivalStatisticsColumn, parseColumn, getArchivalCountProp, getCustomConfig } from "./config"

const router = useRouter()

const newArchivalStatisticsColumn = ref(archivalStatisticsColumn)

// 归档指标
const archivalItemCode = ref<Array<string>>([])

// 归档天数options
const archivalDaysOptions = ref<Array<Record<string, any>>>([])

const isDisplayTable = ref(true)

const userTableDataLoading = ref(false)

const dynamicProp = ref<Array<string>>([])

onMounted(() => {
  getArchivalData()
})

const archiveDateEnd = new Date(new Date().setHours(23, 59, 59, 999)).getTime()
const archiveDateStart = new Date(
  new Date(new Date().setMonth(new Date().getMonth() - 1)).setHours(0, 0, 0, 0)
).getTime()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  deptWsid: "",
  outHospitalDatetime: [archiveDateStart, archiveDateEnd] as [number, number]
})

const archivalStatisticParams = reactive({
  deptWsid: "",
  archiveDateStart: archiveDateStart,
  archiveDateEnd: archiveDateEnd
})

const { searchParams, handleQuery, handleReset } = useTableSearch(
  {},
  "",
  { ...searchFormState, ...archivalStatisticParams },
  archivalStatisticParams,
  searchFormState
)

const tableSearchParams = computed(() => {
  return { ...searchParams.queryParams, itemCode: archivalItemCode.value }
})

watch(
  () => searchFormState.outHospitalDatetime,
  val => {
    archivalStatisticParams.archiveDateStart = val[0]
    archivalStatisticParams.archiveDateEnd = val[1]
  }
)

function handleClick(item, prop) {
  const label = newArchivalStatisticsColumn.value.find(item => item.prop === prop)?.label
  const itemType = archivalDaysOptions.value.find(item => item.label === label)?.value || ""
  router.push(
    `/statistical-statement/archival-statistics-detail?deptWsid=${
      item.deptName ? (item.deptWsid ? item.deptWsid : "") : ""
    }&archiveDateStart=${JSON.stringify(archivalStatisticParams.archiveDateStart)}&archiveDateEnd=${JSON.stringify(
      archivalStatisticParams.archiveDateEnd
    )}&itemType=${itemType}`
  )
}

// 向数组的指定位置插入数组
function insertIntoArray(originalArray, elementsToInsert) {
  const result = originalArray.concat(elementsToInsert)
  return result
}

// 切换表格或柱状图
function changeTableChart() {
  isDisplayTable.value = !isDisplayTable.value
}

/* ======================== 自定义指标与自定义配置 ======================== */

const customConfigDialogRef = ref()
const userConfigDialogRef = ref()

/* ======================== 表格数据 ======================== */

function getArchivalData() {
  const itemCode: Array<string> = []
  userTableDataLoading.value = true
  // 获取用户自定义配置信息
  getUserArchivalConfigApi()
    .then(res => {
      const userConfigData = res.data.data
      archivalDaysOptions.value = getCustomConfig(userConfigData)
      userConfigData.forEach(item => {
        if (item.archiveCountCode && item.archiveCountCheck) {
          itemCode.push(item.archiveCountCode)
        }
        if (item.archivePercentageCode && item.archivePercentageCheck) {
          itemCode.push(item.archivePercentageCode)
        }
      })
      archivalItemCode.value = itemCode
    })
    .catch(err => {
      toastError(err, "获取自定义配置失败")
    })
    .finally(() => {
      userTableDataLoading.value = false
    })
}

// 格式化表格数据
function formatArchivalTableData(res) {
  // 获取所有归档数的字符
  dynamicProp.value = getArchivalCountProp(res.data.data.rows)
  // 动态解析columns
  const result = parseColumn(res.data.data.rows)
  newArchivalStatisticsColumn.value = insertIntoArray(archivalStatisticsColumn, result)
  return res.data.data.rows
}

// 自定义配置更新后更新表格
function handleUpdateConfig() {
  // 获取表格数据
  getArchivalData()
}

const exportParams = computed(() => {
  return {
    archiveDateStart: searchFormState.outHospitalDatetime[0],
    archiveDateEnd: searchFormState.outHospitalDatetime[1],
    itemCode: archivalItemCode.value
  }
})
</script>

<style lang="less" scoped>
.table-chart-tool {
  .table-setting-btn {
    margin-right: 8px;
    padding: 4px 8px;
    cursor: pointer;
  }

  i {
    cursor: pointer;
    img {
      vertical-align: bottom;
    }
  }
}
</style>

<style lang="less">
.archival-statistics-table {
  .common-table-header {
    display: block !important;
  }
}
</style>
