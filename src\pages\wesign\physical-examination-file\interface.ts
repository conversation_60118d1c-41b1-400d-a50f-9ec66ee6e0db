import axios from "@/interfaces/axios-instance"

// 体检档案列表
export const getPhysicalExaminationFileListApi = (params: IPaginationRequestParams) => {
  return axios({
    method: "get",
    url: "/api/checkup/documents/paging",
    params
  })
}

// 体检单文件
export const getPhysicalExaminationFileDetailApi = (checkupId: string, fileWsid: string) => {
  return axios({
    method: "get",
    url: `/api/checkup/documents/${checkupId}/file/${fileWsid}`
  })
}

// 体检单历史文件
export const getPhysicalExaminationFileHistoryApi = (checkupId: string) => {
  return axios({
    method: "get",
    url: `/api/checkup/documents/${checkupId}/his`
  })
}
