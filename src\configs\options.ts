/* ======================== 公共常用选项 ======================== */

import { QcStatusEnum } from "./enums"
import type { MedicalRecordStatus, CatalogOperationType } from "@/types"
import cancelSvg from "@/assets/svg/other/cancel.svg"
import returnSvg from "@/assets/svg/other/return.svg"
import saveSvg from "@/assets/svg/other/save.svg"
import syncSvg from "@/assets/svg/other/sync.svg"
import viewSvg from "@/assets/svg/other/view.svg"

// 启用&禁用选项
export const rowStatusOptions = [
  { label: "全部", value: "" },
  { label: "启用", value: "ENABLE" },
  { label: "禁用", value: "DISABLE" }
]

// 是&否选项
export const booleanOptions = [
  { label: "是", value: true },
  { label: "否", value: false }
]

// 保密级别选项
export const secrecyLevelOptions = [
  { label: "不保密", value: "NO_SECRECY" },
  { label: "一级保密", value: "SECRECY_ONE" },
  { label: "二级保密", value: "SECRECY_TWO" },
  { label: "三级保密", value: "SECRECY_THREE" },
  { label: "四级保密", value: "SECRECY_FOUR" },
  { label: "五级保密", value: "SECRECY_FIVE" },
  { label: "六级保密", value: "SECRECY_SIX" }
]

// 审批状态选项
export const approvalStatusOptions = [
  { value: "", label: "全部", code: "", color: "success" },
  { value: "APPLICATION", label: "待审批", code: "0", color: "wait" },
  { value: "AUDIT_PASS", label: "审批通过", code: "1", color: "success" },
  { value: "TIME_OUT_AUDIT", label: "审批超时", code: "4", color: "danger" },
  { value: "AUDIT_REFUSE", label: "申请驳回", code: "2", color: "danger" },
  { value: "OVER_DUE", label: "已过期", code: "3", color: "danger" }
]

// 时间范围选项
export const shortcuts = [
  {
    text: "过去一周",
    value: () => {
      const end = new Date(new Date().setHours(23, 59, 59, 999))
      const start = new Date(new Date().setHours(0, 0, 0, 0))
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: "过去一个月",
    value: () => {
      const end = new Date(new Date().setHours(23, 59, 59, 999))
      const start = new Date(new Date().setHours(0, 0, 0, 0))
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: "过去三个月",
    value: () => {
      const end = new Date(new Date().setHours(23, 59, 59, 999))
      const start = new Date(new Date().setHours(0, 0, 0, 0))
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  }
]

interface MedicalRecordStatusOption {
  label: string
  value: MedicalRecordStatus
  tagType: "success" | "info" | "warning" | "danger" | ""
  color?: string
}

// 病案状态选项
export const medicalRecordStatusOptions: Array<MedicalRecordStatusOption> = [
  // { label: "待提交", value: "COLLECTING", tagType: "warning", color: "#4E74FF" },
  { label: "上级医师审核", value: "WAIT_SENIOR_REVIEW_QC", tagType: "warning", color: "#9F5AF8" },
  { label: "质控护士签收", value: "WAIT_NURSE_REVIEW_QC", tagType: "warning", color: "#FD714A" },
  { label: "待编目", value: "WAIT_CATALOG", tagType: "warning", color: "#7464FF" },
  { label: "返修", value: "REPAIR", tagType: "danger", color: "#FD714A" },
  { label: "待终末质控", value: "WAIT_FINAL_QC", tagType: "warning", color: "#00996B" },
  { label: "待归档", value: "WAIT_ARCHIVED", tagType: "warning", color: "#F5AC00" },
  { label: "已归档", value: "YET_ARCHIVED", tagType: "success", color: "#3AA600" },
  { label: "已上架", value: "SHELVES_YET", tagType: "success", color: "#3AA600" },
  { label: "已召回", value: "RECALL", tagType: "success", color: "#3AA600" },
  { label: "未知", value: "UNKNOWN", tagType: "info", color: "#7C889C" }
]
// 病案提交状态选项
export const medicalRecordSubmitStatusOptions: Array<MedicalRecordStatusOption> = [
  { label: "待提交", value: "NOT_SUBMIT", tagType: "warning", color: "#4E74FF" },
  { label: "已提交", value: "SUBMIT_SUCCESS", tagType: "success", color: "#2BB353" },
  { label: "未知", value: "UNKNOWN", tagType: "info", color: "#7C889C" }
]

// 质控状态选项
export const qcStatusOptions = [
  { label: "质控通过", value: QcStatusEnum.PASS },
  { label: "质控不通过", value: QcStatusEnum.NOT_PASS },
  { label: "未质控", value: QcStatusEnum.WAIT },
  { label: "已删除", value: QcStatusEnum.DELETE }
]

// 时间动态筛选范围类型
//过去，现在，未来
export const dateRangeType = [
  {
    label: "过去",
    value: "PAST",
    key: "-"
  },
  {
    label: "未来",
    value: "FUTURE",
    key: "+"
  }
]

// 时间范围
// 天，周，月，季，年
export const dateRange = [
  {
    label: "天",
    value: "DAY",
    key: "d"
  },
  {
    label: "周",
    value: "WEEK",
    key: "w"
  },
  {
    label: "月",
    value: "MONTH",
    key: "m"
  },
  {
    label: "季",
    value: "QUARTER",
    key: "q"
  },
  {
    label: "年",
    value: "YEAR",
    key: "y"
  }
]

//民族选项
export const nationOptions = [
  { label: "汉族", value: "汉族" },
  { label: "壮族", value: "壮族" },
  { label: "满族", value: "满族" },
  { label: "回族", value: "回族" },
  { label: "苗族", value: "苗族" },
  { label: "维吾尔族", value: "维吾尔族" },
  { label: "土家族", value: "土家族" },
  { label: "彝族", value: "彝族" },
  { label: "蒙古族", value: "蒙古族" },
  { label: "藏族", value: "藏族" },
  { label: "布依族", value: "布依族" },
  { label: "侗族", value: "侗族" },
  { label: "瑶族", value: "瑶族" },
  { label: "朝鲜族", value: "朝鲜族" },
  { label: "白族", value: "白族" },
  { label: "哈尼族", value: "哈尼族" },
  { label: "哈萨克族", value: "哈萨克族" },
  { label: "黎族", value: "黎族" },
  { label: "傣族", value: "傣族" },
  { label: "畲族", value: "畲族" },
  { label: "傈僳族", value: "傈僳族" },
  { label: "仡佬族", value: "仡佬族" },
  { label: "东乡族", value: "东乡族" },
  { label: "高山族", value: "高山族" },
  { label: "拉祜族", value: "拉祜族" },
  { label: "水族", value: "水族" },
  { label: "佤族", value: "佤族" },
  { label: "纳西族", value: "纳西族" },
  { label: "羌族", value: "羌族" },
  { label: "土族", value: "土族" },
  { label: "仫佬族", value: "仫佬族" },
  { label: "锡伯族", value: "锡伯族" },
  { label: "柯尔克孜族", value: "柯尔克孜族" },
  { label: "达斡尔族", value: "达斡尔族" },
  { label: "景颇族", value: "景颇族" },
  { label: "毛南族", value: "毛南族" },
  { label: "撒拉族", value: "撒拉族" },
  { label: "布朗族", value: "布朗族" },
  { label: "塔吉克族", value: "塔吉克族" },
  { label: "阿昌族", value: "阿昌族" },
  { label: "普米族", value: "普米族" },
  { label: "鄂温克族", value: "鄂温克族" },
  { label: "怒族", value: "怒族" },
  { label: "京族", value: "京族" },
  { label: "基诺族", value: "基诺族" },
  { label: "德昂族", value: "德昂族" },
  { label: "保安族", value: "保安族" },
  { label: "俄罗斯族", value: "俄罗斯族" },
  { label: "裕固族", value: "裕固族" },
  { label: "乌孜别克族", value: "乌孜别克族" },
  { label: "门巴族", value: "门巴族" },
  { label: "鄂伦春族", value: "鄂伦春族" },
  { label: "独龙族", value: "独龙族" },
  { label: "塔塔尔族", value: "塔塔尔族" },
  { label: "赫哲族", value: "赫哲族" },
  { label: "珞巴族", value: "珞巴族" },
  { label: "其他", value: "其他" }
]

interface CatalogOperationOption {
  label: string
  value: CatalogOperationType
  tagType: "success" | "info" | "warning" | "danger" | "primary" | "" | "text"
  icon: string
  color?: string
}

// 编目操作按钮属性
export const catalogOperationOptions: Array<CatalogOperationOption> = [
  { label: "提交", icon: "", value: "confirm", tagType: "primary", color: "#3860F4" },
  { label: "校验", icon: "", value: "check", tagType: "primary", color: "#3860F4" },
  { label: "同步", icon: syncSvg, value: "sync", tagType: "text", color: "" },
  { label: "暂存", icon: saveSvg, value: "save", tagType: "text", color: "" },
  { label: "查看", icon: viewSvg, value: "view", tagType: "text", color: "" },
  { label: "退回", icon: returnSvg, value: "return", tagType: "text", color: "" },
  { label: "撤销", icon: cancelSvg, value: "cancel", tagType: "text", color: "" }
]
