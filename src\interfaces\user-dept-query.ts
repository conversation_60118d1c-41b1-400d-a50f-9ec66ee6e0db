import axios from "@/interfaces/axios-instance"
import type { UserItem } from "@/types"

/* ======================== 用户&科室公共查询接口 ======================== */

/**
 * @method GET
 * @desc   查询科室部门列表
 */
export function getHospitalDeptsApi(deptName = "") {
  return axios({
    method: "get",
    url: `/api/hospital/dept/tree`,
    params: {
      deptName
    }
  })
}

/**
 * @method GET
 * @type   选项列表获取
 * @desc   获取所有科室列表
 */
export function getAllDeptApi() {
  return axios
    .request<IResponseData>({
      method: "get",
      url: `/api/document/allDept`
    })
    .then(res => res.data?.data ?? [])
}

/**
 * @method GET
 * @desc   查询科室下的所有医护信息
 */
export function getUsersInDeptsApi(obj) {
  const { deptWsid } = obj
  return axios({
    method: "get",
    url: `/api/hospital/users/dept`,
    params: {
      deptWsid
    }
  })
}

/**
 * @method GET
 * @desc   根据科室id及关键词查询用户列表（不分页）
 */
export function getFuzzyUsersInDeptsApi(obj) {
  const { deptWsid, item } = obj
  return axios({
    method: "get",
    url: `/api/hospital/users/fuzzy-query`,
    params: {
      deptWsid,
      item
    }
  })
}

/**
 * @method GET
 * @desc   查询HQMS科室
 */
export function getHQMSDepartmentApi() {
  return axios({
    method: "get",
    url: `/api/catalog/choice/option/dept`
  }).then(res => res.data?.data ?? [])
}

/**
 * @method GET
 * @desc   角色管理-分页获取角色列表
 */
export function getRolesList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: "/api/system/roles/list",
    params
  })
}

/**
 * @method GET
 * @desc   角色管理-获取指定角色绑定的账户信息
 */
export function getUserInRolesApi(params: { roleWsid: string }) {
  const { roleWsid } = params
  return axios
    .request<IResponseData<UserItem[]>>({
      method: "get",
      url: `/api/system/roles/${roleWsid}/users`
    })
    .then(res => res.data?.data ?? [])
}

/**
 * @method GET
 * @desc   用户管理-用户列表
 */
export function getUserListApi(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/hospital/users/list`,
    params
  })
}
