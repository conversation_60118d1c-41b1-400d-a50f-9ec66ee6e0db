<template>
  <PageContainer separate>
    <template #search>
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
          @filter-prop-change="handleBaseFieldChange"
        />

        <DepartmentFormItem v-model:model-value="searchFormState.outHospitalDeptWsid" label="出院科室" />

        <DaterangeFormItem v-model:model-value="searchFormState.outHospitalDatetime" label="出院日期" />

        <el-form-item label="收藏标签">
          <el-select
            v-model="searchFormState.labelName"
            multiple
            filterable
            clearable
            collapse-tags
            collapse-tags-tooltip
          >
            <el-option v-for="option in collectTags" :key="option.value" :label="option.label" :value="option.value" />
          </el-select>
        </el-form-item>
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="commonTableRef"
        table-id="medicalRecordCollectionTable"
        :table-columns="medicalRecordCollectionColumns"
        :request-api="getCollectListApi"
        :request-params="searchParams"
      >
        <template #header>
          <div>
            <BatchOperationButton
              type="primary"
              :disabled="!selectedRows.length"
              :icon="Tickets"
              @click="handleBorrowMulti()"
            >
              批量借阅
            </BatchOperationButton>
            <BatchOperationButton
              type="warning"
              :disabled="!selectedRows.length"
              :icon="StarFilled"
              @click="handleCancelCollectMulti()"
            >
              批量取消收藏
            </BatchOperationButton>
          </div>
        </template>
        <template #labelName="{ row }">
          <span>{{ formatTag(row.labelName) }}</span>
        </template>
        <template #statusEnumName="{ row }">
          <MedicalRecordStatusTag :status="row.statusEnumName" />
        </template>
        <template #inHospitalDatetime="{ row }">
          <span>{{ formatDatetime(row.inHospitalDatetime) }}</span>
        </template>
        <template #outHospitalDatetime="{ row }">
          <span>{{ formatDatetime(row.outHospitalDatetime) }}</span>
        </template>
        <template #operation="{ row }">
          <TableButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Borrow)"
            tooltip="该病案无法借阅，或已经借阅过"
            :disabled="row.borrowStatus !== 'NO_BORROW' || row.status !== 7"
            @click="handleBorrowSingle(row)"
          >
            借阅
          </TableButton>
          <TableButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Favorite)"
            @click="handleCancelCollectSingle(row)"
          >
            取消收藏
          </TableButton>
        </template>
      </CommonTable>
    </template>

    <BorrowDialog
      ref="borrowDialogRef"
      :table-ref="commonTableRef"
      :borrow-tree="borrowState.borrowTree"
      :packet-list="borrowState.packetList"
    />
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, computed } from "vue"
import { StarFilled, Tickets } from "@element-plus/icons-vue"
import {
  CommonTable,
  TableButton,
  PatientFilterFormItem,
  DepartmentFormItem,
  DaterangeFormItem,
  BatchOperationButton,
  MedicalRecordStatusTag,
  PageContainer,
  SearchContainer
} from "@/base-components"
import { getCollectionTagListApi, getCollectListApi, cancelCollectApi } from "../interface"
import BorrowDialog from "./components/BorrowDialog.vue"
import { medicalRecordCollectionColumns } from "./config"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { getBorrowApplyInfoApi } from "@/interfaces"
import { useUserStore } from "@/stores"
import { SystemPrompt, Message, formatDatetime, formatRecordTree, toastError } from "@/utils"

const menuId = "/medical-record/collection"

const { hasOperationPermission } = useUserStore()

/* ======================== 页面首次加载时获取选项 ======================== */

const collectTags = ref<Array<any>>([])

onMounted(() => {
  // 获取收藏标签列表
  getCollectionTagListApi().then(res => {
    collectTags.value = res?.data?.data?.map(item => {
      return {
        label: item,
        value: item
      }
    })
  })
})

/* ======================== 搜索相关数据及方法 ======================== */
type BaseField = "patientName" | "inpNo" | "mrNo" | "idCard"

const searchFormState = reactive({
  patientFilterProp: "patientName",
  patientFilterValue: "",
  outHospitalDeptWsid: "",
  outHospitalDatetime: "",
  labelName: []
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState, undefined, { labelName: [] })

const handleBaseFieldChange = (selectedField: BaseField) => {
  searchFormState.patientFilterProp = selectedField
  searchFormState.patientFilterValue = ""
}

/* ======================== 表格操作 ======================== */

const commonTableRef = ref<InstanceType<typeof CommonTable>>()

// 当前选择表格数据
const selectedRows = computed(() => commonTableRef.value?.tableState?.selectedRows ?? [])

/*------------------- 借阅 ---------------------*/
interface BorrowState {
  dialogVisible: boolean
  borrowTree: {
    name: string
    documents: any
    type: string
    filePage: any
  }[] // 树节点
  packetList: string[] // 文件袋信息列表
}

const borrowDialogRef = ref()
const borrowState = reactive<BorrowState>({
  dialogVisible: false,
  borrowTree: [],
  packetList: []
})

// 借阅单条数据
const handleBorrowSingle = (row: any) => {
  if (row.borrowStatus === "CAN_NOT_BORROW") {
    Message.error("存在未归档的病案，不能借阅")
    return
  }
  return getBorrowInfo([row.wsid], "single")
}

// 借阅多条数据
const handleBorrowMulti = () => {
  const selectedRecordList = selectedRows.value
  for (let record of selectedRecordList) {
    if (record.borrowStatus === "CAN_NOT_BORROW") {
      Message.error("存在未归档的病案，不能借阅")
      return
    }
  }
  const recordWsidList = selectedRecordList.map(record => record.wsid)
  return getBorrowInfo(recordWsidList, "multi")
}

/**
 * 获取借阅信息
 * @param recordWsidList 所选借阅文档的wsid列表
 * @param borrowType 借阅类型 single - 单条； multi - 多条
 */
const getBorrowInfo = (recordWsidList: string[], borrowType: "single" | "multi") => {
  getBorrowApplyInfoApi(recordWsidList).then(res => {
    if (res.data.code !== "100100000") return
    const borrowInfo = res.data.data
    const borrowTree = borrowInfo.treeInfo // 文档树 []
    const packetList = borrowInfo.bagsInfo // 文件袋信息 []
    const alreadyBorrowed: boolean = borrowInfo.hasBorrowFile || false // 是否含有已借阅的病案袋 boolean

    // 无法借阅
    if (packetList.length === 0) {
      if (borrowType === "single") {
        SystemPrompt("该病案已申请借阅，或者还在借阅有效期内")
      } else {
        SystemPrompt("当前借阅所有病案已申请借阅或者还在借阅有效期内")
      }
      // 可以借阅 但 已经借阅了一部分
    } else if (alreadyBorrowed) {
      SystemPrompt(
        "您当前借阅包含已申请借阅或者在借阅有效期内的病案，本次借阅将自动取消已申请借阅或者在借阅有效期内的病案"
      ).then(() => setBorrowInfo(borrowTree, packetList))
      // 可以全部借阅
    } else {
      setBorrowInfo(borrowTree, packetList)
    }
  })
}

// 设置可借阅信息
const setBorrowInfo = (borrowTree, packetList) => {
  borrowDialogRef.value.open()
  borrowState.borrowTree = formatRecordTree(borrowTree)
  borrowState.packetList = packetList.map(packet => {
    return `${packet.mrNo}(${packet.inpNo})，${packet.patientName}`
  })
}

/*===================== 收藏 ======================*/

// 批量取消收藏
const handleCancelCollectMulti = () => {
  SystemPrompt("是否取消对选中数据的收藏？").then(() => {
    const wsids: string[] = selectedRows.value.map(item => {
      return item.wsid
    })
    cancelCollectApi(wsids)
      .then(() => {
        commonTableRef.value?.refreshTableData()
        Message.success("操作成功")
      })
      .catch(error => {
        toastError(error)
      })
  })
}

// 取消收藏
const handleCancelCollectSingle = row => {
  SystemPrompt("是否取消收藏？").then(() => {
    const wsids: string[] = [row?.documentBagWsid]
    cancelCollectApi(wsids)
      .then(() => {
        commonTableRef.value?.refreshTableData()
        Message.success("操作成功")
      })
      .catch(error => {
        toastError(error)
      })
  })
}

const formatTag = tag => {
  tag = tag.replace(/\[/g, "")
  tag = tag.replace(/\]/g, "、")
  tag = tag.substr(0, tag.length - 1)
  return tag
}
</script>
