<!-- 创建/编辑库房弹窗 -->
<template>
  <DialogContainer v-model:visible="visible" :title="dialogTitle" :width="600">
    <el-form ref="formRef" :model="storeroomState" :rules="formRules" :label-width="90" label-suffix=":">
      <el-form-item :label="businessType === 'storeroom' ? '库房名称' : '库位名称'" prop="name">
        <el-input
          v-model.trim="storeroomState.name"
          maxlength="20"
          show-word-limit
          :placeholder="businessType === 'storeroom' ? '请输入库房名称' : '请输入库位名称'"
        />
      </el-form-item>

      <el-form-item :label="businessType === 'storeroom' ? '库房编码' : '库位编码'" prop="code">
        <el-input
          v-model.trim="storeroomState.code"
          maxlength="20"
          show-word-limit
          :placeholder="businessType === 'storeroom' ? '请输入库房编码' : '请输入库位编码'"
          :disabled="isContainsArchive"
        />
      </el-form-item>
      <el-form-item v-if="businessType === 'storeroom'" label="地址" prop="address">
        <el-input
          v-model="storeroomState.address"
          :placeholder="businessType === 'storeroom' ? '请输入库房地址' : '请输入库位地址'"
          type="textarea"
          :maxlength="50"
          :show-word-limit="true"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button :disabled="confirmLoading" @click="() => (visible = false)">取消</el-button>
      <el-button type="primary" :loading="confirmLoading" @click="handleSave">确认</el-button>
    </template>
  </DialogContainer>
</template>

<script setup lang="ts">
import { reactive, ref, computed } from "vue"
import { noop } from "lodash"
import { DialogContainer } from "@/base-components"
import { Message, toastError } from "@/utils"
import {
  addStoreroomApi,
  checkStorageDeleteApi,
  editStoreroomApi,
  editStoreLocationApi,
  addStoreLocationApi
} from "../../interface"

const props = defineProps({
  confirmCallback: { type: Function, default: noop }
})

const visible = ref(false)
const loading = ref(false)
const type = ref<"create" | "edit">("create")
const businessType = ref<"storeroom" | "storeLocation">("storeroom")
const formRef = ref()
const confirmLoading = ref(false)
const isContainsArchive = ref(false) //是否存放了档案
const storeroomState = reactive({
  wsid: "",
  name: "",
  code: "",
  address: "",
  roomWsid: ""
})

// 弹窗名称
const dialogTitle = computed(() => {
  if (businessType.value === "storeroom") {
    return type.value === "create" ? "新建库房" : "编辑库房"
  }
  return type.value === "create" ? "新建库位" : "编辑库位"
})

// 表单校验
const formRules = computed(() => {
  if (businessType.value === "storeroom") {
    return {
      name: [
        { required: true, message: "请输入库房名称", trigger: "blur" },
        { max: 20, message: "库房名称不可超过20字", trigger: "blur" }
      ],
      code: [
        { required: true, message: "请输入库房编码", trigger: "blur" },
        { max: 20, message: "库房编码不可超过20字", trigger: "blur" }
      ],
      address: [{ maxLength: 50, message: "地址不可超过50字", trigger: "blur" }]
    }
  }
  return {
    name: [
      { required: true, message: "请输入库位名称", trigger: "blur" },
      { max: 20, message: "库位名称不可超过20字", trigger: "blur" }
    ],
    code: [
      { required: true, message: "请输入库位编码", trigger: "blur" },
      { max: 20, message: "库位编码不可超过20字", trigger: "blur" }
    ]
  }
})

// 打开弹窗
async function openDialog(dialogType, modeType, row) {
  if (dialogType === "create") {
    isContainsArchive.value = false
    for (let key in storeroomState) {
      if (key === "roomWsid") {
        storeroomState[key] = row && row[key] !== undefined ? row[key] : ""
        continue
      }
      storeroomState[key] = ""
    }
  } else {
    for (let key in storeroomState) {
      storeroomState[key] = row && row[key] !== undefined ? row[key] : ""
    }
    await validateStorage()
  }
  type.value = dialogType
  businessType.value = modeType
  visible.value = true
}

//编辑库房/库位时，判断是否存放了档案
const validateStorage = async () => {
  await checkStorageDeleteApi(storeroomState.wsid)
    .then((res: any) => {
      isContainsArchive.value = !res.data.data
    })
    .catch(err => {
      isContainsArchive.value = true
    })
}
// 新建/编辑库房/库位
function handleSave() {
  formRef.value?.validate(valid => {
    if (!valid) return
    let requestApi
    let params = {}
    if (businessType.value === "storeroom") {
      requestApi = type.value === "create" ? addStoreroomApi : editStoreroomApi
      params = {
        documentStorageRoomName: storeroomState.name,
        documentStorageRoomAddress: storeroomState.address,
        ...(type.value === "create" && { documentStorageRoomNumber: storeroomState.code }),
        ...(type.value === "edit" && { documentStorageRoomWsid: storeroomState.wsid })
      }
    } else {
      requestApi = type.value === "create" ? addStoreLocationApi : editStoreLocationApi
      params = {
        documentStorageRackName: storeroomState.name,
        documentStorageRoomWsid: storeroomState.roomWsid,
        ...(type.value === "create" && { documentStorageRackNumber: storeroomState.code }),
        ...(type.value === "edit" && { documentStorageRackWsid: storeroomState.wsid })
      }
    }
    confirmLoading.value = true
    requestApi(params)
      .then(res => {
        Message.success(type.value === "create" ? "新建成功" : "编辑成功")
        visible.value = false
        if (props.confirmCallback) props.confirmCallback()
      })
      .catch(err => {
        toastError(err, type.value === "create" ? "新建失败" : "编辑失败")
      })
      .finally(() => {
        confirmLoading.value = false
      })
  })
}
defineExpose({ openDialog, businessType })
</script>

<style lang="less" scoped>
.storeroom-img-container {
  width: 200px;
  height: 200px;
  border: 1px dashed #dcdfe6;
  border-radius: 5px;
  position: relative;
  .storeroom-img {
    object-fit: contain;
  }
  .ri-close-circle-line {
    position: absolute;
    font-size: 20px;
    top: -12px;
    right: -10px;
    color: #f56c6c;
  }
}

.upload-icon {
  width: 200px;
  height: 200px;
  border: 1px dashed #dcdfe6;
  border-radius: 5px;
}
</style>
