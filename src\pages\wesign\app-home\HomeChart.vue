<template>
  <div ref="chartDom" class="echart"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, markRaw } from "vue"
import * as echarts from "echarts"

const chartDom = ref()
const myChart = ref()
const props = defineProps({
  options: {
    type: Object,
    required: true
  },
  chartWidth: {
    type: Number
  }
})
const resizeHandler = () => {
  myChart.value.resize()
}
onMounted(() => {
  pieInit()
})

const pieInit = () => {
  myChart.value = markRaw(echarts.init(chartDom.value, null, { renderer: "svg" }))
  myChart.value.setOption(props.options, true)
  window.addEventListener("resize", resizeHandler)
}

watch(
  () => props.options,
  newOptions => {
    myChart.value.setOption(newOptions)
  },
  {
    deep: true
  }
)
</script>

<style lang="less" scoped>
.echart {
  min-height: 300px;
  width: 100%;
}
</style>
