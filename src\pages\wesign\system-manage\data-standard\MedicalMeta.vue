<template>
  <div class="container">
    <div v-loading="standardLoading" class="left">
      <CommonTree ref="standardTreeRef" node-key="id" :data="standardList" @click-node="selectStandard">
        <template #title="{ node, data }">
          <div class="content">
            <span style="margin-right: 4px">{{ data.fileName }}</span>

            <el-popover
              popper-class="page-side-popover"
              :popper-style="{ minWidth: '75px', padding: '5px' }"
              placement="right"
              :width="85"
              trigger="hover"
            >
              <template #reference>
                <i v-if="data.id" class="ri-more-fill" style="cursor: pointer"></i>
              </template>
              <div class="btns">
                <div v-if="data.parentId !== 0" class="item-more-btn" @click="handleStandard('edit', data)">编辑</div>
                <div v-if="data.parentId === 0" class="item-more-btn" @click="handleStandard('add', data)">
                  新增分类
                </div>
                <div v-if="data.parentId !== 0" class="item-more-btn" @click="handleDeleteStandard(data)">删除</div>
              </div>
            </el-popover>
          </div>
        </template>
      </CommonTree>

      <!-- 新增、编辑分类 -->
      <DialogForm
        v-model:visible="standardVisible"
        :title="handleStandardType === 'edit' ? '编辑' : '新增'"
        :confirm-callback="standardHandleConfirm"
        :form-config="standardFormConfig"
        :form-state="standardDialogForm"
        :disabled-fields="isEdit ? ['collectItem', 'collectFieldName'] : []"
      />
    </div>

    <div class="right">
      <PageContainer separate>
        <template #search>
          <SearchContainer @query-btn-click="toQuery" @reset-btn-click="toReset">
            <CommonInputFormItem v-model="searchFormState.collectFieldName" label="字段标识" />
            <CommonInputFormItem v-model="searchFormState.collectItem" label="字段名称" />
            <CommonSelectFormItem
              v-model="searchFormState.widgetType"
              label="控件类型"
              :options="widgetTypeOptions"
            ></CommonSelectFormItem>
          </SearchContainer>
        </template>

        <template #table>
          <CommonTable
            ref="commonTableRef"
            :table-columns="MedicalInsuranceMetaColumns"
            :request-api="getTableData"
            :request-params="searchParams"
            :data-callback="dataCallback"
          >
            <template #header>
              <AddButton @click="handleAdd">新增字段</AddButton>
              <el-button @click="handleBatchSet('parentId')">批量设置字段分类</el-button>
              <el-button @click="handleBatchSet('widgetType')">批量设置控件类型</el-button>
            </template>
            <template #operation="{ row }">
              <TableButton @click="handleEdit(row)">编辑</TableButton>
              <TableButton :disabled="false" @click="handleDelete(row)">删除</TableButton>
            </template>
            <template #valueRangeKey="{ row }">
              <TableButton :disabled="!row.valueRangeKey" tooltip="当前字段无取值范围" @click="showMetaRange(row)">
                {{ row.valueRangeKey || "--" }}
              </TableButton>
            </template>
          </CommonTable>
        </template>

        <!-- 新增、编辑字段 -->
        <DialogContainer
          v-model:visible="medicalMetaDialogVisible"
          :title="isEdit ? '修改字段' : '新增字段'"
          :width="550"
          :confirm-callback="handleConfirm"
          :confirm-loading="confirmLoading"
        >
          <el-form
            ref="ruleFormRef"
            :model="medicalMetaForm"
            label-position="right"
            :label-width="120"
            label-suffix="："
            :rules="formRules"
          >
            <el-form-item label="字段名称" prop="title">
              <el-input v-model.trim="medicalMetaForm.title" placeholder="请输入字段名称" />
            </el-form-item>
            <el-form-item label="字段标识" prop="name">
              <el-input v-model.trim="medicalMetaForm.name" placeholder="请输入字段标识" :disabled="isEdit" />
            </el-form-item>
            <el-form-item label="数据类型" prop="valueType">
              <el-select v-model="medicalMetaForm.valueType" placeholder="请选择数据类型">
                <el-option
                  v-for="option in valueTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="字段分类" prop="parentId">
              <el-tree-select
                v-model="medicalMetaForm.parentId"
                class="tree-select"
                placeholder="请选择"
                fit-input-width
                default-expand-all
                check-strictly
                node-key="id"
                filterable
                :data="parentIdList"
                @node-click="handleSelect"
              ></el-tree-select>
            </el-form-item>
            <el-form-item label="控件类型" prop="widgetType">
              <el-select v-model="medicalMetaForm.widgetType" placeholder="请选择控件类型">
                <el-option
                  v-for="option in widgetTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              v-if="
                medicalMetaForm.widgetType === 'SINGLE_CHOICES' || medicalMetaForm.widgetType === 'MULTIPLE_CHOICES'
              "
              label="取值来源"
              prop="collectSourceType"
            >
              <el-select
                v-model="medicalMetaForm.collectSourceType"
                placeholder="请选择取值来源"
                @change="selectCollectSourceType"
              >
                <el-option
                  v-for="option in collectSourceTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <template
              v-if="
                medicalMetaForm.widgetType === 'SINGLE_CHOICES' || medicalMetaForm.widgetType === 'MULTIPLE_CHOICES'
              "
            >
              <el-form-item
                v-if="medicalMetaForm.collectSourceType === 'INTERFACE'"
                label="接口地址"
                prop="collectValueSource"
              >
                <!-- <el-input v-model.trim="medicalMetaForm.collectValueSource" placeholder="请输入接口地址" /> -->

                <el-select v-model="medicalMetaForm.collectValueSource" filterable placeholder="请选择接口地址">
                  <el-option
                    v-for="option in apiList"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                v-if="medicalMetaForm.collectSourceType === 'METADATA'"
                label="取值范围"
                prop="valueRangeKey"
              >
                <el-select v-model="medicalMetaForm.valueRangeKey" clearable filterable placeholder="请选择取值范围">
                  <el-option
                    v-for="option in fieldRangeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </template>

            <!-- <el-form-item label="长度" prop="length">
              <el-input-number v-model="medicalMetaForm['length']" placeholder="请输入长度" />
            </el-form-item> -->
            <el-form-item
              v-if="activeStandard.catalogType === 'REPORT' || activeStandard.fileType === 'REPORT'"
              label="字段对照"
              prop="refFirstPageName"
            >
              <el-tree-select
                v-model="medicalMetaForm.refFirstPageName"
                class="tree-select"
                placeholder="请选择"
                fit-input-width
                default-expand-all
                filterable
                node-key="code"
                :check-strictly="false"
                :data="refFirstPageNameOption"
              ></el-tree-select>
            </el-form-item>

            <el-form-item label="备注" prop="remark">
              <el-input v-model.trim="medicalMetaForm.remark" placeholder="请输入备注" />
            </el-form-item>
          </el-form>
        </DialogContainer>

        <!-- 取值范围展示弹窗 -->
        <DialogContainer
          v-model:visible="rangeDialogVisible"
          :title="`${activeRow.valueRangeKey}取值范围`"
          :width="550"
          :no-footer="true"
        >
          <CommonTable
            :table-columns="fieldRangeColumns"
            :request-api="searchAllMetaCodeTableFieldApi"
            :request-params="{
              filters: `code=${activeRow.valueRangeKey}`
            }"
            :border="false"
            :pagination="false"
          />
        </DialogContainer>

        <!-- 批量设置文书分类/控件类型 -->
        <DialogContainer
          v-model:visible="batchSetDialogVisible"
          :title="batchState.type === 'parentId' ? '字段分类' : '设置控件类型'"
          :width="500"
          :confirm-callback="confirmSet"
          :confirm-loading="batchState.loading"
        >
          <el-tree-select
            v-if="batchState.type === 'parentId'"
            v-model="batchState.parentId"
            class="tree-select"
            placeholder="请选择字段分类"
            fit-input-width
            default-expand-all
            check-strictly
            node-key="id"
            filterable
            :data="parentIdList"
          ></el-tree-select>

          <el-select v-else v-model="batchState.widgetType" placeholder="请选择控件类型">
            <el-option
              v-for="option in widgetTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </DialogContainer>
      </PageContainer>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from "vue"
import { cloneDeep } from "lodash-es"
import {
  PageContainer,
  SearchContainer,
  CommonTable,
  DialogContainer,
  AddButton,
  TableButton,
  CommonTree,
  CommonInputFormItem,
  CommonSelectFormItem
} from "@/base-components"
import { DialogForm } from "@/page-components"
import { useTableSearch } from "@/hooks"
import {
  searchAllMetaCodeTableFieldApi,
  getHomeMetaListApi,
  getCatalogStandardList,
  addCatalogStandardApi,
  editCatalogStandardApi,
  deleteCatalogStandardApi,
  editStandardApi,
  addStandardApi,
  searchStandardRangeClassifyApi,
  queryDictionaryDetailByGroup
} from "@/interfaces"
import { SystemPrompt, Message, toastError, formatTreeInfo } from "@/utils"
import {
  fieldRangeColumns,
  medicalMetaFormConfig,
  MedicalInsuranceMetaColumns,
  transformValueType,
  standardFormConfig,
  widgetTypeOptions,
  transWidgetType,
  formRules,
  valueTypeOptions,
  collectSourceTypeOptions
} from "./config"
import {
  getStandardsFieldApi,
  deleteStandardsFieldApi,
  changeStandardsFieldStatusApi,
  addHomeMetaField,
  editHomeMetaField,
  deleteHomeMetaField,
  batchModifyStandardApi
} from "./interface"
import type { BaseOptionItem } from "@/types"
import type { FormInstance } from "element-plus"

/* ======================== 左侧字段分类列表 ======================== */

const standardList = ref<Array<Record<string, any>>>([])

const standardVisible = ref(false)
const standardLoading = ref(false)
const handleStandardType = ref("")
const activeStandard = ref<Record<string, any>>({
  scenarioType: "HOME_PAGE"
})
const standardDialogForm = reactive({
  fileName: ""
})

// 点击左侧分类
const selectStandard = node => {
  console.log(`output->node`, node)
  activeStandard.value = node
}

// 获取分类列表
const getStandardList = async () => {
  standardLoading.value = true
  try {
    standardList.value = (await getCatalogStandardList())?.data?.data
    standardLoading.value = false
  } catch (err: any) {
    toastError(err)
    standardLoading.value = false
  }
}

onMounted(async () => {
  await getStandardList()
  selectStandard(standardList.value?.[0]?.children[0])
})

const handleStandard = (type: string, data: Record<string, any>) => {
  handleStandardType.value = type
  activeStandard.value = data
  standardVisible.value = true
  standardDialogForm.fileName = type === "edit" ? data.fileName : ""
}

/**
 * 新增/编辑分类
 * @returns {Promise<void>}
 */
const standardHandleConfirm = async () => {
  try {
    if (handleStandardType.value === "add") {
      await addCatalogStandardApi({
        fileName: standardDialogForm.fileName,
        fileType: activeStandard.value.fileType,
        catalogType: "CATALOG",
        parentId: activeStandard.value.id,
        scenarioType: activeStandard.value.scenarioType
      })
    } else if (handleStandardType.value === "edit") {
      await editCatalogStandardApi({
        id: activeStandard.value.id,
        fileName: standardDialogForm.fileName,
        fileType: activeStandard.value.fileType,
        parentId: activeStandard.value.parentId,
        scenarioType: activeStandard.value.scenarioType
      })
    }
    Message.success("操作成功！")
    standardVisible.value = false
    await getStandardList()
  } catch (err: any) {
    toastError(err)
  }
}

/**
 * 删除分类
 * @param {Record<string, any>} data 要删除的分类信息
 */
const handleDeleteStandard = (data: Record<string, any>) => {
  activeStandard.value = data

  SystemPrompt(`您正在删除字段名为“${data.fileName}”的分类，确定删除？`).then(() => {
    deleteCatalogStandardApi({
      fileType: data.fileType,
      id: data.id
    })
      .then(() => {
        Message.success("操作成功！")
        getStandardList()
      })
      .catch(err => toastError(err))
  })
}

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  collectItem: "",
  collectFieldName: "",
  widgetType: "",
  parentId: ""
})

const queryParams = reactive({
  scenarioType: "",
  likeTitle: "",
  likeFieldName: "",
  widgetType: ""
})

// 获取右侧table数据
const getTableData = computed(() => {
  if (activeStandard.value?.fileType === "SYSTEM") {
    return getHomeMetaListApi
  } else {
    return getStandardsFieldApi
  }
})

// 监听activeStandard
watch(
  () => activeStandard.value,
  val => {
    //首页分类以外的查询条件
    // if (!activeStandard.value?.id) return
    toQuery()
  },
  {
    deep: true
  }
)
const toQuery = () => {
  queryParams.scenarioType = activeStandard.value.fileType === "SYSTEM" ? "" : activeStandard.value.scenarioType
  queryParams.likeTitle = activeStandard.value.fileType === "SYSTEM" ? "" : searchFormState.collectItem
  queryParams.likeFieldName = activeStandard.value.fileType === "SYSTEM" ? "" : searchFormState.collectFieldName
  searchFormState.parentId = activeStandard.value.id
  handleQuery()
}

// 重置
const toReset = () => {
  for (const key in searchFormState) {
    searchFormState[key] = ""
  }
  searchParams.filters = activeStandard.value.id ? `parentId=${activeStandard.value.id}` : ""
  for (const key in searchParams.queryParams) {
    searchParams.queryParams[key] = ""
  }
  searchParams.queryParams.scenarioType = activeStandard.value.scenarioType
}

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState, "", {}, queryParams, {})

/* ======================== 表格相关配置状态及方法 ======================== */

const commonTableRef = ref<InstanceType<typeof CommonTable>>()

// 被选中表格项
const selectedRows = computed(() => commonTableRef.value?.tableState?.selectedRows ?? [])

// 当前点击的表格行
const activeRow = ref<Record<string, any>>({})

// 兼容首页分类字段和其他分类字段
function dataCallback(data: Array<any>) {
  return data.map(item => ({
    ...item,
    requiredDesc: item.required ? "是" : "否",
    valueTypeDesc: transformValueType(item.collectValueType || item.valueType),
    collectItem: item.collectItem || item.title,
    collectFieldName: item.collectFieldName || item.name,
    widgetTypeDesc: transWidgetType(item.widgetType),
    collectLength: item.collectLength || item.length
  }))
}

// 新增字段
const handleAdd = () => {
  isEdit.value = false
  getOption("")
  for (let key in medicalMetaForm) {
    medicalMetaForm[key] = initFormData[key]
  }
  if (activeStandard.value.id) {
    medicalMetaForm.parentId = activeStandard.value.id
  } else {
    medicalMetaForm.parentId = activeStandard.value?.children[0]?.id
  }
  medicalMetaDialogVisible.value = true
}

// 编辑字段
const handleEdit = (row: Record<string, any>) => {
  isEdit.value = true
  getOption("")

  // 数据上报
  if (activeStandard.value?.fileType === "REPORT") {
    for (let key in medicalMetaForm) {
      medicalMetaForm[key] = row[key]
    }
    medicalMetaForm.collectSourceType = collectSourceTypeOptions.find(item => item.key === row.collectSourceType)?.value
    medicalMetaForm.widgetType = widgetTypeOptions.find(item => item.key === row.widgetType)?.value
  } else {
    // 首页字段
    for (let key in medicalMetaForm) {
      medicalMetaForm[key] = row[key]
    }
    medicalMetaForm.name = row.collectFieldName
    medicalMetaForm.title = row.collectItem
    medicalMetaForm["length"] = row.collectLength
    medicalMetaForm.valueType = row.collectValueType
  }
  medicalMetaDialogVisible.value = true
}

// 删除字段
function handleDelete(row) {
  SystemPrompt(`您正在删除字段名为“${row.title || row.collectItem}”的数据，确定删除？`).then(() => {
    const handleDeleteApi = activeStandard.value?.fileType === "SYSTEM" ? deleteHomeMetaField : deleteStandardsFieldApi
    const params =
      activeStandard.value?.fileType === "SYSTEM"
        ? row.id
        : { scenarioType: activeStandard.value?.scenarioType, name: row.name }
    handleDeleteApi(params)
      .then(() => {
        Message.success("操作成功！")
        commonTableRef.value?.refreshTableData()
      })
      .catch(err => toastError(err))
  })
}

// 启用&禁用字段编目
function handleSwitchStatus(row) {
  const nextOperation = row.status === "ENABLE" ? "禁用" : "启用"
  const nextStatus = row.status === "ENABLE" ? "DISABLE" : "ENABLE"
  SystemPrompt(`您正在${nextOperation}字段名为“${row.name}”的数据，确定${nextOperation}？`).then(() => {
    changeStandardsFieldStatusApi({
      scenarioType: "MEDICAL_INSURANCE",
      names: [row.name],
      status: nextStatus
    })
      .then(() => {
        Message.success("操作成功！")
        commonTableRef.value?.refreshTableData()
      })
      .catch(err => toastError(err))
  })
}

/* ======================== 字段弹窗相关状态及方法 ======================== */

const medicalMetaDialogVisible = ref(false)
const isEdit = ref(false)
const refFirstPageNameOption = ref<Array<Record<string, any>>>([])
const parentIdList = ref<Array<Record<string, any>>>([])
onMounted(() => {
  getOption("")
})
const getOption = async (item: string) => {
  refFirstPageNameOption.value = formatTreeInfo(
    (
      await getCatalogStandardList({
        catalogType: "FIELD",
        catalogFileType: "SYSTEM"
      })
    )?.data?.data
  )
  parentIdList.value = formatTreeInfo(
    (
      await getCatalogStandardList({
        catalogType: "CATALOG",
        catalogFileType: activeStandard.value?.fileType,
        filters:
          isEdit.value && activeStandard.value?.scenarioType
            ? `scenario_type=${activeStandard.value?.scenarioType}`
            : ""
      })
    )?.data?.data
  )
  console.log(`output->parentIdList.value`, parentIdList.value)
}

const initFormData = {
  scenarioType: "",
  name: "",
  title: "",
  valueType: "",
  length: 0,
  remark: "",
  parentId: "",
  catalogType: "",
  refFirstPageName: "",
  valueRangeKey: "",
  collectSourceType: "",
  collectValueSource: "",
  widgetType: ""
}

const medicalMetaForm = reactive(cloneDeep(initFormData))

// 取值范围
const fieldRangeOptions = ref<Array<BaseOptionItem>>([])

// 初始化字段取值范围选项
onMounted(() => {
  searchStandardRangeClassifyApi().then(res => {
    const resData = res.data?.data ?? []
    fieldRangeOptions.value = resData.map(item => ({
      label: `${item.name}(${item.code})`,
      value: item.code
    }))
  })
})

// 接口地址列表

const apiList = ref<Array<Record<string, any>>>([])

onMounted(async () => {
  apiList.value = (await queryDictionaryDetailByGroup({ groupKey: "VALUE_SOURCE" }))?.map(item => {
    return {
      label: item.value,
      value: item.value
    }
  })
})

const ruleFormRef = ref<FormInstance>()
const confirmLoading = ref(false)
// 确认新增&编辑
function handleConfirm() {
  ruleFormRef.value?.validate(async valid => {
    if (!valid) return
    medicalMetaForm.scenarioType = activeStandard.value?.scenarioType
    medicalMetaForm.catalogType = "FIELD"
    confirmLoading.value = true
    if (isEdit.value) {
      const handleEditApi = activeStandard.value?.fileType === "SYSTEM" ? editHomeMetaField : editStandardApi
      const data =
        activeStandard.value?.fileType === "SYSTEM"
          ? {
              ...medicalMetaForm,
              collectFieldName: medicalMetaForm.name,
              collectItem: medicalMetaForm.title,
              collectValueType: medicalMetaForm.valueType
            }
          : medicalMetaForm
      return await handleEditApi(data)
        .then(() => {
          medicalMetaDialogVisible.value = false
          Message.success("操作成功！")
          commonTableRef.value?.refreshTableData()
          confirmLoading.value = false
        })
        .catch(err => {
          toastError(err)

          confirmLoading.value = false
        })
    } else {
      const handleAddApi = activeStandard.value?.fileType === "SYSTEM" ? addHomeMetaField : addStandardApi
      const data =
        activeStandard.value?.fileType === "SYSTEM"
          ? {
              ...medicalMetaForm,
              collectFieldName: medicalMetaForm.name,
              collectItem: medicalMetaForm.title,
              collectValueType: medicalMetaForm.valueType
            }
          : medicalMetaForm
      return await handleAddApi(data)
        .then(() => {
          medicalMetaDialogVisible.value = false
          Message.success("操作成功！")
          commonTableRef.value?.refreshTableData()
          confirmLoading.value = false
        })
        .catch(err => {
          toastError(err)
          confirmLoading.value = false
        })
    }
  })
}

/* ======================== 字段取值范围弹窗展示 ======================== */

const rangeDialogVisible = ref(false)

const showMetaRange = row => {
  activeRow.value = row
  if (row.valueRangeKey) {
    rangeDialogVisible.value = true
  }
}

const selectCollectSourceType = val => {
  if (val === "INTERFACE") {
    medicalMetaForm.valueRangeKey = ""
  } else if (val === "METADATA") {
    medicalMetaForm.collectValueSource = ""
  }
}

/* ======================== 批量设置 ======================== */
const batchSetDialogVisible = ref(false)

const batchState = reactive({
  type: "parentId",
  parentId: null,
  widgetType: "",
  loading: false
})

const handleBatchSet = async type => {
  if (!selectedRows.value.length) return Message.warning("请选择一条数据")
  batchState.type = type
  batchSetDialogVisible.value = true
  batchState.parentId = null
  batchState.widgetType = ""
  parentIdList.value = formatTreeInfo(
    (
      await getCatalogStandardList({
        catalogType: "CATALOG",
        catalogFileType: activeStandard.value?.fileType,
        filters: activeStandard.value?.scenarioType ? `scenario_type=${activeStandard.value?.scenarioType}` : ""
      })
    )?.data?.data
  )
}

// 确认设置
const confirmSet = async () => {
  console.log(`output->batchState `, batchState)
  if (!batchState[batchState.type]) return
  if (batchState.loading) return
  batchState.loading = true
  try {
    // todo
    await batchModifyStandardApi({
      ids: selectedRows.value.map(item => item.id),
      widgetType: batchState.type === "parentId" ? "" : batchState.widgetType,
      parentId: batchState.type === "parentId" ? batchState.parentId : null,
      fileType: activeStandard.value?.fileType
    })
    Message.success("设置成功")
    batchSetDialogVisible.value = false
    batchState.loading = false
    commonTableRef.value?.refreshTableData()
  } catch (err: any) {
    batchState.loading = false
    toastError(err)
  }
}

const handleSelect = val => {
  medicalMetaForm.scenarioType = val?.scenarioType
}
</script>

<style lang="less" scoped>
.container {
  display: flex;
  justify-content: space-between;
  height: 100%;
  // align-items: center;

  .left {
    padding: 20px;
    background-color: #fff;
    margin-right: 20px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 35, 114, 0.1);
    // min-width: 10vw;

    .content {
      width: 100%;
      display: flex;
      justify-content: space-between;
    }
  }
  .right {
    flex: 1;
    overflow-x: auto;
  }
}

.btns {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.item-more-btn {
  cursor: pointer;
  padding: 4px;
  &:hover {
    background-color: #ecf5ff;
  }
}
</style>
