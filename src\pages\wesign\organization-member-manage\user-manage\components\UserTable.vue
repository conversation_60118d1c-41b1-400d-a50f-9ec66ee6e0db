<template>
  <PageContainer separate>
    <!-- 头部搜索 -->
    <template #search>
      <div style="padding-top: 10px; font-weight: 600">{{ props.deptName }}·{{ commonTableRef?.tableState.total }}</div>

      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <el-form-item label="姓名">
          <el-input v-model="searchFormState.realName" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="工号">
          <el-input v-model="searchFormState.jobId" placeholder="请输入工号" />
        </el-form-item>
        <el-form-item label="用户类型">
          <el-select v-model="searchFormState.inside" placeholder="请选择用户类型">
            <el-option label="内部用户" value="INNER"></el-option>
            <el-option label="外部用户" value="OUT"></el-option>
          </el-select>
        </el-form-item>
      </SearchContainer>
    </template>

    <!-- 用户表格 -->
    <template #table>
      <CommonTable
        ref="commonTableRef"
        :table-columns="userTableColumns"
        :request-api="getUserListApi"
        :request-params="requestParams"
        :data-callback="dataCallback"
      >
        <template #header>
          <div>
            <AddButton @click="addUserDialogRef?.clickAddUser">新增</AddButton>
            <ImportButton @click="userImportDialogRef?.openUserImportDialog()">批量导入</ImportButton>
          </div>
        </template>
        <template #inside="{ row }">
          {{ row.inside === "INNER" ? "内部用户" : "外部用户" }}
        </template>
        <template #expirationTime="{ row }">
          {{ row.expirationTime ? formatDatetime(row.expirationTime) : "永久" }}
        </template>
        <template #sysUserStatus="{ row }">
          <el-tag :type="row.sysUserStatus ? 'success' : 'danger'">
            {{ row.sysUserStatus ? "已启用" : "已禁用" }}
          </el-tag>
        </template>
        <template #operation="{ row }">
          <TableButton @click="addUserDialogRef?.clickTableEdit(row)">编辑</TableButton>
          <TableButton @click="clickTableDisable(row)">{{ row.sysUserStatus ? "禁用" : "启用" }}</TableButton>
          <el-popover
            placement="bottom"
            :popper-style="{ minWidth: '80px', padding: '4px' }"
            :width="100"
            trigger="hover"
          >
            <template #reference>
              <span>
                <TableButton>
                  更多
                  <el-icon style="font-size: 10px; margin-left: 2px"><ArrowDownBold /></el-icon>
                </TableButton>
              </span>
            </template>
            <div>
              <PopoverButton @click="clickTableResetPassword(row)">重置密码</PopoverButton>
              <PopoverButton @click="relationRoleDialogRef?.clickRelationRole(row)">关联角色</PopoverButton>
              <PopoverButton @click="handleModifyHolidayTime(row)">休假设置</PopoverButton>
              <PopoverButton @click="clickTableDelete(row)">删除</PopoverButton>
            </div>
          </el-popover>
        </template>
      </CommonTable>
    </template>
  </PageContainer>

  <AddUserDialog ref="addUserDialogRef" :refresh-data="refreshData" />
  <RelationRoleDialog ref="relationRoleDialogRef" />
  <BatchImport
    ref="userImportDialogRef"
    :download-template-api="downloadTemplatesExcelApi"
    :import-file-api="importUserApi"
  />

  <!-- 休假时间弹窗 -->
  <DialogContainer
    v-model:visible="holidayVisible"
    title="休假设置"
    :width="500"
    :confirm-callback="confirmModifyHolidayTime"
    class="holiday-dialog"
  >
    <el-form ref="holidayRuleFormRef" label-position="top" :model="holidayForm" class="form">
      <el-form-item label="休假日期" prop="selectedDateRange">
        <el-date-picker
          v-model="holidayForm.selectedDateRange"
          type="datetimerange"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="x"
          @change="changeDateRange"
        />
      </el-form-item>
    </el-form>
  </DialogContainer>
</template>

<script setup lang="ts">
import { reactive, ref, watch } from "vue"
import { ArrowDownBold } from "@element-plus/icons-vue"
import {
  CommonTable,
  TableButton,
  PopoverButton,
  PageContainer,
  AddButton,
  ImportButton,
  SearchContainer,
  DialogContainer
} from "@/base-components"
import { BatchImport } from "@/page-components"
import { useTableSearch } from "@/hooks"
import { getUserListApi, getUserHolidayApi } from "@/interfaces"
import { useSystemStore } from "@/stores"
import { formatDatetime } from "@/utils"
import { SystemPrompt, Message, toastError } from "@/utils/message-tool"
import { RelationRoleDialog, AddUserDialog } from "../components"
import { userTableColumns } from "../config"
import {
  changeUserPasswordApi,
  changeUserStatusApi,
  importUserApi,
  downloadTemplatesExcelApi,
  managerUpdateUserHolidayApi
} from "../interface"
import type { FormInstance, FormRules } from "element-plus"

const systemStore = useSystemStore()

const props = defineProps({
  clickDept: { type: Object, required: true },
  getUserStatistics: { type: Function, required: true },
  deptName: { type: String, default: "" }
})

// 弹窗ref
const addUserDialogRef = ref<InstanceType<typeof AddUserDialog>>()
const userImportDialogRef = ref<InstanceType<typeof BatchImport>>()
const relationRoleDialogRef = ref<InstanceType<typeof RelationRoleDialog>>()

// table请求参数
const requestParams = reactive({ filters: "" })

/* ===================== 头部搜索 ====================== */
const searchFormState = reactive({
  deptWsid: "",
  realName: "",
  jobId: "",
  inside: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

// 监听搜索表单和点击的科室
watch([() => props.clickDept, () => searchParams.filters], val => setRequestParams(), { immediate: true, deep: true })

// 设置表格请求参数(请求参数要加上左侧点击的科室)
function setRequestParams() {
  requestParams.filters = searchParams.filters
  if (props.clickDept.filterValue && searchParams.filters)
    requestParams.filters = searchParams.filters + "," + props.clickDept.filterValue
  else if (props.clickDept.filterValue && !searchParams.filters) requestParams.filters = props.clickDept.filterValue
}

/* ===================== 用户表格 ====================== */

const commonTableRef = ref<InstanceType<typeof CommonTable>>()

const dataCallback = (data: Array<Record<string, any>>) => {
  return data.map(item => ({
    ...item,
    deptCode: item.deptCode ? item.deptCode.split(",") : []
  }))
}

// 刷新表格
function refreshData() {
  if (commonTableRef.value) commonTableRef.value.refreshTableData()
  props.getUserStatistics()
}

// 删除表格单行数据
function clickTableDelete(row) {
  SystemPrompt("您确定删除该用户？").then(() => {
    changeUserStatusApi({ status: "DEL", wsid: row.wsid })
      .then(res => {
        if (res.data.data) Message.success("删除成功")
        else Message.error("操作失败")
        refreshData()
      })
      .catch(err => toastError(err))
  })
}

// 切换启用&禁用状态
function clickTableDisable(row) {
  const nextStatus = row.sysUserStatusName === "启用" ? "DISABLE" : "ENABLE"
  const operation = row.sysUserStatusName === "启用" ? "禁用" : "启用"
  SystemPrompt(`您确定${operation}该用户？`).then(() => {
    changeUserStatusApi({ status: nextStatus, wsid: row.wsid })
      .then(res => {
        if (res.data.data) Message.success(`${operation}成功`)
        else Message.error("操作失败")
        refreshData()
      })
      .catch(err => toastError(err))
  })
}

// 重置密码
function clickTableResetPassword(row) {
  SystemPrompt("您确定重置该用户的密码重置后密码变为系统默认密码？").then(() => {
    changeUserPasswordApi({ userWsid: row.userWsid }).then(res => {
      Message.success(res.data?.message || "重置成功")
    })
  })
}

/* ====================== 休假时间 ======================== */
const holidayVisible = ref(false)
const holidayForm = reactive({
  selectedDateRange: [
    new Date(new Date().setHours(0, 0, 0, 0)).getTime(),
    new Date(new Date().setHours(23, 59, 59, 999)).getTime()
  ]
})
const selectUserWsid = ref("")

const holidayRuleFormRef = ref<FormInstance>()

const holidayRules: FormRules = {
  selectedDateRange: [{ required: false, message: "请选择休假日期", trigger: "blur" }]
}

const handleModifyHolidayTime = async row => {
  selectUserWsid.value = row?.userWsid
  holidayVisible.value = true
  const result = (await getUserHolidayApi(row?.userWsid))?.data?.data
  holidayForm.selectedDateRange = [result.holidayStartTime, result.holidayEndTime]
}

const changeDateRange = () => {}

// 确认设置休假时间
const confirmModifyHolidayTime = () => {
  console.log(`output->holidayForm`, holidayForm)
  holidayRuleFormRef.value?.validate(valid => {
    if (!valid) return
    systemStore.showLoading("加载中")
    managerUpdateUserHolidayApi({
      userWsid: selectUserWsid.value,
      holidayStartTime: holidayForm.selectedDateRange ? holidayForm.selectedDateRange[0] : null,
      holidayEndTime: holidayForm.selectedDateRange ? holidayForm.selectedDateRange[1] : null
    })
      .then(() => {
        systemStore.hideLoading()
        Message.success("设置成功")
        holidayVisible.value = false
      })
      .catch(err => {
        systemStore.hideLoading()
        toastError(err, "设置失败，请重试")
      })
  })
}

defineExpose({ commonTableRef })
</script>

<style lang="less" scoped>
@media screen and (min-width: 960px) {
  .holiday-dialog {
    :deep(.el-overlay-dialog) {
      top: -260px;
    }
  }
}
</style>
