import type { TableColumnItem, SearchFormConfigItem } from "@/types"

export const tabsRouterList = [
  { label: "待提交", path: "/medical-record/submission-wait" },
  { label: "已提交", path: "/medical-record/submission-success" }
]

export const submissionSearchFormConfig: Array<SearchFormConfigItem> = [
  { type: "patient", label: "患者信息" },
  { type: "daterange", label: "出院时间", prop: "outHospitalDatetime" },
  { type: "input", label: "主治医师", prop: "doctorInCharge" }
]

export const submissionColumns: Array<TableColumnItem> = [
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "mrNo", label: "病案号", minWidth: 150, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 120 },
  { prop: "patientSex", minWidth: 60, label: "性别", must: true },
  { prop: "visitId", label: "住院次数", minWidth: 110, sortable: true },
  { prop: "inHospitalDatetime", label: "入院日期", minWidth: 180, sortable: true, must: true },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 100 },
  { prop: "outHospitalDatetime", label: "出院日期", minWidth: 180, sortable: true, must: true },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 100 },
  { prop: "inHospitalDays", label: "住院天数", minWidth: 100 },
  { prop: "doctorInCharge", label: "主治医师", minWidth: 100 },
  { prop: "submitterName", label: "提交人", minWidth: 100 },
  { prop: "submitterTime", label: "提交时间", minWidth: 170 },
  { prop: "submitStatusEnum", label: "状态", minWidth: 100 },
  { prop: "operation", label: "操作", width: 100, fixed: "right", must: true }
]

export const SUBMISSION_STATUS_MAP = new Map<string, { tag: "success" | "warning" | "info" | "danger"; label: string }>(
  [
    ["NOT_SUBMIT", { tag: "info", label: "未提交" }],
    ["SUBMIT_ING", { tag: "warning", label: "提交中" }],
    ["SUBMIT_SUCCESS", { tag: "success", label: "提交成功" }],
    ["SUBMIT_FAIL", { tag: "danger", label: "提交失败" }]
  ]
)
