import SparkMD5 from "spark-md5"
import axios from "@/interfaces/axios-instance"

/**
 * @method POST
 * @desc   用户登录
 */
export function loginApi(obj) {
  const { userName, password } = obj
  return axios({
    method: "post",
    url: "/api/auth/login",
    data: {
      userName,
      password: SparkMD5.hash(password)
    }
  }).then(res => res.data?.data ?? {})
}

/**
 * @method POST
 * @desc   创建登录二维码
 */
export function getLoginQrCode() {
  return axios({
    method: "post",
    url: "/api/auth/login/qrcode",
    data: {
      acceptDataType: "RAW"
    }
  })
}

/**
 * @method POST
 * @desc   二维码登录--检查是否登录
 */
export function checkLoginQrCode(loginId) {
  return axios({
    method: "get",
    url: "/api/auth/login/qrcode",
    params: {
      loginId
    }
  })
}
