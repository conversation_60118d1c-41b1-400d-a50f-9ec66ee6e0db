<template>
  <DialogContainer
    v-model:visible="visible"
    title="自动导出配置"
    :width="550"
    :confirm-callback="handleConfirm"
    :confirm-loading="confirmLoading"
  >
    <el-form
      ref="ruleFormRef"
      :model="exportForm"
      label-position="right"
      :label-width="130"
      label-suffix="："
      :rules="exportFormRules"
    >
      <el-form-item label="导出配置" prop="outpatientHqmsExportAutoSwitch">
        <el-switch v-model="exportForm.outpatientHqmsExportAutoSwitch" />
      </el-form-item>
      <el-form-item label="IP地址" prop="outpatientHqmsExportIp">
        <el-input v-model="exportForm.outpatientHqmsExportIp" />
      </el-form-item>
      <el-form-item label="保存地址" prop="outpatientHqmsExportDirectory">
        <el-input v-model="exportForm.outpatientHqmsExportDirectory" />
      </el-form-item>
      <el-form-item label="开始时间" prop="outpatientHqmsExportStartDate">
        <el-date-picker v-model="exportForm.outpatientHqmsExportStartDate" type="date" clearable value-format="x" />
      </el-form-item>
      <el-form-item prop="outpatientHqmsExportDay">
        <template #label>
          数据量（天）
          <el-tooltip
            class="item"
            effect="dark"
            content="导出N天的数据量，默认导出1天，最大支持导出31天的数据"
            placement="top"
          >
            <i class="ri-question-fill" style="font-size: 18px"></i>
          </el-tooltip>
          ：
        </template>
        <el-input v-model="exportForm.outpatientHqmsExportDay" type="number" min="1" max="31">
          <template #append>天</template>
        </el-input>
      </el-form-item>
    </el-form>
  </DialogContainer>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from "vue"
import { DialogContainer } from "@/base-components"
import { querySystemConfig, updateSystemConfig } from "@/interfaces"
import { Message, toastError } from "@/utils"
import { exportFormRules } from "../config"

const visible = ref(false)
const confirmLoading = ref(false)

const exportForm = reactive({
  outpatientHqmsExportAutoSwitch: "" as any,
  outpatientHqmsExportIp: "",
  outpatientHqmsExportDirectory: "",
  outpatientHqmsExportStartDate: 0, // 开始日期
  outpatientHqmsExportDay: 1 // 数据量(天)
})

onMounted(async () => {
  await querySystemConfig({ type: "OUTPATIENT_HQMS" }).then(res => {
    res.data.data?.forEach((item: Record<string, any>) => {
      if (item.key === "outpatientHqmsExportStartDate") {
        exportForm.outpatientHqmsExportStartDate = Number(item.value)
      } else {
        exportForm[item.key] = item.value
      }
    })
  })
  exportForm.outpatientHqmsExportAutoSwitch = exportForm.outpatientHqmsExportAutoSwitch === "1"
})
const ruleFormRef = ref()

const handleConfirm = async () => {
  ruleFormRef.value?.validate(async valid => {
    if (!valid) return
    try {
      confirmLoading.value = true
      await updateSystemConfig([
        { key: "outpatientHqmsExportAutoSwitch", value: exportForm.outpatientHqmsExportAutoSwitch ? "1" : "0" },
        { key: "outpatientHqmsExportIp", value: exportForm.outpatientHqmsExportIp },
        { key: "outpatientHqmsExportDirectory", value: exportForm.outpatientHqmsExportDirectory },
        {
          key: "outpatientHqmsExportStartDate",
          value: new Date(exportForm.outpatientHqmsExportStartDate).setHours(0, 0, 0, 0)
        },
        { key: "outpatientHqmsExportDay", value: exportForm.outpatientHqmsExportDay }
      ])
      confirmLoading.value = false
      Message.success("保存成功")
      visible.value = false
    } catch (error: any) {
      confirmLoading.value = false
      toastError(error, "保存失败")
    }
  })
}

const show = () => {
  visible.value = true
}

defineExpose({
  show
})
</script>
