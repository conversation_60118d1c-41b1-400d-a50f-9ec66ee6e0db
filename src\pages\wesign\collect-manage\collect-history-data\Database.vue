<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />

      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <CommonInputFormItem v-model="searchFormState.name" label="名称" />
        <CommonSelectFormItem v-model="searchFormState.dataType" label="数据类型" :options="dataTypeOptions" />
        <CommonSelectFormItem v-model="searchFormState.status" label="状态" :options="rowStatusOptions" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="tableRef"
        :table-columns="databaseTableColumns"
        :request-api="getDatabaseHistoryListApi"
        :request-params="searchParams"
      >
        <template #header>
          <AddButton @click="showAddForm">新增</AddButton>
        </template>
        <template #systemWsid="{ row }">
          {{ systemVendorOptions.find(option => option.value === row.systemWsid)?.label || "--" }}
        </template>
        <template #dataSourceWsid="{ row }">
          {{ dataSourceTypeOptions.find(option => option.value === row.dataSourceWsid)?.label || "--" }}
        </template>
        <template #dataType="{ row }">
          {{ getDataTypeDesc(row.dataType) }}
        </template>
        <template #mrClassCode="{ row }">
          {{ mrClassOptions.find(option => option.value === row.mrClassCode)?.label || "--" }}
        </template>
        <template #extractType="{ row }">
          {{ getExtractTypeDesc(row.extractType) }}
        </template>
        <template #operation="{ row }">
          <TableButton @click="showEditForm(row)">编辑</TableButton>
          <TableButton @click="confirmToggle(row, row.wsid, row.status === 'ENABLE' ? 'DISABLE' : 'ENABLE')">
            {{ row.status === "ENABLE" ? "禁用" : "启用" }}
          </TableButton>
          <TableButton v-if="row.status === 'ENABLE'" @click="triggerJob(row)">执行一次</TableButton>
          <TableButton @click="confirmDelete(row, row.wsid)">删除</TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>

  <DialogContainer
    v-model:visible="visible"
    :title="actionType === 'edit' ? '修改数据库配置' : '添加数据库配置'"
    :width="1000"
  >
    <el-form
      ref="formRef"
      label-position="right"
      label-width="145px"
      label-suffix="："
      :model="formState"
      :rules="databaseFormRules"
    >
      <CommonInputFormItem v-model="formState.name" label="名称" prop="name" />

      <CommonSelectFormItem
        v-model="formState.systemWsid"
        label="厂商系统"
        prop="systemWsid"
        :options="systemVendorOptions"
      />

      <CommonSelectFormItem
        v-model="formState.dataSourceWsid"
        label="数据源名称"
        prop="dataSourceWsid"
        :options="dataSourceTypeOptions"
      />

      <el-form-item label="数据类型" prop="dataType">
        <template #label>
          <span>数据类型</span>
          <el-popover
            placement="right"
            width="800"
            trigger="click"
            :popper-style="{ background: 'rgba(0, 0, 0, 0.5)' }"
          >
            <el-table
              :data="dataTypeTableData"
              :loading="dataTypeTableLoading"
              style="width: 100%"
              :border="true"
              max-height="250"
            >
              <el-table-column prop="dataTypeEnumName" label="字段类型">
                <template #default="scope">
                  <span v-if="scope.row.dataTypeEnumName === 'RESULT_FIELD'">查询字段</span>
                  <span v-else>条件字段</span>
                </template>
              </el-table-column>
              <el-table-column prop="fieldName" label="字段名" />
              <el-table-column prop="typeCh" label="字段中文名" width="100px" />
              <el-table-column prop="fieldType" label="字段类型" />
              <el-table-column prop="required" label="是否必须">
                <template #default="scope">
                  <span v-if="scope.row.required">是</span>
                  <span v-else>否</span>
                </template>
              </el-table-column>
            </el-table>
            <template #reference>
              <i class="ri-question-fill" style="font-size: 18px"></i>
            </template>
          </el-popover>
          <span>：</span>
        </template>
        <el-select v-model="formState.dataType" placeholder="请选择数据类型" @change="changeDataType">
          <el-option
            v-for="item in dataTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <CommonSelectFormItem
        v-if="formState.dataType === 'MR_CONTENT'"
        v-model="formState.mrClassCode"
        label="文书分类"
        prop="mrClassCode"
        :options="mrClassOptions"
      />

      <el-form-item ref="sqlLargeScreen" required label="SQL语句配置" prop="querySql" class="sql-item">
        <template #label>
          <span>SQL语句配置</span>
          <el-popover
            placement="right"
            width="800"
            trigger="click"
            :popper-style="{ background: 'rgba(0, 0, 0, 0.5)' }"
          >
            <div class="tips-content">
              <p>①sql返回列 必选包含必填字段 别名形式返回；</p>
              <p>
                ②sql的where 条件支持 【患者id】${patientId}
                ，【住院次】${patientId}，【患者编号】${patientId}，【身份证】${idCard}；例如： WHERE t.partent_id =
                '${patientId}' AND t.visit_id = '${visitId}'
              </p>
              <p>③sql支持批量查询的字段：【患者编号】${patientId} ；例如： WHERE t.inpNo in ('${inpNos}')</p>
              <p>④查询的文件，解析顺序base64->fileBlob->fileDownloadUrl->fileFtpPath</p>
            </div>
            <template #reference>
              <i class="ri-question-fill" style="font-size: 18px"></i>
            </template>
          </el-popover>
          <span>：</span>
        </template>
        <Codemirror
          v-model="formState.querySql"
          placeholder="请输入SQL语句配置"
          :tab-size="10"
          :extensions="extensions"
          :style="{ maxHeight: '150px', width: '100%' }"
        />
        <div ref="sqlFullScreen" class="fullscreen-btn">
          <i v-if="!isSqlFullscreen" title="全屏显示" class="ri-fullscreen-line" @click="sqlToggle"></i>
          <i v-else title="退出全屏" class="ri-fullscreen-exit-line" @click="sqlToggle"></i>
        </div>
      </el-form-item>

      <el-form-item>
        <p style="font-size: 12px">
          <span class="dialog-hint-red">提示：</span>
          您所配置的SQL语句，必须来源于数据类型字段
        </p>
      </el-form-item>

      <el-form-item ref="dataCleanLargeScreen" label="数据清洗" prop="sourceCode" class="sql-item">
        <Codemirror
          v-model="formState.sourceCode"
          placeholder="请输入"
          :tab-size="10"
          :extensions="dataExtensions"
          :style="{ maxHeight: '150px', width: '100%' }"
        />
        <div ref="dataCleanFullScreen" class="fullscreen-btn">
          <i v-if="!isDataCleanFullscreen" title="全屏显示" class="ri-fullscreen-line" @click="dataCleanToggle"></i>
          <i v-else title="退出全屏" class="ri-fullscreen-exit-line" @click="dataCleanToggle"></i>
        </div>
      </el-form-item>

      <CommonRadioFormItem
        v-model="formState.extractType"
        label="采集模式"
        prop="extractType"
        :options="extractTypeOptions"
      />

      <template v-if="formState.extractType === 'INCREMENT'">
        <CommonInputFormItem
          v-model="formState.incrementFieldKey"
          label="自增key"
          prop="incrementFieldKey"
          placeholder="请输入自增key"
        />

        <CommonRadioFormItem
          v-model="formState.incrementFieldType"
          label="自增字段类型"
          prop="incrementFieldType"
          :options="incrementTypeOptions"
          @change="() => (formState.incrementLastValue = '')"
        />

        <CommonInputFormItem
          v-if="formState.incrementFieldType === 'NUMBER'"
          v-model="formState.incrementLastValue"
          label="最近自增值"
          prop="incrementLastValue"
          placeholder="请输入最近自增值"
        />

        <el-form-item v-if="formState.incrementFieldType === 'TIME'" label="最近自增值" prop="incrementLastValue">
          <el-date-picker
            v-model="formState.incrementLastValue"
            placeholder="请选择最近自增时间"
            type="datetime"
            value-format="x"
          />
        </el-form-item>

        <CommonInputFormItem
          v-model="formState.incrementDelta"
          label="增幅"
          prop="incrementDelta"
          placeholder="请输入增幅"
        />
      </template>

      <el-form-item label="采集频率(cron)" prop="cron">
        <el-input v-model="formState.cron" placeholder="0 0/10 * * * ?" />
      </el-form-item>

      <CommonRadioFormItem
        v-model="formState.resultDataType"
        :options="resultDataTypeOptions"
        prop="resultDataType"
        label="返回数据类型"
        placeholder="请选择返回数据类型"
      />
    </el-form>

    <template #footer>
      <el-button :loading="actionLoading" @click="closeFormDialog()">取消</el-button>
      <el-button :loading="actionLoading || testLoading" @click="handleTest">测试数据读取</el-button>
      <el-button :loading="actionLoading" type="primary" @click="handleSave">保存</el-button>
    </template>
  </DialogContainer>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import { java } from "@codemirror/lang-java"
import { sql } from "@codemirror/lang-sql"
import { useFullscreen } from "@vueuse/core"
import { Codemirror } from "vue-codemirror"
import {
  PageContainer,
  DialogContainer,
  CommonTable,
  AddButton,
  TableButton,
  CommonSelectFormItem,
  CommonInputFormItem,
  CommonRadioFormItem,
  SearchContainer
} from "@/base-components"
import { TabsRouter } from "@/page-components"
import { rowStatusOptions } from "@/configs"
import { useTableSearch, useCommonOptions } from "@/hooks"
import useFormSetting from "@/hooks/useFormSetting_v2"
import useTableOperation from "@/hooks/useTableOperation_v2"
import { getMrClassList } from "@/interfaces"
import { SystemPrompt, Message, toastError } from "@/utils"
import {
  dbTestSelectOne,
  getAllDataSources,
  getCollectSystemList,
  getFieldDicts,
  triggerJobApi
} from "../collect-way/interface"
import {
  tabsRouterList,
  databaseTableColumns,
  dataTypeOptions,
  myTheme,
  extractTypeOptions,
  databaseFormRules,
  getDataTypeDesc,
  getExtractTypeDesc,
  incrementTypeOptions,
  resultDataTypeOptions
} from "./config"
import {
  addDatabaseHistoryApi,
  changeDatabaseHistoryStatusApi,
  deleteDatabaseHistoryApi,
  getDatabaseHistoryListApi,
  updateDatabaseHistoryApi
} from "./interface"
import type { FormInstance } from "element-plus"

/* ======================== 页面加载时获取选项 ======================== */

// 获取所有厂商系统选项
const { options: systemVendorOptions } = useCommonOptions({
  getOptionsApi: getCollectSystemList,
  labelAlias: "systemName",
  valueAlias: "wsid"
})

// 获取所有数据源选项
const { options: dataSourceTypeOptions } = useCommonOptions({
  getOptionsApi: getAllDataSources,
  labelAlias: "name",
  valueAlias: "wsid"
})

// 获取所有文书分类选项
const { options: mrClassOptions } = useCommonOptions({
  getOptionsApi: getMrClassList,
  labelAlias: "mrClassName",
  valueAlias: "mrClassCode"
})

/* ======================== 搜索 ======================== */

const tableRef = ref<InstanceType<typeof CommonTable>>()

const searchFormState = reactive({
  name: "",
  dataType: "",
  status: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/* ======================== 行操作 ======================== */

const showAddForm = () => {
  changeDataType()
  showForm("add")
}

const showEditForm = row => {
  changeDataType()
  showForm("edit", row)
}

// 执行一次
function triggerJob(row) {
  triggerJobApi(row.wsid)
    .then(res => {
      if (res.data?.message) {
        Message.success(res.data.message)
      }
    })
    .catch(err => toastError(err, "操作失败"))
}

/* ======================== 表格 ======================== */

const formRef = ref<FormInstance>()
const testLoading = ref(false)

const formInitialValue = {
  wsid: "",
  name: "", // 数据库名称
  systemWsid: "", // 厂商系统
  dataSourceWsid: "", // 数据源id
  dataType: "", // 数据类型
  mrClassCode: "", // 文书分类
  querySql: "", // sql语句配置
  sourceCode: "", // 数据清洗
  extractType: "ALL", // 采集模式
  cron: "", // 采集频率
  resultDataType: "FILE", // 返回数据类型,
  incrementFieldKey: "", // 自增key
  incrementFieldType: "TIME", // 自增key类型
  incrementLastValue: "", // 自增key值
  incrementDelta: "" // 自增步长
}

const { visible, actionType, formState, showForm, closeForm } = useFormSetting(formInitialValue)

const { confirmAdd, confirmEdit, confirmToggle, confirmDelete, actionLoading } = useTableOperation(formState, {
  apiGroup: {
    addApi: addDatabaseHistoryApi,
    editApi: updateDatabaseHistoryApi,
    toggleApi: changeDatabaseHistoryStatusApi,
    deleteApi: deleteDatabaseHistoryApi
  },
  closeForm: closeFormDialog,
  deleteConfirmation: row => SystemPrompt(`您确定要删除“${row?.name}”吗？`),
  toggleConfirmation: row => {
    const action = row?.status === "DISABLE" ? "启用" : "禁用"
    return SystemPrompt(`您确定要${action}“${row?.name}”吗？`)
  },
  callback: () => tableRef.value?.refreshTableData()
})

// 测试数据库
const handleTest = () => {
  if (!formRef.value) return
  formRef.value.validate(valid => {
    if (!valid) return
    // 测试前判断数据源是否有效
    const dataSourceTypeItem = dataSourceTypeOptions.value.find(item => item.value === formState.dataSourceWsid)
    if (!dataSourceTypeItem) return Message.error("数据源不存在")
    testLoading.value = true
    dbTestSelectOne({ dataSourceWsid: formState.dataSourceWsid, querySql: formState.querySql })
      .then(() => Message.success("测试成功"))
      .catch(err => toastError(err, "测试失败"))
      .finally(() => (testLoading.value = false))
  })
}

const handleSave = () => {
  formRef.value?.validate(valid => {
    if (!valid) return
    actionType.value === "edit" ? confirmEdit() : confirmAdd()
  })
}

// 关闭编辑弹窗
function closeFormDialog() {
  closeForm()
  testLoading.value = false
}

/* ================= 数据类型 ==================== */

const dataTypeTableData = ref()
const dataTypeTableLoading = ref(false)

const changeDataType = () => {
  const type = formState.dataType || null
  dataTypeTableLoading.value = true
  getFieldDicts({
    filters: `type=${type}`
  })
    .then(res => {
      dataTypeTableData.value = res.data.data
      dataTypeTableLoading.value = false
    })
    .catch(() => {
      dataTypeTableLoading.value = false
    })
}

/* ======================== 代码块 ======================== */

// SQL语句配置
const extensions = [sql(), myTheme]

const sqlLargeScreen = ref(null)
const sqlFullScreen = ref(null)
const { toggle: sqlToggle, isFullscreen: isSqlFullscreen } = useFullscreen(sqlLargeScreen)

// 数据清洗
const dataExtensions = [java(), myTheme]

const dataCleanLargeScreen = ref(null)
const dataCleanFullScreen = ref(null)
const { toggle: dataCleanToggle, isFullscreen: isDataCleanFullscreen } = useFullscreen(dataCleanLargeScreen)
</script>

<style lang="less" scoped>
:deep(.cm-editor) {
  border: 1px solid #dcdfe6;
}

:deep(.cm-focused) {
  outline: none !important;
}

:deep(.cm-lineNumbers) {
  border-right: 1px solid #dcdfe6;
}

.sql-item {
  :deep(.el-form-item__content) {
    align-items: baseline;
    position: relative;
  }
}

.fullscreen-btn {
  position: absolute;
  cursor: pointer;
  right: 10px;
  top: 15px;
  line-height: 1;
  transform: translateY(-50%);
  i {
    opacity: 0.5;
    font-size: 20px;
    color: #333;
  }
  &:hover {
    i {
      opacity: 1;
    }
  }
}
</style>
