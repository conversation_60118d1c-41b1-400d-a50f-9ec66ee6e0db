<template>
  <PageContainer>
    <!-- 搜索表单 -->
    <SearchForm
      :form-config="systemNoticeSearchFormConfig"
      :form-state="searchFormState"
      @query-btn-click="handleQuery"
      @reset-btn-click="handleReset"
    />

    <!-- 消息公告表格 -->
    <CommonTable
      ref="systemNoticeTableRef"
      :table-columns="systemNoticeTableColumns"
      :request-api="getSystemNoticeListApi"
      :request-params="searchParams"
    >
      <template #header>
        <AddButton @click="handleAdd">新增</AddButton>
      </template>
      <template #createDateTime="{ row }">
        {{ formatDatetime(row.createDateTime) }}
      </template>
      <template #updateDateTime="{ row }">
        {{ formatDatetime(row.updateDateTime) }}
      </template>
      <template #sendDateTime="{ row }">
        {{ row.statusEnum === NoticeStatusEnum.DELETE ? "--" : formatDatetime(row.sendDateTime || row.updateDateTime) }}
      </template>
      <template #statusEnum="{ row }">
        <el-tag v-if="row.statusEnum === NoticeStatusEnum.NOT_SEND">未发送</el-tag>
        <el-tag v-else-if="row.statusEnum === NoticeStatusEnum.HAS_BEEN_SEND" type="success">已发送</el-tag>
        <el-tag v-else-if="row.statusEnum === NoticeStatusEnum.DELETE" type="danger">已删除</el-tag>
        <el-tag v-else type="warning">未知</el-tag>
      </template>
      <template #operation="{ row }">
        <TableButton
          :disabled="row.statusEnum !== NoticeStatusEnum.NOT_SEND"
          tooltip="已发送的公告不可再次编辑！"
          @click="handleEdit(row)"
        >
          编辑
        </TableButton>
        <TableButton @click="handlePreview(row)">预览</TableButton>
        <TableButton v-if="row.statusEnum === NoticeStatusEnum.NOT_SEND" @click="handleDelete(row)">删除</TableButton>
      </template>
    </CommonTable>
  </PageContainer>

  <!-- 新增&编辑公告 -->
  <DialogContainer
    v-model:visible="noticeDialogVisible"
    :title="noticeDialogTitle"
    :width="600"
    :confirm-callback="handleConfirm"
  >
    <el-form
      ref="ruleFormRef"
      label-suffix="："
      :label-width="95"
      :rules="systemNoticeFormRules"
      :model="noticeFormState"
    >
      <el-form-item v-model="noticeFormState.title" label="标题" prop="title">
        <el-input v-model="noticeFormState.title"></el-input>
      </el-form-item>
      <el-form-item label="发送内容" prop="contentHtml">
        <NoticeEditor
          ref="editorRef"
          v-model="noticeFormState.contentHtml"
          :content="noticeFormState.contentHtml"
          required
          @get-html="getHtml"
        />
      </el-form-item>
      <el-form-item label="发送方式" prop="sendType">
        <el-radio-group v-model="noticeFormState.sendType" @change="changeSendType">
          <el-radio :label="SendTypeEnum.Immediate" size="small">立刻发送</el-radio>
          <el-radio :label="SendTypeEnum.Assigned" size="small">指定时间</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        v-if="noticeFormState.sendType === SendTypeEnum.Assigned && noticeFormState.sendDateTime"
        label="发送时间"
        prop="sendDateTime"
      >
        <el-date-picker
          v-model="noticeFormState.sendDateTime"
          type="datetime"
          format="YYYY-MM-DD HH:mm"
          popper-class="not-show-now"
          :disabled-date="getDisabledDate"
          :clearable="false"
        />
      </el-form-item>
    </el-form>
  </DialogContainer>

  <!-- 公告预览 -->
  <DialogContainer v-model:visible="previewDialogVisible" :width="480" :title="noticeDetail.title">
    <div class="ql-editor" v-html="noticeDetail.contentHtml"></div>
    <div v-if="noticeDetail.statusEnum !== NoticeStatusEnum.DELETE" style="text-align: right">
      {{ formatDatetime(noticeDetail.sendDateTime) }}
    </div>

    <template #footer>
      <el-button type="primary" @click="previewDialogVisible = false">知道了</el-button>
    </template>
  </DialogContainer>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue"
import moment from "moment"
import { PageContainer, CommonTable, DialogContainer, AddButton, TableButton } from "@/base-components"
import { SearchForm } from "@/page-components"
import { useTableSearch } from "@/hooks"
import { Message, toastError, formatDatetime, SystemPrompt } from "@/utils"

import { getSystemNoticeListApi, addSystemNoticeApi, editSystemNoticeApi, deleteSystemNoticeApi } from "../interface"
import NoticeEditor from "./NoticeEditor.vue"
import {
  systemNoticeTableColumns,
  systemNoticeFormRules,
  NoticeStatusEnum,
  SendTypeEnum,
  systemNoticeSearchFormConfig
} from "./config"
import type { FormInstance } from "element-plus"

const systemNoticeTableRef = ref<InstanceType<typeof CommonTable>>()

/* ======================== 搜索 ======================== */

const searchFormState = reactive({
  title: "",
  status: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/* ======================== 公告编辑表单 ======================== */

const ruleFormRef = ref<FormInstance>()

const editorRef = ref()

const noticeFormState = reactive({
  wsid: "",
  title: "",
  contentHtml: "",
  sendDateTime: null as Date | null,
  sendType: SendTypeEnum.Immediate
})

// 设置今天以前的日期为不可选状态
function getDisabledDate(date) {
  return date.getTime() < moment().subtract(1, "day").valueOf()
}

// 切换发送日期类型
function changeSendType(nextSendType) {
  if (nextSendType === SendTypeEnum.Immediate) noticeFormState.sendDateTime = null
  else noticeFormState.sendDateTime = new Date()
}

// 获取消息内容回填富文本
async function getHtml(html, isSuccess) {
  // backspace会产生空格，影响判断，所以校验失败需要去除空格
  if (isSuccess) noticeFormState.contentHtml = html
  else noticeFormState.contentHtml = ""
  await ruleFormRef.value?.validateField("contentHtml")
}

/* ======================== 新增&修改公告 ======================== */

const noticeDialogVisible = ref(false)
const noticeDialogTitle = ref("")

// 新增公告
function handleAdd() {
  noticeFormState.wsid = ""
  noticeFormState.title = ""
  noticeFormState.contentHtml = ""
  noticeFormState.sendDateTime = null
  noticeFormState.sendType = SendTypeEnum.Immediate

  noticeDialogVisible.value = true
  noticeDialogTitle.value = "新增"
}

// 修改公告
function handleEdit(row) {
  const sendDateTime = row.sendDateTime ? new Date(row.sendDateTime) : null
  noticeFormState.wsid = row.wsid
  noticeFormState.title = row.title
  noticeFormState.contentHtml = row.contentHtml
  noticeFormState.sendDateTime = sendDateTime
  noticeFormState.sendType = sendDateTime ? SendTypeEnum.Assigned : SendTypeEnum.Immediate

  noticeDialogVisible.value = true
  noticeDialogTitle.value = "编辑"
}

// 确认操作
const handleConfirm = async () => {
  const noticeData = {
    title: noticeFormState.title,
    contentHtml: editorRef.value.returnHTML(),
    sendDateTime: noticeFormState.sendDateTime ? noticeFormState.sendDateTime.getTime() : null
  }
  noticeFormState.contentHtml = editorRef.value.returnHTML()
  ruleFormRef.value?.validate(valid => {
    if (!valid) return
    const handler =
      noticeDialogTitle.value === "新增"
        ? addSystemNoticeApi(noticeData)
        : editSystemNoticeApi(noticeFormState.wsid, noticeData)

    handler
      .then(() => {
        noticeDialogVisible.value = false
        Message.success(`${noticeDialogTitle.value}公告成功`)
        systemNoticeTableRef.value?.refreshTableData()
      })
      .catch(err => toastError(err))
  })
}

/* ======================== 公告预览 ======================== */

const previewDialogVisible = ref(false)

// 公告详情
const noticeDetail = reactive({
  statusEnum: "",
  title: "",
  contentHtml: "",
  sendDateTime: ""
})

// 预览公告
function handlePreview(row) {
  noticeDetail.statusEnum = row.statusEnum
  noticeDetail.title = row.title
  noticeDetail.contentHtml = row.contentHtml
  noticeDetail.sendDateTime = row.sendDateTime || row.updateDateTime
  previewDialogVisible.value = true
}

// 删除公告
function handleDelete(row) {
  SystemPrompt("是否确认删除此公告，删除公告后将会同步取消发送").then(() => {
    deleteSystemNoticeApi(row.wsid)
      .then(() => {
        Message.success("删除成功")
        systemNoticeTableRef.value?.refreshTableData()
      })
      .catch(err => toastError(err))
  })
}
</script>
