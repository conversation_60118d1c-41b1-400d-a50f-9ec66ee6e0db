<template>
  <div>
    <el-dialog v-model="visible" title="提交详情" width="90vw" align-center>
      <div v-loading="loading">
        <el-select v-model="businessWsid" @change="handleVersionChange">
          <el-option
            v-for="history in historyList"
            :key="history.snapshotBagWsid"
            :label="history.versionShow"
            :value="history.snapshotBagWsid"
          ></el-option>
        </el-select>

        <div class="submit-history-detail common-box-shadow">
          <!-- <div>
          <MedicalRecordTree
            :base-info="recordInfo.baseInfo"
            :tree-info="recordInfo.treeInfo"
            @click-pdf="handleFileChange"
          />
        </div>

        <div class="pdf-container common-box-shadow">
          <PdfPreviewComponent ref="pdfPreviewRef" :src="pdfSrc" />
        </div> -->

          <div class="view-left">
            <MedicalRecordTree
              ref="recordTreeRef"
              can-collapse
              :base-info="recordInfo.baseInfo"
              :tree-info="recordInfo.treeInfo"
              @click-pdf="handleFileChange"
            />
          </div>

          <div class="view-right">
            <!-- 顶部信息及按钮 -->
            <view class="tool-bar">
              <view class="document-name">{{ recordInfo.documentName }}</view>

              <view class="tool-bar-buttons">
                <el-button @click="toPrev">上一份文件</el-button>
                <el-button @click="toNext">下一份文件</el-button>
              </view>
            </view>

            <!-- pdf -->
            <div class="view-middle common-box-shadow">
              <!-- 病案备注 -->
              <div v-if="recordInfo.baseInfo.submitRemark" class="remark" @click="showSubmitRemark">
                <div>备注：{{ recordInfo.baseInfo.submitRemark }}</div>
              </div>

              <PdfPreviewComponent
                ref="pdfPreviewRef"
                :src="pdfSrc"
                :medical-location="recordInfo.baseInfo?.documentStorageLocation"
              />
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <DialogContainer v-model:visible="remarkDialogVisible" title="提交备注" :width="550">
      <el-input disabled :value="recordInfo.baseInfo.submitRemark" type="textarea" :rows="10" />
      <template #footer>
        <div>
          <el-button type="primary" @click="remarkDialogVisible = false">知道了</el-button>
        </div>
      </template>
    </DialogContainer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue"
import { debounce } from "lodash-es"
import { PdfPreviewComponent, DialogContainer } from "@/base-components"
import { MedicalRecordTree } from "@/page-components"
import { toastError } from "@/utils"
import { getSubmitHistoryApi, getSubmitSnapshotApi } from "../../interface"

const loading = ref(false)
const pdfSrc = ref("")

const handleFileChange = node => {
  pdfSrc.value = "/api" + node?.contentFilePath
  // recordInfo.targetFileWsid = node?.fileWsid
  recordInfo.value.documentName = node?.title
  recordInfo.value.documentWsid = node?.wsid
}

/* ============== 获取数据 =============== */

const businessWsid = ref("") // 当前版本wsid
const historyList = ref<Record<string, any>>([]) // 历史快照列表
// 病案信息
const recordInfo = ref<Record<string, any>>({
  treeInfo: {},
  baseInfo: {},
  documentName: "", // 当前文档名称
  documentWsid: "" as string, // 当前点击的文档wsid
  quashCause: ""
})

// 获取历史快照
const getSubmitHistory = async action => {
  businessWsid.value = action.businessWsid
  loading.value = true
  try {
    const res = await Promise.all([
      getSubmitHistoryApi(action.documentBagWsid),
      getSubmitSnapshotApi(action.businessWsid)
    ])
    historyList.value = res[0].data.data
    recordInfo.value = res[1].data.data
    loading.value = false
  } catch (error: any) {
    loading.value = false
    toastError(error || "获取数据失败")
  }
}

// 选择不同版本，重新获取快照信息
const handleVersionChange = val => {
  loading.value = true
  getSubmitSnapshotApi(val)
    .then(res => {
      recordInfo.value = res.data.data
      loading.value = false
    })
    .catch(error => {
      loading.value = false
      toastError(error)
    })
}

/* ============== dialog =============== */

const visible = ref(false)

const show = action => {
  visible.value = true
  getSubmitHistory(action)
}

const close = () => {
  visible.value = false
}

/* =============== toolbar =============== */

const recordTreeRef = ref<InstanceType<typeof MedicalRecordTree>>()
const documentNodeList = computed(() => recordTreeRef.value?.state.documentNodeList)

const registerKeyboardEvent = () => {
  document.onkeyup = e => {
    const keyCode = e.code
    // 左/上键
    if (keyCode === "ArrowLeft" || keyCode === "ArrowUp") {
      toPrev()
    }
    // 右/下键
    else if (keyCode === "ArrowRight" || keyCode === "ArrowDown") {
      toNext()
    }
  }
}
onMounted(() => {
  registerKeyboardEvent()
})

const toPrev = debounce(() => {
  if (!documentNodeList.value) return
  const index = documentNodeList.value.findIndex(item => item.wsid === recordInfo.value.documentWsid) || 0
  if (index <= 0) return
  const targetNode = documentNodeList.value[index - 1]
  handleFileChange(targetNode)
  recordTreeRef.value?.setCurrentKey(targetNode.wsid)
}, 300)

const toNext = debounce(() => {
  if (!documentNodeList.value) return
  const index = documentNodeList.value.findIndex(item => item.wsid === recordInfo.value.documentWsid)
  if (index >= documentNodeList.value.length - 1) return
  const targetNode = documentNodeList.value[index + 1]
  handleFileChange(targetNode)
  recordTreeRef.value?.setCurrentKey(targetNode.wsid)
}, 300)

// 展示备注
const remarkDialogVisible = ref(false)
const showSubmitRemark = () => {
  remarkDialogVisible.value = true
}

defineExpose({
  show,
  close
})
</script>

<style lang="less" scoped>
.submit-history-detail {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  max-height: 70vh;
  overflow-y: auto;

  .view-middle {
    overflow-y: auto;
    height: calc(100% - 60px);
    min-width: 400px;
    flex: 1;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
  }
}

.pdf-container {
  overflow-x: auto;
  min-width: 400px;
  flex: 1;
  margin: 0 16px 0px 0px;
  box-sizing: border-box;
}
.tool-bar {
  background: #fff;
  border-radius: 8px;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 40px;

  .document-name {
    font-size: 16px;
    font-weight: 600;
  }
}

.remark {
  color: #f59a23;
  background-color: #fdf6ec;
  padding: 10px;
  cursor: pointer;
  div {
    max-width: 800px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.view-right {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
}
</style>
