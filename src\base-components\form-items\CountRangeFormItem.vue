<template>
  <el-form-item :label="props.label" :error="errorString">
    <div class="count-range-container">
      <el-input v-model="fromValue" type="number" :controls="false" placeholder="最小值" />
      <span style="margin: 0px 5px">-</span>
      <el-input v-model="toValue" type="number" :controls="false" placeholder="最大值" />
      <div class="count-range-unit">{{ props.unit }}</div>
    </div>
  </el-form-item>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue"

interface PropsType {
  label: string
  from: string
  to: string
  min?: number | ""
  max?: number | ""
  unit?: string
  error?: string
}

const props = withDefaults(defineProps<PropsType>(), {
  min: "",
  max: "",
  unit: "",
  error: ""
})

// 错误信息
const errorString = ref("")

const emits = defineEmits(["update:from", "update:to", "update:error"])

function validateValue(from, to) {
  const minValue = from === "" ? "" : Number(from)
  const maxValue = to === "" ? "" : Number(to)
  if (props.min !== "" && minValue !== "" && minValue < props.min) {
    errorString.value = `最小值不能小于${props.min}`
    emits("update:error", errorString.value)
    return false
  } else if (props.max !== "" && maxValue !== "" && maxValue > props.max) {
    errorString.value = `最大值不能大于${props.min}`
    emits("update:error", errorString.value)
    return false
  } else if (minValue !== "" && maxValue !== "" && minValue > maxValue) {
    errorString.value = `最小值不能大于最大值`
    emits("update:error", errorString.value)
    return false
  }
  errorString.value = ""
  emits("update:error", errorString.value)

  return true
}

const fromValue = computed({
  get: () => props.from,
  set: val => {
    emits("update:from", val)
  }
})

const toValue = computed({
  get: () => props.to,
  set: val => {
    emits("update:to", val)
  }
})

watch(
  [() => fromValue.value, () => toValue.value],
  val => {
    validateValue(fromValue.value, toValue.value)
  },
  { immediate: true }
)

defineExpose({ errorString })
</script>

<style lang="less" scoped>
.count-range-container {
  display: flex;
  :deep(.el-input) {
    width: 70px;
    input[type="number"]::-webkit-inner-spin-button {
      -webkit-appearance: none !important;
    }
  }
  .count-range-unit {
    font-size: 12px;
    color: 606266;
    margin-left: 5px;
  }
}
</style>
