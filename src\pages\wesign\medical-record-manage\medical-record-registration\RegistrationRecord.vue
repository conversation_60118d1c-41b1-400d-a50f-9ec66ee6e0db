<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchForm
        :form-config="registrationSearchFormConfig"
        :form-state="searchFormState"
        @query-btn-click="handleQuery"
        @reset-btn-click="handleReset"
      />
    </template>

    <template #table>
      <CommonTable
        ref="commonTableRef"
        table-id="RegistrationWaitTable"
        :table-columns="registrationRecordTableColumns"
        :request-api="getRegistrationRecordListApi"
        :request-params="searchParams"
      >
        <template #inHospitalDatetime="{ row }">
          {{ formatDatetime(row.inHospitalDatetime) }}
        </template>
        <template #outHospitalDatetime="{ row }">
          {{ formatDatetime(row.outHospitalDatetime) }}
        </template>
        <template #createdDatetime="{ row }">
          {{ formatDatetime(row.createdDatetime) }}
        </template>
        <template #operation="{ row }">
          <TableButton @click="showDetailDialog(row)">详情</TableButton>
        </template>
      </CommonTable>
    </template>

    <!-- 登记详情弹窗 -->
    <DialogContainer
      v-model:visible="detailState.dialogVisible"
      title="登记详情"
      :width="800"
      no-footer
      :confirm-callback="() => (detailState.dialogVisible = false)"
    >
      <!-- 缺失登记表格 -->
      <BaseTable
        :loading="detailState.loading"
        :data="detailState.tableData"
        :columns="missingReportRecordTableColumns"
        border
      >
        <template #predictDatetime="{ row }">
          {{ formatDate(row.predictDatetime) }}
        </template>
        <template #mrClassCode="{ row }">
          {{ getRecordType(row.mrClassCode) }}
        </template>
        <template #type="{ row }">
          <span v-if="row.type === MissingReportTypeEnum.ELECTRON">电子</span>
          <span v-else-if="row.type === MissingReportTypeEnum.PAPER">纸质</span>
        </template>
      </BaseTable>
    </DialogContainer>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive } from "vue"
import { CommonTable, PageContainer, TableButton, DialogContainer, BaseTable } from "@/base-components"
import { TabsRouter, SearchForm } from "@/page-components"
import { MissingReportTypeEnum } from "@/configs"
import { useTableSearch, useCommonOptions } from "@/hooks"
import { getAllDocTypes } from "@/interfaces"
import { formatDate, formatDatetime } from "@/utils"
import {
  tabsRouterList,
  registrationSearchFormConfig,
  registrationRecordTableColumns,
  missingReportRecordTableColumns
} from "./config"
import { getRegistrationRecordListApi, getMissingReportDetailApi } from "./interface"

// 病案类型数据
const { getOptionLabel: getRecordType } = useCommonOptions({
  getOptionsApi: getAllDocTypes,
  labelAlias: "mrClassName",
  valueAlias: "mrClassCode"
})

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  patientFilterProp: "patientName",
  patientFilterValue: "",
  doctorInCharge: "",
  outHospitalDatetime: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/* ======================== 登记相关 ======================== */

interface DetailState {
  loading: boolean
  selectedRow: any
  dialogVisible: boolean
  tableData: Array<any>
}

const detailState = reactive<DetailState>({
  loading: false,
  selectedRow: undefined,
  dialogVisible: false,
  tableData: []
})

// 打开登记详情
const showDetailDialog = row => {
  detailState.dialogVisible = true
  detailState.loading = true
  detailState.selectedRow = row
  // 获取已经提交的缺失登记
  getMissingReportDetailApi(row.wsid)
    .then(res => {
      console.log(res)
      detailState.tableData = res.data.data ?? []
    })
    .finally(() => {
      detailState.loading = false
    })
}
</script>
