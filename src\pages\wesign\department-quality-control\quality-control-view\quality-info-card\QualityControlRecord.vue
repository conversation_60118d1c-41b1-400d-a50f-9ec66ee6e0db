<template>
  <div class="qx-record-container">
    <el-collapse v-model="activeCollapse">
      <!-- 关联质控意见 -->
      <template v-for="(records, recordsIndex) in relevanceList" :key="records.recordWsid">
        <el-collapse-item
          v-if="records.qcStatus !== QcStatusEnum.WAIT"
          class="relevance-container"
          :name="records.recordWsid + recordsIndex"
          :disabled="!records.comments || records.comments?.length === 0"
        >
          <template #title>
            <div class="relevance-title-container">
              <el-text class="relevance-status" :type="getQcStatusTagType(records.qcStatus)">
                {{ getQcStatus(records.qcStatus, records.qcType) }}
              </el-text>
              <OverflowTooltip class="relevance-user" :content="records.qcOperatorName"></OverflowTooltip>
              <div class="relevance-time">
                {{ formatDatetime(records.qcDatetime) }}
              </div>
              <div class="relevance-arrow">
                <i
                  v-if="records.comments && records.comments.length !== 0"
                  :class="
                    activeCollapse.includes(records.recordWsid + recordsIndex)
                      ? 'ri-arrow-up-s-line'
                      : 'ri-arrow-down-s-line'
                  "
                ></i>
              </div>
            </div>
          </template>

          <!-- 关联质控记录列表 -->
          <div class="record-list-container">
            <div v-if="records.comments && records.comments.length > 0" class="record-list">
              <el-descriptions
                v-for="item in records.comments"
                :key="item.createdDatetime"
                class="record-detail-item record-item"
                :column="1"
              >
                <el-descriptions-item label="标题:">{{ item.subject }}</el-descriptions-item>
                <el-descriptions-item label="问题类型:">{{ item.questionCategory }}</el-descriptions-item>
                <el-descriptions-item label="问题分类:">{{ item.questionType }}</el-descriptions-item>
                <el-descriptions-item label="问题描述:">{{ item.description }}</el-descriptions-item>
              </el-descriptions>
            </div>
            <el-empty v-else style="height: 100%" />
          </div>
        </el-collapse-item>
      </template>

      <!-- 当前质控意见 -->
      <el-collapse-item v-if="props.isControl" class="quality-control-record" name="current" :disabled="true">
        <template #title>
          <div class="relevance-title-container">
            <AddButton class="add-btn" @click.stop="openAddSuggest">添加质控意见</AddButton>
          </div>
        </template>

        <!-- 质控记录列表 -->
        <div v-loading="suggestListLoading" class="record-list-container">
          <div v-if="suggestList.length > 0" class="record-list">
            <div v-for="item in suggestList" :key="item.createdDatetime" class="record-item">
              <el-descriptions class="record-detail-item" :column="1">
                <el-descriptions-item label="标题:">{{ item.subject }}</el-descriptions-item>
                <el-descriptions-item label="问题类型:">{{ item.questionType }}</el-descriptions-item>
                <el-descriptions-item label="问题分类:">{{ item.questionCategory }}</el-descriptions-item>
                <el-descriptions-item label="问题描述:">{{ item.description }}</el-descriptions-item>
              </el-descriptions>
              <div class="record-item-action">
                <el-button link type="danger" @click="deleteRecord(item)">删除</el-button>
                <div class="line"></div>
                <el-button link type="primary" @click="openEditSuggest(item)">编辑</el-button>
              </div>
            </div>
          </div>
          <el-empty v-else style="height: 100%" />
        </div>
      </el-collapse-item>
    </el-collapse>

    <!-- 质控操作 -->
    <!-- <div v-if="props.isControl" class="result-actions">
      <el-button :disabled="noPassLoading" :loading="passLoading" type="success" @click="passQualityControl">
        质控通过
      </el-button>
      <el-button :disabled="passLoading" :loading="noPassLoading" type="danger" @click="noPassQualityControl">
        质控不通过
      </el-button>
    </div> -->
  </div>

  <!-- 添加/修改质控意见弹窗 -->
  <DialogContainer
    v-if="props.isControl"
    v-model:visible="addSuggestState.visible"
    title="质控意见"
    :width="520"
    :confirm-loading="addSuggestLoading"
    :confirm-callback="handleAddSuggest"
  >
    <el-form ref="formRef" label-width="90px" label-suffix=":" :model="addSuggestState.form" :rules="addRules">
      <el-form-item label="问题分类" prop="questionCategory">
        <el-select v-model="addSuggestState.form.questionCategory" @change="handleSelectQuestionCategory">
          <el-option
            v-for="item in addSuggestState.questionCategory"
            :key="item.value"
            :label="item.value"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="文书名称" prop="subject">
        <el-select v-model="addSuggestState.form.subject" filterable clearable multiple placeholder="请选择文书">
          <el-option
            v-for="item in documentBagsFiles"
            :key="item.wsid"
            :label="item.title"
            :value="item.title"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="问题类型" prop="questionType">
        <el-select v-model="addSuggestState.form.questionType">
          <el-option
            v-for="item in addSuggestState.questionType"
            :key="item.value"
            :label="item.value"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="问题描述" prop="description">
        <el-input
          v-model="addSuggestState.form.description"
          type="textarea"
          placeholder="请输入标题"
          show-word-limit
          :maxlength="300"
        />
      </el-form-item>
    </el-form>
  </DialogContainer>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, watch } from "vue"
import { useRoute, useRouter } from "vue-router"
import { cloneDeep } from "lodash-es"
import { AddButton, DialogContainer, OverflowTooltip } from "@/base-components"
import { getQcQuestionTypeList } from "../../interface"
import { QcStatusEnum, QualityControlTypeEnum } from "@/configs"
import {
  getQualityOptionRecordApi,
  addQualityControlSuggestApi,
  changeQualityControlStatusApi,
  editQualityControlSuggestApi,
  deleteRecordApi,
  getApproveReasonApi,
  getDocumentBagsFilesApi
} from "@/interfaces"
import { Message, SystemPrompt, toastError, getQcStatus, getQcStatusTagType, formatDatetime } from "@/utils"

const route = useRoute()
const router = useRouter()

const props = defineProps({
  isControl: { type: Boolean, default: false }
})
const emits = defineEmits(["refresh"])

const activeCollapse = ref(["current"])

/*==================意见列表==================*/
// 当前医疗/护理质控意见列表
const suggestListLoading = ref(false)
const suggestList = ref<Array<Record<string, any>>>([])
const passStatus = ref<QcStatusEnum>(QcStatusEnum.WAIT)
// 相关护理/医疗质控意见列表
const relevanceList = ref<Array<Record<string, any>>>([])

const passLoading = ref(false)
const noPassLoading = ref(false)
const documentBagsFiles = ref<Record<string, any>>([])
const initDocumentBagsFiles = ref<Record<string, any>>([])

onMounted(async () => {
  getQualityControlRecord()
  initDocumentBagsFiles.value = (await getDocumentBagsFilesApi(route.query.inpNo as string))?.data?.data
  documentBagsFiles.value = cloneDeep(initDocumentBagsFiles.value)
})

// 选择对应的问题分类的文书
const handleSelectQuestionCategory = val => {
  const key = addSuggestState.questionCategory.find(item => item.value === val)?.key
  // documentBagsFiles.value = initDocumentBagsFiles.value.filter(item => item.mrClassLabelKey === key)
}

function getQualityControlRecord() {
  suggestListLoading.value = true
  getQualityOptionRecordApi({ inpNo: route.query.inpNo, urlType: route.query.urlType })
    .then(res => {
      if (props.isControl) {
        suggestList.value = res.data.data.currentComments || []
        relevanceList.value = res.data.data.hisComments.filter(item => item.qcType !== route.query.urlType)
      } else {
        passStatus.value =
          res.data.data.hisComments.find(item => item.qcType === route.query.urlType)?.qcStatus || QcStatusEnum.WAIT
        relevanceList.value = res.data.data.hisComments || []
      }
    })
    .catch(err => {
      toastError(err, "获取质控意见列表失败")
    })
    .finally(() => {
      suggestListLoading.value = false
    })
}

// 通过质控
function passQualityControl() {
  SystemPrompt("是否通过质控？确认后不能修改。").then(() => {
    changeQualityControlStatus(QcStatusEnum.PASS)
  })
}

// 不通过质控
function noPassQualityControl() {
  if (suggestList.value.length === 0) return Message.error("请添加质控意见！")
  SystemPrompt("是否不通过质控？确认后不能修改。").then(() => {
    changeQualityControlStatus(QcStatusEnum.NOT_PASS)
  })
}

// 修改质控状态
function changeQualityControlStatus(status) {
  if (status === QcStatusEnum.PASS) passLoading.value = true
  else noPassLoading.value = true
  changeQualityControlStatusApi({ inpNo: route.query.inpNo as string, qcType: route.query.urlType as string, status })
    .then(res => {
      Message.success(`${status === QcStatusEnum.PASS ? "通过" : "不通过"}成功`)
      // 通过后isControl为false不可再编辑
      if (status === QcStatusEnum.PASS) passLoading.value = false
      else noPassLoading.value = false
      router.replace({ query: { ...route.query, isControl: "false" } })
      setTimeout(() => {
        window.location.reload()
      })
    })
    .catch(err => {
      if (status === QcStatusEnum.PASS) passLoading.value = false
      else noPassLoading.value = false
      toastError(err, `${status === QcStatusEnum.PASS ? "通过" : "不通过"}失败`)
    })
}
/*==================添加意见弹窗==================*/
const initAddSuggestForm = {
  id: "",
  subject: "" as any,
  // questionCategory:
  //   router.currentRoute.value.query.urlType === QualityControlTypeEnum.CROSS_QC_NURSE ||
  //   router.currentRoute.value.query.urlType === QualityControlTypeEnum.DEPT_QC_NURSE
  //     ? "护理"
  //     : "医疗",
  questionCategory: "",
  questionType: "",
  mrClassName: "",
  description: ""
}

const formRef = ref()
const addSuggestState = reactive({
  visible: false,
  type: "add" as "add" | "edit",
  form: cloneDeep(initAddSuggestForm),
  questionType: [] as Array<Record<string, any>>,
  questionCategory: [] as Array<Record<string, any>>
})

// 质控意见表单规则
const addRules = {
  subject: [
    { required: false, message: "请输入标题", trigger: "blur" }
    // { max: 50, message: "标题不能超过50个字", trigger: "blur" }
  ],
  questionCategory: [{ required: true, message: "请选择问题类型", trigger: "change" }],
  questionType: [{ required: true, message: "请选择问题分类", trigger: "change" }],
  description: [{ required: true, message: "请输入问题描述", trigger: "blur" }]
}
onMounted(() => {
  getQuestionType()
  getQuestionCategory()
})

function getQuestionType() {
  getApproveReasonApi({ groupKey: "QC_QUESTION_TYPE" })
    .then(res => {
      addSuggestState.questionType = res || []
    })
    .catch(err => {
      toastError(err, "获取问题类型失败")
    })
}

// 当前默认选中的问题分类
const selectQuestionCategory = ref<Record<string, any> | undefined>({})
function getQuestionCategory() {
  getApproveReasonApi({ groupKey: "QC_QUESTION_CATEGORY" })
    .then(res => {
      addSuggestState.questionCategory = res || []
      // 质控护士签收
      if (router.currentRoute.value.query.urlType === QualityControlTypeEnum.DEPT_QC_NURSE) {
        selectQuestionCategory.value = addSuggestState.questionCategory.find(item => item.describe === "护理")
      } else if (router.currentRoute.value.query.urlType === QualityControlTypeEnum.DEPT_QC_MEDICAL) {
        // 上级医师审核
        selectQuestionCategory.value = addSuggestState.questionCategory.find(item => item.describe === "医疗")
      }
    })
    .catch(err => {
      toastError(err, "获取问题分类失败")
    })
}

// 打开添加质控意见弹窗
function openAddSuggest() {
  addSuggestState.type = "add"
  addSuggestState.visible = true
  if (formRef.value) formRef.value.resetField()
  for (let key in addSuggestState.form) {
    addSuggestState.form[key] = initAddSuggestForm[key]
  }
  //设置默认的问题分类
  addSuggestState.form.questionCategory = selectQuestionCategory.value?.value
  const key = selectQuestionCategory.value?.key
  // documentBagsFiles.value = initDocumentBagsFiles.value.filter(item => item.mrClassLabelKey === key)
}

function openEditSuggest(row) {
  for (let key in addSuggestState.form) {
    addSuggestState.form[key] = row[key]
  }
  addSuggestState.form.subject = row.subject ? row.subject.split(",") : ""
  addSuggestState.type = "edit"
  addSuggestState.visible = true
  if (formRef.value) formRef.value.resetField()
}

watch(
  () => addSuggestState.form.questionType,
  val => {
    if (!val) addSuggestState.form.mrClassName = ""
    else {
      const questionTypeItem = addSuggestState.questionType.find(item => item.questionType === val)
      addSuggestState.form.mrClassName = questionTypeItem?.mrClassName || ""
    }
  },
  { immediate: true }
)

const addSuggestLoading = ref(false)
// 添加/编辑质控意见
function handleAddSuggest() {
  if (!formRef.value) return
  formRef.value.validate(valid => {
    if (!valid) return
    const form = cloneDeep(addSuggestState.form)
    form.subject = form.subject.join(",")
    const data = { inpNo: route.query.inpNo, urlType: route.query.urlType, ...form }

    addSuggestLoading.value = true
    if (addSuggestState.type === "add")
      addQualityControlSuggestApi(data)
        .then(res => {
          if (res.data) {
            Message.success("添加成功")
            getQualityControlRecord()
            emits("refresh")
            addSuggestState.visible = false
          } else Message.error(res.data.message || "添加失败")
          addSuggestLoading.value = false
        })
        .catch(err => {
          addSuggestLoading.value = false
          toastError(err, "添加失败")
        })
    else if (addSuggestState.type === "edit")
      editQualityControlSuggestApi(data)
        .then(res => {
          if (res.data) {
            Message.success("添加成功")
            getQualityControlRecord()
            emits("refresh")
            addSuggestState.visible = false
          } else Message.error(res.data.message || "添加失败")
          addSuggestLoading.value = false
        })
        .catch(err => {
          addSuggestLoading.value = false
          toastError(err, "添加失败")
        })
  })
}

function deleteRecord(row) {
  SystemPrompt(`请确认是否要删除“${row.subject}”？`).then(res => {
    deleteRecordApi({ inpNo: route.query.inpNo, urlType: route.query.urlType, id: row.id })
      .then(res => {
        Message.success("删除成功")
        getQualityControlRecord()
        emits("refresh")
      })
      .catch(err => {
        toastError(err, "删除失败")
      })
  })
}

defineExpose({
  noPassQualityControl,
  passQualityControl,
  openAddSuggest
})
</script>

<style lang="less" scoped>
.qx-record-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  padding: 20px;
  box-sizing: border-box;
  :deep(.el-collapse) {
    height: 90%;
    display: flex;
    flex-direction: column;
    .is-disabled {
      .el-collapse-item__header {
        cursor: default;
      }
    }
    .el-collapse-item__header {
      .el-collapse-item__arrow {
        display: none;
      }
    }

    .el-collapse-item__content {
      height: 100%;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
    }

    .quality-control-record {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;

      .add-btn {
        // width: fit-content;
        width: 100%;
        margin: 8px 0px;
      }
    }
    .relevance-title-container {
      display: flex;
      align-items: center;
      width: 100%;
      justify-content: space-between;
      color: #303133;
      .relevance-status {
        flex-shrink: 0;
        white-space: nowrap;
        width: 110px;
        text-align: left;
      }
      .relevance-user {
        flex: 1;
        min-width: 0px;
        margin: 0px 6px;
      }
      .relevance-time {
        flex-shrink: 0;
        white-space: nowrap;
      }
      .relevance-arrow {
        width: 15px;
        text-align: right;
      }
    }
    .record-list-container {
      flex: 1;
      min-height: 0px;

      .record-list {
        height: 100%;
        overflow: auto;
        padding-right: 10px;
        .record-item {
          border: 1px solid #e2e3eb;
          margin-bottom: 16px;
        }
        .record-item-action {
          height: 30px;
          display: flex;
          justify-content: space-evenly;
          border-top: 1px solid #e2e3eb;

          .line {
            height: 100%;
            width: 1px;
            background-color: #e2e3eb;
          }
        }
        .record-detail-item {
          padding: 16px;
          &:not(:last-child) {
            // border-bottom: 1px solid rgba(0, 0, 0, 0.1);
          }
        }
      }
    }
  }

  .result-actions {
    display: flex;
    justify-content: space-evenly;
    margin-top: 10px;
  }

  :deep(.el-descriptions__cell) {
    padding: 0px;
    display: flex;
    .el-descriptions__label {
      width: 60px;
      text-align: right;
      font-weight: 500;
      font-size: 14px;
      color: rgba(10, 22, 51, 0.6) !important;
    }
    .el-descriptions__content {
      flex: 1;
      min-width: 0px;
      font-weight: 500;
      font-size: 14px;
      color: #0a1633 !important;
    }
  }
}
:deep(.el-collapse-item__wrap) {
  height: 100% !important;
  border: none !important;
}
</style>
