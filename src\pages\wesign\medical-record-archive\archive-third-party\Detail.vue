<template>
  <div id="medical-record-detail">
    <!-- 左侧文件列表（树形结构） -->
    <div v-loading="state.loading" class="view-left">
      <MedicalRecordTree
        ref="recordTreeRef"
        can-collapse
        :base-info="state.baseInfo"
        :tree-info="state.treeInfo"
        @click-pdf="handlePdfClick"
      />
    </div>

    <div class="view-right">
      <!-- 顶部信息及按钮 -->
      <view class="tool-bar">
        <view class="document-name">{{ state.documentName }}</view>

        <view class="tool-bar-buttons">
          <el-button @click="toPrev">上一份文件</el-button>
          <el-button @click="toNext">下一份文件</el-button>
        </view>
      </view>

      <!-- pdf -->
      <div class="view-middle common-box-shadow">
        <PdfObjectPreview
          ref="pdfPreviewRef"
          :src="state.pdfSrc"
          :medical-location="state.baseInfo?.documentStorageLocation"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, ref, computed } from "vue"
import { useRoute } from "vue-router"
import { debounce } from "lodash-es"
import { PdfObjectPreview } from "@/base-components"
import { MedicalRecordTree, DocumentMeta } from "@/page-components"
import { getThirdPartyArchiveDetailApi } from "./interface"

const route = useRoute()

onMounted(async () => {
  await getData()
  registerKeyboardEvent()
})

const getData = async () => {
  const inpNo = route.query.inpNo as string
  state.loading = true
  await getThirdPartyArchiveDetailApi(inpNo).then(res => {
    if (res.data.code !== "100100000") return
    const recordDetail = res.data.data
    state.baseInfo = recordDetail.baseInfo || {}
    state.firstPageFields = recordDetail.firstPageFields
    state.treeInfo = recordDetail.treeInfo
    state.loading = false
  })
}

const state = reactive({
  loading: false,
  baseInfo: {} as Record<string, any>, // 患者基础信息
  pdfSrc: "",
  targetFileWsid: "",
  firstPageFields: [] as Array<any>, // 首页字段
  treeInfo: {} as { mrClassTree: any[]; noCatalogDocuments: any[] }, // 左侧tree需要的原始数据
  documentName: "", // 当前文档名称
  documentWsid: "" as string // 当前点击的文档wsid
})

// 切换pdf源
const handlePdfClick = node => {
  console.log(node)
  let fileUrl = node?.contentFilePath
  state.pdfSrc = "/api" + fileUrl
  state.targetFileWsid = node?.fileWsid
  state.documentName = node?.title
  state.documentWsid = node?.wsid
}

/* =============== toolbar =============== */

const recordTreeRef = ref<InstanceType<typeof MedicalRecordTree>>()
const documentNodeList = computed(() => recordTreeRef.value?.state.documentNodeList)

const registerKeyboardEvent = () => {
  document.onkeyup = e => {
    const keyCode = e.code
    // 左/上键
    if (keyCode === "ArrowLeft" || keyCode === "ArrowUp") {
      toPrev()
    }
    // 右/下键
    else if (keyCode === "ArrowRight" || keyCode === "ArrowDown") {
      toNext()
    }
  }
}

const toPrev = debounce(() => {
  if (!documentNodeList.value) return
  const index = documentNodeList.value.findIndex(item => item.wsid === state.documentWsid) || 0
  if (index <= 0) return
  const targetNode = documentNodeList.value[index - 1]
  handlePdfClick(targetNode)
  recordTreeRef.value?.setCurrentKey(targetNode.wsid)
}, 300)

const toNext = debounce(() => {
  if (!documentNodeList.value) return
  const index = documentNodeList.value.findIndex(item => item.wsid === state.documentWsid)
  if (index >= documentNodeList.value.length - 1) return
  const targetNode = documentNodeList.value[index + 1]
  handlePdfClick(targetNode)
  recordTreeRef.value?.setCurrentKey(targetNode.wsid)
}, 300)
</script>

<style lang="less" scoped>
#medical-record-detail {
  display: flex;
  justify-content: space-between;
  height: 100%;

  .view-middle {
    overflow-y: auto;
    height: calc(100% - 60px);
    min-width: 400px;
    flex: 1;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
  }
}

.tool-bar {
  background: #fff;
  border-radius: 8px;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 40px;

  .document-name {
    font-size: 16px;
    font-weight: 600;
  }
}

.view-right {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
}
</style>
