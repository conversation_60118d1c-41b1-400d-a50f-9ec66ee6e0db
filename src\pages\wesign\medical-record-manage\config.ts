import type { TableColumnItem, BaseOptionItem } from "@/types"

// 缺失登记表格
export const missingReportTableColumns: Array<TableColumnItem> = [
  { type: "index", label: "序号", width: 60 },
  { prop: "predictDatetime", label: "预计补交日期", minWidth: 110 },
  { prop: "mrClassName", label: "文书分类", minWidth: 90 },
  { prop: "type", label: "报告类型", width: 90 },
  { prop: "name", label: "报告名称", width: 110 },
  { prop: "reason", label: "缺失原因", width: 150 }
]

// 签名验证枚举
export enum verifyStatusEnum {
  INFO = "INFO", // 验证通过
  WARN = "WARN", // 存在风险
  ERROR = "ERROR" // 验证失败
}

// 完整性校验枚举
export enum integralityEnum {
  NO_GATHER = "NO_GATHER",
  GATHER_HAVE_RULE = "GATHER_HAVE_RULE",
  GATHER_NO_RULE = "GATHER_NO_RULE"
}

export type AllowMissingType = "YES" | "NO"

export const beforeDaysOptions: Array<BaseOptionItem> = [
  { label: "近7天", value: "7" },
  { label: "近14天", value: "14" },
  { label: "近1月", value: "30" },
  { label: "近3月", value: "90" },
  { label: "近6月", value: "180" }
]
