<template>
  <PageContainer separate>
    <template #search>
      <SearchForm
        :form-config="patientSearchFormConfig"
        :form-state="searchForm"
        @query-btn-click="handleQuery"
        @reset-btn-click="handleReset"
      />
    </template>

    <template #table>
      <CommonTable
        v-if="displayMode === 'table'"
        ref="patientTableRef"
        table-id="patientTable"
        :table-columns="patientTableColumns"
        :request-params="searchParams"
        :request-api="getPatientListApi"
        :data-callback="dataCallback"
      >
        <template #header>
          <TooltipButton
            tooltip="切换为卡片展示模式"
            :icon="h('i', { class: 'ri-function-line', style: { 'font-size': '18px' } })"
            @click="toggleDisplayMode"
          ></TooltipButton>
        </template>
        <template #operation="{ row }">
          <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.View)" @click="handleRowClick(row)">
            查看
          </TableButton>
          <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.Sync)" @click="handleSync(row)">
            同步
          </TableButton>
          <!-- <TableButton>补录</TableButton> -->
        </template>
      </CommonTable>

      <PatientCardList
        v-else
        ref="patientCardListRef"
        :request-params="searchParams"
        :request-api="getPatientListApi"
        @to-detail="handleRowClick"
        @handle-sync="handleSync"
      >
        <template #header>
          <TooltipButton
            tooltip="切换为表格展示模式"
            :icon="h('i', { class: 'ri-list-check', style: { 'font-size': '18px' } })"
            @click="toggleDisplayMode"
          ></TooltipButton>
        </template>
      </PatientCardList>
    </template>

    <SealDialog ref="sealDialogRef" :selected-row="medicalRecord" :confirm-callback="confirmOperation" />

    <SyncDialog ref="syncDialogRef" :inp-no="medicalRecord?.inpNo" @success="handleRefresh" />
  </PageContainer>
</template>

<script lang="ts" setup>
import { ref, reactive, h, nextTick } from "vue"
import { useRouter } from "vue-router"
import { TooltipButton, CommonTable, TableButton, PageContainer } from "@/base-components"
import { SealDialog, SearchForm, SyncDialog } from "@/page-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { syncRecordApi } from "@/interfaces"
import { useUserStore } from "@/stores"
import { formatDatetime, Message, toastError, encryptStr } from "@/utils"

import { getPatientListApi } from "../interface"
import PatientCardList from "./components/PatientCardList.vue"
import { patientSearchFormConfig, patientTableColumns } from "./config"

const menuId = "/medical-record/patient-query"

const { hasOperationPermission } = useUserStore()

const router = useRouter()
const patientTableRef = ref()
const patientCardListRef = ref()

const medicalRecord = ref<Record<string, any> | null>(null) // 用户点击的病案

/*===================== 查询 =====================*/

const searchForm = reactive({
  patientFilterProp: "",
  patientFilterValue: "",
  visitIdSymbol: "",
  visitIdCount: "",
  inHospitalDeptWsid: "",
  inHospitalDatetime: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchForm)

/*===================== 表格 =====================*/

// 格式化字段
const dataCallback = (data: Array<Record<string, any>>) => {
  data.forEach(item => {
    item.inHospitalDatetime = formatDatetime(item.inHospitalDatetime)
    item.outHospitalDatetime = formatDatetime(item.outHospitalDatetime)
  })
  return data.map(item => ({
    ...item
  }))
}

/*===================== 表格操作 =====================*/

const syncDialogRef = ref<InstanceType<typeof SyncDialog>>()

// 同步
const handleSync = row => {
  medicalRecord.value = row
  nextTick(() => syncDialogRef.value?.show())
}

/* ======================== 封存弹窗相关 ======================== */

const sealDialogRef = ref() // SealDialog组件ref

const handleRowClick = row => {
  medicalRecord.value = row
  sealDialogRef.value.checkSealed()
}

// 检查封存之后的回调
const confirmOperation = () => {
  const query = { inpNo: medicalRecord.value?.inpNo }
  if (sealDialogRef.value.secretKey) {
    query["secretKey"] = encryptStr(sealDialogRef.value.secretKey)
  }
  router.push({
    path: "/medical-record/detail",
    query: query
  })
}

/*===================== 表格展示方式 =====================*/

const displayMode = ref<"table" | "card">("table")

// 切换数据展示方式
const toggleDisplayMode = () => {
  displayMode.value = displayMode.value === "table" ? "card" : "table"
}

/*===================== 刷新 =====================*/

const handleRefresh = () => {
  patientCardListRef.value?.getDataList()
  patientTableRef.value?.refreshTableData()
}
</script>
