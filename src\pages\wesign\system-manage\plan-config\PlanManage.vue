<template>
  <PageContainer separate class="plan-config-container">
    <!-- 头部搜索 -->
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="clickHandleResetClick">
        <el-form-item label="方案名称">
          <el-input v-model.trim="searchFilterForm.schemeName" placeholder="请输入方案名称" clearable></el-input>
        </el-form-item>
      </SearchContainer>
    </template>

    <template #table>
      <div class="plan-manage-content">
        <!-- 左侧方案分类列表 -->
        <PageSideList
          ref="pageSideListRef"
          title="方案分类"
          :hide-add="true"
          :list-data="planClassifyState.list"
          :loading="planClassifyState.loading"
          @click-add="clickAddClassify"
          @click-edit="clickEditClassify"
          @click-delete="clickDeleteClassify"
          @change-active="clickCategory"
        />
        <!-- 方案列表 -->
        <CardContainer title="方案列表">
          <CommonTable
            ref="commonTableRef"
            :table-columns="planManageTableColumns"
            :request-api="getSchemesListApi"
            :request-params="searchParams"
            :border="false"
          >
            <template #header>
              <AddButton @click="handleAddBtn()">新增</AddButton>
            </template>
            <template #operation="{ row }">
              <TableButton @click="handleEdit(row)">编辑</TableButton>
              <TableButton @click="handleRelevanceBtn(row)">查看</TableButton>
              <TableButton v-if="row.isCommon !== 1" @click="handleDelete(row)">删除</TableButton>
            </template>
          </CommonTable>
        </CardContainer>
      </div>
    </template>
  </PageContainer>

  <!-- 新增/编辑方案弹窗 -->
  <DialogContainer
    v-model:visible="dialogVisible"
    :title="schemaState.dialogTitle"
    :width="500"
    :confirm-loading="schemaState.submitLoading"
    :confirm-callback="schemaConfirmBack"
  >
    <div v-loading="schemaState.loading" class="plan-change-from">
      <el-form ref="planFormRef" label-width="80px" :model="schemaState.dialogFormData" :rules="schemaFormRules">
        <CommonInputFormItem v-model="schemaState.dialogFormData.schemeName" label="方案名称" prop="schemeName" />

        <CommonInputFormItem
          v-model="schemaState.dialogFormData.schemeCode"
          label="方案代码"
          prop="schemeCode"
          :disabled="schemaState.dialogTitle === '编辑方案' && isCommon === 1"
        />

        <el-form-item label="方案类型" prop="categoryWsid">
          <el-select v-model="schemaState.dialogFormData.categoryWsid" disabled placeholder="请选择方案代码">
            <el-option
              v-for="item in planClassifyState.list"
              :key="item.wsid"
              :label="item.typeName"
              :value="item.wsid"
            />
          </el-select>
        </el-form-item>

        <CommonInputFormItem
          v-model="schemaState.dialogFormData.describe"
          label="描述"
          prop="describe"
          :disabled="schemaState.dialogTitle === '编辑方案' && isCommon === 1"
        />

        <el-form-item
          v-if="schemaState.type !== 'TYPE_PRINT' && schemaState.type !== 'TYPE_HQMS'"
          label="应用科室"
          required
        >
          <el-select
            v-if="isCommon !== 1"
            v-model="schemaState.schemesInfo.depts"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            :max-collapse-tags="3"
            placeholder="请选择应用科室"
            placement="top"
          >
            <template #header>
              <el-input v-model="schemaState.searchDept" placeholder="请输入科室名称" clearable />
            </template>
            <el-option v-for="item in filterChoiceDept" :key="item.key" :label="item.label" :value="item.key" />
          </el-select>
          <div v-else>全院</div>
        </el-form-item>
      </el-form>
    </div>
  </DialogContainer>

  <!-- 查看树形结构弹窗 -->
  <el-drawer
    v-model="relevanceState.dialogVisible"
    :title="relevanceState.schemeName"
    direction="rtl"
    close-on-press-escape
    @close="closeDrawer"
  >
    <!-- 编辑 -->
    <template v-if="relevanceState.addCatalogueDialog">
      <!-- 树节点 过滤 -->
      <div style="display: flex; align-items: center; justify-content: space-between">
        <el-input v-model="filterText" :prefix-icon="Search" placeholder="目录名称" />
      </div>
      <div class="tree-header">
        <div class="tree-title">
          <span>目录名称</span>
          <span>目录代码</span>
        </div>
        <span class="check-all-container">
          <span>全选</span>
          <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange"></el-checkbox>
        </span>
      </div>
      <CommonTree
        ref="selectTreeRef"
        style-type="table"
        :data="relevanceState.catalogueTree"
        :checked-keys="relevanceState.selectWsid"
        :filter-node-keyword="filterText"
        :default-expand-all="false"
        :show-checkbox="true"
        :draggable="schemaState.type === 'TYPE_HQMS'"
        :allow-drop="allowDrop"
        :allow-drag="allowDrag"
      >
        <template #title="{ node, data }">
          <span class="custom-tree-node">
            <span>
              <el-tooltip class="relevance-icon-tooltip" effect="dark" content="拖拽顺序" placement="top-end">
                <i v-if="schemaState.type === 'TYPE_HQMS'" class="icon-drag" style="margin-right: 4px"></i>
              </el-tooltip>

              <i v-if="data.type === 'MR_CLASS'" class="ri-file-text-line" style="margin-right: 4px"></i>
              <span class="label">{{ node.label }}</span>
            </span>
            <span v-if="data.code">{{ data.code }}</span>
          </span>
        </template>
      </CommonTree>
      <div class="foot">
        <el-button @click="relevanceClosedBack">取消</el-button>
        <el-button type="primary" style="margin-left: 16px" @click="catalogueConfirmBack">确定</el-button>
      </div>
    </template>

    <!-- 查看 -->
    <div v-else v-loading="relevanceState.loading">
      <template v-if="relevanceState.selectWsid.length">
        <!-- 树节点 过滤 -->
        <div style="display: flex; align-items: center; justify-content: space-between">
          <el-input v-model="filterText" :prefix-icon="Search" placeholder="目录名称" style="width: 70%" />
          <el-button type="primary" @click="addCatalogue">编辑目录</el-button>
        </div>
        <div class="tree-header">
          <div class="tree-title">
            <span>目录名称</span>
            <span>目录代码</span>
          </div>
        </div>
        <!-- 隐藏的tree，用于筛选半勾选和勾选的数据 -->
        <CommonTree
          v-show="false"
          ref="hideTreeRef"
          style-type="table"
          :data="relevanceState.catalogueTree"
          :checked-keys="relevanceState.selectWsid"
          :filter-node-keyword="filterText"
          :default-expand-all="false"
          :show-checkbox="true"
        ></CommonTree>
        <CommonTree
          ref="commonTreeRef"
          style-type="table"
          :data="relevanceState.viewData"
          :filter-node-keyword="filterText"
          :default-expand-all="false"
        >
          <template #title="{ node, data }">
            <span class="custom-tree-node">
              <span>
                <i v-if="data.type === 'MR_CLASS'" class="ri-file-text-line" style="margin-right: 4px"></i>
                <span class="label">{{ node.label }}</span>
              </span>
              <span v-if="data.code">{{ data.code }}</span>
            </span>
          </template>
        </CommonTree>
      </template>
      <div v-else class="blank-container">
        暂无关联方案目录，
        <text @click="addCatalogue">点击新增</text>
      </div>
    </div>
  </el-drawer>

  <!-- 编辑分类弹窗 -->
  <DialogContainer
    v-model:visible="planClassifyState.dialogVisible"
    :title="planClassifyState.dialogTitle"
    :confirm-loading="planClassifyState.dialogLoading"
    :confirm-callback="classifyConfirmBack"
  >
    <el-form ref="editClassifyFormRef" :model="planClassifyState.dialogFormData">
      <CommonInputFormItem
        v-model="planClassifyState.dialogFormData.schemeCategoryName"
        label="分类名称"
        prop="schemeCategoryName"
      />

      <CommonInputFormItem
        v-model="planClassifyState.dialogFormData.schemeCategoryCode"
        label="分类代码"
        prop="schemeCategoryCode"
        :disabled="planClassifyState.dialogTitle === '编辑分类'"
      />

      <el-form-item label="所属类型" prop="schemeCategoryType">
        <el-select
          v-model="planClassifyState.dialogFormData.schemeCategoryType"
          placeholder="请选择所属类型"
          :disabled="planClassifyState.dialogTitle === '编辑分类'"
        >
          <el-option
            v-for="item in planClassifyState.list"
            :key="item.wsid"
            :label="item.typeName"
            :value="item.wsid"
          />
        </el-select>
      </el-form-item>
    </el-form>
  </DialogContainer>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, watch } from "vue"
import { cloneDeep } from "lodash-es"
import { Search } from "@element-plus/icons-vue"
import {
  SearchContainer,
  CommonTable,
  CardContainer,
  AddButton,
  TableButton,
  DialogContainer,
  PageContainer,
  CommonInputFormItem,
  CommonTree
} from "@/base-components"
import { TabsRouter, PageSideList } from "@/page-components"
import { useTableSearch } from "@/hooks"
import { useGlobalOptionsStore } from "@/stores"
import { CollectTypeItem } from "@/types"
import { Message, SystemPrompt, toastError, formatRecordDocumentTree, hasChildren, flattenTree } from "@/utils"
import { tabsRouterList, planManageTableColumns, schemaFormRules, catalogColumns } from "./config"
import {
  getPlanManageApi,
  getSchemesInfoApi,
  getChoiceDeptApi,
  editClassifyApi,
  addClassifyApi,
  deleteClassifyApi,
  getSchemesListApi,
  addSchemesApi,
  editSchemesApi,
  deleteSchemesApi,
  getSchemeRelationApi,
  changeSchemesCataloguesApi,
  changeRelevanceInfoApi,
  getRelevanceInfoApi,
  getSchemesCataloguesApi
} from "./interface"
import type Node from "element-plus/es/components/tree/src/model/node"
import type { AllowDropType } from "element-plus/es/components/tree/src/tree.type"

const globalOptionsStore = useGlobalOptionsStore()

onMounted(async () => {
  await refreshClassifyList()
  // 进入页面默认选中第一个
  pageSideListRef.value?.changeActive(planClassifyState.list[0].wsid, "", planClassifyState.list[0])
})

/* ======================== 搜索相关数据及方法 ======================== */

const searchFilterForm = reactive({
  schemeName: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFilterForm)

// 重置
const clickHandleResetClick = () => {
  handleReset()
  planClassifyState.showCategoryWsid = ""
  pageSideListRef.value?.reset()
}

/* ======================== 新增/编辑方案弹窗 ======================== */
const planFormRef = ref()
const commonTableRef = ref<InstanceType<typeof CommonTable>>()

const initDialogFormData = {
  schemeName: "",
  schemeCode: "",
  describe: "",
  categoryWsid: ""
}

const dialogVisible = ref(false)
const schemaState = reactive({
  loading: false,
  submitLoading: false,
  schemesInfo: {
    depts: []
  },
  choiceDept: [] as Array<Record<string, any>>,
  searchDept: "",
  dialogTitle: "",
  dialogFormData: { ...initDialogFormData },
  editCategoryWsid: "",
  editSchemeWsid: "",
  type: ""
})

const filterChoiceDept = computed(() => {
  return schemaState.choiceDept.filter(item => item.label.includes(schemaState.searchDept))
})

const isCommon = ref(0)

// 打开新增方案弹窗
const handleAddBtn = async () => {
  isCommon.value = 0
  if (!planClassifyState.showCategoryWsid) {
    return Message.error("请先选择左侧方案分类后再进行添加")
  }
  if (planFormRef.value) planFormRef.value.resetFields()
  schemaState.dialogTitle = "新增方案"
  schemaState.dialogFormData = { ...initDialogFormData }
  schemaState.schemesInfo.depts = []

  // 设置默认的方案类型
  schemaState.dialogFormData.categoryWsid = planClassifyState.showCategoryWsid

  await getChoiceDept("")

  dialogVisible.value = true
}

// 打开编辑弹窗
const handleEdit = async row => {
  isCommon.value = row.isCommon
  if (!planClassifyState.showCategoryWsid) {
    return Message.error("请先选择左侧方案分类后再进行编辑")
  }

  dialogVisible.value = true
  schemaState.loading = true
  schemaState.dialogTitle = "编辑方案"
  schemaState.editCategoryWsid = row.categoryWsid
  schemaState.editSchemeWsid = row.wsid
  try {
    await getSchemesDeptInfo({ schemeWsid: row.wsid })
  } catch (error) {
    console.log("error:", error)
  }
}

// 确认新增/编辑方案
const schemaConfirmBack = async () => {
  if (!planFormRef.value) return
  planFormRef.value.validate(async valid => {
    if (!valid) return
    schemaState.submitLoading = true
    try {
      const params = {
        ...schemaState.dialogFormData,
        depts: schemaState.schemesInfo.depts,
        schemeWsid: ""
      }
      let res
      if (schemaState.dialogTitle === "新增方案") {
        params.categoryWsid = schemaState.editCategoryWsid
        res = await addSchemesApi(params)
      } else {
        params.schemeWsid = schemaState.editSchemeWsid
        res = await editSchemesApi(params)
      }

      if (res.data.data && typeof res.data.data === "boolean") {
        dialogVisible.value = false
        Message.success("操作成功！")
        commonTableRef.value?.refreshTableData()
      }
    } catch (error: any) {
      toastError(error)
    } finally {
      schemaState.submitLoading = false
    }
  })
}

// 获取可用科室
async function getChoiceDept(schemeWsid) {
  try {
    // 获取可用科室列表
    const choiceDeptRes = await getChoiceDeptApi({
      schemeWsid,
      schemeCategoryWsid: planClassifyState.showCategoryWsid
    })
    const resData = choiceDeptRes?.data?.data || []
    resData.forEach(item => {
      item.label = item.deptName
      item.key = item.wsid
    })
    schemaState.choiceDept = choiceDeptRes?.data?.data ?? []
  } catch (error: any) {
    toastError(error, "获取可用科室失败")
  }
}

// 获取方案基本信息
const getSchemesDeptInfo = async (row: { schemeWsid: string } = { schemeWsid: "" }) => {
  const schemeWsid = row.schemeWsid ?? ""
  try {
    // 获取可用科室列表
    await getChoiceDept(schemeWsid)

    // 获取该条数据的基本信息
    const schemesInfoRes = await getSchemesInfoApi({ schemeWsid })
    schemaState.schemesInfo = schemesInfoRes.data.data ?? {}
    const depts = schemesInfoRes.data?.data?.depts || []

    // 是否存在不在可选列表里的已选数据
    const isNotExit = depts.filter(item => {
      return schemaState.choiceDept.findIndex(dept => dept.wsid === item) === -1
    })
    // 存在则推入可选科室
    if (isNotExit.length > 0)
      isNotExit.forEach(item => {
        const deptItem = globalOptionsStore.departmentOptions.find(dept => dept.value === item)
        if (deptItem) schemaState.choiceDept.push(deptItem)
      })

    schemaState.dialogFormData.categoryWsid = schemesInfoRes.data.data.categoryWsid
    schemaState.dialogFormData.describe = schemesInfoRes.data.data.describe
    schemaState.dialogFormData.schemeCode = schemesInfoRes.data.data.schemeCode
    schemaState.dialogFormData.schemeName = schemesInfoRes.data.data.schemeName
  } catch (error: any) {
    toastError(error, "获取信息失败")
  }
  schemaState.loading = false
}
/* ======================== 删除方案 ======================== */
// 删除
function handleDelete(row) {
  SystemPrompt(`您正在删除名称为“${row.schemeName}”的方案，确定删除？`).then(() => {
    deleteSchemesApi({ schemeWsid: row.wsid }).then(() => {
      Message.success("操作成功！")
      commonTableRef.value?.refreshTableData()
    })
  })
}

/* ======================== 选择应用科室 ======================== */

// 筛选科室
const filterDept = (query, item) => {
  return item.label.includes(query)
}

/* ======================== 方案分类 ======================== */
const pageSideListRef = ref()

interface PlanListType extends CollectTypeItem {
  schemeCategoryName: string
  wsid: string
  typeName: string
}
//方案分类
const planClassifyState = reactive({
  list: [] as Array<PlanListType>,
  dialogVisible: false,
  dialogFormData: {
    schemeCategoryName: "",
    schemeCategoryCode: "",
    schemeCategoryType: ""
  },
  targetWsid: "",
  loading: false,
  dialogLoading: false,
  dialogTitle: "",
  showCategoryWsid: ""
})

//获取分类列表
const refreshClassifyList = async () => {
  planClassifyState.loading = true
  try {
    const res = await getPlanManageApi()
    planClassifyState.list = res.data.data ?? []
    planClassifyState.list.forEach(item => {
      item.name = item.schemeCategoryName
      item.id = item.wsid
    })
  } catch (error: any) {
    toastError(error, "获取分类列表失败")
  }
  planClassifyState.loading = false
}

//选择展示分类
const clickCategory = (wsid, code, item) => {
  console.log(`output->item`, item)
  planClassifyState.showCategoryWsid = wsid
  searchParams.filters = `categoryWsid=${wsid}`
  schemaState.editCategoryWsid = wsid
  schemaState.type = item?.type
}

// 分类删除
const clickDeleteClassify = item => {
  SystemPrompt(`您正在删除名称为“${item.schemeCategoryName}”的分类，确定删除？`, "error").then(() => {
    deleteClassifyApi({ schemeCategoryWsid: item.wsid })
      .then(res => {
        if (res.data.data && typeof res.data.data === "boolean") {
          Message.success("操作成功")
          clickHandleResetClick()
        } else Message.error("操作失败")
        refreshClassifyList()
      })
      .catch(err => toastError(err))
  })
}

/* ======================== 新增/编辑方案分类弹窗 ======================== */
//打开编辑分类弹窗
const clickEditClassify = classify => {
  planClassifyState.dialogTitle = "编辑分类"
  planClassifyState.dialogVisible = true

  planClassifyState.dialogFormData.schemeCategoryName = classify.schemeCategoryName
  planClassifyState.dialogFormData.schemeCategoryCode = classify.schemeCategoryCode
  planClassifyState.dialogFormData.schemeCategoryType = classify.wsid

  planClassifyState.targetWsid = classify.wsid
}

// 分类新增
const clickAddClassify = () => {
  planClassifyState.dialogTitle = "新增分类"
  planClassifyState.dialogFormData.schemeCategoryCode = ""
  planClassifyState.dialogFormData.schemeCategoryName = ""
  planClassifyState.dialogFormData.schemeCategoryType = ""
  planClassifyState.dialogVisible = true
}

// 确认新增/编辑分类
const classifyConfirmBack = async () => {
  planClassifyState.dialogLoading = true
  const data = { ...planClassifyState.dialogFormData }
  try {
    let res
    if (planClassifyState.dialogTitle === "新增分类") {
      res = await addClassifyApi({ ...data })
    } else {
      res = await editClassifyApi({ schemeCategoryWsid: planClassifyState.targetWsid, ...data })
    }
    if (res.data.data && typeof res.data.data === "boolean") Message.success("操作成功")
    else Message.error("操作失败")
    planClassifyState.dialogVisible = false
    refreshClassifyList()
  } catch (error: any) {
    toastError(error)
  }
  planClassifyState.dialogLoading = false
}

/* ======================== 查看 目录抽屉 ======================== */

const filterText = ref("")

const relevanceState = reactive({
  dialogVisible: false,
  inputValue: "",
  loading: false,
  catalogueTree: [] as any,
  addCatalogueDialog: false,
  showSchemeWsid: "",
  isEdit: false,
  selectWsid: [] as any,
  viewData: [] as any,
  schemeName: "",
  schemeCode: ""
})

const commonTreeRef = ref()
const hideTreeRef = ref()

// 关闭抽屉
const closeDrawer = () => {
  relevanceState.addCatalogueDialog = false
  // 重置hidden-tree的勾选数据
  relevanceState.selectWsid = []
}

// 所有勾选的节点，包括半勾选
let allCheckedNodes = []

// 获取关联数据 点击查看加载row的详情
const getRelevanceInfo = async schemeWsid => {
  relevanceState.loading = true
  try {
    const res = await getSchemesCataloguesApi({ schemeWsid: schemeWsid })
    const { defaultCatalogues = "", selectWsid = "", activeCatalogues } = res.data.data
    const catalogueTree = relevanceState.schemeCode === "HQMS" ? activeCatalogues : defaultCatalogues
    // 所有目录结构(hqms方案的使用activeCatalogues字段)
    relevanceState.catalogueTree = formatRecordDocumentTree(catalogueTree)
    // 已选择的目录wsid列表
    relevanceState.selectWsid = selectWsid
  } catch (error: any) {
    toastError(error)
  }
  relevanceState.loading = false
  allCheckedNodes = hideTreeRef.value
    ?.getHalfCheckedNodes()
    ?.map(item => item.wsid)
    .concat(relevanceState.selectWsid)
  relevanceState.viewData = allCheckedNodes ? buildData(cloneDeep(relevanceState.catalogueTree)) : []
}

// 查看
const handleRelevanceBtn = async row => {
  relevanceState.dialogVisible = true
  relevanceState.viewData = []
  relevanceState.showSchemeWsid = row.wsid
  relevanceState.schemeName = row.schemeName
  relevanceState.schemeCode = row.schemeCode
  await getRelevanceInfo(row.wsid)
}

// 取消 返回查看
const relevanceClosedBack = async () => {
  relevanceState.addCatalogueDialog = false
  await getRelevanceInfo(relevanceState.showSchemeWsid)
}

// 构建展示的目录树形结构
const buildData = (treeData: Array<Record<string, any>>) => {
  if (!treeData.length) return []
  treeData = treeData.filter(item => allCheckedNodes.indexOf(item.wsid) !== -1)
  treeData.forEach(tree => {
    if (tree.children?.length) {
      tree.children = tree.children.filter(item => allCheckedNodes.indexOf(item.wsid) !== -1)
      return buildData(tree.children)
    }
  })
  return treeData
}

// 监听hideTree挂载，重新构建树形结构数据
watch(
  () => hideTreeRef.value,
  async val => {
    if (val) {
      await getRelevanceInfo(relevanceState.showSchemeWsid)
    }
  },
  { immediate: true, deep: true }
)

/* ======================== 编辑目录 ======================== */

const selectTreeRef = ref()
const activeCatalogues = ref<Array<any>>([])

// 编辑目录
const addCatalogue = () => {
  relevanceState.addCatalogueDialog = true
}

// 确认新增目录
const catalogueConfirmBack = async () => {
  const selectData = selectTreeRef.value?.getCheckedNodes(false, false).map(item => item.wsid)
  try {
    const res = await changeSchemesCataloguesApi({
      schemeWsid: relevanceState.showSchemeWsid,
      selectWsid: selectData ?? [],
      activeCatalogues: relevanceState.schemeCode === "HQMS" ? relevanceState.catalogueTree : []
    })
    if (res.data.data && typeof res.data.data === "boolean") {
      await getRelevanceInfo(relevanceState.showSchemeWsid)
      Message.success("操作成功！")
      // 更新勾选掉的数据
      selectTreeRef.value?.setCheckedKeys(relevanceState.selectWsid)
    }
  } catch (error: any) {
    console.log(`output->error`, error)
    toastError(error)
  }
}

// 全选
const checkAll = computed(() => {
  return selectTreeRef.value?.getCheckedNodes().length === flattenTree(relevanceState.catalogueTree).length
})

// 不确定状态
const isIndeterminate = computed(() => {
  return !checkAll.value && selectTreeRef.value?.getCheckedNodes().length
})

const handleCheckAllChange = val => {
  selectTreeRef.value?.checkAllNodes(val)
}

// 可拖拽
const allowDrag = (draggingNode: Node) => {
  return true
}

const allowDrop = (draggingNode: Node, dropNode: Node, type: AllowDropType) => {
  if (dropNode.data.type === "MR_CLASS") {
    return type !== "inner"
  } else {
    return true
  }
}
</script>

<style lang="less" scoped>
.plan-config-container {
  :deep(.common-search-content) {
    padding: 0px;
    background-color: transparent;
  }
}
.plan-manage-content {
  display: flex;
  height: 100%;
}

.plan-change-from {
  :deep(.el-transfer) {
    display: flex;
    align-items: center;
    .el-transfer-panel {
      width: 190px;
    }
    .el-transfer__buttons {
      display: flex;
      flex-direction: column;
      row-gap: 5px;
      padding: 0px 22px;
      .el-button {
        margin-left: 0px;
      }
    }
  }
}

.blank-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;

  text {
    color: #409eff;
    cursor: pointer;
  }
}

.tree-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(225, 226, 230, 1);
  padding: 5px 16px;
  margin: 10px 0;
  padding-left: 24px;

  .tree-title {
    width: calc(100% - 36px);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  span {
    text-align: center;
    // width: 45%;
  }
}

.common-tree {
  height: auto;
}

.foot {
  padding-top: 10px;
  text-align: right;
}

.check-all-container {
  display: flex;
  align-items: center;
  width: 10% !important;
  justify-content: space-between;
  margin-left: 10px;
}

.icon-drag::before {
  width: 12px;
  margin-left: 16px;
  background-color: white;
  content: url(@/assets/svg/tree/tree-icon.svg);
}
</style>
