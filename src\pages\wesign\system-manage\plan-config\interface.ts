import axios from "@/interfaces/axios-instance"

/* ======================== 文档类型 ======================== */

/**
 * @method GET
 * @desc   文档类型-分页列表
 */
export function getDocTypeList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: "/api/document/mr-classs",
    params: {
      ...params,
      // fields: "wsid,mrClassCode,mrClassName,mrDataSourceType,fileWsid,templateName,ulx,uly,lrx,lry,mrStandardDicWsid"
      fields: ""
    }
  })
}

/**
 * @method PUT
 * @desc   文档类型-删除
 */
export function delDocTypeCatalogue(obj) {
  const { mrClassCode = "", item = "" } = obj
  return axios({
    method: "put",
    url: `/api/document/mr-classs/${mrClassCode}`,
    params: {
      item: item
    }
  })
}

interface IDocTypeCatalogueData {
  mrClassCode?: string
  mrClassName: string
  mrStandardDicWsid?: string
  mrDataSourceType?: string
  mrClassLabelKey?: string
  templateWsid?: string
  templateName?: string
  fileWsid?: string
  ulx?: number
  uly?: number
  lrx?: number
  lry?: number
}

/**
 * @method POST
 * @desc   文档类型-新增
 */
export function addDocTypeCatalogue(data: IDocTypeCatalogueData) {
  return axios({
    method: "post",
    url: "/api/document/mr-classs",
    data: data
  })
}

/**
 * @method POST
 * @desc   文档类型-编辑
 */
export function editDocTypeCatalogue(data: IDocTypeCatalogueData) {
  return axios({
    method: "post",
    url: `/api/document/mr-classs/${data.mrClassCode}`,
    data: data
  })
}

/**
 * @method GET
 * @desc   获取文书类型列表
 */
export function getDataSourcesTypeApi() {
  return axios({
    method: "get",
    url: "/api/document/mr-classs/data-sources-type"
  }).then(res => (res.data?.data ?? []) as CommonObjectList)
}

/**
 * @method GET
 * @desc   获取标准文档类型列表
 */
export function getStandardsTypeApi() {
  return axios({
    method: "get",
    url: "/api/document/mr-classs/standards"
  }).then(res => (res.data?.data ?? []) as CommonObjectList)
}

/* ======================== 目录管理 ======================== */

/**
 * @method GET
 * @desc   目录管理-获取目录树
 */
export function getDirectoryManageTreeApi(obj) {
  const { catalogueName } = obj
  return axios({
    method: "get",
    url: "/api/document/catalogues",
    params: {
      catalogueName
    }
  }).then(res => (res.data?.data ?? []) as CommonObjectList)
}

/**
 * @method POST
 * @desc   目录管理-新增
 */
type TypeAddDirectoryCatalogue = {
  parentWsid: string
  catalogueName: string
  catalogueCode: string
}
export function addDirectoryCatalogueApi(obj: TypeAddDirectoryCatalogue) {
  const { parentWsid = "", catalogueName = "", catalogueCode = "" } = obj
  return axios({
    method: "post",
    url: "/api/document/catalogues",
    data: {
      parentWsid,
      catalogueName,
      catalogueCode
    }
  })
}

/**
 * @method PUT
 * @desc   目录管理删除
 */
export function delDirectoryCatalogueApi(obj) {
  const { catalogueWsid = "" } = obj
  return axios({
    method: "put",
    url: `/api/document/catalogues/${catalogueWsid}`
  })
}

/**
 * @method POST
 * @desc   目录管理-编辑
 */
type TypeEditDirectoryCatalogue = {
  wsid: string
  parentWsid: string
  catalogueName: string
  catalogueCode: string
}
export function editDirectoryCatalogueApi(obj: TypeEditDirectoryCatalogue) {
  const { wsid = "", parentWsid = "", catalogueName = "", catalogueCode = "" } = obj
  return axios({
    method: "post",
    url: `/api/document/catalogues/${wsid}`,
    data: {
      wsid,
      parentWsid,
      catalogueName,
      catalogueCode
    }
  })
}

/**
 * @method GET
 * @desc   目录管理-获取关联组合信息
 */
export function getRelevanceInfoApi() {
  return axios({
    method: "get",
    url: "/api/document/catalogues/mr-classs/relevance"
  })
}

/**
 * @method DELETE
 * @desc   目录管理删除
 */
export function delDirectoryMrclassApi(obj) {
  const { wsid = "" } = obj
  return axios({
    method: "delete",
    url: "/api/document/catalogues/delete-mrclass",
    params: {
      wsid
    }
  })
}

/**
 * @method POST
 * @desc   目录管理-分类关联
 */
export function changeDirectoryRelevanceInfoApi(obj: { catalogueTree: any[] }) {
  const { catalogueTree } = obj
  return axios({
    method: "post",
    url: `/api/document/catalogues/mr-classs/relevance`,
    data: {
      catalogueTree
    }
  })
}

/* ======================== 方案管理 ======================== */

/**
 * @method GET
 * @desc   方案管理-方案-列表
 */
export function getSchemesListApi(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/document/schemes`,
    params
  })
}

/**
 * @method POST
 * @desc   方案管理-方案-新增
 */
type TypeAddSchemes = {
  categoryWsid: string
  schemeName: string
  schemeCode: string
  describe: string
  depts: string[]
}
export function addSchemesApi(obj: TypeAddSchemes) {
  const { categoryWsid = "", schemeName = "", schemeCode, describe, depts } = obj
  return axios({
    method: "post",
    url: `/api/document/schemes`,
    data: {
      categoryWsid,
      schemeName,
      schemeCode,
      describe,
      depts
    }
  })
}

/**
 * @method PUT
 * @desc   方案管理-方案-编辑
 */
interface TypeEditSchemes extends TypeAddSchemes {
  schemeWsid: string
}
export function editSchemesApi(obj: TypeEditSchemes) {
  const { categoryWsid = "", schemeName = "", schemeCode, describe, depts, schemeWsid } = obj
  return axios({
    method: "put",
    url: `/api/document/schemes/${schemeWsid}`,
    data: {
      categoryWsid,
      schemeName,
      schemeCode,
      describe,
      depts
    }
  })
}

/**
 * @method DELETE
 * @desc   方案管理-方案-删除
 */

export function deleteSchemesApi(obj) {
  const { schemeWsid = "" } = obj
  return axios({
    method: "delete",
    url: `/api/document/schemes/${schemeWsid}`
  })
}

/**
 * @method GET
 * @desc   方案管理-方案-基础信息（含选择的科室）
 */

export function getSchemesInfoApi(obj) {
  const { schemeWsid = "" } = obj
  return axios({
    method: "get",
    url: `/api/document/schemes/${schemeWsid}`
  })
}

/**
 * @method GET
 * @desc   方案管理-方案-可选的科室
 */

export function getChoiceDeptApi(obj) {
  const { schemeWsid = "", schemeCategoryWsid = "" } = obj
  return axios({
    method: "get",
    url: `/api/document/schemes-category/${schemeCategoryWsid}/enable-dept`,
    params: {
      schemeWsid
    }
  })
}

/**
 * @method GET
 * @desc   方案管理-方案关联-信息
 */

export function getSchemeRelationApi(obj) {
  const { schemeWsid = "" } = obj
  return axios({
    method: "get",
    url: `/api/document/schemes/${schemeWsid}/mr-classes`
  })
}

/**
 * @method GET
 * @desc   方案管理-分类-列表
 */
export function getPlanManageApi() {
  return axios({
    method: "get",
    url: `/api/document/schemes-category`
  })
}

/**
 * @method POST
 * @desc   方案管理-分类-新增
 */

export function addClassifyApi(obj) {
  const { schemeCategoryName = "", schemeCategoryCode = "" } = obj
  return axios({
    method: "post",
    url: `/api/document/schemes-category`,
    data: {
      schemeCategoryName,
      schemeCategoryCode
    }
  })
}

/**
 * @method PUT
 * @desc   方案管理-分类-编辑
 */

export function editClassifyApi(obj) {
  const { schemeCategoryWsid = "", schemeCategoryName = "", schemeCategoryCode = "" } = obj
  return axios({
    method: "put",
    url: `/api/document/schemes-category/${schemeCategoryWsid}`,
    data: {
      schemeCategoryName,
      schemeCategoryCode
    }
  })
}

/**
 * @method DELETE
 * @desc   方案管理-分类-删除
 */

export function deleteClassifyApi(obj) {
  const { schemeCategoryWsid } = obj
  return axios({
    method: "delete",
    url: `/api/document/schemes-category/${schemeCategoryWsid}`
  })
}

/**
 * @method GET
 * @desc   方案管理-方案目录-信息
 */

export function getSchemesCataloguesApi(params) {
  const { schemeWsid = "" } = params
  return axios({
    method: "get",
    url: `/api/document/schemes/${schemeWsid}/catalogues`
  })
}

/**
 * @method PUT
 * @desc   方案管理-方案目录-修改或新增
 */

export function changeSchemesCataloguesApi(obj) {
  const { schemeWsid = "", selectWsid = [], activeCatalogues = [] } = obj
  return axios({
    method: "put",
    url: `/api/document/schemes/${schemeWsid}/catalogues`,
    data: {
      selectWsid,
      activeCatalogues
    }
  })
}

/**
 * @method PUT
 * @desc   文档类型-方案关联-修改或新增
 */

export function changeRelevanceInfoApi(obj: { catalogueTree: any[]; schemeWsid: string }) {
  const { catalogueTree, schemeWsid } = obj
  return axios({
    method: "put",
    url: `/api/document/schemes/${schemeWsid}/mr-classes`,
    data: {
      catalogueTree
    }
  })
}

interface ISignPositionData {
  mrClassCode: string
  ulx: number | string
  uly: number | string
  lrx: number | string
  lry: number | string
  fileWsid: string
}

/**
 * @method POST
 * @desc   签章坐标设置
 */

export function setSignPositionApi(data: ISignPositionData) {
  return axios({
    method: "post",
    url: `/api/document/mr-classs/${data.mrClassCode}/sign-position?fileWsid=${data.fileWsid}`,
    data
  })
}

/**
 * @method GET
 * @desc   文书导入模板下载
 */

export function downloadMrclassImportTemplateApi(params) {
  return axios({
    method: "get",
    url: `/api/document/mr-classs/import-template`,
    responseType: "arraybuffer"
  })
}

/**
 * @method POST
 * @desc   文书模板批量导入
 */
export function importMrclassTemplateApi(data: { file: File }) {
  const { file } = data
  const formData = new FormData()
  formData.append("file", file)
  return axios({
    method: "post",
    url: `/api/document/mr-classs/import`,
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  })
}

/**
 * @method GET
 * @desc   病案分类查询
 */

export function mrclassQueryApi(mrClassCode: string) {
  return axios({
    method: "get",
    url: `/api/document/mr-classs/${mrClassCode}/query`
  })
}
