<template>
  <div v-if="!props.showTable" class="line-chart-container">
    <div class="header flex-between">
      <div class="header-title">平均得分</div>

      <div class="operation-icons">
        <div class="table-chart-tool">
          <el-tooltip content="表格展示">
            <i v-if="props.showTable"><img src="@/assets/svg/statistical-statement/table-active.svg" alt="" /></i>
            <i v-else @click="emits('changeTab', true)">
              <img src="@/assets/svg/statistical-statement/table.svg" alt="" />
            </i>
          </el-tooltip>
          <el-tooltip content="图表展示">
            <i v-if="props.showTable">
              <img src="@/assets/svg/statistical-statement/chart.svg" alt="" />
            </i>
            <i v-else><img src="@/assets/svg/statistical-statement/chart-active.svg" alt="" /></i>
          </el-tooltip>
        </div>
        <i class="ri-download-2-line" @click="emits('export')"></i>
      </div>
    </div>
    <v-chart class="line-chart" :option="options" autoresize></v-chart>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue"
import { LineChart } from "echarts/charts"
import { GridComponent } from "echarts/components"
import { use } from "echarts/core"
import { UniversalTransition } from "echarts/features"
import { CanvasRenderer } from "echarts/renderers"
import VChart from "vue-echarts"

use([GridComponent, LineChart, UniversalTransition, CanvasRenderer])

const props = defineProps({
  showTable: { type: Boolean, default: true },
  timeList: { type: Array, default: () => [] },
  series: { type: Array, default: () => [] }
})

const emits = defineEmits(["changeTab", "export"])

const options = computed(() => ({
  grid: {
    bottom: 0,
    left: "2%",
    right: "2%",
    containLabel: true
  },
  tooltip: {
    trigger: "axis"
  },
  xAxis: {
    type: "category",
    data: props.timeList
  },
  yAxis: {
    type: "value"
  },
  series: [
    {
      type: "line",
      color: "#1BA1FC",
      data: props.series,
      smooth: false
    }
  ]
}))
</script>

<style lang="less" scoped>
.line-chart-container {
  border: 1px solid #d4d4d4;
  border-radius: 4px;
  padding: 20px;
  box-sizing: border-box;
  width: calc(100% - 450px);
  margin-left: 20px;

  .header {
    .operation-icons {
      display: flex;
      align-items: center;
      gap: 10px;
      i,
      img {
        cursor: pointer;
      }
    }

    .header-title {
      font-size: 14px;
      font-weight: bold;
      color: #333;
    }

    .download-icon {
      i {
        font-size: 16px;
        color: #333;
      }
    }
  }

  .line-chart {
    height: 224px;
  }
}
</style>
