import * as pdfjsLib from "pdfjs-dist"
pdfjsLib.GlobalWorkerOptions.workerSrc = "/src/libs/pdfjs-dist/pdf.worker.min.js"

const defaultPdfRenderConfigs = {
  cMapUrl: "/src/libs/pdfjs-dist/cmaps/",
  cMapPacked: true,
  rangeChunkSize: 256 * 1024, // 256kb
  disableStream: true,
  disableAutoFetch: true
}

export const getPdfDocument = (source: string) => {
  const pdfLoadingTask = pdfjsLib.getDocument({
    ...defaultPdfRenderConfigs,
    url: source
  })
  return pdfLoadingTask
}
