import { onMounted, ref } from "vue"
import { getSealingConfigApi } from "./interface"

type ConfigType = "YES" | "NO"

export const useSealingConfig = () => {
  const allowBorrow = ref<ConfigType>("NO")
  const allowPrint = ref<ConfigType>("NO")
  const needSign = ref<ConfigType>("NO")

  onMounted(() => {
    getSealingConfigApi().then(res => {
      allowBorrow.value = res.data.data.borrowStatus
      allowPrint.value = res.data.data.printStatus
      needSign.value = res.data.data.signStatus
    })
  })

  return { allowBorrow, allowPrint, needSign }
}
