import { ref } from "vue"
import moment from "moment"
import { StatisticTypeEnum } from "@/configs/enums"
import { formatDate, formatDatetime } from "@/utils"
import type { TableColumnItem } from "@/types"

export interface DateOptionItem {
  label: string
  value: StatisticTypeEnum | ""
}

export interface CollectRecords {
  // inpNo: string // 住院号
  patientId: string // 患者编号
  dataSourceName: string // 系统类别
  collectDateTime: string //采集时间
  collectStatus: string //采集状态,
  collectStatusEnum: string //采集状态枚举
}

export interface BusinessStatistics {
  businessTypeStr: string // 数据类型
  item: Array<Record<string, any>> // 数据项
}

export interface SystemAbnormalType {
  systemName: string
  count: number
}

export const dataCollectColumn: Array<TableColumnItem> = [
  { prop: "dataSourceName", label: "系统类别", minWidth: 70 },
  { prop: "patientId", label: "患者编号", minWidth: 70 },
  { prop: "collectDateTime", label: "采集时间", minWidth: 70 },
  { prop: "collectStatus", label: "采集状态", minWidth: 50 }
]

// 日期选项
export const dateOptions: Array<DateOptionItem> = [
  { label: "今日", value: StatisticTypeEnum.DAY },
  { label: "本周", value: StatisticTypeEnum.WEEK },
  { label: "本月", value: StatisticTypeEnum.MONTH }
]

// 无数据时echarts的配置项
export const noDataOption = ref({
  title: {
    text: "暂无数据",
    x: "center",
    y: "center",
    textStyle: {
      fontSize: 14,
      fontWeight: "normal"
    }
  }
})

// 根据statisticType获取时间范围
export const getRangeDateByType = (val: StatisticTypeEnum | "") => {
  let startTime = moment()

  switch (val) {
    case "DAY": {
      // 当日
      startTime = moment().startOf("day")
      break
    }
    case "WEEK": {
      // 本周
      startTime = moment().startOf("isoWeek")
      break
    }
    case "MONTH": {
      // 本月
      startTime = moment().startOf("month")
      break
    }
    default: {
      startTime = moment().startOf("day")
      break
    }
  }
  return [formatDatetime(startTime.toDate(), "YYYY-MM-DD"), formatDatetime(new Date(), "YYYY-MM-DD")]
}

// 采集状态枚举
export enum CollectStatusEnum {
  COLLECT_FAIL = "COLLECT_FAIL", // 采集失败
  COLLECT_WAIT = "COLLECT_WAIT", // 待采集
  COLLECT_SUCCESS = "COLLECT_SUCCESS" // 采集成功
}

// 出院数据分类枚举
export enum BusinessStatisticsEnum {
  COLLECT = "COLLECT", // 采集病案数
  OUT_HOSPITAL = "OUT_HOSPITAL", // 出院人数
  ARCHIVE = "ARCHIVE" // 归档数
}

// 系统运行状态枚举
export enum SystemStatusEnum {
  GOOD = " GOOD", // 正常
  WARN = "WARN", // 警告
  ERROR = "ERROR" // 异常
}
