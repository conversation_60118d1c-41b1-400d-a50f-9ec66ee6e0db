<template>
  <DialogContainer
    v-model:visible="dialogVisible"
    :title="title"
    :width="width"
    :confirm-callback="handleConfirm"
    :confirm-loading="isSubmitting"
  >
    <el-form ref="ruleFormRef" :label-width="labelWidth" label-suffix="：" :model="dialogFormState">
      <el-form-item
        v-for="item in formConfig"
        :key="item.prop"
        :label="item.label"
        :prop="item.prop"
        :rules="item.rules"
      >
        <!-- 文本输入框 -->
        <el-input
          v-if="item.type === 'input'"
          v-model.trim="dialogFormState[item.prop]"
          :placeholder="`请输入${item.label}`"
          :disabled="$props.disabledFields?.includes(item.prop)"
        />

        <!-- 数字输入框 -->
        <el-input-number
          v-else-if="item.type === 'number'"
          v-model="dialogFormState[item.prop]"
          :placeholder="`请输入${item.label}`"
          :disabled="$props.disabledFields?.includes(item.prop)"
        />

        <!-- 多行文本框 -->
        <el-input
          v-else-if="item.type === 'textarea'"
          v-model="dialogFormState[item.prop]"
          :placeholder="`请输入${item.label}`"
          :rows="3"
          type="textarea"
          resize="none"
          :disabled="$props.disabledFields?.includes(item.prop)"
        />

        <!-- 单选 -->
        <el-radio-group
          v-else-if="item.type === 'radio'"
          v-model.trim="dialogFormState[item.prop]"
          :disabled="$props.disabledFields?.includes(item.prop)"
        >
          <el-radio v-for="option in item.options" :key="option.value as string" :label="option.value">
            {{ option.label }}
          </el-radio>
        </el-radio-group>

        <!-- 下拉选择 -->
        <el-select
          v-else-if="item.type === 'select'"
          v-model="dialogFormState[item.prop]"
          :placeholder="`请选择${item.label}`"
          :disabled="$props.disabledFields?.includes(item.prop)"
          :multiple="item.multiple"
          :multiple-limit="item['multiple-limit']"
          filterable
        >
          <el-option
            v-for="option in item.options"
            :key="option.value as string"
            :label="option.label"
            :value="option.value"
          />
        </el-select>

        <!-- 时间选择 -->
        <el-date-picker
          v-else-if="item.type === 'datetime'"
          v-model="dialogFormState[item.prop]"
          style="width: 100%"
          type="datetime"
          :disabled="$props.disabledFields?.includes(item.prop)"
          :disabled-date="item.disabledDate"
          placeholder="请选择日期时间"
        />
      </el-form-item>
    </el-form>
  </DialogContainer>
</template>

<script setup lang="ts">
import { ref, computed } from "vue"
import { noop } from "lodash-es"
import { DialogContainer } from "@/base-components"
import type { DialogFormConfigItem } from "@/types"
import type { FormInstance } from "element-plus"

const props = withDefaults(
  defineProps<{
    visible: boolean
    title: string
    width?: number
    labelWidth?: number
    confirmCallback: () => Promise<any>
    formConfig: Array<DialogFormConfigItem>
    formState: Record<string, any>
    disabledFields?: Array<string> // 表单禁止编辑的字段
  }>(),
  { width: 480, labelWidth: 100, disabledFields: () => [] }
)

const isSubmitting = ref(false)

const emits = defineEmits(["update:visible"])

const ruleFormRef = ref<FormInstance>()

const dialogVisible = computed({
  get: () => props.visible,
  set: val => emits("update:visible", val)
})

const dialogFormState = computed({
  get: () => props.formState,
  set: noop
})

function handleConfirm() {
  ruleFormRef.value?.validate(valid => {
    if (!valid) return
    isSubmitting.value = true
    props.confirmCallback().then(() => {
      isSubmitting.value = false
    })
  })
}
</script>
