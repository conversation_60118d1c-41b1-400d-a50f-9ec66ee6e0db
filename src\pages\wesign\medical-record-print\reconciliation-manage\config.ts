import type { TableColumnItem } from "@/types"

export const menuId = "/print/reconciliation-manage"

export const tabsRouterList = [
  {
    label: "T+1交易对账",
    path: "/print/reconciliation-manage/transaction"
  },
  {
    label: "资金账单",
    path: "/print/reconciliation-manage/fund-bill"
  }
]

export const transactionColumns: Array<TableColumnItem> = [
  { type: "selection", width: 45, fixed: "left" },
  { prop: "channelStr", label: "支付渠道", minWidth: 100 },
  { prop: "billDateStr", label: "对账日期", minWidth: 150, sortable: true },
  { prop: "printTradeNum", label: "应收交易量（笔数）", minWidth: 180, sortable: true },
  { prop: "printTradeAmount", label: "应收交易金额（元）", minWidth: 200, sortable: true },
  { prop: "thirdTradeNum", label: "实收交易量（笔数）", minWidth: 180, sortable: true },
  { prop: "thirdTradeAmount", label: "实收交易金额（元）", minWidth: 180, sortable: true },
  { prop: "netAmount", label: "总额差", minWidth: 180, sortable: true },
  { prop: "operation", label: "操作", width: 100, fixed: "right", must: true }
]

export const transactionDetailColumns: Array<TableColumnItem> = [
  { type: "selection", width: 45, fixed: "left" },
  { prop: "tradeDate", label: "交易时间", minWidth: 200, sortable: true },
  { prop: "tradeNo", label: "应收商户订单单号", minWidth: 150 },
  { prop: "printPayAmount", label: "应收订单金额", minWidth: 150, sortable: true },
  { prop: "printRefundAmount", label: "应收退款金额", minWidth: 150, sortable: true },
  { prop: "thirdTradeNo", label: "实收商户订单单号", minWidth: 150 },
  { prop: "billType", label: "实收交易类型", minWidth: 120 },
  { prop: "thirdPayAmount", label: "实收订单金额", minWidth: 150, sortable: true },
  { prop: "thirdRefundAmount", label: "实收退款金额", minWidth: 150, sortable: true },
  { prop: "channelStr", label: "支付渠道", minWidth: 100 }
]

export const fundBillColumns: Array<TableColumnItem> = [
  { type: "selection", width: 45, fixed: "left" },
  { prop: "channelStr", label: "支付渠道", minWidth: 100 },
  { prop: "billDate", label: "账单日期", minWidth: 120, sortable: true },
  { prop: "totalPayNum", label: "收入笔数", minWidth: 100, sortable: true },
  { prop: "totalPayAmount", label: "收入金额", minWidth: 100, sortable: true },
  { prop: "totalRefundNum", label: "支出笔数", minWidth: 100, sortable: true },
  { prop: "totalRefundAmount", label: "支出金额", minWidth: 100, sortable: true },
  { prop: "totalNetAmount", label: "收支净额", minWidth: 100, sortable: true }
]
