<template>
  <div class="graph-wrapper">
    <div ref="graphContainer" class="graph-container"></div>
    <TeleportContainer />
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue"
import { Graph } from "@antv/x6"
import { register, getTeleport } from "@antv/x6-vue-shape"
import { formatApprovalNodeDataToFront, formatApprovalNodeDataToEnd } from "../config"
import { getDefaultProcessData, addNodeSize, startNodeSize, endNodeSize, approvalNodeSize } from "../util"
import AddNode from "./AddNode.vue"
import ApplyNode from "./ApplyNode.vue"
import ApprovalNode from "./ApprovalNode.vue"
import EndNode from "./EndNode.vue"
import StartNode from "./StartNode.vue"

const TeleportContainer = getTeleport()
const graphContainer = ref(null)

interface Props {
  data: any
}

const props = defineProps<Props>()

// 流程graph
const graphData = ref()

// 流程图初始数据
const initialData = ref<any>([])

// 获取流程图所有节点数据
const getGraphData = () => {
  const cells = graphData.value.toJSON().cells
  const nodes = cells.filter(cell => cell.shape !== "connect-edge")
  const sortedCells = nodes.sort((a, b) => {
    return a.position.y - b.position.y
  })

  const data: any = [...sortedCells, ...cells.filter(cell => cell.shape === "connect-edge")]
  const formattedDataToEnd = formatApprovalNodeDataToEnd(data)
  return formattedDataToEnd
}

onMounted(() => {
  // 注册节点
  register({
    shape: "START_EVENT",
    inherit: "vue-shape",
    x: 200,
    y: 150,
    ...startNodeSize,
    component: StartNode
  })

  register({
    shape: "USER_TASK",
    inherit: "vue-shape",
    ...approvalNodeSize,
    component: ApprovalNode
  })

  register({
    shape: "APPLY_NODE",
    inherit: "vue-shape",
    width: 116,
    height: 40,
    component: ApplyNode
  })

  register({
    shape: "gateway",
    inherit: "vue-shape",
    ...addNodeSize,
    component: AddNode
  })

  register({
    shape: "END_EVENT",
    inherit: "vue-shape",
    ...endNodeSize,
    component: EndNode
  })

  Graph.registerEdge(
    "connect-edge",
    {
      inherit: "edge",
      attrs: {
        line: {
          stroke: "#ccc",
          strokeWidth: 1,
          targetMarker: {
            tagName: "path",
            stroke: "none"
          },
          sourceMarker: {
            tagName: "path",
            stroke: "none"
          }
        }
      }
    },
    true
  )

  if (!graphContainer.value) return
  const graph = new Graph({
    interacting: { nodeMovable: false },
    container: graphContainer.value,
    background: false,
    height: 6000,
    grid: false,
    connecting: {
      anchor: "center"
    }
  })

  graphData.value = graph

  if (props.data) {
    const data = formatApprovalNodeDataToFront(props.data, graph)
    initialData.value = data
  } else {
    initialData.value = getDefaultProcessData(graph)
  }

  graph.fromJSON(initialData.value)
})

defineExpose({
  getGraphData
})
</script>

<style lang="less" scoped>
.graph-wrapper {
  height: 100%;
  overflow: auto;
  background-color: #eff2f7;
  .graph-container {
    margin: auto;
    width: 1200px;
  }
}
</style>
../util
