import { FormRules } from "element-plus"
import type { TableColumnItem } from "@/types"

export const dataDictionaryColumns: Array<TableColumnItem> = [
  { prop: "key", label: "字典键(key)", minWidth: 200 },
  { prop: "value", label: "字典明细值(value)", minWidth: 150 },
  { prop: "createdDatetime", label: "创建时间", minWidth: 180 },
  { prop: "describe", label: "字典项描述", minWidth: 150 },
  { prop: "operation", label: "操作", width: 200, fixed: "right" }
]

export const dataDictionaryColumnsWithKeywords: Array<TableColumnItem> = [
  { prop: "key", label: "字典键(key)", minWidth: 200 },
  { prop: "value", label: "字典明细值(value)", minWidth: 150 },
  { prop: "createdDatetime", label: "创建时间", minWidth: 180 },
  { prop: "describe", label: "字典项描述", minWidth: 150 },
  { prop: "length", label: "关键字长度", minWidth: 150 },
  { prop: "operation", label: "操作", width: 200 }
]

export const detailFormRules: FormRules = {
  key: [{ required: true, message: "请输入字典键", trigger: "blur" }],
  value: [{ required: true, message: "请输入字典明细值", trigger: "blur" }],
  length: [
    { required: true, message: "请输入关键字长度", trigger: "blur" },
    {
      type: "number",
      validator: (rule, value, callback) => {
        if (value < 0 || value > 15) {
          callback(new Error("关键字长度必须为1~15的数字"))
        } else {
          callback()
        }
      },
      trigger: "blur"
    }
  ]
}
