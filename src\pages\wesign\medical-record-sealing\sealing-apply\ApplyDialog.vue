<template>
  <!-- 封存弹窗 -->
  <el-dialog
    v-model="visible"
    :title="step === 1 ? '封存文件预览' : '封存申请'"
    :width="1000"
    top="10vh"
    destroy-on-close
    :close-on-click-modal="false"
    :show-close="false"
    @closed="reset"
  >
    <!-- 第一步：pdf确认 -->
    <div v-show="step === 1" v-loading="documentLoading" class="document-info">
      <div class="document-info__tree">
        <CommonTree :data="documentTree" @click-node="handleNodeClick" />
      </div>
      <div class="document-info__pdf">
        <div class="document-info__sync-time">
          <span>最后采集时间：{{ formatDatetime(medicalRecordInfo?.syncDatetime) }}</span>
          <el-button
            type="primary"
            size="small"
            style="font-size: 14px"
            :disabled="statusEnumName !== 'COLLECTING'"
            @click="syncDocument"
          >
            同步
          </el-button>
        </div>
        <PdfPreviewComponent v-if="!syncLoading" :src="pdfSrc" :default-width="750" />
      </div>
    </div>

    <!-- 第二步：表单填写 -->
    <el-form
      v-show="step === 2"
      ref="sealFormRef"
      label-width="150px"
      :model="sealForm"
      :rules="sealingFormRules"
      class="apply-form"
    >
      <el-form-item label="申请人：" prop="applicant">
        <el-input v-model="sealForm.applicant" />
      </el-form-item>
      <el-form-item label="申请人证件号：" prop="applicantCertificatesNo">
        <el-input v-model="sealForm.applicantCertificatesNo" />
      </el-form-item>
      <el-form-item label="联系方式：" prop="applicantContent">
        <el-input v-model="sealForm.applicantContent" />
      </el-form-item>
      <el-form-item label="与患者关系" prop="relationship">
        <el-radio-group v-model="sealForm.relationship">
          <el-radio label="OWN">本人</el-radio>
          <el-radio label="AGENT">代理人</el-radio>
          <el-radio label="EXTEND">继承人</el-radio>
          <el-radio label="EXTEND_AGENT">继承代理人</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="封存时间：" prop="unlockType">
        <el-radio-group v-model="sealForm.unlockType">
          <el-radio label="LIMIT_TIME">限定封存时间</el-radio>
          <el-radio label="UNLIMITED_TIME">不限定封存时间</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="sealForm.unlockType === 'LIMIT_TIME'" label="封存截止时间：" prop="unlockDatetime">
        <el-date-picker
          v-model="sealForm.unlockDatetime"
          type="date"
          placeholder="结束时间"
          :disabled-date="disabledDate"
          style="width: 100%"
          class="apply-form__limit-time"
          value-format="x"
        />
      </el-form-item>
      <el-form-item label="是否允许追加病历：" prop="additionalCases">
        <el-radio-group v-model="sealForm.additionalCases">
          <el-radio label="ALLOW">允许</el-radio>
          <el-radio label="NOT_ALLOW">不允许</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="封存原因：" prop="lockReason">
        <el-input
          v-model="sealForm.lockReason"
          class="apply-form__reason"
          :rows="2"
          type="textarea"
          placeholder="请输入..."
        />
      </el-form-item>
      <el-form-item label="身份证明材料上传：" prop="files">
        <div class="identification-materials">
          <UploadDocument ref="UploadDocumentRef" v-model:files="sealForm.files" />
          <div class="identification-materials__hint">1.本人：请上传或拍摄患者本人有效身份证明材料</div>
          <div class="identification-materials__hint">
            2.代理人：请上传或拍摄患者及代理人有效身份证明，关系证明和授权委托书
          </div>
          <div class="identification-materials__hint">
            3.继承人：请上传或拍摄患者死亡证明，继承人有效身份证明及患者与继承人关系的法定证明材料
          </div>
          <div class="identification-materials__hint">
            4.继承人代理人：请上传或拍摄患者死亡证明、死亡患者法定继承人及代理人有效身份证明，死亡
            死亡患者与继承人关系法定证明，代理人与继承人的关系证明及授权委托书
          </div>
        </div>
      </el-form-item>
    </el-form>

    <!-- 第三步：签名 -->
    <div v-show="step === 3" v-loading="signLoading" class="document-sign">
      <PdfPreviewComponent :src="signPdfSrc" :default-width="950"></PdfPreviewComponent>
    </div>

    <!-- dialog footer -->
    <template #footer>
      <div style="display: flex">
        <el-button
          v-if="step === 2"
          :disabled="uploadLoading || sealLoading"
          :loading="UploadDocumentRef?.CameraDialogRef?.cameraLoading"
          type="primary"
          @click="UploadDocumentRef?.takePhoto()"
        >
          拍摄
        </el-button>
        <div style="flex: 1; min-width: 0px">
          <el-button :disabled="syncLoading || uploadLoading || sealLoading" @click="close">取消</el-button>

          <template v-if="step === 1">
            <el-button type="primary" :disabled="syncLoading" @click="step += 1">下一步</el-button>
          </template>
          <template v-else-if="step === 2">
            <el-button :disabled="uploadLoading || signLoading || sealLoading" @click="step -= 1">上一步</el-button>
            <el-button type="primary" :loading="uploadLoading || signLoading || sealLoading" @click="applySign">
              {{ $props.needSign === "YES" ? "下一步" : "确认" }}
            </el-button>
          </template>
          <template v-else>
            <el-button :disabled="sealLoading" @click="step -= 1">上一步</el-button>
            <el-button :loading="sealLoading" type="primary" @click="checkSignResult">确定</el-button>
          </template>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from "vue"
import { cloneDeep } from "lodash-es"
import { PdfPreviewComponent, CommonTree } from "@/base-components"
import { getRecordViewData, syncRecordApi } from "@/interfaces"
import { Message, formatRecordTree, getFileNodeList, toastError, formatDatetime, SystemAlert } from "@/utils"
import UploadDocument from "../components/upload-document.vue"
import { applySignApi, handleLockSealingApi, checkSealSignResultApi } from "../interface"
import { disabledDate, sealingFormRules } from "./config"
import type { FormInstance, UploadUserFile } from "element-plus"

const props = defineProps<{ needSign: "YES" | "NO" }>() // 是否开启了签名配置 是则需要第四步

const emits = defineEmits(["success"])

const UploadDocumentRef = ref()

const medicalRecordInfo = ref<Record<string, any>>()
const step = ref(1)
let inpNo = ""

/* ============== 第一步：查看文件 ============== */

const syncLoading = ref(false) // 同步loading
const documentTree = ref<Record<string, any>[]>([])
const documentLoading = ref(false)
const pdfSrc = ref("")

// 获取病案信息
const getMedicalRecordInfo = () => {
  documentLoading.value = true
  getRecordViewData({ inpNo: inpNo }).then(res => {
    medicalRecordInfo.value = res.data.data.baseInfo
    documentTree.value = formatRecordTree(res.data.data.treeInfo)
    const pdfNodeList = getFileNodeList(documentTree.value)
    handleNodeClick(pdfNodeList[0]) // 设置初始pdf
    documentLoading.value = false
  })
}

// 切换文档
const handleNodeClick = node => {
  if (!node?.contentFilePath) return
  const newPdfSrc = "/api" + node.contentFilePath
  if (pdfSrc.value === newPdfSrc) return
  if (node.type === "FILE") {
    pdfSrc.value = newPdfSrc
  }
}

// 同步
const syncDocument = () => {
  syncLoading.value = true
  syncRecordApi(inpNo)
    .then(() => {
      Message.success("操作成功")
      getMedicalRecordInfo()
    })
    .catch(err => {
      toastError(err)
    })
    .finally(() => {
      syncLoading.value = false
    })
}

/* ============= 第二步 ============= */

interface FileType extends UploadUserFile {
  fileWsid?: string
  fileName?: string
  wsid?: string
  filePage?: number
  source?: string
}

const sealFormRef = ref<FormInstance>()
const sealLoading = ref(false)

// form data
const sealForm = reactive({
  applicant: "",
  applicantCertificatesNo: "",
  applicantContent: "",
  unlockType: "LIMIT_TIME",
  unlockDatetime: "",
  additionalCases: "ALLOW",
  lockReason: "",
  wsid: "",
  relationship: "OWN",
  files: [] as Array<FileType>
})

// 已上传的文件
const uploadedFiles = computed(() => {
  const params = cloneDeep(sealForm)
  const uploadedFileList = [] as Array<FileType>
  params.files.forEach(item => {
    if (item.status === "success")
      uploadedFileList.push({ name: item.name, fileWsid: item.wsid, fileName: item.name, filePage: item.filePage })
  })
  return uploadedFileList
})

/* ---------------- 上传身份材料 ------------- */
// 是否有文件正在上传
const uploadLoading = computed(() => {
  const haveUpload = sealForm.files.findIndex(file => file.status === "uploading")
  return haveUpload !== -1
})

/* =============== 第三步 ============== */

const signPdfSrc = ref("")
const signLoading = ref(false)
let tempEnvelopeWsid = ""

const applySign = () => {
  sealFormRef.value?.validate(valid => {
    if (!valid) return
    if (uploadedFiles.value.length < 3) return SystemAlert("至少上传三张证明材料")
    // 不需要签名直接申请封存
    if (props.needSign === "NO") return applySeal()
    signLoading.value = true
    applySignApi({
      wsid: sealForm.wsid,
      applicant: sealForm.applicant,
      applicantCertificatesNo: sealForm.applicantCertificatesNo,
      applicantContent: sealForm.applicantContent,
      unlockType: sealForm.unlockType,
      unlockDatetime: sealForm.unlockDatetime,
      additionalCases: sealForm.additionalCases,
      lockReason: sealForm.lockReason,
      relationship: sealForm.relationship
    })
      .then(res => {
        step.value = 3
        const { envelopeWsid, fileBytes } = res.data.data
        tempEnvelopeWsid = envelopeWsid
        signPdfSrc.value = "data:application/pdf;base64," + fileBytes
        signLoading.value = false
      })
      .catch(error => {
        toastError(error)
        signLoading.value = false
      })
  })
}

// 检测签名结果
const checkSignResult = () => {
  sealLoading.value = true
  checkSealSignResultApi(tempEnvelopeWsid)
    .then(res => {
      sealLoading.value = false
      if (res.data.data.status === "SUCCESS") {
        applySeal(res.data.data.bytes)
      } else if (res.data.data.status === "APPLY_FAIL") {
        SystemAlert("签名失败，请重新发起封存申请")
      } else if (res.data.data.status === "APPLY") {
        SystemAlert("签名中，请稍后再试")
      }
    })
    .catch(error => {
      sealLoading.value = false
      toastError(error)
    })
}

/* =============== 发起封存申请 ============== */

// 发起封存申请
const applySeal = (fileBytes?: string) => {
  const params = cloneDeep(sealForm)
  sealLoading.value = true
  params.files = [] // 传给后端的字段是file，所以此处清空
  params.unlockDatetime += 24 * 60 * 60 * 1000 - 1000
  if (params.unlockType === "UNLIMITED_TIME") {
    params.unlockDatetime = ""
  }
  handleLockSealingApi({ ...params, file: uploadedFiles.value, signNo: tempEnvelopeWsid, signFileBytes: fileBytes })
    .then(() => {
      Message.success("操作成功")
      emits("success")
      close()
    })
    .catch(err => toastError(err))
    .finally(() => {
      sealLoading.value = false
    })
}

/* ============ dialog ============ */

const visible = ref(false)

const reset = () => {
  documentTree.value = []
  pdfSrc.value = ""
  signPdfSrc.value = ""
  medicalRecordInfo.value = undefined
  step.value = 1
  // form reset
  sealFormRef.value?.resetFields()
  sealForm.applicant = ""
  sealForm.applicantCertificatesNo = ""
  sealForm.applicantContent = ""
  sealForm.unlockType = "LIMIT_TIME"
  sealForm.unlockDatetime = ""
  sealForm.additionalCases = "ALLOW"
  sealForm.lockReason = ""
  sealForm.wsid = ""
  sealForm.relationship = "OWN"
  sealForm.files = [] as Array<FileType>
}
const statusEnumName = ref("")

const show = row => {
  visible.value = true
  inpNo = row.inpNo
  sealForm.wsid = row.wsid
  statusEnumName.value = row.statusEnumName
  getMedicalRecordInfo()
}

const close = () => {
  visible.value = false
}

defineExpose({
  show,
  close
})
</script>

<style lang="less" scoped>
.document-info {
  display: flex;
  height: 560px;
  padding: 16px;
  border: 1px solid rgb(225 226 230);
  border-radius: 4px;
  box-sizing: border-box;

  &__tree {
    width: 260px;
    height: 100%;
  }

  &__pdf {
    max-width: calc(100% - 245px);
    height: calc(100% - 24px);
    flex: 1;
    overflow: hidden;
  }

  &__sync-time {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 8.5px;
    font-weight: 400;
    font-style: normal;
    font-size: 12px;
    margin-bottom: 12px;
    color: rgb(3 8 20 / 65.5%);
  }
}

.apply-form {
  max-height: 620px;
  overflow-y: auto;
  padding-right: 10px;

  &__limit-time {
    display: inline-flex;
  }

  &__reason {
    textarea {
      resize: none;
    }
  }

  :deep(.el-upload) {
    width: 88px;
    height: 124px;
  }
  :deep(.el-upload-list__item) {
    width: 88px;
    height: 124px;
  }
}

.identification-materials {
  display: flex;
  flex-direction: column;
  font-size: 14px;
  color: #f56c6c;
  line-height: 1.5;
  margin-top: 10px;
  width: 100%;
}

.document-sign {
  height: 600px;
}
.pdf-thumbnail {
  width: 120px;
  height: auto;
}

.preview-image {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;

  &__close {
    position: absolute;
    right: 20px;
    top: 20px;
    font-size: 28px;
    color: #fff;
    cursor: pointer;
  }

  img {
    width: 65%;
    height: 65%;
    object-fit: contain;
  }
}
</style>
