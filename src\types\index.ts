import type { FormItemRule } from "element-plus"

export interface TableColumnItem {
  // default | selection | index | expand 其余为CustomTable组件自定义内容
  type?: "selection" | "index" | "select" | "input" | "date" | "autocomplete" | "expand"
  fixed?: "left" | "right"
  align?: "left" | "right" | "center"
  "header-align"?: "left" | "right" | "center"
  prop?: string
  label?: string
  width?: number
  minWidth?: number
  isShow?: boolean
  must?: boolean
  sortable?: boolean
  optionName?: string
  showOverflowTooltip?: boolean
  timeType?: string
  tooltipContent?: string
}

export interface TableColSettingItem extends TableColumnItem {
  id: string
  checked: boolean
}

export interface BaseOptionItem {
  label: string
  value: string | number | boolean
}

export interface CommonOptionItem extends BaseOptionItem {
  [prop: string]: any
}

export type SearchFormConfigItem =
  | {
      type: "input"
      label: string
      prop: string
    }
  | {
      type: "select"
      label: string
      prop: string
      options: Array<BaseOptionItem>
    }
  | {
      type: "daterange"
      label: string
      prop: string
    }
  | {
      type: "department"
      label: string
      prop: string
    }
  | {
      type: "patient"
      label: string
    }
  | {
      type: "visit"
      label: string
    }

export type DialogFormConfigItem =
  | {
      type: "input" | "number" | "textarea"
      label: string
      prop: string
      rules?: Array<FormItemRule>
    }
  | {
      type: "select" | "radio"
      label: string
      prop: string
      options: Array<BaseOptionItem>
      rules?: Array<FormItemRule>
      multiple?: boolean
      "multiple-limit"?: number
    }
  | {
      type: "datetime"
      label: string
      prop: string
      rules?: Array<FormItemRule>
      disabledDate?: (date: Date) => boolean
    }

// 病案借阅状态
export type BorrowStatusEnum = "BORROWED" | "NO_BORROW" | "CAN_NOT_BORROW"

// 病案状态
export type MedicalRecordStatus =
  | "COLLECTING" // 0 待回收
  | "WAIT_SENIOR_REVIEW_QC" // 1 上级医师审核
  | "WAIT_NURSE_REVIEW_QC" // 2 质控护士签收
  | "WAIT_CATALOG" // 3 待编目
  | "REPAIR" // 4 返修
  | "WAIT_FINAL_QC" // 5 待终末质控
  | "WAIT_ARCHIVED" // 6 待归档
  | "YET_ARCHIVED" // 7 已归档
  | "SHELVES_YET" // 8 已上架
  | "RECALL" // 9 已召回
  | "UNKNOWN" // -99 未知
  | "SUBMIT_SUCCESS" // 已提交 80
  | "NOT_SUBMIT" // 待提交

export interface MenuOperationItem {
  index: string
  id: string
  name: string
  status: 0 | 1
}

export interface MenuConfigItem {
  index: number
  name: string
  id: string
  path: string
  icon?: string
  status: 0 | 1
  level: 0 | 1 | 2
  functions: Array<string>
  children: Array<MenuConfigItem>
  operations: Array<MenuOperationItem>
  ext?: Record<string, any>
  hideMenu?: number
}

export enum DocumentMetaTypeEnum {
  BasicInfo = "BasicInfo",
  SignVerificationInfo = "SignVerificationInfo"
}

// 采集列表
export type CollectTypeItem = {
  id: string
  name: string
  oriCode: string
}

// 用户信息
export interface UserItem {
  wsid: string
  userWsid: string
  jobId: string
  realName: string
}

export type FilterType = "EMR_QEURY" | "BORROW_APPLY" | "FULL_TEXT_SEARCH"

export enum MedicalPrintMetaTypeEnum {
  Registration = "Registration",
  Tracer = "Tracer"
}

export type RelationshipType = "OWN" | "AGENT" | "GUARDIAN" | "HEIR" | "HEIR_AGENT"
export enum RelationshipTypeEnum {
  OWN = "OWN",
  AGENT = "AGENT",
  GUARDIAN = "GUARDIAN",
  HEIR = "HEIR",
  HEIR_AGENT = "HEIR_AGENT"
}

export interface TabState {
  title: string
  name: string
  content?: string
  parentRoute?: string //父路由
}

export type MenuType = "user" | "admin" | "coding" | "physical-examination"

// 病案状态
export type CatalogOperationType = "confirm" | "check" | "sync" | "save" | "view" | "return" | "cancel"
