<template>
  <template v-if="$props.tooltip">
    <el-tooltip :content="$props.tooltip" placement="top">
      <el-button
        :type="$props.type"
        :plain="$props.plain"
        :icon="$props.icon"
        :disabled="$props.disabled"
        :loading="$props.loading"
        @click="$emit('click')"
      >
        <slot></slot>
      </el-button>
    </el-tooltip>
  </template>
  <template v-else>
    <el-button
      :type="$props.type"
      :plain="$props.plain"
      :icon="$props.icon"
      :disabled="$props.disabled"
      :loading="$props.loading"
      @click="$emit('click')"
    >
      <slot></slot>
    </el-button>
  </template>
</template>

<script setup lang="ts">
import type { Component } from "vue"

withDefaults(
  defineProps<{
    tooltip: string
    icon: Component | string
    type?: "" | "default" | "success" | "warning" | "info" | "text" | "primary" | "danger"
    plain?: boolean
    disabled?: boolean
    loading?: boolean
  }>(),
  {
    type: "",
    plain: false,
    disabled: false,
    loading: false
  }
)

defineEmits(["click"])
</script>
