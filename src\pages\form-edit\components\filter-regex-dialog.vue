<template>
  <el-dialog v-model="dialogState.visible" title="限定输入格式" width="516px" @close="closeDialog">
    <div class="form-container">
      <div class="title">
        <span
          :style="{
            color: '#ccc'
          }"
        >
          选择下方常用表达式或自定义输入
        </span>
        <el-tooltip
          effect="dark"
          content="在左侧提供了常用的表达式，您直接点击选择即可直接使用，或者基于提供表达式修改为自定义的表达式。(自定义表达式不能保存到左侧的常用表达式)"
          placement="top"
        >
          <i class="ri-question-fill"></i>
        </el-tooltip>
      </div>

      <div class="content">
        <div class="left">
          <p
            v-for="(item, index) in menuItems"
            :key="index"
            style="cursor: pointer"
            :index="String(index + 1)"
            :class="{ active: form.regex === item.regex }"
            @click="changeDefaultRegex(item)"
          >
            {{ item.name }}
          </p>
        </div>

        <div class="right">
          <el-form :model="form" label-width="100px" label-position="top">
            <el-form-item label="标题">
              <el-input v-model="form.title" placeholder="请输入标题名称" show-word-limit maxlength="15"></el-input>
            </el-form-item>
            <el-form-item label="正则表达式">
              <template #label>
                正则表达式
                <el-tooltip
                  effect="dark"
                  content="在这里编辑您需要的表达式，开头不能有空格，从外面复制表达式时注意是否有空格。"
                  placement="top"
                >
                  <i class="ri-question-fill"></i>
                </el-tooltip>
              </template>
              <el-input v-model="form.regex" type="textarea" clearable placeholder="请输入正则表达式"></el-input>
            </el-form-item>
            <el-form-item label="错误时提示">
              <template #label>
                错误时提示
                <el-tooltip
                  effect="dark"
                  content="在这里输入错误提示文本，当输入不符合正则表达式时会显示这里的文本。"
                  placement="top"
                >
                  <i class="ri-question-fill"></i>
                </el-tooltip>
              </template>
              <el-input v-model="form.errorMsg" placeholder="请输入有效文本"></el-input>
            </el-form-item>
            <el-form-item label="测试">
              <template #label>
                测试
                <el-tooltip
                  effect="dark"
                  content="在这里输入需要测试的文本，点击确定后会在下方显示测试结果。"
                  placement="top"
                >
                  <i class="ri-question-fill"></i>
                </el-tooltip>
              </template>
              <el-input v-model="form.test" clearable placeholder="测试"></el-input>
            </el-form-item>
            <div
              v-if="form.testResult"
              :style="{
                color: 'rgb(244, 67, 54)',
                marginLeft: '100px'
              }"
            >
              {{ form.testResult }}
              <i class="ri-close-circle-fill"></i>
            </div>
            <div
              v-else-if="!form.testResult && form.test"
              :style="{
                color: 'rgb(0, 195, 69)',
                marginLeft: '100px'
              }"
            >
              测试通过
              <i class="ri-checkbox-circle-fill"></i>
            </div>
          </el-form>
        </div>
      </div>

      <div
        :style="{
          textAlign: 'right',
          marginTop: '20px'
        }"
      >
        <el-button @click="closeDialog">取消</el-button>
        <el-button :disabled="!form.title || !form.regex" type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, watch } from "vue"
import { active } from "sortablejs"
import { v4 as uuidv4 } from "uuid"

const menuItems = [
  {
    name: "字母",
    regex: "^[A-Za-z]*$"
  },
  {
    name: "字母数字",
    regex: "^[A-Za-z0-9]*$"
  },
  {
    name: "数字",
    regex: "^[0-9]*$"
  },
  {
    name: "大写字母",
    regex: "^[A-Z]*$"
  },
  {
    name: "小写字母",
    regex: "^[a-z]*$"
  },
  {
    name: "6个字母",
    regex: "^[a-zA-Z]{6}$"
  },
  {
    name: "6位数字",
    regex: "^[0-9]{6}$"
  },
  {
    name: "IP地址",
    regex: "^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
  },
  {
    name: "邮箱",
    regex: "^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+"
  },
  {
    name: "链接",
    regex: "^(https?:\\/\\/)?([\\da-z\\.-]+)\\.([a-z\\.]{2,6})([\\/\\w \\.-]*)*\\/?$"
  },
  {
    name: "电话",
    regex: "^1[3-9][0-9]{9}$"
  }
]

const form = reactive({
  defaultRegex: "",
  title: "",
  regex: "",
  errorMsg: "",
  test: "",
  testResult: "",
  id: ""
})

const emits = defineEmits(["changeRegex"])
watch(
  () => form.test,
  val => {
    if (val) {
      const reg = new RegExp(form.regex)
      console.log("afs", reg)
      if (!reg.test(val)) {
        console.log("不匹配")
        form.testResult = form.errorMsg
      } else {
        form.testResult = ""
      }
    }
  }
)

const dialogState = reactive({
  visible: false
})

const openDialog = (regex?: Record<string, any>) => {
  dialogState.visible = true
  if (regex) {
    form.title = regex.title
    form.regex = regex.regex
    form.errorMsg = regex.errorMsg
    form.id = regex.id
  }
}

const closeDialog = () => {
  dialogState.visible = false
  //将form里所有值清空
  Object.keys(form).forEach(key => {
    form[key] = ""
  })
}

const handleConfirm = () => {
  emits("changeRegex", {
    title: form.title,
    regex: form.regex,
    errorMsg: form.errorMsg,
    id: form.id || uuidv4()
  })
  closeDialog()
}

const changeDefaultRegex = item => {
  form.regex = item?.regex
  form.title = item?.name
  form.errorMsg = `请输入${item?.name}`
  // 清空测试结果
  form.testResult = ""
  form.test = ""
}

defineExpose({
  openDialog,
  closeDialog
})
</script>

<style lang="less" scoped>
.form-container {
  .title {
    font-weight: 500;
    font-size: 12px;
    color: #81878e;
    margin-bottom: 10px;
  }
  .content {
    border: 1px solid #ebeef5;

    display: flex;
    justify-content: space-between;

    .left {
      padding: 16px 20px;
      flex: 1;
      display: flex;
      flex-direction: column;
      border-right: 1px solid #ebeef5;
      justify-content: space-evenly;
      p {
        font-weight: 500;
        font-size: 14px;
        color: #0a1633;
        margin-bottom: 12px;
      }
    }

    .right {
      padding: 16px 20px;
    }

    .active {
      color: #3860f4 !important;
    }
  }
}
</style>
