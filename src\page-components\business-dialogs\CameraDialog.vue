<template>
  <div>
    <JYCameraDialog
      v-if="ScanMachine === 'JY'"
      ref="JYCameraDialogRef"
      :multiple="props.multiple"
      :submit-loading="props.submitLoading"
      :max-count="props.maxCount"
      :default-rotate="props.defaultRotate"
      @submit="data => emits('submit', data)"
    />
    <LTCameraDialog
      v-if="ScanMachine === 'LT'"
      ref="LTCameraDialogRef"
      :multiple="props.multiple"
      :submit-loading="props.submitLoading"
      :max-count="props.maxCount"
      :default-rotate="props.defaultRotate"
      @submit="data => emits('submit', data)"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue"
import JYCameraDialog from "./JYCameraDialog.vue"
import LTCameraDialog from "./LTCameraDialog.vue"

const ScanMachine = window.__GLOBAL_CONFIG__?.ScanMachine

const emits = defineEmits(["submit"])

const props = defineProps({
  submitLoading: { type: Boolean, default: false },
  multiple: { type: Boolean, default: true },
  maxCount: { type: Number, default: 10 },
  defaultRotate: { type: Number, default: 90 }
})

const JYCameraDialogRef = ref()
const LTCameraDialogRef = ref()

function openDialog() {
  if (ScanMachine === "LT") LTCameraDialogRef.value.openDialog()
  else if (ScanMachine === "JY") JYCameraDialogRef.value.openDialog()
}

function closeDialog() {
  if (ScanMachine === "LT") LTCameraDialogRef.value.closeDialog()
  else if (ScanMachine === "JY") JYCameraDialogRef.value.closeDialog()
}

const cameraLoading = computed(() => {
  if (ScanMachine === "LT") return LTCameraDialogRef.value?.cameraLoading
  else return JYCameraDialogRef.value?.cameraLoading
})

defineExpose({ openDialog, closeDialog, cameraLoading })
</script>
