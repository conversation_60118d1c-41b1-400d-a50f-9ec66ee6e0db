<template>
  <div
    v-if="props.tableData"
    class="common-table-container"
    :class="{
      error: handleError()
    }"
  >
    <!-- <div class="common-table-header">
      <AddButton @click="handleOperation('', 'add')">新增</AddButton>
    </div> -->
    <el-table
      v-if="props?.config?.tableArrangement === 'table'"
      class="common-table"
      :data="props.tableData"
      :border="border"
      :row-key="mode === 'tree' ? 'wsid' : undefined"
      :header-cell-style="{ background: 'rgb(248,249,252)', color: '#030814' }"
    >
      <template v-for="item in props.tableColumns" :key="item">
        <el-table-column
          v-if="item.type === 'selection' || item.type === 'index'"
          :type="item.type"
          :width="50"
          fixed="left"
          :label="item?.label || ''"
        ></el-table-column>
        <el-table-column
          v-if="item.type !== 'index'"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
          :min-width="item.minWidth"
          :sortable="item.sortable"
          :show-overflow-tooltip="true"
          :resizable="true"
          :fixed="item.fixed"
          :align="item.align"
        >
          <template #default="scope">
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="scope.row[item.prop]"
              placement="bottom-start"
              :disabled="!scope.row[item.prop] || item.prop === 'pageOptionProp'"
            >
              <div
                :class="{
                  change:
                    props.differenceData?.[props.tableId][scope.$index]?.[item.prop] &&
                    props.differenceData?.[props.tableId][scope.$index]?.[item.prop] !== '' &&
                    props.differenceData?.[props.tableId][scope.$index]?.[item.prop] !== null
                }"
              >
                <slot :name="item.prop" :row="scope.row">
                  <!-- 单行文本 -->
                  <el-input
                    v-if="item.type === FormItemType.BaseInput"
                    v-model="scope.row[item.prop]"
                    :disabled="item.fieldEditable === 'readOnly' || actionType === 'record'"
                    size="large"
                  ></el-input>

                  <!-- 多行文本域 -->
                  <el-input
                    v-if="item.type === FormItemType.BaseTextarea"
                    v-model="scope.row[item.prop]"
                    :disabled="item.fieldEditable === 'readOnly' || actionType === 'record'"
                    size="large"
                    type="textarea"
                    :placeholder="props.config.placeholder ? props.config.placeholder : `请输入${props.config.name}`"
                  />

                  <!-- 下拉选择 -->
                  <template v-else-if="item.type === FormItemType.BaseSelect">
                    <!-- 可查询的下拉框 -->
                    <el-select
                      v-if="item.fieldLinkage"
                      v-model="scope.row[item.prop]"
                      :disabled="item.fieldEditable === 'readOnly' || actionType === 'record'"
                      size="large"
                      fit-input-width
                      filterable
                      remote
                      reserve-keyword
                      clearable
                      style="width: 100%"
                      :remote-method="val => handleOptionsChange(val, item.prop)"
                      @change="handleCodeChange($event, scope, item)"
                      @click="getDefaultOption(item.prop)"
                    >
                      <template #header>
                        <div class="code-header">
                          <span>编码</span>
                          <span>名称</span>
                          <span>医保灰码</span>
                        </div>
                      </template>
                      <el-option
                        v-for="option in item.options"
                        :key="option.code"
                        style="padding: 0 20px"
                        :label="option.code"
                        :value="option"
                      >
                        <div class="code-content">
                          <span :title="option.value">{{ option.value }}</span>
                          <span style="color: #aaa" :title="option.label">{{ option.label }}</span>
                          <span style="color: #aaa">{{ option?.greyCode ? "是" : "否" }}</span>
                        </div>
                      </el-option>
                    </el-select>

                    <el-select
                      v-else
                      v-model="scope.row[item.prop]"
                      :disabled="item.fieldEditable === 'readOnly' || actionType === 'record'"
                      size="large"
                      filterable
                      clearable
                      fit-input-width
                      style="width: 100%"
                    >
                      <el-option
                        v-for="option in item.options"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      ></el-option>
                    </el-select>
                  </template>

                  <!-- 日期选择 -->
                  <el-date-picker
                    v-else-if="item.type === FormItemType.BaseDate"
                    v-model="scope.row[item.prop]"
                    :disabled="item.fieldEditable === 'readOnly' || actionType === 'record'"
                    size="large"
                    :type="getDateType(item.dateFormat)"
                    :format="item.dateFormat"
                    :value-format="item.dateFormat"
                    :placeholder="item.placeholder ? item.placeholder : `请选择${item.name}`"
                  />

                  <!-- 数字表单 -->
                  <NumberInput
                    v-else-if="item.type === FormItemType.BaseNumber"
                    v-model:value="scope.row[item.prop]"
                    :disabled="item.fieldEditable === 'readOnly' || actionType === 'record'"
                    size="large"
                    :placeholder="item.placeholder ? item.placeholder : `请输入${item.label}`"
                    :unit="item.unit"
                    :precision="item?.allowDecimal ? item.precision : undefined"
                  />

                  <!-- 操作。 获取row 的index -->
                  <div v-else-if="item.prop === 'pageOptionProp' && actionType !== 'record'" style="display: flex">
                    <div
                      v-for="(operation, operationIndex) in props?.config?.tableOperationConfig"
                      :key="operationIndex"
                    >
                      <!-- 上移、置顶 -->
                      <div v-if="operation.key === 'up' || operation.key === 'top'">
                        <div
                          v-if="scope.$index !== 0 && operation.checked"
                          class="table-operation"
                          @click="handleMove(scope, operation.key)"
                        >
                          <el-button
                            v-if="props?.config?.tableOperationIcon"
                            :icon="operation.icon"
                            type="text"
                          ></el-button>
                          <el-button v-else type="primary">{{ operation.label || operation.checkboxLabel }}</el-button>
                        </div>
                      </div>
                      <!-- 下移 -->
                      <div v-else-if="operation.key === 'down'">
                        <div
                          v-if="scope.$index !== props.tableData.length - 1 && operation.checked"
                          class="table-operation"
                          @click="handleMove(scope, 'down')"
                        >
                          <el-button
                            v-if="props?.config?.tableOperationIcon"
                            :icon="operation.icon"
                            type="text"
                          ></el-button>
                          <el-button v-else type="primary">{{ operation.label || operation.checkboxLabel }}</el-button>
                        </div>
                      </div>
                      <!-- 其他 -->
                      <div v-else>
                        <div
                          v-if="operation.checked"
                          class="table-operation"
                          @click="handleTableOperation(operation, scope)"
                        >
                          <el-button
                            v-if="props?.config?.tableOperationIcon"
                            :icon="operation.icon"
                            type="text"
                          ></el-button>
                          <el-button v-else type="primary">{{ operation.label || operation.checkboxLabel }}</el-button>
                        </div>
                      </div>
                    </div>
                  </div>
                </slot>
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
      </template>
      <template #append>
        <div v-if="actionType !== 'record'" class="operation">
          <div v-for="(item, index) in props?.config?.tableGlobalConfiguration" :key="index" class="table-operation">
            <el-button v-if="item.checked" type="primary" @click="handleTableOperation(item)">
              {{ item.label || item.checkboxLabel }}
            </el-button>
          </div>
        </div>
      </template>
    </el-table>

    <!-- 平铺方式table -->
    <div v-if="props?.config?.tableArrangement === 'list'" class="table-form-edit-area">
      <div v-for="(data, index) in props.tableData" :key="index" class="table-item-container">
        <div class="table-item-header">
          <div v-if="props?.config?.tableIndex" class="title">{{ index + 1 }}</div>
          <div v-else class="title" style="width: 10px"></div>
          <!-- 操作按钮 -->
          <div v-if="actionType !== 'record'" class="operation">
            <div v-for="(item, operationIndex) in props?.config?.tableOperationConfig" :key="operationIndex">
              <!-- 上移、置顶 -->
              <div v-if="item.key === 'up' || item.key === 'top'">
                <div
                  v-if="index !== 0 && item.checked"
                  class="table-operation"
                  @click="handleMove({ $index: index, row: data }, item.key)"
                >
                  <el-button v-if="props?.config?.tableOperationIcon" :icon="item.icon" type="text"></el-button>
                  <el-button v-else type="primary">{{ item.label || item.checkboxLabel }}</el-button>
                </div>
              </div>
              <!-- 下移 -->
              <div v-else-if="item.key === 'down'">
                <div
                  v-if="index !== props.tableData.length - 1 && item.checked"
                  class="table-operation"
                  @click="handleMove({ $index: index, row: data }, 'down')"
                >
                  <el-button v-if="props?.config?.tableOperationIcon" :icon="item.icon" type="text"></el-button>
                  <el-button v-else type="primary">{{ item.label || item.checkboxLabel }}</el-button>
                </div>
              </div>
              <!-- 其他 -->
              <div v-else>
                <div
                  v-if="item.checked"
                  class="table-operation"
                  @click="handleTableOperation(item, { $index: index, row: data })"
                >
                  <el-button v-if="props?.config?.tableOperationIcon" :icon="item.icon" type="text"></el-button>
                  <el-button v-else type="primary">{{ item.label || item.checkboxLabel }}</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <TableFormItem
          v-for="(item, columnIndex) in props.tableColumns"
          :key="item.prop"
          :class="{
            change:
              props.differenceData?.[props.tableId][index]?.[item.prop] &&
              props.differenceData?.[props.tableId][index]?.[item.prop] !== '' &&
              props.differenceData?.[props.tableId][index]?.[item.prop] !== null
          }"
          :prop="item.prop"
          :config="item"
          :index="index"
          :table-config="props.config"
          :table-data="props.tableData"
          @handle-change="handleChange"
        />
      </div>
      <!-- 底部操作按钮 -->
      <div v-if="actionType !== 'record'" class="footer-operation">
        <div v-if="actionType !== 'record'" class="operation">
          <div v-for="(item, index) in props?.config?.tableGlobalConfiguration" :key="index" class="table-operation">
            <el-button v-if="item.checked" type="primary" @click="handleTableOperation(item)">
              {{ item.label || item.checkboxLabel }}
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误信息 -->
    <div class="error-table-info">
      {{ displayErrorInfo }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue"
import { useRoute, useRouter } from "vue-router"
import { cloneDeep, uniqBy } from "lodash-es"
import { Plus, Refresh, Bottom, Top, Upload, Delete, RefreshRight } from "@element-plus/icons-vue"
import { AddButton, NumberInput } from "@/base-components"
// import { getOperationIcdInfo, getIcdInfo, getPathologyIcdInfo } from "../../interface"
import TableFormItem from "./TableFormItem.vue"
import type { TableColumnItem, TableColSettingItem } from "@/types"
import { FormItemType, FormItemConfig } from "@/configs"
import { getIcdCode, searchMetaCodeTableFieldApi, clinicalToInsuranceApi } from "@/interfaces"
import axios from "@/interfaces/axios-instance"
import { toastError, Message, getDateType, getOptions } from "@/utils"
const route = useRoute()

interface CommonTableProps {
  tableId: string
  tableColumns: Array<Record<string, any>>

  border?: boolean
  pagination?: boolean
  mode?: "basic" | "tree"

  tableData?: Array<any>
  differenceData?: Record<string, any>
  addAble?: boolean
  selectOptions: Record<string, any>
  errorInfo?: Record<string, any>

  handleOperation: (
    tableData: Array<any> | undefined,
    scope: Record<string, any>,
    type: "add" | "delete",
    tableId: string,
    formConfig?: Array<Record<string, any>>
  ) => any
  handleMove: (tableData: Array<any> | undefined, scope: Record<string, any>, type: "up" | "down" | "top") => any
  updateColumn: (
    tableData: Array<any> | undefined,
    scope: Record<string, any>,
    val: Record<string, any>,
    item: Record<string, any>,
    tableColumns: Array<Record<string, any>>
  ) => any
  formConfig: Array<Record<string, any>>
  config: Record<string, any>
}

const props = withDefaults(defineProps<CommonTableProps>(), {
  addAble: false,
  border: true,
  pagination: true,
  mode: "basic"
})

const actionType = computed(() => {
  return route.query.actionType as string
})

let initColumnsConfig = props.tableColumns.map(item => ({ ...item, id: item.prop || item.type || "", checked: true }))
const customTableColumns = ref<Array<TableColSettingItem>>(initColumnsConfig)

// 表格行 上、下移
const handleMove = (scope, type: "up" | "down" | "top") => {
  props.handleMove(props.tableData, scope, type)
}

// 删除、添加
const handleOperation = (scope, type: "add" | "delete") => {
  console.log(`output->props.config`, props.config)
  if (props.config.default.length >= props.config?.max && type === "add") return
  if (props.config.default.length <= props.config?.min && type === "delete") return
  // 限制不能删除最后一条数据
  // if (props.config.default.length < 2 && type === "delete") return

  if (props.config.default.length >= props.config?.max && type === "add") return
  if (props.config.default.length <= props.config?.min && type === "delete") return

  props.handleOperation(props.tableData, scope, type, props.tableId, props.formConfig)
  if (props.config.tableDataCountField) {
    const max = props.formConfig.find(item => item.prop === props.config.tableDataCountField)?.max
    const min = props.formConfig.find(item => item.prop === props.config.tableDataCountField)?.min

    // if (type === "add" && props.formConfig.find(item => item.prop === props.config.tableDataCountField).default < max)
    //   props.formConfig.find(item => item.prop === props.config.tableDataCountField).default++
    // if (
    //   type === "delete" &&
    //   props.formConfig.find(item => item.prop === props.config.tableDataCountField).default > min
    // )
    //   props.formConfig.find(item => item.prop === props.config.tableDataCountField).default--
  }
}

// 列修改数据（icd10、icd9、icd-o）
const handleCodeChange = (val, scope, item) => {
  // icd编码、名称选择时限制重复数据
  props.config.default[scope.$index][item.prop] = val.value
  const valuesList = cloneDeep(props.config.default)
  // 除当前选择行的其他数据
  valuesList.splice(scope.$index, 1)
  if (valuesList.some(item => Object.values(item)?.includes(val.value))) {
    Message.warning("该数据已存在，请重新选择")
    props.config.default[scope.$index][item.prop] = ""
  } else {
    props.updateColumn(props.tableData, scope, val, item, props.tableColumns)
  }
}

const handleChange = params => {
  handleCodeChange(params.val, params.scope, params.item)
}

/**
 * 更新option
 * @param val
 * @param queryParam
 */
const handleOptionsChange = async (val, prop) => {
  if (val) {
    const config = props.config?.tableColumn?.find(item => item.id === prop)
    props.config.tableColumn.find(item => item.id === prop).options = await getOptions(config, val)
  }
}

// 加载初始option
const getDefaultOption = async prop => {
  const config = props.config?.tableColumn?.find(item => item.id === prop)
  if (!config.options.length && config.collectValueSource) {
    props.config.tableColumn.find(item => item.id === prop).options = await getOptions(config, "")
  }
}

// 医保同步
const formDataList = ref<Array<Record<string, any>>>([])
const medicalInsuranceLoading = ref(false)
const medicalInsuranceTableData = ref<Array<Record<string, any>>>([])
const toSync = async () => {
  if (medicalInsuranceLoading.value) return
  medicalInsuranceLoading.value = true
  formDataList.value = []
  try {
    const res = (
      await clinicalToInsuranceApi({
        formDataList: props.config.default,
        name: props.config?.medicalInsuranceName || "",
        code: props.config?.medicalInsuranceCode || "",
        icdType: props.config?.icdType || ""
      })
    )?.data?.data

    // 转换到对应的医保版table的default中
    cloneDeep(JSON.parse(res || "{}")).forEach(item => {
      let val = {}
      for (const key in item) {
        // 筛选掉病理诊断编码、名称（医保版不需要的字段）
        if (!key.split("_")[1]) {
          const mapField = props.config?.tableFormConfig.find(item => item.prop === key)?.mapField
          // val[`Y_${key}`] = item[key]
          val[mapField] = item[key]
        }
      }
      formDataList.value.push(val)
    })
    console.log(`output->JSON.parse(res || "{}")`, JSON.parse(res || "{}"))
    console.log(`output->formDataList.value`, formDataList.value)
    // 医保版的初始值
    medicalInsuranceTableData.value = cloneDeep(
      props.formConfig.find(item => item.prop === props.config.medicalInsuranceTable)?.default
    )
    // 去重的key
    const filterKey = props.config?.tableFormConfig.find(
      item => item.prop === props.config?.medicalInsuranceCode
    )?.mapField
    // 去重（包括 所有值全为空的 数据）
    medicalInsuranceTableData.value = uniqBy(
      // medicalInsuranceTableData.value.concat(formDataList.value),
      formDataList.value,
      filterKey
    ).filter(
      item =>
        Object.values(item).filter(val => val === "" || val === undefined || val === null).length <
        Object.values(item).length
    )
    // 重新赋值
    props.formConfig.find(item => item.prop === props.config.medicalInsuranceTable).default =
      medicalInsuranceTableData.value
    console.log(`output->medicalInsuranceTableData.value`, medicalInsuranceTableData.value)
    medicalInsuranceLoading.value = false
  } catch (err: any) {
    toastError(err, "操作失败")
    medicalInsuranceLoading.value = false
  }
}

// 病理同步
const pathologicalFormDataList = ref<Array<Record<string, any>>>([])
const pathologicalTableData = ref<Array<Record<string, any>>>([])
const toSyncPathologicalDiagnosis = async () => {
  pathologicalFormDataList.value = []
  pathologicalTableData.value = []

  // 生成转换后的病理诊断数据
  cloneDeep(props.config.default).forEach(item => {
    let val = {}
    for (const key in item) {
      if (key.split("_")[1]) {
        val[`${key.split("_")[1]}`] = item[key]
      }
    }
    pathologicalFormDataList.value.push(val)
  })
  // 原病理诊断数据
  pathologicalTableData.value = cloneDeep(
    props.formConfig.find(item => item.prop === props.config.pathologicalDiagnosisTable)?.default
  )
  // 去重（包括 所有值全为空的 数据）
  pathologicalTableData.value =
    // [
    //   ...new Set(pathologicalTableData.value.concat(pathologicalFormDataList.value).map(item => JSON.stringify(item)))
    // ]
    //   .map(item => JSON.parse(item))
    uniqBy(pathologicalTableData.value.concat(pathologicalFormDataList.value), "C09C").filter(
      item =>
        Object.values(item).filter(val => val === "" || val === undefined || val === null).length <
        Object.values(item).length
    )
  // 重新赋值
  props.formConfig.find(item => item.prop === props.config.pathologicalDiagnosisTable).default =
    pathologicalTableData.value
}

// table 操作
const handleTableOperation = (item: Record<string, any>, scope?: Record<string, any>) => {
  switch (item.key) {
    case "add":
      return handleOperation(scope, "add")
    case "sync-pathology":
      return toSyncPathologicalDiagnosis()
    case "sync-medicalInsurance":
      return toSync()
    case "delete":
      return handleOperation(scope, "delete")

    default:
      return
  }
}

const handleError = () => {
  if (Object.keys(props.errorInfo || {}).length) {
    const tableProps = props.config.tableFormConfig.map(item => item.prop).join(",")
    return Object.keys(props.errorInfo || {}).some(key => tableProps.includes(key))
  }
}

const displayErrorInfo = computed(() => {
  if (Object.keys(props.errorInfo || {}).length) {
    let errorStr = [] as any
    const tableProps = props.config.tableFormConfig.map(item => item.prop).join(",")
    Object.keys(props.errorInfo || {}).forEach(key => {
      if (tableProps.includes(key)) {
        errorStr.push(props.errorInfo?.[key])
      }
    })
    return errorStr.join("，")
  } else {
    return ""
  }
})
</script>

<style lang="less">
.el-select-dropdown {
  width: auto !important;
}
.common-table-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  //   overflow-y: auto;

  .common-table-header {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;

    .table-setting-btn {
      display: flex;
      align-items: center;
      color: #666;
      .table-setting-text {
        margin-left: 4px;
        font-size: 14px;
      }
    }
  }
  .common-table {
    flex: 1;
    overflow-y: hidden;
    :deep(.el-scrollbar__view) {
      height: 100%;
    }
  }

  .table-pagination {
    margin-top: 20px;
    align-self: flex-end;
  }
}

:deep(.el-table) tr {
  //   background-color: #dadfe7;
}

:deep(.el-table) th {
  background-color: #dadfe7 !important;
}
.operation {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 10px;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  font-size: 14px;
  width: calc(100% - 32px);
  min-width: 20vw;
  span {
    width: 33%;
    text-align: left;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
.code-content {
  display: flex;
  justify-content: space-between;
  align-items: center;

  span {
    width: 33%;
    text-align: left;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
.table-form-edit-area {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  min-height: 40px;
  align-items: center;
  // border: 1px solid #ccc;
}
.table-item-container {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  min-height: 40px;
  align-items: center;
  // border: 1px solid #ccc;
  margin-bottom: 1px;
}

.table-item-header {
  background-color: #f5f7fa;
  width: calc(100% - 40px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 20px;
  color: rgba(10, 22, 51, 0.85) !important;

  .operation {
    display: flex;
    justify-content: space-between;
  }
}

.table-operation {
  color: #409eff;
  cursor: pointer;
  margin-left: 5px;
}

.common-table-container {
  color: rgba(10, 22, 51, 0.85) !important;
}

.change {
  color: red !important;
  :deep(.el-input) {
    :deep(.el-input__inner) {
      color: red !important;
      -webkit-text-fill-color: red !important;
    }
  }
}
:deep(.el-select-dropdown__item) {
  padding: 0 20px !important;
}
</style>

<style scoped lang="less">
.error-table-info {
  display: none;
}
.error {
  border: 1px solid #f56c6c;
  border-radius: 4px;
  :deep(.el-input) {
    border: 1px solid transparent !important;
    border-radius: 4px;
  }

  &:hover {
    .error-table-info {
      display: block;
      position: absolute;
      bottom: -30px;
      color: #f56c6c;
    }
  }
}
</style>
