<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
        />

        <el-form-item label="出院科室">
          <el-select v-model="searchFormState.outHospitalDeptWsid" style="width: 200px">
            <el-option
              v-for="item in globalOptionsStore.departmentOptions"
              :key="item.value as string"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <DaterangeFormItem v-model:model-value="searchFormState.outHospitalDatetime" label="出院时间" />

        <el-form-item label="主治医师">
          <el-input v-model="searchFormState.attendingDoctor" style="width: 240px" />
        </el-form-item>
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="waitingCatalogTableRef"
        table-id="medicalInsuranceWaitingCatalogTableIdent"
        :table-columns="waitingCatalogControlColumns"
        :request-api="getCatalogListData"
        :request-params="waitingCatalogParams"
        :data-callback="dataCallback"
      >
        <template #operation="{ row }">
          <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.Catalog)" @click="catalogItem(row)">
            编辑
          </TableButton>
          <!-- <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.View)" @click="viewItem(row)">
            查看
          </TableButton> -->
        </template>
      </CommonTable>
    </template>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref, computed } from "vue"
import { useRouter } from "vue-router"
import {
  SearchContainer,
  PatientFilterFormItem,
  CommonTable,
  TableButton,
  DaterangeFormItem,
  PageContainer
} from "@/base-components"
import { TabsRouter } from "@/page-components"
import { waitingCatalogControlColumns, tabsRouterList, menuId } from "./config"
import { getCatalogListData } from "./interface"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { useGlobalOptionsStore, useUserStore } from "@/stores"
import { formatDatetime } from "@/utils"

const { hasOperationPermission } = useUserStore()

const globalOptionsStore = useGlobalOptionsStore()

/* ======================== 搜索相关数据及方法 ======================== */
const searchFormState = reactive({
  patientFilterValue: "",
  patientFilterProp: "patientName",
  outHospitalDeptWsid: "",
  outHospitalDatetime: "",
  attendingDoctor: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

const waitingCatalogParams = computed(() => {
  return { ...searchParams, catalogStatus: "NOT_CATALOG", scenarioType: "MEDICAL_INSURANCE_IN" }
})

/* ======================== 表格相关方法 ======================== */

function dataCallback(data: Array<Record<string, any>>) {
  return data.map(item => ({
    ...item,
    inHospitalDatetime: formatDatetime(item.inHospitalDatetime),
    outHospitalDatetime: formatDatetime(item.outHospitalDatetime)
  }))
}

const waitingCatalogTableRef = ref()

const router = useRouter()
// 查看
const viewItem = row => {
  router.push({
    path: "/coding/medical-insurance/detail",
    query: {
      inpNo: row.inpNo
    }
  })
}

// 编目
const catalogItem = row => {
  router.push({
    path: "/catalog-preview",
    query: {
      appScenario: "MISL_ENCODE",
      businessDataWsid: row.inpNo,
      returnUrl: "/coding/medical-insurance/pending"
    }
  })
}
</script>
