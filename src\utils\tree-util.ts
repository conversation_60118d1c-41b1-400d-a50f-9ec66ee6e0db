import { cloneDeep } from "lodash-es"

// 格式化病案详情的原始数据，为格式化tree做准备
export const formatRecordTree = (__treeInfo: { mrClassTree: any[]; noCatalogDocuments: any[] }) => {
  if (!__treeInfo) return []
  const treeInfo = cloneDeep(__treeInfo)
  if (!treeInfo?.noCatalogDocuments?.length) {
    return formatRecordDocumentTree(treeInfo?.mrClassTree)
  }
  // push noCatalogDocuments会导致内存变化，组件props中的值也会引发变化会重复触发，故此处做判断防止无限循环触发watch
  if (treeInfo.mrClassTree.find(treeNode => treeNode.wsid === "WSID_DMCA_OTHER")) return treeInfo.mrClassTree
  // 将未分类的档案放入树形列表
  const filePageCount = treeInfo.noCatalogDocuments.reduce((accumulator, curDoc) => {
    return accumulator + curDoc.filePage
  }, 0)
  treeInfo?.noCatalogDocuments.forEach(doc => {
    doc.name = doc.title
    doc.type = "FILE"
    doc.isSupplement = doc?.isSupplement || 0
  })
  treeInfo.mrClassTree.push({
    wsid: "WSID_DMCA_OTHER",
    name: `其他`,
    children: treeInfo?.noCatalogDocuments,
    documents: treeInfo?.noCatalogDocuments,
    type: "CATALOGUE",
    filePage: filePageCount,
    renamable: false
  })
  return formatRecordDocumentTree(treeInfo.mrClassTree)
}

/**
 *
 * @param tree 包含childrenCatalogues和childrenMrClass字段的列表
 * @returns
 */
export const formatRecordDocumentTree = (tree: Array<any>) => {
  if (!tree?.length) return []
  tree.forEach(item => {
    if (item.wsid !== "WSID_DMCA_OTHER") {
      // 构建树形children
      item.children = item?.childrenCatalogues || []
      if (item.childrenMrClass) item.children = item.children.concat(item.childrenMrClass)
      // 兼容系统配置 病案目录树是否显示文书分类
      if (item.documents) item.children = item.children.concat(item.documents)

      // children继续构建
      item.children.forEach(child => {
        // 存在目录或者文档类型
        if (child.childrenCatalogues || child.childrenMrClass) {
          return formatRecordDocumentTree(item.children)
        } else if ((child.type === "MR_CLASS" || child.type === "CATALOGUE") && child?.documents?.length) {
          child.children = child.documents.map(document => {
            return {
              name: document.title,
              type: "FILE",
              isSupplement: document?.isSupplement || 0,
              ...document
            }
          })
        } else if (!child.type) {
          // 兼容系统配置 病案目录树是否显示文书分类  没有MR_CLASS结构
          child.type = "FILE"
        }
      })
    }
  })
  return tree
}

/**
 * 设置树节点指定key的value
 * @param tree tree数据
 * @param wsid treeNode的wsid
 * @param key 需要改变数据的key
 * @param value 需要改变的数据值
 */
export const setTreeNodeValue = (tree: any[], wsid: string, key: string, value: any) => {
  for (const treeNode of tree) {
    // 如果是当前node
    if (treeNode.wsid === wsid) {
      treeNode[key] = value
      break
      // 不是当前node，则遍历其children
    } else if (treeNode.children?.length) {
      treeNode.children = setTreeNodeValue(treeNode.children, wsid, key, value)
    }
  }
  return tree
}

// 获取pdf根节点列表
export const getFileNodeList = (tree: any[]) => {
  const getFileNode = (tree: any[]) => {
    for (const treeNode of tree) {
      // file类型则push
      if (treeNode.type === "FILE") {
        fileNodeList.push(treeNode)
        // 非file类型且有children则继续遍历
      } else if (treeNode.children?.length) {
        getFileNode(treeNode.children)
      }
    }
  }
  const fileNodeList: any[] = []
  getFileNode(tree)
  return fileNodeList
}

// 获取指定属性的pdf根节点列表
export const getSpecifiedPropFileNodeList = (tree: any[], key: string, value: string | number | undefined) => {
  const getFileNode = (tree: any[]) => {
    for (const treeNode of tree) {
      // file类型则push
      if (treeNode.type === "FILE" && treeNode[key] === value) {
        fileNodeList.push(treeNode)
        // 非file类型且有children则继续遍历
      } else if (treeNode.children?.length) {
        getFileNode(treeNode.children)
      }
    }
  }
  const fileNodeList: any[] = []
  getFileNode(tree)
  return fileNodeList
}

// 设置节点value
export const setSpecifiedPropFileNodes = (tree: any[], key: string, value: string | number | undefined | boolean) => {
  for (const treeNode of tree) {
    // file类型则push
    if (treeNode.type === "FILE") {
      // 将treeNode里面key设置为value
      treeNode[key] = value
      console.log(treeNode)
      // 非file类型且有children则继续遍历
    } else if (treeNode.children?.length) {
      setSpecifiedPropFileNodes(treeNode.children, key, value)
    }
  }

  return tree
}

// 树形结构扁平化
export function flattenTree(treeArray: Array<CommonTreeItem>) {
  return treeArray.reduce((result, currentItem) => {
    const { children, ...rest } = currentItem
    // 其他属性构成的对象推入result, 如果有children值则再次调用
    return result.concat(rest, children && children.length ? flattenTree(children) : [])
  }, [])
}

// 获取树形节点的文字
export const getNodeTooltipContent = (node, data, showFilePage: boolean) => {
  // 兼容系统配置 病案目录树是否显示文书分类  没有MR_CLASS结构
  let content =
    node?.label?.replace(".pdf", "").replace(".png", "") || data?.title?.replace(".pdf", "").replace(".png", "")
  if (showFilePage && data.type === "FILE") {
    content += `（${data.filePage}页）`
  }
  return content
}

// 获取分类的页数
export const getCatalogPage = data => {
  return data.type !== "FILE" ? `（${data.filePage || 0}页）` : ""
}
