<template>
  <div class="upload-avatar-dialog">
    <DialogContainer
      v-model:visible="uploadState.visible"
      class="upload-dialog"
      :title="props.title"
      :confirm-callback="confirm"
      :width="530"
      :confirm-loading="uploadState.loading"
    >
      <div class="cropped-container">
        <!-- 裁剪框 -->
        <div class="cropped-box">
          <VueCropper
            ref="cropper"
            class="cropper"
            output-type="png"
            :img="props.imgSrc"
            :output-size="1"
            :can-scale="true"
            :auto-crop="true"
            :center-box="true"
            :info="true"
            :fixed="props.fixedNumber ? true : false"
            :fixed-number="props.fixedNumber"
            @real-time="realTime"
          />
          <div class="actions">
            <span @click.stop="rotate('left')"><i class="icon ri-anticlockwise-line"></i></span>
            <span @click.stop="rotate('right')"><i class="icon ri-clockwise-line"></i></span>
            <span @click.stop="zoom(3)"><i class="icon ri-zoom-in-line"></i></span>
            <span @click.stop="zoom(-3)"><i class="icon ri-zoom-out-line"></i></span>
          </div>
        </div>

        <!-- 预览框 -->
        <div class="preview-box">
          <div
            :style="{
              width: uploadState.preview.w + 'px',
              height: uploadState.preview.h + 'px',
              overflow: 'hidden',
              borderRadius: props.previewType === 'circle' ? '50%' : '0%'
            }"
          >
            <div :style="uploadState.preview.div">
              <img :src="uploadState.preview.url" :style="uploadState.preview.img" />
            </div>
          </div>
        </div>
      </div>
    </DialogContainer>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import { VueCropper } from "vue-cropper"
import { DialogContainer } from "@/base-components"
import "vue-cropper/dist/index.css"

const cropper = ref()

interface PropsType {
  imgSrc: string
  title: string
  previewType?: "circle" | "square"
  fixedNumber?: null | Array<number>
}

const props = withDefaults(defineProps<PropsType>(), {
  title: "裁剪",
  previewType: "circle",
  fixedNumber: null
})

const uploadState = reactive({
  visible: false,
  loading: false,
  preview: {
    html: "",
    w: 0,
    h: 0,
    url: "",
    img: {},
    div: {}
  }
})

defineExpose({ openDialog, closeDialog })

// 打开弹窗
function openDialog() {
  uploadState.visible = true
}

// 关闭弹窗
function closeDialog() {
  uploadState.loading = false
  uploadState.visible = false
}

// 实时预览
function realTime(data) {
  uploadState.preview = data
}

// 旋转
function rotate(direction) {
  if (!cropper.value) return
  if (direction === "left") cropper.value.rotateLeft()
  else if (direction === "right") cropper.value.rotateRight()
}

// 缩放  scale>0: 放大  scale<0：缩小
function zoom(scale) {
  if (!cropper.value) return
  cropper.value.changeScale(scale)
}

// 提交
const emits = defineEmits(["submitCropper"])
function confirm() {
  uploadState.loading = true
  cropper.value.getCropBlob(data => {
    emits("submitCropper", data)
  })
}
</script>

<style lang="less" scoped>
.upload-avatar-dialog {
  .upload-dialog {
    :deep(.el-dialog__body) {
      padding: 20px 20px 0px;
    }
  }
  .cropped-container {
    display: flex;
    column-gap: 30px;
    .cropped-box {
      display: flex;
      flex-direction: column;
      .cropper {
        width: 200px;
        height: 200px;
      }
      .actions {
        display: flex;
        justify-content: center;
        margin-top: 10px;
        span {
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 3px;
          width: 30px;
          line-height: 30px;
          border-radius: 50%;
          background: #7c7c7c;
          font-size: 20px;

          &:hover {
            background: #3c4651;
          }
        }
        .icon {
          font-size: 20px;
          color: #fff;
        }

        .ri-anticlockwise-line {
          margin-left: -2px;
        }
        .ri-clockwise-line {
          margin-left: 4px;
        }
      }
    }
    .preview-box {
      width: 200px;
      height: 200px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid #ccc;
      overflow: hidden;
      img {
        object-fit: contain;
      }
    }
  }
}
</style>
