<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchForm
        :form-config="dataOriginSearchFormConfig"
        :form-state="searchFilterForm"
        @query-btn-click="handleQuery"
        @reset-btn-click="handleReset"
      />
    </template>

    <template #table>
      <CommonTable
        ref="dataOriginTableRef"
        :table-columns="DataOriginTableColumns"
        :request-api="getDataSourcesList"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <template #header>
          <AddButton @click="showAddForm">新增</AddButton>
        </template>
        <template #createdDatetime="{ row }">
          {{ formatDatetime(row.createdDatetime) }}
        </template>
        <template #operation="{ row }">
          <TableButton @click="showEditForm(row)">编辑</TableButton>
          <TableButton @click="handleToggle(row)">
            {{ row.status ? "禁用" : "启用" }}
          </TableButton>
          <TableButton @click="handleDelete(row)">删除</TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>

  <DialogContainer
    v-model:visible="dialogVisible"
    :title="actionType === 'edit' ? '修改数据源' : '添加数据源'"
    :width="490"
  >
    <el-form
      ref="dialogFormRef"
      label-position="right"
      label-width="120px"
      label-suffix="："
      :model="dataOriginFormState"
      :rules="dataOriginFormRules"
    >
      <CommonSelectFormItem
        v-model="dataOriginFormState.dbType"
        label="数据源类型"
        prop="dbType"
        :options="dbTypeOptions"
      />

      <CommonSelectFormItem
        v-model="dataOriginFormState.systemWsid"
        label="厂商系统"
        prop="systemWsid"
        :options="systemVendorOptions"
      />

      <CommonInputFormItem v-model="dataOriginFormState.name" label="数据源名称" prop="name" />

      <CommonInputFormItem v-model="dataOriginFormState.url" label="URL" prop="url" />

      <CommonInputFormItem v-model="dataOriginFormState.username" label="用户名" prop="username" />

      <CommonInputFormItem v-model="dataOriginFormState.password" label="密码" prop="password" />

      <CommonInputFormItem v-model="dataOriginFormState.dbName" label="数据库名称" prop="dbName" />

      <CommonInputFormItem v-model="dataOriginFormState.jdbcUrlParams" label="连接参数" prop="jdbcUrlParams" />

      <el-form-item label="超时时间(秒)：" prop="timeout">
        <el-input v-model.trim="dataOriginFormState.timeout" maxlength="5" type="number" placeholder="请输入超时时间" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleClose()">取消</el-button>
      <el-button type="primary" :loading="testLoading" @click="handleTest()">测试数据源</el-button>
      <el-button type="primary" :loading="confirmLoading" @click="handleSave()">保存</el-button>
    </template>
  </DialogContainer>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import {
  PageContainer,
  DialogContainer,
  CommonTable,
  AddButton,
  TableButton,
  CommonSelectFormItem,
  CommonInputFormItem
} from "@/base-components"
import { TabsRouter, SearchForm } from "@/page-components"
import { useTableSearch, useFormSetting, useTableOperation, useCommonOptions } from "@/hooks"
import { SystemPrompt, SystemAlert, Message, toastError, formatDatetime } from "@/utils"

import { getCollectSystemList } from "../collect-way/interface"
import {
  tabsRouterList,
  DataOriginTableColumns,
  dataOriginFormRules,
  dbTypeOptions,
  dataOriginSearchFormConfig
} from "./config"
import {
  getDataSourcesList,
  addDataSources,
  dataSourcesConnectTest,
  modifyDataSources,
  modifyDataSourcesStatus,
  checkDataSourceUsed
} from "./interface"
import type { IDataSourceFormData } from "./interface"
import type { FormInstance } from "element-plus"

/* ======================== 页面加载时获取选项 ======================== */

// 获取所有厂商系统选项
const { options: systemVendorOptions } = useCommonOptions({
  getOptionsApi: getCollectSystemList,
  labelAlias: "systemName",
  valueAlias: "wsid"
})

/* ======================== 搜索 ======================== */

const searchFilterForm = reactive({
  name: "",
  dbType: "",
  status: ""
})
const { searchParams, handleQuery, handleReset } = useTableSearch(searchFilterForm)

/* ======================== 表格 ======================== */

const dataOriginTableRef = ref<InstanceType<typeof CommonTable>>()
const dialogFormRef = ref<FormInstance>()
const dialogVisible = ref(false)
const actionType = ref<"add" | "edit">("add")
const testLoading = ref(false)
const confirmLoading = ref(false)

const initFormValues = {
  wsid: "",
  dbType: "",
  name: "",
  url: "",
  username: "",
  password: "",
  jdbcUrlParams: "",
  dbName: "",
  timeout: "",
  systemWsid: ""
}

const dataOriginFormState = reactive({ ...initFormValues })

const { showAddForm, showEditForm } = useFormSetting(dataOriginFormState, initFormValues, actionType, dialogVisible)

const { confirmAdd, confirmEdit, confirmToggle, confirmDelete } = useTableOperation<IDataSourceFormData>(
  dataOriginFormState,
  dialogVisible,
  {
    addApi: addDataSources,
    editApi: modifyDataSources,
    toggleApi: modifyDataSourcesStatus,
    deleteApi: (wsid: string) => modifyDataSourcesStatus(wsid, "DEL")
  },
  dataOriginTableRef,
  confirmLoading
)

// 关闭编辑弹窗
function handleClose() {
  dialogVisible.value = false
}

function dataCallback(data: Array<Record<string, any>>) {
  return data.map(item => ({
    ...item,
    systemName: systemVendorOptions.value?.find(data => data.value === item.systemWsid)?.label
  }))
}

// 测试数据源
function handleTest() {
  dialogFormRef.value?.validate(valid => {
    if (valid) {
      testLoading.value = true
      dataSourcesConnectTest(dataOriginFormState)
        .then(res => {
          if (res.data?.data) Message.success("测试成功")
          else Message.error("测试失败")
        })
        .catch(err => toastError(err, "测试失败"))
        .finally(() => (testLoading.value = false))
    }
  })
}

// 保存编辑
function handleSave() {
  dialogFormRef.value?.validate(valid => {
    if (!valid) return
    if (actionType.value === "edit") confirmEdit()
    else confirmAdd()
  })
}

// 切换启用&禁用状态
const handleToggle = row => {
  if (row.status) {
    SystemPrompt(`您确定要禁用${row.name}`).then(() => confirmToggle(row.wsid, "DISABLE"))
  } else {
    SystemPrompt(`是否确定启用${row.name} `).then(() => confirmToggle(row.wsid, "ENABLE"))
  }
}

// 删除
function handleDelete(row) {
  checkDataSourceUsed(row.wsid).then(isUsed => {
    if (isUsed) {
      SystemAlert("此数据源在采集配置方案中被使用，不能删除!")
    } else {
      SystemPrompt(`您确定要删除${row.name} ，删除后关联该数据源所有配置将不能使用`).then(() => confirmDelete(row.wsid))
    }
  })
}
</script>
