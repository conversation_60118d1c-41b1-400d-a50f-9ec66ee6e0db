<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <DaterangeFormItem v-model:model-value="searchFormState.billDate" label="对账日期" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="transactionTableRef"
        :table-columns="transactionColumns"
        :request-api="getTradeBillList"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <template #header>
          <div style="margin-bottom: 12px">
            <ComplexExportButton
              v-if="hasOperationPermission(menuId, MenuOperationEnum.Export)"
              :table-info="transactionTableRef?.tableState"
              :export-api="exportTradeBill"
              row-id-key="id"
              :search-filters="searchParams.filters"
              :sorts="'-billDate'"
            />
          </div>
        </template>
        <template #operation="{ row }">
          <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.Reconciliation)" @click="viewDetail(row)">
            对账明细
          </TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>

  <DialogContainer
    v-model:visible="tradeBillDetailVisible"
    :title="'对账明细'"
    :width="1000"
    :close-on-click-modal="false"
    :no-footer="true"
    :class="'detail'"
  >
    <CommonTable
      ref="tradeBillDetailTableRef"
      :table-columns="transactionDetailColumns"
      :request-api="getTradeBillDetail"
      :request-params="tradeBillDetailParams"
      :data-callback="dataCallback"
    >
      <template #header>
        <BatchOperationButton
          :icon="Download"
          :disabled="!selectedBillDetailRows.length"
          @click="handleExportTradeDetail"
        >
          导出
        </BatchOperationButton>
      </template>
    </CommonTable>
  </DialogContainer>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from "vue"
import { Download } from "@element-plus/icons-vue"
import {
  SearchContainer,
  CommonTable,
  TableButton,
  ComplexExportButton,
  DialogContainer,
  DaterangeFormItem,
  BatchOperationButton,
  PageContainer
} from "@/base-components"
import { TabsRouter } from "@/page-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { useUserStore } from "@/stores"
import { formatDate, formatDatetime,  Message, toastError, downloadFile } from "@/utils"
import { getTradeBillList, getTradeBillDetail, exportTradeBill, exportTradeBillDetail } from "../interface"
import { transactionColumns, transactionDetailColumns, tabsRouterList, menuId } from "./config"

const { hasOperationPermission } = useUserStore()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  billDate: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

const tradeBillDetailParams = ref({ filters: "" })


/* ======================== 表格相关方法 ======================== */

const transactionTableRef = ref<InstanceType<typeof CommonTable>>()

const tradeBillDetailTableRef = ref<InstanceType<typeof CommonTable>>()

const selectedBillDetailRows = computed(() => tradeBillDetailTableRef.value?.tableState.selectedRows ?? [])

const billType = {
  PAY: "支付",
  REFUND: "退款"
}

const channel = {
  WECHAT: "微信",
  ALIPAY: "支付宝"
}

const dataCallback = (data: Array<Record<string, any>>) => {
  data.forEach(item => {
    item.outHospitalDatetime = formatDatetime(item.outHospitalDatetime)
    item.billDateStr = formatDate(item.billDate)
    item.tradeDate = formatDatetime(item.tradeDate)
    item.billType = billType[item.billType]
    item.channelStr = channel[item.channel]
  })
  return data.map(item => ({
    ...item
  }))
}

const tradeBillDetailVisible = ref(false)

// 对账明细
const viewDetail = row => {
  tradeBillDetailVisible.value = true
  tradeBillDetailParams.value = { filters: `billDate=${row.billDate},channel=${row.channel}` }
}

// 导出对账明细记录
function handleExportTradeDetail() {
  const filters = "wsids=" + selectedBillDetailRows.value.map(item => item.id).join(";")
  exportTradeBillDetail({ filters })
    .then(res => {
      downloadFile({ fileData: res.data, fileType: "application/vnd.ms-excel", fileName: "导出文件.xlsx" })
      Message.success("导出文件成功")
    })
    .catch(err => {
      toastError(err, "导出文件失败")
    })
}
</script>

<style lang="less" scoped>
.detail {
  .common-table-container {
    height: 500px !important;
  }
}
</style>
