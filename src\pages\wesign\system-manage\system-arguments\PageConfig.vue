<template>
  <PageContainer>
    <TabsRouter :tabs-config="tabsRouterList" />
    <div v-loading="state.loading" class="page-config">
      <el-form ref="form">
        <el-form-item label="系统logo：" class="flex-wrap">
          <div class="img-container" @click="handleUpload('logo')">
            <img v-if="logoSrc" :src="logoSrc" />
            <div v-else class="img-container-desc">
              <i class="ri-add-line add-btn"></i>
              <span>上传图片</span>
            </div>
          </div>
          <div class="tips">支持类型JPG、JPEG、PNG、SVG</div>
        </el-form-item>

        <el-form-item label="favicon：" class="flex-wrap">
          <div class="img-container favicon-img-container" @click="handleUpload('favicon')">
            <img v-if="faviconSrc" :src="faviconSrc" />
            <div v-else class="img-container-desc">
              <i class="ri-add-line add-btn"></i>
              <span>上传图片</span>
            </div>
          </div>
          <div class="tips">支持类型JPG、JPEG、PNG，尺寸12*12</div>
        </el-form-item>

        <el-form-item label="主题颜色：">
          <el-color-picker v-model="formData.color" color-format="rgb" show-alpha></el-color-picker>
        </el-form-item>
        <el-form-item label="机构名称：">
          <el-input v-model="formData.organizationName"></el-input>
        </el-form-item>
        <el-form-item label="机构代码：">
          <el-input v-model="formData.organizationCode"></el-input>
        </el-form-item>
        <el-form-item label="联系电话：">
          <el-input v-model="formData.tell"></el-input>
        </el-form-item>
        <el-form-item label="版权信息：">
          <el-input v-model="formData.copyright"></el-input>
        </el-form-item>
        <el-form-item label=" ">
          <div class="btn-container">
            <el-button type="primary" @click="save">保存</el-button>
            <el-button @click="reset">重置</el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </PageContainer>

  <!-- 裁剪弹窗 -->
  <CropperDialog
    ref="cropperDialogRef"
    preview-type="square"
    :title="uploadState.type === 'logo' ? '系统logo' : 'favicon'"
    :img-src="uploadState.src"
    :auto-crop-height="uploadState.height"
    :auto-crop-width="uploadState.width"
    :fixed-number="uploadState.type === 'logo' ? null : [1, 1]"
    @submit-cropper="submitCropper"
  />
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from "vue"
import { PageContainer } from "@/base-components"
import { TabsRouter, CropperDialog } from "@/page-components"
import { tabsRouterList } from "./config"
import { getImage, getImageBase64, uploadSystemFile, querySystemConfig, updateSystemConfig } from "@/interfaces"
import { useResourceStore, useSystemStore } from "@/stores"
import { selectFile, Message, toastError, blobToBase64 } from "@/utils"

const formData = reactive({
  logo: "",
  favicon: "",
  color: "",
  organizationName: "",
  organizationCode: "",
  tell: "",
  copyright: ""
})

const state = reactive({
  loading: false,
  data: [] as Array<Record<string, any>>
})

const systemStore = useSystemStore()
const resourceStore = useResourceStore()

const logoSrc = ref("")
const faviconSrc = ref("")

onMounted(() => {
  querySystemConfig({ type: "PAGE" }).then(res => {
    res.data.data.forEach(item => {
      if (item.value) {
        formData[item.key] = item.value
        if (formData["color"].indexOf("rgb") === -1) {
          formData["color"] = "rgb(" + formData["color"] + ")"
        }
        //加载图片
        if (item.key === "logo") {
          getImage(item.value).then(data => {
            logoSrc.value = data
          })
        }
        if (item.key === "favicon") {
          getImage(item.value).then(data => {
            faviconSrc.value = data
          })
        }
      }
    })
  })
})

/*======================== 上传裁剪 ========================*/
const cropperDialogRef = ref()
const uploadState = reactive({
  type: "",
  src: "",
  width: 100,
  height: 100
})

// 选择图片（打开裁剪弹窗）
const handleUpload = async (type: string) => {
  if (!cropperDialogRef.value) return
  uploadState.type = type
  selectFile(".jpg,.jpeg,.png,.svg", 1024 * 1024 * 30).then(async file => {
    const reader = new FileReader()
    reader.readAsDataURL(file as Blob)
    reader.onload = function (e) {
      const img = new Image()
      img.src = e.target?.result as string
      img.onload = async function () {
        uploadState.width = img.width
        uploadState.height = img.height
        uploadState.src = (await blobToBase64(file as Blob)) as string
        cropperDialogRef.value.openDialog()
      }
    }
  })
}

// 上传图片（裁剪完成）
const submitCropper = async file => {
  if (uploadState.type === "logo") logoSrc.value = (await blobToBase64(file as Blob)) as string
  else faviconSrc.value = (await blobToBase64(file as Blob)) as string
  uploadSystemFile({ file: file })
    .then(res => {
      formData[uploadState.type] = res.data.data.fileWsid
      Message.success("上传图片成功")
      if (cropperDialogRef.value) cropperDialogRef.value.closeDialog()
    })
    .catch(error => {
      toastError(error, "上传图片失败")
    })
}

/*======================== 表单底部按钮操作 ========================*/
// 重置
const reset = () => {
  for (let key in formData) {
    formData[key] = ""
    state.data.push({ key: key, value: formData[key] })
  }
}

// 保存配置
const save = async () => {
  if (state.loading) return
  state.loading = true
  for (let key in formData) {
    if (key === "color") {
      //只传数值给后台
      state.data.push({ key: key, value: formData[key].slice(5, formData[key].length - 1) })
    } else {
      state.data.push({ key: key, value: formData[key] })
    }
  }

  updateSystemConfig(state.data)
    .then(async () => {
      Message.success("保存成功")
      if (formData.logo) {
        const logoBase = await getImageBase64(formData.logo)
        resourceStore.setLogo(logoBase)
      }
      if (formData.favicon) {
        const faviconBase = await getImageBase64(formData.favicon)
        resourceStore.setFavicon(faviconBase)
      }
    })
    .catch(error => {
      toastError(error, "保存失败")
    })
    .finally(() => {
      state.loading = false
      state.data = []
    })
}
</script>

<style lang="less" scoped>
.page-config {
  padding-top: 30px;
  position: relative;
  overflow-y: auto;
  :deep(.el-input) {
    width: 50%;
  }
  :deep(.el-form-item__label) {
    justify-content: space-between;
    width: 85px;
  }

  :deep(.el-form-item) {
    margin-bottom: 15px;
  }
}

.common-page-container {
  position: relative;
}

.flex-wrap {
  :deep(.el-form-item__content) {
    flex-direction: column;
    align-items: start;
  }
}

.img-container {
  border: 1px dashed var(--el-border-color);
  padding: 5px;
  line-height: 1;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 70px;
  min-height: 70px;
  box-sizing: border-box;
  background: rgba(56, 96, 244, 0.2);

  .img-container-desc {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  img {
    object-fit: contain;
  }

  .add-btn {
    width: 24px;
    height: 24px;
    font-size: 24px;
  }
}

.favicon-img-container {
  width: 70px;
  height: 70px;
  img {
    width: 100%;
    height: 100%;
  }
}
</style>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}
</style>
