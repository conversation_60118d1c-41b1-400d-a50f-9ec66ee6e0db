import axios from "@/interfaces/axios-instance"

/* ================== 科室授权 =================== */
// 科室授权借阅分页列表
export const getDeptAuthorizationListApi = (params: IPaginationRequestParams) => {
  return axios({
    method: "get",
    url: `/api/document/authorization/dept`,
    params
  })
}

// 根据科室id获取已授权科室列表
export const getBorrowDeptAuthorizationDeptsApi = (params) => {
  return axios({
    method: "get",
    url: `/api/document/authorization/dept/depts`,
    params: { deptWsid: params.deptWsid }
  }).then(res => {
    return res.data?.data || []
  })
}

// 根据科室id获取已授权用户列表
export const getBorrowDeptAuthorizationUsersApi = (params) => {
  return axios({
    method: "get",
    url: `/api/document/authorization/dept/users`,
    params: { deptWsid: params.deptWsid }
  }).then(res => {
    return res.data?.data || []
  })
}

/* ================== 病案号授权 =================== */

// 病案号授权分页列表
export const getMrnoAuthorizationListApi = (params: IPaginationRequestParams) => {
  return axios({
    method: "get",
    url: `/api/document/authorization/bags`,
    params
  })
}

// 根据病案号id获取授权科室信息列表
export const getBorrowMrnoAuthorizationDeptsApi = (params) => {
  return axios({
    method: "get",
    url: `/api/document/authorization/bag/depts`,
    params: {
      wsid: params.wsid // 病案袋wsid
    }
  }).then(res => {
    return res.data?.data || []
  })
}

// 根据病案号id获取授权用户信息列表
export const getBorrowMrnoAuthorizationUsersApi = (params) => {
  return axios({
    method: "get",
    url: `/api/document/authorization/bag/users`,
    params: {
      wsid: params.wsid
    }
  }).then(res => {
    return res.data?.data || []
  })
}


/* ================== 授权操作 =================== */

// 病案袋授权
export const authorizeByMrnoApi = (params) => {
  return axios({
    method: "post",
    url: `/api/document/authorization/bags/do`,
    data: {
      documeBagWsid: params.documeBagWsid,
      type: params.type,
      wsids: params.wsids
    }
  })
}

// 科室授权
export const authorizeByDeptApi = (params) => {
  return axios({
    method: "post",
    url: `/api/document/authorization/dept/do`,
    data: {
      deptWsid: params.deptWsid,
      type: params.type,
      wsids: params.wsids,
      secrecyGrade: params.secrecyGrade
    }
  })
}