<template>
  <el-input
    v-model="displayValue"
    :placeholder="props.placeholder"
    :value="displayValue"
    :size="props.size"
    :disabled="props.disabled"
    @input="onInput"
    @blur="onBlur"
  >
    <template v-if="props.unit && props.unit?.length > 0" #append>
      <span v-if="props.unit && props.unit?.length > 0">{{ props.unit }}</span>
      <!-- <slot></slot> -->
    </template>
  </el-input>
</template>

<script setup lang="ts">
import { ref, watchEffect, defineProps, defineEmits } from "vue"
import { isNaN, indexOf, size, endsWith, toString, isNil } from "lodash/fp"
import {
  percentageParser,
  integerParser,
  floatParser,
  percentageFormat,
  PERCENTAGE,
  fixedNumberFormat,
  INTEGER,
  FLOAT,
  FIXED_NUMBER
} from "./config"
interface NumberInputProps {
  value?: number
  min?: number
  max?: number
  precision?: number
  format?: string
  placeholder?: string // 占位符
  readonly?: boolean
  unit?: string
  size?: string
  disabled?: boolean
}

const props = withDefaults(defineProps<NumberInputProps>(), {
  value: undefined,
  min: undefined,
  max: undefined,
  precision: 1,
  format: "INTEGER",
  placeholder: "",
  readonly: false,
  unit: "",
  size: "default",
  disabled: false
})

const emit = defineEmits(["update:value", "input", "blur"])

const displayValue = ref("")
const editing = ref(false)

watchEffect(() => {
  if (!editing.value) {
    displayValue.value = getDisplayValue()
  }
})

function getPrecision(value: number): number {
  const strValue = toString(value)
  const index = indexOf(".")(strValue)
  if (index !== -1) {
    return size(strValue) - index - 1
  }
  return 0
}

function formatValue(value: number): number {
  if (props.max !== undefined && value > props.max) return props.max
  if (props.min !== undefined && value < props.min) return props.min
  return value
}

function parseValue(value: string): number {
  if (props.format === PERCENTAGE) {
    return percentageParser(value, props.precision)
  }
  if (props.format === INTEGER) {
    return integerParser(value)
  }
  if (props.format === FLOAT) {
    return floatParser(value, props.precision)
  }
  if (props.format === FIXED_NUMBER) {
    return floatParser(value, props.precision)
  }
  return integerParser(value)
}

function onInput(value: string) {
  editing.value = true
  if (props.precision) {
    const newPrecision = getPrecision(parseFloat(value))
    let oldPrecision = getPrecision(props.value ?? 0)
    if (props.format === PERCENTAGE) {
      oldPrecision -= 2
    }
    if (newPrecision > oldPrecision && newPrecision > props.precision) return
  }

  displayValue.value = value
  console.log("value", value, parseValue(value))
  const parsedValue = parseValue(value)

  if (
    (value !== "0" && parsedValue === 0) ||
    endsWith(".")(value) ||
    isNaN(parsedValue) ||
    parsedValue === props.value
  ) {
    return
  }

  const formattedValue = formatValue(parsedValue)
  emit("update:value", formattedValue)
  emit("input", formattedValue)
}

function getDisplayValue(): string {
  if (isNil(props.value) || isNaN(props.value)) {
    return ""
  }
  if (props.format === PERCENTAGE) {
    return percentageFormat(props.value, props.precision) as any
  }
  if (props.format === FIXED_NUMBER) {
    return fixedNumberFormat(props.value, props.precision)
  }
  if (props.precision && !props.format) {
    return Number(props.value).toFixed(props.precision)
  }
  return props.value.toString()
}

function onBlur(event: FocusEvent) {
  emit("blur", event)
  editing.value = false
  displayValue.value = getDisplayValue()
}
</script>
