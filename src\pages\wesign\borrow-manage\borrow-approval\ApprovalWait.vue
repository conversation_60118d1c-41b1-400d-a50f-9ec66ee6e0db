<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="approvalRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
        />

        <el-form-item label="借阅类型">
          <el-select v-model="searchFormState.applyType">
            <el-option
              v-for="item in globalOptionsStore.borrowTypeOptions"
              :key="item.value as string"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <DaterangeFormItem v-model:model-value="searchFormState.applyDatetime" label="申请时间" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="commonTableRef"
        table-id="borrowApprovalTable"
        :table-columns="borrowApprovalWaitColumns"
        :request-api="getWaitApprovalListApi"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <template #statusEnumName="{ row }">
          <el-tag :type="APPROVAL_TAG_MAP.get(row.statusEnumName)">
            {{ APPROVAL_STATUS_MAP.get(row.statusEnumName) }}
          </el-tag>
        </template>

        <template #operation="{ row }">
          <TableButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Approve)"
            :disabled="row.statusEnumName !== 'APPLICATION'"
            tooltip="该借阅已无法审批"
            @click="showApprovalDrawer(row)"
          >
            审批
          </TableButton>
        </template>
      </CommonTable>
    </template>

    <!-- 审批抽屉 -->
    <ApprovalDrawer ref="approvalDrawerRef" @success="commonTableRef?.refreshTableData" />
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import {
  CommonTable,
  TableButton,
  PatientFilterFormItem,
  DaterangeFormItem,
  PageContainer,
  SearchContainer
} from "@/base-components"
import { TabsRouter } from "@/page-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { useUserStore, useGlobalOptionsStore } from "@/stores"
import { formatDatetime } from "@/utils"
import ApprovalDrawer from "./components/ApprovalDrawer.vue"
import {
  borrowApprovalWaitColumns,
  APPROVAL_STATUS_MAP,
  APPROVAL_TAG_MAP,
  approvalRouterList,
  menuId,
  unitOptions
} from "./config"
import { getWaitApprovalListApi } from "./interface"

const { hasOperationPermission } = useUserStore()
const globalOptionsStore = useGlobalOptionsStore()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  patientFilterProp: "",
  patientFilterValue: "",
  applyType: "",
  applyDatetime: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

const dataCallback = data => {
  data.forEach(item => {
    item.applyDatetime = formatDatetime(item.applyDatetime)
    item.approverDatetime = formatDatetime(item.approverDatetime)
    item.approveLatestTime = formatDatetime(item.approveLatestTime)
    item.timeLimit =
      item.approveTimeLimitCh === "null"
        ? item.timeLimit + unitOptions[item.timeLimitUnitEnumName]
        : item.approveTimeLimitCh + unitOptions[item.approveTimeLimitUnitEnumName]
    if (item.approveTimeLimitCh === "null") {
      if (item.timeLimitUnitEnumName === "FOREVER") item.timeLimit = "永久"
    } else {
      if (item.approveTimeLimitUnitEnumName === "FOREVER") item.timeLimit = "永久"
    }
  })
  return data
}

const commonTableRef = ref<InstanceType<typeof CommonTable>>()

/* ======================== 借阅审批弹窗 ======================== */

const approvalDrawerRef = ref<InstanceType<typeof ApprovalDrawer>>()

const showApprovalDrawer = row => {
  approvalDrawerRef.value?.show(row)
}
</script>
