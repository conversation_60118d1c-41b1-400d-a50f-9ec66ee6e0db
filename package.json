{"name": "wesign-hospital", "version": "0.0.0", "scripts": {"dev": "vite --mode development", "build": "node --max_old_space_size=8192 ./node_modules/vite/bin/vite.js build --mode production ", "build-image": "node ./scripts/build-image.cjs", "preview": "vite preview"}, "dependencies": {"@antv/x6": "^2.16.1", "@antv/x6-vue-shape": "^2.1.1", "@codemirror/autocomplete": "^6.18.1", "@codemirror/lang-java": "^6.0.1", "@codemirror/lang-sql": "^6.7.1", "@codemirror/view": "^6.34.1", "@element-plus/icons-vue": "^2.1.0", "@signit/common-utils": "^0.0.4", "@signit/vue3-components": "^0.1.0", "@vueup/vue-quill": "^1.2.0", "@vueuse/core": "^10.1.2", "ant-design-vue": "^4.0.6", "archiver": "^6.0.1", "axios": "^0.27.2", "codemirror": "^6.0.1", "crypto-js": "^4.2.0", "currency.js": "^2.0.4", "echarts": "^5.4.3", "el-table-infinite-scroll": "^3.0.3", "element-china-area-data": "^6.0.2", "element-plus": "2.4.3", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "glob": "^8.0.3", "jszip": "^3.7.1", "localforage": "^1.10.0", "lodash-es": "^4.17.21", "moment": "^2.29.4", "pdfjs-dist": "2.6.347", "pdfobject": "^2.3.0", "pinia": "^2.0.23", "pinia-plugin-persistedstate": "^2.3.0", "print-js": "^1.6.0", "qrcode": "^1.5.3", "quill": "^1.3.7", "react-dnd-html5-backend": "^16.0.1", "remixicon": "^3.4.0", "shelljs": "^0.8.5", "sortablejs": "^1.15.6", "spark-md5": "3.0.0", "uuid": "^8.3.2", "version-polling": "^1.2.6", "vite-plugin-static-copy": "^0.13.0", "vue": "^3.2.37", "vue-codemirror": "^6.1.1", "vue-codemirror6": "^1.3.4", "vue-cropper": "^1.1.1", "vue-draggable-plus": "^0.5.3", "vue-echarts": "^6.6.1", "vue-pdf-embed": "^1.0.5", "vue-router": "^4.0.13", "vue3-dnd": "^2.0.2", "vue3-print-nb": "^0.1.4"}, "devDependencies": {"@types/crypto-js": "^4.2.1", "@types/echarts": "^4.9.22", "@types/glob": "^7.2.0", "@types/node": "^16.11.45", "@types/qrcode": "^1.5.5", "@typescript-eslint/eslint-plugin": "^5.60.0", "@typescript-eslint/parser": "^5.60.0", "@vitejs/plugin-legacy": "^4.0.0", "@vitejs/plugin-vue": "^4.5.0", "@vitejs/plugin-vue-jsx": "^3.1.0", "autoprefixer": "^10.4.8", "chalk": "^4.1.2", "cross-env": "^7.0.3", "eslint": "^8.42.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.0", "eslint-plugin-vue": "^9.14.1", "increase-memory-limit": "^1.0.7", "less": "^4.1.3", "postcss": "^8.4.18", "postcss-preset-env": "^9.3.0", "prettier": "^2.8.8", "sass": "1.56.1", "ssh2-sftp-client": "^9.1.0", "terser": "^5.36.0", "typescript": "~4.7.4", "unplugin-auto-import": "^0.16.4", "unplugin-element-plus": "^0.7.1", "unplugin-vue-components": "^0.25.1", "vite": "^4.0.0", "vue-tsc": "^0.40.4"}, "browserslist": [">1%", "last 4 version", "not dead"]}