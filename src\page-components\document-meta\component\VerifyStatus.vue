<template>
  <div :class="verifyText.className" class="verify-status">
    <i :class="verifyText?.icon"></i>
    {{ verifyText?.text }}
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue"

interface VerifyStatusProps {
  verifyEnvelopeRequestStatus: boolean
  verifySignData?: Record<string, any> | any
}
const props = defineProps<VerifyStatusProps>()

// 验证签名状态
const verifyText = computed(() => {
  if (!props.verifySignData)
    return {
      text: "无任何签名",
      icon: "ri-information-line",
      key: "FILE_UNSIGNED",
      className: "color-normal"
    }
  if (props.verifySignData?.verifyStatus === "FILE_UNSIGNED" || props?.verifySignData?.verifyResults?.length === 0)
    return {
      text: "无任何签名",
      icon: "ri-information-line",
      key: "FILE_UNSIGNED",
      className: "color-normal"
    }
  if (
    props.verifySignData?.verifyStatus === "FILE_MODIFIED" ||
    props?.verifySignData?.verifyResults?.some(item => item?.signInfo?.modified === true)
  ) {
    return {
      key: "FILE_MODIFIED",
      text: "文档已被篡改",
      icon: "ri-error-warning-line",
      className: "color-error"
    }
  }
  if (
    props.verifySignData?.verifyStatus === "FILE_INTEGRAL_USER_CERT_INVALID" ||
    props?.verifySignData?.verifyResults?.some(
      verify => verify?.certInfo?.certInvalid || !verify?.certInfo?.certAgainstRootValid
    )
  ) {
    let warningSignCount = 0
    props?.verifySignData?.verifyResults?.forEach((data: any) => {
      if (data?.certInfo?.certInvalid || !data?.certInfo?.certAgainstRootValid) warningSignCount++
    })
    return {
      text: `至少${warningSignCount}个签名存在风险`,
      icon: "ri-error-warning-line",
      key: "FILE_INTEGRAL_USER_CERT_INVALID",
      className: "color-info"
    }
  }
  return {
    text: "已签名且所有签名都有效",
    icon: "ri-checkbox-circle-fill",
    key: "FILE_INTEGRAL_CERT_VALID",
    className: "color-success"
  }
})
</script>

<style lang="less" scoped>
.verify-status {
  font-size: 14px;
  font-weight: 700;
}
.color-success {
  color: #67c23a;
  background-color: rgba(238, 248, 237, 1);
}
.color-error {
  color: #f56c6c;
  background-color: rgba(245, 108, 108, 0.2);
}
.color-info {
  color: #909399;
  background-color: rgba(144, 147, 153, 0.2);
}

.color-normal {
  color: #909399;
  background-color: rgba(144, 147, 153, 0.2);
}
</style>
