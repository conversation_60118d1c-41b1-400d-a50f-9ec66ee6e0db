<template>
  <el-popover placement="bottom" width="210" trigger="hover" popper-class="export-popover">
    <template #reference>
      <el-button :loading="props.loading" :icon="props.showIcon ? Download : undefined">
        {{ props.buttonText }}
      </el-button>
    </template>

    <PopoverButton :center="false" :tip="`(当前选中${props.selectedCount}条数据)`" @click="handleExportSelected">
      导出选中
    </PopoverButton>
    <el-tooltip
      :disabled="$props.tooltipDisabled"
      effect="dark"
      :content="`单次最大数据导出量${props.max}条`"
      placement="bottom"
    >
      <PopoverButton :center="false" :tip="`(共${props.total}条数据)`" @click="handleExportAll">导出全部</PopoverButton>
    </el-tooltip>
  </el-popover>

  <DialogContainer v-model:visible="dialogVisible" title="导出" :no-footer="true" :width="550">
    <!-- 导出成功 -->
    <el-result
      v-if="exportStatus === 'success'"
      icon="success"
      title="导出成功"
      sub-title="导出成功，请前往文件夹查看！"
    />

    <!-- 导出失败 -->
    <el-result v-else-if="exportStatus === 'error'" icon="error" title="导出失败" :sub-title="props.errorMessage" />

    <!-- 导出中 -->
    <el-result v-else title="导出中，请稍后..." sub-title="关闭当前窗口不会影响导出操作。">
      <template #icon><div v-loading="true" style="width: 56px; height: 56px"></div></template>
    </el-result>

    <!-- 导出进度 -->
    <el-progress :percentage="progress" :status="exportStatus === 'error' ? 'exception' : ''"></el-progress>
  </DialogContainer>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue"
import { Download } from "@element-plus/icons-vue"
import { Message, SystemPrompt } from "@/utils"
import { DialogContainer } from "../containers"
import { PopoverButton } from "."

const props = withDefaults(
  defineProps<{
    buttonText?: string
    selectedCount: number // 已选数量
    total?: number // 数据总量
    max?: number // 单次最大导出
    errorMessage?: string
    loading?: boolean
    showIcon?: boolean
    tooltipDisabled?: boolean
  }>(),
  {
    buttonText: "导出",
    max: 10000,
    total: 0,
    errorMessage: "导出失败，请稍后再试！",
    loading: false,
    showIcon: true,
    tooltipDisabled: false
  }
)

const emits = defineEmits(["export", "exportAll"])

/* ============== 导出 ============== */

const exportState = reactive({
  loading: false,
  exportAll: false // 是否导出所有
})

// 导出选中
const handleExportSelected = () => {
  if (!props.selectedCount) return Message.warning("请至少勾选一条数据")
  SystemPrompt("确认导出数据吗").then(() => {
    exportState.exportAll = false
    emits("export")
  })
}

// 导出所有
const handleExportAll = () => {
  if (!props.total) {
    return Message.warning("没有数据可以导出")
  }
  SystemPrompt("确认导出全部数据吗").then(() => {
    exportState.exportAll = true
    emits("exportAll")
  })
}

/* ============== 导出进度弹窗 ============== */

let progressInterval // 进度计时器
const dialogVisible = ref(false)
const progress = ref(0) // 导出进度
const exportStatus = ref<"underway" | "error" | "success">("underway") // 导出状态

// 模拟进度条
const handleExportStart = () => {
  progress.value = 0
  exportStatus.value = "underway"
  dialogVisible.value = true
  exportState.loading = true
  progressInterval = setInterval(() => {
    if (progress.value < 90) progress.value += 10
    else if (progress.value < 99) progress.value += 1
    // 到达99还没有导出成功则卡死在99等待导出结果
    else if (progress.value === 99) {
      clearInterval(progressInterval)
    }
  }, 100)
}

const handleExportDone = () => {
  clearInterval(progressInterval)
  progress.value = 100
  exportStatus.value = "success"
  exportState.loading = false
}

const handleExportError = () => {
  clearInterval(progressInterval)
  progress.value = 0
  exportStatus.value = "error"
  exportState.loading = false
}

defineExpose({
  handleExportStart,
  handleExportDone,
  handleExportError
})
</script>
