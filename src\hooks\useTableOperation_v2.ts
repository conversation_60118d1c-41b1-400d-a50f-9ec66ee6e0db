import { ref } from "vue"
import { Message, toastError } from "@/utils"

interface ITableOperationApiGroup<T> {
  addApi: (data: T) => Promise<any> // 添加数据
  editApi: (data: T) => Promise<any> // 修改数据
  toggleApi?: (wsid: string, nextStatus: "ENABLE" | "DISABLE") => Promise<any> // 切换状态
  deleteApi: (wsid: string) => Promise<any> // 删除数据
}

const useTableOperation = <T extends Record<string, any>>(
  formState: T, // 表单数据
  options: {
    apiGroup: ITableOperationApiGroup<T>
    callback?: (params?: T) => any // 添加或修改成功后执行的回调函数
    closeForm?: () => void
    toggleConfirmation?: (row?: Record<string, any>) => Promise<any> // 切换前确认函数
    deleteConfirmation?: (row?: Record<string, any>) => Promise<any> // 删除前确认函数
  }
) => {
  const actionLoading = ref(false) // 表单是否加载中

  const { addApi, editApi, toggleApi, deleteApi } = options.apiGroup
  const { callback, toggleConfirmation, deleteConfirmation, closeForm } = options

  async function confirmAdd() {
    try {
      actionLoading.value = true
      await addApi(formState)
      Message.success("添加成功")
      closeForm && closeForm()
      callback && callback()
    } catch (err: any) {
      toastError(err, "添加失败")
    } finally {
      actionLoading.value = false
    }
  }

  async function confirmEdit() {
    try {
      actionLoading.value = true
      await editApi(formState)
      Message.success("修改成功")
      closeForm && closeForm()
      callback && callback()
    } catch (err: any) {
      return toastError(err, "修改失败")
    } finally {
      actionLoading.value = false
    }
  }

  async function confirmToggle(row: any, wsid: string, nextStatus: "ENABLE" | "DISABLE") {
    try {
      toggleConfirmation && (await toggleConfirmation(row))
      await toggleApi?.(wsid, nextStatus)
      Message.success("修改状态成功")
      callback && callback()
    } catch (err: any) {
      console.log(err)
      if (err === "cancel") return
      return toastError(err, "修改状态失败")
    }
  }

  async function confirmDelete(row: any, wsid: string) {
    try {
      deleteConfirmation && (await deleteConfirmation(row))
      await deleteApi(wsid)
      Message.success("删除成功")
      callback && callback()
    } catch (err: any) {
      if (err === "cancel") return
      return toastError(err, "删除失败")
    }
  }

  return {
    actionLoading,
    confirmAdd,
    confirmEdit,
    confirmToggle,
    confirmDelete
  }
}

export default useTableOperation
