<template>
  <div ref="largeScreen" class="large-screen-monitor-root">
    <div class="monitor-title-container">
      <div class="online-user-count">
        <span class="user-count-title">在线用户数</span>
        <span class="user-count">{{ onLineUser }}</span>
      </div>
      <div class="time-container">
        <div class="current-time">{{ currentTime }}</div>
        <div class="divide-line"></div>
        <div class="current-date">
          <div>{{ currentDate }}</div>
          <div>{{ currentWeek }}</div>
        </div>
      </div>
    </div>
    <div class="monitor-data-container">
      <!-- 数据采集表格 -->
      <DataCollectTable />

      <!-- 出院数据折线图 -->
      <DischargeDataChart :discharge-data="dischargeData" />

      <!-- 服务器运行状态图 -->
      <ServerStatusChart :system-monitor-data="systemMonitorData" />

      <!-- 数据采集条形图 -->
      <DataCollectChart />

      <!-- 近6月统计柱状图 -->
      <LatestStatisticsChart :latest-statistics-data="latestStatisticsData" />

      <!-- 7日异常数据状态 -->
      <AbnormalDataChart :abnormal-data="systemAbnormalData" />
    </div>
    <div class="fullscreen-btn">
      <i v-if="!isFullscreen" title="全屏显示" class="ri-fullscreen-line" @click="toggle"></i>
      <i v-else title="退出全屏" class="ri-fullscreen-exit-line" @click="toggle"></i>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from "vue"
import { useFullscreen, useNow, useDateFormat } from "@vueuse/core"
import { toastError } from "@/utils"
import {
  DataCollectTable,
  DischargeDataChart,
  ServerStatusChart,
  DataCollectChart,
  LatestStatisticsChart,
  AbnormalDataChart
} from "./components"
import { getLargeScreenMonitorStatisticsApi } from "./interface"

onMounted(() => {
  getLargeScreenMonitorStatistics()
  intervalId.value = setInterval(getLargeScreenMonitorStatistics, 300000)
})

onBeforeUnmount(() => {
  clearInterval(intervalId.value)
})

const largeScreen = ref(null)
const { toggle, isFullscreen } = useFullscreen(largeScreen)

// 系统时间
const currentTime = useDateFormat(useNow(), "HH:mm:ss")
const currentDate = useDateFormat(useNow(), "YYYY-MM-DD")
const currentWeek = useDateFormat(useNow(), "ddd")

// 在线用户数
const onLineUser = ref(0)

// 近期出院数据(近7天)
const dischargeData = ref([])

// 近6月统计数据
const latestStatisticsData = ref([])

// 硬件监控数据
const systemMonitorData = ref()

// 7日异常数据
const systemAbnormalData = ref([])

const intervalId = ref()

// 获取大屏监控汇总数据
function getLargeScreenMonitorStatistics() {
  getLargeScreenMonitorStatisticsApi()
    .then(res => {
      onLineUser.value = res.data.data.onLineUser
      dischargeData.value = res.data.data.lastDaysBusinessStatistics || []
      latestStatisticsData.value = res.data.data.lastMonthsBusinessStatistics || []
      systemMonitorData.value = res.data.data.systemMonitor
      systemAbnormalData.value = res.data.data.systemAbnormalCount
    })
    .catch(err => {
      toastError(err)
    })
}
</script>

<style lang="less" scoped>
.large-screen-monitor-root {
  width: 100%;
  min-width: 1626px;
  height: 100%;
  background: #00041f;
  position: relative;
  overflow: auto;
  .monitor-title-container {
    width: 100%;
    height: 107px;
    background: url("@/assets/svg/monitor/monitor-title.png") no-repeat;
    background-size: 100%;
    display: flex;
    justify-content: space-between;
    .online-user-count {
      padding-left: 32px;
      padding-top: 23px;
      .user-count-title {
        color: #fff;
        padding-right: 15px;
      }
      .user-count {
        font-size: 20px;
        color: #2fff29;
      }
    }
    .time-container {
      padding-top: 24px;
      padding-right: 10px;
      display: flex;
      .current-time {
        color: #fff;
        font-size: 20px;
      }
      .divide-line {
        width: 0px;
        height: 32px;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        border: 1px solid;
        border-image: linear-gradient(180deg, rgba(139, 144, 160, 0), rgba(139, 144, 160, 1), rgba(139, 144, 160, 0)) 1
          1;
        margin-left: 9px;
        margin-right: 16px;
      }
      .current-date {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.54);
      }
    }
  }
  .monitor-data-container {
    width: 100%;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
  }
  .fullscreen-btn {
    position: fixed;
    top: 50%;
    right: 40px;
    cursor: pointer;
    i {
      opacity: 0.5;
      font-size: 20px;
      color: #fff;
    }
    &:hover {
      i {
        opacity: 1;
      }
    }
  }
}
</style>
