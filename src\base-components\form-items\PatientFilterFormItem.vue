<template>
  <el-form-item :label="props.label">
    <el-input v-model.trim="patientFilterValue" style="width: 300px" clearable>
      <template #prepend>
        <el-select v-model="patientFilterProp" style="width: 100px" @change="handleFilterPropChange">
          <el-option
            v-for="option in $props.filterPropOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          ></el-option>
        </el-select>
      </template>
      <!-- 搜索按钮 -->
      <template v-if="showSearch" #append>
        <el-button :icon="Search" @click="search" />
      </template>
    </el-input>
  </el-form-item>
</template>

<script setup lang="ts">
import { computed } from "vue"
import { Search } from "@element-plus/icons-vue"
import type { BaseOptionItem } from "@/types"

const props = withDefaults(
  defineProps<{
    label: string
    filterProp: string
    filterValue: string
    filterPropOptions?: Array<BaseOptionItem>
    showSearch?: boolean
  }>(),
  {
    filterPropOptions: () => [
      { label: "姓名", value: "patientName" },
      { label: "住院号", value: "inpNo" },
      { label: "患者编号", value: "patientId" },
      { label: "病案号", value: "mrNo" }
    ],
    showSearch: false
  }
)

const emits = defineEmits(["update:filterProp", "update:filterValue", "filterPropChange", "search"])

const patientFilterProp = computed({
  get: () => props.filterProp,
  set: val => emits("update:filterProp", val)
})

const patientFilterValue = computed({
  get: () => props.filterValue,
  set: val => emits("update:filterValue", val)
})

function handleFilterPropChange(nextProp: string) {
  emits("update:filterProp", nextProp)
  emits("update:filterValue", "")
  emits("filterPropChange", nextProp)
}
const search = () => {
  emits("search", patientFilterProp.value, patientFilterValue.value)
}
</script>
