<template>
  <div :id="id" ref="pdfEmbed" class="vue-pdf-embed">
    <div v-for="pageNum in pagesNumber" :key="pageNum">
      <slot name="before-page" :page="pageNum" />

      <div :id="id && `${id}-${pageNum}`" class="vue-pdf-embed__page">
        <canvas />
      </div>

      <slot name="after-page" :page="pageNum" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { watch, ref } from "vue"
import { ElMessage } from "element-plus"
import { getPdfDocument } from "./config"

interface Props {
  height?: number | string
  id?: string
  page?: number | Array<number>
  rotation?: number | string
  scale?: number
  source: string
  width: number
  isThumbnail?: boolean // 是否是缩略图
}

const props = withDefaults(defineProps<Props>(), {
  id: undefined,
  page: undefined,
  rotation: 0,
  scale: 1,
  width: 800,
  isThumbnail: false
})

const pagesNumber = ref<Array<number>>([])
const pdfEmbed = ref()
const emits = defineEmits(["rendered", "rendering-failed"])
// 加载pdf文件
const loadFile = async () => {
  try {
    const pdfLoadingTask = getPdfDocument(props.source)
    const pdf = await pdfLoadingTask.promise
    await getPageDimensions(pdf)
    renderPages(pdf)
    emits("rendered", pdf, rawsSize.value)
  } catch (error) {
    console.log(error)
    ElMessage.error("文件加载失败！")
  }
}

//释放资源
const releaseCanvas = () => {
  if (!pdfEmbed.value) return
  pdfEmbed.value?.querySelectorAll("canvas").forEach(canvas => {
    canvas.width = 1
    canvas.height = 1
    canvas.getContext("2d")?.clearRect(0, 0, 1, 1)
  })
}
watch(
  () => [props.source, props.height, props.page, props.rotation, props.width],
  async () => {
    if (!props.source) return
    await releaseCanvas()
    await loadFile()
  },
  {
    immediate: true,
    deep: true
  }
)

// 获取pdf文件的宽高
function getPageSize(pageNum) {
  let width = 0
  let height = 0
  if (props.isThumbnail) {
    return [props.width, props.width * rawsSize.value[pageNum]?.ratio]
  }
  // 因为有可能每一页的尺寸不一样，所以这里需要根据每一页实际的尺寸来计算，
  // 首先找到rawsSize里最小的宽，然后这一页的宽度使用props.width,其他比这页的宽大的页使用比例计算
  const minRaw = Math.min(...Object.values(rawsSize.value).map((item: any) => item.width))
  if (rawsSize.value[pageNum]?.width === minRaw) {
    width = props.width
    height = width * rawsSize.value[pageNum]?.ratio
  } else {
    width = (rawsSize.value[pageNum]?.width / minRaw) * props.width
    height = width * rawsSize.value[pageNum]?.ratio
  }
  return [Math.floor(width), Math.floor(height)]
}

const rawsSize = ref({})

// 获取所有页的尺寸
const getPageDimensions = async pdf => {
  rawsSize.value = {}
  await Promise.all(
    Array.from({ length: pdf.numPages }, (_, index) => {
      return pdf.getPage(index + 1).then(pageData => {
        if (pageData) {
          const { height, width } = pageData.getViewport({ scale: props.scale })
          rawsSize.value[index + 1] = {
            width: width,
            height: height,
            ratio: Number(height / width)
          }
        }
      })
    })
  )
}

// 渲染pdf
async function renderPages(pdf) {
  if (!pdfEmbed.value || !pdf) return
  try {
    if (props.page && !Array.isArray(props.page)) {
      pagesNumber.value = [props.page]
    } else if (props.page && Array.isArray(props.page)) {
      pagesNumber.value = props.page
    } else {
      pagesNumber.value = [...Array(pdf.numPages + 1).keys()].slice(1)
    }
    const pageElements = pdfEmbed.value.getElementsByClassName("vue-pdf-embed__page")
    await Promise.all(
      pagesNumber.value.map(async (pageNum, index) => {
        const page = await pdf.getPage(pageNum)
        const pageRotation = props.rotation + page.rotate
        const [canvas] = pageElements[index].children
        const [actualWidth, actualHeight] = getPageSize(pageNum)
        canvas.style.width = `${Math.floor(actualWidth)}px`
        canvas.style.height = `${Math.floor(actualHeight)}px`
        await renderPage(page, canvas, actualWidth, pageRotation)
      })
    )
  } catch (e) {
    console.error(e)
    pagesNumber.value = []
    emits("rendering-failed", e)
  }
}

// 渲染每一页
async function renderPage(page, canvas, width, rotation) {
  const pageWidth = (rotation / 90) % 2 ? page.view[3] : page.view[2]
  const pdfScale = width / pageWidth
  const viewport = page.getViewport({
    // scale: props.scale * (Math.ceil(width / pageWidth) + 1),
    scale: pdfScale * window.devicePixelRatio * props.scale,
    rotation
  })
  canvas.width = viewport.width
  canvas.height = viewport.height
  await page.render({
    canvasContext: canvas.getContext("2d"),
    viewport
  })
}

defineExpose({
  pdfEmbed: pdfEmbed
})
</script>

<style lang="less" scoped>
.vue-pdf-embed {
  &__page {
    position: relative;

    canvas {
      display: block;
    }
  }
}
</style>
