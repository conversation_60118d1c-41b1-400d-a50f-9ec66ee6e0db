<template>
  <div v-loading="loading">
    <el-timeline class="approval-record">
      <el-timeline-item
        v-for="item in approvalRecord"
        :key="item.activityId"
        :timestamp="item.activityName"
        placement="top"
        :type="((getParentWorkflowStatus(item?.activityStatus).type ) as any)"
      >
        <el-card v-if="item?.childActInstanceDtos?.length > 0" class="approval-card">
          <div v-for="(approvalUser, index) in item.childActInstanceDtos" :key="index" class="approval-card-content">
            <BlobImage
              v-if="approveUserAvatars?.[approvalUser?.taskCompleteDto?.approveWsid]"
              class="approval-user-avatar"
              :file-wsid="approveUserAvatars?.[approvalUser?.taskCompleteDto?.approveWsid]"
              :default="defaultAvatar"
            />

            <div class="approval-user-content">
              <div v-if="approvalUser?.taskCompleteDto?.workflowStatus">
                <span v-if="approvalUser?.taskCompleteDto?.approveName" class="approval-name">
                  {{ approvalUser?.taskCompleteDto?.approveName }}
                </span>
                <span v-if="approvalUser?.endTime" class="approval-date">
                  {{ formatDatetime(approvalUser?.endTime) }}
                </span>
              </div>
              <div
                class="approval-status"
                :class="getWorkflowStatus(approvalUser?.taskCompleteDto?.workflowStatus).class"
              >
                <i :class="getWorkflowStatus(approvalUser?.taskCompleteDto?.workflowStatus).icon"></i>

                <span>{{ getWorkflowStatus(approvalUser?.taskCompleteDto?.workflowStatus).text }}</span>
                <span v-if="approvalUser?.taskCompleteDto?.desc" class="approval-desc">
                  ({{ approvalUser?.taskCompleteDto?.desc }})
                </span>
              </div>
            </div>
          </div>
        </el-card>
      </el-timeline-item>
    </el-timeline>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue"
import { flow, map, compact } from "lodash/fp"
import { BlobImage } from "@/base-components"
import defaultAvatar from "@/assets/png/avatar2.png"
import { getProcessActInstance, batchGetUserAvatarsByWsid } from "@/interfaces"
import { formatDatetime } from "@/utils"
import { getWorkflowStatus } from "./config"

interface Props {
  businessKey: string
}

const props = defineProps<Props>()

const approvalRecord = ref<Array<Record<string, any>>>([])

const loading = ref(false)

// 审批人头像
const approveUserAvatars = ref({})

watch(
  () => props.businessKey,
  async () => {
    if (!props.businessKey) return
    loading.value = true
    const res = await getProcessActInstance({ businessKey: props.businessKey })
    const nodesData = res.data.data
    const applyWsid = Math.random().toString(36)
    const applyUserData = [
      {
        activityName: "申请节点",
        activityType: "APPLY",
        startTime: nodesData.startTime,
        activityId: applyWsid,
        activityStatus: "APPLY",
        childActInstanceDtos: [
          {
            activityName: nodesData.applyUserName,
            activityType: "APPLY",
            startTime: nodesData.startTime,
            activityId: applyWsid,
            taskCompleteDto: {
              approveName: nodesData.applyUserName,
              approveWsid: nodesData.applyUserWsid,
              businessStatus: "APPROVAL_APPLY",
              workflowStatus: "APPLY"
            }
          }
        ]
      }
    ]

    // 根据wsid获取用户头像
    if (nodesData?.applyUserWsid) {
      approveUserAvatars.value[nodesData.applyUserWsid] = ""
    }
    nodesData.childActInstanceDtos.forEach((item: any) => {
      item?.childActInstanceDtos.forEach((child: any) => {
        if (child?.taskCompleteDto?.approveWsid) {
          approveUserAvatars.value[child.taskCompleteDto?.approveWsid] = ""
        }
      })
    })
    await batchGetUserAvatarsByWsid(Object.keys(approveUserAvatars.value)).then(res => {
      res.data.data.forEach((item: any) => {
        approveUserAvatars.value[item.wsid] = item.avatar
      })
    })

    nodesData.childActInstanceDtos.forEach(item => {
      if (item?.childActInstanceDtos?.length > 0) {
        // 如果每一个审批人都没有审批时间，说明该节点还没有审批，那么只保留第一个审批人
        if (item?.childActInstanceDtos?.every((child: any) => !child?.endTime)) {
          item.childActInstanceDtos = [item.childActInstanceDtos[0]]
        }
        // 如果每一个审批人都有审批时间，并且只有一个人的有taskCompleteDto,说明这节点审批策略是仅一人审批即可，那么只保留有taskCompleteDto的那个人
        if (
          item?.childActInstanceDtos?.every((child: any) => child?.endTime) &&
          item?.childActInstanceDtos?.filter((child: any) => child?.taskCompleteDto).length === 1
        ) {
          item.childActInstanceDtos = item.childActInstanceDtos.filter((child: any) => child?.taskCompleteDto)
        }
      }
    })

    approvalRecord.value = [...applyUserData, ...nodesData.childActInstanceDtos]
    loading.value = false
    console.log("审批记录", approvalRecord.value)
  },
  {
    immediate: true
  }
)

// 获取父节点的流程结果状态
const getParentWorkflowStatus = (activityStatus: string) => {
  // 如果没有子节点，返回处理中
  if (!activityStatus || activityStatus.length === 0) {
    return {
      text: "待处理",
      type: "info",
      color: "#dcdfe6"
    }
  }

  // 如果每一个节点都是PASS，返回PASS
  if (activityStatus === "PASS" || activityStatus === "APPLY") {
    return {
      text: "通过",
      type: "success",
      color: "#42b625"
    }
  }

  if (activityStatus === "AUDIT_ING") {
    return {
      text: "处理中",
      type: "warning",
      color: "#FF9D03"
    }
  }

  // 如果有 DENY，返回DENY
  if (activityStatus === "DENY") {
    return {
      text: "已驳回",
      type: "danger",
      color: "#f56c6c"
    }
  }

  // 如果有 UNKNOWN，返回UNKNOWN
  if (activityStatus === "UNKNOWN") {
    return {
      text: "未知",
      type: "warning",
      color: "#dcdfe6"
    }
  }

  // 如果有ROLLBACK，返回ROLLBACK
  if (activityStatus === "ROLLBACK") {
    return {
      text: "回退",
      type: "danger",
      color: "#f56c6c"
    }
  }

  // 默认返回处理中
  return {
    text: "待处理",
    type: "info",
    color: "#dcdfe6"
  }
}
</script>

<style lang="less" scoped>
.approval-record {
  margin-left: 1px;

  :deep(.el-timeline-item__timestamp) {
    font-weight: bold;
    color: #0a1633;
    font-size: 14px;
  }
  :deep(.el-timeline-item) {
    padding-bottom: 16px;
  }
  .approval-card {
    background: #f6f7f9;

    :deep(.el-card__body) {
      padding: 16px;
    }
    .approval-card-content {
      display: flex;
      align-items: center;
      gap: 8px;
      &:not(:last-child) {
        margin-bottom: 16px;
      }
      .approval-user-avatar {
        width: 46px;
        height: 46px;
        background: #d9d9d9;
        border-radius: 50%;
      }
      .approval-user-content {
        width: 100%;
      }
      .approval-name {
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: #0a1633;
        line-height: 20px;
        padding-right: 8px;
      }
      .approval-date {
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: rgba(10, 22, 51, 0.6);
        line-height: 20px;
      }
      .approval-status {
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 14px;
        line-height: 20px;
        padding-top: 4px;
        display: flex;
        align-items: center;
        gap: 8px;
        .approval-desc {
          flex: 1;
        }
      }
      .error-status {
        color: #ff4d4f;
      }
      .success-status {
        color: #67c23a;
      }
      .warning-status {
        color: #e6a23c;
      }
      .info-status {
        color: #909399;
      }
    }
  }
}
</style>
