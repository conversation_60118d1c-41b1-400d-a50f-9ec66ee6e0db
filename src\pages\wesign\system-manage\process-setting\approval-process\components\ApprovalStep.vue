<template>
  <div class="step-component">
    <div v-for="(item, index) in approvalStepOptions" :key="item.value" class="step-item">
      <div class="step-item__content" @click="changeStep(item.value)">
        <div v-if="activeStep === 1 && index === 0 && props.status" class="step-item__icon color-success">
          <el-icon><CircleCheck /></el-icon>
        </div>
        <div v-else-if="activeStep === 1 && index === 0 && !props.status" class="step-item__icon color-error">
          <el-icon><CircleClose /></el-icon>
        </div>

        <div
          v-else
          :class="{
            'is-active': activeStep >= index
          }"
          class="step-item__title"
        >
          {{ item.value + 1 }}
        </div>
        <div :class="activeStep >= index ? 'is-active__text' : ''" class="step-item__label">{{ item.label }}</div>
      </div>
      <div v-if="index !== approvalStepOptions.length - 1" class="approval-line"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue"

interface StepProps {
  activeStep: number
  status: boolean
}

const approvalStepOptions = ref([
  {
    value: 0,
    label: "基本信息"
  },
  {
    value: 1,
    label: "审批节点"
  }
])

const props = defineProps<StepProps>()

const emits = defineEmits(["update:activeStep"])

const activeStep = computed({
  get: () => props.activeStep,
  set: (val: number) => {
    emits("update:activeStep", val)
  }
})
const changeStep = step => {
  activeStep.value = step
}
</script>

<style scoped lang="less">
.step-component {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-bottom: 16px;
  .step-item {
    display: flex;
    align-items: center;
    &__content {
      display: flex;
      align-items: center;
      cursor: pointer;
    }
    &__title {
      font-size: 16px;
      font-weight: bold;
      width: 35px;
      height: 35px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      background: #dcdfe6;
      color: #81878e;
    }
    &__label {
      font-size: 16px;
      font-weight: 500;
    }
    &__icon {
      font-size: 36px;
      width: 35px;
      height: 35px;
      line-height: 35px;
      margin-right: 16px;
    }
    .color-success {
      color: var(--el-color-primary);
    }
    .color-error {
      color: red;
    }
    .is-active {
      background-color: var(--el-color-primary);
      color: #fff;
    }
    .is-active__text {
      color: var(--el-color-primary);
    }
  }
  .approval-line {
    width: 250px;
    border: 1px dashed var(--el-color-primary);
    margin: 0 10px;
  }
}
</style>
