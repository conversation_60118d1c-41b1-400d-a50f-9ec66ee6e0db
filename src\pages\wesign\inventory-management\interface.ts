import { off } from "process"
import axios from "@/interfaces/axios-instance"

/**
 * @method GET
 * @desc   库房管理-获取库房列表
 */
export function getStoreroomListApi() {
  return axios({
    method: "get",
    url: `/api/document/storage-manage/storage-rooms`
  })
}

/**
 * @method POST
 * @desc   库房管理-新增库房
 */
interface AddStoreroomParams {
  documentStorageRoomName: string // 库房名称
  documentStorageRoomNumber: string // 库房编码
  documentStorageRoomAddress: string // 库房地址
}
export function addStoreroomApi(data: AddStoreroomParams) {
  return axios({
    method: "post",
    url: `/api/document/storage-manage/storage-rooms`,
    data
  })
}

/**
 * @method PUT
 * @desc   库房管理-编辑库房
 */
// 参数继承于UpdateStoreroomParams
interface EditStoreroomParams extends AddStoreroomParams {
  documentStorageRoomWsid: string // 库房id
}
export function editStoreroomApi(data: EditStoreroomParams) {
  const { documentStorageRoomWsid, ...rest } = data
  return axios({
    method: "put",
    url: `/api/document/storage-manage/storage-rooms/${documentStorageRoomWsid}`,
    data: {
      ...rest
    }
  })
}

/**
 * @method DELETE
 * @desc   库房管理-删除库房
 */
export function deleteStoreroomApi(documentStorageRoomWsid: string) {
  return axios({
    method: "delete",
    url: `/api/document/storage-manage/storage-rooms/${documentStorageRoomWsid}`
  })
}

/**
 * @method POST
 * @desc   库房管理-新增库位
 */
interface AddStoreLocationParams {
  documentStorageRoomWsid: string // 库位编码
  documentStorageRackName: string // 库位名称
  documentStorageRackNumber: string // 库位编码
}

export function addStoreLocationApi(data: AddStoreLocationParams) {
  return axios({
    method: "post",
    url: `/api/document/storage-manage/storage-racks`,
    data
  })
}

/**
 * @method PUT
 * @desc   库房管理-编辑库位
 */
interface EditStoreLocationParams {
  documentStorageRackName: string // 库位名称
  documentStorageRackNumber: string // 库位编码
  documentStorageRackWsid: string // 库位id
}
export function editStoreLocationApi(data: EditStoreLocationParams) {
  const { documentStorageRackWsid, ...rest } = data
  return axios({
    method: "put",
    url: `/api/document/storage-manage/storage-racks/${documentStorageRackWsid}`,
    data: {
      ...rest
    }
  })
}

/**
 * @method DELETE
 * @desc   库房管理-删除库位
 */
export function deleteStoreLocationApi(documentStorageRackWsid: string) {
  return axios({
    method: "delete",
    url: `/api/document/storage-manage/storage-racks/${documentStorageRackWsid}`
  })
}

/**
 * @method GET
 * @desc   库房管理-获取库位列表
 */
interface GetStoreLocationListParams {
  documentStorageRoomWsid: string // 库房id
  offset: number // 偏移量
  limit: number // 限制数量
}
export function getStoreLocationListApi(params: GetStoreLocationListParams) {
  return axios({
    method: "get",
    url: `/api/document/storage-manage/storage-rooms/${params.documentStorageRoomWsid}`,
    params: {
      offset: params.offset || 0,
      limit: params.limit
    }
  })
}

// 获取装箱管理列表
export function getPackingListApi(data) {
  return axios({
    method: "post",
    url: `/api/document/storage-manage/storage-boxes-fuzzy`,
    data: data
  })
}

// 病案装箱
interface PackingParams {
  documentStorageBoxNumber: string // 待装箱箱号
  documents: Array<{
    barCode: string // 病案条码
  }> //病案基本信息
}
export function createPackingApi(data: PackingParams) {
  return axios({
    method: "post",
    url: `/api/document/storage-manage/storage-boxes`,
    data
  })
}

// 病案箱移除
export function removePackingApi(documentStorageBoxWsid) {
  return axios({
    method: "delete",
    url: `/api/document/storage-manage/storage-boxes/${documentStorageBoxWsid}`
  })
}

// 获取病案箱详情
interface PackingDetailParams {
  documentStorageBoxWsid: string // 箱号id
  offset: number // 偏移量
  limit: number // 限制数量
}
export function getPackingDetailApi(params: PackingDetailParams) {
  return axios({
    method: "get",
    url: `/api/document/storage-manage/storage-boxes/${params.documentStorageBoxWsid}`,
    params: {
      offset: params.offset || 0,
      limit: params.limit
    }
  })
}

interface OnShelfOrOffShelfParams {
  documentStorageRackWsid: string // 库位id
  documentStorageBoxWsids: Array<string> // 待上架箱号
}
// 病案箱上架
export function onShelfPackingApi(data: OnShelfOrOffShelfParams) {
  return axios({
    method: "put",
    url: `/api/document/storage-manage/storage-boxes/shelve`,
    data: data
  })
}

// 病案箱移库
export function offShelfPackingApi(data: OnShelfOrOffShelfParams) {
  return axios({
    method: "put",
    url: `/api/document/storage-manage/storage-boxes/transfer`,
    data: data
  })
}

// 检测某个库存是否满足删除的条件
export function checkStorageDeleteApi(documentStorageWsid) {
  return axios({
    method: "get",
    url: `/api/document/storage-manage/check-allowed-delete/${documentStorageWsid}`
  })
}

// 删除病案箱里的病案
export function deletePackingDocumentApi(documentStorageBoxWsid, documentBagWsid) {
  return axios({
    method: "delete",
    url: `/api/document/storage-manage/storage-boxes/${documentStorageBoxWsid}/document-bag/${documentBagWsid}`
  })
}

// 通过病案条形码查询病案
export function getDocumentByBarCodeApi(barCode) {
  console.log(barCode)
  return axios({
    method: "post",
    url: `/api/document/bags/bar_code`,
    data: [barCode]
  })
}
