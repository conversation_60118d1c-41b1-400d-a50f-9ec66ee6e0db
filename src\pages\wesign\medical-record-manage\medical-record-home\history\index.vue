<template>
  <PageContainer separate>
    <template #search>
      <Tabs />

      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
        />

        <DepartmentFormItem v-model:model-value="searchFormState.outHospitalDeptWsid" label="出院科室" />

        <CommonSelectFormItem
          v-model="searchFormState.status"
          label="病案状态"
          :options="globalOptionsStore.medicalRecordStatusOptions"
        />

        <DaterangeFormItem v-model:model-value="searchFormState.inHospitalDatetime" label="入院日期" />

        <DaterangeFormItem v-model:model-value="searchFormState.outHospitalDatetime" label="出院日期" />

        <CommonSelectFormItem
          v-model="searchFormState.isOutHospital"
          label="是否出院"
          :options="inHospitalOptions"
          @change="handleChangeIsInHospital"
        />

        <PatientLabelFormItem v-model="searchFormState.patientLabel" multiple />

        <el-form-item label="病案标记">
          <el-select v-model="medicalSymbolValue" clearable filterable multiple @change="handleSelectSymbol">
            <el-option v-for="item in symbolOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>

        <template #after-action>
          <el-badge style="margin-left: auto" :is-dot="advancedOptions ? true : false">
            <el-button
              style="margin-left: auto"
              :type="advancedOptions ? 'primary' : 'default'"
              @click="ComplexSearchRef?.openDrawer()"
            >
              高级搜索
            </el-button>
          </el-badge>
        </template>
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        v-if="displayMode === 'table'"
        ref="tableRef"
        table-id="historyRecordTable"
        :table-columns="historyTableColumns"
        :request-api="getHistoryMedicalRecordListApi"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <template #header>
          <TooltipButton
            tooltip="切换为卡片展示模式"
            :icon="h('i', { class: 'ri-function-line', style: { 'font-size': '18px' } })"
            @click="toggleDisplayMode"
          ></TooltipButton>
          <BatchOperationButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Favorite)"
            type="warning"
            :disabled="!selectedRows.length"
            :icon="StarFilled"
            @click="collectDialogRef?.open()"
          >
            批量收藏
          </BatchOperationButton>

          <el-popover
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Export)"
            placement="bottom"
            width="210"
            trigger="hover"
          >
            <template #reference>
              <el-button :icon="Download">导出</el-button>
            </template>

            <PopoverButton
              :tip="`(当前选中${tableRef?.tableState.selectedRows.length}条数据)`"
              :center="false"
              @click="handleExportSelected"
            >
              导出选中
            </PopoverButton>
            <el-tooltip effect="dark" content="单次最大数据导出量10000条" placement="bottom">
              <PopoverButton :center="false" :tip="`(共${tableRef?.tableState.total}条数据)`" @click="handleExportAll">
                导出全部
              </PopoverButton>
            </el-tooltip>
          </el-popover>
        </template>

        <!-- 业务详情 -->
        <template #businessDetails="{ row }">
          <div>
            <div class="business-tip" @click.stop="handleMedical(row)">
              <div class="business-tip-placeholder"></div>
              <img
                class="business-tip-icon"
                :style="{ filter: `drop-shadow(500px 0 ${bulbColor(row)})` }"
                src="@/assets/home/<USER>"
              />
            </div>
          </div>
        </template>

        <!-- 患者姓名 -->
        <template #patientName="{ row }">
          <div style="display: flex; align-items: center; flex-wrap: wrap">
            <el-button
              link
              type="primary"
              @click="handleRowClick(row, '/medical-record/detail', MenuOperationEnum.View)"
            >
              {{ row.patientName }}
            </el-button>
            <PatientLabelTag :row="row" />
          </div>
        </template>

        <!-- 封存 -->
        <template #sealingStatus="{ row }">
          {{ sealingStatusOptions.find(option => option.value === row.sealingStatus)?.label }}
        </template>

        <!-- 特殊病案 -->
        <template #isSpecial="{ row }">
          <el-button
            v-if="row.isSpecialEnum === 'YES'"
            type="primary"
            link
            @click.stop="showSpecialDialog(row, 'check')"
          >
            是
          </el-button>
          <el-button v-else link @click.stop>否</el-button>
        </template>

        <!-- 缺失记录数量 -->
        <template #deletionCount="{ row }">
          <el-button v-if="row.deletionCount >= 1" type="primary" link @click.stop="showMissingDetail(row)">
            {{ row.deletionCount }}
          </el-button>
          <el-button v-else link @click.stop>{{ row.deletionCount }}</el-button>
        </template>

        <!-- 住院时间 -->
        <template #inHospitalDatetime="{ row }">
          {{ formatDatetime(row.inHospitalDatetime) }}
        </template>

        <!-- 出院时间 -->
        <template #outHospitalDatetime="{ row }">
          {{ formatDatetime(row.outHospitalDatetime) }}
        </template>

        <!-- 是否死亡 -->
        <template #death="{ row }">{{ row.death === 1 ? "是" : "否" }}</template>

        <template #operation="{ row }">
          <div style="display: flex">
            <div v-for="btn in operationBtnList" :key="btn.key" @click.stop>
              <TableButton
                v-if="hasOperationPermission(menuId, btn.key)"
                :disabled="isOperationDisabled(btn, row)"
                @click="handleRowClick(row, btn.path, btn.key)"
              >
                <span v-if="btn.key === MenuOperationEnum.Favorite">{{ row.isFavorite ? "取消收藏" : "收藏" }}</span>
                <span v-else>{{ btn.label }}</span>
              </TableButton>
            </div>
          </div>
        </template>
      </CommonTable>

      <PatientCardList
        v-else
        ref="patientCardListRef"
        :request-params="searchParams"
        :request-api="getHistoryMedicalRecordListApi"
        @to-detail="handleRowClick"
      >
        <template #header>
          <TooltipButton
            tooltip="切换为表格展示模式"
            :icon="h('i', { class: 'ri-list-check', style: { 'font-size': '18px' } })"
            @click="toggleDisplayMode"
          ></TooltipButton>
        </template>

        <!-- 卡片列表操作按钮 -->
        <template #operation="{ row }">
          <div style="display: flex">
            <div v-for="btn in operationBtnList" :key="btn.key">
              <TableButton
                v-if="hasOperationPermission(menuId, btn.key)"
                :disabled="isOperationDisabled(btn, row)"
                @click="handleRowClick(row, btn.path, btn.key)"
              >
                <span v-if="btn.key === MenuOperationEnum.Favorite">{{ row.isFavorite ? "取消收藏" : "收藏" }}</span>
                <span v-else>{{ btn.label }}</span>
              </TableButton>
            </div>
          </div>
        </template>
      </PatientCardList>
    </template>

    <!-- 导出弹窗 -->
    <ExportDialog
      ref="exportDialogRef"
      :total="tableRef?.tableState.total || 0"
      :selected-rows="selectedRows"
      :filters="searchParams.filters"
      :export-all="exportAll"
    />

    <!-- 收藏弹窗 -->
    <CollectDialog
      ref="collectDialogRef"
      :table-ref="tableRef"
      :selected-rows="selectedRows"
      :selected-row="operationState.selectedRow"
    />

    <!-- 封存检查弹窗 -->
    <SealDialog ref="sealDialogRef" :selected-row="operationState.selectedRow" :confirm-callback="confirmOperation" />

    <!-- 特殊病案弹窗 -->
    <SpecialDialog
      ref="specialDialogRef"
      :selected-row="operationState.selectedRow"
      @success="tableRef?.refreshTableData"
    />

    <!-- 缺失详情弹窗 -->
    <MissingDetailDialog
      ref="missingDetailDialogRef"
      :get-record-type="getRecordType"
      :selected-row="operationState.selectedRow"
      @success="tableRef?.refreshTableData"
    />

    <!-- 示踪抽屉 -->
    <TraceDrawer
      ref="traceDrawerRef"
      :inp-no="operationState.selectedRow?.inpNo"
      @check-submit="showSubmitHistoryDialog"
    />

    <!-- 高级搜索 -->
    <ComplexSearch
      ref="ComplexSearchRef"
      filter-type="EMR_QEURY"
      :disabled-options-prop="disabledOptions"
      :default-active-template-id="defaultActiveTemplateID"
      :default-advanced-options="defaultAdvancedOptions"
      @search="handleQuery"
      @reset="handleReset"
    />

    <!-- 提交历史记录弹窗 -->
    <SubmitHistoryDialog ref="submitHistoryDialogRef" />
  </PageContainer>

  <!-- 业务详情的抽屉 -->
  <BusinessDetailDrawer ref="businessDetailDrawerRef" />
</template>

<script setup lang="ts">
import { reactive, ref, computed, nextTick, onBeforeMount, h } from "vue"
import { useRouter } from "vue-router"
import { cloneDeep } from "lodash-es"
import { StarFilled, Download } from "@element-plus/icons-vue"
import {
  SearchContainer,
  CommonTable,
  PatientFilterFormItem,
  PatientLabelFormItem,
  DepartmentFormItem,
  DaterangeFormItem,
  BatchOperationButton,
  TableButton,
  PopoverButton,
  PageContainer,
  CommonSelectFormItem,
  TooltipButton,
  PatientLabelTag
} from "@/base-components"
import { SealDialog, ComplexSearch } from "@/page-components"
import { cancelCollectApi } from "../../interface"
import {
  CollectDialog,
  ExportDialog,
  SpecialDialog,
  MissingDetailDialog,
  TraceDrawer,
  SubmitHistoryDialog,
  PatientCardList,
  BusinessDetailDrawer,
  Tabs
} from "../components"
import {
  sealingStatusOptions,
  inHospitalOptions,
  symbolOptions,
  historyBtnConfig,
  historyTableColumns,
  menuId
} from "../config"
import { getHistoryMedicalRecordListApi } from "../interface"
import { MedicalRecordStatusEnum, MenuOperationEnum } from "@/configs"
import { useCommonOptions } from "@/hooks"
import { permissionCheckApi, getAllDocTypes } from "@/interfaces"
import { useGlobalOptionsStore, useSystemStore, useUserStore } from "@/stores"
import { encryptStr, Message, SystemPrompt, formatDatetime, toastError } from "@/utils"

const router = useRouter()
const systemStore = useSystemStore()
const { hasOperationPermission } = useUserStore()
const globalOptionsStore = useGlobalOptionsStore()

// 病案类型数据
const { getOptionLabel: getRecordType } = useCommonOptions({
  getOptionsApi: getAllDocTypes,
  labelAlias: "mrClassName",
  valueAlias: "mrClassCode"
})

/* ================ 高级筛选 ============== */
const ComplexSearchRef = ref()
const disabledOptions = ["a11", "a48", "b15", "b16c"]

/* ================ 操作按钮 ============== */

const operationBtnList = computed(() => {
  return historyBtnConfig.filter(item => hasOperationPermission(menuId, item.key))
})

// 检查操作按钮是否禁用
const isOperationDisabled = (config, row) => {
  // 同步
  if (
    config.key === MenuOperationEnum.Sync &&
    row.statusEnumName !== MedicalRecordStatusEnum.COLLECTING &&
    row.statusEnumName !== MedicalRecordStatusEnum.REPAIR
  ) {
    return true
  }
  return false
}

/*===================== 表格展示方式 =====================*/

const displayMode = ref<"table" | "card">("table")

// 切换数据展示方式
const toggleDisplayMode = () => {
  displayMode.value = displayMode.value === "table" ? "card" : "table"
}

/*===================== 查询 =====================*/

const tableRef = ref<InstanceType<typeof CommonTable>>()
const selectedRows = computed(() => tableRef.value?.tableState?.selectedRows ?? [])
const archiveDateEnd = new Date().setHours(23, 59, 59, 999)
const archiveDateStart = new Date(new Date().setMonth(new Date().getMonth() - 6)).setHours(0, 0, 0, 0)
const initFormValue = {
  patientFilterValue: "",
  patientFilterProp: "patientName",
  outHospitalDeptWsid: "", // 出院科室
  status: "" as "" | number, // 病案状态
  inHospitalDatetime: "", // 入院日期
  outHospitalDatetime: [archiveDateStart, archiveDateEnd] as [number, number] | "", // 出院日期
  sealingStatus: "", // 封存状态
  isLateFiling: false, // 迟归标记
  isSealing: false, // 封存标记
  isSpecial: false, //特殊标记
  isRepair: false, // 返修标记 YES | NO
  isInHospital: false, //在院患者
  isOutHospital: "", //出院患者
  patientLabel: [] //患者标签
}
const searchFormState = reactive(cloneDeep(initFormValue))

const searchParams = reactive({
  filters: `outHospitalDatetime>=${archiveDateStart},outHospitalDatetime<=${archiveDateEnd}`,
  orFieldName: ""
})

const defaultActiveTemplateID = ref("")
const defaultAdvancedOptions = ref([])
// 选择是否出院
const handleChangeIsInHospital = val => {
  if (typeof val === "boolean") searchFormState.isInHospital = !val
  if (searchFormState.isInHospital) searchFormState.outHospitalDatetime = ""
}

// 选择病案标记
const medicalSymbolValue = ref<Array<string>>([])
const handleSelectSymbol = val => {
  searchFormState.isLateFiling = false
  searchFormState.isRepair = false
  searchFormState.isSealing = false
  searchFormState.isSpecial = false
  val.forEach(item => {
    searchFormState[item] = true
  })
}

onBeforeMount(() => {
  const status = (router.currentRoute.value.query?.status as string) || ""
  if (status) {
    searchFormState.status = Number(status)
    searchFormState.outHospitalDatetime = ""
    searchParams.filters = `status=${status}`
    saveParams()
    router.replace("/medical-record/list/recently")
  } else {
    const pageParams = systemStore.pageParams.find(item => item.id === location.pathname)
    //病案标记列表
    const medicalSymbolValueList = symbolOptions.map(item => item.value)
    for (const key in pageParams?.searchFilterForm) {
      searchFormState[key] = pageParams?.searchFilterForm[key]
      // 回显已勾选的病案标记
      if (medicalSymbolValueList.includes(key) && pageParams?.searchFilterForm[key]) {
        medicalSymbolValue.value.push(key)
      }
    }
    for (const key in pageParams?.searchParams) {
      searchParams[key] = pageParams?.searchParams[key]
    }
    defaultActiveTemplateID.value = pageParams?.activeTemplateID || ""
    defaultAdvancedOptions.value = pageParams?.advancedOptions || []
  }
  isInit.value = true
})

const isInit = ref(true)
const advancedOptions = computed(() => {
  let haveFilter = false
  if (isInit.value && (defaultActiveTemplateID.value || defaultAdvancedOptions.value.length > 0)) {
    haveFilter = true
  }
  if (ComplexSearchRef.value?.advancedFilters.filters) {
    haveFilter = true
  }
  return haveFilter
})

// 搜索
function handleQuery() {
  const filters: Array<string> = []
  if (searchFormState.patientFilterProp && searchFormState.patientFilterValue) {
    filters.push(`${searchFormState.patientFilterProp}=${searchFormState.patientFilterValue}`)
  }
  console.log(`output->searchFormState`, searchFormState)
  for (const key in searchFormState) {
    const value = searchFormState[key]
    // 复合字段跳过
    if (key === "patientFilterProp" || key === "patientFilterValue") {
      continue
      // 日期范围字段
    } else if (Array.isArray(value) && value.length === 2 && key.includes("Datetime")) {
      filters.push(`${key}>=${value[0]}`, `${key}<=${value[1] + (1000 * 60 * 60 * 24 - 1)}`)
    } else if (typeof value === "boolean" && value) {
      filters.push(`${key}=YES`)
    } else if (key === "patientTag") {
      value.forEach(item => {
        filters.push(item)
      })
    }
    // 其他基础字段
    else if (value || value === 0) {
      if (Array.isArray(value)) {
        if (value.length) filters.push(`${key}=${value.join("#")}`)
      } else {
        filters.push(`${key}=${value}`)
      }
    }
  }

  // 获取复杂查询的筛选值
  ComplexSearchRef.value?.getAdvancedFilters()
  if (ComplexSearchRef.value?.advancedFilters.filters)
    filters.push(`${ComplexSearchRef.value?.advancedFilters.filters}`)
  searchParams.filters = filters.join(",")
  searchParams.orFieldName = ComplexSearchRef.value?.advancedFilters.orFieldName || ""
  saveParams()
}

// 重置
function handleReset() {
  for (let key in searchFormState) {
    if (key === "patientFilterProp") searchFormState[key] = "patientName"
    else searchFormState[key] = initFormValue[key]
  }
  searchParams.filters = `outHospitalDatetime>=${archiveDateStart},outHospitalDatetime<=${archiveDateEnd}`
  searchParams.orFieldName = ""
  // 清除复杂查询的筛选值
  ComplexSearchRef.value?.resetValue()
  medicalSymbolValue.value = []
  saveParams()
}

function saveParams() {
  isInit.value = false

  systemStore.setPageParams({
    id: location.pathname,
    searchParams: searchParams,
    searchFilterForm: searchFormState,
    activeTemplateID: ComplexSearchRef.value?.activeTemplateID || "",
    advancedOptions: ComplexSearchRef.value?.advancedOptions || []
  })
}

const dataCallback = (data: Array<Record<string, any>>) => {
  return data.map(item => ({
    ...item,
    patientTag: {
      death: item.death,
      daySurgery: item.daySurgery,
      nonMedicalLeave: item.nonMedicalLeave
    },
    hidePatientTag: [item.death, item.daySurgery, item.nonMedicalLeave].every(item => item === 0)
  }))
}

// 灯泡颜色
const bulbColor = row => {
  return globalOptionsStore.medicalRecordStatusOptions.find(item => item.statusEnumName === row.statusEnumName)?.color
}

/*===================== 行操作相关 =====================*/

const operationState = reactive({
  selectedRow: {} as Record<string, any>,
  routerPath: "",
  key: ""
})

const sealDialogRef = ref() // SealDialog组件ref

// 点击行的操作
const handleRowClick = (row: Record<string, any>, routerPath: string, operationKey: string) => {
  operationState.selectedRow = row
  operationState.routerPath = routerPath
  operationState.key = operationKey
  // 收藏/取消收藏
  if (operationKey === MenuOperationEnum.Favorite) {
    handleCollectSingle()
  }
  // 示踪
  else if (operationKey === MenuOperationEnum.Trace) {
    nextTick(() => traceDrawerRef.value?.show())
  } else {
    permissionCheckApi(row?.inpNo).then(res => {
      if (res.data.data) {
        sealDialogRef.value.checkSealed()
      } else {
        Message.warning("无当前操作权限")
      }
    })
  }
}

// 检查封存成功之后的回调操作
const confirmOperation = () => {
  const query = { inpNo: operationState.selectedRow?.inpNo }
  if (sealDialogRef.value.secretKey) {
    query["secretKey"] = encryptStr(sealDialogRef.value.secretKey)
  }
  router.push({
    path: operationState.routerPath,
    query: query
  })
}

/*===================== 收藏 =====================*/

const collectDialogRef = ref()

// 收藏单条
const handleCollectSingle = () => {
  // 已收藏 - 取消收藏
  if (operationState.selectedRow?.isFavorite) {
    handleCancelCollect()
  }
  // 未收藏 - 收藏
  else {
    collectDialogRef.value.open()
  }
}

// 取消收藏
const handleCancelCollect = () => {
  SystemPrompt("是否取消收藏？").then(() => {
    cancelCollectApi([operationState.selectedRow?.wsid])
      .then(() => {
        tableRef.value?.refreshTableData()
        Message.success("操作成功")
      })
      .catch(err => toastError(err))
  })
}

/*===================== 导出 =====================*/

const exportDialogRef = ref()
const exportAll = ref(false)

// 导出选中
const handleExportSelected = () => {
  if (!selectedRows.value.length) return Message.warning("请至少勾选一条数据")
  exportAll.value = false
  exportDialogRef.value.open()
}

// 导出所有
const handleExportAll = () => {
  if (!tableRef.value?.tableState.total) {
    return Message.warning("没有数据可以导出")
  }
  exportAll.value = true
  exportDialogRef.value.open()
}

/*===================== 特殊病案 =====================*/

const specialDialogRef = ref<InstanceType<typeof SpecialDialog>>()

const showSpecialDialog = (row, type) => {
  operationState.selectedRow = row
  nextTick(() => specialDialogRef.value?.show(type))
}

/*===================== 示踪 =====================*/

const traceDrawerRef = ref<InstanceType<typeof TraceDrawer>>()

/*===================== 提交版本记录 =====================*/

const submitHistoryDialogRef = ref<InstanceType<typeof SubmitHistoryDialog>>()

const showSubmitHistoryDialog = action => {
  traceDrawerRef.value?.close()
  submitHistoryDialogRef.value?.show(action)
}

/*===================== 缺失详情 =====================*/

const missingDetailDialogRef = ref<InstanceType<typeof MissingDetailDialog>>()

const showMissingDetail = row => {
  operationState.selectedRow = row
  nextTick(() => missingDetailDialogRef.value?.show())
}

/*============================ 业务详情 ============================*/

const businessDetailDrawerRef = ref<InstanceType<typeof BusinessDetailDrawer>>()

const handleMedical = row => {
  businessDetailDrawerRef.value?.show(row)
}
</script>

<style lang="less" scoped>
.business-tip {
  position: relative;

  .business-tip-placeholder {
    width: 20px;
    height: 20px;
    cursor: pointer;
  }

  .business-tip-icon {
    transform: translateX(-500px) translateY(-50%);
    position: absolute;
    left: 0;
    top: 50%;
  }
}
</style>
