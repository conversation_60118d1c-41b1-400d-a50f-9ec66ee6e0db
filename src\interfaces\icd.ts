import axios from "./axios-instance"

/* ======================== icd查询公共接口 ======================== */

/**
 * @method GET
 * @desc   ICD9-CM列表查询
 */
export function getIcdCmListApi(params) {
  return axios({ method: "get", url: `/api/hospital/icd-cm`, params })
}

/**
 * @method GET
 * @desc   ICD10-列表查询
 */
export function getIcdListApi(params) {
  return axios({ method: "get", url: "/api/hospital/icd", params })
}

/**
 * @method GET
 * @desc   肿瘤形态学编码-列表查询
 */
export function getTumourApi(params) {
  return axios({ method: "get", url: `/api/hospital/icd-o`, params })
}

/**
 * @method GET
 * @desc   ICD-版本查询
 */
export function getIcdLevelConfigApi(obj) {
  const { type } = obj
  return axios({
    method: "get",
    url: "/api/hospital/icd/version",
    params: {
      type
    }
  })
}

/**
 * @method GET
 * @desc   医保icd模糊查询
 */
export function getIcdCode(params: Record<string, any>) {
  return axios({
    method: "get",
    url: `/api/hospital/icd/medical-insurance/fuzzy-query/code`,
    params
  }).then(res => res.data?.data ?? [])
}
