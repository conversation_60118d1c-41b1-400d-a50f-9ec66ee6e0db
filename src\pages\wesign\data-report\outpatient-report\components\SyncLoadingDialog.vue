<template>
  <el-dialog v-model="visible" title="数据同步">
    <el-result v-if="loading" style="padding: 0">
      <template #icon>
        <Spin :spinning="loading"></Spin>
      </template>
      <template #title>
        <div style="font-size: 18px; color: #333">数据同步中，请稍候……</div>
      </template>
      <template #sub-title>
        <div style="color: red; margin-top: 20px">关闭窗口后不会中止，将会自动运行</div>
      </template>
    </el-result>
    <el-result v-else icon="success" title="同步成功" :sub-title="`数据同步成功，共同步${syncCount}条数据`"></el-result>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from "vue"
import { Spin } from "ant-design-vue"

const loading = ref(false)
const syncCount = ref(0)

const finish = count => {
  syncCount.value = count
  loading.value = false
}

const visible = ref(false)

const show = () => {
  visible.value = true
  loading.value = true
}

const close = () => {
  visible.value = false
}

defineExpose({
  show,
  close,
  finish
})
</script>

<style lang="less" scoped>
.count {
  color: red;
}
</style>
