<template>
  <el-form ref="formRef" class="page-search-form" label-width="auto" @keydown.enter="$emit('queryBtnClick')">
    <slot></slot>
    <el-form-item>
      <el-button
        type="primary"
        :loading="props.searchLoading"
        :icon="h('i', { class: 'ri-search-line', style: { 'font-size': '16px' } })"
        @click="$emit('queryBtnClick')"
      >
        查询
      </el-button>
      <el-button
        :disabled="props.searchLoading"
        :icon="h('i', { class: 'ri-refresh-line', style: { 'font-size': '16px' } })"
        @click="$emit('resetBtnClick')"
      >
        重置
      </el-button>
      <slot name="action"></slot>
    </el-form-item>
    <slot name="after-action"></slot>
  </el-form>
</template>

<script setup lang="ts">
import { h, ref } from "vue"

const formRef = ref()

const props = defineProps({
  searchLoading: { type: Boolean, default: false }
})

function queryBtnClick() {
  formRef.value?.validate(valid => {
    if (!valid) return
    emits("queryBtnClick")
  })
}

const emits = defineEmits(["resetBtnClick", "queryBtnClick"])
</script>

<style lang="less" scoped>
.page-search-form {
  display: flex;
  column-gap: 20px;
  padding-top: 20px;
  flex-wrap: wrap;

  :deep(.el-date-editor) {
    width: 240px;
  }

  :deep(.el-form-item) {
    // width: 30% !important;
    // min-width: 30% !important;
    width: 19vw;
    max-width: 20vw;
    min-width: 280px;
  }

  :deep(.el-form-item__content) {
    .el-select,
    .el-input {
      width: 100% !important;
    }
    .el-input-group__prepend {
      padding: 0 !important;
    }
  }
}
</style>
