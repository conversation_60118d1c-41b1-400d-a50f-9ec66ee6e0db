<template>
  <DialogContainer
    ref="dialogRef"
    v-model:visible="visible"
    :title="dialogTitle"
    :width="curTab !== 'user' ? 632 : 908"
    :cancel-callback="clear"
    :confirm-callback="handleConfirmAuthorize"
  >
    <el-form ref="formRef" label-position="left" label-width="100px">
      <el-form-item label="授权对象：">
        <el-radio-group v-model="curTab" @change="handleTabChange">
          <el-radio label="user">用户</el-radio>
          <el-radio label="department">科室</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="authorizeType === 'department'" label="保密等级：" prop="secrecyLevel">
        <el-select v-model="deptState.secrecyLevel" style="width: 200px" placeholder="请选择保密等级">
          <el-option v-for="item in secrecyLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
    </el-form>

    <!-- 授权给用户 -->
    <div v-if="curTab === 'user'" class="authorize-container">
      <div class="authorize-wrapper">
        <div class="authorize-title">选择用户：</div>
        <div class="authorize-card">
          <div>
            <div class="authorize-card__header">
              <el-input v-model="userState.keyword" :prefix-icon="Search" placeholder="姓名/工号" />
            </div>
            <div class="authorize-dept-list">
              <div
                class="dept"
                :class="userState.selectedDeptWsid ? '' : 'dept--selected'"
                @click="handleSelectDept({ label: '全部科室', value: '' })"
              >
                全部科室
              </div>
              <div
                v-for="option in globalOptionsStore.departmentOptions"
                :key="option.value"
                class="dept"
                :class="userState.selectedDeptWsid === option.value ? 'dept--selected' : ''"
                @click="handleSelectDept(option)"
              >
                {{ option.label }}
              </div>
            </div>
          </div>

          <div>
            <div class="authorize-card__header">
              <el-checkbox
                v-model="userState.checkAll"
                style="margin-left: 10px"
                :indeterminate="userState.indeterminate"
                @change="handleCheckAllUser"
              >
                全选 ({{ userState.deptUserList.length }})
              </el-checkbox>
            </div>
            <div v-infinite-scroll="handleUserListScroll" :infinite-scroll-distance="1" class="dept-user-list">
              <el-checkbox-group
                v-model="userState.checkedUserList"
                class="custom-checkbox-group user-checkbox-group"
                @change="handleCheckUser"
              >
                <el-checkbox
                  v-for="user in userState.deptUserList"
                  :key="user.userWsid"
                  :label="user.userWsid"
                  class="custom-checkbox user-checkbox"
                >
                  <div class="user">
                    <img class="user__avatar" src="~@/assets/png/avatar.png" alt="" />
                    <span class="user__name">{{ `${user.realName}(${user.jobId})` }}</span>
                  </div>
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </div>
      </div>

      <div class="authorize-wrapper">
        <div class="authorize-title">已授权用户：</div>
        <div class="authorize-card--small">
          <div class="authorized-user-list">
            <div v-for="user in userState.authorizedUserList" :key="user.wsid" class="user" style="margin-bottom: 8px">
              <div class="flex-start">
                <img class="user__avatar" src="~@/assets/png/avatar.png" />
                <span class="user__name">{{ `${user.realName}(${user.jobId})` }}</span>
              </div>
              <el-icon style="font-size: large; cursor: pointer" @click="handleDeleteAuthorizedUser(user.wsid)">
                <CircleClose />
              </el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 授权给科室 -->
    <div v-else class="authorize-container--dept">
      <div class="authorize-wrapper">
        <div class="authorize-title">选择科室：</div>
        <div class="authorize-card--dept">
          <div class="authorize-card__header">
            <el-input v-model="deptState.keyword" :prefix-icon="Search" placeholder="科室名"></el-input>
          </div>
          <el-checkbox
            v-model="deptState.checkAll"
            :indeterminate="deptState.indeterminate"
            @change="handleCheckAllDept"
          >
            全部科室
          </el-checkbox>
          <div class="dept-checkbox-group">
            <el-checkbox-group
              v-model="deptState.checkedDeptList"
              class="custom-checkbox-group"
              @change="handleCheckDept"
            >
              <el-checkbox
                v-for="dept in deptState.deptList"
                :key="dept.wsid"
                class="custom-checkbox dept-checkbox"
                :label="dept.wsid"
              >
                {{ dept.deptName }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
      </div>

      <div class="authorize-wrapper">
        <div class="authorize-title">已授权科室({{ deptState.authorizedDeptList.length }})</div>
        <div class="authorize-card--small">
          <div class="authorized-user-list">
            <div v-for="dept in deptState.authorizedDeptList" :key="dept.wsid" class="authorized-dept">
              <span class="authorized-dept__name">{{ dept.deptName }}</span>
              <el-icon style="font-size: large; cursor: pointer" @click="handleDeleteAuthorizedDept(dept.wsid)">
                <CircleClose />
              </el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
  </DialogContainer>
</template>

<script setup lang="ts">
import { reactive, ref, computed, watch, nextTick } from "vue"
import { debounce, cloneDeep } from "lodash-es"
import { Search } from "@element-plus/icons-vue"
import { DialogContainer } from "@/base-components"
import { secrecyLevelOptions } from "@/configs"
import { getHospitalDeptsApi, getUsersInDeptsApi, getFuzzyUsersInDeptsApi } from "@/interfaces"
import { useGlobalOptionsStore } from "@/stores"
import { Message, toastError } from "@/utils"

import {
  getBorrowMrnoAuthorizationDeptsApi,
  getBorrowMrnoAuthorizationUsersApi,
  getBorrowDeptAuthorizationDeptsApi,
  getBorrowDeptAuthorizationUsersApi,
  authorizeByMrnoApi,
  authorizeByDeptApi
} from "../interface"
import type { CheckboxValueType } from "element-plus"

/*============= 基础部分 =============*/
const globalOptionsStore = useGlobalOptionsStore()
const visible = ref(false) // 弹窗可见
const curTab = ref<"user" | "department">("user") // tab 用户授权/科室授权

interface Props {
  bagWsid?: string
  deptWsid?: string
  authorizeType: "mrno" | "department" // 授权类型(外层列表tab) 病案号授权/科室授权
  tableRef: any // 页面的tableRef，用于刷新页面数据
}

const props = defineProps<Props>()

// 弹窗标题
const dialogTitle = computed(() => {
  return props.authorizeType === "department" ? "科室授权" : "病案号授权"
})

// 切换tab，刷新已授权的用户/科室列表
const handleTabChange = tab => {
  if (tab === curTab.value) return
  curTab.value = tab
  if (tab === "user") {
    getAuthorizedUserList()
  } else {
    getAuthorizedDeptList()
  }
}

// 确认授权
const handleConfirmAuthorize = () => {
  if (curTab.value === "user" && !userState.checkedUserList.length) {
    Message.error("请选择要授权的用户")
    return
  }
  if (curTab.value === "department" && !deptState.checkedDeptList.length) {
    Message.error("请选择要授权的科室")
    return
  }
  // 病案号授权
  if (props.authorizeType === "mrno") {
    confirmAuthorizeByMrno()
    // 科室授权
  } else {
    confirmAuthorizeByDept()
  }
}

// 确认病案号授权
const confirmAuthorizeByMrno = () => {
  const requestParams = {
    documeBagWsid: props.bagWsid,
    type: curTab.value === "user" ? "AUTHORIZATION_TO_USER" : "AUTHORIZATION_TO_DEPT",
    wsids:
      curTab.value === "user"
        ? [...userState.checkedUserList, ...userState.authorizedUserList.map(user => user.userWsid)]
        : [...deptState.checkedDeptList, ...deptState.authorizedDeptList.map(dept => dept.wsid)]
  }
  authorizeByMrnoApi(requestParams)
    .then(() => {
      Message.success("授权成功")
      props.tableRef.refreshTableData()
      clear()
    })
    .catch(error => {
      toastError(error)
    })
}

// 确认科室授权
const confirmAuthorizeByDept = () => {
  const requestParams = {
    deptWsid: props.deptWsid,
    type: curTab.value === "user" ? "AUTHORIZATION_TO_USER" : "AUTHORIZATION_TO_DEPT",
    wsids: curTab.value === "user" ? [...userState.checkedUserList] : [...deptState.checkedDeptList],
    secrecyGrade: deptState.secrecyLevel
  }
  authorizeByDeptApi(requestParams)
    .then(() => {
      props.tableRef.refreshTableData()
      Message.success("授权成功")
      clear()
    })
    .catch(error => {
      toastError(error)
    })
}

// 清除数据
const clear = () => {
  clearAuthorizeUserState()
  clearAuthorizeDeptState()
  close()
}

const open = () => {
  nextTick(() => {
    console.log(props.bagWsid, props.deptWsid)
    getAuthorizedDeptList()
    getAuthorizedUserList()
  })
  visible.value = true
}

const close = () => {
  visible.value = false
}

defineExpose({ open, close })

/*============= 授权用户相关 =============*/

interface AuthorizeUserState {
  keyword: string // 输入的搜索用户关键字
  loading: boolean
  indeterminate: boolean // checkbox原生属性
  checkAll: boolean
  deptUserList: any[] // 科室用户列表
  checkedUserList: any[]
  authorizedUserList: any[] // 已授权的用户列表
  selectedDeptWsid: string // 当前选择的科室列表
}

const userState = reactive<AuthorizeUserState>({
  keyword: "",
  loading: false,
  indeterminate: false,
  checkAll: false,
  checkedUserList: [],
  deptUserList: [],
  authorizedUserList: [],
  selectedDeptWsid: ""
})

// 选择科室
const handleSelectDept = dept => {
  userState.selectedDeptWsid = dept.value
}
const initUserData = ref([])

// 获取科室下所有用户列表
const getDeptUserList = () => {
  userState.loading = true
  // getUsersInDeptsApi({ deptWsid: userState.selectedDeptWsid })
  getFuzzyUsersInDeptsApi({ deptWsid: userState.selectedDeptWsid, item: userState.keyword })
    .then(res => {
      initUserData.value = res.data
      userState.deptUserList = cloneDeep(initUserData.value.slice(0, start.value))
      userState.loading = false
    })
    .catch(error => {
      toastError(error)
      userState.loading = false
    })
}

const start = ref(10)
const handleUserListScroll = debounce(e => {
  userState.deptUserList = userState.deptUserList.concat(initUserData.value.slice(start.value, start.value + 10))
  start.value += 10
}, 500)

// 全选用户
const handleCheckAllUser = (isCheckAll: CheckboxValueType) => {
  userState.checkedUserList = isCheckAll ? userState.deptUserList.map(user => user.userWsid) : []
  userState.indeterminate = false
}

// 单选用户
const handleCheckUser = (checkedList: CheckboxValueType[]) => {
  console.log(checkedList)
  userState.checkAll = checkedList.length === userState.deptUserList.length
  userState.indeterminate = checkedList.length > 0 && checkedList.length < userState.deptUserList.length
}

// 获取已授权的用户列表 - 根据来源类型判断
const getAuthorizedUserList = () => {
  let getAuthorizedUserListApi = getBorrowMrnoAuthorizationUsersApi
  let requestParams: Record<string, any> = { wsid: props.bagWsid }
  if (props.authorizeType === "department") {
    getAuthorizedUserListApi = getBorrowDeptAuthorizationUsersApi
    requestParams = { deptWsid: props.deptWsid }
  }
  getAuthorizedUserListApi(requestParams).then(res => {
    console.log(res)
    userState.authorizedUserList = res
    userState.checkedUserList = res.map(user => user.userWsid)
    handleCheckUser(userState.checkedUserList)
  })
}

// 删除已授权用户
const handleDeleteAuthorizedUser = (userWsid: string) => {
  userState.authorizedUserList = userState.authorizedUserList.filter(user => user.wsid !== userWsid)
}

// 查询科室下的用户列表
const searchDeptUserList = () => {
  userState.loading = true
  getFuzzyUsersInDeptsApi({ deptWsid: userState.selectedDeptWsid, item: userState.keyword })
    .then(res => {
      userState.deptUserList = res.data
      userState.loading = false
    })
    .catch(error => {
      toastError(error)
      userState.loading = false
    })
}

// 清除数据
const clearAuthorizeUserState = () => {
  userState.indeterminate = false
  userState.keyword = ""
  userState.selectedDeptWsid = ""
  userState.checkedUserList = []
  userState.authorizedUserList = []
}

// 监听用户输入查询关键字查找用户
watch(
  () => userState.keyword,
  () => {
    // bug 需要先定义debounce，否则不触发
    const searchDebounce = debounce(searchDeptUserList, 300)
    searchDebounce()
  }
)

// 监听选择科室，刷新获取科室用户列表
watch(
  () => userState.selectedDeptWsid,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      if (userState.keyword) {
        // 有查询关键字，则保留关键字查询
        searchDeptUserList()
      } else {
        // 默认获取全部
        getDeptUserList()
      }
    }
  },
  { immediate: true }
)

/*============= 授权科室相关 =============*/

interface AuthorizeDeptState {
  keyword: string // 输入的搜索科室关键字
  loading: boolean
  indeterminate: boolean // checkbox原生属性
  checkAll: boolean
  checkedDeptList: any[]
  deptList: any[] // 科室用户列表
  authorizedDeptList: any[] // 已授权的用户列表
  secrecyLevel: string // 保密等级
}

const deptState = reactive<AuthorizeDeptState>({
  keyword: "",
  loading: false,
  indeterminate: false,
  checkAll: false,
  checkedDeptList: [],
  deptList: [],
  authorizedDeptList: [],
  secrecyLevel: ""
})

// 获取授权的科室列表
const getAuthorizedDeptList = () => {
  let getAuthorizedDeptListApi = getBorrowMrnoAuthorizationDeptsApi
  let requestParams: Record<string, any> = { wsid: props.bagWsid }
  if (props.authorizeType === "department") {
    getAuthorizedDeptListApi = getBorrowDeptAuthorizationDeptsApi
    requestParams = { deptWsid: props.deptWsid }
  }
  getAuthorizedDeptListApi(requestParams).then(res => {
    console.log(res)
    deptState.authorizedDeptList = res
    deptState.checkedDeptList = res.map(dept => dept.wsid)
    handleCheckDept(deptState.checkedDeptList)
  })
}

// 全选科室
const handleCheckAllDept = (isCheckAll: CheckboxValueType) => {
  deptState.checkedDeptList = isCheckAll ? deptState.deptList.map(dept => dept.wsid) : []
  deptState.indeterminate = false
}

// 单选科室
const handleCheckDept = (checkedList: CheckboxValueType[]) => {
  deptState.checkAll = checkedList.length === deptState.checkedDeptList.length
  deptState.indeterminate = checkedList.length > 0 && checkedList.length < deptState.deptList.length
}

// 查询科室
const searchDeptList = () => {
  getHospitalDeptsApi(deptState.keyword).then(res => {
    deptState.deptList = res.data.data
  })
}

// 删除已授权科室
const handleDeleteAuthorizedDept = (deptWsid: string) => {
  deptState.authorizedDeptList = deptState.authorizedDeptList.filter(dept => dept.wsid !== deptWsid)
}

// 清除数据
const clearAuthorizeDeptState = () => {
  deptState.indeterminate = false
  deptState.keyword = ""
  deptState.checkedDeptList = []
  deptState.secrecyLevel = ""
  deptState.authorizedDeptList = []
}

// 监听用户输入查询关键字查找用户
watch(
  () => deptState.keyword,
  () => {
    // bug 需要先定义debounce，否则不触发
    const searchDebounce = debounce(searchDeptList, 300)
    searchDebounce()
  },
  { immediate: true }
)
</script>

<style lang="less" scoped>
// 公共部分
.custom-checkbox-group {
  padding-right: 10px;
}

.custom-checkbox {
  align-items: center;
  flex-direction: row-reverse;
  width: 100%;
  height: 34px;
  justify-content: space-between;
  margin-right: 0;
  padding-bottom: 6px;
}

.authorize-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  align-items: center;
  column-gap: 20px;
}

.authorize-container--dept {
  display: grid;
  grid-template-columns: 1fr 1fr;
  column-gap: 20px;
}

.authorize-title {
  margin: 10px 0;
}

.authorize-card {
  border: 1px solid rgb(220 223 230);
  border-radius: 4px;
  padding: 20px;
  height: 384px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  column-gap: 10px;

  &__header {
    margin-bottom: 10px;
  }
}

.authorize-card--small {
  border: 1px solid rgb(220 223 230);
  border-radius: 4px;
  padding: 20px;
  height: 384px;
}

.authorize-card--dept {
  border: 1px solid rgb(220 223 230);
  border-radius: 4px;
  padding: 20px;
  height: 384px;
}

// 授权用户

.authorize-dept-list {
  height: 340px;
  overflow-y: scroll;
}

.dept {
  height: 40px;
  line-height: 40px;
  border-radius: 4px;
  padding: 0 10px;
  cursor: pointer;

  &:hover {
    font-weight: bold;
    background: rgb(245 245 246);
  }

  &--selected {
    color: #2c68ff;
    background-color: rgba(44, 104, 255, 0.098);
  }
}

.dept-user-list {
  height: 340px;
  // overflow-y: scroll;
  overflow-y: auto;
}

.authorized-user-list {
  height: 100%;
  overflow-y: scroll;
  padding-right: 10px;
}

.user {
  display: flex;
  align-items: center;
  justify-content: space-between;

  &__avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
  }

  &__name {
    margin-left: 6px;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

// 授权科室

.dept-checkbox-group {
  height: 320px;
  overflow-y: scroll;
}

.dept-checkbox {
  height: 40px;
  line-height: 40px;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    font-weight: bold;
    background: rgb(245 245 246);
  }
}

.authorized-dept {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
}
</style>
