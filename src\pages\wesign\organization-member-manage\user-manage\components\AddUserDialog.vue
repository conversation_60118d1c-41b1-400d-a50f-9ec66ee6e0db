<template>
  <DialogForm
    v-model:visible="addUserDialogVisible"
    :title="actionType === 'add' ? '新增' : '编辑'"
    :width="440"
    :form-config="formConfig"
    :form-state="addUserFormState"
    :confirm-callback="confirmUserChange"
    :disabled-fields="disabledFields"
  />
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from "vue"
import { cloneDeep } from "lodash-es"
import { DialogForm } from "@/page-components"
import { useFormSetting } from "@/hooks"
import { getHospitalDeptsApi, getJobTitlesApi, getSpecialtiesApi, getInpatientAreaApi } from "@/interfaces"
import { Message, extractErrorMsg } from "@/utils/message-tool"
import { addUserDialogFormConfig } from "../config"
import { createUserInfoApi, changeUserInfoApi } from "../interface"
import type { BaseOptionItem } from "@/types"

// 需要实时改变校验规则
const formConfig = computed(() => {
  addUserDialogFormConfig.find(config => config.prop === "expirationTime")!.rules = [
    {
      required: addUserFormState.inside === "OUT",
      message: "请选择有效期",
      trigger: "change"
    }
  ]
  return cloneDeep(
    addUserDialogFormConfig.map(item => {
      if (item.prop === "jobTitleCode") {
        return { ...item, options: tempData.value }
      } else if (item.prop === "deptCode") {
        return { ...item, options: tempDeptList.value }
      } else if (item.prop === "inpatientAreaCode") {
        return { ...item, options: inpatientAreaList.value }
      } else if (item.prop === "specialtiesCode") {
        return { ...item, options: specialtiesList.value }
      } else {
        return { ...item }
      }
    })
  )
})

/* ======================== 首次加载获取表单选项 ======================== */

const props = defineProps<{
  refreshData: () => void
}>()

const initFormData = {
  realName: "",
  jobId: "",
  birthDate: 1,
  phone: "",
  email: "",
  idCard: "",
  deptCode: "" as any,
  jobTitleCode: "",
  inside: "INNER", // 用户类型 内部 | 外部
  expirationTime: "", // 外部用户过期时间
  wsid: "",
  inpatientAreaCode: "",
  specialtiesCode: ""
}

const addUserFormState = reactive({ ...initFormData })

const actionType = ref<"add" | "edit">("add")

const addUserDialogVisible = ref(false)

const { showAddForm, showEditForm } = useFormSetting(addUserFormState, initFormData, actionType, addUserDialogVisible)

const tempData = ref<Array<BaseOptionItem>>([])
const tempDeptList = ref<Array<BaseOptionItem>>([])

const specialtiesList = ref<Array<BaseOptionItem>>([])
const inpatientAreaList = ref<Array<BaseOptionItem>>([])

onMounted(async () => {
  // 获取职称
  tempData.value = (await getJobTitlesApi())?.data?.data.map(item => {
    return {
      value: item.key,
      label: item.value
    }
  })

  // 获取科室
  tempDeptList.value = (await getHospitalDeptsApi())?.data?.data.map(item => {
    return {
      value: item.deptCode,
      label: item.deptName
    }
  })

  // 专业
  specialtiesList.value = (await getSpecialtiesApi())?.data?.data.map(item => {
    return {
      value: item.key,
      label: item.value
    }
  })

  // 病区
  inpatientAreaList.value = (await getInpatientAreaApi())?.data?.data.map(item => {
    return {
      value: item.key,
      label: item.value
    }
  })
})

// 禁止选择的字段
const disabledFields = computed(() => {
  let fields: string[] = []
  if (actionType.value !== "add") {
    fields.push("jobId")
  }
  if (addUserFormState.inside === "INNER") {
    fields.push("expirationTime")
  }
  return fields
})

// 内部用户无法选择时间
watch(
  () => addUserFormState.inside,
  val => {
    if (val === "INNER") {
      addUserFormState.expirationTime = ""
    }
  }
)

/* ======================== 表单操作 ======================== */

// 确认新增&编辑
function confirmUserChange() {
  // addUserFormState.deptCode = addUserFormState.deptCode.join(",")
  // 点击确定时科室选择框保持不变
  const data = cloneDeep(addUserFormState)
  data.deptCode = data.deptCode.join(",")
  if (actionType.value === "add")
    return createUserInfoApi(data)
      .then(res => {
        if (res.data.data) {
          props.refreshData()
          addUserDialogVisible.value = false
          Message.success("添加成功")
        } else {
          Message.error("操作失败")
        }
      })
      .catch(err => Message.error(extractErrorMsg(err)))
  else
    return changeUserInfoApi(data)
      .then(res => {
        if (res.data.data) {
          props.refreshData()
          addUserDialogVisible.value = false
          Message.success("修改成功")
        } else {
          Message.error("操作失败")
        }
      })
      .catch(err => Message.error(extractErrorMsg(err)))
}

defineExpose({
  clickAddUser: showAddForm,
  clickTableEdit: showEditForm
})
</script>
