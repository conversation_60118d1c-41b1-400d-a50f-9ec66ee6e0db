<template>
  <div class="process-graph-wrapper">
    <div class="process-graph">
      <div class="start-node">
        <span>申请节点</span>
      </div>
      <div class="process-edge"></div>

      <!-- 循环生成审批节点 -->
      <div v-for="(item, index) in processGraphData" :key="index" class="approval-node">
        <div class="approval-node-content" :style="{ borderColor: item?.color }">
          <div class="approval-desc">
            <div class="approval-node-name">
              <img src="/src/assets/svg/other/seal-icon.svg" alt="" />
              <OverflowTooltip :content="item.name" />
            </div>
            <span :style="{ color: item?.color }">
              {{ item.workflowStatusText }}
            </span>
          </div>
          <div class="approval-user">
            <span>{{ getApprovalName(item.approveName) }}</span>
            <i class="ri-arrow-right-s-line"></i>
          </div>
        </div>
        <div class="process-edge"></div>
      </div>
      <!-- 结束节点 -->
      <div class="end-node">
        <span>结束</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue"
import { flow, map, join, compact } from "lodash/fp"
import { OverflowTooltip } from "@/base-components"
import { getProcessInstance, getApprovalProcessDetailApi } from "@/interfaces"
import { Message } from "@/utils"

interface Props {
  businessKey: string
}
const props = defineProps<Props>()

// 流程图数据
const processGraphData = ref<Array<Record<string, any>>>([])
// 通过子节点获取父节点得的流程结果状态
const getParentWorkflowStatus = (childActInstanceDtos?: Array<Record<string, any>>) => {
  // 如果没有子节点，返回处理中,因为当前审批的数据获取流程图的时候不会返回未进行的节点,未进行的节点是通过查流程图获取的,这个时候不会有子节点
  if (!childActInstanceDtos || childActInstanceDtos.length === 0) {
    return {
      text: "待处理",
      type: "info",
      color: "#dcdfe6"
    }
  }

  // 遍历子节点，如果每个子节点都有结束时间，说明该节点已经审批过了
  if (childActInstanceDtos.every((child: any) => child?.endTime)) {
    return {
      text: "已处理",
      type: "success",
      color: "#42B625"
    }
  }

  // 默认返回处理中
  return {
    text: "处理中",
    type: "warning",
    color: "#FF9D03"
  }
}

// 获取审批人
const getApprovalName = (approvalName: Array<string>) => {
  if (!approvalName) return ""
  return flow(compact, join("、"))(approvalName)
}

watch(
  () => props.businessKey,
  async () => {
    if (!props.businessKey) return
    await getProcessInstance({ businessKey: props.businessKey }).then(async res => {
      const graphData = res.data.data?.childActInstanceDtos

      const processDefinitionId = res.data.data?.processDefinitionId
      if (!processDefinitionId) {
        Message.error("未找到流程定义ID")
        return
      }
      await getApprovalProcessDetailApi(processDefinitionId).then(res => {
        const data = res.data.data?.tasks
        processGraphData.value = data.map((item: any) => {
          const nodeData = graphData.find((node: any) => node.activityId === item.id)
          if (nodeData) {
            let child = nodeData?.childActInstanceDtos
            if (nodeData?.childActInstanceDtos?.length > 0) {
              // 如果每一个审批人都没有审批时间，说明该节点还没有审批，那么只保留第一个审批人
              if (nodeData?.childActInstanceDtos?.every((child: any) => !child?.endTime)) {
                child = [nodeData.childActInstanceDtos[0]]
              }
              // 如果每一个审批人都有审批时间，并且只有一个人的有taskCompleteDto,说明这节点审批策略是仅一人审批即可，那么只保留有taskCompleteDto的那个人
              if (
                nodeData?.childActInstanceDtos?.every((child: any) => child?.endTime) &&
                nodeData?.childActInstanceDtos?.filter((child: any) => child?.taskCompleteDto).length === 1
              ) {
                child = nodeData.childActInstanceDtos.filter((child: any) => child?.taskCompleteDto)
              }
            }

            const workflowInfo = getParentWorkflowStatus(child)
            return {
              ...item,
              ...nodeData,
              workflowStatusText: workflowInfo.text,
              color: workflowInfo.color,
              approveName: flow(map("name"), compact)(item?.assigneeConfig?.approver),
              ...{
                ...(child && child?.length > 0 && { childActInstanceDtos: child })
              }
            }
          }
          const workflowInfo = getParentWorkflowStatus(nodeData?.childActInstanceDtos)

          return {
            ...item,
            workflowStatusText: workflowInfo.text,
            color: workflowInfo.color,
            approveName: flow(map("name"), compact)(item?.assigneeConfig?.approver)
          }
        })
        console.log("流程图", processGraphData.value)
      })
    })
  },
  {
    immediate: true
  }
)
</script>

<style lang="scss" scoped>
.process-graph-wrapper {
  display: flex;
  justify-content: center;

  .start-node {
    width: 116px;
    height: 32px;
    background: #42b625;
    box-shadow: 0px 2px 8px 0px rgba(51, 51, 51, 0.1);
    border-radius: 30px 30px 30px 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: auto;
    span {
      color: #fff;
      font-size: 14px;
      font-weight: 600;
      line-height: 16px;
    }
  }
  .end-node {
    width: 116px;
    height: 32px;
    background: #333333;
    box-shadow: 0px 2px 8px 0px rgba(51, 51, 51, 0.1);
    border-radius: 34px 34px 34px 34px;
    border: 1px solid #dcdfe6;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: auto;
    span {
      color: #fff;
      font-size: 14px;
      font-weight: 600;
      line-height: 16px;
    }
  }
  .approval-node {
    display: flex;
    flex-direction: column;
    .approval-node-content {
      padding: 12px 16px;
      width: 234px;
      box-shadow: 0px 2px 8px 0px rgba(51, 51, 51, 0.1);
      border-radius: 4px;
      border: 1px solid #dcdfe6;

      .approval-desc {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 9px;
        color: #333333;
        font-size: 14px;
        line-height: 16px;

        .approval-node-name {
          display: flex;
          align-items: center;
          gap: 8px;
        }
      }
      .approval-user {
        width: 92%;
        background: rgba(129, 135, 142, 0.1);
        border-radius: 2px;
        padding: 3px 10px;
        color: #81878e;
        margin: auto;
        text-align: center;
        font-size: 14px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
  }
  .process-edge {
    width: 1px;
    height: 34px;
    background-color: rgba(193, 197, 205, 1);
    margin: auto;
    margin-top: 4px;
    margin-bottom: 10px;
    position: relative;
    &::before {
      // 添加一个等边三角形
      content: "";
      position: absolute;
      bottom: -6px;
      left: -6px;
      border-left: 6px solid transparent;
      border-right: 6px solid transparent;
      border-top: 6px solid rgba(193, 197, 205, 1);
    }
  }
}
</style>
