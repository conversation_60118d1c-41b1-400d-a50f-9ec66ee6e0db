<!-- 已保存的模板 -->
<template>
  <div v-loading="loading" class="filter-template-root">
    <VueDraggable
      v-if="templateData.length > 0"
      v-model="templateData"
      :animation="150"
      handle=".handle"
      class="filter-template-list"
      @update="changeSort"
    >
      <div
        v-for="(item, index) in templateData"
        :key="item.wsid"
        class="filter-template-item"
        :class="props.activeTemplateID === item.wsid ? 'is-active-template' : ''"
        @click="searchTemplate(item)"
      >
        <i class="handle ri-draggable"></i>
        <div class="template-name">{{ item.name }}</div>
        <el-dropdown trigger="click" :teleported="false">
          <i class="handle ri-more-line" @click.stop></i>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="openEditTemplate(item)">编辑</el-dropdown-item>
              <el-dropdown-item @click="copyTemplate(item)">复制</el-dropdown-item>
              <el-dropdown-item @click="deleteTemplate(item)">删除</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </VueDraggable>
    <EmptyContent v-else />
    <el-button style="align-self: flex-end" @click="reset">重置</el-button>

    <!-- 编辑抽屉 -->
    <el-drawer v-model="editDrawerVisible" :teleported="false" :size="505" :destroy-on-close="true">
      <template #header>
        <div class="edit-header-container">
          <div v-if="!editName">{{ editTemplate.name }}</div>
          <el-input v-else v-model="editTemplate.name" :disabled="editLoading" placeholder="请输入模板名称" />
          <i class="ri-edit-2-line" @click="editName = !editName"></i>
        </div>
      </template>
      <div style="height: 100%">
        <SearchList
          v-model:options="editTemplate.filters"
          :filter-type="props.filterType"
          :disabled-options-prop="props.disabledOptionsProp"
        >
          <template #actions>
            <el-button :loading="editLoading" type="primary" @click="handleEdit">保存</el-button>
          </template>
        </SearchList>
      </div>
    </el-drawer>

    <DialogContainer
      v-model:visible="copyVisible"
      title="复制模板"
      :confirm-loading="copyLoading"
      :confirm-callback="handleCopy"
    >
      <el-form ref="copyFormRef" :model="{ templateName }">
        <el-form-item prop="templateName" :rules="copyRule" label="模板名称">
          <el-input v-model.trim="templateName" placeholder="请输入模板名称" />
        </el-form-item>
      </el-form>
    </DialogContainer>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue"
import { cloneDeep } from "lodash-es"
import { VueDraggable } from "vue-draggable-plus"
import { EmptyContent, DialogContainer } from "@/base-components"
import {
  deleteFilterTemplateApi,
  createFilterTemplateApi,
  getFilterTemplateApi,
  editFilterTemplateApi,
  changeFilterTemplateSortApi
} from "@/interfaces"
import { FilterType } from "@/types"
import { Message, SystemAlert, toastError } from "@/utils"
import SearchList from "./SearchList.vue"

interface PropsType {
  filterType: FilterType
  disabledOptionsProp?: Array<string>
  activeTemplateID?: string
}
const props = withDefaults(defineProps<PropsType>(), {
  activeTemplateID: "",
  disabledOptionsProp: () => {
    return []
  }
})
const emits = defineEmits(["search", "reset"])
/* ======================== 获取模板数据 ======================== */
const loading = ref(false)
const templateData = ref<Array<Record<string, any>>>([])
const templateDataCopy = ref<Array<Record<string, any>>>([])

// 刷新模板数据
function refreshData() {
  loading.value = true
  getFilterTemplateApi(props.filterType)
    .then(res => {
      const data = res.data.data || []
      data.forEach(item => {
        if (item.filters) item.filters = JSON.parse(item.filters)
      })
      templateData.value = data
      templateDataCopy.value = cloneDeep(data)
      loading.value = false
    })
    .catch(err => {
      loading.value = false
      toastError(err, "获取模板失败")
    })
}

/* ======================== 模板操作 ======================== */

function reset() {
  emits("reset")
}
/* ================= 复制模板 ================= */

const copyLoading = ref(false)
const copyVisible = ref(false)
const templateName = ref("")
const activeRow = ref<null | Record<string, any>>(null)
const copyFormRef = ref()
const copyRule = [
  { required: true, message: "请输入模板名称", trigger: "blur" },
  { max: 20, message: "模板名称不可超过20字符", trigger: "blur" }
]

// 复制模板
function copyTemplate(row) {
  activeRow.value = row
  copyVisible.value = true
  templateName.value = ""
  copyFormRef.value?.resetField()
}

function handleCopy() {
  copyFormRef.value?.validate(valid => {
    if (!valid) return
    if (!activeRow.value) return Message.error("获取模板数据失败")
    const data = {
      name: templateName.value,
      module: props.filterType,
      filters: JSON.stringify(activeRow.value.filters)
    }
    copyLoading.value = true
    createFilterTemplateApi(data)
      .then(res => {
        copyLoading.value = false
        copyVisible.value = false
        Message.success("模板复制成功")
        refreshData()
      })
      .catch(err => {
        copyLoading.value = false
        toastError(err, "模板复制失败")
      })
  })
}

/* ================= 删除模板 ================= */
// 删除模板
const deleteLoading = ref(false)
function deleteTemplate(row) {
  if (deleteLoading.value) return
  deleteLoading.value = true
  deleteFilterTemplateApi(row.wsid)
    .then(res => {
      deleteLoading.value = false

      Message.success("删除成功")
      refreshData()
      emits("search", [], "")
    })
    .catch(err => {
      deleteLoading.value = false
      toastError(err, "删除失败")
    })
}

/* ================= 编辑模板 ================= */
const editDrawerVisible = ref(false)
const editTemplate = ref<Record<string, any>>({})
const editName = ref(false)
function openEditTemplate(row) {
  editTemplate.value = row
  editDrawerVisible.value = true
}

const editLoading = ref(false)
function handleEdit() {
  if (!editTemplate.value.name) return Message.warning("模板名称不可为空")
  if (editTemplate.value.name.length > 20) return Message.warning("模板名称长度不可以超过二十个字符")
  editLoading.value = true
  const data = {
    wsid: editTemplate.value.wsid,
    name: editTemplate.value.name,
    module: props.filterType,
    filters: JSON.stringify(editTemplate.value.filters)
  }

  editFilterTemplateApi(data)
    .then(res => {
      editLoading.value = false
      Message.success("模板编辑成功")
      editName.value = false
      refreshData()
      emits("search", editTemplate.value.filters, editTemplate.value.wsid)
    })
    .catch(err => {
      editLoading.value = false
      toastError(err, "模板编辑失败")
    })
}

/* ================= 模板排序 ================= */
function changeSort() {
  const data = templateData.value.map((item, index) => {
    item.sort = index + 1
    return {
      wsid: item.wsid,
      sort: item.sort
    }
  })
  changeFilterTemplateSortApi(data)
    .then(res => {
      Message.success("修改顺序成功")
      templateDataCopy.value = templateData.value
    })
    .catch(err => {
      templateData.value = templateDataCopy.value
      toastError(err, "修改顺序失败")
    })
}

/* ================= 搜索 ================= */
// 点击模板进行搜索
function searchTemplate(template) {
  emits("search", template.filters, template.wsid)
}

defineExpose({ refreshData })
</script>

<style lang="less" scoped>
.filter-template-root {
  width: 100%;
  height: 100%;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
}
.filter-template-list {
  width: 100%;
  overflow: auto;
  flex: 1;
  min-height: 0px;
  margin-bottom: 20px;

  .filter-template-item {
    width: 100%;
    height: 36px;
    display: flex;
    align-items: center;
    background: #f6f6f6;
    border-radius: 4px 4px 4px 4px;
    cursor: pointer;
    padding: 0px 20px 0px 5px;
    box-sizing: border-box;
    &:not(:first-child) {
      margin-top: 4px;
    }

    &:hover {
      background: #ecf3ff;
      .ri-draggable,
      .ri-more-line {
        opacity: 1;
      }
    }
    .ri-draggable,
    .ri-more-line {
      color: #81878e;
      opacity: 0;
    }
    .template-name {
      flex: 1;
      min-width: 0px;
      color: rgba(10, 22, 51, 0.6);
      margin-left: 8px;
    }
  }

  .is-active-template {
    background: var(--el-color-primary);
    .template-name {
      color: #fff;
    }
    &:hover {
      background: var(--el-color-primary);
    }
    .ri-draggable,
    .ri-more-line {
      color: #fff;
    }
  }
}

.edit-header-container {
  display: flex;
  align-items: center;
  height: 32px;
  .ri-edit-2-line {
    color: #409eff;
    margin-left: 10px;
    cursor: pointer;
    margin-top: 2px;
  }
}
</style>
