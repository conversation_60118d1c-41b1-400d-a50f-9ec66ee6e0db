import axios from "@/interfaces/axios-instance"

/* ======================== 病案封存公共查询接口 ======================== */

/**
 * @method GET
 * @desc   病案文档-病案数据同步
 */
export function syncRecordApi(inpNo: string) {
  return axios({
    method: "get",
    url: `/api/collect/document-sync`,
    params: { inpNo },
    timeout: 60000
  })
}

/**
 * @method GET
 * @desc   病案查询-验证病案是否封存
 */
export function checkSealedApi(inpNo: string) {
  return axios
    .request<IResponseData<boolean>>({
      method: "get",
      url: `/api/document/bag/sealed-check`,
      params: {
        inpNo
      }
    })
    .then(res => res.data?.data ?? true)
}

/**
 * @method GET
 * @desc   病案解封密码验证
 */
export function getCheckKeyApi(obj) {
  const { inpNo = "", sealKey } = obj
  return axios
    .request<IResponseData<boolean>>({
      method: "get",
      url: `/api/document/bag/check-key`,
      params: {
        inpNo,
        sealKey
      }
    })
    .then(res => res.data?.data ?? false)
}
