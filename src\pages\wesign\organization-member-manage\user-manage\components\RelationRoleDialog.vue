<!-- 关联角色编辑弹窗 -->
<template>
  <DialogContainer
    v-model:visible="relationState.isVisible"
    title="关联角色"
    :cancel-callback="cancelRelationEdit"
    :confirm-callback="confirmRelationEdit"
  >
    <div v-loading="relationState.loading">
      <el-select v-model="relationState.selectOption" multiple placeholder="请选择关联角色" style="width: 100%">
        <el-option v-for="item in roleOptions" :key="item.wsid" :label="item.roleName" :value="item.wsid" />
      </el-select>
    </div>
  </DialogContainer>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue"
import { DialogContainer } from "@/base-components"
import { Message, toastError } from "@/utils/message-tool"
import { getRoleListApi } from "../../interface"
import { editUserInfoRoleApi, getUserInfoRoleApi } from "../interface"

/* ======================== 获取关联角色选项列表 ======================== */

type RoleOptionItem = {
  wsid: string
  roleName: string
}
const roleOptions = ref<Array<RoleOptionItem>>([])

onMounted(() => {
  getRoleListApi().then(res => {
    const { roles = [] } = res.data
    // 过滤掉超管选项(超管只有一个)
    roleOptions.value = roles.filter(role => role.wsid !== "WSID_ROLE_SUPER_ADMIN")
  })
})

/* ======================== 关联角色对话框操作 ======================== */

const relationState = reactive({
  isVisible: false,
  selectOption: [] as Array<string>,
  editWsid: "",
  loading: false
})

// 确认编辑
function confirmRelationEdit() {
  relationState.loading = true
  editUserInfoRoleApi({
    userWsid: relationState.editWsid,
    item: relationState.selectOption
  })
    .then(res => {
      if (res.data.message) {
        Message.success(res.data.message)
        relationState.isVisible = false
      } else {
        Message.error("操作失败")
      }
    })
    .catch(err => toastError(err))
    .finally(() => (relationState.loading = false))
}

// 取消编辑
function cancelRelationEdit() {
  relationState.isVisible = false
  relationState.selectOption = []
  relationState.editWsid = ""
}

// 弹出关联角色编辑对话框
function clickRelationRole(row) {
  relationState.isVisible = true
  relationState.editWsid = row.userWsid
  relationState.loading = true
  getUserInfoRoleApi({ userWsid: row.userWsid })
    .then(res => {
      const { roles = [] } = res.data
      relationState.selectOption = roles.map(item => item.wsid)
    })
    .catch(err => toastError(err))
    .finally(() => (relationState.loading = false))
}

defineExpose({ clickRelationRole })
</script>
