<template>
  <el-form-item label="患者标签">
    <el-select
      v-model="patientLabelValue"
      filterable
      clearable
      :multiple="props.multiple"
      collapse-tags
      collapse-tags-tooltip
    >
      <el-option
        v-for="option in patientLabelOptions"
        :key="option.value as string"
        :label="option.label"
        :value="option.value"
      />
    </el-select>
  </el-form-item>
</template>

<script setup lang="ts">
import { computed } from "vue"
import { useGlobalOptionsStore } from "@/stores"

interface DepartmentFormProps {
  modelValue: Array<string> | string
  multiple?: boolean
}

const props = withDefaults(defineProps<DepartmentFormProps>(), {
  multiple: false
})

const globalOptionsStore = useGlobalOptionsStore()

const patientLabelOptions = computed(() => globalOptionsStore.patientLabelOptions)

const emits = defineEmits(["update:modelValue"])

const patientLabelValue = computed({
  get: () => props.modelValue,
  set: val => emits("update:modelValue", val)
})
</script>
