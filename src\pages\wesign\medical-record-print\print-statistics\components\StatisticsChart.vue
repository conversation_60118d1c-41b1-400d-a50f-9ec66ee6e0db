<template>
  <div ref="chartDom" class="echart"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, markRaw, reactive } from "vue"
import * as echarts from "echarts"
import { EChartsOption } from "echarts"

const chartDom = ref()
const myChart = ref()
interface ChartProp {
  timeList?: Array<string | number> //x轴坐标
  options?: EChartsOption
  series?: [] //展示数据
  showLegend?: boolean //展示不同图例组件
  legendData?: [] //图例组件数据
}

const props = withDefaults(defineProps<ChartProp>(), {
  showLegend: false
})

const resizeHandler = () => {
  myChart.value.resize()
}

onMounted(() => {
  pieInit()
})

const state = reactive({
  options: ref<EChartsOption>()
})

// 图表初始化
const pieInit = () => {
  state.options = {
    title: {},
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0, 0, 0, 0.7)",
      textStyle: {
        color: "#fff"
      },
      formatter: function (params) {
        let str = `${params?.[0].axisValueLabel}<br/>`
        params.forEach(item => {
          str += `${item.marker}${item.seriesName} ${item.value}<br/>`
        })
        return str
      }
    },
    grid: {
      left: 10,
      right: 10,
      top: 30,
      bottom: 0,
      containLabel: true
    },
    legend: {
      data: props.legendData,
      show: props.showLegend,
      icon: "rect",
      right: "1%",
      top: 0
    },
    xAxis: {
      type: "category",
      boundaryGap: true,
      // boundaryGap: ["20%", "20%"],
      data: props.timeList,
      axisLabel: { interval: 0, rotate: 30 }
    },
    yAxis: [
      {
        type: "value",
        splitLine: {
          lineStyle: {
            type: "dashed"
          }
        }
      }
    ],
    series: props.series
  }
  myChart.value = markRaw(echarts.init(chartDom.value))
  myChart.value.setOption(state.options, true)
  window.addEventListener("resize", resizeHandler)
}

// 监听新的数据和x轴数据，重新绘图
watch(
  [() => props.series, () => props.timeList],
  ([newSeries, newTimeList]) => {
    state.options = {
      title: {},
      tooltip: {
        trigger: "axis"
      },
      //   grid: {
      //     left: "3%",
      //     right: "4%",
      //     bottom: "3%",
      //     containLabel: true
      //   },
      legend: {
        data: props.legendData,
        show: props.showLegend
      },
      xAxis: {
        type: "category",
        boundaryGap: true,
        data: newTimeList || props.timeList,
        axisLabel: { interval: 0, rotate: 30 }
      },
      yAxis: {
        type: "value"
      },
      series: newSeries || props.series
    }
    myChart.value.setOption(state.options)
  },
  {
    deep: true
  }
)
</script>

<style lang="less" scoped>
.echart {
  width: 100%;
  height: 300px;
}
</style>
