<template>
  <!-- 查看病案业务流程 -->
  <el-drawer v-model="visible" title="业务详情">
    <el-tabs v-model="currentTab">
      <el-tab-pane label="流程图" name="processGraph">
        <FlowGraph :data="businessInfo?.nodes" />
      </el-tab-pane>
      <el-tab-pane label="运行日志" name="runningLogs">
        <RunningLogs :data="businessInfo?.logs" />
      </el-tab-pane>
    </el-tabs>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref } from "vue"
import { getMedicalRecordProcessApi } from "@/interfaces"
import { useSystemStore } from "@/stores"
import { toastError } from "@/utils"
import FlowGraph from "./FlowGraph.vue"
import RunningLogs from "./RunningLogs.vue"

const systemStore = useSystemStore()

/* ============== tabs =============== */

type TabValue = "processGraph" | "runningLogs"

const currentTab = ref<TabValue>("processGraph")

/* ============== data =============== */

const businessInfo = ref<Record<string, any>>({})

const getBusinessInfo = async (inpNo: string) => {
  currentTab.value = "processGraph"
  businessInfo.value = (await getMedicalRecordProcessApi(inpNo)).data.data
}

/* ============== visible =============== */

const visible = ref(false)

const show = async row => {
  try {
    systemStore.showLoading("加载中")
    await getBusinessInfo(row.inpNo)
    systemStore.hideLoading()
    visible.value = true
  } catch (error: any) {
    systemStore.hideLoading()
    toastError(error)
  }
}

const close = () => {
  visible.value = false
}

defineExpose({
  show,
  close
})
</script>
