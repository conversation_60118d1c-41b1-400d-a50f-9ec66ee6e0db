import type { TableColumnItem } from "@/types"

export const menuId = "/coding/medical-insurance"

export const tabsRouterList = [
  { label: "待编码", path: "/coding/medical-insurance/pending", name: "medicalInsuranceWaitingCatalog" },
  { label: "已编码", path: "/coding/medical-insurance/finished", name: "medicalInsuranceCataloged" }
]

const catalogColumns: Array<TableColumnItem> = [
  // { type: "index", label: "序号", minWidth: 80, must: true },
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "patientSex", label: "性别", minWidth: 80 },
  { prop: "patientAge", label: "年龄", minWidth: 100, sortable: true },
  { prop: "mrNo", label: "病案号", minWidth: 150, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 120, must: true },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 120 },
  { prop: "inHospitalDatetime", label: "入院时间", minWidth: 180, sortable: true },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 120 },
  { prop: "outHospitalDatetime", label: "出院时间", minWidth: 180, sortable: true },
  // { prop: "dischargeDiagnosisName", label: "出院主诊断", minWidth: 120, must: true },
  { prop: "attendingDoctor", label: "主治医师", minWidth: 120, must: true }
  // { prop: "residentDoctor", label: "住院医师", minWidth: 120 },
  // { prop: "payMode", label: "医疗付费方式", minWidth: 120 }
]

export const waitingCatalogControlColumns: Array<TableColumnItem> = [
  ...catalogColumns,
  { prop: "operation", label: "操作", width: 120, fixed: "right", must: true }
]

export const catalogedControlColumns: Array<TableColumnItem> = [
  ...catalogColumns,
  { prop: "catalogerName", label: "编码员", minWidth: 120 },
  { prop: "catalogTime", label: "编码时间", minWidth: 180, sortable: true },
  { prop: "operation", label: "操作", width: 150, fixed: "right", must: true }
]
