<template>
  <el-dialog v-model="visible" @closed="handleClosed">
    <el-result v-if="errorMessage" icon="error" :title="`${props.title}失败`" :sub-title="errorMessage" />
    <div v-else-if="progress < 100" class="progress-content">
      <slot name="content">
        {{ content }}
      </slot>
      <el-progress :percentage="Math.floor(progress)" />
    </div>
    <el-result v-else icon="success" :title="`${props.title}成功`" :sub-title="successMessage" />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from "vue"

const props = withDefaults(
  defineProps<{
    title?: string
    content?: string
    successMessage: string
    timeLimit?: number // 定时器的最长时间 单位s
    overtimeCallback?: () => void
  }>(),
  { title: "同步", content: "" }
)

const progress = ref(0)
const errorMessage = ref("")
let progressInterval
let second = 0

// 模拟进度条
const start = () => {
  progress.value = 0
  progressInterval = setInterval(() => {
    if (progress.value >= 99) {
      progress.value = 99
    } else {
      progress.value += 1
    }
    second += 1
    if (props.timeLimit && second >= props.timeLimit) {
      props.overtimeCallback && props.overtimeCallback()
    }
  }, 1000)
}

// 下载完成
const finish = () => {
  progress.value = 100
  clearInterval(progressInterval)
}

// 下载失败
const fail = (message: string) => {
  progress.value = 0
  clearInterval(progressInterval)
  errorMessage.value = message
}

/* ================================= */

const visible = ref(false)

// 开始下载
const show = () => {
  visible.value = true
}

// 关闭对话框
const close = () => {
  visible.value = false
}

const handleClosed = () => {
  progress.value = 0
  if (errorMessage.value) {
    errorMessage.value = ""
  }
  clearInterval(progressInterval)
}

defineExpose({
  show,
  start,
  finish,
  fail,
  close,
  progress
})
</script>

<style lang="less" scoped>
.progress-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style>
