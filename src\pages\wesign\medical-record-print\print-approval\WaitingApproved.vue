<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
          :filter-prop-options="filterPropOptions"
        />

        <CommonSelectFormItem
          v-model:model-value="searchFormState.receiveType"
          label="领取方式"
          :options="receiveTypeOptions"
        />

        <DaterangeFormItem v-model:model-value="searchFormState.createdDatetime" label="申请时间" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="tableRef"
        table-id="waitingApprovedTableIdent"
        :table-columns="waitingApprovedColumns"
        :request-api="getPrintApplyList"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <template #operation="{ row }">
          <TableButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Approve)"
            @click="showApprovalDialog(row)"
          >
            审批
          </TableButton>
        </template>
      </CommonTable>
    </template>

    <ApprovalDialog ref="approvalDialogRef" @success="tableRef?.refreshTableData()" />
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import {
  SearchContainer,
  CommonTable,
  TableButton,
  PatientFilterFormItem,
  DaterangeFormItem,
  CommonSelectFormItem,
  PageContainer
} from "@/base-components"
import { TabsRouter } from "@/page-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { useUserStore } from "@/stores"
import { formatDatetime } from "@/utils"
import { getPrintApplyList } from "../interface"
import { receiveTypeOptions, filterPropOptions } from "../print-config"
import ApprovalDialog from "./components/ApprovalDialog.vue"
import { waitingApprovedColumns, tabsRouterList, menuId } from "./config"

const { hasOperationPermission } = useUserStore()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  patientFilterValue: "",
  patientFilterProp: "patientName",
  receiveType: "",
  createdDatetime: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState, "status=APPLICATION")

/* ======================== 表格相关方法 ======================== */

const tableRef = ref<InstanceType<typeof CommonTable>>()

function dataCallback(data: Array<Record<string, any>>) {
  data.forEach(item => {
    item.createdDatetime = formatDatetime(item.createdDatetime)
  })
  return data
}

/* ================ 审批弹窗 ================== */

const approvalDialogRef = ref<InstanceType<typeof ApprovalDialog>>()

const showApprovalDialog = row => {
  approvalDialogRef.value?.getApprovalInfo(row.wsid)
}
</script>
