<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
        />

        <DepartmentFormItem v-model:model-value="searchFormState.outHospitalDeptWsid" label="出院科室" />

        <DaterangeFormItem v-model:model-value="searchFormState.outHospitalDatetime" label="出院时间" />

        <el-form-item label="主治医师">
          <el-input v-model="searchFormState.attendingDoctor" />
        </el-form-item>

        <DaterangeFormItem v-model:model-value="searchFormState.catalogTime" label="编码时间" />

        <PatientLabelFormItem v-model="searchFormState.patientLabel" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="catalogedTableRef"
        table-id="catalogedTableIdent"
        :table-columns="catalogedControlColumns"
        :request-api="getCatalogLists"
        :request-params="catalogedParams"
        :data-callback="dataCallback"
      >
        <!-- 患者姓名 -->
        <template #patientName="{ row }">
          <div style="display: flex; align-items: center; flex-wrap: wrap">
            <span style="margin: 0 16px">{{ row.patientName }}</span>
            <PatientLabelTag :row="row" />
          </div>
        </template>
        <template #patientPrintCount="{ row }">
          {{ row.patientPrintCount > 0 ? "已打印" : "未打印" }}
        </template>
        <template #operation="{ row }">
          <TableButton @click="check(row)">校验</TableButton>
          <TableButton :disabled="!row.canModify" @click="catalogItem(row)">修改</TableButton>
          <TableButton @click="viewItem(row)">编目记录</TableButton>
        </template>
      </CommonTable>
    </template>

    <!-- 评分校验 -->
    <ScoreDrawer
      title="病案首页评分"
      :drawer-visible="scoreDrawerVisible"
      :close="closeScoreDrawer"
      :drawer-data="drawerData"
      :score-drawer-data="scoreDrawerData"
      :pdf-key="pdfKey"
      :inp-no="inpNo"
      :total-score="totalScore"
      :update-drawer-data="updateDrawerData"
      :drawer-able="true"
    />
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref, computed } from "vue"
import { useRouter } from "vue-router"
import {
  SearchContainer,
  PatientFilterFormItem,
  CommonTable,
  TableButton,
  DaterangeFormItem,
  DepartmentFormItem,
  PageContainer,
  PatientLabelTag,
  PatientLabelFormItem
} from "@/base-components"
import { TabsRouter, ScoreDrawer } from "@/page-components"
import { getCatalogLists, viewCatalogGrade, archiveCheck } from "../interface"
import { catalogedControlColumns, tabsRouterList } from "./config"
import { useTableSearch } from "@/hooks"
import { formatDatetime, Message, extractErrorMsg } from "@/utils"

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  patientFilterValue: "",
  patientFilterProp: "patientName",
  outHospitalDeptWsid: "",
  outHospitalDatetime: "",
  attendingDoctor: "",
  catalogTime: "",
  patientLabel: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

const catalogedParams = computed(() => {
  return { ...searchParams, queryType: "cataloged" }
})

/* ======================== 表格相关方法 ======================== */

const dataCallback = (data: Array<Record<string, any>>) => {
  return data.map(item => ({
    ...item,
    inHospitalDatetime: formatDatetime(item.inHospitalDatetime),
    outHospitalDatetime: formatDatetime(item.outHospitalDatetime),
    catalogTime: formatDatetime(item.catalogTime)
  }))
}

const catalogedTableRef = ref()

const router = useRouter()
// 查看
const viewItem = row => {
  router.push({
    path: "/catalog-preview",
    query: {
      appScenario: "HOME_PAGE_ENCODE",
      businessDataWsid: row.inpNo,
      returnUrl: "/coding/hospitalization/finished",
      actionType: "record"
    }
  })
}

// 编目
const catalogItem = row => {
  // 先检查能否修改
  archiveCheck(row.inpNo)
    .then(res => {
      if (!res.data.data) {
        router.push({
          path: "/catalog-preview",
          query: {
            appScenario: "HOME_PAGE_ENCODE",
            businessDataWsid: row.inpNo,
            returnUrl: "/coding/hospitalization/finished",
            actionType: "finished"
          }
        })
      } else {
        Message.warning("当前病案已归档，无法修改")
      }
    })
    .catch(err => {
      Message.error(extractErrorMsg(err, "校验病案状态失败，请重试"))
    })
}

const scoreDrawerVisible = ref(false)
const drawerData = ref<Array<Record<string, any>>>([])
const scoreDrawerData = ref<Array<Record<string, any>>>([])
const pdfKey = ref()
const inpNo = ref("")
const totalScore = ref()
// 校验问题
const check = row => {
  scoreDrawerVisible.value = true
  inpNo.value = row.inpNo
  viewCatalogGrade(row.inpNo).then(res => {
    drawerData.value = res.data.data.deducts
    scoreDrawerData.value = drawerData.value
    pdfKey.value = res.data.data.pdfKey
    totalScore.value = res.data.data.totalScore
  })
}

const closeScoreDrawer = () => {
  scoreDrawerVisible.value = false
}

const updateDrawerData = (val: string) => {
  scoreDrawerData.value = drawerData.value.filter(item => val.includes(item.controlLevelEnum))
}
</script>
