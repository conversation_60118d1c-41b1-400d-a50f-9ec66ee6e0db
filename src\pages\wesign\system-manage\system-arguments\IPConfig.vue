<template>
  <PageContainer>
    <TabsRouter :tabs-config="tabsRouterList" />
    <!-- IP黑名单/白名单设置 -->
    <div class="ip-config-container">
      <el-form ref="formRef" :model="formData" label-width="110px" label-suffix="：" :rules="IPRules">
        <el-form-item label="名单类型" prop="ipListType">
          <div class="form-rules" style="align-items: center; column-gap: 40px">
            <el-radio-group v-model="formData.ipListType">
              <el-radio :label="IPListTypeEnum.WHITE">白名单</el-radio>
              <el-radio :label="IPListTypeEnum.BLACK">黑名单</el-radio>
            </el-radio-group>
            <div class="hint-container">黑、白名单互斥，同一时间只支持其中一种方式生效。</div>
          </div>
        </el-form-item>
        <el-form-item label="规则" prop="ipList">
          <div class="form-rules">
            <el-input v-model="formData.ipList" class="ip-config-rules" type="textarea" />

            <div class="hint-container">
              <div>（1）以换行符（回车符）相隔，一行输入一个，不可重复；</div>
              <div>（2）IP黑名单/白名单内容为空时，表示当前未开启IP黑/白名单功能；</div>
              <div>
                （3）支持如下格式网段：127.0.0.1、127.0.0.1/8、127.0.0.1/16、127.0.0.1/24、127.0.0.1/32；其他格式暂不支持；
              </div>
              <div>（4）最多支持4000个；</div>
            </div>
          </div>
        </el-form-item>
        <div>
          <el-form-item>
            <el-button type="primary" @click="save">保存</el-button>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>
  </PageContainer>
</template>

<script setup lang="ts">
import { onMounted, ref, reactive } from "vue"
import { PageContainer } from "@/base-components"
import { TabsRouter } from "@/page-components"
import { querySystemConfig, updateSystemConfig } from "@/interfaces"
import { Message, toastError } from "@/utils"
import { tabsRouterList, IPListTypeEnum, IPRules } from "./config"

const formData = reactive({
  ipListType: "",
  ipList: ""
})

const formRef = ref()

// 获取IP黑名单/白名单设置
const getIPConfig = () => {
  querySystemConfig({
    type: "IP_LIMIT"
  })
    .then(res => {
      const result = res.data.data
      result?.forEach(item => {
        if (item.key === "ipList") {
          formData[item.key] = item.value?.split(",")?.join("\n")
        }
        if (item.key === "ipListType") {
          // 如果item.key不在IPListTypeEnum中，说明后端返回的数据有误，不做处理
          if (Object.values(IPListTypeEnum).includes(item.value)) {
            formData[item.key] = item.value
          }
        }
      })
    })
    .catch(err => {
      toastError(err, "获取IP黑/白名单设置失败")
    })
}

onMounted(() => {
  getIPConfig()
})

// 保存 ip 黑白名单设置
const save = () => {
  formRef?.value?.validate(valid => {
    if (!valid) return
    const rules = formData.ipList
      ?.split("\n")
      ?.map(item => item?.trim())
      ?.filter(item => item?.length !== 0)
      ?.join(",")
    const params = [
      {
        key: "ipList",
        value: rules
      },
      {
        key: "ipListType",
        value: formData.ipListType
      }
    ]
    updateSystemConfig(params)
      .then(() => {
        Message.success("保存成功")
      })
      .catch(err => {
        toastError(err, "保存失败")
      })
  })
}

// 重置 ip 黑白名单设置
const reset = () => {
  formData.ipList = ""
  formData.ipListType = IPListTypeEnum.WHITE
}
</script>

<style lang="less" scoped>
.ip-config-container {
  width: 100%;
  height: calc(100% - 34px);
  overflow: auto;
  padding: 40px 28px 20px;
  box-sizing: border-box;

  .form-rules {
    display: flex;
    column-gap: 10px;
    .ip-config-rules {
      width: 400px;
      min-height: 200px;
      :deep(.el-textarea__inner) {
        height: 100%;
      }
    }

    .hint-container {
      display: flex;
      flex-direction: column;
      line-height: 1.5;
      row-gap: 5px;
      flex: 1;
      min-width: 400px;
      font-size: 12px;
      color: #999;
    }
  }
}
</style>
