import axios from "@/interfaces/axios-instance"
import { toastError } from "@/utils"

/* ======================== 规则配置 ======================== */

/**
 * @method GET
 * @desc   查询质控规则列表
 */
export function getQualityControlRuleList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/qc/rule`,
    params
  })
}

/**
 * @method GET
 * @desc   查询指定质控规则列表
 */
export function getGivenQualityControlRule(obj) {
  const { wsid } = obj
  return axios({
    method: "get",
    url: `/api/qc/rule/${wsid}`
  })
}

interface IRuleData {
  name?: string
  code?: string
  type?: "FIRST_HOME_PAGE"
  qcPoint?: "HOME_PAGE_CATALOG_COMPLETE" | "HQMS_REPORT"
  ruleExpression?: string
  promptContent?: string
  consult?: string
  checkType?: "INTEGRITY" | "TATIONALITY" | "TIMELINESS" | "CONSISTENCE" | "LOGICALITY" | "NORMATIVENESS"
  controlLevel?: "REMIND" | "WARN" | "ERROR"
  qcScoreStandardWsid?: string
  score?: string
  qcTarget?: string
}

/**
 * @method POST
 * @desc   新增质控规则
 */
export function addQualityControlRule(data: IRuleData) {
  return axios({
    method: "post",
    url: `/api/qc/rule`,
    data
  })
}

interface IEditRuleData extends IRuleData {
  wsid: string
}

/**
 * @method PUT
 * @desc   编辑质控规则
 */
export function editQualityControlRule(data: IEditRuleData) {
  return axios({
    method: "put",
    url: `/api/qc/rule`,
    data
  })
}

/**
 * @method POST
 * @desc   校验质控表达式
 */
interface ICheckQcExpressionData {
  condition: string
  data: string
}

export function checkQualityControlExpression(data: ICheckQcExpressionData) {
  return axios({
    method: "post",
    url: "/api/qc/rule/check-expression",
    data
  })
    .then(res => res.data?.data)
    .catch(err => toastError(err))
}

interface IModifyRuleStatusData {
  wsid: string
  status: "DISABLE" | "ENABLE" | "DEL"
}

/**
 * @method PUT
 * @desc   修改指定质控规则状态
 */
export function modifyRuleStatus(data: IModifyRuleStatusData) {
  const { wsid, status } = data
  return axios({
    method: "put",
    url: `/api/qc/rule/${wsid}`,
    params: {
      status: status
    }
  })
}

/**
 * @method GET
 * @desc   查询指定的评分标准配置
 */
export function queryGivenScoreStandard(obj) {
  const { wsid } = obj
  return axios({
    method: "get",
    url: `/api/qc/score-standard/${wsid}`
  })
}

interface IUpdateGivenScoreStandardData {
  wsid: string
  status: "DISABLE" | "ENABLE"
}

/**
 * @method PUT
 * @desc   更新指定的评分标准配置状态
 */
export function updateGivenScoreStandard(data: IUpdateGivenScoreStandardData) {
  return axios({
    method: "put",
    url: `/api/qc/score-standard`,
    data
  })
}

interface IBatchUpdateGivenScoreStandardData {
  wsid: string
  name: string
  type: "PROBLEM_CATEGORIES" | "PROBLEM_SUBCLASS" | "SCORE_ITEM"
  documentType: string
  level: number
  score: number
  totalScore: number
  singleReject: "YES" | "NO"
  rejectLevel?: "LEVE_ONE" | "LEVE_TWO" | "LEVE_THREE"
  status: "DISABLE" | "ENABLE"
  children: Array<string>
  parentWsid: string
  scoreType: "FIRST_HOME_SCORE" | "DEPT_SCORE"
}

/**
 * @method POST
 * @desc   批量更新评分标准配置
 */
export function batchUpdateGivenScoreStandard(data: Array<IBatchUpdateGivenScoreStandardData>) {
  return axios({
    method: "post",
    url: `/api/qc/score-standard`,
    data
  })
}

interface IDeleteGivenScoreStandardData {
  wsid: string
  reconfirm: boolean
}

/**
 * @method DELETE
 * @desc   删除指定评分标准配置
 */
export function deleteGivenScoreStandard(data: IDeleteGivenScoreStandardData) {
  return axios({
    method: "delete",
    url: `/api/qc/score-standard`,
    data
  })
}

/* ======================== 函数配置 ======================== */

/**
 * @method GET
 * @desc   查询文档元素列表
 */
export function getElementMetadataList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/qc/elementMetadata/list`,
    params
  })
}

/**
 * @method GET
 * @desc   模糊查询首页标准字段
 */
export function getStandardsFieldFuzzyApi(params) {
  return axios({
    method: "get",
    url: `/api/catalog/standard/first-scenario/fuzzy-query`,
    params
  })
}

/**
 * @method GET
 * @desc   查询指定文档元素
 */
export function getGivenElementMetadata(obj) {
  const { wsid } = obj
  return axios({
    method: "get",
    url: `/api/qc/elementMetadata/${wsid}`
  })
}

interface IElementMetaData {
  elementName: string
  elementType: "HOME_PAGE" | "FILE"
  standardKey?: string
  describe?: string
  qcPoint: "HOME_PAGE_CATALOG_COMPLETE"
}

interface IAddElementMetaData extends IElementMetaData {
  elementCode: string
}

/**
 * @method POST
 * @desc   新增环境变量配置项
 */
export function addElementMetadata(data: IAddElementMetaData) {
  return axios({
    method: "post",
    url: `/api/qc/elementMetadata`,
    data
  })
}

interface IEditElementMetaData extends IElementMetaData {
  wsid: string
}

/**
 * @method PUT
 * @desc   修改环境变量配置项
 */
export function editElementMetadata(data: IEditElementMetaData) {
  return axios({
    method: "put",
    url: `/api/qc/elementMetadata`,
    data
  })
}

/**
 * @method DELETE
 * @desc   删除环境变量配置项
 */
export function deleteElementMetadata(wsid: string) {
  return axios({
    method: "delete",
    url: `/api/qc/elementMetadata/${wsid}`
  })
}

/**
 * @method GET
 * @desc   函数质控-分页列表
 */
export function getQcFunctionList(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/qc/rule-functions`,
    params
  })
}

interface IQcFunctionData {
  code: string
  funName: string
  funSource: string
  funMethodName?: string
}

/**
 * @method GET
 * @desc   函数质控-分页列表
 */
export function getQcFunctionDetail(code: string) {
  return axios({
    method: "get",
    url: `/api/qc/rule-functions/${code}`
  })
}

/**
 * @method GET
 * @desc   函数质控-模糊查询
 */
export function getFuzzyQcFunction(params: Record<string, any>) {
  return axios({
    method: "get",
    url: `/api/qc/rule-functions/fuzzy-query`,
    params
  })
}

/**
 * @method POST
 * @desc   函数质控-新增
 */
export function addQcFunction(data: IQcFunctionData) {
  return axios({
    method: "post",
    url: `/api/qc/rule-functions`,
    data
  })
}

/**
 * @method PUT
 * @desc   函数质控-编辑
 */
export function editQcFunction(data: IQcFunctionData) {
  return axios({
    method: "put",
    url: `/api/qc/rule-functions/${data.code}`,
    data
  })
}

/**
 * @method DELETE
 * @desc   函数质控-删除
 */
export function deleteQcFunction(code: string) {
  return axios({
    method: "delete",
    url: `/api/qc/rule-functions/${code}`
  })
}

interface IModifyQcFunctionStatusData {
  code: string
  status: string
}

/**
 * @method PUT
 * @desc   函数质控-修改状态
 */
export function modifyQcFunctionStatus(data: IModifyQcFunctionStatusData) {
  return axios({
    method: "put",
    url: `/api/qc/rule-functions/${data.code}/change-status?status=${data.status}`
  })
}

interface IQcFunctionCheckData {
  params: string
  funSource: string
  funMethodName?: string
}

/**
 * @method POST
 * @desc   函数质控-校验
 */
export function checkQcFunctionStatus(data: IQcFunctionCheckData) {
  return axios({
    method: "post",
    url: `/api/qc/rule-functions/check`,
    data
  })
}

/* ======================== 质控分类配置 ======================== */

/**
 * @method GET
 * @desc   查询质控分类配置
 */
export function getClassRule(params: IPaginationRequestParams) {
  return axios
    .request<IPaginationResponseData>({
      method: "get",
      url: `/api/qc/class_rule`,
      params: {
        offset: 0,
        limit: 1000,
        filters: params.filters
      }
    })
    .then(res => res.data?.data?.rows ?? [])
}

interface ClassRuleDetailsItem {
  hospitalDepartmentWsid: string
  hospitalDeptName: string
}
interface IAddClassRuleData {
  documentMrClassWsid: string
  classRuleDetails: Array<ClassRuleDetailsItem>
  documentMrClassName: string
  mrClassCode: string
  hospitalDeptScope: "ALL" | "PART"
}

/**
 * @method POST
 * @desc   添加质控分类配置
 */
export function addClassRule(data: IAddClassRuleData) {
  return axios({
    method: "post",
    url: `/api/qc/class_rule`,
    data
  })
}

interface IEditClassRuleData extends IAddClassRuleData {
  wsid: string
}

/**
 * @method PUT
 * @desc   修改质控分类配置
 */
export function editClassRule(data: IEditClassRuleData) {
  return axios({
    method: "put",
    url: `/api/qc/class_rule`,
    data
  })
}

/**
 * @method PUT
 * @desc   根据wsid修改质控分类配置状态
 */
export function modifyClassRuleStatus(wsid: string, nextStatus: "ENABLE" | "DISABLE" | "DEL") {
  return axios({
    method: "put",
    url: `/api/qc/class_rule/${wsid}`,
    params: {
      status: nextStatus
    }
  })
}

interface IExportScoreStandardParams {
  documentType: string
  status?: "DISABLE" | "ENABLE"
  scoreType: "FIRST_HOME_SCORE" | "DEPT_SCORE" | "FINAL_SCORE"
}

/**
 * @method GET
 * @desc   导出评分配置
 */
export function exportScoreStandard(params: IExportScoreStandardParams) {
  return axios({
    method: "get",
    url: `/api/qc/score-standard/download`,
    responseType: "arraybuffer",
    params: params
  })
}

interface IDownloadTemplateParams {
  scoreType: "FIRST_HOME_SCORE" | "DEPT_SCORE" | "FINAL_SCORE"
}

/**
 * @method GET
 * @desc   导出评分标准模板
 */
export function downloadScoreStandardTemplate(params: IDownloadTemplateParams) {
  return axios({
    method: "get",
    url: `/api/qc/score-standard/import-template`,
    responseType: "arraybuffer",
    params: params
  })
}

interface IImportTemplateData extends IDownloadTemplateParams {
  file: File
}

/**
 * @method POST
 * @desc   导入评分配置
 */
export function importScoreStandardTemplate(data: IImportTemplateData) {
  const { scoreType, file } = data
  const formData = new FormData()
  formData.append("file", file)
  return axios({
    method: "post",
    url: `/api/qc/score-standard/import?scoreType=${scoreType}`,
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  })
}

/**
 * @method GET
 * @desc   完整性规则-页面级联数据
 */
export function getCascadeConditionApi() {
  return axios({
    method: "get",
    url: `/api/qc/rule-completenesses/cascade-condition`
  })
}

/**
 * @method GET
 * @desc   完整性规则-获取关联科室列表
 */
export function getDepartmentListApi() {
  return axios({
    method: "get",
    url: `/api/hospital/departments`
  })
}

/**
 * @method GET
 * @desc   规则-分页数据
 */
export function getCompleteRuleListApi(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/qc/rule-completenesses`,
    params
  })
}

interface ApplicationDeptData {
  deptCode: string
  deptName: string
  wsid: string
}

interface DocumentDtosData {
  documentClassCode: string
  documentClassName: string
  documentName: string
}

interface ConditionDtosData {
  conditionType: string
  subConditionName: string
  operator: string
  conditionValue: string
  logicalOperator: string
}

interface ICompleteRuleData {
  wsid?: string
  name: string
  description: string
  applicationDept: Array<ApplicationDeptData>
  documentDtos: Array<DocumentDtosData>
  conditionDtos: Array<ConditionDtosData>
}

/**
 * @method POST
 * @desc   新增规则
 */
export function addCompleteRuleApi(data: ICompleteRuleData) {
  return axios({
    method: "post",
    url: `/api/qc/rule-completenesses`,
    data
  })
}

/**
 * @method PUT
 * @desc   编辑规则
 */
export function editCompleteRuleApi(data: ICompleteRuleData) {
  return axios({
    method: "put",
    url: `/api/qc/rule-completenesses/${data.wsid}`,
    data
  })
}

interface ICompleteRuleStatus {
  wsid: string
  status: string
}

/**
 * @method PUT
 * @desc   更改规则状态
 */
export function editCompleteRuleStatusApi(data: ICompleteRuleStatus) {
  return axios({
    method: "put",
    url: `/api/qc/rule-completenesses/${data.wsid}/change-status?status=${data.status}`
  })
}

/**
 * @method PUT
 * @desc   删除规则
 */
export function deleteCompleteRuleApi(wsid: string) {
  return axios({
    method: "delete",
    url: `/api/qc/rule-completenesses/${wsid} `
  })
}
