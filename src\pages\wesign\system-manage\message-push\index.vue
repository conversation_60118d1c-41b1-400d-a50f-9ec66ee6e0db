<template>
  <PageContainer>
    <!-- 搜索表单 -->
    <SearchForm
      :form-config="messagePushSearchFormConfig"
      :form-state="searchFormState"
      @query-btn-click="handleQuery"
      @reset-btn-click="handleReset"
    />

    <!-- 消息推送设置表格 -->
    <CommonTable
      ref="messagePushTableRef"
      :table-columns="messagePushTableColumns"
      :request-api="getBagPushConfigApi"
      :request-params="searchParams"
    >
      <template #header>
        <AddButton @click="handleAdd">新增</AddButton>
      </template>
      <template #systemWsid="{ row }">
        <span>{{ getSystemName(row.systemWsid) }}</span>
      </template>
      <template #status="{ row }">
        <el-tag v-if="row.status === PushStatusTypeEnum.ENABLE" type="success">启用</el-tag>
        <el-tag v-else-if="row.status === PushStatusTypeEnum.DISABLE" type="danger">禁用</el-tag>
        <el-tag v-else type="warning">未知</el-tag>
      </template>
      <template #operation="{ row }">
        <TableButton @click="handleEdit(row)">编辑</TableButton>
        <TableButton
          v-if="row.status === PushStatusTypeEnum.ENABLE"
          v-loading="row.statuesLoading"
          @click="handleChange(row, PushStatusTypeEnum.DISABLE)"
        >
          禁用
        </TableButton>
        <TableButton v-else v-loading="row.statuesLoading" @click="handleChange(row, PushStatusTypeEnum.ENABLE)">
          启用
        </TableButton>
        <TableButton @click="handleDelete(row)">删除</TableButton>
      </template>
    </CommonTable>
  </PageContainer>
  <DialogContainer
    v-model:visible="messagePushDialogVisible"
    :title="messagePushDialogTitle"
    :width="1000"
    :confirm-callback="handleConfirm"
  >
    <el-scrollbar height="600" style="max-height: 600px; padding-right: 10px">
      <el-form
        ref="ruleFormRef"
        label-suffix="："
        label-width="auto"
        :rules="messagePushFormRules"
        :model="messagePushFormState"
      >
        <el-form-item label="厂商系统" prop="systemWsid">
          <el-select v-model="messagePushFormState.systemWsid" placeholder="请选择厂商系统" clearable>
            <el-option
              v-for="item in systemOptions"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="URL" prop="url">
          <el-input
            v-model="messagePushFormState.url"
            placeholder="当订阅事件发生时，推送通知单你指定的web URL"
            clearable
          />
        </el-form-item>
        <el-form-item label="推送方式" prop="pushMethod">
          <el-select v-model="messagePushFormState.pushMethod" placeholder="请选择推送方式">
            <el-option
              v-for="item in pushConfigOptions.pushMethodEnumList"
              :key="item.enumName"
              :label="item.showName"
              :value="item.enumName"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="入参" prop="param">
          <div v-for="(item, index) in messagePushFormState.param" :key="index" class="param-item">
            <el-input v-model="item.paramKey" placeholder="接收通知业务系统参数名" />
            <el-select v-model="item.paramValue">
              <el-option
                v-for="optionItem in pushConfigOptions.eventParamEnumList"
                :key="optionItem.paramValue"
                :label="optionItem.paramKey"
                :value="optionItem.paramValue"
              />
            </el-select>
            <el-button link @click="handleDeleteParam(index)">
              <i class="ri-delete-bin-line"></i>
            </el-button>
          </div>
          <el-button type="primary" @click="handleAddParam">添加</el-button>
        </el-form-item>
        <el-form-item label="回调密钥位置" prop="keyLocation">
          <el-select v-model="messagePushFormState.keyLocation">
            <el-option
              v-for="item in pushConfigOptions.keyLocationEnumList"
              :key="item.enumName"
              :label="item.showName"
              :value="item.enumName"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="回调密钥" prop="keyName">
          <el-input v-model="messagePushFormState.keyName" placeholder="请输入回调密钥"></el-input>
        </el-form-item>
        <el-form-item label="回调密钥值" prop="keyValue">
          <el-input v-model="messagePushFormState.keyValue" placeholder="请输入回调密钥值"></el-input>
        </el-form-item>
        <el-form-item label="订阅事件" prop="eventTypes">
          <!-- <el-select v-model="messagePushFormState.eventTypes" multiple>
          <el-option
            v-for="item in pushConfigOptions.eventTypeEnumList"
            :key="item.enumName"
            :label="item.showName"
            :value="item.enumName"
          ></el-option>
        </el-select> -->
          <el-table
            ref="eventTypesTableRef"
            :data="pushConfigOptions.eventTypeEnumList"
            row-key="enumName"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="showName" min-width="120px" label="事件名" />
            <el-table-column prop="enumName" min-width="240px" label="事件ID" />
            <el-table-column prop="description" min-width="240px" label="事件描述" />
          </el-table>
        </el-form-item>
      </el-form>
    </el-scrollbar>
  </DialogContainer>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from "vue"
import { TableInstance } from "element-plus"
import { PageContainer, CommonTable, DialogContainer, AddButton, TableButton } from "@/base-components"
import { SearchForm } from "@/page-components"
import { useTableSearch } from "@/hooks"
import { Message, SystemPrompt, toastError } from "@/utils"
import { getCollectSystemList } from "../../collect-manage/collect-way/interface"
import {
  deleteBagPushConfigApi,
  getBagPushConfigApi,
  getBagPushConfigEnumApi,
  addBagPushConfigApi,
  editBagPushConfigApi,
  getBagPushConfigDetailApi,
  changeBagPushConfigStatusApi
} from "../interface"
import { messagePushTableColumns, messagePushSearchFormConfig, PushStatusTypeEnum } from "./config"

const messagePushTableRef = ref<InstanceType<typeof CommonTable>>()

/* ======================== 搜索 ======================== */

const searchFormState = reactive({
  systemWsid: "",
  status: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

const messagePushSearchFormConfigRef = ref(messagePushSearchFormConfig)
const systemOptions = ref<
  {
    label: string
    value: string
  }[]
>([])

onMounted(() => {
  getCollectSystemList().then(res => {
    if (messagePushSearchFormConfigRef.value[0]?.type === "select") {
      systemOptions.value = res.map(item => {
        return {
          value: item.wsid,
          label: item.systemName
        }
      })
      messagePushSearchFormConfigRef.value[0].options = [...systemOptions.value]
    }
  })
})

const getSystemName = (wsid: string) => {
  if (messagePushSearchFormConfigRef.value[0]?.type !== "select") return ""

  const systemName = messagePushSearchFormConfigRef.value[0].options.find(item => {
    return item.value === wsid
  })
  return systemName?.label || ""
}

/* ======================== 新增 ======================== */
const ruleFormRef = ref()
const messagePushDialogVisible = ref(false)
const messagePushDialogTitle = ref("")
const documentPushConfigWsid = ref("")
const eventTypesTableRef = ref<TableInstance>()
const messagePushFormState = reactive({
  systemWsid: "",
  url: "",
  pushMethod: "",
  param: [{ paramValue: "", paramKey: "" }] as Record<string, any>[],
  keyLocation: "" || null,
  keyName: "" || null,
  keyValue: "" || null,
  eventTypes: [] as string[]
})
const pushConfigOptions = ref<Record<string, any>>({})

const messagePushFormRules = {
  systemWsid: [{ required: true, message: "请选择厂商系统！", trigger: "blur" }],
  url: [{ required: true, message: "请输入URL！", trigger: "blur" }],
  pushMethod: [{ required: true, message: "请选择推送方式！", trigger: "change" }],
  param: [
    {
      required: true,
      validator: (rule: any, value: any, callback: any) => {
        if (messagePushFormState.param.some(item => Object.values(item).some(value => value === ""))) {
          callback(new Error("请设置参数！"))
        } else {
          callback()
        }
      },
      trigger: "blur"
    }
  ],
  eventTypes: [{ required: true, message: "请选择事件类型！", trigger: "change" }],
  keyLocation: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (messagePushFormState.keyValue !== "" && messagePushFormState.keyLocation === "") {
          callback(new Error("请输入回调密钥！"))
        } else {
          callback()
        }
      },
      trigger: "blur"
    }
  ],
  keyName: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (messagePushFormState.keyValue !== "" && messagePushFormState.keyName === "") {
          callback(new Error("请输入回调密钥！"))
        } else {
          callback()
        }
      },
      trigger: "blur"
    }
  ]
}

const handleAdd = () => {
  messagePushFormState.systemWsid = ""
  messagePushFormState.url = ""
  messagePushFormState.pushMethod = ""
  messagePushFormState.param = [{ paramValue: "", paramKey: "" }]
  messagePushFormState.keyLocation = null
  messagePushFormState.keyName = null
  messagePushFormState.keyValue = null
  messagePushFormState.eventTypes = []

  messagePushDialogVisible.value = true
  messagePushDialogTitle.value = "新增"
}

const handleEdit = (row: any) => {
  getBagPushConfigDetailApi(row.documentPushConfigWsid).then(res => {
    documentPushConfigWsid.value = row.documentPushConfigWsid
    messagePushFormState.systemWsid = res.data.data.systemWsid
    messagePushFormState.url = res.data.data.url
    messagePushFormState.pushMethod = res.data.data.pushMethod
    messagePushFormState.param = JSON.parse(res.data.data.param)
    messagePushFormState.keyLocation = res.data.data.keyLocation
    messagePushFormState.keyName = res.data.data.keyName
    messagePushFormState.keyValue = res.data.data.keyValue
    messagePushFormState.eventTypes = res.data.data.eventTypes

    messagePushDialogVisible.value = true
    messagePushDialogTitle.value = "编辑"

    nextTick(() => {
      toggleSelection(messagePushFormState.eventTypes as string[])
    })
  })
}

const handleConfirm = async () => {
  const messagePushData = {
    systemWsid: messagePushFormState.systemWsid,
    url: messagePushFormState.url,
    pushMethod: messagePushFormState.pushMethod,
    param: JSON.stringify(messagePushFormState.param.filter(item => item.paramValue && item.paramKey)),
    keyLocation: messagePushFormState.keyLocation,
    keyName: messagePushFormState.keyName,
    keyValue: messagePushFormState.keyValue,
    eventTypes: messagePushFormState.eventTypes
  }
  ruleFormRef.value?.validate(valid => {
    if (!valid) return
    const handler =
      messagePushDialogTitle.value === "新增"
        ? addBagPushConfigApi(messagePushData)
        : editBagPushConfigApi({
            documentPushConfigWsid: documentPushConfigWsid.value,
            ...messagePushData
          })
    handler
      .then(() => {
        messagePushDialogVisible.value = false
        Message.success(`${messagePushDialogTitle.value}消息推送成功`)
        messagePushTableRef.value?.refreshTableData()
      })
      .catch(err => toastError(err))
  })
}

const handleChange = (row: any, status: string) => {
  row.statuesLoading = true
  changeBagPushConfigStatusApi(row.documentPushConfigWsid, status)
    .then(res => {
      Message.success(`${status === "ENABLE" ? "启用" : "禁用"}成功`)
      messagePushTableRef.value?.refreshTableData()
    })
    .finally(() => {
      row.statuesLoading = false
    })
}

const handleDelete = row => {
  SystemPrompt("是否删除此消息推送").then(() => {
    deleteBagPushConfigApi(row.documentPushConfigWsid)
      .then(() => {
        Message.success("删除成功")
        messagePushTableRef.value?.refreshTableData()
      })
      .catch(err => toastError(err))
  })
}

onMounted(() => {
  getBagPushConfigEnumApi().then(res => {
    pushConfigOptions.value = res.data.data
    pushConfigOptions.value = {
      ...pushConfigOptions.value,
      eventParamEnumList: pushConfigOptions.value.eventParamEnumList.map(item => ({
        paramKey: item.showName,
        paramValue: item.enumName
      }))
    }
  })
})

const handleDeleteParam = (index: number) => {
  if (messagePushFormState.param.length === 1) return
  messagePushFormState.param.splice(index, 1)
}

const handleAddParam = () => {
  messagePushFormState.param.push({ paramValue: "", paramKey: "" })
}

const handleSelectionChange = (selection: any[]) => {
  messagePushFormState.eventTypes = selection.map(item => item.enumName)
}

const toggleSelection = (eventTypes?: string[]) => {
  const rows = eventTypes?.map(item => {
    return pushConfigOptions.value.eventTypeEnumList.find(row => row.enumName === item)
  })
  if (rows) {
    rows.forEach(row => {
      eventTypesTableRef.value!.toggleRowSelection(row, true)
    })
  } else {
    eventTypesTableRef.value!.clearSelection()
  }
}
</script>

<style lang="less" scoped>
.param-item {
  display: flex;
  justify-content: space-between;
  width: 100%;
  gap: 15px;
  margin-bottom: 10px;
}
</style>
