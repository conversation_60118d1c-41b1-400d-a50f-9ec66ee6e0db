<template>
  <div class="message-center-root">
    <!-- 左侧消息菜单 -->
    <div class="message-menu">
      <div
        v-for="item in menuConfig"
        :key="item.key"
        class="message-menu-item"
        :class="pageState.activeMenu === item.key ? 'active-menu-item' : ''"
        @click="changeMenu(item)"
      >
        {{ item.title }}
      </div>
    </div>

    <!-- 右侧消息列表 -->
    <div class="message-container">
      <!-- 顶部消息状态 -->
      <div class="menu-tabs-container">
        <el-tabs
          v-model="pageState.activeTabs"
          class="message-tabs"
          @tab-click="e => { pageState.activeTabs = e.props.name as string }"
        >
          <el-tab-pane label="未读消息" :name="ReadEnum.NOT_READ"></el-tab-pane>
          <el-tab-pane label="已读消息" :name="ReadEnum.READ"></el-tab-pane>
        </el-tabs>
        <el-button v-if="pageState.activeTabs === ReadEnum.NOT_READ" type="primary" class="read-btn" @click="readAll">
          全部标记为已读
        </el-button>
      </div>

      <!-- 消息列表 -->
      <div v-loading="messageData.loading" class="message-list-root">
        <div v-if="messageData.rows.length > 0" class="message-list-container">
          <div class="message-list">
            <!-- 消息 -->
            <div v-for="item in messageData.rows" :key="item.id" class="message-item">
              <div class="row-box">
                <div class="message-type">{{ item.title }}</div>
                <div class="message-content">{{ item.createDateTimeCh }}</div>
              </div>
              <div class="row-box">
                <div class="message-content">{{ item.content }}</div>
                <div class="message-action" @click="openDialog(item)">
                  {{ pageState.activeMenu !== MenuEnum.WAIT_DISPOSE ? "查看" : "去处理" }}
                </div>
              </div>
            </div>
          </div>

          <!-- 翻页器 -->
          <el-pagination
            v-model:page-size="pageState.pageSize"
            v-model:current-page="pageState.currentPage"
            background
            :page-sizes="[10, 25, 50, 100]"
            layout="sizes, prev, pager, next, jumper, total"
            :total="messageData.total"
            @update:page-size="refreshList"
          />
        </div>
        <EmptyContent v-else />
      </div>
    </div>

    <!-- 消息详情弹窗 -->
    <DialogContainer
      v-if="dialogData.clickItem"
      v-model:visible="dialogData.visible"
      :title="dialogData.clickItem.title"
      :width="450"
      :close-callback="() => readOne(dialogData.clickItem)"
    >
      <div
        v-if="dialogData.clickItem.dialogType === 'HTML'"
        class="detail-content ql-editor"
        v-html="dialogData.clickItem.contentHtml"
      ></div>
      <div v-else v-text="dialogData.clickItem.content"></div>
      <div class="detail-time">{{ dialogData.clickItem.createDateTime }}</div>
      <template #footer>
        <el-button type="primary" @click="dialogData.visible = false">知道了</el-button>
      </template>
    </DialogContainer>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch } from "vue"
import { useRouter } from "vue-router"
import { DialogContainer, EmptyContent } from "@/base-components"
import { useSystemStore } from "@/stores"
import { Message, toastError, formatDatetime } from "@/utils"
import { menuConfig, MessageItemType, ReadEnum, MenuEnum } from "./config"
import {
  getAnnouncementsApi,
  getMessageOrDisposeApi,
  readAllAnnouncementsApi,
  readAllMessageOrDisposeApi,
  readAnnouncementApi,
  readMessageOrDisposeApi
} from "./interface"

const router = useRouter()
const systemStore = useSystemStore()

/* ======================== 切换菜单 ======================== */
function changeMenu(item) {
  pageState.activeMenu = item.key
  pageState.activeTabs = ReadEnum.NOT_READ
}

/* ======================== 获取列表 ======================== */
const pageState = reactive({
  pageSize: 25,
  currentPage: 1,
  activeMenu: MenuEnum.WAIT_DISPOSE as string, //消息类型
  activeTabs: ReadEnum.NOT_READ as string //未读/已读状态
})

const messageData = reactive({
  loading: true,
  total: 0,
  rows: [] as Array<MessageItemType> //消息列表
})

// 监听翻页器刷新数据
watch(
  () => pageState,
  () => getMessageList(),
  { deep: true, immediate: true }
)

watch([() => pageState.activeMenu, () => pageState.activeTabs], () => {
  pageState.currentPage = 1
})

// 刷新列表
function refreshList() {
  // 如果当前就是第一页则强制刷新
  if (pageState.currentPage === 1) getMessageList()
  else pageState.currentPage = 1
}

// 获取消息列表
function getMessageList() {
  messageData.loading = true
  // 获取系统公告
  if (pageState.activeMenu === MenuEnum.ANNOUNCEMENT) {
    let params = {
      limit: pageState.pageSize,
      offset: (pageState.currentPage - 1) * pageState.pageSize,
      filters: `isRead=${pageState.activeTabs}`,
      sorts: "-createDateTime"
    }
    getAnnouncementsApi(params)
      .then(res => {
        const resData = res.data?.data
        const rows: Array<MessageItemType> = []
        resData.rows.forEach(item => {
          rows.push({
            ...item,
            content: item.contentText,
            dialogType: "HTML",
            createDateTime: formatDatetime(item.createDateTime)
          })
        })
        messageData.rows = rows
        messageData.total = resData.page.totalElements
        messageData.loading = false
      })
      .catch(err => toastError(err, "获取列表失败！"))
  } else {
    // 获取消息通知/待处理
    let params = {
      limit: pageState.pageSize,
      offset: (pageState.currentPage - 1) * pageState.pageSize,
      filters: `isRead=${pageState.activeTabs}`,
      typeName: pageState.activeMenu,
      sorts: "-createDateTime"
    }
    getMessageOrDisposeApi(params)
      .then(res => {
        const resData = res.data?.data
        const rows: Array<MessageItemType> = []
        resData.rows.forEach(item => {
          rows.push({
            ...item,
            dialogType: "TEXT",
            createDateTime: formatDatetime(item.createDateTime)
          })
        })
        messageData.rows = rows
        messageData.total = resData.page.totalElements
        messageData.loading = false
      })
      .catch(err => toastError(err, "获取列表失败！"))
  }
}

/* ======================== 已读 ======================== */

// 一键已读
function readAll() {
  const handler =
    pageState.activeMenu === MenuEnum.ANNOUNCEMENT
      ? readAllAnnouncementsApi()
      : readAllMessageOrDisposeApi({ typeName: pageState.activeMenu })
  handler
    .then(() => {
      systemStore.$patch({ refreshMessage: true })
      Message.success("一键已读成功")
      refreshList()
    })
    .catch(err => toastError(err, "一键已读失败！"))
}

// 单击已读
function readOne(item) {
  if (item.isRead) {
    //单击已读消息
    if (pageState.activeMenu === MenuEnum.WAIT_DISPOSE) router.push(item.redirectPath)
    else dialogData.visible = false
  } else {
    //单击未读消息
    if (pageState.activeMenu === MenuEnum.ANNOUNCEMENT) {
      // 单击未读公告
      readAnnouncementApi(item.id)
        .then(() => {
          systemStore.$patch({ refreshMessage: true })
          refreshList()
        })
        .catch(err => toastError(err, "查看失败！"))
        .finally(() => {
          dialogData.visible = false
        })
    } else {
      // 单击未读通知/待处理
      readMessageOrDisposeApi(item.id)
        .then(() => {
          systemStore.$patch({ refreshMessage: true })
          if (pageState.activeMenu === MenuEnum.WAIT_DISPOSE) router.push(item.redirectPath)
          else refreshList()
        })
        .catch(err => toastError(err, "查看失败！"))
        .finally(() => {
          dialogData.visible = false
        })
    }
  }
}

/* ======================== 消息弹窗 ======================== */

const dialogData = reactive({
  visible: false,
  clickItem: null as MessageItemType | null //当前点击的消息
})

// 打开消息弹窗
function openDialog(item) {
  if (pageState.activeMenu === MenuEnum.WAIT_DISPOSE) return readOne(item)
  dialogData.visible = true
  dialogData.clickItem = item
}
</script>

<style lang="less" scoped>
.message-center-root {
  display: flex;
  height: 100%;
  width: 100%;

  .message-menu {
    width: 150px;
    height: calc(100% - 32px);
    background: #fff;
    box-shadow: 0px 2px 8px rgba(0, 35, 114, 0.1);
    padding: 16px 8px;

    .message-menu-item {
      width: 102px;
      height: 40px;
      line-height: 40px;
      padding: 0px 16px;
      color: rgba(3, 8, 20, 0.85);
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;
    }

    .message-menu-item:hover {
      background-color: rgba(244, 246, 249, 1);
    }

    .active-menu-item {
      background-color: rgba(244, 246, 249, 1);
      font-weight: 600;
    }
  }

  .message-container {
    width: calc(100% - 150px);
    height: 100%;
    background: #fff;
    margin-left: 20px;
    border-radius: 4px;
    box-shadow: 0px 2px 8px rgba(0, 35, 114, 0.1);

    .menu-tabs-container {
      position: relative;

      :deep(.el-tabs) {
        --el-tabs-header-height: 48px;

        .el-tabs__header {
          margin: 0px;
        }

        .el-tabs__nav-scroll {
          padding: 0px 10px;
        }

        .el-tabs__item.is-active {
          color: #030814d9;
          font-weight: 600;
        }

        .el-tabs__active-bar {
          background-color: var(--el-color-primary);
        }

        .el-tabs__item:hover {
          color: #030814d9;
        }
      }

      .read-btn {
        position: absolute;
        right: 12px;
        top: 0px;
        bottom: 0px;
        margin: auto;
        font-size: 14px;
      }
    }

    .message-list-root {
      height: calc(100% - 48px);

      .message-list-container {
        height: 100%;

        .message-list {
          overflow-y: auto;
          font-size: 14px;
          height: calc(100% - 52px);

          .message-item {
            padding: 0px 16px;
            border-bottom: 1px solid rgba(0, 35, 114, 0.1);

            .row-box {
              display: flex;
              justify-content: space-between;
              margin: 10px 0px;

              .message-type {
                color: #030814d8;
              }

              .message-content {
                color: #030814a5;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              .message-action {
                color: var(--el-color-primary);
                white-space: nowrap;
                margin-left: 10px;
                cursor: pointer;
              }
            }
          }
        }
      }

      :deep(.el-pagination) {
        justify-content: flex-end;
        padding: 10px;
      }
    }
  }

  .detail-time {
    text-align: right;
  }
}
</style>
