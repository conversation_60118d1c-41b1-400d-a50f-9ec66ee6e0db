<template>
  <div class="medical-record-trace">
    <div v-loading="state.loading">
      <MedicalRecordTree
        can-collapse
        :base-info="state.baseInfo"
        :tree-info="state.treeInfo"
        @click-pdf="handlePdfClick"
      />
    </div>

    <div class="view-middle common-box-shadow">
      <PdfPreviewComponent ref="pdfPreviewRef" :src="state.pdfSrc" />
    </div>

    <!-- 示踪记录 -->
    <div v-loading="state.loading" class="view-right">
      <CardContainer title="病案示踪" sign>
        <el-timeline>
          <el-timeline-item
            v-for="(action, index) in traceActions"
            :key="index"
            :timestamp="action.timestamp"
            placement="top"
          >
            <div class="action">
              <div class="action-title" :style="{ background: action.color }">{{ action.title }}</div>
              <div class="action-user">操作人：{{ action.userName }}</div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </CardContainer>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, computed } from "vue"
import { useRoute } from "vue-router"
import { PdfPreviewComponent, CardContainer } from "@/base-components"
import { MedicalRecordTree } from "@/page-components"
import { decryptStr, formatDatetime } from "@/utils"
import { getRecordTraceInfoApi } from "../interface"

const route = useRoute()

interface TraceActivityLogItem {
  userName: string //操作人
  oper: number //操作类型
  operDescription: string //操作描述
  [prop: string]: any
}

const state = reactive({
  loading: false,
  baseInfo: {} as Record<string, any>, // 患者基础信息
  pdfSrc: "",
  treeInfo: {} as any, // 左侧tree需要的原始数据
  logs: [] as Array<TraceActivityLogItem> // 右侧timeline数据
})

const handlePdfClick = node => {
  state.pdfSrc = "/api" + node?.contentFilePath
}

onMounted(() => {
  const inpNo = route.query.inpNo
  const secretKey = (route.query.secretKey || "") as string
  const params = { inpNo: inpNo }
  if (secretKey) {
    const sealKey = decryptStr(secretKey)
    params["sealKey"] = sealKey
  }
  state.loading = true
  getRecordTraceInfoApi(params).then(res => {
    if (res.data.code !== "100100000") return
    state.baseInfo = res.data.data.baseInfo
    state.treeInfo = res.data.data.treeInfo
    state.logs = res.data.data.logs
    state.loading = false
  })
})

const traceActions = computed(() => {
  return state.logs.map(log => {
    const curOption = actionOptions.find(option => option.value === log.oper)
    return {
      ...log,
      timestamp: formatDatetime(log.createdDatetime),
      title: curOption?.label,
      color: curOption?.color
    }
  })
})

// 流程行为选项配置
const actionOptions = [
  { label: "新建-采集", value: 0, color: "#5E76CF" },
  { label: "已出院", value: 1, color: "#5E76CF" },
  { label: "回收", value: 2, color: "#5E76CF" },
  { label: "提交", value: 3, color: "#5E76CF" },
  { label: "科室医疗质控", value: 4, color: "#5E76CF" },
  { label: "科室护理质控", value: 5, color: "#5E76CF" },
  { label: "交叉医疗质控", value: 6, color: "#5E76CF" },
  { label: "交叉护理质控", value: 7, color: "#5E76CF" },
  { label: "终末质控", value: 8, color: "#5E76CF" },
  { label: "编目", value: 9, color: "#5E76CF" },
  { label: "归档", value: 10, color: "#5E76CF" },
  { label: "封存", value: 11, color: "#5E76CF" },
  { label: "解封", value: 12, color: "#5E76CF" },
  { label: "借阅", value: 13, color: "#5E76CF" },
  { label: "召回", value: 14, color: "#5E76CF" },
  { label: "打印", value: 15, color: "#5E76CF" },
  { label: "导出", value: 16, color: "#5E76CF" },

  { label: "科室医疗质控通过", value: 101, color: "#61B32A" },
  { label: "科室医疗不通过", value: 102, color: "#FA5151" },
  { label: "科室护理质控通过", value: 103, color: "#61B32A" },
  { label: "科室护理不通过", value: 104, color: "#FA5151" },

  { label: "交叉医疗质控通过", value: 105, color: "#61B32A" },
  { label: "交叉医疗不通过", value: 106, color: "#FA5151" },
  { label: "交叉护理质控通过", value: 107, color: "#61B32A" },
  { label: "交叉护理不通过", value: 108, color: "#FA5151" },

  { label: "终末质控通过", value: 109, color: "#61B32A" },
  { label: "终末质控不通过", value: 110, color: "#FA5151" },

  { label: "科室评分", value: 111, color: "#5E76CF" },
  { label: "添加质控记录", value: 112, color: "#5E76CF" },
  { label: "终末评分", value: 113, color: "#5E76CF" },

  { label: "归档审批通过", value: 114, color: "#61B32A" },
  { label: "归档审批不通过", value: 115, color: "#FA5151" },

  { label: "封存审批通过", value: 116, color: "#61B32A" },
  { label: "封存审批不通过", value: 117, color: "#FA5151" },
  { label: "解封审批通过", value: 118, color: "#61B32A" },
  { label: "解封审批不通过", value: 119, color: "#FA5151" },

  { label: "借阅审批通过", value: 120, color: "#61B32A" },
  { label: "借阅审批不通过", value: 121, color: "#FA5151" },
  { label: "借阅授权", value: 122, color: "#5E76CF" },

  { label: "召回审批通过", value: 123, color: "#61B32A" },
  { label: "召回审批不通过", value: 124, color: "#FA5151" },

  { label: "提交退回", value: 125, color: "#FA5151" },
  { label: "编目退回", value: 126, color: "#FA5151" },
  { label: "未知", value: -99, color: "#FA5151" }
]
</script>

<style lang="less" scoped>
.medical-record-trace {
  display: flex;
  justify-content: space-between;
  height: 100%;

  .view-middle {
    overflow-x: auto;
    min-width: 400px;
    flex: 1;
    margin: 0 16px 0px 0px;
    box-sizing: border-box;
  }

  .view-right {
    width: 300px;

    .action {
      border-radius: 10px;
      background: #fff;
      margin-top: 20px;

      .action-title {
        color: #fff;
        font-weight: bold;
        padding: 8px 20px;
        border-radius: 10px 10px 0 0;
      }

      .action-user {
        color: #6c7385;
        background: #f6f7f9;
        padding: 16px 20px;
        border-radius: 0 0 10px 10px;
      }
    }

    :deep(.el-card__header) {
      padding: 12px;
    }

    :deep(.el-card__body) {
      padding: 12px;
      padding-left: 20px;
    }
  }
}
</style>
