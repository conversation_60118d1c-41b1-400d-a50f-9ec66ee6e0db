<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <IcdSearch ref="icdSearchRef" v-model:filters="requestParams.filters" :icd-type="ICDTypeEnum.ICD_10" />
    </template>

    <template #table>
      <CommonTable
        ref="commonTableRef"
        :table-columns="icd10TableColumns"
        :request-api="getIcdListApi"
        :request-params="requestParams"
      >
        <template #header>
          <AddButton @click="handleAdd">新增</AddButton>
        </template>
        <template #greyCode="{ row }">
          <span v-if="row?.greyCode">是</span>
          <span v-else>否</span>
        </template>
        <template #operation="{ row }">
          <TableButton @click="handleEdit(row)">编辑</TableButton>
          <TableButton @click="handleDelete(row)">删除</TableButton>
        </template>
      </CommonTable>
    </template>

    <DialogContainer
      :visible="dialogVisible"
      :title="isEdit ? '编辑' : '新增'"
      :confirm-callback="handleConfirm"
      :cancel-callback="handleCancel"
      :close-callback="handleCancel"
      :confirm-loading="isSubmitting"
    >
      <el-form ref="ruleFormRef" :rules="icd10FormRules" :label-width="100" label-suffix="：" :model="dialogForm">
        <el-form-item label="编码" required prop="code">
          <el-input
            v-model.trim="dialogForm.code"
            :show-word-limit="true"
            maxlength="40"
            placeholder="请输入编码"
            clearable
            type="text"
          ></el-input>
        </el-form-item>
        <el-form-item label="名称" required prop="name">
          <el-input
            v-model.trim="dialogForm.name"
            :show-word-limit="true"
            maxlength="100"
            placeholder="请输入名称"
            clearable
            type="text"
          ></el-input>
        </el-form-item>
        <el-form-item label="附加编码" prop="additionalCode">
          <el-input
            v-model.trim="dialogForm.additionalCode"
            :show-word-limit="true"
            maxlength="40"
            placeholder="请输入名称"
            clearable
            type="text"
          ></el-input>
        </el-form-item>

        <el-form-item label="医保灰码" prop="greyCode">
          <el-select v-model="dialogForm.greyCode" clearable placeholder="请选择医保灰码">
            <el-option label="是" :value="true"></el-option>
            <el-option label="否" :value="false"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </DialogContainer>
  </PageContainer>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue"
import { PageContainer, AddButton, DialogContainer, TableButton, CommonTable } from "@/base-components"
import { TabsRouter } from "@/page-components"
import { getIcdListApi } from "@/interfaces"
import { Message, toastError, SystemPrompt, SystemAlert } from "@/utils/message-tool"
import IcdSearch from "./components/IcdSearch.vue"
import { tabsRouterList, ICDTypeEnum, icd10TableColumns, icd10FormRules } from "./config"
import { addIcdApi, deleteIcdApi, editIcdApi } from "./interface"
import type { FormInstance } from "element-plus"

const requestParams = reactive({ filters: "icdVersionId=1" })

const icdSearchRef = ref<InstanceType<typeof IcdSearch>>()
const commonTableRef = ref<InstanceType<typeof CommonTable>>()

const dialogVisible = ref(false)
const isEdit = ref(false)
const dialogForm = reactive({
  id: -1,
  code: "",
  additionalCode: "",
  name: "",
  greyCode: false
})

const isSubmitting = ref(false)

const ruleFormRef = ref<FormInstance>()

// 新增
function handleAdd() {
  isEdit.value = false
  for (let prop in dialogForm) {
    if (prop === "greyCode") {
      dialogForm[prop] = false
    } else {
      dialogForm[prop] = ""
    }
  }
  dialogVisible.value = true
}

// 编辑
function handleEdit(row) {
  isEdit.value = true
  for (let prop in dialogForm) {
    dialogForm[prop] = row[prop] || ""
  }
  dialogVisible.value = true
}

// 删除
function handleDelete(row) {
  console.log(row)
  SystemPrompt("确定删除该条记录吗？", "warning").then(() => {
    deleteIcdApi({ id: row.id, icdVersionId: row?.icdVersionId })
      .then(() => {
        Message.success("删除成功")
        commonTableRef.value?.refreshTableData()
      })
      .catch(err => toastError(err))
  })
}

// 确认
function handleConfirm() {
  const icdVersionId = icdSearchRef.value?.searchFormState.icdVersionId as any
  if (!icdVersionId) return SystemAlert("请先选择版本", "warning")
  ruleFormRef.value?.validate(valid => {
    if (!valid) return

    isSubmitting.value = true

    if (isEdit.value) {
      return editIcdApi({ ...dialogForm, icdVersionId }) // 暂时写死
        .then(() => {
          Message.success("修改成功")
          commonTableRef.value?.refreshTableData()
          dialogVisible.value = false
        })
        .catch(err => toastError(err))
        .finally(() => (isSubmitting.value = false))
    } else {
      return addIcdApi({ ...dialogForm, icdVersionId })
        .then(() => {
          Message.success("添加成功")
          commonTableRef.value?.refreshTableData()
          dialogVisible.value = false
        })
        .catch(err => toastError(err))
        .finally(() => (isSubmitting.value = false))
    }
  })
}

// 关闭新建/编辑弹窗
function handleCancel() {
  dialogVisible.value = false
  for (let prop in dialogForm) {
    if (prop === "greyCode") {
      dialogForm[prop] = false
    } else {
      dialogForm[prop] = ""
    }
  }
}
</script>
