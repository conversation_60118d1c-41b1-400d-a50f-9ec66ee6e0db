<template>
  <el-button :icon="Download" @click="exportFile" :disabled="props.disabled"><slot></slot></el-button>
  <div class="progress-dialog-root">
    <DialogContainer v-model:visible="loadingVisible" title="导出" :no-footer="true" :width="550">
      <div class="batch-export">
        <!-- 导出成功 -->
        <el-result
          v-if="exportStatus === 'success'"
          icon="success"
          title="导出成功"
          sub-title="导出成功，请前往文件夹查看！"
        />

        <!-- 导出失败 -->
        <el-result v-else-if="exportStatus === 'error'" icon="error" title="导出失败" :sub-title="errorContent" />

        <!-- 导出中 -->
        <el-result v-else title="导出中，请稍后..." sub-title="关闭当前窗口不会影响导出操作。">
          <template #icon><div v-loading="true" style="width: 56px; height: 56px"></div></template>
        </el-result>

        <!-- 导出进度 -->
        <el-progress :percentage="progress" :status="exportStatus === 'error' ? 'exception' : ''"></el-progress>
      </div>
    </DialogContainer>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue"
import { noop } from "lodash-es"
import { Download } from "@element-plus/icons-vue"
import { DialogContainer } from "@/base-components"
import { Message, downloadFile, extractErrorMsg } from "@/utils"

const props = defineProps({
  exportApi: { type: Function, default: noop }, //导出接口
  exportParams: {
    type: Object,
    default() {
      return {}
    }
  }, //导出接口参数
  fileType: { type: String, required: true }, //导出文件类型
  fileName: { type: String, required: true }, //导出文件名称
  disabled: { type: Boolean, default: false } // 是否禁用
})

let progressTime
const progress = ref(0) //导出进度
const exportStatus = ref<"underway" | "error" | "success">("underway") //导出状态
const errorContent = ref("导出失败，请稍后再试！") //导出错误信息

// 点击导出打开loading弹窗
const loadingVisible = ref(false)
function exportFile() {
  progress.value = 0
  exportStatus.value = "underway"
  loadingVisible.value = true
  if (!props.exportApi) return
  else {
    mockProgress()
    props
      .exportApi(props.exportParams)
      .then(res => {
        clearInterval(progressTime)
        progress.value = 100
        exportStatus.value = "success"
        downloadFile({ fileData: res.data, fileType: props.fileType, fileName: props.fileName })
        Message.success("导出成功")
      })
      .catch(err => {
        clearInterval(progressTime)
        errorContent.value = extractErrorMsg(err)
        exportStatus.value = "error"
      })
  }
}

// 模拟进度条
function mockProgress() {
  progressTime = setInterval(() => {
    if (progress.value < 90) progress.value += 10
    else if (progress.value < 99) progress.value += 1
    // 到达99还没有导出成功则卡死在99等待导出结果
    else if (progress.value === 99) {
      clearInterval(progressTime)
    }
  }, 100)
}
</script>

<style lang="less" scoped>
.batch-export {
  width: 100%;
  .loading-hint {
    text-align: center;
  }
  .close-hint {
    text-align: center;
    font-size: 12px;
    color: #bbb;
    margin: 10px 0px;
  }
  :deep(.el-result) {
    padding: 0px 30px 20px;
  }
  :deep(.el-progress) {
    width: 100%;
    &:not(.is-exception) {
      .el-progress-bar__inner {
        background-color: unset;
        background-image: linear-gradient(to right, #0c7ffc, #67c23a);
      }
    }

    .el-progress__text {
      font-size: 14px;
      line-height: 15px;
      min-width: 40px;
    }
    .el-progress-bar__outer {
      height: 12px !important;
      border: none;
      background-color: transparent;
    }
  }
}
</style>
