import type { TableColumnItem } from "@/types"

export const qualityControlTaskAssignmentColumns: Array<TableColumnItem> = [
  { type: "selection", width: 50, fixed: "left" },
  { prop: "patientName", label: "姓名", minWidth: 200, must: true, showOverflowTooltip: false },
  { prop: "mrNo", label: "病案号", minWidth: 150, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 120, must: true },
  { prop: "visitId", label: "住院次数", minWidth: 120, sortable: true },
  { prop: "patientSex", label: "性别", minWidth: 120 },
  { prop: "inHospitalDatetime", label: "入院时间", minWidth: 180, sortable: true },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 120 },
  { prop: "outHospitalDatetime", label: "出院时间", minWidth: 180, sortable: true, must: true },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 120 },
  { prop: "doctorName", label: "主治医师", minWidth: 100, must: true },
  { prop: "qcFinalCount", label: "质控次数", minWidth: 120 },
  { prop: "isAssignmentFinalQc", label: "状态", minWidth: 120 },
  { prop: "qcFinalOperator", label: "质控人", minWidth: 120 },
  { prop: "qcFinalStatus", label: "质控结果", minWidth: 120 },
  { prop: "qcFinalDatetime", label: "质控时间", minWidth: 180 },
  { prop: "patientPrintCount", label: "患者打印", minWidth: 120 },
  { prop: "outClinicalDeptOperator", label: "出科操作人", minWidth: 120 },
  { prop: "outClinicalDeptDatetime", label: "出科时间", minWidth: 180, sortable: true, must: true }
]

export const qualityControlProcessColumns: Array<TableColumnItem> = [
  { prop: "patientId", label: "患者编号", minWidth: 100, sortable: true },
  { prop: "patientName", label: "姓名", minWidth: 100 },
  { prop: "mrNo", label: "病案号", minWidth: 150, sortable: true },
  { prop: "visitId", label: "住院次数", minWidth: 120, sortable: true },
  { prop: "createDatetime", label: "分配时间", minWidth: 170, sortable: true },
  { prop: "qcOperator", label: "质控员", minWidth: 100 },
  { prop: "qcStatus", label: "质控状态", minWidth: 110 },
  { prop: "qcDatetime", label: "质控时间", minWidth: 180, sortable: true }
]

export const assignmentDialogColumns: Array<TableColumnItem> = [
  { type: "selection", width: 50, fixed: "left" },
  { prop: "realName", label: "质控员", minWidth: 100 },
  { prop: "jobId", label: "工号", minWidth: 100 },
  { prop: "deptName", label: "所属科室", minWidth: 100 }
]

export const qcFinalStatusOption = {
  0: "--",
  1: "通过",
  2: "不通过",
  9: "--"
}
