import { EditorView } from "@codemirror/view"
import type { TableColumnItem } from "@/types"
import type { FormRules } from "element-plus"
export const menuId = "/quality-control-config/rule-config"

export const integrityRuleColumns: Array<TableColumnItem> = [
  { prop: "name", label: "规则名称", minWidth: 200 },
  { prop: "documentDtos", label: "关联文书分类", minWidth: 300 },
  { prop: "description", label: "描述", minWidth: 300 },
  { prop: "applicationDept", label: "应用科室", minWidth: 100 },
  { prop: "statusText", label: "状态", minWidth: 100 },
  { prop: "operation", label: "操作", width: 200, fixed: "right" }
]

export const checkTypeOptions = [
  { value: "INTEGRITY", label: "完整性" },
  { value: "TATIONALITY", label: "合理性" },
  { value: "TIMELINESS", label: "时效性" },
  { value: "NORMATIVENESS", label: "规范性" },
  { value: "CONSISTENCE", label: "一致性" },
  { value: "LOGICALITY", label: "逻辑性" }
]

export const typeOptions = [
  {
    value: "FIRST_HOME_PAGE",
    label: "住院病案首页"
  },
  {
    value: "MEDICAL_INSURANCE",
    label: "医保编目"
  },
  {
    value: "OUT_PATIENT_FIRST_HOME_PAGE",
    label: "门急诊病案首页"
  }
]

export const statusOptions = [
  { value: "DISABLE", label: "已禁用" },
  { value: "ENABLE", label: "已启用" }
]

export const logicalOperatorOptions = [
  { value: "C_AND", label: "且" },
  { value: "C_OR", label: "或" }
]

export const getQcPointDesc = value => {
  switch (value) {
    case "HOME_PAGE_CATALOG_COMPLETE":
      return "首页编目完成"
    case "HQMS_REPORT":
      return "HQMS上报"
    default:
      return ""
  }
}

// 规则扣分校验
const ruleScoreValidator = (_rule: any, value: string, callback: (err?: Error) => void) => {
  if (value === "") {
    callback(new Error("请输入规则扣分"))
  } else {
    const reg = /^[+]?(0|([1-9]\d*))(\.\d{1})?$/
    if (reg.test(value)) {
      callback()
    } else {
      callback(new Error("请输入最多1位小数的扣分值"))
    }
  }
}
// 规则名称校验
const nameValidator = (_rule: any, value: string, callback: (err?: Error) => void) => {
  if (value === "") {
    callback(new Error("请输入规则名称"))
  } else {
    if (value.length <= 100) {
      callback()
    } else {
      callback(new Error("请输入最多100位字符规则名称"))
    }
  }
}
// 描述校验
const descriptionValidator = (_rule: any, value: string, callback: (err?: Error) => void) => {
  if (value === "") {
    callback(new Error("请输入描述"))
  } else {
    if (value.length <= 300) {
      callback()
    } else {
      callback(new Error("请输入最多300位字符描述"))
    }
  }
}

export const integrityRuleFormRules: FormRules = {
  name: [{ required: true, validator: nameValidator, trigger: "blur" }],
  qcRuleConditionList: [{ required: true, message: "请设置关联条件", trigger: "blur" }],
  applicationDept: [{ required: false, message: "请选择关联科室", trigger: "blur" }],
  documentDtos: [{ required: false, message: "请选择关联文书", trigger: "blur" }],
  description: [{ required: true, validator: descriptionValidator, trigger: "blur" }],
  param: [{ required: true, validator: ruleScoreValidator, trigger: "blur" }]
}

export const myTheme = EditorView.theme(
  {
    // 输入的字体颜色
    "&": {
      color: "#0052D9",
      backgroundColor: "#FFFFFF"
    },
    ".cm-content": {
      caretColor: "#0052D9"
    },
    // 激活背景色
    ".cm-activeLine": {
      backgroundColor: "#FFFFDD"
    },
    // 激活序列的背景色
    ".cm-activeLineGutter": {
      backgroundColor: "#FAFAFA"
    },
    //光标的颜色
    "&.cm-focused .cm-cursor": {
      borderLeftColor: "#0052D9"
    },
    // 选中的状态
    "&.cm-focused .cm-selectionBackground, ::selection": {
      backgroundColor: "#0052D9",
      color: "#dcdfe6"
    },
    // 左侧侧边栏的颜色
    ".cm-gutters": {
      backgroundColor: "#FFFFFF",
      color: "#ddd", //侧边栏文字颜色
      border: "none"
    }
  },
  { dark: true }
)
