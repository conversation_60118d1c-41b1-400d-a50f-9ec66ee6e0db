import { MenuOperationEnum } from "@/configs"
import type { RouteRecordRaw } from "vue-router"

const sealingRouter: RouteRecordRaw = {
  path: "/sealing",
  name: "MedicalRecordSealing",
  redirect: "/sealing/apply",
  meta: {
    title: "病案封存",
    icon: "ri-folder-forbid-line",
    type: "multiple",
    showBreadcrumb: true
  },
  children: [
    {
      path: "/sealing/apply",
      redirect: "/sealing/apply/seal",
      meta: { title: "封存申请", operations: [{ id: MenuOperationEnum.Sealing, name: "封存" }] },
      children: [
        {
          path: "/sealing/apply/seal",
          component: () => import("./sealing-apply/SealApply.vue"),
          meta: { title: "封存申请" }
        },
        {
          path: "/sealing/apply/unlock",
          component: () => import("./sealing-apply/UnlockApply.vue"),
          meta: { title: "解封申请" }
        }
      ]
    },
    {
      path: "/sealing/approval",
      redirect: "/sealing/approval/waiting-approve",
      meta: {
        title: "封存审批",
        operations: [
          { id: MenuOperationEnum.Approve, name: "审批" },
          { id: MenuOperationEnum.View, name: "查看" },
          { id: MenuOperationEnum.Append, name: "追加" }
        ]
      },
      children: [
        {
          path: "/sealing/approval/waiting-approve",
          component: () => import("./sealing-approval/WaitingApprove.vue"),
          meta: { title: "待审批" }
        },
        {
          path: "/sealing/approval/approved",
          component: () => import("./sealing-approval/SealingApproved.vue"),
          meta: { title: "已审批" }
        },
        {
          path: "/sealing/approval/detail",
          component: () => import("./detail/index.vue"),
          meta: { title: "审批详情" }
        }
      ]
    },
    {
      path: "/sealing/history",
      redirect: "/sealing/history/list",
      meta: {
        title: "历史封存记录",
        operations: [
          { id: MenuOperationEnum.View, name: "查看" },
          { id: MenuOperationEnum.Cipher, name: "查看密钥" }
        ]
      },
      children: [
        {
          path: "/sealing/history/list",
          component: () => import("./sealing-history/index.vue"),
          meta: { title: "封存记录列表" }
        }
        // {
        //   path: "/sealing/history/detail",
        //   component: () => import("./detail/index.vue"),
        //   meta: { title: "封存记录详情" }
        // }
      ]
    },
    {
      path: "/sealing/history/detail",
      component: () => import("./detail/index.vue"),
      meta: { title: "封存记录详情", hideMenu: true }
    }
  ]
}

export default sealingRouter
