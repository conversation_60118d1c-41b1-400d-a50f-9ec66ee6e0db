<template>
  <div :ref="preview" :style="{ opacity: isDragging ? 0 : 1 }">
    <el-checkbox
      :key="id"
      class="common-table-setting-checkbox"
      :label="label"
      :model-value="checked"
      :disabled="must"
      @change="checkVal => $emit('check', props.id, checkVal)"
    >
      {{ label }}
      <i :ref="node => drag(drop(node))" class="ri-draggable drag-icon"></i>
    </el-checkbox>
  </div>
</template>

<script setup lang="ts">
import { toRefs } from "@vueuse/core"
import { noop } from "lodash-es"
import { useDrag, useDrop } from "vue3-dnd"

interface CardItem {
  id: string
  index: number
}

const props = defineProps({
  id: { type: String, default: "" },
  index: { type: Number, required: true },
  checked: { type: Boolean, required: true },
  must: { type: Boolean, default: false },
  label: { type: String, default: "" },
  moveCard: { type: Function, default: noop }
})

defineEmits(["check"])

const [collect, drag, preview] = useDrag(() => ({
  type: "card",
  item: () => ({ id: props.id, index: props.index }),
  collect: monitor => ({
    isDragging: monitor.isDragging()
  })
}))

const [, drop] = useDrop(() => ({
  accept: "card",
  hover(item: CardItem) {
    if (item.id !== props.id) {
      props.moveCard(item.id, props.id)
    }
  }
}))
const { isDragging } = toRefs(collect)
</script>

<style lang="less" scoped>
.common-table-setting-checkbox {
  display: flex;
  .drag-icon {
    cursor: move;
    position: absolute;
    right: 5px;
  }
}
</style>
