<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchForm
        :form-config="systemSearchFormConfig"
        :form-state="searchFilterForm"
        @query-btn-click="handleQuery"
        @reset-btn-click="handleReset"
      />
    </template>

    <template #table>
      <CommonTable
        ref="commonTableRef"
        :table-columns="systemTableColumns"
        :request-api="getSystemList"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <template #header>
          <AddButton @click="showAddForm">新增</AddButton>
        </template>
        <!-- 表格操作 -->
        <template #operation="{ row }">
          <TableButton @click="showEditForm(row)">编辑</TableButton>
          <TableButton @click="switchSystemItemStatus(row)">
            {{ row.status === "ENABLE" ? "禁用" : "启用" }}
          </TableButton>
          <TableButton @click="deleteSystemItem(row)">删除</TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>

  <DialogForm
    v-model:visible="isSystemVisibleDialog"
    :title="actionType === 'edit' ? '修改厂商配置' : '添加厂商配置'"
    :form-config="actionType === 'edit' ? systemDialogFormConfig : systemDialogFormConfig.slice(1, 3)"
    :form-state="vendorConfigForm"
    :confirm-callback="confirmCallback"
    :disabled-fields="actionType === 'edit' ? ['appId'] : []"
  />
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import { AddButton, TableButton, CommonTable, PageContainer } from "@/base-components"
import { SearchForm, TabsRouter, DialogForm } from "@/page-components"
import { useTableSearch, useFormSetting, useTableOperation } from "@/hooks"
import { SystemPrompt, SystemAlert, formatDatetime } from "@/utils"
import { tabsRouterList, systemTableColumns, systemSearchFormConfig, systemDialogFormConfig } from "./config"
import { getSystemList, addSystems, modifySystems, modifySystemStatus, checkSystemUsed } from "./interface"

/* ======================== 搜索 ======================== */

const searchFilterForm = reactive({
  systemName: "",
  provider: "",
  status: ""
})
const { searchParams, handleQuery, handleReset } = useTableSearch(searchFilterForm)

/* ======================== 表格 ======================== */

const initFormValues = {
  wsid: "",
  systemName: "",
  provider: "",
  appId: ""
}
const vendorConfigForm = reactive({ ...initFormValues })

const isSystemVisibleDialog = ref(false)
const actionType = ref<"add" | "edit">("add")

const commonTableRef = ref<InstanceType<typeof CommonTable>>()

function dataCallback(data: Array<any>) {
  return data.map(item => ({
    ...item,
    createdDatetime: formatDatetime(item.createdDatetime),
    modifiedDatetime: formatDatetime(item.modifiedDatetime || item.createdDatetime)
  }))
}

const { showAddForm, showEditForm } = useFormSetting(
  vendorConfigForm,
  initFormValues,
  actionType,
  isSystemVisibleDialog
)

const { confirmAdd, confirmEdit, confirmToggle, confirmDelete } = useTableOperation(
  vendorConfigForm,
  isSystemVisibleDialog,
  {
    addApi: addSystems,
    editApi: modifySystems,
    toggleApi: modifySystemStatus,
    deleteApi: (wsid: string) => modifySystemStatus(wsid, "DEL")
  },
  commonTableRef
)

// 新增提交&编辑提交
const confirmCallback = () => {
  if (actionType.value === "edit") return confirmEdit()
  else return confirmAdd()
}

// 切换启用&禁用状态
const switchSystemItemStatus = row => {
  if (row.status === "ENABLE") {
    SystemPrompt(`您确定要禁用${row.systemName} ，禁用后关联该厂商系统的所有配置将不能使用`).then(() =>
      confirmToggle(row.wsid, "DISABLE")
    )
  } else {
    SystemPrompt(`是否确定要启用${row.systemName} `).then(() => confirmToggle(row.wsid, "ENABLE"))
  }
}

// 删除
function deleteSystemItem(item) {
  checkSystemUsed(item.wsid).then(isUsed => {
    if (isUsed) {
      SystemAlert("此系统厂商在采集配置方案中被使用，不能删除!")
    } else {
      SystemPrompt(`您确定要删除${item.systemName} ，删除后关联该厂商系统的所有配置将不能使用`, "error").then(() =>
        confirmDelete(item.wsid)
      )
    }
  })
}
</script>
