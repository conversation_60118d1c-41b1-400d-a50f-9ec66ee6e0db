<template>
  <div class="system-self-inspection">
    <el-collapse v-model="activeNames" class="self-inspection-collapse">
      <!-- 分类校验 -->
      <!-- <el-collapse-item class="sort-check" name="sortCheck">
        <template #title="{ isActive }">
          <div class="form-group-header">
            <div class="title-bar"></div>
            <div>分类校验</div>
          </div>
        </template>
        <BaseTable :border="true" :columns="sortCheckColumns" :data="tableState.sortCheckData">
          <template #index="{ index }">
            <div>{{ index + 1 }}</div>
          </template>
          <template #status="{ row }">
            <el-text :type="row.status === SortCheckStatusEnum.NORMAL ? 'success' : 'danger'">
              {{ row.status === SortCheckStatusEnum.NORMAL ? "正常" : "缺失" }}
            </el-text>
          </template>
          <template #qcDatetime="{ row }">
            <div>{{ formatDatetime(row.qcDatetime) }}</div>
          </template>
        </BaseTable>
      </el-collapse-item> -->
      <!-- 文书完整性验证 -->
      <el-collapse-item name="file">
        <template #title="{ isActive }">
          <div class="form-group-header">
            <div class="title-bar"></div>
            <div>文书完整性验证</div>
          </div>
        </template>
        <div v-for="item in documentIntegralityList" :key="item.mrClassCode" class="integrality-item">
          <div class="integrality-title" :style="{ color: item.qcResult === 'REGISTERED' ? '#f59a23' : '#f52f3e' }">
            *{{ item.mrClassName }}
            <div v-if="item.qcResult === 'REGISTERED'" class="status">
              <el-button size="small" type="success">已登记</el-button>
            </div>
          </div>
          <div class="integrality-des">{{ item.conditionHitDesc.join(",") }}</div>
        </div>
        <div v-if="!documentIntegralityList.length" class="empty-integrality">暂无数据</div>
      </el-collapse-item>
      <!-- 缺失未提交报告列表 -->
      <el-collapse-item
        name="missingReport"
        class="missing-report"
        :style="{
          height: missingReportHeight
        }"
      >
        <template #title="{ isActive }">
          <div class="form-group-header">
            <div class="title-bar"></div>
            <div>缺失未提交报告列表</div>
          </div>
        </template>
        <BaseTable :border="true" :columns="missingReportColumns" :data="tableState.missingReportData">
          <template #index="{ index }">
            <div>{{ index + 1 }}</div>
          </template>
          <template #status="{ row }">
            <el-text :type="row.status === MissingReportStatusEnum.COMPLETE_SUBMIT ? 'success' : 'danger'">
              {{ row.status === MissingReportStatusEnum.COMPLETE_SUBMIT ? "已补交" : "待补交" }}
            </el-text>
          </template>
          <template #type="{ row }">
            <div>{{ row.type === MissingReportTypeEnum.ELECTRON ? "电子" : "纸质" }}</div>
          </template>
          <template #predictDatetime="{ row }">
            <div>{{ formatDatetime(row.predictDatetime) }}</div>
          </template>
        </BaseTable>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch, onMounted } from "vue"
import { useRoute } from "vue-router"
import { BaseTable } from "@/base-components"
import { MissingReportTypeEnum, MissingReportStatusEnum } from "@/configs"
import {
  getQualityControlledSortCheckApi,
  getQualityControlledMissingReportApi,
  checkIntegralityApi
} from "@/interfaces"
import { formatDatetime, toastError } from "@/utils"
import { sortCheckColumns, missingReportColumns, SortCheckStatusEnum } from "../../config"

const route = useRoute()

const activeNames = ref(["sortCheck", "missingReport", "file"])

const props = defineProps({
  documentBagWsid: { type: String, default: "" }
})

/* =================== 分类校验表格 =====================*/
const tableState = reactive({
  sortCheckData: [],
  missingReportData: []
})

const checkIntegrality = async inpNo => {
  try {
    const documentIntegrityList = (await checkIntegralityApi(inpNo))?.documentIntegrityList
    console.log(`output->documentIntegrityList`, documentIntegrityList)
    documentIntegralityList.value = documentIntegrityList
      .sort((prev, next) => {
        return prev.mrClassName.localeCompare(next.mrClassName, "zh")
      })
      .filter(item => item.conditionHit && item.qcResult !== "COMPLETE")
  } catch (error: any) {
    console.log(`output->error`, error)
    toastError(error, "检查病案完整性失败！")
  }
}

watch(
  () => route.query.inpNo,
  val => {
    if (val) {
      getSortCheck()
      checkIntegrality(val)
    }
  },
  { immediate: true }
)

const documentIntegralityList = ref<any[]>([]) // 病案完整性

// 获取分类校验明细table数据
function getSortCheck() {
  getQualityControlledSortCheckApi({ inpNo: route.query.inpNo, urlType: route.query.urlType })
    .then(res => {
      const result = res.data.data || []
      tableState.sortCheckData = result
    })
    .catch(err => {
      toastError(err, "获取分类校验明细失败")
    })
}

watch(
  () => props.documentBagWsid,
  val => {
    if (val) getMissingReport()
  },
  { immediate: true }
)

// 获取缺失未提交报告table数据
function getMissingReport() {
  getQualityControlledMissingReportApi(props.documentBagWsid)
    .then(res => {
      const result = res.data.data || []
      tableState.missingReportData = result
    })
    .catch(err => {
      toastError(err, "获取缺失未提交报告失败")
    })
}

/* =================== 缺失报告表格 =====================*/
const missingReportHeight = computed(() => {
  if (activeNames.value.includes("sortCheck")) {
    return activeNames.value.includes("missingReport") ? "300px" : "50px"
  } else return "100%"
})
</script>

<style lang="less" scoped>
.system-self-inspection {
  width: 100%;
  height: 100%;
  overflow: auto;
  padding: 20px;
  box-sizing: border-box;
  :deep(.el-collapse) {
    .el-collapse-item__header {
      font-size: 14px;
      font-weight: 600;
      border-bottom-color: #ebeef5 !important;
    }
    .el-collapse-item__content {
      padding: 10px 0px;
    }
  }

  .self-inspection-collapse {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .sort-check.is-active {
    flex: 1;
    // min-height: 0px;
  }

  .is-active {
    display: flex;
    flex-direction: column;
    :deep(.el-collapse-item__wrap) {
      flex: 1;
      min-height: 0px;
      .el-collapse-item__content {
        height: 100%;
        box-sizing: border-box;
      }
    }
  }

  :deep(.el-scrollbar__view) {
    height: 100%;
  }
  :deep(.el-table__empty-text) {
    height: 100%;
  }
  :deep(.el-empty) {
    padding: 10px 0px;
    height: 100%;

    .el-empty__image {
      width: 80px;
    }
    .el-empty__description {
      margin-top: 0px;
    }
  }
}

.form-group-header {
  height: 30px;
  font-weight: bold;
  font-size: 14px;
  color: #0a1633;
  line-height: 30px;
  display: flex;
  align-items: center;
  padding: 12px 0;
  font-weight: bold;
  .title-bar {
    height: 16px;
    width: 4px;
    background: #3860f4;
    margin-right: 8px;
  }
}
.integrality {
  &-item {
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid #eaedf2;
    padding-bottom: 25px;
    padding-top: 16px;
    &:last-child {
      border-bottom: none;
      padding-bottom: 0;
    }
  }

  &-title {
    display: flex;
    align-items: center;
    width: 100%;
    font-weight: 500;
    font-size: 14px;
    color: #f52f3e;
    margin-bottom: 8px;

    .status {
      margin-left: 30px;
    }
  }

  &-des {
    font-weight: 500;
    font-size: 12px;
    color: #545c71;
    margin-bottom: 12px;
  }

  &-operation {
    display: flex;
    justify-content: flex-end;
  }
}
.empty-integrality {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: rgb(144, 147, 153);
}
</style>
