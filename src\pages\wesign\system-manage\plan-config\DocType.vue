<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <CommonInputFormItem v-model="searchForm.mrClassCode" label="分类代码" />

        <CommonInputFormItem v-model="searchForm.mrClassName" label="分类名称" />

        <CommonSelectFormItem
          v-model="searchForm.mrStandardDicWsid"
          label="标准文书类型"
          :options="standardsOptions"
        ></CommonSelectFormItem>
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="commonTableRef"
        :table-columns="docTypeTableColumns"
        :request-api="getDocTypeList"
        :request-params="searchParams"
      >
        <template #header>
          <AddButton @click="handleAddBtn">新增</AddButton>
          <ImportButton @click="importDialogRef?.openUserImportDialog()">导入</ImportButton>
        </template>
        <template #operation="{ row }">
          <TableButton @click="handleEditBtn(row, false)">编辑</TableButton>
          <TableButton @click="handleEditBtn(row, true)">合成模板</TableButton>
          <TableButton @click="handleSealSetting(row)">签章设置</TableButton>
          <TableButton @click="handleDeleteBtn(row)">删除</TableButton>
        </template>
      </CommonTable>
    </template>

    <!-- 新增、编辑文书 -->
    <DialogContainer
      v-model:visible="dialogVisible"
      :title="isEdit ? (isModifyingTemplate ? '合成模板' : '编辑文书') : '新增文书'"
      :width="480"
      :confirm-callback="handleConfirm"
      :confirm-loading="confirmLoading"
    >
      <el-form
        ref="dialogFormRef"
        :model="dialogFormData"
        :rules="docTypeFormRules"
        label-width="145"
        label-suffix="："
      >
        <template v-if="!isModifyingTemplate">
          <CommonInputFormItem
            v-model="dialogFormData.mrClassCode"
            label="分类代码"
            prop="mrClassCode"
            :disabled="isEdit"
          />

          <CommonInputFormItem v-model="dialogFormData.mrClassName" label="分类名称" prop="mrClassName" />

          <CommonSelectFormItem
            v-model="dialogFormData.mrClassLabelKey"
            label="文书标签"
            prop="mrClassLabelKey"
            :options="classLabelKeyOptions"
          ></CommonSelectFormItem>

          <!-- <CommonInputFormItem v-model.trim="dialogFormData.mrClassOid" label="文书模板编号OID" prop="mrClassOid" /> -->
          <CommonSelectFormItem
            v-model="dialogFormData.mrDataSourceType"
            prop="mrDataSourceType"
            label="类型"
            :options="dataSourcesTypeOptions"
          ></CommonSelectFormItem>

          <CommonSelectFormItem
            v-model="dialogFormData.mrStandardDicWsid"
            prop="mrStandardDicWsid"
            label="标准文书类型"
            :filterable="true"
            :options="standardsOptions"
          ></CommonSelectFormItem>
        </template>

        <CommonSelectFormItem
          v-else
          v-model="dialogFormData.templateWsid"
          prop="templateWsid"
          label="合成文书模板"
          :filterable="true"
          :options="templatesOptions"
          @change="handleTemplateChange"
        ></CommonSelectFormItem>
      </el-form>
    </DialogContainer>

    <!-- 签章设置 -->
    <DialogContainer
      v-model:visible="sealSettingDialogVisible"
      title="签章设置"
      :width="800"
      :confirm-callback="handleConfirmSet"
      :confirm-loading="sealSettingLoading"
    >
      <div class="seal-setting">
        <div class="left">
          <el-form ref="sealRuleForm" v-model="sealSettingDialogFormData">
            <el-form-item label="签章图片:">
              <div class="content">
                <div class="tips">支持类型PNG，尺寸200*200</div>
                <div class="paging-seal" @click="handleUpload">
                  <img v-if="sealSrc" :src="sealSrc" />
                  <div v-else class="img-container-desc">
                    <i class="ri-add-line add-btn"></i>
                    <span>上传图片</span>
                  </div>
                </div>
              </div>
            </el-form-item>

            <el-form-item label="坐标定位:">
              <el-input
                v-model="sealSettingDialogFormData.ulx"
                type="number"
                min="0"
                style="margin-bottom: 10px"
                placeholder="请输入X轴坐标"
                @input="$event => scanPosition($event, 'ulx')"
              />
              <el-input
                v-model="sealSettingDialogFormData.uly"
                type="number"
                min="0"
                placeholder="请输入Y轴坐标"
                @input="$event => scanPosition($event, 'uly')"
              />
            </el-form-item>
          </el-form>
        </div>
        <div class="right">
          <div class="img-container">
            <img class="background" src="@/assets/png/paging-seal-file.png" />
            <div v-if="sealSrc" class="sela-img" :style="sealStyle">
              <img :src="sealSrc" />
            </div>
          </div>
        </div>
      </div>
    </DialogContainer>

    <!-- 导入弹窗 -->

    <BatchImport
      ref="importDialogRef"
      :download-template-api="downloadMrclassImportTemplateApi"
      :import-file-api="importMrclassTemplateApi"
      :import-params="{}"
      :confirm-callback="refreshData"
    />
  </PageContainer>
</template>

<script lang="ts" setup>
import { ref, reactive, h, onMounted, computed } from "vue"
import { ElMessageBox, ElInput, ElForm, ElFormItem } from "element-plus"
import {
  CommonInputFormItem,
  CommonSelectFormItem,
  PageContainer,
  SearchContainer,
  CommonTable,
  DialogContainer,
  AddButton,
  TableButton,
  ImportButton
} from "@/base-components"
import { TabsRouter, BatchImport } from "@/page-components"
import { useTableSearch, useCommonOptions } from "@/hooks"
import { getApproveReasonApi, getAllTemplate, uploadSystemFile, getImage } from "@/interfaces"
import { toastError, Message, selectFile, blobToBase64 } from "@/utils"

import { docTypeFormRules, docTypeTableColumns, tabsRouterList } from "./config"
import {
  getDocTypeList,
  delDocTypeCatalogue,
  addDocTypeCatalogue,
  editDocTypeCatalogue,
  getDataSourcesTypeApi,
  getStandardsTypeApi,
  setSignPositionApi,
  downloadMrclassImportTemplateApi,
  importMrclassTemplateApi,
  mrclassQueryApi
} from "./interface"
import type { FormInstance } from "element-plus"

/* ======================== 搜索 ======================== */

const searchForm = reactive({
  mrClassCode: "",
  mrClassName: "",
  mrStandardDicWsid: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchForm)

/* ======================== 表格&编辑弹窗 ======================== */

const isEdit = ref(false)
const commonTableRef = ref<InstanceType<typeof CommonTable>>()

// 病案分类列表
const { options: standardsOptions } = useCommonOptions({
  getOptionsApi: getStandardsTypeApi,
  labelAlias: "value",
  valueAlias: "key"
})

// 文书类型列表
const { options: dataSourcesTypeOptions } = useCommonOptions({
  getOptionsApi: getDataSourcesTypeApi,
  labelAlias: "value",
  valueAlias: "key"
})

const { options: templatesOptions } = useCommonOptions({
  getOptionsApi: getAllTemplate,
  labelAlias: "templateName",
  valueAlias: "templateWsid"
})

// 文书分类列表
const classLabelKeyOptions = ref([])
onMounted(() => {
  getApproveReasonApi({ groupKey: "MR_CLASS_LABEL" }).then(res => {
    classLabelKeyOptions.value = res.map(item => {
      return {
        label: item.value,
        value: item.key
      }
    })
  })
})

// 添加
const handleAddBtn = () => {
  isModifyingTemplate.value = false
  isEdit.value = false

  dialogFormData.mrClassCode = ""
  dialogFormData.mrClassName = ""
  dialogFormData.mrStandardDicWsid = ""
  dialogFormData.mrDataSourceType = "1"
  dialogFormData.mrClassLabelKey = ""
  dialogFormData.templateName = ""

  dialogVisible.value = true
}

// 导入
const importDialogRef = ref()
const refreshData = () => {
  commonTableRef.value?.refreshTableData()
}

const dialogFormRef = ref<FormInstance>()

//弹窗配置
const dialogVisible = ref(false)

const dialogFormData = reactive({
  mrClassCode: "",
  mrClassName: "",
  mrStandardDicWsid: "",
  mrDataSourceType: "",
  mrClassLabelKey: "",
  templateName: "",
  templateWsid: ""
})

const confirmLoading = ref(false)

// 确认编辑
const handleConfirm = () => {
  dialogFormRef.value?.validate().then(isValid => {
    if (!isValid) return
    const handle = isEdit.value ? editDocTypeCatalogue : addDocTypeCatalogue
    if (confirmLoading.value) return
    confirmLoading.value = true
    handle(dialogFormData)
      .then(() => {
        dialogVisible.value = false
        Message.success("操作成功！")
        confirmLoading.value = false
        commonTableRef.value?.refreshTableData()
      })
      .catch(err => {
        confirmLoading.value = false
        toastError(err)
      })
  })
}

const isModifyingTemplate = ref(false)

const handleTemplateChange = val => {
  dialogFormData.templateName = templatesOptions.value.find(item => item.value === val)?.label || ""
}

// 修改
const handleEditBtn = (row, value) => {
  isEdit.value = true
  dialogVisible.value = true
  isModifyingTemplate.value = value

  dialogFormData.mrClassCode = row.mrClassCode
  dialogFormData.mrClassName = row.mrClassName
  dialogFormData.mrStandardDicWsid = row.mrStandardDicWsid
  dialogFormData.mrDataSourceType = row.mrDataSourceType.toString()
  dialogFormData.mrClassLabelKey = row.mrClassLabelKey

  dialogFormData.templateName = row.templateName
  dialogFormData.templateWsid = row.templateWsid
}

// 删除文书
function handleDeleteBtn(row) {
  const oidNumber = ref("")
  const oidError = ref("")
  ElMessageBox({
    title: "删除",
    showCancelButton: true,
    confirmButtonText: "确认删除",
    message: () =>
      h(ElForm, null, [
        h(ElFormItem, { error: oidError.value, required: true }, [
          h("i", { style: "margin-bottom: 4px" }, `请输入“DELETE-${row.mrClassCode}”,确认删除`),
          h(ElInput, {
            placeholder: "请输入“DELETE-文书代码”",
            modelValue: oidNumber.value,
            onInput: value => (oidNumber.value = value),
            onBlur: value => {
              if (oidNumber.value) oidError.value = ""
              else oidError.value = "文书模板编号不可为空"
            }
          })
        ])
      ]),
    beforeClose: (action, instance, done) => {
      // 点击确认删除
      if (action === "confirm") {
        if (!oidNumber.value) return (oidError.value = "文书模板编号不可为空")
        else if (oidNumber.value !== `DELETE-${row.mrClassCode}`)
          return (oidError.value = "所输入的文书模板编号和待删除的文书模板编号不符合，无法删除")
      }
      done()
    }
  }).then(action => {
    if (action === "confirm") {
      delDocTypeCatalogue({ mrClassCode: row.mrClassCode, item: oidNumber.value })
        .then(() => {
          Message.success("操作成功！")
          commonTableRef.value?.refreshTableData()
        })
        .catch(err => toastError(err, "删除失败"))
    }
  })
}

/* ======================== 签章设置 ======================== */
const sealSettingDialogVisible = ref(false)
const sealSettingLoading = ref(false)

const handleSealSetting = async row => {
  sealSrc.value = ""
  sealSettingDialogVisible.value = true
  for (const key in sealSettingDialogFormData) {
    sealSettingDialogFormData[key] = row[key] || ""
  }
  if (row?.fileWsid) sealSrc.value = await getImage(row?.fileWsid)
}

const sealSrc = ref("")

const sealSettingDialogFormData = reactive({
  mrClassCode: "",
  mrClassName: "",
  fileWsid: "",
  ulx: "" as any,
  uly: "" as any,
  lrx: "" as any,
  lry: "" as any
})

function handleUpload() {
  selectFile(".png", 1024 * 1024 * 30)
    .then(async file => {
      const reader = new FileReader()
      reader.readAsDataURL(file as Blob)
      reader.onload = function (e) {
        const img = new Image()
        img.src = e.target?.result as string
        img.onload = async function () {
          sealSrc.value = (await blobToBase64(file as Blob)) as string
          uploadSystemFile({ file: file })
            .then(res => {
              sealSettingDialogFormData.fileWsid = res.data.data.fileWsid
              Message.success("上传图片成功")
            })
            .catch(error => {
              toastError(error, "上传图片失败")
            })
        }
      }
    })
    .catch(err => {
      if (err.message === "ERROR_FILE_TYPE") {
        Message.error("选择的文件类型错误，请选择正确的文件")
      } else if (err.message === "ERROR_FILE_SIZE") {
        Message.error("文件的大小超过限制")
      }
    })
}

const sealStyle = computed(() => {
  return `left: ${(sealSettingDialogFormData.ulx * 3) / 4}px; top:${(sealSettingDialogFormData.uly * 3) / 4}px`
})

// 输入坐标限制
const scanPosition = (val, key) => {
  if (val < 0) sealSettingDialogFormData[key] = 0
  if (key === "ulx" && val > 595 - 200) sealSettingDialogFormData[key] = 595 - 200
  if (key === "uly" && val > 842 - 200) sealSettingDialogFormData[key] = 842 - 200
}

const handleConfirmSet = async () => {
  if (!sealSettingDialogFormData.fileWsid) return Message.error("请上传图片")
  if (!sealSettingDialogFormData.ulx) return Message.error("请设置印章位置")
  if (!sealSettingDialogFormData.uly) return Message.error("请设置印章位置")
  try {
    sealSettingDialogFormData.lrx = Math.round(sealSettingDialogFormData.ulx) + 200
    sealSettingDialogFormData.lry = Math.round(sealSettingDialogFormData.uly) + 200
    if (sealSettingLoading.value) return
    sealSettingLoading.value = true
    await editDocTypeCatalogue(sealSettingDialogFormData)
    Message.success("设置成功")
    sealSettingDialogVisible.value = false
    sealSettingLoading.value = false
    commonTableRef.value?.refreshTableData()
  } catch (err: any) {
    sealSettingLoading.value = false
    toastError(err, "设置失败")
  }
}
</script>

<style lang="less" scoped>
.seal-setting {
  display: flex;
  align-items: center;

  .left {
    position: relative;

    .tips {
      font-size: 12px;
      color: #333;
    }
    .paging-seal {
      width: 100px;
      height: 100px;
      cursor: pointer;
      border: 1px dashed var(--el-border-color);
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        object-fit: scale-down;
        width: 100%;
        height: 100%;
      }
    }
  }

  .right {
    padding: 0 20px;
    .img-container {
      position: relative;
      display: flex;
      .background {
        // 页面宽度(默认:595.00f) 页面高度(默认:842.00f) 单位 pt
        width: 446.25px;
        height: 631.5px;
        border: 1px solid var(--el-border-color);
        object-fit: contain;
      }
      .sela-img {
        position: absolute;
        width: 150px;
        height: 150px;

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
    }
  }
}
</style>
