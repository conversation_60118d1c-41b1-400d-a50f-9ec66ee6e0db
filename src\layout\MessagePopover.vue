<template>
  <el-popover placement="top-start" trigger="hover" :teleported="false" :width="300" :show-after="200">
    <template #reference>
      <el-badge :hidden="messageData.total <= 0" :value="messageData.total" :max="99" class="news-hint-wrapper">
        <i style="color: #fff" class="ri-notification-3-line"></i>
      </el-badge>
    </template>
    <div class="news-content-wrapper">
      <div class="news-title">{{ `消息通知(${messageData.total})` }}</div>
      <div class="news-content-container">
        <div v-if="messageData.rows.length > 0">
          <div v-for="item in messageData.rows" :key="item.id" class="news-content-item">
            <div class="news-type">
              <div class="red-point"></div>
              <div class="type-name">{{ item.title }}</div>
              <div class="news-time">{{ item.createDateTimeCh }}</div>
            </div>
            <el-tooltip :content="item.content" popper-class="message-detail-tooltip" :show-after="500">
              <div class="news-detail" :tooltip="item.content">{{ item.content }}</div>
            </el-tooltip>
          </div>
        </div>
        <el-empty v-else description="暂无数据" :image-size="60" />
      </div>

      <div class="news-footer" @click="router.push('/message-center')">查看全部</div>
    </div>
  </el-popover>
</template>

<script setup lang="ts">
import { onMounted, reactive, watch } from "vue"
import { useRouter } from "vue-router"
import { getHeaderMessageListApi, getHeaderMessageCountApi } from "@/interfaces"
import { useSystemStore } from "@/stores"

const router = useRouter()
const systemStore = useSystemStore()

/* ======================== 消息通知 ======================== */

type MessageListType = { id: string; title: string; createDateTimeCh: string; content: string }
type MessageDataType = {
  total: number
  rows: Array<MessageListType>
}
const messageData: MessageDataType = reactive({
  total: 0,
  rows: []
})

// 监听其他地方是否已读，刷新数据
watch(
  () => systemStore.refreshMessage,
  val => {
    if (val) {
      refreshMessage()
      systemStore.$patch({ refreshMessage: false })
    }
  }
)

// 页面渲染获取数据
onMounted(() => {
  if (!localStorage.getItem("SessionWsid")) return
  refreshMessage()
})

// 获取未读消息列表和未读消息数量
function refreshMessage() {
  getHeaderMessageCountApi().then(res => {
    const resData = res.data
    messageData.total = resData.data
  })
  getHeaderMessageListApi().then(res => {
    const resData = res.data?.data
    const rows: Array<MessageListType> = []
    const keyArray = ["waitDisposes", "waitMessages", "waitNotices"]
    keyArray.forEach(item => {
      if (resData[item].length > 0) {
        resData[item].forEach(resItem => {
          if (rows.length < 3) {
            rows.push({
              id: resItem.id,
              title: resItem.title,
              content: item === "waitNotices" ? resItem.contentText : resItem.content,
              createDateTimeCh: resItem.createDateTimeCh
            })
          }
        })
      }
    })
    messageData.rows = rows
  })
}
</script>

<style lang="less" scoped>
.el-badge {
  --el-badge-size: 14px;

  .el-badge__content.is-fixed {
    top: 1px;
    right: calc(2px + var(--el-badge-size) / 2);
  }
}

.news-hint-wrapper {
  position: relative;
  margin-right: 36px;
  display: flex;
  align-items: center;
  cursor: pointer;
  &:hover {
    .ri-notification-3-line {
      color: var(--el-color-primary);
    }
  }

  .ri-notification-3-line {
    font-size: 24px;
    color: #666;
  }

  .news-count {
    position: absolute;
    right: 0px;
    top: -4px;
    background: rgba(245, 47, 62, 1);
    font-size: 10px;
    border-radius: 14px;
    line-height: 1;
    padding: 2px 5px;
    color: #fff;
  }
}

.news-content-wrapper {
  margin: -12px;
  .news-title {
    padding: 12px;
    border-bottom: 1px solid #e1e2e6;
    color: #333;
    cursor: default;
  }

  .news-content-container {
    height: 180px;
    cursor: default;

    .news-content-item {
      padding: 6px 12px;
      border-bottom: 1px solid #e1e2e6;

      .news-type {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .red-point {
          width: 6px;
          height: 6px;
          background-color: rgba(245, 47, 62, 1);
          border-radius: 50%;
        }

        .type-name {
          flex: 1;
          margin: 0px 12px;
          color: #333;
        }

        .news-time {
          color: #030814a5;
        }
      }

      .news-detail {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-top: 6px;
      }
    }

    .news-content-item:nth-child(3) {
      border-bottom: none;
    }
  }

  .news-footer {
    padding: 12px;
    text-align: center;
    cursor: pointer;
    color: var(--el-color-primary);
    border-top: 1px solid #e1e2e6;
  }
}
</style>

<style lang="less">
// 顶部消息内容popover样式
.el-popper.message-detail-tooltip {
  max-width: 400px;
}
</style>
