import { StatisticTypeEnum } from "@/configs/enums"

export interface ChartRequestParams {
  startDate: string
  endDate: string
  statisticsType: StatisticTypeEnum | string
}

export interface ChartData {
  timeList?: []
  series?: any
  showLegend?: boolean
  legendData?: []
  tabsData?: { key: string; value: string }[]
  dropdownData?: { key: string; value: string }[]
  date?: Array<any>
  printTypeCode?: string
}

export interface TransactionOverviewProps {
  orderAmount: number
  orderPayNum: number
  addUserNum: number
  orderUserNum: number
}
