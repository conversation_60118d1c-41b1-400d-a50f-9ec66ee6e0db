<template>
  <div id="medical-record-history">
    <div v-loading="state.loading" class="view-left">
      <div class="select-history" :style="{ width: recordTreeRef?.state.isCollapse ? '0px' : '264px' }">
        <div class="title">病案历史</div>
        <el-select
          v-model="state.selectedVersion"
          style="width: 150px"
          placeholder="请选择患者编号"
          @change="handleVersionChange"
        >
          <el-option v-for="item in state.historyOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="content">
        <MedicalRecordTree
          ref="recordTreeRef"
          can-collapse
          :base-info="state.baseInfo"
          :tree-info="state.treeInfo"
          @click-pdf="handlePdfClick"
        />
      </div>
    </div>
    <div class="view-middle common-box-shadow">
      <PdfPreviewComponent ref="pdfPreviewRef" :src="state.pdfSrc" />
    </div>
    <div class="view-right">
      <DocumentMeta
        v-loading="state.loading"
        can-collapse
        :document-wsid="state.documentWsid"
        :base-info-data="state.firstPageFields"
        :file-wsid="state.targetFileWsid"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, ref, computed } from "vue"
import { useRoute } from "vue-router"
import { PdfPreviewComponent } from "@/base-components"
import { MedicalRecordTree, DocumentMeta } from "@/page-components"
import { decryptStr, formatDate } from "@/utils"
import { getRecordDetailApi, getRecordHistoryApi } from "../interface"

const route = useRoute()
const secretKey = route.query.secretKey as string
const inpNo = route.query.inpNo as string

const recordTreeRef = ref()

const state = reactive({
  loading: false,
  pdfSrc: "",
  baseInfo: {},
  firstPageFields: [],
  historyOptions: [] as any[],
  selectedVersion: "",
  targetFileWsid: "",
  treeInfo: {} as any,
  documentWsid: ""
})

onMounted(() => {
  getData(inpNo)
  getHistoryVersion(inpNo, true)
})

const handlePdfClick = node => {
  state.pdfSrc = "/api" + node?.contentFilePath
  state.targetFileWsid = node?.fileWsid
  state.documentWsid = node?.wsid
}

const getData = (inpNo = "") => {
  const params = { inpNo: inpNo }
  if (secretKey) {
    const sealKey = decryptStr(secretKey)
    params["sealKey"] = sealKey
  }
  state.loading = true
  getRecordDetailApi(params).then(res => {
    if (res.data.code !== "100100000") return
    state.baseInfo = res.data.data.baseInfo
    state.firstPageFields = res.data.data.firstPageFields
    state.treeInfo = res.data.data.treeInfo
    state.loading = false
  })
}

// 获取历史版本信息
function getHistoryVersion(inpNo, init = false) {
  getRecordHistoryApi({ inpNo }).then(res => {
    const { code, data } = res.data
    if (code === "100100000") {
      state.historyOptions = data.map(item => ({
        label: `${formatDate(item.outHospitalDatetime)}（${item.inpNo}）`,
        value: item.inpNo
      }))
      if (init) state.selectedVersion = state.historyOptions[0].value
    }
  })
}

// 版本切换
function handleVersionChange(inpNo) {
  getData(inpNo)
  getHistoryVersion(inpNo)
}
</script>

<style lang="less" scoped>
#medical-record-history {
  display: flex;
  justify-content: space-between;
  height: 100%;
  .view-middle {
    overflow-x: auto;
    min-width: 400px;
    flex: 1;
    margin: 0 16px 0px 0px;
    box-sizing: border-box;
  }
  .view-left {
    height: 100%;
    .select-history {
      display: flex;
      width: 234px;
      height: 56px;
      margin-bottom: 16px;
      background-color: white;
      box-shadow: 0 2px 8px rgb(0 35 114 / 10%);
      line-height: 56px;
      gap: 20px;
      align-items: center;
      overflow: hidden;
      transition: all 0.5s;
      .title {
        font-size: 16px;
        color: rgb(3 8 20 / 85%);
        font-weight: 700;
        font-style: normal;
        margin-left: 15px;
        white-space: nowrap;
      }
    }
    .content {
      height: calc(100% - 72px);
    }
  }
}
</style>
