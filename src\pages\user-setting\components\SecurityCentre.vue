<template>
  <div class="security-box-root">
    <div class="security-box-title">安全中心</div>
    <el-descriptions class="security-box-content" title="" :column="1">
      <el-descriptions-item label="工号：">{{ userStore.userHospitalInfo.jobId }}</el-descriptions-item>
      <el-descriptions-item label="手机：">{{ userStore.userHospitalInfo.phone }}</el-descriptions-item>
      <el-descriptions-item label="登录密码：">
        <div class="pwd-content">
          <span class="password">********</span>
          <el-button color="#409fee" plain class="handleModify-btn" @click="handleModify">修改</el-button>
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="打印密码：">
        <div class="pwd-content" style="justify-content: end">
          <el-switch v-model="printPassword" class="ml-2" @change="changePrintPassword" />
        </div>
      </el-descriptions-item>
      <el-descriptions-item v-if="printPassword" label="密码设置：">
        <div class="pwd-content">
          <span class="password">********</span>
          <el-button color="#409fee" plain class="handleModify-btn" @click="handleModifyPrint">设置</el-button>
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="休假时间：">
        <div class="pwd-content">
          <span class="password"></span>
          <el-button color="#409fee" plain class="handleModify-btn" @click="handleModifyHolidayTime">设置</el-button>
        </div>
      </el-descriptions-item>
    </el-descriptions>
  </div>

  <PasswordDialog ref="passwordDialogRef" />

  <!-- 打印密码弹窗 -->
  <DialogContainer v-model:visible="visibleDialog" title="打印密码" :width="500" :confirm-callback="confirmModify">
    <el-form ref="ruleFormRef" label-position="top" :model="ruleForm" :rules="rules" class="form">
      <el-form-item label="打印密码" required prop="newPassword">
        <el-input v-model="ruleForm.newPassword" type="password" placeholder="请输入新密码（至少6位）" show-password />
      </el-form-item>
      <el-form-item label="确认新密码" required prop="confirmPassword">
        <el-input
          v-model="ruleForm.confirmPassword"
          type="password"
          placeholder="请再次确认您的打印密码"
          show-password
        />
      </el-form-item>
    </el-form>
  </DialogContainer>

  <!-- 休假时间弹窗 -->
  <DialogContainer
    v-model:visible="holidayVisible"
    title="休假设置"
    :width="500"
    :confirm-callback="confirmModifyHolidayTime"
  >
    <el-form ref="holidayRuleFormRef" label-position="top" :model="holidayForm" class="form">
      <el-form-item label="休假日期" prop="selectedDateRange">
        <el-date-picker
          v-model="holidayForm.selectedDateRange"
          type="datetimerange"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="x"
          @change="changeDateRange"
        />
      </el-form-item>
    </el-form>
  </DialogContainer>
</template>

<script setup lang="ts">
import { ref, nextTick, reactive, watch, onMounted } from "vue"
import { DialogContainer } from "@/base-components"
import { PasswordDialog } from "@/page-components"
import { getPrintPasswordStatus, getUserHolidayApi } from "@/interfaces"
import { useUserStore, useSystemStore } from "@/stores"
import { Message, toastError } from "@/utils"
import { updatePrintPassword, updatePrintPasswordStatus, updateUserHolidayApi } from "../interface"
import type { FormInstance, FormRules } from "element-plus"

const ruleFormRef = ref<FormInstance>()

//用户信息
const userStore = useUserStore()
const systemStore = useSystemStore()

const passwordDialogRef = ref()

const handleModify = () => {
  passwordDialogRef.value?.openDialog()
}

const ruleForm = reactive({
  newPassword: "",
  confirmPassword: ""
})

const passwordValidator = (_rule: any, value: string, callback: (err?: Error) => void) => {
  if (value === "") {
    callback(new Error("请输入密码"))
  } else {
    const reg = /^.{6,16}$/
    if (reg.test(value)) {
      callback()
    } else {
      callback(new Error("请输入6~16位的密码"))
    }
  }
}

const visibleDialog = ref(false)

const rules: FormRules = {
  newPassword: [
    { required: true, message: "请输入新密码", trigger: "blur" },
    {
      validator: passwordValidator
    }
  ],
  confirmPassword: [
    { required: true, message: "请再次确认您的密码", trigger: "blur" },
    {
      validator: passwordValidator
    }
  ]
}

/* ====================== 打印密码 ======================== */

const handleModifyPrint = () => {
  nextTick(() => {
    visibleDialog.value = true
  })
}
const printPassword = ref(false)

// 获取打印密码开启状态
onMounted(async () => {
  try {
    const status = (await getPrintPasswordStatus())?.data?.data?.status
    printPassword.value = status === "ENABLE"
  } catch (err: any) {
    toastError(err)
  }
})

const changePrintPassword = val => {
  systemStore.showLoading("切换中")
  updatePrintPasswordStatus(val ? "ENABLE" : "DISABLE")
    .then(res => {
      systemStore.hideLoading()
      Message.success("切换成功")
    })
    .catch(err => {
      systemStore.hideLoading()
      toastError(err, "修改状态失败")
    })
}

// 确认修改密码
const confirmModify = () => {
  if (ruleForm.newPassword !== ruleForm.confirmPassword) {
    return Message.error("两次输入密码不一致，请重新输入")
  }
  ruleFormRef.value?.validate(valid => {
    if (!valid) return
    systemStore.showLoading("加载中")
    updatePrintPassword({
      password: ruleForm.confirmPassword
    })
      .then(() => {
        systemStore.hideLoading()
        Message.success("修改密码成功")
        visibleDialog.value = false
      })
      .catch(err => {
        systemStore.hideLoading()
        toastError(err, "修改密码失败")
      })
  })
}

/* ====================== 休假时间 ======================== */
const holidayVisible = ref(false)
const holidayForm = reactive({
  selectedDateRange: [
    new Date(new Date().setHours(0, 0, 0, 0)).getTime(),
    new Date(new Date().setHours(23, 59, 59, 999)).getTime()
  ]
})

onMounted(async () => {
  try {
    const result = (await getUserHolidayApi(userStore.userWsid))?.data?.data
    holidayForm.selectedDateRange = [result.holidayStartTime, result.holidayEndTime]
  } catch (err: any) {
    toastError(err)
  }
})

const holidayRuleFormRef = ref<FormInstance>()

const holidayRules: FormRules = {
  selectedDateRange: [{ required: true, message: "请选择休假日期", trigger: "blur" }]
}

// 确认设置休假时间
const handleModifyHolidayTime = () => {
  nextTick(() => {
    holidayVisible.value = true
  })
}

const changeDateRange = val => {}

const confirmModifyHolidayTime = () => {
  holidayRuleFormRef.value?.validate(valid => {
    if (!valid) return
    systemStore.showLoading("加载中")
    updateUserHolidayApi({
      holidayStartTime: holidayForm.selectedDateRange ? holidayForm.selectedDateRange[0] : null,
      holidayEndTime: holidayForm.selectedDateRange ? holidayForm.selectedDateRange[1] : null
    })
      .then(() => {
        systemStore.hideLoading()
        Message.success("设置成功")
        visibleDialog.value = false
      })
      .catch(err => {
        systemStore.hideLoading()
        toastError(err, "设置失败，请重试")
      })
  })
}
</script>

<style lang="less" scoped>
.security-box-root {
  width: 660px;
  background-color: #fff;
  box-shadow: 0px 2px 10px rgba(0, 35, 114, 0.1);

  .security-box-title {
    font-size: 16px;
    font-weight: 650;
    padding: 15px;
    border-bottom: 1px solid #ebedf0;
  }

  .security-box-content {
    padding: 15px;
    :deep .el-descriptions__cell {
      display: flex;
      align-items: center;
      padding-bottom: 15px;
    }

    :deep .el-descriptions__label {
      width: 80px;
    }

    :deep .el-descriptions__content {
      width: calc(100% - 80px);
    }

    .pwd-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}

.form :deep(.el-form-item) {
  display: block;
}

@media screen and (min-width: 960px) {
  :deep(.el-overlay-dialog) {
    top: -260px;
  }
}
</style>
