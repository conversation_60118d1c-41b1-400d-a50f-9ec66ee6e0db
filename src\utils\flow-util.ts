import { EditorView } from "@codemirror/view"

export const businessProcessIcons = [
  {
    key: "medicalRecordSubmit",
    label: "病案提交",
    img: new URL("/src/assets/svg/business-process/medical-submit.svg", import.meta.url).href
  },
  {
    key: "departmentQualityControl",
    label: "科室质控",
    img: new URL("/src/assets/svg/business-process/department-quality-control.svg", import.meta.url).href
  },
  {
    key: "medicalRecordSendOut",
    label: "病案送出",
    img: new URL("/src/assets/svg/business-process/medical-send-out.svg", import.meta.url).href
  },
  {
    key: "medicalRecordCatalog",
    label: "病案编目",
    img: new URL("/src/assets/svg/business-process/medical-catalog.svg", import.meta.url).href
  },
  {
    key: "finalQualityControl",
    label: "终末质控",
    img: new URL("/src/assets/svg/business-process/final-quality-control.svg", import.meta.url).href
  },
  {
    key: "medicalRecordArchive",
    label: "病案归档",
    img: new URL("/src/assets/svg/business-process/medical-archive.svg", import.meta.url).href
  },
  {
    key: "doctorAudit",
    label: "上级医师审核",
    img: new URL("/src/assets/svg/business-process/department-quality-control.svg", import.meta.url).href
  },
  {
    key: "nurseReceive",
    label: "质控护士签收",
    img: new URL("/src/assets/svg/business-process/medical-send-out.svg", import.meta.url).href
  },
  {
    key: "codeAudit",
    label: "编码审核",
    img: new URL("/src/assets/svg/business-process/department-quality-control.svg", import.meta.url).href
  },
  {
    key: "medicalSignFor",
    label: "病案签收",
    img: new URL("/src/assets/svg/business-process/medical-sign-for.svg", import.meta.url).href
  },
  {
    key: "medicalQualityControl",
    label: "医疗质控",
    img: new URL("/src/assets/svg/business-process/medical-quality-control.svg", import.meta.url).href
  },
  {
    key: "nursingQualityControl",
    label: "护理质控",
    img: new URL("/src/assets/svg/business-process/nursing-quality-control.svg", import.meta.url).href
  },
  {
    key: "medicalControl",
    label: "医护质控",
    img: new URL("/src/assets/svg/business-process/medical-control.svg", import.meta.url).href
  }
]

export enum ConfigFormTypeEnum {
  INPUT = "INPUT",
  SELECT = "SELECT",
  RADIO = "RADIO",
  RADIO_PLUS = "RADIO_PLUS",
  DATE = "DATE",
  TEXTAREA = "TEXTAREA",
  NUMBER = "NUMBER",
  SWITCH = "SWITCH"
}

enum ConfigFormTypeValidateErrorDesc {
  INPUT = "请输入",
  SELECT = "请选择",
  RADIO = "请选择",
  RADIO_PLUS = "请检查",
  DATE = "请选择",
  TEXTAREA = "请输入",
  NUMBER = "请输入",
  SWITCH = "请选择"
}

// 校验业务流程和全局配置
export const checkBusinessProcessAndGlobalConfig = (configs: Array<Record<string, any>>, title: string) => {
  let errorInfo = {}
  const formRadioRelations = configs.map(item => {
    return {
      [item.businessKey]: item?.formRadioRelation
    }
  })
  // 将formRadioRelations合并成一个对象
  const formRadioRelationsObj = formRadioRelations.reduce((prev, next) => {
    return { ...prev, ...next }
  }, {})

  //TODO 只支持同组内的值，跨节点的值暂不支持
  const formRadioRelationsArr = configs.map(item => item?.formRadioRelation).flat()

  configs.map((item: Record<string, any>) => {
    if (formRadioRelationsArr.includes(item?.businessKey)) {
      //去formRadioRelationsObj里面找到item?.businessKey所在的key
      const targetKey = Object.keys(formRadioRelationsObj).find(key =>
        formRadioRelationsObj[key]?.includes(item?.businessKey)
      )
      const targetConfig = configs.find(config => config?.businessKey === targetKey)
      //如果targetConfig的value是'0',则item的value可以为空
      if (targetConfig?.value === "0") {
        return true
      }
    }
    if (!item.value) {
      errorInfo = {
        ...errorInfo,
        [title]: {
          ...errorInfo[title],
          [item.formLabel]: `${ConfigFormTypeValidateErrorDesc[item.formType]}`
        }
      }
      return false
    }
    if (item?.formType === "RADIO_PLUS") {
      const extraConfig = JSON.parse(item.extraConfig)
      const targetConfig = extraConfig.find(config => config?.radioValue === item.value)
      // 如果选的值需要填附加项
      if (extraConfig && targetConfig) {
        if (!item.extraValue) {
          errorInfo = {
            ...errorInfo,
            [title]: {
              ...errorInfo[title],
              [item.formLabel]: `${targetConfig?.label}不能为空`
            }
          }
          return false
        }
      }
    }
    return true
  })
  return {
    validate: false,
    errorInfo
  }
}

export const myTheme = EditorView.theme(
  {
    // 输入的字体颜色
    "&": {
      color: "#0052D9",
      backgroundColor: "#FFFFFF"
    },
    ".cm-content": {
      caretColor: "#0052D9"
    },
    // 激活背景色
    ".cm-activeLine": {
      backgroundColor: "#FFFFDD"
    },
    // 激活序列的背景色
    ".cm-activeLineGutter": {
      backgroundColor: "#FAFAFA"
    },
    //光标的颜色
    "&.cm-focused .cm-cursor": {
      borderLeftColor: "#0052D9"
    },
    // 选中的状态
    "&.cm-focused .cm-selectionBackground, ::selection": {
      backgroundColor: "#0052D9",
      color: "#dcdfe6"
    },
    // 左侧侧边栏的颜色
    ".cm-gutters": {
      backgroundColor: "#FFFFFF",
      color: "#ddd", //侧边栏文字颜色
      border: "none"
    }
  },
  { dark: true }
)
