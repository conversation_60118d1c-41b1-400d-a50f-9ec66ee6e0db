import type { TableColumnItem } from "@/types"
import type { FormRules } from "element-plus"

export const secrecyTableColumns: Array<TableColumnItem> = [
  { type: "selection", width: 50, fixed: "left" },
  { prop: "secrecyGrade", label: "保密等级", minWidth: 100 },
  { prop: "secrecyScope", label: "保密类型", minWidth: 100 },
  { prop: "mrNo", label: "病案号", minWidth: 100, sortable: true, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 100, sortable: true, must: true },
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "visitId", label: "住院次数", minWidth: 120, sortable: true },
  { prop: "inHospitalDatetime", label: "入院时间", minWidth: 180 },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 100 },
  { prop: "outHospitalDatetime", label: "出院时间", minWidth: 180 },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 100 },
  { prop: "outDischargeDiagnosisName", label: "出院诊断", minWidth: 100 },
  { prop: "residentName", label: "住院医师", minWidth: 100 },
  { prop: "doctorName", label: "主治医师", minWidth: 100 },
  { prop: "operationName", label: "手术名称", minWidth: 100 },
  { prop: "operationDatetime", label: "手术时间", minWidth: 180 },
  { prop: "incisionGradeName", label: "切口等级", minWidth: 100 },
  { prop: "anaesthesiaTypeName", label: "麻醉方式", minWidth: 100 },
  { prop: "status", label: "病案状态", minWidth: 100 },
  { prop: "operation", label: "操作", width: 75, fixed: "right", must: true }
]

export const timeOptions = [
  { label: "2小时", value: "1" },
  { label: "1天", value: "2" },
  { label: "2天", value: "3" },
  { label: "7天", value: "4" },
  { label: "永久", value: "5" }
]

export const secrecyFormRules: FormRules = {
  secrecyScope: [{ required: true, message: "请选择保密类型", trigger: "blur" }],
  secrecyGrade: [{ required: true, message: "请选择保密等级", trigger: "blur" }]
}
