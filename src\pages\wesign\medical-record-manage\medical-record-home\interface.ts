import axios from "@/interfaces/axios-instance"

/* ================= 列表查询 ================= */

export const getMedicalRecordListApi = (params: Record<string, any>) => {
  params.filters?.includes("isLateFiling") &&
    (params.filters = params.filters.replace("isLateFiling=true", "isLateFiling=YES"))
  params.filters?.includes("isSealing") && (params.filters = params.filters.replace("isSealing=true", "isSealing=YES"))
  params.filters?.includes("isSpecial") && (params.filters = params.filters.replace("isSpecial=true", "isSpecial=YES"))
  params.filters?.includes("isRepair") && (params.filters = params.filters.replace("isRepair=true", "isRepair=YES"))
  params.filters?.includes("isInHospital") && (params.filters = params.filters.replace("isRepair=true", "isRepair=YES"))
  params.filters?.includes("isOutHospital") &&
    (params.filters = params.filters.replace("isRepair=true", "isRepair=YES"))
  return axios({
    method: "get",
    // url: `/api/document/bags/merge`,
    url: `/api/document/complex-query`,
    params: params
  })
}

// 获取历史病案列表
export const getHistoryMedicalRecordListApi = (params: Record<string, any>) => {
  params.filters?.includes("isLateFiling") &&
    (params.filters = params.filters.replace("isLateFiling=true", "isLateFiling=YES"))
  params.filters?.includes("isSealing") && (params.filters = params.filters.replace("isSealing=true", "isSealing=YES"))
  params.filters?.includes("isSpecial") && (params.filters = params.filters.replace("isSpecial=true", "isSpecial=YES"))
  params.filters?.includes("isRepair") && (params.filters = params.filters.replace("isRepair=true", "isRepair=YES"))
  params.filters?.includes("isInHospital") && (params.filters = params.filters.replace("isRepair=true", "isRepair=YES"))
  params.filters?.includes("isOutHospital") &&
    (params.filters = params.filters.replace("isRepair=true", "isRepair=YES"))
  return axios({
    method: "get",
    url: `/api/document/his/paging`,
    params: params
  })
}

/* ================= 病案提交 ================= */

// 获取病案提交配置 - 是否需要签名
export const getSubmitConfigApi = (documentBagWsid: string) => {
  return axios({
    method: "get",
    url: `/api/document/bags/${documentBagWsid}/submit-config`
  })
}

/* ================= 缺失登记 ================= */

// 根据科室获取文书分类
export const getRecordTypeByDeptApi = (deptCode: string) => {
  return axios({
    method: "get",
    url: "/api/document/mrClassDept",
    params: {
      deptCode: deptCode
    }
  })
}

// 获取病案登记的缺失表格数据
export const getMissingReportListApi = (documentBagWsid: string) => {
  return axios({
    method: "get",
    url: `/api/document/deletion/${documentBagWsid}`
  })
}

// 提交病案缺失记录
export const submitMissingReportApi = (documentBagWsid: string, inpNo: string, missingReport: any) => {
  return axios({
    method: "post",
    url: "/api/document/deletion/addOrUpdate",
    data: {
      documentBagWsid: documentBagWsid,
      inpNo: inpNo,
      ...missingReport
    }
  })
}

// 删除病案缺失记录
export const deleteMissingReportApi = (wsid: string) => {
  return axios({
    method: "delete",
    url: "/api/document/deletion",
    params: { wsid: wsid }
  })
}

// 获取提交缺失登记详情
export const getMissingReportDetailApi = (documentBagWsid: string) => {
  return axios({
    method: "get",
    url: `/api/document/deletion/${documentBagWsid}`
  })
}

/* ================= 特殊病案 ================= */

// 获取特殊病案信息
export const getSpecialInfoApi = (inpNo: string) => {
  return axios({
    method: "get",
    url: `/api/document/bags/${inpNo}/special`
  })
}

// 设置特殊病案信息
export const setSpecialInfoApi = (inpNo: string, properties: string[], desc: string) => {
  return axios({
    method: "post",
    url: `/api/document/bags/${inpNo}/special`,
    data: {
      properties: properties,
      desc: desc
    }
  })
}
