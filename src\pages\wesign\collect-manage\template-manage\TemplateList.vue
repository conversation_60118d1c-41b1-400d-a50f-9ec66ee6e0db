<template>
  <PageContainer>
    <SearchContainer @query-btn-click="templateName = inputValue" @reset-btn-click="handleReset">
      <CommonInputFormItem v-model="inputValue" label="模板名称" width="280px" />
    </SearchContainer>

    <CommonTable
      ref="templateTableRef"
      :table-columns="templateTableColumns"
      :request-api="getTemplateList"
      :request-params="{ templateName }"
      :data-callback="dataCallback"
    >
      <template #header>
        <div style="margin-bottom: 12px">
          <AddButton @click="uploadTemplate('add')">上传模板</AddButton>
          <BatchOperationButton type="danger" :icon="Delete" :disabled="!isSelected" @click="batchDelete()">
            批量删除
          </BatchOperationButton>
        </div>
      </template>
      <template #operation="{ row }">
        <TableButton @click="uploadTemplate('update', row.templateWsid)">重新上传</TableButton>
        <TableButton @click="viewTemplate(row)">查看</TableButton>
        <TableButton @click="changeStatus(row)">{{ row.status ? "禁用" : "启用" }}</TableButton>
        <TableButton @click="handleDelete(row)">删除</TableButton>
      </template>
    </CommonTable>
  </PageContainer>
</template>

<script setup lang="ts">
import { ref, computed } from "vue"
import { useRouter } from "vue-router"
import { Delete } from "@element-plus/icons-vue"
import {
  SearchContainer,
  PageContainer,
  CommonTable,
  AddButton,
  TableButton,
  BatchOperationButton,
  CommonInputFormItem
} from "@/base-components"
import { getTemplateList } from "@/interfaces"
import { SystemPrompt, Message, formatDatetime, selectFile } from "@/utils"
import { templateTableColumns } from "./config"
import { addTemplate, changeTemplateStatus, updateTemplate, deleteTemplate } from "./interface"

const router = useRouter()

/* ======================== 搜索相关数据及方法 ======================== */

const inputValue = ref("")
const templateName = ref("")

// 重置搜索
function handleReset() {
  templateName.value = ""
  inputValue.value = ""
}

/* ======================== 表格操作 ======================== */

const templateTableRef = ref<InstanceType<typeof CommonTable>>()

// 是否有表格项被选中
const isSelected = computed(() => {
  const selectedRows = templateTableRef.value?.tableState?.selectedRows ?? []
  return selectedRows.length > 0
})

function dataCallback(data: Array<Record<string, any>>) {
  return data.map(item => ({
    ...item,
    createTime: formatDatetime(item.createTime),
    updateTime: formatDatetime(item.updateTime)
  }))
}

// 上传&重新上传文件
const uploadTemplate = (type: "add" | "update", templateWsid?: string) => {
  selectFile(".docx", 1024 * 1024 * 30)
    .then((file: any) => {
      const handler =
        type === "add"
          ? addTemplate({ fileName: file.name.slice(0, file.name.length - 5), file })
          : updateTemplate({ fileName: file.name.slice(0, file.name.length - 5), file, templateWsid })
      handler.then(() => {
        templateTableRef.value?.refreshTableData()
      })
    })
    .catch(err => {
      if (err.message === "ERROR_FILE_TYPE") {
        Message.error("选择的文件类型错误，请选择正确的文件")
      } else if (err.message === "ERROR_FILE_SIZE") {
        Message.error("文件的大小超过限制")
      }
    })
}

// 启用&禁用模板
const changeStatus = row => {
  const operation = row.status ? "禁用" : "启用"
  const nextStatus = row.status ? "DISABLE" : "ENABLE"
  SystemPrompt(`您正在${operation}名称为“${row.templateName}”的模板，确定${operation}？`).then(() => {
    changeTemplateStatus({ templateWsid: row.templateWsid, status: nextStatus }).then(() => {
      templateTableRef.value?.refreshTableData()
    })
  })
}

// 删除单个模板
function handleDelete(row) {
  SystemPrompt(`您正在删除名称为“${row.templateName}”的模板，确定删除？`, "error").then(() =>
    deleteTemplate([row.templateWsid]).then(() => {
      templateTableRef.value?.refreshTableData()
    })
  )
}

// 批量删除
function batchDelete() {
  const selectedRows = templateTableRef.value?.tableState.selectedRows ?? []
  const wsids = selectedRows.map(item => item.templateWsid)
  SystemPrompt("您正在批量删除所选模板，确定删除？").then(() =>
    deleteTemplate(wsids).then(() => {
      templateTableRef.value?.refreshTableData()
    })
  )
}

// 查看模板
const viewTemplate = row => {
  router.push({ path: "/collect/template/view", query: { wsid: row.templateWsid } })
}
</script>
