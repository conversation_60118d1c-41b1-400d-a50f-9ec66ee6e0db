<template>
  <div id="print-application">
    <div class="header">
      <div class="tab-wrapper">
        <el-tabs v-model="state.previewType" class="demo-tabs">
          <el-tab-pane label="病案打印" name="print"></el-tab-pane>
          <el-tab-pane label="证件采集" name="gather"></el-tab-pane>
        </el-tabs>
      </div>
      <div class="operation">
        <el-button text :icon="RefreshRight" style="color: #79bbff; font-size: 20px" @click="fetchClient"></el-button>
        <el-select v-model="toClientId" placeholder="请选择推送设备" clearable filterable>
          <el-option
            v-for="item in clientList"
            :key="item.clientId"
            :label="item.name"
            :value="item.clientId"
          ></el-option>
        </el-select>
        <el-button
          style="margin-left: 12px"
          type="primary"
          :disabled="!allowedSubmit || state.loading"
          @click="confirmSubmit"
        >
          提交并审核
        </el-button>
      </div>
    </div>
    <div class="print-preview-container">
      <!-- <el-radio-group v-model="state.previewType" class="print-preview-tab">
        <el-radio-button label="print">病案打印</el-radio-button>
        <el-radio-button label="gather">证件采集</el-radio-button>
      </el-radio-group> -->

      <div v-show="state.previewType === 'print'" class="print-preview-wrapper">
        <div v-loading="state.loading" class="view-left">
          <!-- 病案列表 -->
          <el-collapse v-if="state.patientsList.length > 0" v-model="formData.inpNo" accordion @change="getRecordData">
            <el-collapse-item
              v-for="(item, index) in state.patientsList"
              :key="index"
              :title="formatDatetime(item.outHospitalDatetime, 'YYYY/MM/DD') + '(' + item.inHospitalDeptName + ')'"
              :name="item.inpNo"
            >
              <el-checkbox
                v-model="checkAll"
                label="全选"
                :disabled="checkAllDisable"
                @change="$event => checkAllNodes($event, index)"
              />
              <CommonTree
                ref="commonTreeRef"
                :show-checkbox="true"
                :data="state.treeInfo"
                :checked-keys="state.checkedKeys"
                @click-node="handlePdfClick"
                @check-node="checkNode"
              >
                <template #title="{ node, data }">
                  <OverflowTooltip
                    :content="getNodeTooltipContent(node, data, false) + getCatalogPage(data)"
                    max-width="85%"
                  />
                </template>
              </CommonTree>
            </el-collapse-item>
          </el-collapse>
          <el-empty v-else :image="NoDataImage" description="请先查询待打印患者信息" :image-size="100" />
        </div>
        <!-- pdf展示 -->
        <div class="view-middle">
          <div v-if="!state.pdfSrc" class="blank">暂无数据,请先查询待打印患者信息</div>
          <PdfPreviewComponent v-else :src="state.pdfSrc" />
        </div>
      </div>
      <!-- 证件信息采集 -->
      <div v-show="state.previewType === 'gather'" class="print-preview-wrapper">
        <GatherInfo ref="GatherInfoRef" :relationship="formData.relationship" @ocr-update="updateOcrInfo" />
      </div>
    </div>
    <div class="view-right">
      <!-- <el-radio-group v-model="state.selectValue" class="title">
        <el-radio-button :label="MedicalPrintMetaTypeEnum.Registration">登记</el-radio-button>
        <el-radio-button :label="MedicalPrintMetaTypeEnum.Tracer">示踪</el-radio-button>
      </el-radio-group> -->

      <div class="tab-wrapper">
        <el-tabs v-model="state.selectValue" class="demo-tabs">
          <el-tab-pane label="登记" :name="MedicalPrintMetaTypeEnum.Registration"></el-tab-pane>
          <el-tab-pane label="示踪" :name="MedicalPrintMetaTypeEnum.Tracer"></el-tab-pane>
        </el-tabs>
      </div>

      <div v-loading="GatherInfoRef?.uploadLoading" class="content">
        <template v-if="state.selectValue === MedicalPrintMetaTypeEnum.Registration">
          <div class="search-container">
            <!-- 搜索病案 -->
            <PatientFilterFormItem
              v-model:filter-prop="state.complexSelect"
              v-model:filter-value="state.complexInput"
              label=""
              :show-search="true"
              :filter-prop-options="filterPropOptions"
              @filter-prop-change="changeSelectValue"
              @search="search"
            />

            <!-- 选中病案信息 -->
            <!-- <el-descriptions v-loading="state.loading" title="" :column="1">
              <el-descriptions-item label="姓名：">{{ state.baseInfo.patientName || "暂无" }}</el-descriptions-item>
              <el-descriptions-item label="患者编号：">{{ state.baseInfo.patientId || "暂无" }}</el-descriptions-item>
              <el-descriptions-item label="出院科室：">
                {{ state.baseInfo.inHospitalDeptName || "暂无" }}
              </el-descriptions-item>
              <el-descriptions-item label="出院日期：">
                {{ formatDatetime(state.baseInfo.outHospitalDatetime) }}
              </el-descriptions-item>
              <el-descriptions-item label="入院日期：">
                {{ formatDatetime(state.baseInfo.inHospitalDatetime) }}
              </el-descriptions-item>
            </el-descriptions> -->
          </div>

          <!-- 申请需要填写表单 -->
          <div class="form-container">
            <el-form ref="printFormRef" label-width="120px" label-suffix="：" :model="formData" :rules="printFormRules">
              <FormGroupHeader title="患者信息" />

              <CommonInputFormItem v-model="formData.patientName" label="患者姓名" prop="patientName" clearable />

              <CommonSelectFormItem
                v-model="formData.papersType"
                label="证件类型"
                prop="papersType"
                :options="typeOptions"
              />

              <el-form-item
                :label="formData.papersType === 'PHONE' ? '患者手机号' : '患者证件号'"
                prop="idNumber"
                :rules="idNumberRule"
              >
                <el-input v-model.trim="formData.idNumber" placeholder="刷卡或手动输入" clearable></el-input>
              </el-form-item>

              <FormGroupHeader title="申请人信息" />

              <CommonSelectFormItem
                v-model="formData.relationship"
                label="与患者关系"
                prop="relationship"
                :options="relationshipStrOptions"
              />

              <CommonInputFormItem v-model="formData.agentName" label="申请人姓名" prop="agentName" clearable />

              <CommonInputFormItem
                v-model="formData.agentIdCard"
                label="申请人证件号"
                prop="agentIdCard"
                placeholder="请输入申请人证件号（刷卡或手动输入）"
                clearable
              />

              <CommonInputFormItem v-model="formData.agentPhone" label="申请人电话" prop="agentPhone" clearable />

              <FormGroupHeader title="打印信息" />
              <el-form-item label="打印类型" prop="printTypeCode">
                <el-select v-model="formData.printTypeCode" placeholder="请选择打印类型" @change="setPrintTypeName">
                  <el-option
                    v-for="item in printTypeOptions"
                    :key="item.groupid"
                    :label="item.groupname"
                    :value="item.groupid"
                  ></el-option>
                </el-select>
              </el-form-item>

              <CommonSelectFormItem
                v-model="formData.printPurposeCode"
                label="打印用途"
                prop="printPurposeCode"
                :options="printPurposeTypeOptions"
                @change="setPrintPurposeName"
              />

              <el-form-item label="打印份数" prop="duplicateNum" :rules="duplicateNumRule">
                <el-input
                  v-model.number="formData.duplicateNum"
                  placeholder="请输入"
                  type="number"
                  :min="1"
                  @input="calculateOrderAmount"
                ></el-input>
              </el-form-item>

              <el-form-item label="总打印页数">{{ totalPrintPages }}页</el-form-item>

              <FormGroupHeader :title="'费用信息'"></FormGroupHeader>
              <el-form-item label="支付金额">{{ orderAmount }}元</el-form-item>
            </el-form>

            <el-form ref="mailFormRef" label-width="120px" label-suffix="：" :model="printMail" :rules="mailFormRules">
              <FormGroupHeader :title="'邮寄信息'">
                <template #content>
                  <el-button text :icon="RefreshRight" style="color: #79bbff" @click="fetchMailAddress">
                    更新地址
                  </el-button>
                </template>
              </FormGroupHeader>
              <el-form-item label="是否邮寄">
                <el-radio-group v-model="state.mail">
                  <el-radio :label="true">是</el-radio>
                  <el-radio :label="false">否</el-radio>
                </el-radio-group>
              </el-form-item>
              <template v-if="state.mail">
                <el-form-item label="收件人" prop="mailName">
                  <el-select v-model="printMail.mailName" placeholder="请选择收件人" @change="setPrintTypeName">
                    <el-option
                      v-for="item in addressList"
                      :key="item.userCode"
                      :label="item.mailName"
                      :value="item.mailName"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="手机号" prop="mailPhone">
                  <el-select v-model="printMail.mailPhone" placeholder="请选择手机号" @change="setPrintTypeName">
                    <el-option
                      v-for="item in addressList"
                      :key="item.userCode"
                      :label="item.mailPhone"
                      :value="item.mailPhone"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <!-- <CommonInputFormItem v-model="printMail.mailName" label="收件人" prop="mailName" />

                <CommonInputFormItem v-model="printMail.mailPhone" label="手机号" prop="mailPhone" /> -->

                <el-form-item label="收件地址" prop="mailAddress">
                  <el-cascader
                    v-model="mailAddress"
                    placeholder="请选择"
                    :options="regionData"
                    :props="{ value: 'label' }"
                    @change="$event => changeMailAddress($event)"
                  />
                  <!-- <el-input v-model="printMail.mailAddress" placeholder="请输入详细地址"></el-input> -->

                  <el-select v-model="printMail.mailAddress" placeholder="请选择详细地址" @change="setPrintTypeName">
                    <el-option
                      v-for="item in addressList"
                      :key="item.userCode"
                      :label="item.mailAddress"
                      :value="item.mailAddress"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </template>
            </el-form>
          </div>
          <!-- <el-button class="confirm-button" type="primary" @click="confirmSubmit">确定</el-button> -->
        </template>
        <!-- 示踪 -->
        <template v-else>
          <el-timeline v-if="traceData.length > 0" class="trace-timeline">
            <el-timeline-item v-for="(item, index) in traceData" :key="index">
              <div class="time-header">
                <div>{{ formatDatetime(item.createdDatetime) }}</div>
                <TableButton @click="() => InfoDetailDialogRef?.openDialog(item.printApplyWsid, item.patientInpNo)">
                  查看详情
                </TableButton>
              </div>
              <div class="info-wrapper">
                {{ `第${traceData.length - index}次  ${item.operator} 打印` }}
              </div>
              <div class="info-wrapper">
                {{ `病案文书共计${item.pageCount}页` }}
              </div>
            </el-timeline-item>
          </el-timeline>
          <EmptyContent v-else desc="暂无数据" />
        </template>
      </div>
    </div>

    <InfoDetailDialog ref="InfoDetailDialogRef" />
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, ref, watch, nextTick, onBeforeUnmount } from "vue"
import { regionData } from "element-china-area-data"
import { debounce } from "lodash-es"
import { RefreshRight } from "@element-plus/icons-vue"
import {
  CommonInputFormItem,
  CommonSelectFormItem,
  PdfPreviewComponent,
  FormGroupHeader,
  PatientFilterFormItem,
  CommonTree,
  OverflowTooltip,
  TableButton,
  EmptyContent
} from "@/base-components"
import NoDataImage from "@/assets/png/nodata.png"
import { useCommonOptions } from "@/hooks"
import {
  getRecordListApi,
  getRecordViewData,
  getApproveReasonApi,
  getPrintType,
  querySystemConfig,
  getClientListApi,
  getPrintAddressApi,
  pushSelectDocApi,
  sseHeartbeatApi
} from "@/interfaces"
import { MedicalPrintMetaTypeEnum, RelationshipType, RelationshipTypeEnum } from "@/types"
import {
  formatDatetime,
  extractErrorMsg,
  Message,
  phoneNumberValidator,
  IdNumberValidator,
  flattenTree,
  formatRecordTree,
  getNodeTooltipContent,
  getCatalogPage,
  getFileNodeList,
  toastError,
  createSSEStream
} from "@/utils"
import { getPrintFile, applyForPrinting, getPrintPatientInfoApi, getApplyPrintRecordsApi } from "../interface"
import GatherInfo from "./components/gather-info.vue"
import InfoDetailDialog from "./components/info-detail-dialog.vue"
import { typeOptions, relationshipStrOptions, printFormRules, mailFormRules } from "./config"
import type { FormInstance } from "element-plus"

const SSEUrl = window.__GLOBAL_CONFIG__?.SSEUrl

const state = reactive({
  loading: false,
  baseInfo: {} as Record<string, any>, // 患者基础信息
  pdfSrc: "",
  treeInfo: [] as Array<Record<string, any>>, // 左侧tree需要的原始数据
  complexInput: "",
  complexSelect: "idCard",
  patientsList: [] as any,
  mail: false,
  checkedKeys: [] as Array<string>,
  allFiles: [] as Array<Record<string, any>>,
  selectValue: MedicalPrintMetaTypeEnum.Registration,
  previewType: "print" as "print" | "gather"
})

const commonTreeRef = ref<Array<any>>([])
const GatherInfoRef = ref()
const InfoDetailDialogRef = ref()

/* ======================== 表单相关数据及方法 ======================== */

const formData = reactive({
  documentBagWsid: "",
  inpNo: "",
  patientName: "",
  papersType: "ID_CARD" as "ID_CARD" | "MEDICAL_CARD" | "HEALTH_CARD" | "PHONE" | "FINGERPRINT",
  idNumber: "",
  relationship: RelationshipTypeEnum.OWN as RelationshipType,
  agentName: "",
  agentIdCard: "",
  agentPhone: "",
  printTypeCode: "",
  printTypeName: "",
  printPurposeCode: "",
  printPurposeName: "",
  duplicateNum: 1,
  paymentMethod: "1",
  documentLists: [] as Array<string> // 选中的病案文件wsid集合
})

// 选填表单 邮寄信息
const printMail = reactive({
  mailName: "",
  mailPhone: "",
  mailAddress: ""
})

const checkAll = ref(false)

const checkAllDisable = ref(false)

const printFormRef = ref<FormInstance>()
const mailFormRef = ref<FormInstance>()

// 获取打印类型、打印用途、病案保密等级、复印费
const { options: printPurposeTypeOptions, getOptionLabel: getPrintPurposeLabel } = useCommonOptions({
  getOptionsApi: () => getApproveReasonApi({ groupKey: "PRINT_PURPOSE" }),
  labelAlias: "value",
  valueAlias: "wsid"
})
const printTypeOptions = ref<Array<any>>([])
const printSecrecyGrade = ref("")
const printAmount = ref(0)

const sseSource = ref("" as any)
const intervalId = ref()

const duplicateNumRule = ref<Array<any>>([])
onMounted(async () => {
  getPrintType().then(resData => {
    printTypeOptions.value = resData
    printTypeOptions.value.push({
      groupid: "99",
      groupname: "自定义"
    })
  })
  await querySystemConfig({ type: "PRINT" }).then(res => {
    printSecrecyGrade.value = res.data.data.find(item => item.key === "printSecrecyGrade").value
    printAmount.value = Number(res.data.data.find(item => item.key === "printAmount").value) * 100
    duplicateNumRule.value = [
      { required: true, message: "请输入打印份数", trigger: "blur" },
      {
        type: "number",
        min: 1,
        max: Number(res.data.data.find(item => item.key === "maxPrintDuplicateNum").value),
        message: `打印份数最多为${Number(res.data.data.find(item => item.key === "maxPrintDuplicateNum").value)}`,
        trigger: "blur"
      }
    ]
  })

  clientList.value = (await getClientListApi("SIGN_PAD"))?.data?.data

  try {
    formClientId.value = localStorage.getItem("formClientId")
      ? (localStorage.getItem("formClientId") as string)
      : `WEB${Math.round(Math.random() * 100000)}`
    localStorage.setItem("formClientId", formClientId.value)
    sseSource.value = new EventSource(`${SSEUrl}?clientId=${formClientId.value}&t=${new Date().getTime()}`)
    // 连接打开
    sseSource.value.onopen = function () {
      console.log("连接打开")
    }

    // 连接错误
    sseSource.value.onerror = function (err) {
      console.log("连接错误:", err)
      handleCloseSse()
      sseSource.value = new EventSource(`${SSEUrl}?clientId=${formClientId.value}&t=${new Date().getTime()}`)
    }

    // 接收到数据
    sseSource.value.onmessage = function (event) {
      console.log("接收到数据:", event)
      const data = JSON.parse(event?.data || "{}")
      const patientIdCard = JSON.parse(data?.data || "{}")?.patientIdCard
      // 可提交判断条件
      if (patientIdCard !== "" && patientIdCard === formData.idNumber) allowedSubmit.value = true
    }
    intervalId.value = setInterval(async () => {
      try {
        await sseHeartbeatApi(formClientId.value)
      } catch (err: any) {
        console.log("output", err)
        // 重连
        sseSource.value = new EventSource(`${SSEUrl}?clientId=${formClientId.value}&t=${new Date().getTime()}`)
      }
    }, 4 * 60 * 1000)
  } catch (err) {
    console.log("output", err)
  }
})

const handleCloseSse = () => {
  sseSource.value.close()
}

onBeforeUnmount(() => {
  clearInterval(intervalId.value)
})

const idNumberRule = ref<Array<any>>([{ required: true, message: "请输入", trigger: "blur" }])

watch(
  () => formData.papersType,
  val => {
    if (val === "ID_CARD") {
      idNumberRule.value = [
        { required: true, message: "请输入", trigger: "blur" },
        { validator: IdNumberValidator, trigger: "blur" }
      ]
    } else if (val === "PHONE") {
      idNumberRule.value = [
        { required: true, message: "请输入", trigger: "blur" },
        { validator: phoneNumberValidator, trigger: "blur" }
      ]
    } else {
      idNumberRule.value = [{ required: true, message: "请输入", trigger: "blur" }]
    }
  }
)

const mailAddress = ref("")
// 选择地址
const changeMailAddress = val => {
  printMail.mailAddress = val.join("")
}

// 选择打印类型
const setPrintTypeName = async val => {
  // 清空已选数据
  state.checkedKeys = []
  totalPrintPages.value = 0
  formData.documentLists = []
  formData.printTypeName = printTypeOptions.value.find(option => option.groupid === val).groupname
  if (formData.documentBagWsid && val !== "99") {
    await getPrintFileByPrintType()
    // 选择方案后，push文书数据
    if (toClientId.value) await submit()
  }
}

// 选择打印用途
const setPrintPurposeName = val => {
  formData.printPurposeName = getPrintPurposeLabel(val)
}

// 是否是本人
watch(
  () => formData.relationship,
  val => {
    if (val === "OWN") {
      formData.agentName = formData.patientName
      if (formData.papersType === "PHONE") formData.agentPhone = formData.idNumber
      if (formData.papersType === "ID_CARD") formData.agentIdCard = formData.idNumber
    }
  }
)

// 提交
const confirmSubmit = () => {
  if (formData.documentLists.length < 1) {
    Message.warning("请选择病案文件")
    return
  }
  printFormRef.value?.validate(valid => {
    // 需要验证邮寄信息
    if (state.mail) {
      mailFormRef.value?.validate(result => {
        if (!valid || !result) return
        if (!validateGatherInfo()) return
        submitApi()
      })
    } else {
      // 直接验证其余信息
      if (!valid) return
      if (!validateGatherInfo()) return
      submitApi()
    }
  })
}

function validateGatherInfo() {
  if (!GatherInfoRef.value || !GatherInfoRef.value?.certificateInfo) {
    Message.warning("请前往证件采集，并完成所有相关证件的上传！")
    return false
  }
  for (let key in GatherInfoRef.value?.certificateInfo) {
    if (!GatherInfoRef.value?.certificateInfo[key]) {
      Message.warning("请完成所有相关证件的上传！")
      return false
    }
  }

  return true
}
const submitApi = () => {
  if (state.loading) return
  state.loading = true
  applyForPrinting(
    state.mail
      ? {
          authimageFile: GatherInfoRef.value?.certificateInfo,
          printMail: printMail,
          ...formData
        }
      : {
          authimageFile: GatherInfoRef.value?.certificateInfo,
          ...formData
        }
  )
    .then(() => {
      Message.success("操作成功")
      resetData()
    })
    .catch(err => {
      Message.error(extractErrorMsg(err, "操作失败，请重试"))
    })
    .finally(() => {
      state.loading = false
      activeRecord.lastInpNo = ""
    })
}

/* ======================== 搜索 ======================== */
const filterPropOptions = [
  { label: "身份证号", value: "idCard" },
  { label: "姓名", value: "patientName" },
  { label: "住院号", value: "inpNo" },
  { label: "患者编号", value: "patientId" },
  { label: "病案号", value: "mrNo" }
]

// 初始化搜索
const changeSelectValue = () => {
  state.complexInput = ""
}

// 搜索病案
const search = (patientFilterProp, patientFilterValue, idNumber = "") => {
  if (!patientFilterValue) return
  resetData()
  state.loading = true
  // getRecordListApi({
  //   filters: `${patientFilterProp.value}=${patientFilterValue.value},status in(ARCHIVED_YET,SHELVES_YET)`,
  //   offset: 0,
  //   limit: 100
  // })
  getPrintPatientInfoApi({
    [patientFilterProp]: patientFilterValue
  })
    .then(res => {
      state.patientsList = res.data.data.filter(item => item.sealingStatus === 0 || item.sealingStatus === 1)
      state.loading = false
      // 默认展开请求列表的第一个数据
      if (state.patientsList.length) {
        formData.inpNo = state.patientsList[0].inpNo
        formData.idNumber = idNumber
        getRecordData(formData.inpNo, true)
      } else {
        Message.warning("查询结果为空")
      }
    })
    .catch(err => {
      Message.error(extractErrorMsg(err, "查询病案列表失败"))
      state.loading = false
    })
}

/* ======================== 左侧病案列表相关数据及方法 ======================== */

// 加载pdf
const handlePdfClick = node => {
  if (node.type === "FILE") state.pdfSrc = "/api" + node?.contentFilePath
}

// 总打印页数
const totalPrintPages = ref(0)
// 支付金额
const orderAmount = ref(0)

const checkNode = debounce((node, checkedData) => {
  const checkedFiles = checkedData.filter(file => file.type === "FILE")
  checkAll.value = checkedFiles.length === state.allFiles.length
  totalPrintPages.value = checkedData
    .filter(item => item.type === "FILE")
    .reduce((totalPages, next) => {
      return totalPages + next.filePage
    }, 0)
  formData.documentLists = checkedData.filter(item => item.type === "FILE").map(item => item.wsid)
  calculateOrderAmount(formData.duplicateNum)
  //构建push到签字版数据
  bags.value = [
    {
      outHospitalDate: state.baseInfo.outHospitalDatetime,
      outHospitalDeptName: state.baseInfo.outHospitalDeptName,
      emrClassList: []
    }
  ]
  bags.value[0].emrClassList = Object.values(
    state.allFiles.reduce((acc, item) => {
      const code = item.mrClassCode
      if (!acc[code]) {
        acc[code] = {
          emrClassCode: code,
          emrClassName: item.mrClassName,
          totalPages: 0,
          isSelected: checkedData.some(data => data?.mrClassCode === item?.mrClassCode && data.type === "FILE")
        }
      }
      if (checkedData.some(data => data?.wsid === item?.wsid && data.type === "FILE")) {
        acc[code].totalPages += item?.filePage
      }
      return acc
    }, {})
  )
  if (toClientId.value) submit()
}, 300)

// 计算支付金额
const calculateOrderAmount = val => {
  if (val < 1) {
    return (formData.duplicateNum = 1)
  } else formData.duplicateNum = Math.ceil(val)
  orderAmount.value = Number(((formData.duplicateNum * totalPrintPages.value * printAmount.value) / 100).toFixed(2))
}

const activeRecord = reactive({
  treeInfo: {} as Record<string, any>, // 左侧tree需要的原始数据
  lastInpNo: "",
  secrecyGrade: "", //
  secrecyScope: "" //0 整体保密，用 secrecyGrade 判断整体是否可选;1 自定义保密，用treeInfo中的文件的secrecyGrade判断是否可选
})

// 总打印页数、支付金额、已选择打印文件重置
const resetData = () => {
  totalPrintPages.value = 0
  orderAmount.value = 0
  state.checkedKeys = []
  // state.pdfSrc = "/api/"
  state.baseInfo = {}
  for (const key in formData) {
    formData[key] = ""
    if (key === "documentLists") {
      formData[key] = []
    } else if (key === "duplicateNum") {
      formData[key] = 0
    }
  }
  for (const key in printMail) {
    printMail[key] = ""
  }
  mailAddress.value = ""
  formData.relationship = RelationshipTypeEnum.OWN
  formData.papersType = "ID_CARD"
}

// 点击左侧重新请求病案数据
const getRecordData = async (val, isSearch = false) => {
  if (val) formData.documentBagWsid = state.patientsList.find(item => item.inpNo === val).wsid
  if (!isSearch) {
    if (!val || val === activeRecord.lastInpNo) return
    activeRecord.lastInpNo = val
  }
  const params = { inpNo: val }
  state.loading = true
  // 重新点击时重置全选按钮
  checkAll.value = false
  await getRecordViewData(params)
    .then(res => {
      if (res.data.code !== "100100000") {
        state.loading = false
        return
      }
      const recordDetail = res.data.data
      state.baseInfo = recordDetail.baseInfo || {}
      formData.patientName = state.baseInfo.patientName
      // 如果选择身份证，自动填充身份证
      if (formData.papersType === "ID_CARD") {
        formData.idNumber = state.baseInfo.idCard
      }
      // 如果和申请人关系是本人，自动填充申请人相关信息
      if (formData.relationship === RelationshipTypeEnum.OWN) {
        formData.agentName = state.baseInfo.patientName
        formData.agentIdCard = state.baseInfo.idCard
      }
      state.treeInfo = formatRecordTree(recordDetail.treeInfo)
      // 设置初始pdf
      const pdfNodeList = getFileNodeList(state.treeInfo)
      handlePdfClick(pdfNodeList[0])
      nextTick(() => commonTreeRef.value[0]?.setCurrentKey(pdfNodeList[0]?.wsid))
      activeRecord.secrecyGrade = state.baseInfo.secrecyGrade as string
      activeRecord.secrecyScope = state.baseInfo.secrecyScope as string
    })
    .catch(err => {
      Message.error(extractErrorMsg(err, "操作失败"))
    })
    .finally(() => {
      state.loading = false
    })
  handleCheckFile(state.treeInfo)
  // 获取所有file,经过处理后的treeInfo
  state.allFiles = flattenTree(state.treeInfo).filter(file => file.type === "FILE")
  checkAllDisable.value = state.allFiles.some(file => file.disable)
  if (formData.printTypeCode && formData.printTypeCode !== "99") {
    getPrintFileByPrintType()
  }

  refreshTraceData()
}

/**
 *
 * @param files 当前记录所有的文件
 * @param document 选择打印类型后接口返回的文件，默认勾选
 */
const handleCheckFile = (files: Array<Record<string, any>>, document?: Record<string, any>) => {
  files.forEach(item => {
    if (item.type === "FILE") {
      if (activeRecord.secrecyScope === "0") {
        item.disable = printSecrecyGrade.value.indexOf(activeRecord.secrecyGrade) === -1
        if (activeRecord.secrecyGrade === "99") {
          item.disable = false
        }
      } else {
        item.disable = printSecrecyGrade.value.indexOf(item.secrecyGrade) === -1
        if (item.secrecyGrade === "99") {
          item.disable = false
        }
      }
      // 如果选择了打印类型，获取可勾选文件，计算页数
      if (document) {
        if (!item.disable && item.wsid === document.wsid) {
          state.checkedKeys.push(document.wsid)
          totalPrintPages.value += document.filePage
        }
      }
    } else if (item.children.length) {
      handleCheckFile(item.children, document)
    }
  })
  // 默认选中的病案文件wsid集合
  formData.documentLists = state.checkedKeys
}

const checkAllNodes = debounce((val, index) => {
  if (val) {
    handleCheckFile(state.treeInfo)
  } else {
    state.checkedKeys = []
    formData.documentLists = state.checkedKeys
    totalPrintPages.value = 0
  }
  // 选中的病案文件wsid集合
  commonTreeRef.value[index]?.checkAllNodes(val)
  if (toClientId.value) submit()
}, 300)

// 获取选择的打印类型中可打印的病案文件
const getPrintFileByPrintType = async () => {
  await getPrintFile({
    documentBagWsids: [formData.documentBagWsid],
    schemeWsid: formData.printTypeCode
  })
    .then(res => {
      if (res.data.data) {
        res.data.data.documentList.forEach(document => {
          handleCheckFile(state.treeInfo, document)
        })
      }
    })
    .catch(err => {
      console.log(`output->err`, err)
    })
}

/* ======================== 示踪 ======================== */
const traceData = ref<Array<Record<string, any>>>([])

function refreshTraceData() {
  getApplyPrintRecordsApi(formData.inpNo).then(res => {
    traceData.value = res.data.data || []
  })
}

/* ======================== SSE相关 ======================== */
// 已选择的终端
const toClientId = ref("")

//当前的SSE 的ID
const formClientId = ref("")

const clientList = ref<Array<Record<string, any>>>([])
const addressList = ref<Array<Record<string, any>>>([])

const fetchMailAddress = debounce(async () => {
  addressList.value = (await getPrintAddressApi(formData.idNumber))?.data?.data
}, 300)

const submitLoading = ref(false)
const allowedSubmit = ref(false)
const bags = ref<Array<Record<string, any>>>([])

const submit = async () => {
  state.loading = true
  try {
    await pushSelectDocApi({
      formClientId: formClientId.value,
      toClientId: toClientId.value,
      idCard: formData.idNumber,
      name: formData.patientName,
      bags: bags.value
    })
    state.loading = false
  } catch (error: any) {
    console.log(`output->error`, error)
    state.loading = false
    toastError(error, "提交失败")
  }
}

const fetchClient = debounce(async () => {
  toClientId.value = ""
  clientList.value = (await getClientListApi("SIGN_PAD"))?.data?.data
}, 1000)

/* ====================================== ocr识别 ====================================== */
function updateOcrInfo(ocrData) {
  const { activeInfoNode, data } = ocrData
  // 提取姓名
  const nameRegex = /姓名\s*([\u4e00-\u9fa5]+)/
  const nameMatch = data.match(nameRegex)
  const name = nameMatch ? nameMatch[1] : ""

  // 提取身份证号码
  const idCardRegex = /(\d{17}[\dxX]|\d{15})/
  const idCardMatch = data.match(idCardRegex)
  const idCard = idCardMatch ? idCardMatch[1] : ""

  // 患者信息和搜索
  if (activeInfoNode.data.prop === "patientIdCardFront") {
    // 身份证搜索自动填入身份证
    state.complexSelect = "idCard"
    state.complexInput = idCard
    search(state.complexSelect, state.complexInput, idCard)
  }

  // 代理人信息
  // 非本人
  if (formData.relationship !== RelationshipTypeEnum.OWN) {
    if (
      activeInfoNode.data.prop === "agentIdCardFront" ||
      activeInfoNode.data.prop === "guardianIdCardFront" ||
      activeInfoNode.data.prop === "heirIdCardFront" ||
      activeInfoNode.data.prop === "heirAgentIdCardFront"
    ) {
      formData.agentName = name
      formData.agentIdCard = idCard
    }
  }
  // 本人
  else if (formData.relationship === RelationshipTypeEnum.OWN) {
    if (activeInfoNode.data.prop === "patientIdCardFront") {
      formData.agentName = name
      formData.agentIdCard = idCard
    }
  }
}
</script>

<style lang="less" scoped>
#print-application {
  display: flex;
  justify-content: space-between;
  height: 100%;
  min-width: 1110px;
  flex-wrap: wrap;

  .header {
    width: 100%;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    border: 1px solid rgba(10, 22, 51, 0.1);
    border-bottom: none;
    .tab-wrapper {
      // width: 30%;
    }
  }
}

.print-preview-container {
  flex: 1;
  min-width: 0px;
  display: flex;
  flex-direction: column;
  // margin-right: 16px;
  border-radius: 4px;
  border: 1px solid rgba(10, 22, 51, 0.1);
  border-right: none;
  height: calc(100% - 55px);
  .print-preview-tab {
    background: #fff;
    padding: 16px;
    border-bottom: 1px solid rgba(10, 22, 51, 0.1);
  }
  .print-preview-wrapper {
    flex: 1;
    min-height: 0px;
    display: flex;
    .view-left {
      width: 256px;
      flex-shrink: 0;
      height: 100%;
      overflow-y: auto;
      background-color: white;
      border-right: 1px solid rgba(10, 22, 51, 0.1);
      :deep(.el-collapse) {
        .el-collapse-item__header {
          padding: 16px;
        }
        .el-collapse-item__wrap {
          padding: 0px 16px;
        }
      }
    }
    .view-middle {
      overflow-x: auto;
      min-width: 400px;
      flex: 1;
      box-sizing: border-box;
    }
  }
}

.view-right {
  overflow: hidden;
  flex-shrink: 0;
  width: 362px;
  height: calc(100% - 55px);
  background-color: white;
  border-radius: 4px;
  border: 1px solid rgba(10, 22, 51, 0.1);
  .tab-wrapper {
    padding: 0 20px;
    display: flex;
  }
  .title {
    position: relative;
    height: 56px;
    padding: 0 15px;
    line-height: 56px;
    border-bottom: 1px solid rgb(225 226 230);
    width: 100%;

    &::before {
      position: absolute;
      top: 20px;
      left: 0;
      width: 4px;
      height: 18px;
      background-color: rgb(44 104 255);
      border: 2px;
      border-radius: 0 4px 4px 0;
      content: "";
    }

    .custom-select {
      width: 100%;

      :deep(.el-input__wrapper) {
        padding: 0;
        box-shadow: none !important;

        .el-input__inner {
          font-size: 16px;
          color: rgb(3 8 20 / 85%);
          font-weight: 700;
          font-style: normal;
        }
      }
    }
  }

  .content {
    padding: 16px;
    height: calc(100% - 55px);
    overflow-y: auto;
    box-sizing: border-box;
  }

  .form-container {
    // height: calc(100% - 285px);
    height: calc(100% - 110px);
    overflow-y: scroll;
    padding-right: 10px;
  }

  .trace-timeline {
    :deep(.el-timeline-item__node) {
      width: 6px;
      height: 6px;
      background: #74c041;
      border-color: #74c041;
      left: 2px;
      &:before {
        /* 创建伪元素作为边框 */
        content: " ";
        position: absolute;
        top: -3px;
        left: -3px;
        margin: auto;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: rgba(116, 192, 65, 0.2);
      }
      &:after {
        /* 创建伪元素作为边框 */
        content: " ";
        position: absolute;
        top: -6px;
        left: -6px;
        margin: auto;
        width: 18px;
        height: 18px;
        border-radius: 50%;
        background: rgba(116, 192, 65, 0.1);
      }
    }
    :deep(.el-timeline-item__tail) {
      border-width: 1px;
      height: 75%;
      top: 6px;
      bottom: 0px;
      margin: auto;
      border-color: #74c041;
    }
    .time-header {
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      color: rgba(10, 22, 51, 0.4);
    }
    .info-wrapper {
      font-size: 14px;
      color: #0a1633;
      white-space: pre;
      margin: 10px 0px;
    }
  }
}

.form-group-header {
  margin-bottom: 10px;
}

.search-container {
  padding-right: 10px;
  margin-bottom: 10px;
  box-sizing: border-box;
}

.tree-data {
  width: 100%;
}

.confirm-button {
  float: right;
  right: 10px;
  position: relative;
  margin-top: 15px;
}

.blank {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.el-empty {
  height: 100%;
}

.el-form--label-top .el-form-item {
  padding-left: 20px;
}
:deep(.el-input-group) {
  width: 100% !important;
}
:deep(.el-collapse-item__header) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
