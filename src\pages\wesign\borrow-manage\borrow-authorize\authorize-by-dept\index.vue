<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <el-form-item label="科室代码">
          <el-input v-model="searchFormState.deptCode" style="width: 300px" clearable />
        </el-form-item>
        <el-form-item label="科室名称">
          <el-select v-model="searchFormState.deptName" style="width: 300px">
            <el-option
              v-for="option in globalOptionsStore.departmentOptions"
              :key="option.value"
              :label="option.label"
              :value="option.label"
            />
          </el-select>
        </el-form-item>

        <CommonSelectFormItem
          v-model:model-value="searchFormState.secrecyGrade"
          label="保密等级"
          :options="secrecyLevelOptions"
        />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="commonTableRef"
        table-id="deptAuthorizationTable"
        :table-columns="deptAuthorizationTableColumns"
        :request-params="searchParams"
        :request-api="getDeptAuthorizationListApi"
        :data-callback="dataCallback"
      >
        <template #operationTime="{ row }">{{ formatDatetime(row.operationTime) }}</template>
        <template #operation="{ row }">
          <TableButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Authorize)"
            @click="showAuthorizedDialog(row)"
          >
            授权
          </TableButton>
          <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.View)" @click="showAuthorizedDetail(row)">
            详情
          </TableButton>
        </template>
      </CommonTable>
    </template>

    <AuthorizedDialog
      ref="dialogRef"
      authorize-type="department"
      :dept-wsid="authorizeState.deptWsid"
      :table-ref="commonTableRef"
    />

    <el-drawer v-model="detailState.showDrawer" title="详情" direction="rtl" :size="370" destroy-on-close>
      <CommonTable
        style="height: auto"
        :table-columns="deptAuthorizationUsersTableColumns"
        :request-api="getBorrowDeptAuthorizationUsersApi"
        :request-params="detailState.selectedRow"
        :data-callback="usersDataCallback"
        :pagination="false"
      >
        <template #header>
          <div class="drawer-item-wrapper">
            <div class="drawer-item-title">已授权用户({{ detailState.authorizedUsersCount }}):</div>
          </div>
        </template>
      </CommonTable>

      <CommonTable
        style="height: auto"
        :table-columns="deptAuthorizationDeptsTableColumns"
        :request-api="getBorrowDeptAuthorizationDeptsApi"
        :request-params="detailState.selectedRow"
        :data-callback="deptsDataCallback"
        :pagination="false"
      >
        <template #header>
          <div class="drawer-item-wrapper" style="margin-top: 20px">
            <div class="drawer-item-title">已授权科室({{ detailState.authorizedDeptsCount }}):</div>
          </div>
        </template>
      </CommonTable>
    </el-drawer>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from "vue"
import { CommonSelectFormItem, TableButton, PageContainer, SearchContainer, CommonTable } from "@/base-components"
import { TabsRouter } from "@/page-components"

import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { getSecreciesList } from "@/interfaces"
import { useGlobalOptionsStore, useUserStore } from "@/stores"
import { formatDatetime } from "@/utils"
import AuthorizedDialog from "../components/AuthorizedDialog.vue"
import { tabsRouterList, menuId } from "../config"
import {
  getDeptAuthorizationListApi,
  getBorrowDeptAuthorizationDeptsApi,
  getBorrowDeptAuthorizationUsersApi
} from "../interface"
import {
  deptAuthorizationTableColumns,
  deptAuthorizationUsersTableColumns,
  deptAuthorizationDeptsTableColumns
} from "./config"
import type { BaseOptionItem } from "@/types"

const { hasOperationPermission } = useUserStore()

const commonTableRef = ref()
const globalOptionsStore = useGlobalOptionsStore()

const searchFormState = reactive({
  secrecyGrade: "",
  deptCode: "",
  deptName: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

const dataCallback = (data: Array<Record<string, any>>) => {
  data.forEach(row => {
    row.inHospitalDatetime = formatDatetime(row.inHospitalDatetime)
    row.outHospitalDatetime = formatDatetime(row.outHospitalDatetime)
    row.secrecyGradeEnumName = row.secrecyGradeEnumName ? getSecrecyLevelText(row.secrecyGradeEnumName) : ""
  })
  return data
}

const secrecyLevelOptions = ref<Array<BaseOptionItem>>([])
onMounted(async () => {
  secrecyLevelOptions.value = (await getSecreciesList()).data.data.map(item => {
    return { label: item.secrecyName, value: item.secrecyValue }
  })
})

// 获取保密等级
const getSecrecyLevelText = secrecyEnum => {
  return secrecyLevelOptions.value.find(option => option.value === secrecyEnum)?.label || ""
}

/*==================== 授权相关 =====================*/
const dialogRef = ref()

const authorizeState = reactive({
  deptWsid: ""
})

const showAuthorizedDialog = row => {
  authorizeState.deptWsid = row.deptWsid
  dialogRef.value.open()
}

/*==================== 详情业务 =====================*/
const detailState = reactive({
  showDrawer: false,
  selectedRow: {},
  authorizedUsersCount: 0, // 已授权用户数量
  authorizedDeptsCount: 0 // 已授权科室数量
})

const showAuthorizedDetail = row => {
  detailState.selectedRow = row
  detailState.showDrawer = true
}

// 借阅给用户的列表格式化
const usersDataCallback = (data: Array<Record<string, any>>) => {
  // 格式化后的table数据
  const usersList: { level: string; users: string }[] = []
  data.forEach(item => {
    const secrecyLevel = getSecrecyLevelText(item.secrecyGrade)
    const usersIndex = usersList.findIndex(users => users.level === secrecyLevel)
    if (usersIndex >= 0) {
      usersList[usersIndex].users += `${item.realName}（${item.jobId}）`
    } else {
      usersList.push({ level: secrecyLevel, users: `${item.realName}（${item.jobId}）` })
    }
  })
  detailState.authorizedUsersCount = data.length
  return usersList
}

// 借阅给科室的列表格式化
const deptsDataCallback = (data: Array<Record<string, any>>) => {
  // 格式化后的table数据
  const deptsList: { level: string; depts: string }[] = []
  data.forEach(item => {
    const secrecyLevel = getSecrecyLevelText(item.secrecyGrade)
    const deptIndex = deptsList.findIndex(dept => dept.level === secrecyLevel)
    if (deptIndex >= 0) {
      deptsList[deptIndex].depts += `${item.deptName}（${item.deptCode}）`
    } else {
      deptsList.push({ level: secrecyLevel, depts: `${item.deptName}（${item.deptCode}）` })
    }
  })
  detailState.authorizedDeptsCount = data.length
  return deptsList
}
</script>
