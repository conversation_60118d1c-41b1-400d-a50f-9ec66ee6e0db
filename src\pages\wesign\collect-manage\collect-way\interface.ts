import axios from "@/interfaces/axios-instance"
import { toastError } from "@/utils"

/**
 * @method PUT
 * @desc   采集方式-修改状态
 */
export function modifyIntegrations(obj) {
  const { targetWsid, status } = obj
  return axios({
    method: "put",
    url: `/api/collect/status/${targetWsid}/${status}`
  })
}

interface IDbConfig {
  name: string
  dataSourceWsid: string
  querySql: string
  charset: string
  resultDataType: string
  templateWsid?: string
}

interface IApiConfig {
  name: string // 采集方式名称
  url: string // 接口地址
  queryParams: string // 请求参数
  bodyParams: string // 请求体
  contentType: string
  method: string // 请求方式
  authorization: string // 认证信息
  resultType: string // 返回数据类型
  resultField: string // 取值路径
  charset: string // 编码格式
  resultDataType: string // 返回数据类型
  templateWsid: string // 文件合成wsid
  timeout: string // 超时时间
}

// 添加 FTP 配置接口定义
interface IFtpConfig {
  name: string
  parseEl: string
  timeout: string
  ftpSourceWsid?: string
}

export interface IntegrationsData {
  systemWsid: string
  dataType: string
  mrClassCode: string
  cron: string
  triggerType: string
  scene: string
  extractType?: string
  sourceCode?: string
  integrationWsid?: string
  fixedTimeFieldKey?: string
  incrementFieldKey?: string
  incrementFieldType?: string
  incrementLastValue?: string
  incrementDelta?: string
  fixedTimeRange?: string
  dbConfig?: IDbConfig
  ftpConfig?: IFtpConfig
  apiConfig?: IApiConfig
}

/**
 * @method POST
 * @desc   采集方式-新增
 */
export function addIntegrations(data: IntegrationsData) {
  return axios({
    method: "post",
    url: `/api/collect/systems/${data.systemWsid}/integrations`,
    data: data
  })
}

/**
 * @method PUT
 * @desc   采集方式-修改
 */
export function editIntegrations(data: IntegrationsData) {
  // const {
  //   systemWsid,
  //   integrationWsid,
  //   dataType,
  //   dbConfig,
  //   ftpConfig,
  //   apiConfig,
  //   cron,
  //   resultDataType,
  //   templateWsid,
  //   charset,
  //   needConfirmArchive
  // } = obj
  // let { mrClassCode = "" } = obj
  // const selectMrType = ["MR_CONTENT", "MR_BASE_INFO", "MR_DISCHARGE_DIAGNOSIS", "MR_OPERATION", "MR_FEE_INFO"]
  // if (selectMrType.indexOf(dataType) === -1) mrClassCode = ""
  return axios({
    method: "put",
    url: `/api/collect/systems/${data.systemWsid}/integrations/${data.integrationWsid}`,
    data: data
  })
}

/**
 * @method GET
 * @desc   采集方式-获取字段列表
 */
export function getFieldDicts(obj) {
  const { filters } = obj
  return axios({
    method: "get",
    url: "/api/collect/field-dicts",
    params: {
      filters
    }
  })
}

/**
 * @method GET
 * @desc   采集方式-获取所有系统厂商
 */
export function getCollectSystemList() {
  return axios
    .request<IResponseData>({
      method: "get",
      url: "/api/collect/systems/list"
    })
    .then(res => res.data?.data ?? [])
    .catch(err => {
      toastError(err, "获取厂商系统列表失败")
      return []
    })
}

/**
 * @method POST
 * @desc   采集方式-执行采集任务
 */
export function triggerJobApi(targetWsid: string) {
  return axios({
    method: "post",
    url: `/api/collect/${targetWsid}/triggerJob`
  })
}

/**
 * @method GET
 * @desc   采集方式-数据库-db数据源分页列表
 */
export function getDbList(obj) {
  const { fields, filters, sorts, offset, limit } = obj
  return axios({
    method: "get",
    url: "/api/collect/query-integrations/db",
    params: {
      fields,
      filters,
      sorts,
      offset,
      limit
    }
  })
}

/**
 * @method GET
 * @desc   采集方式-获取所有数据源列表
 */
export function getAllDataSources() {
  return axios
    .request<IResponseData>({
      method: "get",
      url: "/api/collect/data-sources/list"
    })
    .then(res => res.data?.data ?? [])
}

/**
 * @method POST
 * @desc   采集方式-数据库-测试数据源
 */
export function dbTestSelectOne(obj) {
  const { dataSourceWsid, querySql } = obj
  return axios({
    method: "post",
    url: "/api/collect/systems/db/testSelectOne",
    data: {
      dataSourceWsid,
      querySql
    }
  })
}

/**
 * @method GET
 * @desc   采集方式-ftp数据源分页列表
 */
export function getFTPList(obj) {
  const { filters, offset, limit } = obj
  return axios({
    method: "get",
    url: "/api/collect/query-integrations/ftp",
    params: {
      filters,
      offset,
      limit
    }
  })
}

/**
 * @method GET
 * @desc   采集方式-接口配置-接口数据源列表
 */
export function getInterfaceList(obj) {
  const { fields, filters, sorts, offset, limit } = obj
  return axios({
    method: "get",
    url: "/api/collect/query-integrations/interface",
    params: {
      fields,
      filters,
      sorts,
      offset,
      limit
    }
  })
}

interface ICollectRecordParams {
  filters: string
  dataType: string
  scene: string
}

/**
 * @method GET
 * @desc   采集方式-采集记录
 */
export function getCollectRecordList(params: ICollectRecordParams) {
  return axios({
    method: "get",
    url: "/api/collect/tasks/es-query",
    params: params
  })
}

// 获取ftp数据源列表
export const getFtpDataSourceListApi = () => {
  return axios
    .request<IResponseData>({
      method: "get",
      url: "/api/collect/data-ftp-sources/list"
    })
    .then(res => res.data?.data ?? [])
    .catch(err => {
      toastError(err, "获取FTP数据源列表失败")
      return []
    })
}
