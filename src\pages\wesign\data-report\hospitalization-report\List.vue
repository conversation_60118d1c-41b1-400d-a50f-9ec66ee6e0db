<template>
  <PageContainer separate>
    <!-- 头部搜素 -->
    <template #search>
      <TabsRouter :tabs-config="hospitalizationTabsConfig" />
      <SearchContainer @reset-btn-click="handleReset" @query-btn-click="handleQuery">
        <CommonInputFormItem v-model="searchFormState.mrNos" label="病案号" placeholder="多个病案号“，”分割" />
        <CommonInputFormItem v-model="searchFormState.names" label="姓名" placeholder="多个患者姓名“，”分割" />
        <DepartmentFormItem v-model="searchFormState.outHospitalDeptWsid" multiple collapse-tags label="出院科室" />
        <el-form-item label="出院时间">
          <el-date-picker
            v-model="searchFormState.outHospitalDatetime"
            type="daterange"
            unlink-panels
            :clearable="false"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :disabled-date="disabledDate"
            value-format="x"
            @calendar-change="handleCalendarChange"
          ></el-date-picker>
        </el-form-item>
        <CommonSelectFormItem v-model="searchFormState.reviewStatus" label="审核状态" :options="reviewStatusOptions" />
      </SearchContainer>
    </template>

    <!-- hqms上报表格数据 -->
    <template #table>
      <CommonTable
        ref="tableRef"
        table-id="hospitalizationDataReportTableIdent"
        :table-columns="hospitalizationDataReportTableColumns"
        :request-api="getInHospitalListApi"
        :request-params="actualParams"
        :data-callback="dataCallback"
      >
        <template #header>
          <div class="flex-start">
            <BatchOperationButton
              type="primary"
              :plain="false"
              :disabled="!auditableRows.length"
              tooltip="请至少选择一条待审核的数据"
              @click="handleAudit"
            >
              审核
            </BatchOperationButton>
            <ManualExportButton
              ref="manualExportRef"
              button-text="导出csv"
              :selected-count="selectedRows.length"
              :total="tableRef?.tableState?.total"
              :max="10000"
              :tooltip-disabled="true"
              :error-message="exportErrorMessage"
              @export="handleExport('selected')"
              @export-all="handleExport('all')"
            />
            <div style="margin-left: 12px"></div>
            <ManualExportButton
              ref="manualExportRef"
              button-text="导出至前置机"
              :selected-count="selectedRows.length"
              :total="tableRef?.tableState?.total"
              :max="10000"
              :show-icon="false"
              :error-message="exportErrorMessage"
              @export="handleExportToRemote('selected')"
              @export-all="handleExportToRemote('all')"
            />
            <div style="margin-left: 12px"></div>
            <BatchOperationButton @click="autoExportConfigDialogRef?.show">自动导出配置</BatchOperationButton>
          </div>
        </template>

        <template #reviewStatus="{ row }">
          <el-tag :type="getReviewsStatusTagType(row.reviewStatus)">
            {{ reviewStatusOptions.find(item => item.value === row.reviewStatus)?.label }}
          </el-tag>
        </template>
        <template #operation="{ row }">
          <TableButton @click="toDetail(row)">详情</TableButton>
        </template>
      </CommonTable>
    </template>

    <AutoExportConfigDialog ref="autoExportConfigDialogRef" />
  </PageContainer>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, watch } from "vue"
import { useRouter } from "vue-router"
import { type DateModelType } from "element-plus"
import {
  PageContainer,
  SearchContainer,
  CommonInputFormItem,
  CommonSelectFormItem,
  DepartmentFormItem,
  CommonTable,
  ManualExportButton,
  BatchOperationButton,
  TableButton
} from "@/base-components"
import { TabsRouter } from "@/page-components"
import useTableSearch from "@/hooks/useTableSearch_v2"
import { useSystemStore, useUserStore } from "@/stores"
import { downloadFile, extractErrorMsg, formatDatetime, Message, SystemAlert, toastError } from "@/utils"
import {
  hospitalizationTabsConfig,
  reviewStatusOptions,
  hospitalizationDataReportTableColumns,
  getReviewsStatusTagType
} from "../config"
import {
  auditInHospitalApi,
  exportToCvsApi,
  exportInHospitalDataToRemoteApi,
  getInHospitalExportLimitApi,
  getInHospitalListApi
} from "../interface"
import AutoExportConfigDialog from "./components/AutoExportConfigDialog.vue"

const systemStore = useSystemStore()
const router = useRouter()

const { hasOperationPermission } = useUserStore()

/*================================= 搜索表单 =================================*/

// 默认三个月前当日0点
const defaultTimeStart = new Date(new Date().setHours(0, 0, 0, 0) - 1 * 30 * 24 * 3600 * 1000).getTime()
const defaultTimeEnd = new Date(new Date().setHours(23, 59, 59, 999)).getTime()

const formInitialData = {
  mrNos: "", // 住院号
  names: "", // 姓名
  outHospitalDeptWsid: [] as string[], // 出院科室
  outHospitalDatetime: [defaultTimeStart, defaultTimeEnd] as [DateModelType, DateModelType], // 出院时间
  reviewStatus: "", // 审核状态
  exportType: "EXPORT_HOME_PAGE_HQMS"
}

const { searchFormState, searchParams, handleReset, handleQuery } = useTableSearch({
  filtersInitialData: formInitialData
})

const actualParams = ref() // 实际的请求参数

// 监听搜索参数变化，更新实际请求参数
watch(
  () => searchParams,
  val => {
    console.log(searchFormState.outHospitalDeptWsid)
    actualParams.value = {
      ...searchFormState,
      outHospitalDeptWsid: searchFormState.outHospitalDeptWsid?.length
        ? searchFormState.outHospitalDeptWsid.join(",")
        : "",
      outHospitalDatetime: searchFormState.outHospitalDatetime || "",
      outHospitalTimeStart: searchFormState.outHospitalDatetime?.[0] || "",
      outHospitalTimeEnd: new Date(searchFormState.outHospitalDatetime?.[1]).setHours(23, 59, 59, 999) || "",
      timestamp: val.timestamp
    }
  },
  { immediate: true, deep: true }
)

/* ======================== 时间筛选限制 ======================== */

const startDate = ref()
const days = 31 * 24 * 3600 * 1000

const handleCalendarChange = date => {
  const [minDate, maxDate] = date
  if (minDate && !maxDate) {
    startDate.value = minDate // 记录选中的首个日期
  } else {
    startDate.value = null
  }
}

const disabledDate = time => {
  // 如果已经选择了开始日期，则禁用所有超出30天范围的日期
  if (startDate.value) {
    return time.getTime() < startDate.value?.getTime() - days || time.getTime() > startDate.value?.getTime() + days
  } else {
    return false
  }
}

/*================================= 表格 =================================*/

const tableRef = ref<InstanceType<typeof CommonTable>>()
const selectedRows = computed(() => tableRef.value?.tableState?.selectedRows || [])

const dataCallback = (data: any) => {
  data.forEach(row => {
    row.inHospitalDatetime = formatDatetime(row.inHospitalDatetime)
    row.outHospitalDatetime = formatDatetime(row.outHospitalDatetime)
  })
  return data
}

const toDetail = (row: any) => {
  router.push({
    path: "/catalog-preview",
    query: {
      appScenario: "HOME_PAGE_HQMS_DETAIL",
      businessDataWsid: row.inpNo,
      returnUrl: "/data-report/hospitalization/list",
      actionType: "edit"
    }
  })
}

/*================================= 审核 =================================*/

const auditableRows = computed(() => selectedRows.value?.filter(row => row.reviewStatus === "REVIEW_WAIT") || []) // 可审核的所选行

// 审核
const handleAudit = async () => {
  try {
    systemStore.showLoading("审核中")
    const wsids = auditableRows.value.map(row => row.inpNo)
    await auditInHospitalApi(wsids)
    systemStore.hideLoading()
    Message.success("审核成功")
    tableRef.value?.refreshTableData()
  } catch (error: any) {
    console.log(error)
    systemStore.hideLoading()
    toastError(error, "操作失败")
  }
}

/* ======================== 导出 ======================== */

const manualExportRef = ref<InstanceType<typeof ManualExportButton>>()
const exportErrorMessage = ref("")
let exportLimit = 10000 // 导出数据的上限：如果超出上限则调用接口完成显示提示语，否则执行下载到本地

onMounted(async () => {
  exportLimit = (await getInHospitalExportLimitApi()).data.data
})

const checkExportable = (type: "all" | "selected") => {
  if (!searchFormState.outHospitalDatetime[1] || !searchFormState.outHospitalDatetime[0]) {
    Message.error("请选择就诊时间")
    return false
  }
  if (type === "all") {
    if (
      searchFormState.outHospitalDatetime[1] &&
      searchFormState.outHospitalDatetime[0] &&
      // @ts-ignore
      Math.abs(searchFormState.outHospitalDatetime[1] - searchFormState.outHospitalDatetime[0]) > 31 * 24 * 3600 * 1000
    ) {
      Message.error("导出时间范围不能超过30天")
      return false
    } else {
      return true
    }
  }
  return true
}

const handleExport = async (type: "all" | "selected") => {
  if (!checkExportable(type)) return
  const totalExportCount = type === "all" ? tableRef.value?.tableState?.total || 0 : selectedRows.value.length // 当前导出总数
  const registerNos = type === "selected" ? selectedRows.value.map(row => row.inpNo) : undefined
  // 如果导出数量大于后台配置的导出上限，调用接口后就提示用户稍后查看（异步任务）
  if (totalExportCount > exportLimit) {
    try {
      systemStore.showLoading()
      await exportToCvsApi({ inpNos: registerNos, ...actualParams.value })
      systemStore.hideLoading()
      SystemAlert("导出任务处理中，请您前往导出记录查看任务进度", "success")
    } catch (error: any) {
      systemStore.hideLoading()
      toastError(error, "导出失败")
    }
  }
  // 否则下载到本地
  else {
    try {
      manualExportRef.value?.handleExportStart()
      const res = await exportToCvsApi({ registerNos: registerNos, ...searchParams })
      const fileName = res.headers["content-disposition"].split("filename")[1].split("=")[1].replace(/"/g, "")
      let fileType = fileName.split(".")[1].toLowerCase()
      if (fileType === "zip") fileType = "application/zip"
      downloadFile({ fileData: res.data, fileType: fileType, fileName })
      manualExportRef.value?.handleExportDone()
      exportErrorMessage.value = ""
    } catch (error: any) {
      let errorMessage = ""
      if (error?.response?.data instanceof Blob) {
        const blob = new Blob([error.response.data])
        const blobJson = JSON.parse(await blob.text())
        errorMessage = blobJson.userMessage
      } else {
        errorMessage = extractErrorMsg(error)
      }
      manualExportRef.value?.handleExportError()
      exportErrorMessage.value = errorMessage
      Message.error(errorMessage || "导出失败")
    }
  }
}

/* ======================== 导出到前置机 ======================== */

const exportRemoteLoading = ref(false)

// 导出至远程目录
const handleExportToRemote = async (type: "all" | "selected") => {
  if (!checkExportable(type)) return
  try {
    exportRemoteLoading.value = true
    const inpNos = type === "selected" ? selectedRows.value.map(row => row.inpNo) : undefined
    await exportInHospitalDataToRemoteApi({ inpNos: inpNos, ...actualParams.value })
    exportRemoteLoading.value = false
    Message.success("导出至前置机成功")
  } catch (error: any) {
    exportRemoteLoading.value = false
    toastError(error, "导出执行失败")
  }
}

/*================================= 自动导出配置 =================================*/

const autoExportConfigDialogRef = ref<InstanceType<typeof AutoExportConfigDialog>>()
</script>

<style lang="less" scoped>
.hqms-table-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  padding-bottom: 10px;
  box-sizing: border-box;

  .header-actions {
    height: 32px;
    margin-bottom: 10px;
  }

  :deep(.el-table) {
    flex: 1;

    .el-scrollbar__view {
      height: 100%;
    }

    .el-table-fixed-column--left {
      background: #ecf5ff;
    }
  }

  :deep(.el-pagination) {
    align-self: flex-end;
    margin-top: 20px;
  }

  :deep(.column-err) {
    width: 100%;
    height: 100%;
    background: #ffb1bb;
    position: absolute;
    top: 0px;
    left: 0px;
    z-index: 1;
    text-align: center;
    line-height: 38px;
    cursor: pointer;
  }
}
</style>
