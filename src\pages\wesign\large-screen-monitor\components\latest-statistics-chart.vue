<template>
  <div class="latest-statistics-chart-container">
    <div class="chart-title"></div>
    <div class="chart-container">
      <v-chart class="latest-statistics-chart" :option="option" autoresize />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, provide, watch } from "vue"
import { BarChart } from "echarts/charts"
import { DatasetComponent, TooltipComponent, GridComponent, LegendComponent } from "echarts/components"
import { use } from "echarts/core"
import { CanvasRenderer } from "echarts/renderers"
import VChart, { THEME_KEY } from "vue-echarts"
import { BusinessStatistics, BusinessStatisticsEnum } from "../config"

/* ===================== 数据处理操作 ===================== */
interface LatestStatisticsProp {
  latestStatisticsData: Array<BusinessStatistics>
}

const props = defineProps<LatestStatisticsProp>()

watch(
  () => props.latestStatisticsData,
  () => {
    formatData(props.latestStatisticsData)
    formatDataSource()
  }
)

// x轴数据
const xAxisData = ref([] as any)
// 采集病案数
const collectNumData = ref([] as any)
// 出院人数
const dischargeNumData = ref([] as any)
// 归档数
const archiveNumData = ref([] as any)

// 柱状图 所需数据格式
const dataSetSource = ref([] as any)

function formatData(latestStatisticsData) {
  let tempCollectNumData: Array<number> = []
  let tempDischargeNumData: Array<number> = []
  let tempArchiveNumData: Array<number> = []
  let tempXAxisData: Array<any> = []
  latestStatisticsData.map(latestStatisticsItem => {
    // 采集病案数
    if (latestStatisticsItem.businessTypeStr === BusinessStatisticsEnum.COLLECT) {
      latestStatisticsItem.items.forEach(item => {
        tempCollectNumData.push(item.count)
      })
    }
    // 出院人数
    else if (latestStatisticsItem.businessTypeStr === BusinessStatisticsEnum.OUT_HOSPITAL) {
      latestStatisticsItem.items.forEach(item => {
        tempDischargeNumData.push(item.count)
      })
    }
    // 归档数
    else if (latestStatisticsItem.businessTypeStr === BusinessStatisticsEnum.ARCHIVE) {
      latestStatisticsItem.items.forEach(item => {
        tempArchiveNumData.push(item.count)
      })
    }

    if (latestStatisticsItem.items && tempXAxisData.length === 0) {
      latestStatisticsItem.items.forEach(item => {
        tempXAxisData.push(item.dateStr)
      })
    }
  })
  collectNumData.value = tempCollectNumData
  dischargeNumData.value = tempDischargeNumData
  archiveNumData.value = tempArchiveNumData
  xAxisData.value = tempXAxisData
}

// 转换成柱状图所需要的格式
function formatDataSource() {
  let tempDataSetSource: Array<any> = []
  xAxisData.value.forEach((item, index) => {
    let temp = [item, dischargeNumData.value[index], archiveNumData.value[index], collectNumData.value[index]]
    tempDataSetSource.push(temp)
  })
  tempDataSetSource.unshift(["number", "出院人数", "归档数", "采集病案数"])
  dataSetSource.value = tempDataSetSource
}
/* ===================== 表格配置项 ===================== */
use([DatasetComponent, TooltipComponent, GridComponent, LegendComponent, BarChart, CanvasRenderer])

provide(THEME_KEY, "dark")

const option = ref({
  legend: {
    x: "center",
    y: "bottom"
  },
  tooltip: {},
  dataset: {
    source: dataSetSource
  },
  xAxis: { type: "category" },
  yAxis: {},
  series: [
    { type: "bar", itemStyle: { color: "#106CED" } },
    { type: "bar", itemStyle: { color: "#37CE56" } },
    { type: "bar", itemStyle: { color: "#F2CA39" } }
  ]
})
</script>

<style lang="less" scoped>
.latest-statistics-chart-container {
  width: 38.6%;
  min-width: 628px;
  margin: 20px 0 20px 20px;
  .chart-title {
    width: 100%;
    height: 43px;
    background: url("@/assets/svg/monitor/recent-statistics.svg") no-repeat;
    background-size: 100%;
  }
  .chart-container {
    margin-top: 15px;
    .latest-statistics-chart {
      width: 100%;
      height: 440px;
    }
  }
}
</style>
