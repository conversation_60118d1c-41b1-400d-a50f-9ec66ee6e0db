<template>
  <div class="container">
    <div v-loading="state.loading" class="view-left">
      <MedicalRecordTree
        ref="recordTreeRef"
        can-collapse
        :base-info="state.baseInfo"
        :tree-info="state.treeInfo"
        @click-pdf="handlePdfClick"
      />
    </div>
    <div class="view-right">
      <!-- 顶部信息及按钮 -->
      <view class="tool-bar">
        <view class="document-name">{{ state.documentName }}</view>

        <view class="tool-bar-buttons">
          <el-button @click="toPrev">上一份文件</el-button>
          <el-button @click="toNext">下一份文件</el-button>
          <el-button type="success" @click="handlePass">签收通过</el-button>
          <el-button type="danger" @click="handleReject">签收不通过</el-button>
        </view>
      </view>

      <div class="pdf-container common-box-shadow">
        <PdfPreviewComponent :src="state.pdfSrc" :default-width="750" />
      </div>

      <div v-loading="state.loading" style="height: calc(100% - 60px)">
        <DocumentMeta :base-info-data="state.firstPageFields" :file-wsid="state.targetNode.fileWsid" />
      </div>
    </div>
  </div>

  <!-- 备注 -->
  <DialogContainer v-model:visible="dialogVisible" title="提交备注" :width="550" :confirm-callback="confirmReject">
    <el-form ref="rejectFormRef" :model="rejectState" :rules="reasonFormRules">
      <el-form-item label="不通过原因" prop="reason">
        <el-input v-model="rejectState.reason" type="textarea" maxlength="300" :rows="5" />
      </el-form-item>
    </el-form>
  </DialogContainer>
</template>

<script setup lang="ts">
import { reactive, onMounted, watch, ref, computed } from "vue"
import { useRoute, useRouter } from "vue-router"
import { FormInstance } from "element-plus"
import { debounce } from "lodash-es"
import { PdfPreviewComponent, DialogContainer } from "@/base-components"
import { MedicalRecordTree, DocumentMeta } from "@/page-components"
import { getRecordViewData } from "@/interfaces"
import { useSystemStore } from "@/stores"
import { decryptStr, SystemAlert, SystemPrompt, toastError } from "@/utils"
import { confirmSignApi, rejectSignApi } from "./interface"

const systemStore = useSystemStore()

const route = useRoute()
const router = useRouter()

type AnyObject = Record<string, any>

const state = reactive({
  loading: false,
  baseInfo: {} as AnyObject,
  firstPageFields: [] as AnyObject[],
  pdfSrc: "",
  targetNode: {} as AnyObject,
  treeInfo: {} as any,
  documentName: "",
  documentWsid: ""
})

const handlePdfClick = node => {
  state.pdfSrc = "/api" + node?.contentFilePath
  state.targetNode = node
  state.documentName = node?.title
  state.documentWsid = node?.wsid
}

watch(
  () => route.query.inpNo,
  val => {
    if (val) {
      getData()
    }
  }
)

const getData = async () => {
  const inpNo = route.query.inpNo as string
  const secretKey = route.query.secretKey || ""
  const params = { inpNo: inpNo }
  if (secretKey) {
    const sealKey = decryptStr(secretKey as string)
    params["sealKey"] = sealKey
  }
  state.loading = true
  getRecordViewData(params).then(res => {
    const { code, data } = res.data
    if (code === "100100000") {
      const { baseInfo = {}, firstPageFields = [] } = data
      state.baseInfo = baseInfo
      state.firstPageFields = firstPageFields
      state.treeInfo = res.data.data.treeInfo
      state.loading = false
    }
  })
}

onMounted(async () => {
  await getData()
  registerKeyboardEvent()
})

/* =============== toolbar =============== */

const recordTreeRef = ref<InstanceType<typeof MedicalRecordTree>>()
const documentNodeList = computed(() => recordTreeRef.value?.state.documentNodeList)

const registerKeyboardEvent = () => {
  document.onkeyup = e => {
    const keyCode = e.code
    // 左/上键
    if (keyCode === "ArrowLeft" || keyCode === "ArrowUp") {
      toPrev()
    }
    // 右/下键
    else if (keyCode === "ArrowRight" || keyCode === "ArrowDown") {
      toNext()
    }
  }
}

const toPrev = debounce(() => {
  if (!documentNodeList.value) return
  const index = documentNodeList.value.findIndex(item => item.wsid === state.documentWsid) || 0
  if (index <= 0) return
  const targetNode = documentNodeList.value[index - 1]
  handlePdfClick(targetNode)
  recordTreeRef.value?.setCurrentKey(targetNode.wsid)
}, 300)

const toNext = debounce(() => {
  if (!documentNodeList.value) return
  const index = documentNodeList.value.findIndex(item => item.wsid === state.documentWsid)
  if (index >= documentNodeList.value.length - 1) return
  const targetNode = documentNodeList.value[index + 1]
  handlePdfClick(targetNode)
  recordTreeRef.value?.setCurrentKey(targetNode.wsid)
}, 300)

/* =============== 弹窗 =============== */

// 展示备注
const dialogVisible = ref(false)

const showRejectDialog = () => {
  dialogVisible.value = true
}

/* =============== 质控通过、不通过 =============== */

const rejectState = reactive({
  reason: ""
})

// 签收
const handlePass = async () => {
  await SystemPrompt("是否确认签收？")
  try {
    systemStore.showLoading("请稍后")
    const success = (await confirmSignApi(state.baseInfo.wsid)).data
    console.log(success)
    systemStore.hideLoading()
    await SystemAlert("操作成功", "success")
    router.push("/medical-record/sign-for/waiting")
  } catch (error: any) {
    systemStore.hideLoading()
    toastError(error)
  }
}

// 签收不通过，打开弹窗
const handleReject = () => {
  showRejectDialog()
}

// 确认不通过
const confirmReject = () => {
  rejectFormRef.value?.validate(async valid => {
    if (!valid) return
    await SystemPrompt("是否签收不通过？确认后不能修改。")
    try {
      systemStore.showLoading()
      const success = (await rejectSignApi(state.baseInfo.wsid, rejectState.reason)).data
      console.log(success)
      systemStore.hideLoading()
      await SystemAlert("操作成功", "success")
      router.push("/medical-record/sign-for/waiting")
    } catch (error: any) {
      systemStore.hideLoading()
      toastError(error)
    }
  })
}

const rejectFormRef = ref<FormInstance>()

const reasonFormRules = {
  reason: [{ required: true, message: "请填写原因", trigger: "blur" }]
}
</script>

<style lang="less" scoped>
.container {
  display: flex;
  justify-content: space-between;
  height: 100%;

  .pdf-container {
    overflow-x: auto;
    height: calc(100% - 60px);
    min-width: 400px;
    flex: 1;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .remark {
      color: #f59a23;
      background-color: #fdf6ec;
      padding: 10px;
      cursor: pointer;
      div {
        max-width: 800px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .tool-bar {
    background: #fff;
    border-radius: 8px;
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 40px;

    .document-name {
      font-size: 16px;
      font-weight: 600;
    }
  }

  .view-right {
    display: flex;
    flex-wrap: wrap;
    flex: 1;
  }
}
.remark-existed {
  height: calc(100% - 40px) !important;
}
@media screen and (max-width: 1280px) {
  .view-middle {
    min-width: 0 !important;
  }
}
</style>
