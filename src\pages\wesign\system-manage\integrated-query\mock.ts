import { QueryTermTypeEnum } from "@/configs"
export const queryTermList = [
  {
    label: "病案号",
    prop: "inpNo",
    type: QueryTermTypeEnum.Input
  },
  {
    label: "年龄",
    prop: "age",
    type: QueryTermTypeEnum.CountRange
  },
  {
    label: "门(急)诊诊断编码",
    prop: "ICD-10-Code",
    type: QueryTermTypeEnum.CodeSelect,
    versionType: "ICD-10"
  },
  {
    label: "门(急)诊诊断名称",
    prop: "ICD-10-Name",
    type: QueryTermTypeEnum.NameSelect,
    versionType: "ICD-10"
  },
  {
    label: "科室",
    prop: "dept",
    type: QueryTermTypeEnum.DeptSelect
  },
  {
    label: "性别",
    prop: "sex",
    type: QueryTermTypeEnum.RangeSelect,
    rcCode: "RC001"
  },
  {
    label: "出院日期",
    prop: "outHospitalDays",
    type: QueryTermTypeEnum.Daterange
  }
]
