<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          :filter-prop-options="outpatientFilterFormOptions"
          label="患者信息"
        />
        <DepartmentFormItem v-model="searchFormState.deptCode" label="就诊科室" />
        <DaterangeFormItem v-model="searchFormState.consultationTime" label="就诊时间" />
        <CommonInputFormItem v-model="searchFormState.doctorName" label="就诊医师" />
        <DaterangeFormItem v-model="searchFormState.catalogTime" label="编码时间" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="tableRef"
        table-id="outpatientCatalogedTableIdent"
        :table-columns="finishedColumns"
        :request-api="getOutpatientCatalogListApi"
        :request-params="catalogedParams"
        :data-callback="dataCallback"
      >
        <!-- 患者姓名 -->
        <template #patientName="{ row }">
          <div style="display: flex; align-items: center; flex-wrap: wrap">
            <span style="margin: 0 16px">{{ row.patientName }}</span>
            <PatientLabelTag :row="row" />
          </div>
        </template>
        <template #consultationTime="{ row }">
          {{ formatDatetime(row.consultationTime, "YYYY-MM-DD HH:mm:ss") }}
        </template>
        <template #patientPrintCount="{ row }">
          {{ row.patientPrintCount > 0 ? "已打印" : "未打印" }}
        </template>
        <template #operation="{ row }">
          <TableButton @click="toEdit(row)">修改</TableButton>
          <TableButton @click="handleCheck(row)">校验</TableButton>
          <TableButton @click="toDetail(row)">查看</TableButton>
          <TableButton @click="downloadPdf(row)">下载PDF</TableButton>
          <!-- <TableButton @click="toEdit(row)">编码记录</TableButton> -->
        </template>
      </CommonTable>
    </template>

    <!-- 评分校验 -->
    <ScoreDrawer
      title="门急诊首页评分"
      :drawer-visible="scoreDrawerVisible"
      :close="closeScoreDrawer"
      :drawer-data="drawerData"
      :score-drawer-data="scoreDrawerData"
      :pdf-key="pdfKey"
      :inp-no="inpNo"
      :total-score="totalScore"
      :update-drawer-data="updateDrawerData"
      :drawer-able="true"
    />
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref, computed } from "vue"
import { useRouter } from "vue-router"
import {
  SearchContainer,
  PatientFilterFormItem,
  CommonTable,
  TableButton,
  DaterangeFormItem,
  DepartmentFormItem,
  PageContainer,
  PatientLabelTag,
  CommonInputFormItem
} from "@/base-components"
import { TabsRouter, ScoreDrawer } from "@/page-components"
import { getOutpatientCatalogListApi, getOutpatientValidateInfoApi } from "../interface"
import { finishedColumns, tabsRouterList, outpatientFilterFormOptions } from "./config"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { exportDynamicFormCatalogApi } from "@/interfaces"
import { useUserStore, useSystemStore } from "@/stores"
import { formatDatetime, formatDate, Message, downloadFile, toastError } from "@/utils"

const router = useRouter()
const systemStore = useSystemStore()

const { hasOperationPermission } = useUserStore()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  patientFilterProp: "",
  patientFilterValue: "",
  deptCode: "",
  consultationTime: "",
  doctorName: "",
  catalogTime: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

const catalogedParams = computed(() => {
  return { ...searchParams, queryType: "cataloged" }
})

/* ======================== 表格相关方法 ======================== */

const dataCallback = (data: Array<Record<string, any>>) => {
  return data.map(item => ({
    ...item,
    inHospitalDatetime: formatDatetime(item.inHospitalDatetime, "YYYY-MM-DD HH:mm:ss"),
    outHospitalDatetime: formatDatetime(item.outHospitalDatetime, "YYYY-MM-DD HH:mm:ss"),
    catalogTime: formatDatetime(item.catalogTime, "YYYY-MM-DD HH:mm:ss")
  }))
}

const tableRef = ref()

/* ==================== 操作 ======================= */

// 编辑 - 编码记录不可编辑
const toEdit = (row: Record<string, any>) => {
  router.push({
    path: "/catalog-preview",
    query: {
      appScenario: "OUTPATIENT_SERVICE_ENCODE",
      businessDataWsid: row.registerNo,
      returnUrl: "/coding/outpatient-and-emergency/finished",
      actionType: "edit"
    }
  })
}

// 查看
const toDetail = (row: Record<string, any>) => {
  router.push(`/coding/outpatient-and-emergency/detail?registerNo=${row.registerNo}`) // TODO
}

// 下载pdf
const downloadPdf = async (row: Record<string, any>) => {
  // TODO
  systemStore.showLoading("正在下载，请稍候")
  exportDynamicFormCatalogApi(row.registerNo)
    .then(res => {
      systemStore.hideLoading()
      Message.success("下载文件成功")

      downloadFile({
        fileData: res.data,
        fileType: "application/pdf",
        fileName: `${decodeURIComponent(res.headers["content-disposition"].split("filename=")[1].replaceAll('"', ""))}`
      })
    })
    .catch(error => {
      systemStore.hideLoading()
      toastError(error, "下载文件失败")
    })
}

/* ==================== 校验抽屉 ======================= */

const scoreDrawerVisible = ref(false)
const drawerData = ref<Array<Record<string, any>>>([])
const scoreDrawerData = ref<Array<Record<string, any>>>([])
const pdfKey = ref()
const inpNo = ref("")
const totalScore = ref()

// 校验问题
const handleCheck = row => {
  scoreDrawerVisible.value = true
  inpNo.value = row.registerNo
  getOutpatientValidateInfoApi({
    appScenario: "OUTPATIENT_SERVICE_HQMS",
    registerNo: row.registerNo
  }).then(res => {
    drawerData.value = res.data.data.deducts
    scoreDrawerData.value = drawerData.value
    pdfKey.value = res.data.data.pdfKey
    totalScore.value = res.data.data.totalScore
  })
}

const closeScoreDrawer = () => {
  scoreDrawerVisible.value = false
}

const updateDrawerData = (val: string) => {
  scoreDrawerData.value = drawerData.value.filter(item => val.includes(item.controlLevelEnum))
}
</script>
