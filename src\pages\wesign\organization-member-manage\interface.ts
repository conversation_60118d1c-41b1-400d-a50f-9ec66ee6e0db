import axios from "@/interfaces/axios-instance"

/* ======================== 角色管理接口 ======================== */

/**
 * @method GET
 * @desc   获取所有角色列表
 */
export function getRoleListApi() {
  return axios({
    method: "get",
    url: "/api/system/roles"
  })
}

/**
 * @method POST
 * @desc   角色管理-新增角色
 */
export function addRoleApi(obj) {
  const { roleName = "", remark = "" } = obj
  return axios({
    method: "post",
    url: "/api/system/roles",
    data: {
      roleName,
      remark
    }
  })
}

/**
 * @method PUT
 * @desc   角色管理-编辑角色
 */
export function editRoleApi(obj) {
  const { roleName = "", remark = "", roleWsid } = obj
  return axios({
    method: "put",
    url: `/api/system/roles/${roleWsid}`,
    data: {
      roleName,
      remark
    }
  })
}

/**
 * @method PUT
 * @desc   角色管理-移除绑定关系
 */
export function removeUserInRoleApi(obj) {
  const { userWsid = "", roleWsid = "" } = obj
  return axios({
    method: "put",
    url: `/api/system/roles/${roleWsid}/users/${userWsid}`
  })
}

/**
 * @method PUT
 * @desc   角色管理-变更状态
 */
export function editRoleStatusApi(obj) {
  const { roleWsid = "", status = "" } = obj
  return axios({
    method: "put",
    url: `/api/system/roles/${roleWsid}/change-status`,
    params: {
      status
    }
  })
}

/**
 * @method POST
 * @desc   角色管理-新增角色
 */
export function editUsersInRoleApi(obj) {
  const { roleWsid = "", userWsids = [] } = obj
  return axios({
    method: "post",
    url: `/api/system/roles/${roleWsid}/users`,
    data: [...userWsids]
  })
}

/* ======================== 权限管理接口 ======================== */

/**
 * @method GET
 * @desc   角色权限-获取当前角色的权限树
 */
export function getRolePermissions(roleWsid: string) {
  return axios({
    method: "get",
    url: `/api/system/roles/${roleWsid}/permissions`
  })
}

/**
 * @method POST
 * @desc   角色权限-更新当前角色的权限树
 */
export function updateRolePermissions(roleWsid: string, jsonData: string) {
  return axios({
    method: "post",
    url: `/api/system/roles/${roleWsid}/permissions`,
    data: { jsonData }
  })
}

/* ======================== 科室管理接口 ======================== */

/**
 * @method GET
 * @desc   分页查询映射关系列表
 */
export function getDepartmentReflectApi(params) {
  return axios({
    method: "get",
    url: `/api/catalog/dept/hosp-standard`,
    params
  })
}

/**
 * @method POST
 * @desc   新增/编辑HQMS科室映射关系
 */
export function changeHQMSReflectionApi(data) {
  return axios({
    method: "post",
    url: `/api/catalog/dept/hosp-standard`,
    data
  })
}

/**
 * @method DELETE
 * @desc   删除HQMS科室映射关系
 */
export function deleteHQMSReflectionApi(id) {
  return axios({
    method: "delete",
    url: `/api/catalog/dept/hosp-standard/${id}`
  })
}

/**
 * @method GET
 * @desc   获取部门菜单和按钮权限
 */
export const getDepartmentPermissionApi = (deptWsid: string) => {
  return axios({
    method: "get",
    url: "/api/system/dept/permissions",
    params: { deptWsid }
  })
}

/**
 * @method POST
 * @desc   更新部门菜单和按钮权限
 */
export const updateDepartmentPermissionApi = (deptWsid: string, jsonData: string) => {
  return axios({
    method: "post",
    url: "/api/system/dept/permissions",
    params: { deptWsid },
    data: { jsonData }
  })
}

/**
 * @method GET
 * @desc   获取职务菜单和按钮权限
 */
export const getJobTitlePermissionApi = (jobTitleCode: string) => {
  return axios({
    method: "get",
    url: "/api/system/job-title/permissions",
    params: { jobTitleCode }
  })
}

/**
 * @method POST
 * @desc   更新职务菜单和按钮权限
 */
export const updateJobTitlePermissionApi = (jobTitleCode: string, jsonData: string) => {
  return axios({
    method: "post",
    url: "/api/system/job-title/permissions",
    params: { jobTitleCode },
    data: { jsonData }
  })
}

interface IgetDataPermissionParams {
  objectType: string
  objectId: string
}

/**
 * @method GET
 * @desc   获取数据权限
 */
export const getDataPermissionApi = (params: IgetDataPermissionParams) => {
  return axios({
    method: "get",
    url: "/api/system/data/permissions",
    params: params
  })
}

interface IupdateDataPermissionData {
  objectType: string
  objectId: string
  permissionsType: string
  customScope: Array<string>
}

/**
 * @method POST
 * @desc   更新数据权限
 */
export const updateDataPermissionApi = (data: IupdateDataPermissionData) => {
  return axios({
    method: "post",
    url: "/api/system/data/permissions",
    data: data
  })
}
