import { createRouter, createWeb<PERSON><PERSON><PERSON> } from "vue-router"
import type { RouteRecordRaw } from "vue-router"
import CatalogPreview from "@/pages/catalog-preview/index.vue"
import { NoPermissionPage, NotFoundPage, ServerErrorPage } from "@/pages/exception"
import FormEdit from "@/pages/form-edit/index.vue"
import LoginPage from "@/pages/login/index.vue"
import MedicalAdviceCheck from "@/pages/medical-advice-check/index.vue"
import MedicalPreview from "@/pages/medical-preview/index.vue"
import MedicalSubmit from "@/pages/medical-submit/index.vue"
import MessageCenter from "@/pages/message-center/index.vue"
import PermissionConfig from "@/pages/permission-config/index.vue"
import ResourceConfig from "@/pages/resource-config/index.vue"
import Sso from "@/pages/sso/index.vue"
import userSettingRouter from "@/pages/user-setting/router"
import ScanSystemRoute from "@/pages/wesign/scan-system/route"
import subRouters, { validPathList } from "@/pages/wesign/sub-routers"

import { useSystemStore, useUserStore } from "@/stores"
import { Message } from "@/utils"

const routes: Array<RouteRecordRaw> = [
  { path: "/", redirect: "/home" },
  { path: "/login", meta: { hideLayout: true }, component: LoginPage },
  { path: "/sso", meta: { hideLayout: true }, component: Sso },
  { path: "/medical-preview", meta: { hideLayout: true }, component: MedicalPreview },
  { path: "/medical-submit", meta: { hideLayout: true }, component: MedicalSubmit },
  { path: "/medical-advice-check", meta: { hideLayout: true }, component: MedicalAdviceCheck },
  { path: "/resource-config", meta: { hideLayout: true }, component: ResourceConfig },
  { path: "/permission-config", meta: { hideLayout: true }, component: PermissionConfig },
  { path: "/catalog-preview", meta: { hideLayout: true }, component: CatalogPreview },
  { path: "/form-edit", meta: { hideLayout: true }, component: FormEdit },
  { path: "/message-center", component: MessageCenter, meta: { showBreadcrumb: false } },
  { path: "/no-permission", component: NoPermissionPage, meta: { showBreadcrumb: false } },
  { path: "/server-error", component: ServerErrorPage, meta: { showBreadcrumb: false } },
  { path: "/:path(.*)", component: NotFoundPage, meta: { showBreadcrumb: false } },
  ScanSystemRoute,
  userSettingRouter,
  ...subRouters
]

const router = createRouter({
  history: createWebHistory("/"),
  routes,
  strict: true
})
let queryList
let targetFlag = true
router.beforeEach((to, from) => {
  const systemStore = useSystemStore()
  const userStore = useUserStore()
  const currentPath = to.path
  // 切换页面时校验登录是否已失效
  if (
    !localStorage.getItem("SessionWsid") &&
    !currentPath.startsWith("/login") &&
    !currentPath.startsWith("/sso") &&
    !currentPath.startsWith("/medical-preview")
  ) {
    Message.warning("登录失效！请您重新登录")
    return {
      path: "/login",
      query: {
        // targetPage: `${to.fullPath}`
      }
    }
  }

  // 如果有targetPage则前往targetPage
  if (from.query.targetPage && targetFlag) {
    targetFlag = false

    // 前往非登录页面
    if (
      !currentPath.startsWith("/login") &&
      !currentPath.startsWith("/sso") &&
      !currentPath.startsWith("/medical-preview")
    ) {
      queryList = {}
      for (const key in from.query) {
        if (key !== "targetPage") queryList[key] = from.query[key]
      }
      // 兼容targetPage内还有query参数的情况
      if (from.query.targetPage.includes("?")) {
        const query = (from.query.targetPage as string)?.split("?")?.[1]?.split("&")
        query.forEach(item => {
          queryList[item.split("=")[0]] = item.split("=")[1]
        })
      }
      return {
        path: from.query.targetPage as string,
        query: queryList
      }
    }

    // 前往登录页面
    else if (currentPath.startsWith("/login")) {
      setTimeout(() => {
        targetFlag = true
      })
      return {
        path: currentPath,
        query: from.query
      }
    }
  }
  // 没有targetPage且前往登录页面 重置targetFlag
  else if (currentPath.startsWith("/login")) {
    targetFlag = true
  }

  // 切换页面时校验是否有目标页面权限(管理员不需要判断)
  if (
    // !userStore.isAdmin &&
    validPathList.includes(currentPath) &&
    !userStore.userAvailablePaths.includes(currentPath)
  ) {
    console.log(userStore.userAvailablePaths.includes(currentPath), currentPath)
    systemStore.$patch({ hasPermission: false })
  } else {
    systemStore.$patch({ hasPermission: true })
  }
})

router.afterEach(to => {
  const systemStore = useSystemStore()
  systemStore.hideLoading()
  const title = to.meta?.title as string
  document.title = title || "医易签"
})

export default router
