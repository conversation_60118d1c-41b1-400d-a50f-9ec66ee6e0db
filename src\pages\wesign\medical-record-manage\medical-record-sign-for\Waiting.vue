<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsConfig" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
        />

        <DepartmentFormItem v-model="searchFormState.outHospitalDeptWsid" label="出院科室" />

        <DaterangeFormItem v-model="searchFormState.outHospitalDatetime" label="出院时间" />

        <PatientLabelFormItem v-model="searchFormState.patientLabel" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="tableRef"
        table-id="waitingCatalogTableIdent"
        :table-columns="waitingColumns"
        :request-api="getSignForListApi"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <!-- 患者姓名 -->
        <template #patientName="{ row }">
          <div style="display: flex; align-items: center; flex-wrap: wrap">
            <span style="margin: 0 16px">{{ row.patientName }}</span>
            <PatientLabelTag :row="row" />
          </div>
        </template>
        <template #operation="{ row }">
          <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.SignFor)" @click="handleSign(row)">
            签收
          </TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>
</template>

<script setup lang="ts">
import { ref } from "vue"
import { useRouter } from "vue-router"
import {
  SearchContainer,
  PatientFilterFormItem,
  CommonTable,
  TableButton,
  DaterangeFormItem,
  PageContainer,
  PatientLabelTag,
  DepartmentFormItem,
  PatientLabelFormItem
} from "@/base-components"
import { TabsRouter } from "@/page-components"
import { MenuOperationEnum } from "@/configs"
import useTableSearch from "@/hooks/useTableSearch_v2"
import { useUserStore } from "@/stores"
import { formatDatetime } from "@/utils"
import { waitingColumns, tabsConfig, menuId } from "./config"
import { getSignForListApi } from "./interface"

const router = useRouter()

const { hasOperationPermission } = useUserStore()

/* ======================== 搜索相关数据及方法 ======================== */

const initialSearchData = {
  patientFilterValue: "",
  patientFilterProp: "patientName",
  outHospitalDeptWsid: "",
  outHospitalDatetime: "",
  patientLabel: "",
  receiptStatus: "PENDING"
}

const { searchFormState, searchParams, handleQuery, handleReset } = useTableSearch({
  filtersInitialData: initialSearchData
})

/* ======================== 表格相关方法 ======================== */

const tableRef = ref<InstanceType<typeof CommonTable>>()

const dataCallback = data => {
  data.forEach(row => {
    row.inHospitalDatetime = formatDatetime(row.inHospitalDatetime)
    row.outHospitalDatetime = formatDatetime(row.outHospitalDatetime)
  })
  return data
}

/* ======================== 操作 ======================== */

const handleSign = row => {
  router.push(`/medical-record/sign-for/detail?inpNo=${row.inpNo}`)
}
</script>
