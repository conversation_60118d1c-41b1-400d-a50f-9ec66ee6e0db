<!-- 轨迹抽屉 -->
<template>
  <div>
    <el-drawer v-model="drawerVisible" title="质控轨迹">
      <div v-loading="tranceLoading" style="height: 100%; overflow-y: auto">
        <el-timeline v-if="traceList.length > 0" style="height: 100%">
          <el-timeline-item
            v-for="(item, index) in traceList"
            :key="index"
            :timestamp="formatDatetime(item.operatorTime)"
            style="padding-right: 10px"
            placement="top"
            color="#74C041"
          >
            <template #dot>
              <img :src="getQcOperateIcon(item.operationTypeEnum)" />
            </template>
            <div class="content-container">
              <p class="drawer-content-bold">{{ item.operatorName }}</p>
              <p class="drawer-content">{{ getQcOperateType(item.operationTypeEnum) }}</p>
              <p
                v-if="showContent(item.operationTypeEnum)"
                :title="item.content"
                class="drawer-content-bold"
                style="margin-top: 8px"
              >
                {{ item.content }}
              </p>
            </div>
          </el-timeline-item>
        </el-timeline>
        <EmptyContent v-else />
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { computed, watch, ref } from "vue"
import { EmptyContent } from "@/base-components"
import QCFailSvg from "@/assets/svg/quality-control-trace/qc-fail.svg"
import FinalFailSvg from "@/assets/svg/quality-control-trace/qc-final-fail.svg"
import FinalRecordSvg from "@/assets/svg/quality-control-trace/qc-final-record.svg"
import FinalScoreSvg from "@/assets/svg/quality-control-trace/qc-final-score.svg"
import FinalSuccessSvg from "@/assets/svg/quality-control-trace/qc-final-success.svg"
import MedicalFailSvg from "@/assets/svg/quality-control-trace/qc-medical-fail.svg"
import MedicalRecordSvg from "@/assets/svg/quality-control-trace/qc-medical-record.svg"
import MedicalScoreSvg from "@/assets/svg/quality-control-trace/qc-medical-score.svg"
import MedicalSuccessSvg from "@/assets/svg/quality-control-trace/qc-medical-success.svg"
import NurseFailSvg from "@/assets/svg/quality-control-trace/qc-nurse-fail.svg"
import NurseRecordSvg from "@/assets/svg/quality-control-trace/qc-nurse-record.svg"
import NurseScoreSvg from "@/assets/svg/quality-control-trace/qc-nurse-score.svg"
import NurseSuccessSvg from "@/assets/svg/quality-control-trace/qc-nurse-success.svg"

import QCRecordSvg from "@/assets/svg/quality-control-trace/qc-record.svg"
import QCScoreSvg from "@/assets/svg/quality-control-trace/qc-score.svg"
import QCSuccessSvg from "@/assets/svg/quality-control-trace/qc-success.svg"

import { QualityControlOperatorTypeEnum } from "@/configs"
import { getQualityControlTraceApi } from "@/interfaces"
import { formatDatetime, toastError, getQcOperateType } from "@/utils"

const props = defineProps({
  visible: { type: Boolean, default: false },
  inpNo: { type: String, required: true }
})

const emits = defineEmits(["update:visible"])

const drawerVisible = computed({
  get: () => props.visible,
  set: val => emits("update:visible", val)
})

const tranceLoading = ref(false)
const traceList = ref<Array<Record<string, any>>>([])

watch(
  () => drawerVisible.value,
  () => {
    if (drawerVisible.value) {
      tranceLoading.value = true
      getQualityControlTraceApi(props.inpNo)
        .then(res => {
          traceList.value = res.data.data || []
          tranceLoading.value = false
        })
        .catch(err => {
          toastError(err, "获取质控轨迹失败")
          tranceLoading.value = false
        })
    } else {
      traceList.value = []
    }
  }
)

// 是否显示详情文本
function showContent(operateType) {
  return [
    QualityControlOperatorTypeEnum.QC_FINAL_COMMENT,
    QualityControlOperatorTypeEnum.QC_FINAL_SCORE,
    QualityControlOperatorTypeEnum.QC_MEDICAL_COMMENT,
    QualityControlOperatorTypeEnum.QC_MEDICAL_SCORE,
    QualityControlOperatorTypeEnum.QC_NURSE_COMMENT,
    QualityControlOperatorTypeEnum.QC_NURSE_SCORE
  ].includes(operateType)
}

// 获取不同类型的操作的图标
function getQcOperateIcon(operateType) {
  switch (operateType) {
    case QualityControlOperatorTypeEnum.QC_FINAL_SCORE:
      return FinalScoreSvg
    case QualityControlOperatorTypeEnum.QC_NURSE_SCORE:
      return NurseScoreSvg
    case QualityControlOperatorTypeEnum.QC_MEDICAL_SCORE:
      return MedicalScoreSvg
    case QualityControlOperatorTypeEnum.QC_FINAL_COMMENT:
      return FinalRecordSvg
    case QualityControlOperatorTypeEnum.QC_NURSE_COMMENT:
      return NurseRecordSvg
    case QualityControlOperatorTypeEnum.QC_MEDICAL_COMMENT:
      return MedicalRecordSvg
    case QualityControlOperatorTypeEnum.QC_MEDICAL_PASS:
      return MedicalSuccessSvg
    case QualityControlOperatorTypeEnum.QC_MEDICAL_FAIL:
      return MedicalFailSvg
    case QualityControlOperatorTypeEnum.QC_NURSE_PASS:
      return NurseSuccessSvg
    case QualityControlOperatorTypeEnum.QC_NURSE_FAIL:
      return NurseFailSvg
    case QualityControlOperatorTypeEnum.QC_FINAL_PASS:
      return FinalSuccessSvg
    case QualityControlOperatorTypeEnum.QC_FINAL_FAIL:
      return FinalFailSvg
    // 医护质控
    case QualityControlOperatorTypeEnum.QC_DEPT_PASS:
      return QCSuccessSvg
    case QualityControlOperatorTypeEnum.QC_DEPT_SCORE:
      return QCScoreSvg
    case QualityControlOperatorTypeEnum.QC_DEPT_COMMENT:
      return QCRecordSvg
    case QualityControlOperatorTypeEnum.QC_DEPT_FAIL:
      return QCFailSvg

    default:
      return ""
  }
}
</script>

<style lang="less" scoped>
:deep(.el-drawer) {
  .el-timeline-item__timestamp {
    padding-top: 20px;
    font-size: 14px;
    color: rgba(10, 22, 51, 0.4);
    line-height: 16px;
  }
  .el-timeline-item__wrapper {
    padding-left: 68px;
  }
  .el-timeline-item__tail {
    left: 24px;
    top: 25px;
  }
}
.content-container {
  background: #f6f7f9;
  padding: 16px;
  font-size: 14px;
}
.drawer-content {
  margin-top: 8px;
  color: rgba(10, 22, 51, 0.6);
}
.drawer-content-bold {
  color: #0a1633;
  font-weight: 600;
  white-space: pre;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
