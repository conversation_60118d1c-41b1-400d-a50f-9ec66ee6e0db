/** 打印模块公共配置 */

import type { BaseOptionItem } from "@/types"

export const invoicingOptions = [
  { label: "是", value: "1" },
  { label: "否", value: "0" }
]

export const receiveTypeOptions: Array<BaseOptionItem> = [
  { label: "自取", value: "OWN_TAKE" },
  { label: "邮寄", value: "MAIL" },
  { label: "电子分发", value: "ELECTRONIC_DISTRIBUTION" }
]

export const filterPropOptions: Array<BaseOptionItem> = [
  { label: "姓名", value: "patientName" },
  { label: "患者编号", value: "patientId" },
  { label: "身份证号", value: "patientIdCard" }
]

export const refundManageOptions: Array<BaseOptionItem> = [
  { label: "姓名", value: "queryName" },
  { label: "患者编号", value: "patientId" },
  { label: "身份证号", value: "queryIdCard" }
]
