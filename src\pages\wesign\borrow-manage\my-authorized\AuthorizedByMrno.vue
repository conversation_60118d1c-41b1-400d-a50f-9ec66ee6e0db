<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchForm
        :form-config="authorizedSearchFormConfig"
        :form-state="searchFormState"
        @query-btn-click="handleQuery"
        @reset-btn-click="handleReset"
      />
    </template>

    <template #table>
      <CommonTable
        table-id="myBorrowMrnoTable"
        :table-columns="authorizedByMrnoColumns"
        :request-api="getAuthorizedByMrnoListApi"
        :request-params="searchParams"
      >
        <template #statusEnumName="{ row }">
          <MedicalRecordStatusTag :status="row.statusEnumName" />
        </template>
        <template #outHospitalDatetime="{ row }">{{ formatDatetime(row.outHospitalDatetime) }}</template>
        <template #operationTime="{ row }">{{ formatDatetime(row.operationTime) }}</template>
        <template #operation="{ row }">
          <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.View)" @click="handleRowClick(row)">
            查看
          </TableButton>
        </template>
      </CommonTable>
    </template>

    <SealDialog ref="sealDialogRef" :selected-row="medicalRecord" :confirm-callback="toDetail"></SealDialog>
  </PageContainer>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue"
import { useRouter } from "vue-router"
import { CommonTable, TableButton, PageContainer, MedicalRecordStatusTag } from "@/base-components"
import { TabsRouter, SealDialog, SearchForm } from "@/page-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { useUserStore } from "@/stores"
import { encryptStr, formatDatetime } from "@/utils"
import { authorizedSearchFormConfig, authorizedByMrnoColumns, tabsRouterList, menuId } from "./config"
import { getAuthorizedByMrnoListApi } from "./interface"

const { hasOperationPermission } = useUserStore()

const router = useRouter()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  patientFilterProp: "",
  patientFilterValue: "",
  outHospitalDeptWsid: "",
  outHospitalDatetime: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/*==================== 点击行操作 ================== */

const medicalRecord = ref<Record<string, any> | null>(null) // 用户点击的病案
const sealDialogRef = ref()

// 点击按钮
const handleRowClick = row => {
  medicalRecord.value = row
  sealDialogRef.value.checkSealed()
}

// 查看详情
const toDetail = () => {
  if (medicalRecord.value?.viewPermissionEnumName === "DISABLE") return
  const query = { id: medicalRecord.value?.wsid, type: "mrno" }
  if (sealDialogRef.value.secretKey) {
    query["secretKey"] = encryptStr(sealDialogRef.value.secretKey)
  }
  router.push({
    path: "/borrow/detail",
    query: query
  })
}
</script>
