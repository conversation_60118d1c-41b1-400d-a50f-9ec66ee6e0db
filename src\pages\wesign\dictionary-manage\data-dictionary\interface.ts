import axios from "@/interfaces/axios-instance"

/* ======================== 数据字典分类 ======================== */

/**
 * @method GET
 * @desc   查询所有数据字典分类
 */
export function queryDictionaryGroup() {
  return axios({
    method: "get",
    url: `/api/system/dictionary/group`
  })
}

/**
 * @method GET
 * @desc   查询指定数据字典分类（暂未使用）
 */
export function queryDictionaryGroupByKey(key: string) {
  return axios({
    method: "get",
    url: `/api/system/dictionary/group/${key}`
  })
}

interface GroupParam {
  key: string
  name: string
  desc: string
  id: string
}

/**
 * @method POST
 * @desc   新增数据字典分类
 */
export function addDictionaryGroup(obj: GroupParam) {
  return axios({
    method: "post",
    url: `/api/system/dictionary/group`,
    data: obj
  })
}

/**
 * @method PUT
 * @desc   修改数据字典分类
 */
export function editDictionaryGroup(obj: groupParam) {
  return axios({
    method: "put",
    url: `/api/system/dictionary/group`,
    data: obj
  })
}

/**
 * @method DELETE
 * @desc   删除数据字典分类
 */
export function deleteDictionaryGroup(key: string) {
  return axios({
    method: "delete",
    url: `/api/system/dictionary/group/${key}`
  })
}

/* ======================== 数据字典明细 ======================== */

/**
 * @method GET
 * @desc   查询指定的数据字典明细（暂未使用）
 */
export function queryDictionaryDetailByKey(wsid: string) {
  return axios({
    method: "get",
    url: `/api/system/dictionary/${wsid}`
  })
}

interface IDictionaryDetailParam {
  groupKey?: string
  wsid?: string
  key: string
  value: string
  describe?: string
  length?: number // 隐私关键字长度
}

/**
 * @method POST
 * @desc   新增数据字典明细
 */
export function addDictionaryDetail(obj: IDictionaryDetailParam) {
  return axios({
    method: "post",
    url: `/api/system/dictionary`,
    data: obj
  })
}

/**
 * @method PUT
 * @desc   编辑数据字典明细
 */
export function editDictionaryDetail(obj: IDictionaryDetailParam) {
  return axios({
    method: "put",
    url: `/api/system/dictionary`,
    data: obj
  })
}

/**
 * @method PUT
 * @desc   设置数据字典明细状态
 */
export function setDictionaryDetailStatus(wsid: string, nextStatus: "ENABLE" | "DISABLE" | "DEL", groupKey: string) {
  return axios({
    method: "put",
    url: `/api/system/dictionary/${wsid}?status=${nextStatus}&groupKey=${groupKey}`
  })
}
