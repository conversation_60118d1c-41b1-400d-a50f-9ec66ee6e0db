import { Ref } from "vue"

const useFormSetting = <T extends Record<string, any>>(
  formState: T,
  initFormValues: T,
  actionType: Ref<"add" | "edit">,
  formVisible: Ref<boolean>
) => {
  function showAddForm() {
    for (const key in formState) {
      formState[key] = initFormValues[key]
    }
    actionType.value = "add"
    formVisible.value = true
  }

  function showEditForm(row: T) {
    for (const key in formState) {
      formState[key] = row[key] ?? initFormValues[key]
    }
    actionType.value = "edit"
    formVisible.value = true
  }

  return { showAddForm, showEditForm }
}

export default useFormSetting
