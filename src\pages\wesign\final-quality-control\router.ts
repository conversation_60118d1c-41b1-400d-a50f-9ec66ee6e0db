import { MenuOperationEnum } from "@/configs"
import type { RouteRecordRaw } from "vue-router"

const finalQualityControlRouter: RouteRecordRaw = {
  path: "/final-quality-control",
  name: "FinalQualityControl",
  redirect: "/final-quality-control/manage",
  meta: {
    title: "终末质控",
    icon: "ri-folder-shield-line",
    type: "multiple",
    showBreadcrumb: true
  },
  children: [
    {
      path: "/final-quality-control/manage",
      redirect: "/final-quality-control/manage/waiting-control",
      meta: {
        title: "终末质控",
        operations: [
          { id: MenuOperationEnum.QC, name: "质控" },
          { id: MenuOperationEnum.View, name: "查看" },
          { id: MenuOperationEnum.Track, name: "质控轨迹" }
        ]
      },
      children: [
        {
          path: "/final-quality-control/manage/waiting-control",
          meta: { title: "待质控" },
          component: () => import("./final-quality-control-manage/WaitingQualityControl.vue")
        },
        {
          path: "/final-quality-control/manage/controlled",
          meta: { title: "已质控" },
          component: () => import("./final-quality-control-manage/QualityControlled.vue")
        }
      ]
    },
    {
      path: "/final-quality-control/manage/view",
      meta: { title: "质控详情", hideMenu: true },
      component: () => import("../department-quality-control/quality-control-view/index.vue")
    },
    {
      path: "/final-quality-control/quality-control-assignment",
      component: () => import("./quality-control-task-assignment/index.vue"),
      meta: {
        title: "质控任务分配",
        operations: [
          { id: MenuOperationEnum.Distribute, name: "分配" },
          { id: MenuOperationEnum.ProcessSearch, name: "进度查询" }
        ]
      }
    }
  ]
}

export default finalQualityControlRouter
