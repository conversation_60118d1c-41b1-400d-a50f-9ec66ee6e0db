import type { TableColumnItem, SearchFormConfigItem } from "@/types"
import type { FormRules } from "element-plus"

export const menuId = "/archive"

export const tabsRouterList = [
  { label: "待归档", path: "/archive/local/pending" },
  { label: "已归档", path: "/archive/local/finished" }
]

/* ======================== 待归档配置 ======================== */

// 待归档表格配置
export const archivePendingTableColumns: Array<TableColumnItem> = [
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "mrNo", label: "病案号", minWidth: 150, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 130, must: true },
  { prop: "visitId", label: "住院次数", minWidth: 100 },
  { prop: "patientSex", label: "性别", minWidth: 100 },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 120 },
  { prop: "inHospitalDatetime", label: "入院时间", minWidth: 180 },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 120 },
  { prop: "outHospitalDatetime", label: "出院时间", minWidth: 180 },
  { prop: "outDischargeDiagnosisName", label: "出院主诊断", minWidth: 150 },
  { prop: "ifOverdue", label: "是否逾期", minWidth: 100 },
  { prop: "overdueDay", label: "逾期天数", minWidth: 100 },
  { prop: "overdueReason", label: "逾期原因", minWidth: 200 },
  {
    prop: "archiveStatus",
    label: "状态",
    minWidth: 120,
    must: true
  },
  {
    prop: "archiveFailReason",
    label: "失败原因",
    minWidth: 150,
    must: true
  },
  { prop: "operation", label: "操作", width: 150, fixed: "right" }
]

// 待归档搜索表单配置
export const archivePendingSearchFormConfig: Array<SearchFormConfigItem> = [
  { type: "patient", label: "患者信息" },
  { type: "department", label: "出院科室", prop: "outHospitalDeptWsid" },
  { type: "daterange", label: "出院时间", prop: "outHospitalDatetime" },
  { type: "visit", label: "住院次数" }
]

/* ======================== 已归档配置 ======================== */

// 已归档表格配置
export const archiveFinishedTableColumns: Array<TableColumnItem> = [
  ...archivePendingTableColumns,
  { prop: "creator", label: "归档人", minWidth: 100 },
  { prop: "archiveDatetime", label: "归档时间", minWidth: 180 }
]

// 已归档搜索表单配置
export const archiveFinishedSearchFormConfig: Array<SearchFormConfigItem> = [
  ...archivePendingSearchFormConfig,
  { type: "daterange", label: "归档时间", prop: "archiveDatetime" }
]

// 补充归档校验
export const archivePendingValidateRules: FormRules = {
  reportName: [{ required: true, message: "请选择报告名称", trigger: "blur" }]
}

export enum ArchiveStatusEnum {
  ARCHIVE_WAIT = "ARCHIVE_WAIT",
  ARCHIVING = "ARCHIVING",
  ARCHIVE_SUCCESS = "ARCHIVE_SUCCESS",
  ARCHIVE_FAIL = "ARCHIVE_FAIL"
}

// 归档状态
export const getArchiveStatus = (archiveStatusEnum: string) => {
  const statusMap = {
    ARCHIVE_WAIT: {
      label: "待归档",
      type: "info"
    },
    ARCHIVING: {
      label: "归档中",
      type: "warning"
    },
    ARCHIVE_SUCCESS: {
      label: "归档成功",
      type: "success"
    },
    ARCHIVE_FAIL: {
      label: "归档失败",
      type: "danger"
    }
  }
  return statusMap[archiveStatusEnum]
}
