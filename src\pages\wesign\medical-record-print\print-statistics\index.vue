<template>
  <div>
    <div class="trade-summary">
      <!-- 系统概况 -->
      <div class="system-summary">
        <span class="system-summary-title">系统概况</span>
        <div class="system-summary-content">
          <div class="system-summary-item">
            <div class="system-summary-item-title">
              <img :src="UserTotalIcon" alt="" />
              <div>
                <span>{{ systemOverviewData.userNum }}</span>
                /个
              </div>
            </div>
            <span class="system-summary-item-content">用户总数</span>
          </div>
          <div class="system-summary-item">
            <div class="system-summary-item-title">
              <img :src="OrderTotalIcon" alt="" />
              <div>
                <span>{{ systemOverviewData.orderNum }}</span>
                /个
              </div>
            </div>
            <span class="system-summary-item-content">订单总数</span>
          </div>
        </div>
      </div>

      <!-- 交易概况 -->
      <div class="trade-overview">
        <ChartContainer
          ref="transactionOverview"
          :width="'100%'"
          :name="'overview'"
          :title="'交易概况'"
          :show-type="false"
          @update-date="updateDate"
        >
          <template #content>
            <TransactionOverview :order-data="transactionOverviewData" />
          </template>
        </ChartContainer>
      </div>
    </div>

    <!-- 订单统计 -->
    <div class="statistics-item">
      <ChartContainer
        ref="order"
        :width="'100%'"
        :name="'order'"
        :title="'订单统计'"
        :show-type="false"
        @update-date="updateDate"
      >
        <template #content>
          <StatisticsChart
            v-if="orderData.series.length"
            :time-list="orderData.timeList"
            :series="orderData.series"
            :show-legend="orderData.showLegend"
          />
          <EmptyContent v-else />
        </template>
      </ChartContainer>
    </div>

    <!-- 方案统计 -->
    <div class="statistics-item">
      <ChartContainer
        ref="printType"
        :width="'100%'"
        :name="'printType'"
        :title="'方案统计'"
        :tabs-data="printTypeData.tabsData"
        :dropdown-data="printTypeData.dropdownData"
        @update-date="updateDate"
        @update-type="updateType"
      >
        <template #content>
          <StatisticsChart
            v-if="printTypeData.series.length"
            :time-list="printTypeData.timeList"
            :series="printTypeData.series"
            :show-legend="printTypeData.showLegend"
          />
          <EmptyContent v-else />
        </template>
      </ChartContainer>
    </div>

    <!-- 是否开票统计 -->
    <!-- <div class="statistics-item">
      <div>
        <ChartContainer
          ref="invoicing"
          :width="'100%'"
          :name="'invoicing'"
          :title="'是否开票'"
          :show-type="false"
          @update-date="updateDate"
        >
          <template #content>
            <StatisticsChart
              v-if="invoicingData.series.length"
              :time-list="invoicingData.timeList"
              :series="invoicingData.series"
              :show-legend="invoicingData.showLegend"
            />
            <EmptyContent v-else />
          </template>
        </ChartContainer>
      </div>
    </div> -->

    <!-- 工作量统计 -->
    <div class="statistics-item">
      <WorkloadStatistic :print-purpose-options="printTypeOptions" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, reactive } from "vue"
import { EChartsOption } from "echarts"
import { cloneDeep } from "lodash-es"
import { EmptyContent } from "@/base-components"
import {
  getStatisticsOrder,
  getStatisticsTrade,
  getStatisticsSystemOverview,
  getStatisticInvoicing,
  getStatisticsPrintTypeApi
} from "../interface"
import ChartContainer from "./components/ChartContainer.vue"
import StatisticsChart from "./components/StatisticsChart.vue"
import TransactionOverview from "./components/TransactionOverview.vue"
import WorkloadStatistic from "./components/WorkloadStatistic.vue"

import { getPrintTypeSeries, getOrderSeries, getInvoicingSeries } from "./config"
import { ChartRequestParams, TransactionOverviewProps, ChartData } from "./types"
import OrderTotalIcon from "@/assets/svg/medical-reacord-trace/orderTotal.svg"
import UserTotalIcon from "@/assets/svg/medical-reacord-trace/userTotal.svg"
import { StatisticTypeEnum } from "@/configs/enums"
import { getPrintType } from "@/interfaces"

/*========================= 图表数据初始化 ======================= */

//订单统计数据
const order = ref()
const orderData = reactive<ChartData>({
  timeList: [],
  series: {} as EChartsOption["series"],
  showLegend: false,
  legendData: []
})

// 打印方案统计数据
const printType = ref()
const printTypeData = reactive<ChartData>({
  timeList: [],
  series: {} as EChartsOption["series"],
  showLegend: false,
  legendData: [],
  date: [],
  tabsData: [],
  // 打印方案code
  printTypeCode: ""
})

//是否开票数据
const invoicing = ref()
const invoicingData = reactive<ChartData>({
  timeList: [],
  series: {} as EChartsOption["series"],
  showLegend: false,
  legendData: []
})

//交易概况数据
const transactionOverview = ref()
const transactionOverviewData = ref<TransactionOverviewProps>({
  orderAmount: 0,
  orderPayNum: 0,
  addUserNum: 0,
  orderUserNum: 0
})

//系统概况数据
const systemOverviewData = ref<Record<string, number>>({
  orderNum: 0,
  userNum: 0
})

//打印方案类型
const printTypeOptions = ref<{ key: string; value: string }[]>([])

onMounted(async () => {
  getStatisticsSystemOverviewData()

  // 初始化都默认加载今日的数据
  order.value?.handleDateRangeChange("DAY")
  transactionOverview.value?.handleDateRangeChange("DAY")
  invoicing.value?.handleDateRangeChange("DAY")
  await getPrintTypeData()
  const tabsData = printTypeData?.tabsData as any
  if (tabsData) {
    printType.value?.handleDateRangeChange("DAY", tabsData[0]?.key || ("" as string))
  }
})

/*========================= 获取各图表数据方法 ======================= */

/**
 * 获取订单统计信息
 */
const getStatisticsOrderData = (
  date: Array<string>,
  type: "DAY" | "WEEK" | "MONTH",
  dateType: "group" | "range",
  dateFormatter: string,
  timeList: []
) => {
  const params = {
    startDate: dateType === "range" ? date[0] : "",
    endDate: dateType === "range" ? date[1] : "",
    statisticsType: dateType === "group" ? type : ""
  }

  getStatisticsOrder(params).then(res => {
    orderData.timeList = timeList

    orderData.series = getOrderSeries(dateFormatter, timeList, res.data.data)
    console.log(`output->date`, dateFormatter, timeList, res.data.data, orderData.series)
  })
}

/**
 * 获取是否开票数据
 */
const getStatisicInvoicingData = (
  date: Array<string>,
  type: "DAY" | "WEEK" | "MONTH",
  dateType: "group" | "range",
  dateFormatter: string,
  timeList
) => {
  const params = {
    startDate: dateType === "range" ? date[0] : "",
    endDate: dateType === "range" ? date[1] : "",
    statisticsType: dateType === "group" ? type : ""
  }
  getStatisticInvoicing(params).then(res => {
    invoicingData.timeList = timeList
    invoicingData.series = getInvoicingSeries(dateFormatter, timeList, res.data.data)
  })
}

/**
 * 获取系统概况数据
 */
const getStatisticsSystemOverviewData = () => {
  getStatisticsSystemOverview().then(res => {
    systemOverviewData.value = res.data.data
  })
}

/**
 * 获取交易概况数据
 */

const getTransactionOverviewData = (date: Array<string>, type: StatisticTypeEnum, dateType: "group" | "range") => {
  const params: ChartRequestParams = {
    startDate: dateType === "range" ? date[0] : "",
    endDate: dateType === "range" ? date[1] : "",
    statisticsType: dateType === "group" ? type : ""
  }
  getStatisticsTrade(params).then(res => {
    transactionOverviewData.value = res.data.data
  })
}

/* ====================== 打印方案统计 ======================= */

// 获取打印用途类型
async function getPrintTypeData() {
  const res = await getPrintType()
  printTypeOptions.value = res.map(item => {
    return {
      key: item.groupid,
      value: item.groupname
    }
  })
  printTypeData.tabsData = cloneDeep(printTypeOptions.value).splice(0, 3)
  printTypeData.dropdownData = cloneDeep(printTypeOptions.value).splice(3, printTypeOptions.value.length)
}

//获取打印用途数据
const getStatisticsPrintTypeData = (
  date: [number, number],
  type: "DAY" | "WEEK" | "MONTH",
  dateType: "group" | "range",
  dateFormatter: string,
  timeList,
  printTypeCode
) => {
  const params = {
    startDate: dateType === "range" ? date[0] : "",
    endDate: dateType === "range" ? date[1] : "",
    statisticsType: dateType === "group" ? type : "",
    printTypeCode
  }
  getStatisticsPrintTypeApi(params).then(res => {
    printTypeData.date = date
    printTypeData.timeList = timeList
    printTypeData.printTypeCode = printTypeCode
    printTypeData.series = getPrintTypeSeries(dateFormatter, timeList, res.data.data)
  })
}

/**
 * 更新时间范围，重新请求数据
 * @type 选择的时间段类型 DAY|WEEK|MONTH|string
 * @timeList x轴的时间段
 * @data 接口请求用到的起始时间戳
 * @name 统计图名称，区分多个统计图选择时间范围的操作
 * @code Tabs的key值
 * @dateType 选择的时间范围类型
 * @dateFormatter 时间格式化的类型
 */
const updateDate = (type, dateType, dateFormatter, timeList, date, name, code) => {
  if (name === "order") {
    getStatisticsOrderData(date, type, dateType, dateFormatter, timeList)
  }
  if (name === "printType") {
    getStatisticsPrintTypeData(date, type, dateType, dateFormatter, timeList, code)
  }
  if (name === "overview") {
    getTransactionOverviewData(date, type, dateType)
  }
  if (name === "invoicing") {
    getStatisicInvoicingData(date, type, dateType, dateFormatter, timeList)
  }
}

/**
 * 更新tab，重新请求数据
 * @type 选择的时间段类型 DAY|WEEK|MONTH|string
 * @timeList x轴的时间段
 * @data 接口请求用到的起始时间戳
 * @name 统计图名称，区分多个统计图选择时间范围的操作
 * @code 打印用途编号
 * @dateFormatter 时间格式化的类型
 * @dateType 控件类型
 */
const updateType = (type, dateType, dateFormatter, timeList, date, name, code) => {
  if (name === "printType") {
    getStatisticsPrintTypeData(date || printTypeData.date, type, dateType, dateFormatter, timeList, code)
  }
}
</script>

<style lang="less" scoped>
.statistics-item {
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  margin-bottom: 10px;
  padding: 10px 20px 20px 20px;
}
.trade-summary {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 10px;

  .system-summary {
    padding: 20px;
    // width: 40%;
    margin-right: 20px;
    background: #ffffff;

    .system-summary-title {
      font-size: 16px;
      font-family: PingFang SC-Bold, PingFang SC;
      font-weight: bold;
      color: rgba(10, 22, 51, 0.85);
      line-height: 22px;
      padding-bottom: 33px;
      display: inline-block;
    }
    .system-summary-content {
      display: flex;
      justify-content: space-between;
      .system-summary-item {
        width: 180px;
        height: 252px;
        width: 180px;
        height: 252px;
        background: #f0f4fb;
        border-radius: 4px;
        text-align: center;

        &:not(:last-child) {
          margin-right: 20px;
        }
        .system-summary-item-title {
          padding-top: 50px;
          padding-bottom: 10px;
          font-size: 14px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
          color: rgba(10, 22, 51, 0.6);
          line-height: 24px;
          img {
            padding-bottom: 25px;
          }
          span {
            font-size: 32px;
            font-family: DIN-Bold, DIN;
            font-weight: bold;
            color: rgba(10, 22, 51, 0.85);
            line-height: 24px;
            padding-right: 6px;
          }
        }
        .system-summary-item-content {
          font-size: 14px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
          color: rgba(10, 22, 51, 0.6);
          line-height: 24px;
        }
      }
    }
  }
}

.trade-overview {
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  padding: 16px 18px 0 18px;
  flex: 1;
  // width: 60%;
}
</style>
