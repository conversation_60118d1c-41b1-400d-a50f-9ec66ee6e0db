<template>
  <div class="add-node">
    <el-popover :show-arrow="false" placement="right" :visible="visible">
      <div
        v-for="item in nodeOptions"
        :key="item.type"
        class="approval-node-option"
        :icon="item.icon"
        @click="() => addNode(item)"
      >
        <i :class="item.icon" class="icon"></i>
        <span>{{ item.label }}</span>
      </div>

      <template #reference>
        <span class="el-dropdown-link" @click="visible = true">
          <i class="ri-add-circle-fill add-icon"></i>
        </span>
      </template>
    </el-popover>
  </div>
</template>

<script setup lang="ts">
import { inject, ref } from "vue"
import { nodeOptions, addNodeFn } from "../config"

const getNode: any = inject("getNode")
const visible = ref(false)
const graph = getNode().getData()?.graph

const addNode = item => {
  visible.value = false
  const currentNodeId = getNode().getData()?.id
  if (!currentNodeId) return
  addNodeFn(item, currentNodeId, graph)
}
</script>

<style lang="less" scoped>
.approval-node-option {
  padding: 5px;
  border-radius: 4px;
  cursor: pointer;
  .icon {
    margin-right: 4px;
  }
  &:hover {
    background-color: #f0f3f6;
  }
}
.add-node {
  .add-icon {
    font-size: 18px;
    background-color: #0c7ffc;
    border-radius: 50%;
    cursor: pointer;
    color: #fff;
  }
}
</style>
