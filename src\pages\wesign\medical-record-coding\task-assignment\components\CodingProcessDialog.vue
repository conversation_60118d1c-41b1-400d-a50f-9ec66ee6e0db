<template>
  <!-- 编码进度查询弹窗 -->
  <DialogContainer
    v-model:visible="visible"
    :title="'编码进度查询'"
    :width="1200"
    :close-on-click-modal="false"
    :cancel-callback="close"
    :no-footer="true"
  >
    <SearchContainer @query-btn-click="catalogProcessHandleQuery" @reset-btn-click="catalogProcessHandleReset">
      <el-form-item class="process-item" label="编码员">
        <el-select v-model="catalogProcessFormState.catalogerUserWsid" style="width: 100%">
          <el-option v-for="item in coders" :key="item.userWsid" :label="item.realName" :value="item.userWsid" />
        </el-select>
      </el-form-item>

      <el-form-item class="process-item" label="分配日期">
        <el-date-picker
          v-model="catalogProcessFormState.assignmentTime"
          type="daterange"
          style="width: 100%"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="x"
        />
      </el-form-item>

      <CommonSelectFormItem
        v-model="catalogProcessFormState.catalogStatus"
        label="状态"
        :options="catalogStatusOptions"
      />
    </SearchContainer>

    <div style="min-height: 320px; height: 60vh">
      <CommonTable
        ref="catalogProcessTableRef"
        :table-columns="props.rangeType === 'IN_HOSPITAL' ? processColumns : outPatientProcessColumns"
        :request-api="props.rangeType === 'IN_HOSPITAL' ? getCatalogLists : getOutpatientCatalogListApi"
        :request-params="catalogProcessParams"
        :data-callback="dataCallback"
      >
        <template #catalogStatus="{ row }">
          <el-tag v-if="isCataloged(row)" type="success">已编码</el-tag>
          <el-tag v-else type="warning">未编码</el-tag>
        </template>
      </CommonTable>
    </div>
  </DialogContainer>
</template>

<script lang="ts" setup>
import { computed, reactive, ref } from "vue"
import { CommonTable, CommonSelectFormItem, SearchContainer, DialogContainer } from "@/base-components"
import { getCatalogConfigInfo, getCatalogLists, getOutpatientCatalogListApi, type RangeType } from "../../interface"
import { catalogStatusOptions, processColumns, catalogStatusEnum, outPatientProcessColumns } from "../config"
import { useTableSearch } from "@/hooks"
import { useSystemStore } from "@/stores"
import { formatDatetime, toastError } from "@/utils"

const systemStore = useSystemStore()

const props = defineProps<{
  rangeType: RangeType
}>()

const coders = ref<Record<string, any>[]>([])

const init = async () => {
  const res = await getCatalogConfigInfo(props.rangeType)
  coders.value = res.data.data.cataloger
}

/* ======================== 表格 ======================== */

const dataCallback = (data: Array<Record<string, any>>) => {
  data.forEach(item => {
    item.inHospitalDatetime = formatDatetime(item.inHospitalDatetime)
    item.outHospitalDatetime = formatDatetime(item.outHospitalDatetime)
    item.assignmentTime = formatDatetime(item.assignmentTime)
    item.catalogTime = formatDatetime(item.catalogTime)
    item.catalogStatusEnum = catalogStatusEnum[item.catalogStatusEnum]
  })
  return data
}

/* ======================== 表单 ======================== */

const catalogProcessFormState = reactive({
  catalogerUserWsid: "",
  assignmentTime: "",
  catalogStatus: ""
})

const {
  searchParams: catalogProcessSearchParams,
  handleQuery: catalogProcessHandleQuery,
  handleReset: catalogProcessHandleReset
} = useTableSearch(catalogProcessFormState)

const catalogProcessParams = computed(() => {
  return { ...catalogProcessSearchParams, queryType: "catalog_plan" }
})

const isCataloged = (row: Record<string, any>) => {
  if (props.rangeType === "IN_HOSPITAL") {
    return row.catalogStatus
  } else {
    return row.catalogStatus === "CATALOGED"
  }
}

/* ================== */

const visible = ref(false)

const show = async () => {
  try {
    systemStore.showLoading("加载中")
    await init()
    systemStore.hideLoading()
    visible.value = true
  } catch (error: any) {
    systemStore.hideLoading()
    toastError(error)
  }
}

const close = () => {
  visible.value = false
}

defineExpose({
  show,
  close
})
</script>
