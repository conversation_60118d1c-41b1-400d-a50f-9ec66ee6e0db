<template>
  <DialogContainer
    v-model:visible="state.orderVisibleDialog"
    title="订单打印记录"
    :width="400"
    :no-footer="true"
    :close-callback="handleClose"
    :cancel-callback="handleClose"
  >
    <div v-loading="confirmLoading" class="table-container">
      <BaseTable ref="commonTableRef" :columns="orderDetailTableColumns" :data="recordSearchState.displayTableData">
        <template #createdDatetime="{ row }">
          {{ formatDatetime(row.createdDatetime) }}
        </template>
      </BaseTable>
    </div>
    <el-pagination
      v-model:current-page="paginationConfig.currentPage"
      v-model:page-size="paginationConfig.pageSize"
      :page-sizes="[10, 25, 50, 100]"
      :background="true"
      layout="sizes, prev, pager, next, jumper, total"
      :total="recordSearchState.tableData.length"
      @update:page-size="resetCurrentPage"
    />
  </DialogContainer>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, watch } from "vue"
import { cloneDeep } from "lodash-es"
import { DialogContainer, BaseTable } from "@/base-components"
import { formatDatetime, toastError } from "@/utils"
import { orderDetailTableColumns } from "../config"
import { queryOrderDetail } from "../interface"

const emits = defineEmits(["close"])

interface recordProps {
  orderVisibleDialog: boolean
  selectedRow: Record<string, any>
}

const props = withDefaults(defineProps<recordProps>(), {
  orderVisibleDialog: false
})

const state = reactive({
  orderVisibleDialog: false
})

watch(
  () => props.orderVisibleDialog,
  val => {
    state.orderVisibleDialog = cloneDeep(val)
  }
)

const recordSearchState = reactive({
  tableData: [],
  displayTableData: []
})

const paginationConfig = reactive({
  currentPage: 1,
  pageSize: 25
})

const confirmLoading = ref(false)

const getTableData = async () => {
  if (confirmLoading.value) return
  confirmLoading.value = true
  await queryOrderDetail({
    wsid: props.selectedRow.wsid
  })
    .then(res => {
      recordSearchState.tableData = res.data.data
      recordSearchState.displayTableData = cloneDeep(recordSearchState.tableData).splice(
        (paginationConfig.currentPage - 1) * paginationConfig.pageSize,
        paginationConfig.currentPage * paginationConfig.pageSize
      )
      confirmLoading.value = false
    })
    .catch(err => {
      confirmLoading.value = false
      toastError(err, "获取数据失败")
    })
}

// 打开弹窗时重新请求
watch(
  () => props.orderVisibleDialog,
  val => {
    if (val) {
      getTableData()
    }
  }
)

// 翻页时更新table数据
watch(
  () => paginationConfig,
  val => {
    recordSearchState.displayTableData = cloneDeep(recordSearchState.tableData).splice(
      (paginationConfig.currentPage - 1) * paginationConfig.pageSize,
      paginationConfig.currentPage * paginationConfig.pageSize
    )
  },
  {
    deep: true
  }
)

// 重置页码为首页
function resetCurrentPage() {
  paginationConfig.currentPage = 1
}

// 关闭弹窗
const handleClose = () => {
  emits("close")
}
</script>

<style lang="less" scoped>
.common-table-container {
  height: 500px !important;
}

:deep(.el-dialog) {
  // height: 400px !important;
}

:deep(.el-dialog__body) {
  height: 73%;
}

.table-container {
  height: 100%;
  margin-bottom: 20px;
}
</style>
