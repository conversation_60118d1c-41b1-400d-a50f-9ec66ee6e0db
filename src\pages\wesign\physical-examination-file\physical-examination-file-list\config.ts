import { TableColumnItem } from "@/types"

export const tableColumns: TableColumnItem[] = [
  { prop: "name", label: "姓名", minWidth: 80 },
  { prop: "gender", label: "性别", width: 80 },
  { prop: "age", label: "年龄", width: 80 },
  { prop: "phone", label: "联系方式", minWidth: 120 },
  { prop: "idCard", label: "身份证号", minWidth: 160 },
  { prop: "checkupDate", label: "体检日期", minWidth: 160 },
  { prop: "checkupId", label: "体检编号", minWidth: 120 },
  { prop: "operation", label: "操作", minWidth: 120 }
]
