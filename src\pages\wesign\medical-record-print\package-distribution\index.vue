<template>
  <PageContainer separate>
    <template #search>
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
          :filter-prop-options="filterPropOptions"
        />

        <CommonSelectFormItem
          v-model:model-value="searchFormState.packageStatus"
          label="状态"
          :options="statusOptions"
        />

        <DaterangeFormItem v-model:model-value="searchFormState.createdDatetime" label="申请时间" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="distributionTableRef"
        table-id="distributionTableIdent"
        :table-columns="distributionColumns"
        :request-api="getPackageList"
        :request-params="searchParams"
      >
        <template #header>
          <BatchOperationButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Resend)"
            type="primary"
            :disabled="!selectedRows.length"
            :icon="Position"
            @click="resend"
          >
            批量重新发送
          </BatchOperationButton>

          <ComplexExportButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Export)"
            :table-info="distributionTableRef?.tableState"
            :export-api="exportPackage"
            filters-prefix="printWsids"
            :search-filters="searchParams.filters"
          />
        </template>
        <template #createdDatetime="{ row }">
          {{ formatDatetime(row.createdDatetime) }}
        </template>
        <template #packageStatusStr="{ row }">
          <el-tag :type="statusValues[row.packageStatus]">
            {{ row.packageStatusStr }}
          </el-tag>
        </template>
        <template #operation="{ row }">
          <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.Progress)" @click="viewProcess(row)">
            进度
          </TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>

  <DialogContainer
    v-model:visible="processVisible"
    :title="'文件分发进度'"
    :width="1000"
    :close-on-click-modal="false"
    :no-footer="true"
  >
    <CommonTable
      ref="distributionProcessTableRef"
      :table-columns="distributionProcessColumns"
      :request-api="getPackageDetailList"
      :request-params="distributionProcessParams"
      :data-callback="processDataCallback"
      :pagination="false"
    >
      <template #header>
        <div style="margin-bottom: 12px" class="process-header">
          <el-form-item label="订单编号：">
            <p>{{ processData.orderCode }}</p>
          </el-form-item>

          <el-form-item label="订单状态：">
            <p>{{ processData.packageStatusStr }}</p>
          </el-form-item>

          <div class="process-container">
            <span class="process-label">分发进度：</span>
            <el-progress :stroke-width="18" :percentage="Number(processData.successRate || 0)" />
          </div>
        </div>
      </template>
    </CommonTable>
  </DialogContainer>
</template>

<script setup lang="ts">
import { reactive, ref, computed } from "vue"
import { Position } from "@element-plus/icons-vue"
import {
  CommonTable,
  DialogContainer,
  TableButton,
  PatientFilterFormItem,
  DaterangeFormItem,
  CommonSelectFormItem,
  BatchOperationButton,
  ComplexExportButton,
  PageContainer,
  SearchContainer
} from "@/base-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { useUserStore } from "@/stores"
import { formatDatetime, Message, extractErrorMsg } from "@/utils"
import { getPackageList, exportPackage, getPackageDetailList, retryPackage } from "../interface"
import { filterPropOptions } from "../print-config"
import { distributionColumns, statusOptions, distributionProcessColumns, menuId } from "./config"

const { hasOperationPermission } = useUserStore()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  patientFilterValue: "",
  patientFilterProp: "patientName",
  createdDatetime: "",
  packageStatus: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

const distributionProcessParams = ref({ wsid: "" })

/* ======================== 表格相关方法 ======================== */

const distributionTableRef = ref<InstanceType<typeof CommonTable>>()

// 表格中被选中项
const selectedRows = computed(() => distributionTableRef.value?.tableState?.selectedRows ?? [])

const processData = reactive({
  wsid: "",
  orderCode: "",
  packageStatus: "",
  packageStatusStr: "",
  successRate: ""
})

const statusValues = {
  UN_HANDLER: "",
  PACKAGE_ING: "warning",
  PACKAGE_FAIL: "danger",
  PACKAGE_COMPLETE: "success"
}

const processDataCallback = data => {
  Object.keys(data.data.data).forEach(key => {
    processData[key] = data.data.data[key]
  })
  data = data?.data?.data?.files
  data.forEach(item => {
    item.packTime = formatDatetime(item.packTime)
  })
  return data
}

const processVisible = ref(false)

// 查看进度
const viewProcess = row => {
  processVisible.value = true
  distributionProcessParams.value = { wsid: row.wsid }
}

//重新发送
const resend = () => {
  const obj = selectedRows.value.map(item => item.wsid)
  retryPackage(obj)
    .then(() => {
      Message.success("操作成功")
      distributionTableRef.value?.refreshTableData()
    })
    .catch(err => {
      Message.error(extractErrorMsg(err, "操作失败"))
    })
}
</script>

<style lang="less" scoped>
.process-header {
  display: flex;
  flex-direction: column;

  :deep(.el-form-item) {
    width: 100%;
  }
}

.process-container {
  height: 20px;
  display: flex;
  .process-label {
    margin-right: 8px;
  }
  .el-progress {
    width: 500px;
  }
}
</style>
