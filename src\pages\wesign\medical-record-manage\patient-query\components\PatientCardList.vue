<template>
  <div class="patient-card-list">
    <div class="patient-card-list__header">
      <slot name="header"></slot>
    </div>

    <div class="patient-card-list__content">
      <el-row v-if="patientCardState.dataList.length" v-bind="rowLayout">
        <template v-for="patient in patientCardState.dataList" :key="patient.wsid">
          <el-col v-bind="colLayout" class="patient-card-col">
            <el-popover trigger="hover" :show-arrow="false" :teleported="false" popper-class="patient-card-popover">
              <template #reference>
                <div class="patient-box">
                  <div class="patient-info-header">
                    <span class="span-ellipsis-text" style="color: #0a1633">{{ patient.patientName }}</span>
                    <span style="display: inline-flex; align-items: center; margin-left: 4px">
                      <i v-if="patient.patientSex === '女'" class="ri-women-line" style="color: #f36854"></i>
                      <i v-else class="ri-men-line" style="color: #3860f4"></i>
                    </span>
                    <span style="margin-left: 16px">{{ patient.age }}</span>
                    <span class="span-ellipsis-text" style="margin-left: 16px">
                      入院日期：{{ formatDatetime(patient.inHospitalDatetime) || "暂无" }}
                    </span>
                  </div>
                  <div class="detail-info-body">
                    <div>
                      <span>{{ patient.inHospitalDeptName }}</span>
                      <span v-if="patient.inpatientArea" style="margin-left: 8px">
                        {{ `${patient.inpatientArea}(${patient.bedNumber})` }}
                      </span>
                    </div>
                    <div class="common-ellipsis-text">患者编号：{{ patient.patientId }}</div>
                    <div style="color: #0a1633; font-weight: bold">
                      {{ patient.diagnosis }}
                    </div>
                    <div>
                      <span class="body-info-tag" :class="getTagColorClass(patient.nurseLevel)">
                        {{ patient.nurseLevel }}
                      </span>
                    </div>
                  </div>
                  <div class="other-info-footer common-ellipsis-text">
                    <span>
                      <span class="footer-text">主管医生：</span>
                      {{ patient.doctorInCharge }}
                    </span>
                    <span style="margin-left: 32px">
                      <span class="footer-text">责任护士：</span>
                      {{ patient.chargeNurse }}
                    </span>
                  </div>
                </div>
              </template>
              <!-- <div><el-button class="cart-btn" size="large" >补录</el-button></div> -->
              <div style="margin-top: 10px">
                <el-button size="large" @click="handleSync(patient)">同步</el-button>
              </div>
              <div style="margin-top: 10px">
                <el-button size="large" @click="toDetail(patient)">详情</el-button>
              </div>
            </el-popover>
          </el-col>
        </template>
      </el-row>
    </div>
  </div>

  <!-- 表格数据分页 -->
  <div style="margin-top: 20px">
    <el-pagination
      v-model:current-page="patientCardState.currentPage"
      v-model:page-size="patientCardState.pageSize"
      :page-sizes="[10, 25, 50, 100]"
      :background="true"
      layout="sizes, prev, pager, next, jumper, total"
      :total="patientCardState.totalElements"
    ></el-pagination>
  </div>
</template>

<script lang="ts" setup>
import { reactive, watch } from "vue"
import { formatDatetime } from "@/utils"

export interface PatientQueryCardProps {
  requestParams: any
  requestApi: (params: any) => any
}

const props = defineProps<PatientQueryCardProps>()

const emits = defineEmits(["toDetail", "handleSync"])

const colLayout = {
  xl: 6, // >1920px 4个
  lg: 6,
  md: 8,
  sm: 12,
  xs: 24
}

const rowLayout = {
  gutter: 16
}

interface PatientCardState {
  loading: boolean
  dataList: any[]
  limit: number
  offset: number
  totalElements: number
  totalPages: number
  pageSize: number
  currentPage: number
}

const patientCardState = reactive<PatientCardState>({
  loading: false,
  dataList: [],
  offset: 0,
  limit: 25,
  totalElements: 0,
  totalPages: 0,
  pageSize: 25,
  currentPage: 1
})

const getDataList = () => {
  patientCardState.loading = true
  props
    .requestApi({
      limit: patientCardState.limit,
      offset: patientCardState.pageSize * (patientCardState.currentPage - 1),
      ...props.requestParams
    })
    .then(res => {
      patientCardState.loading = false
      patientCardState.dataList = res.data.data.rows
      patientCardState.totalElements = res.data.data.page.totalElements
      patientCardState.totalPages = res.data.data.page.totalPages
      patientCardState.pageSize = res.data.data.page.size
      patientCardState.offset += 1
    })
    .catch(error => (patientCardState.loading = false))
}

const getTagColorClass = name => {
  switch (name) {
    case "一级护理":
      return "tag-red"
    case "二级护理":
      return "tag-orange"
    case "三级护理":
      return "tag-green"
    default:
      return ""
  }
}

const toDetail = row => {
  emits("toDetail", row)
}

const handleSync = row => {
  emits("handleSync", row)
}

watch(
  [() => props.requestParams, () => patientCardState.currentPage, () => patientCardState.pageSize],
  () => getDataList(),
  { immediate: true, deep: true }
)

defineExpose({
  getDataList
})
</script>

<style lang="less" scoped>
.patient-card-list {
  overflow-x: hidden;
  overflow-y: auto;
  height: 90%;
  position: relative;

  &__header {
    width: 100%;
    background: white;
    padding-bottom: 10px;
  }

  &__content {
    padding: 10px;
  }

  :deep(.patient-card-col) {
    position: relative;
  }

  :deep(.patient-card-popover) {
    top: 0 !important;
    left: 8px !important;
    display: flex;
    justify-content: center;
    align-items: center;
    width: calc(100% - 16px) !important;
    height: calc(100% - 16px) !important;
    background-color: rgb(10 22 51 / 15%);
    flex-direction: column;
    z-index: 99;
  }

  .patient-box {
    display: flex;
    padding: 0 16px;
    margin-bottom: 16px;
    font-size: 14px;
    font-family: "PingFang SC-Bold", "PingFang SC";
    color: #0a1633;
    border-radius: 4px;
    box-shadow: 0 4px 12px 0 rgb(10 22 51 / 15%);
    flex-direction: column;
    z-index: 100;

    .patient-info-header {
      display: flex;
      height: 40px;
      color: rgb(10 22 51 / 70%);
      line-height: 40px;
    }

    .detail-info-body {
      display: flex;
      justify-content: space-between;
      height: 132px;
      padding: 12px;
      margin-bottom: 12px;
      color: rgb(10 22 51 / 70%);
      background: rgb(56 96 244 / 4%);
      border-radius: 4px;
      box-sizing: border-box;
      flex-direction: column;

      .body-info-tag {
        display: inline-block;
        height: 24px;
        padding: 0 10px;
        border-radius: 35px;
        line-height: 24px;
      }

      .tag-green {
        color: #61b32a;
        background: #e7f2e8;
      }

      .tag-red {
        color: #fb2d00;
        background: #f2e7e7;
      }

      .tag-orange {
        color: #eb8d00;
        background: rgb(255 233 191);
      }
    }

    .other-info-footer {
      border-top: 1px solid #eeceef;
      height: 46px;
      line-height: 46px;

      .footer-text {
        font-size: 14px;
        font-family: "PingFang SC-Medium", "PingFang SC";
        font-weight: 500;
        color: rgb(10 22 51 / 60%);
      }
    }
  }
}

.common-ellipsis-text {
  overflow: hidden;
  width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.span-ellipsis-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
