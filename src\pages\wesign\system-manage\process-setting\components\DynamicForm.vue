<template>
  <Transition name="fade">
    <div v-show="props.visible" class="drawer-mask" @click="handleClose">
      <div class="custom-drawer" @click.stop>
        <div class="custom-drawer-header">
          <span>{{ props.title }}</span>
          <i class="ri-close-line" @click="handleClose"></i>
        </div>

        <div v-loading="uploadLoading" class="custom-drawer-content">
          <div
            v-for="(config, index) in configs"
            :key="index"
            class="custom-item"
            :class="{ relation: config?.formRadioRelation?.length > 0 }"
          >
            <div class="custom-item-label">{{ config?.formLabel }}</div>
            <!-- 简单单选，无附加项 -->
            <template v-if="config.formType === 'RADIO'">
              <el-radio-group v-model="config.value">
                <el-radio v-for="item in JSON.parse(config.formOptions)" :key="item.value" :label="item.value">
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </template>

            <!-- 单选，有附加项 -->
            <template v-if="config.formType === 'RADIO_PLUS'">
              <el-radio-group v-model="config.value" @change="handleUpdateRadioPlusValue(config)">
                <el-radio v-for="item in JSON.parse(config.formOptions)" :key="item.value" :label="item.value">
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
              <div v-if="getRadioPlusOptions(config.value, config?.extraConfig) > -1" class="extra-config">
                <!-- <div class="custom-item-label extra-label">
                  {{ JSON.parse(config.extraConfig)?.[getRadioPlusOptions(config.value, config?.extraConfig)]?.label }}
                </div> -->
                <div
                  v-if="
                    JSON.parse(config.extraConfig)?.[getRadioPlusOptions(config.value, config?.extraConfig)]
                      ?.formType === 'TEXTAREA'
                  "
                >
                  <!-- <el-input
                    v-model="config.extraValue"
                    maxlength="2000"
                    show-word-limit
                    autosize
                    style="width: 95%"
                    type="textarea"
                  /> -->
                  <el-link type="primary" :icon="Edit" @click.stop="openCodeDialog(config)">点击输入代码块</el-link>
                </div>

                <div
                  v-if="
                    JSON.parse(config.extraConfig)?.[getRadioPlusOptions(config.value, config?.extraConfig)]
                      ?.formType === 'UPLOAD_FILE'
                  "
                  class="upload-file"
                >
                  <div v-if="/WSID_FILE/.test(config.extraValue)" class="uploaded-content">
                    <OverflowTooltip :content="config?.extraName" max-width="70%" />
                    <span @click="handleUploadFile(config, 'update')">重新上传</span>
                  </div>
                  <div v-else class="upload-desc" @click="handleUploadFile(config)">
                    <i class="ri-upload-cloud-line"></i>
                    <span>上传</span>
                  </div>
                </div>
              </div>
            </template>

            <!-- 上传图片 -->
            <template v-if="config.formType === 'IMAGE'">
              <div class="upload-img">
                <div class="upload-tips">支持类型PNG，尺寸200*200</div>
                <div class="img-container" @click="handleUploadImage(config)">
                  <img v-if="config?.imageSrc" class="seal-img" :src="config?.imageSrc" />
                  <div v-else class="img-container-desc">
                    <i class="ri-add-line add-btn"></i>
                    <span>上传图片</span>
                  </div>
                </div>
              </div>
            </template>

            <!-- 下拉 -->
            <template v-if="config.formType === 'SELECT'">
              <el-select
                v-model="config.value"
                style="width: 70%"
                :multiple="config.businessKey.includes('VIEW_DOC')"
                :collapse-tags="config.businessKey.includes('VIEW_DOC')"
                @change="$val => handleSelectChange($val, config)"
              >
                <el-option
                  v-for="item in JSON.parse(config.formOptions)"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </template>

            <!-- 输入 -->
            <template v-if="config.formType === 'TEXT'">
              <el-input
                v-model="config.value"
                clearable
                :placeholder="`请输入${config?.formLabel}`"
                style="width: 70%"
              />
            </template>

            <!-- 代码块 -->
            <template v-if="config.formType === 'CODE_SOURCE'">
              <el-link type="primary" :icon="Edit" @click.stop="openCodeDialog(config)">点击输入代码块</el-link>
            </template>

            <div
              v-if="config?.errorMsg"
              :style="{
                color: 'red',
                fontSize: '12px',
                marginTop: '5px'
              }"
            >
              * {{ config?.errorMsg }}
            </div>
          </div>
        </div>
      </div>
      <DialogContainer
        v-model:visible="openCodeDialogVisible"
        title="输入代码"
        :width="1000"
        :confirm-callback="confirmSetCode"
        @click.stop
      >
        <div class="code-container">
          <Codemirror
            v-model="extraValue"
            placeholder="请输入代码块"
            :tab-size="10"
            :extensions="extensions"
            :style="{ height: '100%', width: '100%' }"
          />
        </div>
      </DialogContainer>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue"
import { java } from "@codemirror/lang-java"
import { Codemirror } from "vue-codemirror"
import { Edit } from "@element-plus/icons-vue"
import { OverflowTooltip, DialogContainer } from "@/base-components"
import { myTheme } from "../config"
import { uploadSystemFile } from "@/interfaces"
import { Message, selectFile, blobToBase64, toastError } from "@/utils"

interface Props {
  configs: any
  title: string
  visible: boolean
  handleClose: () => void
  businessNodeCode?: string
  activeBusinessProcessData: Array<Record<string, any>>
}
const props = withDefaults(defineProps<Props>(), {
  configs: [],
  title: "",
  visible: false
})

const emits = defineEmits(["update:configs"])
const configs = computed({
  get: () => props.configs,
  set: val => emits("update:configs", val)
})

// 切换复杂单选项时，需要将额外值置空
const handleUpdateRadioPlusValue = config => {
  // if (config.value !== "3") config.extraValue = null
}

//有额外配置的单选，通过选中的值，获取额外配置项
const getRadioPlusOptions = (value: string, extraConfig: string) => {
  if (!extraConfig) return
  const realExtraConfig = JSON.parse(extraConfig)
  const index = realExtraConfig?.findIndex((item: Record<string, any>) => item.radioValue === value)
  return index
}

/*=======================================上传=======================================*/

const uploadLoading = ref(false)
//上传文件
async function handleUploadFile(config: Record<string, any>, uploadStatus?: string) {
  const type = ".jar,.zip"
  await selectFile(type, 1024 * 1024 * 30).then(async file => {
    uploadLoading.value = true

    await uploadSystemFile({ file: file })
      .then(res => {
        config.extraValue = res.data.data.fileWsid
        config.extraName = res.data.data.name
        Message.success("上传成功")
      })
      .catch(error => {
        toastError(error, "上传失败")
      })
      .finally(() => {
        uploadLoading.value = false
      })
  })
}

// 上传图片
async function handleUploadImage(config: Record<string, any>) {
  const type = ".png"
  await selectFile(type, 1024 * 1024 * 30).then(async file => {
    const reader = new FileReader()
    reader.readAsDataURL(file as Blob)
    reader.onload = function (e) {
      const img = new Image()
      img.src = e.target?.result as string
      img.onload = async function () {
        config.imageSrc = (await blobToBase64(file as Blob)) as string
        uploadLoading.value = true

        await uploadSystemFile({ file: file })
          .then(res => {
            config.value = res.data.data.fileWsid
            Message.success("上传图片成功")
          })
          .catch(error => {
            toastError(error, "上传图片失败")
          })
          .finally(() => {
            uploadLoading.value = false
          })
      }
    }
  })
}
/*=======================================代码输入=======================================*/

const openCodeDialogVisible = ref(false)
const extraValue = ref("")

const currentConfigKey = ref()

// 打开代码块输入
const openCodeDialog = config => {
  currentConfigKey.value = config.businessKey
  openCodeDialogVisible.value = true
  extraValue.value = config?.extraValue || config?.value
}

// 保存代码
const confirmSetCode = () => {
  if (!configs.value.find(item => item.businessKey === currentConfigKey.value).extraValue) {
    configs.value.find(item => item.businessKey === currentConfigKey.value).value = extraValue.value
  } else {
    configs.value.find(item => item.businessKey === currentConfigKey.value).extraValue = extraValue.value
  }
  console.log(`output->configs`, configs.value)
  openCodeDialogVisible.value = false
}

const extensions = [java(), myTheme]

watch(
  () => configs.value,
  (newVal, oldVal) => {
    newVal.forEach(item => {
      validateValue(item)
    })
  },
  { deep: true, immediate: true }
)

// 校验值
const validateValue = config => {
  const formRadioRelations = configs.value.map(item => {
    return {
      [item.businessKey]: item?.formRadioRelation
    }
  })
  // 将formRadioRelations合并成一个对象
  const formRadioRelationsObj = formRadioRelations.reduce((prev, next) => {
    return { ...prev, ...next }
  }, {})

  const formRadioRelationsArr = configs.value.map(item => item?.formRadioRelation).flat()
  if (formRadioRelationsArr.includes(config?.businessKey)) {
    const targetKey = Object.keys(formRadioRelationsObj).find(key =>
      formRadioRelationsObj[key]?.includes(config?.businessKey)
    )
    const targetConfig = configs.value.find(config => config?.businessKey === targetKey)
    //如果targetConfig的value是'0',则item的value可以为空
    if (targetConfig?.value === "1") {
      if (!config?.value) {
        config.errorMsg = "请先设置" + config?.formLabel
      } else {
        config.errorMsg = ""
      }
    } else {
      config.errorMsg = ""
    }
  }
}
const handleSelectChange = (val, config) => {
  if (config?.formRadioRelation.length && val) {
    config?.formRadioRelation.forEach(radioKey => {
      configs.value.find(item => item?.businessKey === radioKey).value = ""
    })
  }
}
</script>

<style scoped lang="less">
// 声明 drawer-in 关键帧
@keyframes rtl-drawer-in {
  0% {
    transform: translate(100%, 0px);
  }

  100% {
    transform: translate(0px, 0px);
  }
}
// 声明 drawer-out 关键帧
@keyframes rtl-drawer-out {
  0% {
    transform: translate(0px, 0px);
  }

  100% {
    transform: translate(100%, 0px);
  }
}

.fade-enter-active,
.fade-leave-active {
  //执行rtl-drawer-in动画
  animation: rtl-drawer-in 0.3s;
}
.fade-enter,
.fade-leave-to {
  //执行rtl-drawer-out动画
  animation: rtl-drawer-out 0.1s;
}

.drawer-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: transparent;
  z-index: 999;
  width: 100%;
  height: 100%;

  .custom-drawer {
    position: absolute;
    right: 0px;
    top: 0;
    width: 400px;
    height: 100%;
    background-color: #fff;
    box-shadow: -2px 0 8px 0 rgba(0, 35, 114, 0.1);
    z-index: 1000;
    border-left: 1px solid #ebeef5;
    overflow-y: auto;

    .custom-drawer-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #ebeef5;
      padding: 20px 0;
      span {
        font-size: 16px;
        font-weight: 500;
        color: #0a1633;
        padding-left: 10px;
      }
      i {
        font-size: 20px;
        color: #b3b6ba;
        cursor: pointer;
        padding-right: 20px;
      }
    }

    .custom-drawer-content {
      // height: calc(100% - 180px);
      overflow: auto;
      padding: 20px;
      display: flex;
      flex-wrap: wrap;

      .custom-item {
        width: 100%;
        margin-bottom: 24px;
        .custom-item-label {
          font-weight: bold;
          font-size: 14px;
          padding-bottom: 16px;
          line-height: 22px;
        }
        .extra-label {
          padding-bottom: 0;
        }
        .upload-desc {
          font-size: 12px;
          cursor: pointer;
          i {
            font-size: 14px;
            color: var(--el-color-primary);
          }
          span:hover {
            color: var(--el-color-primary);
          }
        }
        .extra-config {
          width: 100%;
        }
      }
      .upload-img {
        :deep(.el-form-item__content) {
          flex-direction: column;
          align-items: start;
        }
        .upload-tips {
          color: #999;
        }
        .img-container {
          border: 1px dashed var(--el-border-color);
          padding: 5px;
          height: fit-content;
          line-height: 1;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          min-width: 100px;
          min-height: 100px;
          box-sizing: border-box;

          .img-container-desc {
            display: flex;
            flex-direction: column;
            align-items: center;
            row-gap: 5px;
            color: #999;
            width: 200px;
          }

          .seal-img {
            width: 200px;
            height: 200px;
            object-fit: contain;
          }

          .add-btn {
            width: 24px;
            height: 24px;
            font-size: 24px;
          }
        }
      }
      .upload-file {
        background-color: #eff3f7;
        padding: 10px;
        border-radius: 4px;
        border: 1px dashed #409eff;
        width: 80%;
        .uploaded-content {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .uploaded-text {
            display: inline-block;
            width: 70%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          span {
            cursor: pointer;
            &:hover {
              color: var(--el-color-primary);
            }
          }
        }
      }
    }
  }
}
:deep(.cm-editor) {
  border: 1px solid #dcdfe6;
}

:deep(.cm-focused) {
  outline: none !important;
}

:deep(.cm-lineNumbers) {
  border-right: 1px solid #dcdfe6;
}

.code-container {
  height: 75vh;
}
.relation {
  width: 50% !important;
}
</style>
