import type { TableColumnItem } from "@/types"

export const menuId = "/print/record"

export const recordColumns: Array<TableColumnItem> = [
  { type: "selection", width: 45, fixed: "left", must: true },
  { prop: "createdDatetime", label: "打印时间", minWidth: 200, sortable: true, must: true },
  { prop: "operator", label: "操作人", minWidth: 100 },
  { prop: "printTypeName", label: "打印类型", minWidth: 100 },
  { prop: "duplicateNum", label: "复印份数", minWidth: 100, sortable: true },
  { prop: "pageCount", label: "总页数", minWidth: 100, sortable: true },
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 100, must: true },
  { prop: "outHospitalDatetime", label: "出院日期", minWidth: 180, sortable: true, must: true },
  { prop: "operation", label: "操作", width: 100, fixed: "right", must: true }
]
