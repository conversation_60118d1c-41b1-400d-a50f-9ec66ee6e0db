<template>
  <PageContainer v-loading="loading" separate>
    <template #search>
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
        ></PatientFilterFormItem>

        <DaterangeFormItem v-model:model-value="searchFormState.outHospitalDatetime" label="出院日期" />
        <DaterangeFormItem v-model:model-value="searchFormState.createdDatetime" label="操作时间" />

        <el-form-item label="类型">
          <el-select v-model="searchFormState.type" clearable>
            <el-option
              v-for="item in DocumentPrintRecordTypeOptions"
              :key="item.label"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="来源">
          <el-select v-model="searchFormState.source" clearable>
            <el-option
              v-for="item in DocumentPrintRecordSourceOptions"
              :key="item.label"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="printExportTableRef"
        :table-columns="printExportReportColumns"
        :request-api="getPrintExportReportListApi"
        :request-params="searchFilterParams"
      >
        <template #header>
          <!-- <ExportButton
            :export-api="exportPrintExportReportApi"
            :export-params="exportParams"
            :file-name="`打印导出报表_${getCurrentDate()}.xlsx`"
            file-type="application/vnd.ms-excel"
          >
            导出
          </ExportButton> -->
          <el-popover
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Export)"
            placement="bottom"
            width="210"
            trigger="hover"
          >
            <template #reference>
              <el-button :icon="Download">导出</el-button>
            </template>

            <PopoverButton
              :tip="`(当前选中${printExportTableRef?.tableState.selectedRows.length}条数据)`"
              :center="false"
              @click="handleExport('selected')"
            >
              导出选中
            </PopoverButton>
            <el-tooltip effect="dark" content="单次最大数据导出量10000条" placement="bottom">
              <PopoverButton
                :center="false"
                :tip="`(共${printExportTableRef?.tableState.total}条数据)`"
                @click="handleExport('all')"
              >
                导出全部
              </PopoverButton>
            </el-tooltip>
          </el-popover>
        </template>
        <template #createdDatetime="{ row }">{{ formatDatetime(row.createdDatetime) }}</template>
        <template #outHospitalDatetime="{ row }">{{ formatDatetime(row.outHospitalDatetime) }}</template>
        <template #typeEnum="{ row }">{{ getDocumentPrintRecordType(row.typeEnum) }}</template>
        <template #sourceEnum="{ row }">{{ getDocumentPrintRecordSource(row.sourceEnum) }}</template>
        <template #operation="{ row }">
          <TableButton @click="handleDetail(row)">详情</TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>

  <!-- 详情弹窗 -->
  <PrintExportDetailDialog ref="printExportDetailDialogRef" :document="detailDocuments"></PrintExportDetailDialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue"
import { Download } from "@element-plus/icons-vue"
import {
  PageContainer,
  SearchContainer,
  PatientFilterFormItem,
  DaterangeFormItem,
  CommonTable,
  TableButton,
  PopoverButton
} from "@/base-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { useUserStore } from "@/stores"
import { formatDatetime, Message, downloadFile, toastError } from "@/utils"
import { getCurrentDate } from "../config"
import { getPrintExportReportListApi, exportPrintExportReportApi } from "../interface"
import PrintExportDetailDialog from "./PrintExportDetailDialog.vue"
import {
  printExportReportColumns,
  DocumentPrintRecordTypeOptions,
  DocumentPrintRecordSourceOptions,
  getDocumentPrintRecordType,
  getDocumentPrintRecordSource,
  menuId
} from "./config"

const { hasOperationPermission } = useUserStore()

const searchFormState = reactive({
  patientFilterValue: "",
  patientFilterProp: "patientName",
  outHospitalDatetime: "",
  createdDatetime: "",
  type: "",
  source: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

const searchFilterParams = computed(() => {
  return { filters: searchParams.filters }
})

const printExportTableRef = ref()
const selectedRows = computed(() => printExportTableRef.value?.tableState?.selectedRows ?? [])

// 导出数据params
const exportParams = computed(() => {
  const wsids = printExportTableRef.value?.tableState.selectedRows.map(item => item.id)
  return {
    wsids: wsids,
    filters: searchParams.filters
  }
})

/*=============== 详情弹窗 ==============*/
const printExportDetailDialogRef = ref()

// 每条数据对应的详情数据
const detailDocuments = ref([])

function handleDetail(row) {
  detailDocuments.value = row.document || []
  printExportDetailDialogRef.value?.openDialog()
}

/*===================== 导出 =====================*/
const loading = ref(false)
const handleExport = (type: string) => {
  if (type === "all") {
    if (!printExportTableRef.value?.tableState.total) {
      return Message.warning("没有数据可以导出")
    }
    exportParams.value.wsids = []
  } else {
    if (!selectedRows.value.length) return Message.warning("请至少勾选一条数据")
  }
  if (loading.value) return
  loading.value = true
  exportPrintExportReportApi(exportParams.value)
    .then(res => {
      downloadFile({ fileData: res.data, fileType: "application/vnd.ms-excel", fileName: "导出数据.xlsx" })
      Message.success("导出数据成功！")
      loading.value = false
    })
    .catch(err => {
      toastError(err, "导出失败")
      loading.value = false
    })
}
</script>
