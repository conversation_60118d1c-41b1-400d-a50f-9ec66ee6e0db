import { EditorView } from "@codemirror/view"

export const tabsRouterList = [
  { label: "审批流程", path: "/system-manage/process-setting/approval-process" },
  { label: "业务流程", path: "/system-manage/process-setting/business-process" }
]

// 简单配置枚举
export const simpleConfigEnum = {
  ARCHIVE_NODE_TIME: "归档时间",
  PRINT_READY: "病案打印",
  RECALL_READY: "病案召回"
}

export const myTheme = EditorView.theme(
  {
    // 输入的字体颜色
    "&": {
      color: "#0052D9",
      backgroundColor: "#FFFFFF"
    },
    ".cm-content": {
      caretColor: "#0052D9"
    },
    // 激活背景色
    ".cm-activeLine": {
      backgroundColor: "#FFFFDD"
    },
    // 激活序列的背景色
    ".cm-activeLineGutter": {
      backgroundColor: "#FAFAFA"
    },
    //光标的颜色
    "&.cm-focused .cm-cursor": {
      borderLeftColor: "#0052D9"
    },
    // 选中的状态
    "&.cm-focused .cm-selectionBackground, ::selection": {
      backgroundColor: "#0052D9",
      color: "#dcdfe6"
    },
    // 左侧侧边栏的颜色
    ".cm-gutters": {
      backgroundColor: "#FFFFFF",
      color: "#ddd", //侧边栏文字颜色
      border: "none"
    }
  },
  { dark: true }
)
