<template>
  <PageContainer separate>
    <template #search>
      <SearchForm
        :form-config="borrowBlacklistSearchFormConfig"
        :form-state="searchFormState"
        @query-btn-click="handleQuery"
        @reset-btn-click="handleReset"
      />
    </template>

    <template #table>
      <CommonTable
        ref="tableRef"
        :table-columns="borrowBlacklistColumns"
        :request-api="getBorrowBlacklistApi"
        :request-params="searchParams"
      >
        <template #header>
          <div>
            <AddButton @click="openDialog">新增</AddButton>
          </div>
        </template>
        <template #createdDatetime="{ row }">
          <span>{{ formatDatetime(row.createdDatetime) }}</span>
        </template>
        <template #operate="{ row }">
          <TableButton @click="handleDelete(row)">删除</TableButton>
        </template>
      </CommonTable>
    </template>
    <DialogContainer
      v-model:visible="borrowBlacklistDialogState.visible"
      title="新增"
      :width="450"
      :confirm-callback="handleConfirm"
    >
      <el-form ref="ruleFormRef" :model="borrowBlacklistDialogState.form" :rules="rules" label-width="50">
        <el-form-item label="用户" prop="user">
          <el-select
            v-model="borrowBlacklistDialogState.form.user"
            filterable
            clearable
            placeholder="请输入姓名/工号查询"
            :filter-method="filterUserData"
            :loading="loading"
            value-key="wsid"
          >
            <el-option
              v-for="item in borrowBlacklistDialogState.usersData"
              :key="item.wsid"
              :value="item"
              :label="item.realName"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="原因">
          <el-input
            v-model="borrowBlacklistDialogState.form.reason"
            type="textarea"
            rows="3"
            show-word-limit
            minlength="0"
            maxlength="100"
            resize="none"
          ></el-input>
          <span class="tip-text">注：添加用户后不能发起借阅申请，已借阅的不允许查看</span>
        </el-form-item>
      </el-form>
    </DialogContainer>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from "vue"
import { debounce } from "lodash-es"
import { PageContainer, CommonTable, AddButton, TableButton, DialogContainer } from "@/base-components"
import { SearchForm } from "@/page-components"
import { useTableSearch } from "@/hooks"
import { getUserListApi } from "@/interfaces"
import { Message, SystemPrompt, extractErrorMsg, formatDatetime } from "@/utils"
import { borrowBlacklistSearchFormConfig, borrowBlacklistColumns } from "./config"
import { getBorrowBlacklistApi, addBorrowBlacklistApi, deleteBorrowBlacklistApi } from "./interface"

const loading = ref(false)
const ruleFormRef = ref()
const tableRef = ref()
// 搜索表单状态
const searchFormState = reactive({
  queryJobId: "",
  queryRealName: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

// 表单校验规则
const rules = reactive({
  user: [{ required: true, message: "请选择用户", trigger: "change" }]
})

// 借阅黑名单弹窗状态
const borrowBlacklistDialogState = reactive({
  visible: false,
  keyword: "", // 搜索关键字
  usersData: [] as any[],
  form: {
    user: "",
    reason: ""
  }
})

onMounted(() => {
  getUserData()
})

// 获取用户数据
function getUserData() {
  loading.value = true
  getUserListApi({
    offset: 0,
    limit: 20,
    filters: `item=${borrowBlacklistDialogState.keyword}`
  }).then(res => {
    borrowBlacklistDialogState.usersData = res.data.data?.rows
    loading.value = false
  })
}

// 根据搜索的关键字获取用户数据
function filterUserData(val) {
  borrowBlacklistDialogState.keyword = val
  const searchDebounce = debounce(getUserData, 500)
  searchDebounce()
}

// 打开弹窗
function openDialog() {
  borrowBlacklistDialogState.form.user = ""
  borrowBlacklistDialogState.form.reason = ""
  borrowBlacklistDialogState.visible = true
}

// 删除操作
function handleDelete(row) {
  SystemPrompt("确定要删除吗", "warning").then(() => {
    try {
      deleteBorrowBlacklistApi(row.id).then(() => {
        tableRef?.value.refreshTableData()
        Message.success("删除成功")
      })
    } catch {
      Message.error("删除失败")
    }
  })
}

// 确认弹窗操作
function handleConfirm() {
  if (!ruleFormRef.value) return
  ruleFormRef.value.validate(val => {
    if (!val) return
    const user = JSON.parse(JSON.stringify(borrowBlacklistDialogState.form.user))
    const params = {
      userWsid: user.userWsid,
      realName: user.realName,
      jobId: user.jobId,
      reason: borrowBlacklistDialogState.form.reason
    }
    addBorrowBlacklistApi(params)
      .then(() => {
        Message.success("新增成功")
        tableRef?.value.refreshTableData()
      })
      .catch(err => {
        const errorMessage = extractErrorMsg(err)
        Message.error(errorMessage)
      })
    borrowBlacklistDialogState.visible = false
  })
}
</script>

<style lang="less" scoped>
.tip-text {
  font-size: 12px;
  padding-top: 10px;
  color: #f56c6c;
}
</style>
