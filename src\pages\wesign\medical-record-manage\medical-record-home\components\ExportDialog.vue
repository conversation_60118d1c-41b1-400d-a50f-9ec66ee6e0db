<template>
  <DialogContainer
    :visible="state.visible"
    title="导出数据"
    :cancel-callback="close"
    :close-callback="close"
    :confirm-callback="confirmExport"
    :confirm-loading="state.loading"
  >
    <div v-loading="state.loading" class="dialog-body">
      <div class="select-data-num">
        共
        {{ $props.exportAll ? $props.total : $props.selectedRows.length }}
        条数据
      </div>
      <div>
        <div class="export-select-wrapper">
          <div class="select-choice">请选择导出字段</div>
          <el-checkbox v-model="state.checkAll" :indeterminate="state.indeterminate" @change="handleExportCheckAll">
            全选
          </el-checkbox>
          <el-checkbox-group v-model="state.checkedOptions" @change="handleExportOptionCheck">
            <el-checkbox v-for="option in state.exportOptions" :key="option.prop" :label="option.prop">
              {{ option.label }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
    </div>
  </DialogContainer>
</template>

<script setup lang="ts">
import { reactive, onMounted } from "vue"
import { DialogContainer } from "@/base-components"
import { Message, downloadFile, toastError } from "@/utils"
import { exportRecordApi } from "../../interface"
import { recordTableColumns } from "../config"

interface ExportDialogProps {
  total: number
  selectedRows: Record<string, any>
  filters: string // 搜索的参数
  exportAll: boolean // 是否导出全部
}

const props = withDefaults(defineProps<ExportDialogProps>(), {
  total: 0
})

const emits = defineEmits(["close"])

const state = reactive({
  visible: false,
  loading: false,
  exportAll: false, // 是否导出所有
  checkAll: false, // 是否选中所有字段
  exportOptions: [] as any[], // 导出选项
  checkedOptions: [] as string[], // 所选的导出项
  indeterminate: false // 全选checkbox属性
})

onMounted(() => initExportOptions())

const initExportOptions = () => {
  const exceptFiledList = ["selection", "index", "expand"]
  const exportOptions = recordTableColumns.filter(column => {
    if (!exceptFiledList.includes(column.type as string) && column.prop !== "operation" && column.isShow !== false) {
      return column
    }
  })
  state.exportOptions = exportOptions // 所有项
  state.checkedOptions = exportOptions.map(option => option.prop || "") // 选中的项
  state.checkAll = true
}

// 全选
const handleExportCheckAll = checked => {
  state.checkedOptions = checked ? state.exportOptions.map(option => option.prop || "") : []
  state.indeterminate = false
}

// 选中单条
const handleExportOptionCheck = (checkedList: any[]) => {
  state.checkAll = checkedList.length === state.exportOptions.length
  state.indeterminate = checkedList.length > 0 && checkedList.length < state.exportOptions.length
}

// 确定导出
const confirmExport = () => {
  if (state.loading) {
    Message.warning("正在导出，请稍候")
    return
  }
  state.loading = true
  let recordWsidList = props.selectedRows.map(row => row.wsid)
  const fields = state.checkedOptions.join(",")
  // 导出全部 id列表传空
  if (props.exportAll) {
    recordWsidList = []
  }
  exportRecordApi({ recordWsidList, filters: props.filters, fields })
    .then(res => {
      downloadFile({ fileData: res.data, fileType: "application/vnd.ms-excel", fileName: "导出数据.xlsx" })
      Message.success("导出数据成功！")
      state.loading = false
      close()
    })
    .catch(error => {
      toastError(error)
    })
}

/* ============= 暴露给父组件 ============== */

const open = () => {
  state.visible = true
}

const close = () => {
  state.visible = false
}

defineExpose({ open, close })
</script>
