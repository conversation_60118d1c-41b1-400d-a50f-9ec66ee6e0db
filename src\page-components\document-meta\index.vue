<template>
  <div style="position: relative; height: 100%" :style="{ marginRight: state.isCollapse ? '0px' : '0px' }">
    <div class="record-metadata" :style="{ width: state.isCollapse ? '0px' : '296px' }">
      <el-tabs v-model="state.selectValue" class="demo-tabs">
        <el-tab-pane
          v-if="props.mode === 'both' || props.mode === DocumentMetaTypeEnum.BasicInfo"
          label="基本信息"
          :name="DocumentMetaTypeEnum.BasicInfo"
        ></el-tab-pane>
        <el-tab-pane
          v-if="props.mode === 'both' || props.mode === DocumentMetaTypeEnum.SignVerificationInfo"
          label="验签信息"
          :name="DocumentMetaTypeEnum.SignVerificationInfo"
        ></el-tab-pane>
      </el-tabs>

      <!-- 文档元数据 -->
      <template v-if="state.selectValue === DocumentMetaTypeEnum.BasicInfo">
        <div class="content">
          <div v-for="(item, index) in state.metadata" :key="index" class="content-item">
            <div class="content-item-title">{{ item.label }}：</div>
            <div class="content-item-content">
              <span v-if="item.value !== ''">{{ item.value }}</span>
              <span v-else>{{ "<无>" }}</span>
            </div>
          </div>
        </div>
      </template>

      <!-- 签名验签信息 -->
      <div
        v-if="state.selectValue === DocumentMetaTypeEnum.SignVerificationInfo"
        v-loading="state.loading"
        class="sign-verification"
      >
        <div class="sign-verification__title">
          <VerifyStatus
            class="verify-status sign-verification__text"
            :verify-envelope-request-status="true"
            :verify-sign-data="state.selectedFileVerifyData"
          />
          <span v-if="state.selectedFileVerifyData?.totalSignFields > 0" class="sign-verification-total">
            全部签名共计{{ state.selectedFileVerifyData?.totalSignFields || 0 }}个
          </span>
        </div>

        <!-- 签名列表 -->
        <div v-if="state.selectedFileVerifyData?.verifyResults" class="sign-list">
          <div v-for="(verify, index) in state.selectedFileVerifyData?.verifyResults" :key="index" class="sign-item">
            <div class="sign-item__title">
              <span class="sign-item__text">
                <i :class="getVerifierIcon(verify)"></i>
                由{{ verify?.signInfo?.signUser }}签名
              </span>
              <span class="sign-item__btn" @click="viewSignData(verify)">查看详情</span>
            </div>
            <div class="sign-item__description">
              签名时间：{{ formatDatetime(verify?.certInfo?.tsaResult?.timeStampDate) }}
            </div>
          </div>
        </div>
        <el-empty v-else description="无任何签名" class="sign-verification__empty" />
      </div>

      <!-- <el-button
        v-if="
          !props.disabledDownload &&
          state.selectedFileVerifyData?.verifyResults &&
          state.selectedFileVerifyData?.verifyResults?.length > 0
        "
        class="export-credence"
        type="primary"
        @click="handleExportCredence"
      >
        下载凭证
      </el-button> -->

      <!-- 查看签名详情弹窗 -->
      <SignVerifyDialog v-model:visible="signDialogState.visible" :sign-data="signDialogState.selectedFileVerifyData" />

      <ProgressDialog
        v-model:visible="progressDialogState.visible"
        title="下载"
        detail="文件正在下载中，请稍候..."
        :progress="progress"
      />
    </div>
    <div
      v-if="props.canCollapse"
      class="collapse-icon"
      :style="{
        boxShadow: state.isCollapse
          ? '0px 0px 3px 1px rgba(0, 35, 114, 0.1)'
          : ' 3px 0px 3px 1px rgba(0, 35, 114, 0.1)',
        left: state.isCollapse ? '-4px' : '-24px'
      }"
      @click="() => (state.isCollapse = !state.isCollapse)"
    >
      <el-icon v-if="!state.isCollapse"><ArrowRight /></el-icon>
      <el-icon v-if="state.isCollapse"><ArrowLeft /></el-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch, ref } from "vue"
import { ArrowLeft, ArrowRight } from "@element-plus/icons-vue"
import { ProgressDialog } from "@/page-components"
import { useVirtualProgress } from "@/hooks"
import { getDocumentSignInfoApi, downloadArchiveCertificateApi, queryDictionaryDetailByGroup } from "@/interfaces"
import { DocumentMetaTypeEnum } from "@/types"
import { formatDatetime, toastError, downloadFile, Message } from "@/utils"
import SignVerifyDialog from "./component/SignVerifyDialog.vue"
import VerifyStatus from "./component/VerifyStatus.vue"
import type { BaseOptionItem } from "@/types"

interface PropsType {
  baseInfoData: Array<Record<string, any>>
  fileWsid: string
  documentWsid?: string
  disabledDownload?: boolean
  mode?: DocumentMetaTypeEnum.BasicInfo | DocumentMetaTypeEnum.SignVerificationInfo | "both"
  canCollapse?: boolean
}

const props = withDefaults(defineProps<PropsType>(), {
  baseInfoData: () => [],
  fileWsid: "",
  mode: "both",
  documentWsid: "",
  disabledDownload: false,
  canCollapse: false //是否可收缩
})

const documentMetaTypeOptions = [
  { label: "文档元数据", value: DocumentMetaTypeEnum.BasicInfo },
  { label: "签名验签信息", value: DocumentMetaTypeEnum.SignVerificationInfo }
]

const state = reactive({
  metadata: [] as BaseOptionItem[],
  selectValue: DocumentMetaTypeEnum.BasicInfo,
  selectOption: documentMetaTypeOptions,
  selectedFileVerifyData: null as null | Record<string, any>,
  loading: false,
  isCollapse: false
})

watch(
  () => props.mode,
  val => {
    if (val === "both") state.selectValue = state.selectOption[0].value
    else if (val === DocumentMetaTypeEnum.BasicInfo) state.selectValue = DocumentMetaTypeEnum.BasicInfo
    else if (val === DocumentMetaTypeEnum.SignVerificationInfo)
      state.selectValue = DocumentMetaTypeEnum.SignVerificationInfo
  },
  { immediate: true }
)

const getRecordSignInfo = () => {
  state.loading = true
  getDocumentSignInfoApi({ fileWsid: props?.fileWsid })
    .then(res => {
      state.selectedFileVerifyData = res.data.verifiers.find(item => item.fileWsid === props?.fileWsid)
    })
    .catch(err => {
      state.selectedFileVerifyData = null
      toastError(err, "获取验签信息失败")
    })
    .finally(() => {
      state.loading = false
    })
}

watch(
  () => props.fileWsid,
  () => {
    if (!props.fileWsid) {
      state.selectedFileVerifyData = null
      return
    }
    getRecordSignInfo()
  }
)

const signDialogState = reactive({
  visible: false,
  selectedFileVerifyData: {} as any
})

// 获取签名状态图标
const getVerifierIcon = (verifier?: any) => {
  if (!verifier) return "ri-error-warning-fill error"
  if (verifier?.signInfo.modified) return "ri-error-warning-fill error"
  if (verifier?.certInfo.certInvalid || !verifier?.certInfo.certAgainstRootValid) return "ri-error-warning-fill warning"
  return "ri-shield-check-fill success"
}

const viewSignData = verify => {
  signDialogState.selectedFileVerifyData = verify
  signDialogState.visible = true
}

// 脱敏首页字段
const formatFirstPageFields = async fields => {
  if (!fields.length) return []
  const keywords = await queryDictionaryDetailByGroup({ groupKey: "PRIVACY_KEYWORDS" })
  for (let field of fields) {
    const keyword = keywords.find(
      keyword =>
        keyword.status === "ENABLE" &&
        (field.fieldTitle.includes(keyword.value) || keyword.value.includes(field.fieldTitle))
    )
    if (!keyword) continue
    let hiddenValue = ""
    for (let i = 0; i < field.fieldValue.length; i++) {
      hiddenValue += "*"
    }
    field.fieldValue = hiddenValue
  }
  return fields
}

watch(
  () => props.baseInfoData,
  async val => {
    if (!val.length) return
    val = await formatFirstPageFields(val)
    const arr: any = []
    val.forEach((item: any) => {
      const obj = {}
      obj["label"] = item.fieldTitle
      obj["value"] = item.fieldValue ? item.fieldValue : "--"
      arr.push(obj)
    })
    state.metadata = arr
  },
  {
    immediate: true,
    deep: true
  }
)

/*==================下载凭证==================*/
const progressDialogState = reactive({
  visible: false,
  error: "",
  progress: 0
})
const { progress, startProgress, endProgress } = useVirtualProgress()
// 下载签名凭证
const handleExportCredence = () => {
  progressDialogState.visible = true
  startProgress()
  downloadArchiveCertificateApi(props.documentWsid)
    .then(res => {
      let fileName = state?.selectedFileVerifyData?.fileInfoResult?.fileName
      // 如果文件名有 .pdf后缀，去掉 .pdf后缀
      if (fileName?.endsWith(".pdf")) {
        fileName = fileName.slice(0, fileName.length - 4)
      }
      endProgress()
      downloadFile({
        fileData: res.data,
        fileType: "application/pdf",
        fileName: `《${fileName}》签署电子凭证.pdf`
      })
      Message.success("下载凭证成功！")
      progressDialogState.visible = false
    })
    .catch(err => {
      toastError(err, "下载凭证失败")
      progressDialogState.visible = false
      endProgress()
    })
}
</script>

<style lang="less" scoped>
.record-metadata {
  overflow: hidden;
  height: 100%;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgb(0 35 114 / 10%);
  display: flex;
  flex-direction: column;
  transition: width 0.3s;
  .title {
    position: relative;
    height: 56px;
    padding: 0 15px;
    line-height: 56px;
    border-bottom: 1px solid rgb(225 226 230);
    width: 100%;

    &::before {
      position: absolute;
      top: 20px;
      left: 0;
      width: 4px;
      height: 18px;
      background-color: var(--el-color-primary);
      border: 2px;
      border-radius: 0 4px 4px 0;
      content: "";
    }

    .custom-select {
      width: 100%;

      :deep(.el-input__wrapper) {
        padding: 0;
        box-shadow: none !important;

        .el-input__inner {
          font-size: 16px;
          color: rgb(3 8 20 / 85%);
          font-weight: 700;
          font-style: normal;
        }
      }
    }
  }

  .base-info {
    height: 40px;
    padding: 0 15px;
    font-size: 14px;
    color: var(--el-color-primary);
    font-weight: 700;
    line-height: 40px;
    background-color: rgb(231 242 255);
  }

  .content {
    overflow-y: auto;
    height: calc(100% - 56px - 44px - 40px);
    padding: 17px 20px;
    font-size: 14px;
    color: rgb(3 8 20 / 85.1%);
    font-weight: 400;
    font-style: normal;

    .verify-info {
      position: relative;
    }

    .content-item {
      .content-item-title {
        margin-bottom: 8px;
        font-weight: 500;
        font-style: normal;
        font-size: 14px;
        color: rgba(10, 22, 51, 0.7);
      }

      .content-item-content {
        margin-bottom: 16px;
        span {
          font-size: 16px;
          color: rgba(10, 22, 51, 0.85);
          font-weight: bold;
        }
      }
    }
  }
  // 验签
  .sign-verification {
    flex: 1;
    min-height: 0px;
    display: flex;
    flex-direction: column;
    &__title {
      font-size: 14px;
    }
    &-total {
      font-size: 14px;
      padding: 5px 10px;
      margin-top: 10px;
      display: inline-block;
    }
    &__text {
      padding: 10px;
    }
    .sign-list {
      padding: 10px;
      flex: 1;
      min-height: 0px;
      .sign-item {
        padding: 10px 0;
        border-bottom: 1px solid #dcdfe6;
        &__title {
          display: flex;
          justify-content: space-between;
          font-size: 14px;
          line-height: 14px;
          color: #333;
          align-items: center;

          i {
            margin-right: 6px;
            &.success {
              color: #51c78a;
            }

            &.warning {
              color: #e6a23c;
            }

            &.error {
              color: #fe4f3d;
            }
          }
        }
        &__text {
          display: inline-block;
          width: 170px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        &__btn {
          color: var(--el-color-primary);
          cursor: pointer;
        }
        &__label {
          flex: 1;
          margin-right: 10px;
          overflow: hidden;
          > div {
            width: 194px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        &__description {
          margin-top: 10px;
          font-size: 12px;
          line-height: 15px;
          color: #999;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .sign-verification__empty {
      flex: 1;
      min-height: 0px;
    }
    .export-credence {
      margin: auto;
      margin-bottom: 20px;
    }
  }
}
.collapse-icon {
  width: 24px;
  height: 48px;
  background: #fff;
  left: -24px;
  padding: 16px 4px;
  box-sizing: border-box;
  position: absolute;
  top: 0px;
  bottom: 0px;
  margin: auto;
  z-index: 999;
  cursor: pointer;
  border-radius: 5px 0px 0px 5px;
  &:hover {
    color: #fff;
    background-color: var(--el-color-primary);
  }
}
:deep(.el-tabs__nav) {
  width: 100% !important;
  justify-content: space-evenly !important;
}
</style>
