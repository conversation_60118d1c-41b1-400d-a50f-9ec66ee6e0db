<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchForm
        :form-config="loginLogSearchFormConfig"
        :form-state="searchForm"
        @query-btn-click="handleQuery"
        @reset-btn-click="handleReset"
      />
    </template>

    <template #table>
      <CommonTable :table-columns="loginLogTableColumns" :request-api="getLoginLogApi" :request-params="searchParams">
        <template #operationDateTime="{ row }">
          {{ formatDatetime(row.operationDateTime) }}
        </template>
        <template #operationTypeEnum="{ row }">
          <el-tag v-if="row.operationTypeEnum === OperationTypeEnum.LOGIN">登录</el-tag>
          <el-tag v-else-if="row.operationTypeEnum === OperationTypeEnum.LOGIN_OUT" type="warning">登出</el-tag>
          <el-tag v-else type="info">未知</el-tag>
        </template>
      </CommonTable>
    </template>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive } from "vue"
import { PageContainer, CommonTable } from "@/base-components"
import { TabsRouter, SearchForm } from "@/page-components"
import { useTableSearch } from "@/hooks"
import { formatDatetime } from "@/utils"
import { tabsRouterList, loginLogTableColumns, loginLogSearchFormConfig, OperationTypeEnum } from "./config"
import { getLoginLogApi } from "../interface"

const searchForm = reactive({
  loginName: "",
  operationType: "",
  operationDateTime: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchForm)
</script>
