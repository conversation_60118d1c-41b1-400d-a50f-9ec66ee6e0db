<svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Group 711">
<rect id="Rectangle 1289" width="72" height="72" rx="8" fill="url(#paint0_linear_802_252)"/>
<g id="Group 84">
<rect id="Rectangle 47" x="18" y="14" width="24.75" height="37.5833" rx="4" fill="white" fill-opacity="0.6"/>
<g id="Rectangle 48" filter="url(#filter0_b_802_252)">
<rect x="24.4166" y="18.5859" width="30.25" height="39.4167" rx="4" fill="url(#paint1_linear_802_252)"/>
</g>
<rect id="Rectangle 49" x="29" y="28.6641" width="11.9167" height="1.83333" rx="0.916667" fill="#F5744A"/>
<rect id="Rectangle 50" x="29" y="34.1641" width="9.16667" height="2.75" rx="1.375" fill="#F5744A"/>
<rect id="Rectangle 51" x="29" y="40.5859" width="6.41667" height="1.83333" rx="0.916667" fill="#F5744A"/>
<circle id="Ellipse 118" cx="45" cy="48" r="7" fill="#F67C54"/>
<path id="&#232;&#191;&#159;" d="M47.362 44.61H44.491V46.356H47.362V44.61ZM44.482 46.95C44.41 48.282 44.035 49.419 43.357 50.361L42.88 49.929C43.528 49.071 43.855 47.958 43.861 46.59V44.007H47.974V46.95H44.482ZM45.49 47.382C46.648 48.252 47.611 49.11 48.379 49.956L47.92 50.424C47.242 49.608 46.291 48.732 45.067 47.796L45.49 47.382ZM41.152 43.791C41.782 44.277 42.292 44.742 42.682 45.186L42.214 45.645C41.86 45.213 41.359 44.736 40.711 44.214L41.152 43.791ZM46.381 51.738C46.081 51.738 45.553 51.732 44.797 51.72C44.227 51.714 43.762 51.66 43.402 51.558C43.06 51.45 42.745 51.231 42.457 50.901C42.343 50.751 42.229 50.676 42.115 50.676C41.905 50.676 41.551 51.09 41.053 51.918L40.567 51.477C41.047 50.721 41.47 50.262 41.836 50.1V47.364H40.549V46.77H42.439V50.136C42.505 50.184 42.577 50.253 42.655 50.343C42.877 50.607 43.108 50.796 43.348 50.91C43.618 51.036 44.017 51.108 44.545 51.126C45.169 51.138 45.751 51.144 46.291 51.144C46.567 51.144 46.999 51.138 47.587 51.126C48.079 51.114 48.415 51.102 48.595 51.09L48.442 51.738H46.381Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_b_802_252" x="15.4166" y="9.58594" width="48.25" height="57.418" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="4.5"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_802_252"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_802_252" result="shape"/>
</filter>
<linearGradient id="paint0_linear_802_252" x1="-3.2192e-06" y1="61.7727" x2="72" y2="6.95455" gradientUnits="userSpaceOnUse">
<stop stop-color="#FCAD8C"/>
<stop offset="0.941908" stop-color="#F57046"/>
</linearGradient>
<linearGradient id="paint1_linear_802_252" x1="39.5416" y1="18.5859" x2="39.5416" y2="58.0026" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.8"/>
</linearGradient>
</defs>
</svg>
