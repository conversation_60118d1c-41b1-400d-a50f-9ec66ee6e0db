<template>
  <div
    class="draggable-form-item-card"
    :style="{
      border: props.index === props.activeConfigIndex ? '1px solid #409eff' : '1px dashed transparent',
      width: config.width,
      padding: config.type === FormItemType.IdCardGroup ? '0' : '0 12px',
      backgroundColor: isMouseOver ? 'rgba(56,96,244,0.04)' : ''
    }"
    :class="tableClass(config)"
    @mouseover="handleMouseOver"
    @mouseleave="handleMouseLeave"
    @click.stop="editCard(config.prop)"
  >
    <!-- 拖动按钮 -->
    <!-- <i v-if="isMouseOver" class="ri-draggable drag-icon"></i> -->

    <!-- 预览区域 -->
    <div :class="formContentClass(config)">
      <!-- 分组标题 -->
      <div v-if="config.type === FormItemType.GroupHeadline" class="group-headline" :style="groupHeadlineStyle(config)">
        {{ config.name }}
      </div>

      <!-- 锚点标题 -->
      <FormGroupHeader v-else-if="config.type === FormItemType.AnchorPoint" :title="config.name"></FormGroupHeader>

      <!-- 分割线 -->
      <el-form-item v-else :required="config.required">
        <template v-if="config.type !== FormItemType.IdCardGroup" #label>
          <span
            :style="{
              visibility: config.name ? 'visible' : 'hidden',
              fontSize: '14px'
            }"
          >
            {{ config.name }}
            <el-tooltip v-if="config.desc" placement="top" :content="config.desc">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </span>
        </template>
        <!-- 身份证控件组组件 -->
        <div v-if="config.type === FormItemType.IdCardGroup" class="form-group">
          <el-form-item
            v-for="item in config.tableColumns"
            :key="item.prop"
            :style="{
              width: item.width
            }"
            :required="item.required"
            class="form-group-item"
          >
            <template #label>
              <span
                :style="{
                  visibility: item.name ? 'visible' : 'hidden',
                  fontSize: '14px'
                }"
              >
                {{ item.name }}
                <el-tooltip v-if="item.desc" placement="top" :content="item.desc">
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
              </span>
            </template>
            <FormItem :config="item" />
          </el-form-item>
        </div>

        <FormItem
          v-else
          :config="config"
          :active-table-config-index="props.activeTableConfigIndex"
          :active-table-config="props.activeTableConfig"
          @edit="editTableCard"
          @delete="deleteTableCard"
        />
      </el-form-item>
    </div>

    <!-- 编辑&删除按钮 -->
    <div v-if="isMouseOver" class="operation-buttons" @click.stop>
      <el-tooltip effect="dark" content="编辑" placement="top">
        <i
          v-if="config.type !== FormItemType.IdCardGroup && config.type !== FormItemType.BloodDonationBarcode"
          class="ri-edit-line operation-icon"
          @click="editCard(config.prop)"
        ></i>
      </el-tooltip>

      <el-tooltip effect="dark" content="复制" placement="top">
        <i
          v-if="config.type !== FormItemType.IdCardGroup && config.type !== FormItemType.BloodDonationBarcode"
          class="ri-file-copy-2-line operation-icon"
          @click="copyCard(config)"
        ></i>
      </el-tooltip>

      <el-tooltip effect="dark" content="删除" placement="top">
        <i class="ri-close-line operation-icon" @click="deleteCard(config.prop)"></i>
      </el-tooltip>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue"
import { QuestionFilled } from "@element-plus/icons-vue"
import { FormGroupHeader } from "@/base-components"
import { FormItemType, FormItemConfig } from "@/configs"
import { getFormCustomStyle } from "@/utils"
import FormItem from "./form-item.vue"

const props = defineProps<{
  prop: string
  config: FormItemConfig
  index: number
  activeConfigIndex: number
  activeTableConfigIndex: number
  activeTableConfig: FormItemConfig
  editCard: (prop: string) => void
  copyCard: (config: FormItemConfig) => void
  deleteCard: (prop: string) => void
}>()

/* ==================== 鼠标悬停处理 ====================== */

const isMouseOver = ref(false)

const handleMouseOver = () => {
  isMouseOver.value = true
}

const handleMouseLeave = () => {
  isMouseOver.value = false
}

const tableClass = config => {
  if (config.type === FormItemType.Table) {
    return `table-item prop=${config.prop}`
  }
}
const emits = defineEmits(["edit", "delete"])

const editTableCard = (data, config) => {
  console.log(`output->data`, data)
  emits("edit", data, config)
}

const deleteTableCard = () => {
  emits("delete")
}

/*=====================================表单样式=====================================*/

const { formContentClass, groupHeadlineStyle } = getFormCustomStyle()
</script>

<style lang="less" scoped>
.draggable-form-item-card {
  box-sizing: border-box;
  position: relative;
  border-radius: 8px;
  :deep(.el-form-item__label) {
    margin-bottom: 0;
    font-weight: bold;
    font-size: 14px;
    color: #0a1633;
  }
  .form-content-row {
    :deep(.el-form-item) {
      display: flex;
      align-items: baseline;
      justify-content: space-between;
      margin-bottom: 0; // 因为不需要校验，没有底部间距
      .el-form-item__content {
        justify-content: flex-end;
      }
      .el-form-item__label {
        width: 40%;
        display: inline-block;
      }
    }
  }
  .drag-icon {
    position: absolute;
    left: 0;
    top: 0;
    top: 50%;
    transform: translateY(-50%);
    cursor: move;
    font-size: var(--custom-large-font-size);
  }

  .group-headline {
    background-color: #fafafb;
    font-size: var(--custom-large-font-size);
    font-weight: 600;
    padding: 12px 0px;
  }

  :deep(.el-input) {
    width: 100%;
  }
  :deep(.el-input-number) {
    width: 100%;
  }
  :deep(.el-select) {
    width: 100%;
  }
  :deep(.el-form-item__label) {
    // padding-bottom: 4px;
  }
  :deep(.el-date-editor) {
    width: 100%;
  }
  .operation-buttons {
    position: absolute;
    right: 0;
    top: -10px;
    display: flex;

    .operation-icon {
      color: #aaa;
      cursor: pointer;
      margin-left: 6px;
      background: #fff;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0px 4px 8px 0px rgba(51, 51, 51, 0.15);
      border-radius: 4px;
    }
  }
  .form-group {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    .form-group-item {
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start !important;
      padding: 0 12px;
      box-sizing: border-box;
      // margin-bottom: 18px;
    }
  }
}
</style>
