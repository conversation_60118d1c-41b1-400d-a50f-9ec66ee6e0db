<template>
  <div style="display: flex; flex-direction: column; height: 100%">
    <!-- 高级检索项 -->
    <el-form v-if="advancedOptions.length > 0" ref="formRef" class="save-options-list" @submit.prevent>
      <div v-for="(option, index) in advancedOptions" :key="option.prop" class="save-options-item">
        <!-- 且/或 -->
        <div class="logic-item">
          <div v-if="index === 0" class="logic-item-name">当</div>
          <div v-else-if="index === 1" class="logic-show-line">
            <el-popover trigger="click" popper-class="logic-select-container" :teleported="false">
              <template #reference>
                <div class="logic-select-item logic-item-name">
                  {{ getLogic(option.logic) }}
                  <el-icon style="margin-left: 7px"><ArrowDown /></el-icon>
                </div>
              </template>
              <div class="action-item" @click="setLogic('AND')">且</div>
              <div class="action-item" @click="setLogic('OR')">或</div>
            </el-popover>
          </div>

          <div v-else class="logic-show-line logic-item-name">{{ getLogic(option.logic) }}</div>
        </div>

        <!-- 检索项表单 -->
        <DaterangeFormItem
          v-if="option.type === QueryTermTypeEnum.Daterange"
          v-model:model-value="option.value"
          :label="option.label"
        />
        <RangeSelect
          v-else-if="option.type === QueryTermTypeEnum.RangeSelect"
          v-model:model-value="option.value"
          :label="option.label"
          :rc-code="option.rcCode"
        />
        <DepartmentFormItem
          v-else-if="option.type === QueryTermTypeEnum.DeptSelect"
          v-model:model-value="option.value"
          :label="option.label"
        />
        <CountRangeFormItem
          v-else-if="option.type === QueryTermTypeEnum.CountRange"
          v-model:from="option.value.from"
          v-model:to="option.value.to"
          v-model:error="option.error"
          :label="option.label"
          :min="0"
        />

        <IcdSelect
          v-else-if="option.type === QueryTermTypeEnum.NameSelect || option.type === QueryTermTypeEnum.CodeSelect"
          v-model:model-value="option.value"
          :version-type="option.versionType"
          :label="option.label"
          :type="option.type === QueryTermTypeEnum.NameSelect ? 'name' : 'code'"
        />
        <el-form-item v-else :label="option.label">
          <el-input
            v-if="option.type === QueryTermTypeEnum.Input"
            v-model="option.value"
            :placeholder="`请输入${option.label}`"
          />
          <div v-else>该搜索项失踪了</div>

          <!-- 删除 -->
        </el-form-item>

        <div class="delete-icon" @click="deleteAdvancedOption(option)">
          <img :src="DeleteIcon" />
        </div>
      </div>
    </el-form>
    <!-- 操作 -->
    <div class="save-action-container">
      <el-popover
        :teleported="false"
        popper-class="add-options-popover"
        trigger="click"
        :width="305"
        @show="refreshSourceOptions"
      >
        <template #reference>
          <el-button link type="primary" :icon="Plus" class="add-item">筛选条件</el-button>
        </template>
        <el-input
          v-model="sourceKeyword"
          class="option-search-input"
          placeholder="搜索检索项名称"
          :prefix-icon="Search"
        />
        <div v-loading="sourceOptionsLoading" style="flex: 1; min-height: 0px">
          <div v-if="filterSourceOptions.length > 0" class="options-list">
            <div
              v-for="option in filterSourceOptions"
              :key="option.prop"
              class="option-item"
              :class="advancedOptionsProp.includes(option.prop) ? 'is-active' : ''"
              @click="addAdvancedOption(option)"
            >
              <div>{{ option.label }}</div>
              <i v-if="advancedOptionsProp.includes(option.prop)" class="ri-check-line"></i>
            </div>
          </div>
          <EmptyContent v-else desc="暂无检索项" />
        </div>
      </el-popover>

      <div v-if="advancedOptions.length > 0" style="display: flex">
        <slot name="actions"></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue"
import { Plus, Search, ArrowDown } from "@element-plus/icons-vue"
import {
  DaterangeFormItem,
  RangeSelect,
  DepartmentFormItem,
  CountRangeFormItem,
  IcdSelect,
  EmptyContent
} from "@/base-components"
import DeleteIcon from "@/assets/svg/other/delete-icon.svg"
import { QueryTermTypeEnum } from "@/configs"
import { getIntegratedQueryTermsApi } from "@/interfaces"
import { FilterType } from "@/types"
import { toastError } from "@/utils"

interface PropsType {
  options: Array<Record<string, any>>
  filterType: FilterType
  disabledOptionsProp?: Array<string>
}
const props = withDefaults(defineProps<PropsType>(), {
  disabledOptionsProp: () => {
    return []
  }
})

const emits = defineEmits(["update:options"])
const advancedOptions = computed({
  get: () => props.options,
  set: val => emits("update:options", val)
})

/* ======================== 添加筛选条件 ======================== */
const sourceOptionsLoading = ref(false)
const sourceOptions = ref<Array<Record<string, any>>>([])

// 搜索检索项
const sourceKeyword = ref("")
const filterSourceOptions = computed(() => {
  return sourceOptions.value.filter(item => {
    return item.label.includes(sourceKeyword.value) && !props.disabledOptionsProp.includes(item.prop)
  })
})

// 获取检索项
function refreshSourceOptions() {
  sourceOptionsLoading.value = true
  getIntegratedQueryTermsApi()
    .then(res => {
      sourceOptions.value = res.data.data || []
      sourceOptionsLoading.value = false
    })
    .catch(err => {
      sourceOptionsLoading.value = false
      toastError(err, "获取检索项失败")
    })
}

/* ======================== 高级筛选条件 ======================== */
const advancedOptionsProp = computed(() => {
  return advancedOptions.value.map(item => item.prop)
})

// 添加高级检索项
function addAdvancedOption(option) {
  if (!advancedOptionsProp.value.includes(option.prop)) {
    option.logic = advancedOptions.value?.[0]?.logic || "AND"
    advancedOptions.value.push(option)
    if (option.type === QueryTermTypeEnum.CountRange) {
      option.value = { from: "", to: "" }
    } else option.value = ""
    emits("update:options", advancedOptions.value)
  }
}

// 删除高级检索项
function deleteAdvancedOption(option) {
  if (advancedOptionsProp.value.includes(option.prop)) {
    const data = advancedOptions.value.filter(item => item.prop !== option.prop)
    emits("update:options", data)
  }
}

// 设置逻辑关系
function setLogic(type) {
  // 全部为且/或
  advancedOptions.value.forEach(item => (item.logic = type))
}

function getLogic(type) {
  switch (type) {
    case "OR":
      return "或"
    case "AND":
      return "且"
    default:
      return "且"
  }
}
</script>

<style lang="less" scoped>
.save-options-list {
  width: 100%;
  flex: 1;
  min-height: 0px;
  overflow: auto;
  margin-bottom: 24px;
  .logic-select-container {
    padding: 0px !important;
    min-width: 0px !important;
    width: fit-content !important;
    .action-item {
      cursor: pointer;
      padding: 6px 10px;
      &:hover {
        background: rgba(0, 0, 0, 0.02);
      }
    }
  }

  .save-options-item {
    width: 100%;
    display: flex;

    .logic-item {
      height: 32px;
      line-height: 32px;
      margin-right: 16px;
      position: relative;
      .logic-show-line::before {
        content: " ";
        border: 1px solid #dadfe7;
        position: absolute;
        top: -24px;
        height: 24px;
        left: 11px;
      }
      .logic-select-item {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 0px 5px;
        &:hover {
          background: rgba(0, 0, 0, 0.02);
        }
      }
      .logic-item-name {
        padding: 0px 5px;
        color: rgba(10, 22, 51, 0.6);
        font-size: 14px;
      }
    }

    :deep(.el-form-item) {
      flex: 1;
      min-width: 0px;
      .count-range-container {
        width: 100%;
        .el-input {
          flex: 1;
          min-width: 0px;
        }
      }
      .el-select {
        width: 100%;
      }
    }

    .delete-icon {
      height: 32px;
      display: flex;
      align-items: center;
      margin: 0px 8px;
      color: #f56c6c;
      cursor: pointer;
    }
  }
  .save-options-item:not(:last-child) {
    margin-bottom: 24px;
  }
  .save-options-item:not(:first-child) {
    .logic-item {
      margin-left: 32px;
    }
  }
}
</style>
