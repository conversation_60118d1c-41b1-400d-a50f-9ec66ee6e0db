<template>
  <div class="abnormal-data-chart-container">
    <div class="chart-title"></div>
    <div class="chart-container">
      <v-chart class="abnormal-data-chart" :option="option" autoresize />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, provide, watch } from "vue"
import { PieChart } from "echarts/charts"
import { TooltipComponent, LegendComponent } from "echarts/components"
import { use } from "echarts/core"
import { LabelLayout } from "echarts/features"
import { CanvasRenderer } from "echarts/renderers"
import VChart, { THEME_KEY } from "vue-echarts"
import { SystemAbnormalType } from "../config"

/* ===================== 数据处理操作 ===================== */
interface AbnormalDataProp {
  abnormalData: Array<SystemAbnormalType>
}

const seriesData = ref([] as any)

const props = defineProps<AbnormalDataProp>()

watch(
  () => props.abnormalData,
  () => {
    formatData(props.abnormalData)
  }
)

function formatData(abnormalData) {
  let tempSeriesData: Array<any> = []
  abnormalData.forEach(item => {
    let temp = { value: item.count, name: item.systemName }
    tempSeriesData.push(temp)
  })
  seriesData.value = tempSeriesData
}

/* ===================== 配置项 ===================== */
use([TooltipComponent, LegendComponent, PieChart, CanvasRenderer, LabelLayout])

provide(THEME_KEY, "dark")

const option = ref({
  tooltip: {
    trigger: "item"
  },
  legend: {
    x: "center",
    y: "bottom",
    itemWidth: 16
  },
  series: [
    {
      type: "pie",
      radius: ["40%", "70%"],
      avoidLabelOverlap: false,
      width: "50%",
      left: "25%",
      label: {
        show: true,
        formatter(param) {
          return param.name + ": " + param.percent + "%"
        }
      },
      itemStyle: {
        borderRadius: 10
      },
      labelLine: {
        show: true
      },
      data: seriesData
    }
  ]
})
</script>

<style lang="less" scoped>
.abnormal-data-chart-container {
  width: 28%;
  min-width: 458px;
  margin: 20px 0 20px 20px;
  .chart-title {
    width: 100%;
    height: 43px;
    background: url("@/assets/svg/monitor/abnormal-state.svg") no-repeat;
    background-size: 100%;
  }
  .chart-container {
    margin-top: 15px;
    .abnormal-data-chart {
      width: 100%;
      height: 440px;
    }
  }
}
</style>
