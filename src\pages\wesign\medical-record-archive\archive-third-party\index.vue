<template>
  <PageContainer separate>
    <template #search>
      <SearchForm
        :form-config="searchFormConfig"
        :form-state="searchFormState"
        @query-btn-click="handleQuery"
        @reset-btn-click="handleReset"
      />
    </template>

    <template #table>
      <CommonTable
        ref="tableRef"
        table-id="thirdPartyArchiveTableIndent"
        :table-columns="tableColumns"
        :request-api="getThirdPartyArchiveListApi"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <template #operation="{ row }">
          <TableButton @click="toDetail(row)">查看</TableButton>
          <TableButton @click="handlePrint(row)">打印</TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import { useRouter } from "vue-router"
import printJS from "print-js"
import { PageContainer, CommonTable, TableButton } from "@/base-components"
import { SearchForm } from "@/page-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { downloadFileApi } from "@/interfaces"
import { useGlobalOptionsStore, useSystemStore, useUserStore } from "@/stores"
import { formatDatetime, toastError } from "@/utils"
import { tableColumns, searchFormConfig, menuId } from "./config"
import { getThirdPartyArchiveListApi } from "./interface"

const systemStore = useSystemStore()
const globalOptionsStore = useGlobalOptionsStore()

const router = useRouter()

const { hasOperationPermission } = useUserStore()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  patientFilterProp: "",
  patientFilterValue: "",
  outHospitalDeptWsid: "",
  outHospitalDatetime: "",
  visitIdSymbol: "",
  visitIdCount: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/* ======================== 表格相关方法 ======================== */

const tableRef = ref<InstanceType<typeof CommonTable>>()

function dataCallback(data: Array<Record<string, any>>) {
  data.forEach(row => {
    row.outHospitalDatetime = formatDatetime(row.outHospitalDatetime)
    row.inHospitalDatetime = formatDatetime(row.inHospitalDatetime)
    // row.outHospitalDeptName = globalOptionsStore.departmentOptions.find(
    //   option => option.value === row.outHospitalDeptWsid
    // )
    // row.inHospitalDeptName = globalOptionsStore.departmentOptions.find(
    //   option => option.value === row.inHospitalDeptWsid
    // )
  })
  return data
}

/* ======================== 操作 ======================== */

// 查看详情
const toDetail = row => {
  router.push({ path: "/archive/third-party/detail", query: { inpNo: row?.inpNo } })
}

// 打印
const handlePrint = row => {
  const requestParams = {
    inpNo: row.inpNo,
    type: "COMPOSE",
    source: "HIS_ARCHIVE_PRINT"
  }
  systemStore.showLoading("加载中")
  downloadFileApi(requestParams)
    .then(res => {
      systemStore.hideLoading()
      const data = URL.createObjectURL(new Blob([res.data], { type: "application/pdf" }))
      printJS(data)
    })
    .catch(err => {
      const decoder = new TextDecoder("utf-8")
      err.response.data = JSON.parse(decoder.decode(err.response.data))
      console.log(`output->err`, err)
      systemStore.hideLoading()
      toastError(err, "操作失败")
    })
}
</script>
