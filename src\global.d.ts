declare interface IPaginationRequestParams {
  offset: number
  limit: number
  filters?: string
  fields?: string
  sorts?: string
}

declare type CommonObjectList = Array<Record<string, any>>

declare interface CommonTreeItem {
  children?: Array<CommonTreeItem>
  [key: string]: any
}

declare interface IResponseData<T = CommonObjectList> {
  code: string
  data: T
  message: string
}

declare interface IPaginationResponseData {
  code: string
  data: {
    rows: CommonObjectList
    page: {
      number: number
      size: number
      totalElements: number
      totalPages: number
    }
  }
  message: string
}

declare interface IBaseOptionItem {
  label: string
  value: number | string
  [prop: string]: any
}
