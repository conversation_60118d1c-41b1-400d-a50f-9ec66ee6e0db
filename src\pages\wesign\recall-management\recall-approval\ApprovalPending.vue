<template>
  <PageContainer separate>
    <!-- 搜索区域 -->
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchForm
        :form-config="approvalPendingSearchFormConfig"
        :form-state="searchFormState"
        @query-btn-click="handleQuery"
        @reset-btn-click="handleReset"
      />
    </template>

    <!-- 表格展示 -->
    <template #table>
      <CommonTable
        ref="recallApprovalTableRef"
        table-id="recallApplyTableIdent"
        :request-params="searchParams"
        :table-columns="recallApprovalPendingTableColumns"
        :request-api="getRecallWaitApproveRecordApi"
      >
        <template #inHospitalDatetime="{ row }">
          {{ formatDatetime(row.inHospitalDatetime) }}
        </template>
        <template #outHospitalDatetime="{ row }">
          {{ formatDatetime(row.outHospitalDatetime) }}
        </template>
        <template #createdDatetime="{ row }">
          {{ formatDatetime(row.createdDatetime) }}
        </template>
        <template #archivistName="{ row }">
          {{ row.archivistName || "--" }}
        </template>
        <template #operation="{ row }">
          <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.View)" @click="showDetailDialog(row)">
            审批
          </TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>

  <!-- 召回审批弹窗 -->
  <el-drawer
    v-if="recallApprovalDialogVisible"
    v-model="recallApprovalDialogVisible"
    title="审批申请"
    :size="400"
    :before-close="clearApplyState"
    :destroy-on-close="true"
  >
    <div class="approval-form">
      <div class="approval-form-item">
        <span>召回原因：</span>
        <span>{{ approvalFormState.recallReason }}</span>
      </div>
      <div class="approval-form-item">
        <span>召回时长：</span>
        <span>{{ approvalFormState.recallTimesDescribe }}</span>
      </div>
      <div class="tree">
        <div class="tree-title">
          <span>病人姓名</span>
        </div>
        <div v-loading="approvalFormState.loading" class="tree-content">
          <CommonTree ref="approvalTreeRef" node-key="wsid" :data="approvalFormState.mrClassTree" />
        </div>
      </div>
      <ApprovalDetail :business-key="approvalFormState.wsid" />
    </div>
    <template #footer>
      <div class="approval-reason-label">
        <span class="label">审批意见：</span>
        <el-input v-model="approvalFormState.approvalReason" type="textarea" />
      </div>
      <div v-loading="requestLoading">
        <el-dropdown v-if="refuseState.approvalNodes?.length > 0" @command="$event => handleSelectNode($event)">
          <el-button style="margin-right: 14px">退回</el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-for="node in refuseState.approvalNodes" :key="node.defKey" :command="node.defKey">
                {{ node.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button type="danger" @click="handleReject">驳回</el-button>
        <el-button type="primary" @click="confirmCallback">通过</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue"
import { CommonTable, CommonTree, TableButton, PageContainer } from "@/base-components"
import { TabsRouter, SearchForm, ApprovalDetail } from "@/page-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { checkSealedApi, getApprovalNodeApi } from "@/interfaces"
import { useUserStore } from "@/stores"
import { formatDatetime, formatRecordTree, Message, SystemPrompt, toastError } from "@/utils"
import {
  getRecallWaitApproveRecordApi,
  getRecallDetailApi,
  updateRecallApprovalApi,
  recallApplyRollBackApi
} from "../interface"
import { tabsRouterList, menuId, recallApprovalPendingTableColumns, approvalPendingSearchFormConfig } from "./config"

const { hasOperationPermission } = useUserStore()

const recallApprovalTableRef = ref()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  patientFilterProp: "patientName",
  patientFilterValue: "",
  inHospitalDatetime: "",
  outHospitalDatetime: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/*========================= 审批 ======================= */

// 召回文件tree
const approvalTreeRef = ref()

// 召回申请回显的数据
const approvalFormState = reactive({
  wsid: "",
  inpNo: "",
  processTaskId: "",
  recallTimesDescribe: "",
  recallReason: "",
  mrClassTree: [],
  loading: false,
  approvalReason: ""
})

const requestLoading = ref(false)

// 审批同意
const confirmCallback = () => {
  // 如果病案被封存，不能发起召回审批，需要先在其他地方解除封存
  checkSealedApi(approvalFormState.inpNo).then(isSealed => {
    if (isSealed) return Message.error("病案已封存，无法审批，请先解除封存")
    SystemPrompt("您确定要通过审批吗").then(() => {
      requestLoading.value = true
      updateRecallApprovalApi({
        wsid: approvalFormState.wsid,
        status: "APPROVAL_PASS",
        processTaskId: approvalFormState.processTaskId,
        desc: approvalFormState.approvalReason
      })
        .then(() => {
          Message.success("通过审批")
          clearApplyState()
        })
        .catch(error => {
          toastError(error, "审批失败")
        })
        .finally(() => {
          recallApprovalTableRef.value?.refreshTableData()
          requestLoading.value = false
        })
    })
  })
}

// 清空召回申请表单的数据
const clearApplyState = () => {
  recallApprovalDialogVisible.value = false
  approvalFormState.inpNo = ""
  approvalFormState.wsid = ""
  approvalFormState.recallReason = ""
  approvalFormState.recallTimesDescribe = ""
  approvalFormState.mrClassTree = []
  approvalFormState.processTaskId = ""
  approvalFormState.approvalReason = ""
  refuseState.approvalNodes = []
}

/*========================= 审批弹窗 ======================= */
const recallApprovalDialogVisible = ref(false)

const showDetailDialog = (row: any) => {
  recallApprovalDialogVisible.value = true
  if (row?.wsid) {
    const wsid = row.wsid
    approvalFormState.loading = true
    getRecallDetailApi(wsid)
      .then(async res => {
        approvalFormState.wsid = wsid
        approvalFormState.processTaskId = row?.processTaskId
        approvalFormState.inpNo = row.inpNo
        const result = res.data.data
        approvalFormState.recallReason = result.recallReason
        approvalFormState.recallTimesDescribe = result.recallTimesDescribe
        approvalFormState.mrClassTree = formatRecordTree(result)
        approvalFormState.loading = false

        refuseState.approvalNodes = await getApprovalNodeApi({
          taskId: approvalFormState?.processTaskId,
          businessKey: approvalFormState.wsid
        })
      })
      .catch(error => {
        toastError(error, "获取召回详情内容失败")
      })
      .finally(() => {
        approvalFormState.loading = false
      })
  }
}

/*========================= 驳回弹窗 ======================= */

// 驳回
const handleReject = async () => {
  if (approvalFormState.approvalReason === "") return Message.warning("请填写审批意见")
  SystemPrompt("您确定要驳回吗").then(() => {
    checkSealedApi(approvalFormState.inpNo).then(isSealed => {
      if (isSealed) return Message.warning("病案已封存，无法审批，请先解除封存")
      requestLoading.value = true
      updateRecallApprovalApi({
        wsid: approvalFormState.wsid,
        status: "REJECT",
        desc: approvalFormState.approvalReason,
        processTaskId: approvalFormState.processTaskId
      })
        .then(() => {
          Message.success("驳回成功")
          clearApplyState()
        })
        .catch(() => {
          Message.error("驳回失败")
          clearApplyState()
        })
        .finally(() => {
          recallApprovalTableRef.value?.refreshTableData()
          requestLoading.value = false
        })
    })
  })
}

/*======================回退弹窗========================*/

const refuseState = reactive({
  approvalNodes: [] as Array<{ name: string; defKey: string }>
})

// 选择退回节点,回退
const handleSelectNode = (command: string) => {
  if (approvalFormState.approvalReason === "") return Message.warning("请填写审批意见")

  SystemPrompt("您确定要退回吗").then(() => {
    requestLoading.value = true
    recallApplyRollBackApi({
      processTaskId: approvalFormState.processTaskId,
      processTarActId: command,
      desc: approvalFormState.approvalReason
    })
      .then(() => {
        Message.success("退回成功")
        clearApplyState()
        recallApprovalTableRef.value?.refreshTableData()
      })
      .catch(err => {
        toastError(err, "退回失败")
      })
      .finally(() => {
        requestLoading.value = false
      })
  })
}
</script>

<style lang="less" scoped>
.approval-form {
  &-item {
    padding: 5px 0;
  }
}

.tree {
  margin-top: 10px;
  border: 1px solid #e5e5e5;
}

.approval-reason-label {
  margin-top: 20px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  .label {
    text-align: left;
    width: 90px;
  }
}

.tree-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #333;
  background: #e5e5e5;
  padding: 5px 10px;
}

.tree-content {
  padding: 0 10px;
  min-height: 20vh;
  max-height: 25vh;
  overflow-y: scroll;
}

.dialog-footer {
  margin-top: 24px;
  justify-content: flex-end;
  display: flex;
  gap: 10px;
}
</style>
