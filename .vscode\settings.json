{"eslint.format.enable": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.fixAll.eslint": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "cSpell.words": ["Antd", "axios", "daterange", "Datetime", "Depts", "echart", "echarts", "hqms", "jdbc", "Mr<PERSON>", "nodata", "oper", "<PERSON><PERSON><PERSON>", "Persistedstate", "Pinia", "popconfirm", "qrcode", "unref", "wesign", "wsid", "wsids"], "[svg]": {"editor.defaultFormatter": "jock.svg"}}