<template>
  <el-form-item :label="props.label" :prop="props.prop">
    <el-select
      v-model="commonSelectValue"
      clearable
      :filterable="props.filterable"
      :disabled="props.disabled"
      :placeholder="`请选择${props.label}`"
      :style="{ width: props.width }"
      @change="val => $emit('change', val)"
    >
      <el-option v-for="item in props.options" :key="String(item.value)" :label="item.label" :value="item.value" />
    </el-select>
  </el-form-item>
</template>

<script setup lang="ts">
import { computed } from "vue"
import type { BaseOptionItem } from "@/types"

const props = withDefaults(
  defineProps<{
    label: string
    options: Array<BaseOptionItem>
    modelValue: string | number
    prop?: string
    width?: string
    filterable?: boolean
    disabled?: boolean
  }>(),
  { prop: "", width: "100%", filterable: false, disabled: false }
)

const emits = defineEmits(["update:modelValue", "change"])

const commonSelectValue = computed({
  get: () => props.modelValue,
  set: val => emits("update:modelValue", val)
})
</script>
