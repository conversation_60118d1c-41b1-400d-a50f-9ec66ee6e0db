import { secrecyLevelOptions } from "@/configs"
import type { SearchFormConfigItem, TableColumnItem } from "@/types"

export const sealingHistoryTableColumns: Array<TableColumnItem> = [
  { type: "selection", width: 50, fixed: "left" },
  { prop: "version", label: "封存版本", minWidth: 180 },
  { prop: "decryptionKey", label: "解密密钥", minWidth: 120 },
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "mrNo", label: "病案号", minWidth: 150, sortable: true, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 120, sortable: true, must: true },
  { prop: "visitId", label: "住院次数", sortable: true, minWidth: 120 },
  { prop: "patientSex", label: "性别", minWidth: 100 },
  { prop: "inHospitalDatetime", label: "入院时间", sortable: true, minWidth: 170 },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 150 },
  { prop: "inDischargeDiagnosisName", label: "入院诊断", minWidth: 120 },
  { prop: "outHospitalDatetime", label: "出院时间", sortable: true, minWidth: 170 },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 100 },
  { prop: "dischargeDiagnosisName", label: "出院主诊断", minWidth: 100 },
  { prop: "secrecyGrade", label: "保密等级", sortable: true, minWidth: 120 },
  { prop: "lockDatetime", label: "封存开始时间", sortable: true, minWidth: 170 },
  { prop: "unlockDatetime", label: "封存结束时间", sortable: true, minWidth: 170 },
  { prop: "lockReason", label: "封存原因", minWidth: 100 },
  { prop: "applicant", label: "申请人", minWidth: 100 },
  { prop: "operation", label: "操作", width: 240, fixed: "right" }
]

export const sealingHistorySearchFormConfig: Array<SearchFormConfigItem> = [
  { type: "patient", label: "患者信息" },
  { type: "daterange", label: "封存结束时间", prop: "unlockDatetime" },
  { type: "select", label: "保密等级", prop: "secrecyGrade", options: secrecyLevelOptions }
]
