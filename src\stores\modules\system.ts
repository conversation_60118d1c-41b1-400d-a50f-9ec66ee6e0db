import { cloneDeep } from "lodash-es"
import { defineStore } from "pinia"
import { adminMenuData } from "@/pages/wesign/sub-routers"
import type { MenuConfigItem, TabState } from "@/types"

/* ======================== 系统配置信息 ======================== */

const level3Pages = [
  { name: "缺失登记", path: "/medical-record/registration/wait" },
  { name: "登记记录", path: "/medical-record/registration/record" },
  { name: "解封申请", path: "/sealing/apply/unlock" },
  { name: "文件生成异常", path: "/print/approval/file-generation-exception" },
  { name: "T+1交易对账", path: "/print/reconciliation-manage/transaction" },
  { name: "资金账单", path: "/print/reconciliation-manage/fund-bill" },
  { name: "首页质控", path: "/quality-control-config/scoring-standard/home-page" },
  { name: "科室质控", path: "/quality-control-config/scoring-standard/department-page" },
  { name: "终末质控", path: "/quality-control-config/scoring-standard/final-score-page" },
  { name: "厂商系统", path: "/collect/configuration/vendor-system" },
  { name: "数据源", path: "/collect/configuration/data-origin" },
  { name: "字段映射", path: "/collect/configuration/field-mapping" },
  { name: "数据库", path: "/collect/way/database" },
  { name: "FTP", path: "/collect/way/ftp" },
  { name: "接口配置", path: "/collect/way/interface" },
  //以下是系统管理员才可进入的页面
  { name: "登录日志", path: "/system-manage/system-log/login" },
  { name: "操作日志", path: "/system-manage/system-log/operation" },
  { name: "文档类型", path: "/system-manage/plan-config/doc" },
  { name: "目录管理", path: "/system-manage/plan-config/directory" },
  { name: "方案管理", path: "/system-manage/plan-config/plan" },
  { name: "首页元数据", path: "/system-manage/data-standard/home-meta" },
  { name: "数据元值域代码表", path: "/system-manage/data-standard/code-table" },
  { name: "ICD-10", path: "/icd/icd10" },
  { name: "ICD-9-CM-3", path: "/icd/icd9cm3" },
  { name: "肿瘤形态学编码(M码)", path: "/icd/tumour" },
  { label: "病种对照", path: "/icd/disease-control" },
  { label: "手术对照", path: "/icd/surgical-control" },
  { label: "门诊慢特病病种", path: "/icd/outpatient-disease" },
  { label: "日间手术病种", path: "/icd/day-surgery-disease" },
  { label: "中医疾病诊断", path: "/icd/chinese-medicine-disease" },
  { name: "页面配置", path: "/system-manage/system-arguments/page" },
  { name: "功能配置", path: "/system-manage/system-arguments/function" },
  { name: "登录配置", path: "/system-manage/system-arguments/login" },
  { name: "水印配置", path: "/system-manage/system-arguments/watermark" },
  { name: "IP黑/白名单", path: "/system-manage/system-arguments/IP" }
]

const useSystemStore = defineStore("SYSTEM_INFO", {
  state: () => {
    return {
      pageLoading: false,
      loadingText: "页面加载中",
      isServerError: false, // 后台服务是否异常（暂时不用，保留着）
      menus: [] as Array<MenuConfigItem>,
      hasPermission: true,
      refreshMessage: false,
      pageParams: [] as Array<Record<string, any>>,
      pageTabs: [] as Array<TabState>,
      activeTab: "",
      operLogEnum: [] as Array<Record<string, any>> //操作日志枚举
    }
  },

  getters: {
    // 当前系统配置已启用的菜单树及操作列表
    availableMenus: state => {
      let temp: Array<MenuConfigItem> = []
      const allMenus = cloneDeep(state.menus)
      temp = allMenus.filter(menu => menu.status)
      temp.forEach(menu => {
        // 一级菜单操作列表过滤
        menu.operations = menu.operations.filter(operation => operation.status)
        // 二级菜单过滤
        menu.children = menu.children.filter(item => item.status)
        // 二级菜单操作列表过滤
        menu.children.forEach(subMenu => {
          subMenu.operations = subMenu.operations.filter(operation => operation.status)
        })
      })
      return temp
    },
    // 当前所有可用页面（含三级子页面）
    availablePages: state => {
      const temp: Array<{ name: string; path: string }> = []
      const menus = [...state.menus, ...adminMenuData]
      menus.forEach(menu => {
        temp.push({ name: menu.name, path: menu.path })
        menu.children.forEach(subMenu => {
          temp.push({ name: subMenu.name, path: subMenu.path })
        })
      })
      return [...temp, ...level3Pages]
    }
  },
  actions: {
    setOperLogEnum(val) {
      this.operLogEnum = val
    },
    setPageParams(val) {
      if (this.pageParams.some(item => item.id === val.id)) {
        const index = this.pageParams.findIndex(item => item.id === val.id)
        this.pageParams.splice(index, 1, val)
      } else {
        this.pageParams.push(val)
      }
    },
    clearPageParams() {
      this.pageParams = []
    },
    // 保存已打开的tab页面
    setPageTabsData(tabs, activeTab) {
      this.pageTabs = tabs
      this.activeTab = activeTab
    },
    clearPageTabsData() {
      this.pageTabs = []
      this.activeTab = ""
    },

    // 显示全局loading
    showLoading(message?: string) {
      this.pageLoading = true
      this.loadingText = message || "页面加载中"
    },

    // 取消全局loading展示
    hideLoading() {
      this.pageLoading = false
      this.loadingText = "页面加载中"
    }
  },
  persist: true
})

export default useSystemStore
