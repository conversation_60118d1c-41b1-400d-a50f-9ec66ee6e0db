<template>
  <div class="catalog-detail">
    <!-- 顶部tab及操作按钮 -->
    <!-- <div class="catalog-title-container">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="病历" name="detail"></el-tab-pane>
        <el-tab-pane label="基本信息" name="first"></el-tab-pane>
        <el-tab-pane label="住院信息" name="second"></el-tab-pane>
        <el-tab-pane label="诊断信息" name="third"></el-tab-pane>
        <el-tab-pane label="手术信息" name="fourth"></el-tab-pane>
        <el-tab-pane label="费用信息" name="fifth"></el-tab-pane>
      </el-tabs>
    </div> -->

    <div id="detail" class="medical-record">
      <Detail :catalog="true"></Detail>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed } from "vue"
import { useRoute, useRouter } from "vue-router"
import Detail from "@/pages/wesign/medical-record-manage/sub-pages/detail.vue"
import type { TabsPaneContext, FormInstance } from "element-plus"

const router = useRouter()
const route = useRoute()
const activeName = ref("detail")

// 定位标题锚点
const handleClick = (tab: TabsPaneContext, event: Event) => {
  if (tab.props.name !== "detail") {
    router.push({
      path: `/coding/hospitalization/edit`,
      query: { ...route.query, tab: JSON.stringify(tab) as string }
    })
  }
}
</script>

<style lang="less" scoped>
.catalog-detail {
  height: 100%;
}

.medical-record {
  height: calc(100% - 78px);
}

.catalog-title-container {
  padding: 12px 20px;
  margin-bottom: 16px;
  background: #fff;
  :deep(.el-tabs) {
    float: left;
  }
  .btns-container {
    float: right;
    height: 54px;
    width: 30%;
    min-width: 400px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .btn {
      cursor: pointer;
    }
  }
}

.catalog-title-container::after {
  content: "";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
</style>
