<template>
  <div class="tree-table-container">
    <div class="tree-table-header">
      <div><slot name="header"></slot></div>
    </div>

    <el-table
      ref="tableRef"
      v-loading="state.loading"
      class="tree-table"
      :data="state.tableData"
      border
      :row-key="rowKey"
      :header-cell-style="{ background: 'rgb(248,249,252)', color: '#030814' }"
      :default-expand-all="props.defaultExpandAll"
      @selection-change="handleSelectionChange"
    >
      <template v-for="item in tableColumns" :key="item">
        <el-table-column
          v-if="item.type === 'selection' || item.type === 'index'"
          :type="item.type"
          :width="50"
          fixed="left"
          :selectable="handleSelectable"
        ></el-table-column>
        <!-- 表格数据栏 -->
        <el-table-column
          v-else
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
          :min-width="item.minWidth"
          :sortable="item.sortable"
          :show-overflow-tooltip="item.prop !== 'operation'"
          :resizable="true"
          :fixed="item.fixed"
        >
          <template #default="{ row }">
            <slot :name="item.prop" :row="row">
              {{ row[item.prop as string] }}
            </slot>
          </template>
        </el-table-column>
      </template>
      <template #empty>
        <el-empty :image-size="120" />
      </template>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, watch } from "vue"
import { noop, cloneDeep, last } from "lodash-es"
import type { TableColumnItem } from "@/types"

interface CommonTableProps {
  tableColumns: Array<TableColumnItem>
  data?: Array<Record<string, any>>
  requestApi?: (params: any) => any
  requestParams?: Record<string, any>
  dataCallback?: (data: Array<any>) => Array<any>
  rowKey?: string
  multiple?: boolean // 是否多选
  selectable?: (row: any, index: number) => boolean
  defaultExpandAll?: boolean
}

const props = withDefaults(defineProps<CommonTableProps>(), {
  rowKey: "wsid",
  data: () => [],
  requestApi: noop,
  dataCallback: undefined,
  requestParams: () => ({}),
  multiple: true,
  selectable: undefined,
  defaultExpandAll: false
})

/* ======================== 表格数据 ======================== */

const state = reactive({
  loading: false, // loading状态
  tableData: [] as Array<any>, // 表格数据
  selectedRows: [] as Array<any> // 选中的行
})

// 获取表格数据
function refreshTableData() {
  if (props.requestApi === noop) return
  state.loading = true
  props
    .requestApi({ ...props.requestParams })
    .then(resData => {
      if (props.dataCallback) state.tableData = props.dataCallback(resData)
      else state.tableData = resData
    })
    .finally(() => {
      state.loading = false
    })
}

// 分页配置或请求参数变化后自动重新获取数据
watch([() => props.requestParams], refreshTableData, { immediate: true, deep: true })

// 外部传入表格数据同步至state
watch(
  () => props.data,
  () => {
    state.tableData = props.data
  },
  {
    immediate: true
  }
)

const tableRef = ref()

// 设置选择项
function toggleRowSelection(row, isSelect) {
  if (tableRef.value) tableRef.value.toggleRowSelection(row, isSelect)
}

function handleSelectionChange(rows: Array<any>) {
  if (props.multiple || rows.length === 1) {
    state.selectedRows = rows
    return
  }

  if (rows.length > 1) {
    state.selectedRows = last(rows)
    toggleRowSelection(rows[0], false)
  }
}

// 是否可勾选
function handleSelectable(row, index) {
  if (props.selectable) {
    return props.selectable(row, index)
  } else return true
}

const setCheckedKeys = checkedKeys => {
  handleSelectionChange(checkedKeys)
}

defineExpose({ refreshTableData, tableState: state, setCheckedKeys })
</script>

<style lang="less" scoped>
.tree-table-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  .tree-table-header {
    margin-bottom: 12px;
  }
  .tree-table {
    flex: 1;
    :deep(.el-scrollbar__view) {
      height: 100%;
    }

    :deep(.el-empty__description) {
      margin-top: 0;
    }
  }

  .table-pagination {
    margin-top: 20px;
    align-self: flex-end;
  }
}
</style>
