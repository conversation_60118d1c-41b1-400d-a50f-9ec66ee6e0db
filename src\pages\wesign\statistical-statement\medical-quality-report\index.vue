<template>
  <PageContainer separate>
    <template #search>
      <SearchContainer @queryBtnClick="handleQuery" @reset-btn-click="handleReset">
        <DepartmentFormItem v-model:model-value="searchFormState.outHospitalDeptWsid" label="出院科室" />

        <el-form-item label="出院时间">
          <el-date-picker
            v-model="searchFormState.outHospitalDatetime"
            type="daterange"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :shortcuts="shortcuts"
            value-format="x"
            :clearable="false"
            :disabled-date="disabledDate"
          />
        </el-form-item>
      </SearchContainer>
    </template>

    <template #table>
      <div class="chart-container">
        <!-- 终末质控平均得分 -->
        <FinalQualityScore ref="finalQualityScoreRef" :state="searchParams" />

        <!-- 平均得分折线图 -->
        <AverageScoreLineChart
          :time-list="averageScoreData.timeList"
          :series="averageScoreData.series"
          :show-table="showAverageScoreTable"
          @export="handleExport"
          @change-tab="val => (showAverageScoreTable = val)"
        />

        <!-- 平均得分表格 -->
        <AverageScoreTable
          :time-list="averageScoreData.timeList"
          :series="averageScoreData.series"
          :show-table="showAverageScoreTable"
          @export="handleExport"
          @change-tab="val => (showAverageScoreTable = val)"
        />
      </div>

      <!-- 病案质量-表格 -->
      <MedicalQualityTable
        :average-score="averageScore"
        :show-table="showMedicalQualityTable"
        :data-source="medicalQualityState.dataSource"
        @change-tab="val => (showMedicalQualityTable = val)"
      />
      <!-- 病案质量-chart图 -->
      <MedicalQualityChart
        :data-source="medicalQualityState.dataSource"
        :x-axis-data="medicalQualityState.chartXAxisData"
        :show-table="showMedicalQualityTable"
        @change-tab="val => (showMedicalQualityTable = val)"
      />
    </template>
  </PageContainer>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from "vue"
import { EChartsOption } from "echarts"
import ExcelJS from "exceljs"
import { saveAs } from "file-saver"
import { PageContainer, SearchContainer, DepartmentFormItem } from "@/base-components"
import { shortcuts } from "@/configs/options"
import { useGlobalOptionsStore } from "@/stores"
import { getMedicalQualityReportGroupByDateApi, getMedicalQualityReportGroupByDeptApi } from "../interface"
import {
  FinalQualityScore,
  MedicalQualityTable,
  AverageScoreTable,
  AverageScoreLineChart,
  MedicalQualityChart
} from "./components"
import { getAverageScoreSeriesData, getTimeList, getMidnightMillis } from "./config"

const globalOptionsStore = useGlobalOptionsStore()

/*====================================搜索表单数据及操作====================================*/

// 今天
const today = new Date().getTime()
//今天往前三个月
const LastMonthAgo = new Date(new Date().setMonth(new Date().getMonth() - 1)).getTime()
const searchFormState = reactive({
  outHospitalDeptWsid: "",
  outHospitalDatetime: [LastMonthAgo, today] as [number, number]
})

// 查询参数
const searchParams = reactive({
  outHospitalDeptWsid: "",
  outHospitalDatetime: [LastMonthAgo, today] as [number, number]
})

// 查询
const handleQuery = () => {
  searchParams.outHospitalDeptWsid = searchFormState.outHospitalDeptWsid
  searchParams.outHospitalDatetime = searchFormState.outHospitalDatetime
}

// 重置
const handleReset = () => {
  searchFormState.outHospitalDeptWsid = ""
  searchFormState.outHospitalDatetime = [LastMonthAgo, today]
  searchParams.outHospitalDeptWsid = ""
  searchParams.outHospitalDatetime = [LastMonthAgo, today]
}

/*================================科室质量平均得分数据及操作================================*/
const finalQualityScoreRef = ref<InstanceType<typeof FinalQualityScore>>()
// 获取终末质控平均得分数据
const averageScore = computed(() => finalQualityScoreRef.value?.scoreState.averageScore)
/*===============================科室病案质量数据及操作===============================*/

const showMedicalQualityTable = ref(false) // 是否显示科室病案质量的表格

const medicalQualityState = reactive({
  dataSource: [] as Record<string, any>[],
  chartXAxisData: [] as string[]
})

// 获取科室病案质量数据
const getMedicalQualityData = () => {
  const departmentOptions = globalOptionsStore.departmentOptions

  getMedicalQualityReportGroupByDeptApi({
    startDate: realStartDate.value,
    endDate: realEndDate.value,
    outHospitalDeptWsid: searchParams.outHospitalDeptWsid
  }).then(res => {
    const data = res.data.data
    let dataSource = data.map(item => {
      const department = departmentOptions.find(department => department.value === item.outHospitalDeptWsid)
      const levels = item.levels

      return {
        outHospitalDeptName: department ? department.label : "",
        medicalCount: item.totalCount || 0,
        outHospitalDeptWsid: item.outHospitalDeptWsid,
        averageScore: item.averageScore || 0,
        AMedicalName: "甲级病案",
        AMedical: levels.find(level => level.level === "甲")?.count || 0,
        AMedicalPercentage: levels.find(level => level.level === "甲")?.proportion || 0,
        BMedicalName: "乙级病案",
        BMedical: levels.find(level => level.level === "乙")?.count || 0,
        BMedicalPercentage: levels.find(level => level.level === "乙")?.proportion || 0,
        CMedicalName: "丙级病案",
        CMedical: levels.find(level => level.level === "丙")?.count || 0,
        CMedicalPercentage: levels.find(level => level.level === "丙")?.proportion || 0
      }
    })
    const allOutHospitalsDeptName = dataSource.map(item => item.outHospitalDeptName)
    medicalQualityState.chartXAxisData = Array.from(new Set(allOutHospitalsDeptName))
    medicalQualityState.dataSource = dataSource
    console.log("科室病案质量", data, dataSource)
  })
}

/*========================= 日期范围数据及操作 ======================= */

// 选择日期范围时，禁用未来日期
const disabledDate = computed(() => {
  return (time: Date) => {
    return time.getTime() > Date.now()
  }
})

const dateFormatter = ref("DAY") // x轴时间格式
const timeList = ref([] as string[]) //统计图x轴数据
// 时间区间
const changeDateRange = val => {
  const days = (val[1] - val[0]) / (3600000 * 24)
  const years = Math.floor(days / 365)
  const months = Math.floor(days / 31)
  // 超过一年 x轴按年展示
  if (days / 365 > 1) {
    dateFormatter.value = "YEAR"
    timeList.value = getTimeList(years, val[0], 3600000 * 24 * 365, "YYYY")
  } else if (days / 31 > 1) {
    dateFormatter.value = "OVERMONTH"
    //超过一个月 按月份展示
    timeList.value = getTimeList(months, val[0], 3600000 * 24 * 30, "YYYY-MM", true)
  } else if (months <= 1 && days > 0) {
    dateFormatter.value = "MONTH"
    timeList.value = getTimeList(Math.floor(days), val[0], 3600000 * 24, "MM-DD")
  } else if (days <= 0) {
    dateFormatter.value = "DAY"
    timeList.value = getTimeList(24, val[0], 3600000, "HH:mm")
  }
}

// 计算endDate真实的毫秒，因为daterange选的日期的毫秒是日期的0点，所以要加上一天的毫秒数再减去1毫秒
const realEndDate = computed(() => {
  return getMidnightMillis(searchParams.outHospitalDatetime[1]) + 1000 * 60 * 60 * 24 - 1
})

const realStartDate = computed(() => {
  // 从输入的毫秒数创建一个 Date 对象
  return getMidnightMillis(searchParams.outHospitalDatetime[0])
})
/*====================================== 平均得分 ==================================== */
const showAverageScoreTable = ref(true) // 是否显示平均得分表格

const averageScoreData = reactive({
  timeList: [] as string[],
  options: {} as EChartsOption,
  series: [] as Array<Record<string, any>>,
  showLegend: false
})
// 获取平均得分数据
const getAverageScoreData = () => {
  averageScoreData.timeList = timeList.value
  getMedicalQualityReportGroupByDateApi({
    startDate: realStartDate.value,
    endDate: realEndDate.value,
    outHospitalDeptWsid: searchParams.outHospitalDeptWsid
  }).then(res => {
    const data = res.data.data
    console.log("平均得分", data, timeList.value)
    averageScoreData.series = getAverageScoreSeriesData(dateFormatter.value, timeList.value, data)
  })
}

watch(
  () => searchParams,
  () => {
    changeDateRange(searchParams.outHospitalDatetime)
    getAverageScoreData()
    getMedicalQualityData()
  },
  {
    deep: true,
    immediate: true
  }
)

// 导出平均得分表格
const handleExport = async () => {
  const workbook = new ExcelJS.Workbook()
  const worksheet = workbook.addWorksheet("Sheet1")

  // 添加数据到工作表
  const headers = ["日期", ...averageScoreData.timeList]
  const scores = ["得分", ...averageScoreData.series]
  worksheet.addRow(headers)
  worksheet.addRow(scores)
  // 生成并保存 ExcelJS 文件
  const buffer = await workbook.xlsx.writeBuffer()
  saveAs(new Blob([buffer]), "病案质量报表平均得分.xlsx")
}
</script>

<style lang="less" scoped>
.chart-container {
  display: flex;
  margin-bottom: 20px;
}
</style>
