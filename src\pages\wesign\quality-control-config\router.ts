import type { RouteRecordRaw } from "vue-router"
import { MenuOperationEnum } from "@/configs"

const qualityControlConfigRouter: RouteRecordRaw = {
  path: "/quality-control-config",
  name: "QualityControlConfig",
  redirect: "/quality-control-config/rule-config",
  meta: {
    title: "质控配置",
    icon: "ri-equalizer-line",
    type: "multiple",
    showBreadcrumb: true
  },
  children: [
    {
      path: "/quality-control-config/rule-config",
      meta: {
        title: "规则配置",
        operations: [
          { id: MenuOperationEnum.Add, name: "新增" },
          { id: MenuOperationEnum.Edit, name: "编辑" },
          { id: MenuOperationEnum.Toggle, name: "切换状态" },
          { id: MenuOperationEnum.Delete, name: "删除" }
        ]
      },
      component: () => import("./rule-config/index.vue")
    },
    {
      path: "/quality-control-config/function-config",
      // redirect: "/quality-control-config/function-config/environment-variable",
      redirect: "/quality-control-config/function-config/expression-function",
      meta: {
        title: "函数配置",
        operations: [
          { id: MenuOperationEnum.Add, name: "新增" },
          { id: MenuOperationEnum.Edit, name: "编辑" },
          { id: MenuOperationEnum.Delete, name: "删除" }
        ]
      },
      children: [
        {
          path: "/quality-control-config/function-config/environment-variable",
          meta: { title: "环境变量" },
          component: () => import("./function-config/EnvironmentVariable.vue")
        },
        {
          path: "/quality-control-config/function-config/expression-function",
          meta: { title: "表达式函数" },
          component: () => import("./function-config/ExpressionFunction.vue")
        }
      ]
    },
    {
      path: "/quality-control-config/scoring-standard",
      redirect: "/quality-control-config/scoring-standard/home-page",
      meta: {
        title: "评分标准维护",
        operations: [
          { id: MenuOperationEnum.Edit, name: "编辑" },
          { id: MenuOperationEnum.Toggle, name: "切换状态" },
          { id: MenuOperationEnum.Import, name: "导入" },
          { id: MenuOperationEnum.Export, name: "导出" }
        ]
      },
      children: [
        {
          path: "/quality-control-config/scoring-standard/home-page",
          meta: { title: "首页质控", type: "home-page" },
          component: () => import("./scoring-standard/HomePage.vue"),
          children: [
            {
              path: "/quality-control-config/scoring-standard/home-page/content-setting",
              meta: { title: "内容设置" },
              component: () => import("./scoring-standard/ContentSetting.vue")
            }
          ]
        },
        {
          path: "/quality-control-config/scoring-standard/department-page",
          meta: { title: "科室质控", type: "department-page" },
          component: () => import("./scoring-standard/HomePage.vue"),
          children: [
            {
              path: "/quality-control-config/scoring-standard/department-page/content-setting",
              meta: { title: "内容设置" },
              component: () => import("./scoring-standard/ContentSetting.vue")
            }
          ]
        },
        {
          path: "/quality-control-config/scoring-standard/final-score-page",
          meta: { title: "终末质控", type: "final-score-page" },
          component: () => import("./scoring-standard/HomePage.vue"),
          children: [
            {
              path: "/quality-control-config/scoring-standard/final-score-page/content-setting",
              meta: { title: "内容设置" },
              component: () => import("./scoring-standard/ContentSetting.vue")
            }
          ]
        }
        // {
        //   path: "/quality-control-config/scoring-standard/:type/content-setting",
        //   meta: { title: "内容设置" },
        //   component: () => import("./scoring-standard/ContentSetting.vue")
        // }
      ]
    },
    {
      path: "/quality-control-config/classification-verification-config",
      meta: {
        title: "分类校验配置",
        operations: [
          { id: MenuOperationEnum.Add, name: "新增" },
          { id: MenuOperationEnum.Edit, name: "编辑" },
          { id: MenuOperationEnum.Toggle, name: "切换状态" },
          { id: MenuOperationEnum.Delete, name: "删除" }
        ]
      },
      component: () => import("./classification-verification-config/index.vue")
    },
    {
      path: "/quality-control-config/integrity-rule",
      meta: {
        title: "病案完整性规则",
        operations: [
          { id: MenuOperationEnum.Add, name: "新增" },
          { id: MenuOperationEnum.Edit, name: "编辑" },
          { id: MenuOperationEnum.Import, name: "导入" },
          { id: MenuOperationEnum.Export, name: "导出" },
          { id: MenuOperationEnum.Toggle, name: "切换状态" },
          { id: MenuOperationEnum.Delete, name: "删除" }
        ]
      },
      component: () => import("./integrity-rule/index.vue")
    }
  ]
}

export default qualityControlConfigRouter
