<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="approvalRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
        />

        <el-form-item label="审批状态">
          <el-select v-model="searchFormState.status">
            <el-option
              v-for="item in finishedApprovalStatusOptions"
              :key="item.label"
              :label="item.label"
              :value="item.code"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="借阅类型">
          <el-select v-model="searchFormState.applyType">
            <el-option
              v-for="item in globalOptionsStore.borrowTypeOptions"
              :key="item.value as string"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <DaterangeFormItem v-model:model-value="searchFormState.applyDatetime" label="申请时间" />
      </SearchContainer>
    </template>
    <template #table>
      <CommonTable
        ref="commonTableRef"
        table-id="borrowApprovalTable"
        :table-columns="borrowApprovalFinishedColumns"
        :request-api="getFinishedApprovalListApi"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <template #statusEnumName="{ row }">
          <el-tag :type="APPROVAL_TAG_MAP.get(row.statusEnumName)">
            {{ APPROVAL_STATUS_MAP.get(row.statusEnumName) }}
          </el-tag>
        </template>
        <template #timeLimit="{ row }">
          {{
            row.approveTimeLimitUnitEnumName === "FOREVER"
              ? "永久"
              : row.approveTimeLimit + unitOptions[row.approveTimeLimitUnitEnumName]
          }}
        </template>
        <template #operation="{ row }">
          <TableButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.View)"
            @click="showBorrowDetailDialog(row)"
          >
            详情
          </TableButton>
        </template>
      </CommonTable>
    </template>

    <!-- 审批详情弹窗 -->
    <BorrowDetailDrawer ref="BorrowDetailDrawerRef" />
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"
import {
  CommonTable,
  TableButton,
  PatientFilterFormItem,
  DaterangeFormItem,
  SearchContainer,
  PageContainer
} from "@/base-components"
import { TabsRouter } from "@/page-components"
import { approvalStatusOptions, MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { useUserStore, useGlobalOptionsStore } from "@/stores"
import { formatDatetime } from "@/utils"
import BorrowDetailDrawer from "../module-components/BorrowDetailDrawer.vue"

import {
  borrowApprovalFinishedColumns,
  APPROVAL_STATUS_MAP,
  APPROVAL_TAG_MAP,
  approvalRouterList,
  menuId,
  unitOptions
} from "./config"
import { getFinishedApprovalListApi } from "./interface"

const { hasOperationPermission } = useUserStore()
const globalOptionsStore = useGlobalOptionsStore()

/* ======================== 搜索相关数据及方法 ======================== */

const finishedApprovalStatusOptions = approvalStatusOptions.filter(option => option.value !== "APPLICATION")

const searchFormState = reactive({
  patientFilterProp: "",
  patientFilterValue: "",
  applyType: "",
  applyDatetime: "",
  status: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

const dataCallback = data => {
  data.forEach(item => {
    item.applyDatetime = formatDatetime(item.applyDatetime)
    item.approverDatetime = formatDatetime(item.approverDatetime)
    item.approveLatestTime = formatDatetime(item.approveLatestTime)
  })
  return data
}

/* ======================== 详情 ======================== */
const BorrowDetailDrawerRef = ref()

// 打开详情弹窗
const showBorrowDetailDialog = row => {
  BorrowDetailDrawerRef.value.open(row)
}
</script>

<style lang="less" scoped>
.approval-tree-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: calc(100% - 40px);
  padding: 10px 20px;
  background: #f6f7f9;
  border-radius: 4px;
}

.approval-check-buttons {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.approval-check-button {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32px;
  cursor: pointer;
  overflow: hidden;

  &:hover {
    color: #3860f4;

    .approval-check-button-icon {
      filter: drop-shadow(0 0 0.5px #3860f4);
    }
  }

  &:last-child {
    margin-right: 0;
  }
}

.approval-check-button-text {
  padding-left: 8px;
}

.approval-tree-content {
  padding: 10px 20px;
  max-height: 50vh;
  overflow-y: scroll;
}

.approval-tree-node {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
