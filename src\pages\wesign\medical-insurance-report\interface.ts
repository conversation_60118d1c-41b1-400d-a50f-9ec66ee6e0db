import axios from "@/interfaces/axios-instance"
import type { DateModelType } from "element-plus"

interface IMedicalInsuranceListParams {
  mrNos: string
  names: string
  outHospitalDeptWsid: string
  outHospitalTimeStart: DateModelType | number
  outHospitalTimeEnd: DateModelType | number
  reviewStatus: string
  registerNos?: string[]
  inpNos?: string[] // 导出需要的住院号
}

// 获取医保上报列表
export const getMedicalInsuranceListApi = (data: IMedicalInsuranceListParams) => {
  return axios({
    method: "post",
    url: `/api/catalog/medicare/hqms`,
    data: data
  })
}

// 获取医保导出记录条数
export const getMedicalInsuranceExportLimitApi = () => {
  return axios({
    method: "get",
    url: `/api/catalog/outpatient-hqms/document-max-number`
  })
}

// 导出cvs文件
export const exportMedicalInsuranceToCvsApi = (data: IMedicalInsuranceListParams) => {
  return axios({
    method: "post",
    url: `/api/catalog/inpatient-hqms/export`,
    data: data,
    timeout: 60000,
    responseType: "blob"
  })
}

// 审核医保上报
export const auditMedicalInsuranceApi = (wsids: string[]) => {
  return axios({
    method: "post",
    url: `/api/catalog/medicare/review`,
    data: wsids
  })
}

/* ======================= 导出记录 ====================== */

// 医保导出记录
export const getMedicalInsuranceExportRecordApi = (params: IPaginationRequestParams) => {
  params.sorts = "-createdDatetime"
  return axios({
    method: "get",
    url: `/api/catalog/inpatient-hqms/export-record`,
    params: params
  })
}

// 导出记录下载
export const downloadMedicalInsuranceFileApi = (wsids: string[]) => {
  return axios({
    method: "post",
    url: `/api/catalog/inpatient-hqms/file-download`,
    data: wsids,
    timeout: 60000,
    responseType: "blob"
  })
}
