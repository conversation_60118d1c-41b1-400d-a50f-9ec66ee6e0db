import { cloneDeep, isObject } from "lodash-es"
import {
  edgeHeight,
  addNodeSize,
  startNodeId,
  endNodeSize,
  approvalNodeSize,
  startNodeSize,
  operationNodeSize,
  getAbsolutePositionX
} from "./util"
import type { TableColumnItem, SearchFormConfigItem } from "@/types"

// 待归档搜索表单配置
export const processSearchFormConfig: Array<SearchFormConfigItem> = [
  { type: "input", label: "流程名称", prop: "flowName" }
]

export const approvalProcessTableColumns: Array<TableColumnItem> = [
  { prop: "flowName", label: "流程名称", minWidth: 100, must: true },
  { prop: "flowTypeName", label: "流程类型", minWidth: 100 },
  { prop: "applyDeptName", label: "应用科室", minWidth: 100 },
  { prop: "status", label: "状态", width: 150 },
  { prop: "operation", label: "操作", width: 150, fixed: "right" }
]

export enum FlowTypeEnum {
  SEAL = "SEAL",
  BORROW = "BORROW",
  RECALL = "RECALL"
}

// 审批通过方式
export enum PassStrategyEnum {
  ALL_PASS = "ALL_PASS",
  ONE_PASS = "ONE_PASS"
}

// 审批节点执行方式
export enum ApproveStrategyEnum {
  SYNC = "SYNC", //顺序执行
  ASYNC = "ASYNC" // 并行执行
}

//部门负责人条件
export enum DeptManagerConditionEnum {
  APPLY_USER = "applyUser", // 申请人
  OUT_HOSPITAL_DEPT = "outHospitalDept" // 出院科室
}

// 部门负责人条件名称
export const deptManagerConditionName = {
  [DeptManagerConditionEnum.APPLY_USER]: "申请人",
  [DeptManagerConditionEnum.OUT_HOSPITAL_DEPT]: "出院科室"
}

//审批人为空时的的处理方式
export enum AssigneeNullStrategyEnum {
  AUTO_PASS = "AUTO_PASS", //自动通过
  ADMIN_ASSIGNEE = "ADMIN_ASSIGNEE" // 流程负责人
}

// 审批人设置
export enum ApproveSettingEnum {
  ROLE = "ROLE", // 指定角色
  USER = "USER", // 指定人员
  DEPT_MANAGER = "DEPT_MANAGER" // 部门主管
}

// 审批节点操作
export enum AddTypeEnum {
  // 创建
  CREATE = "CREATE",
  // 复制
  COPY = "COPY"
}

export const createProcessBusinessTypeOptions = [
  {
    label: "借阅",
    value: FlowTypeEnum.BORROW
  },
  {
    label: "封存",
    value: FlowTypeEnum.SEAL
  },
  {
    label: "召回",
    value: FlowTypeEnum.RECALL
  }
]

export const createProcessApplyDepartmentOptions = [
  {
    label: "全院",
    value: "ALL"
  }
]

// 新建审批流程基本信息校验
export const processBasicInfoRules = {
  flowName: [{ required: true, message: "请输入流程名称", trigger: "blur" }],
  flowType: [{ required: true, message: "请选择流程类型", trigger: "change" }],
  applyDepartment: [{ required: true, message: "请选择应用科室", trigger: "change" }],
  adminAssignees: [{ required: true, message: "请选择流程负责人", trigger: "change" }]
}

export const nodeOptions = [
  {
    type: "USER_TASK",
    label: "审批节点",
    icon: "ri-user-3-line"
  }
]

// 审批按钮初始数据
export const approvalBtnInitialData = [
  {
    name: "通过",
    value: "BTN_PASS",
    enable: true
  },
  {
    name: "拒绝",
    value: "BTN_DENY",
    enable: false
  },
  {
    name: "退回",
    value: "BTN_ROLLBACK",
    enable: false
  }
]

// 审批节点初始数据
export const approvalNodeInitialData = {
  nodeName: "审批节点",
  approverSetting: ApproveSettingEnum.USER, //执行人设置
  approveStrategy: ApproveStrategyEnum.SYNC, //执行方式
  passStrategy: PassStrategyEnum.ALL_PASS, //执行结果
  assigneeNullStrategy: AssigneeNullStrategyEnum.AUTO_PASS, // 审批人为空时的处理方式
  members: [],
  role: {},
  approvalBtnOperation: approvalBtnInitialData,
  deptManager: {}
}

/**
 * 添加节点
 * 每次在页面添加一个操作节点，实际上添加了两个节点，一个是操作节点，一个是网关节点，、
 *和连接操作节点和网关节点的边，以及连接当前触发添加事件的节点和操作节点的边，
 *同时需要将当前graph里面边的source是currentNodeId的边的source更新为新添加的网关节点
 *再将触发节点以后的所有节点的y坐标更新，最后将新加的节点和边插入到触发节点以后，保证页面节点的顺序和graph数据的顺序一致
 */

//   涉及流程图所有排序的地方，排序的原因是通过addNode或者adEdge添加的节点或者边，会放在最后面，但是这里需要按照y轴的位置排序

export const addNodeFn = (item: Record<string, string>, id: string, graph: any, addType?: AddTypeEnum) => {
  const cells = graph.toJSON().cells
  const nodes = cells.filter(cell => cell.shape !== "connect-edge")

  const sortedCells = nodes.sort((a, b) => {
    return a.position.y - b.position.y
  })

  const graphData = [...sortedCells, ...cells.filter(cell => cell.shape === "connect-edge")]

  const idPre = addType === AddTypeEnum.COPY ? "copy" : "create"

  // 区分添加或者操作节点，目前id是由graph的长度组成，但是在删除之后，长度变小，有可能导致id重复
  // 生成1-100的任意整数
  const random1 = Math.floor(Math.random() * 1000 + 1)
  const random2 = Math.floor(Math.random() * 1000 + 1)
  let currentNode: Record<string, any> = {}
  if (addType && addType === AddTypeEnum.COPY) {
    const currentNodeIndex = graphData.findIndex(node => node.id === id)
    currentNode = graphData[currentNodeIndex + 1]
  } else {
    currentNode = graphData.find(node => node.id === id)
  }
  const currentNodeId = currentNode.id

  const targetX = getAbsolutePositionX(currentNode, operationNodeSize.width)
  const approvalNodesLength = graphData?.filter(item => item.shape === "USER_TASK")?.length

  const copyData = {
    approverSetting: cloneDeep(graphData.find(node => node.id === id)?.data?.approverSetting),
    approvalStrategy: cloneDeep(graphData.find(node => node.id === id)?.data?.approvalStrategy),
    passStrategy: cloneDeep(graphData.find(node => node.id === id)?.data?.passStrategy),
    assigneeNullStrategy: cloneDeep(graphData.find(node => node.id === id)?.data?.assigneeNullStrategy),
    rollback: cloneDeep(graphData.find(node => node.id === id)?.data?.rollback),
    deptManager: cloneDeep(graphData.find(node => node.id === id)?.data?.deptManager),
    members: cloneDeep(graphData.find(node => node.id === id)?.data?.members),
    role: cloneDeep(graphData.find(node => node.id === id)?.data?.role),
    approvalBtnOperation: cloneDeep(graphData.find(node => node.id === id)?.data?.approvalBtnOperation)
  }
  const newNodes = [
    {
      shape: item.type,
      id: `node_${random1}`,
      x: targetX,
      y: currentNode?.position.y + currentNode?.size.height + edgeHeight,
      data: {
        graph,
        id: `node_${random1}`,
        ...approvalNodeInitialData,
        nodeName: `审批节点${approvalNodesLength + 1}`,
        ...{
          ...(addType === AddTypeEnum.COPY ? copyData : {})
        }
      },
      zIndex: 1
    },
    {
      id: `edge_${idPre}${random1}`,
      shape: "connect-edge",
      source: currentNodeId,
      target: `node_${random1}`
    },
    {
      shape: "gateway",
      id: `node_${random2}`,
      x: currentNode.position.x,
      y: currentNode?.position.y + currentNode?.size.height + edgeHeight + operationNodeSize.height + edgeHeight,
      data: {
        graph,
        id: `node_${random2}`
      },
      zIndex: 1
    },
    {
      id: `edge_${random2}`,
      shape: "connect-edge",
      target: `node_${random2}`,
      source: `node_${random1}`
    }
  ]
  //将新节点插入到graphData.nodes中的currentNodeId节点后面
  const index = graphData.findIndex(node => node.id === currentNodeId)
  const beforeNodes = graphData.slice(0, index + 1)
  const afterNodes = graphData.slice(index + 1)
  //遍历afterNodes,如果当前数据是节点将当前添加节点之后的节点的x,y坐标更新
  //获取newNodes里面最后一个节点的x,y坐标
  //过滤掉newNodes里面的shape为connect-edge的节点
  const defaultY =
    currentNode?.position.y +
    currentNode?.size.height +
    edgeHeight +
    operationNodeSize.height +
    edgeHeight +
    currentNode?.size.height +
    edgeHeight
  let maxY = defaultY
  const updatedAfterNodes: any = []
  afterNodes.map((node, index) => {
    if (node.shape !== "connect-edge") {
      updatedAfterNodes.push({
        ...node,
        position: {
          x: node.position.x,
          y: index === 0 ? defaultY : maxY
        }
      })
      maxY = maxY + node.size.height + edgeHeight
    } else if (node.shape === "connect-edge" && node.source.cell === currentNodeId) {
      updatedAfterNodes.push({
        ...node,
        source: {
          cell: `node_${random2}`
        }
      })
    } else {
      updatedAfterNodes.push(node)
    }
  })

  const newGraphData = [...beforeNodes, ...newNodes, ...updatedAfterNodes]
  //将NewGraphData里面shape为connect-edge的放在最后
  const connectEdges = newGraphData.filter(node => node.shape === "connect-edge")
  const otherNodes = newGraphData.filter(node => node.shape !== "connect-edge")
  const afterConnectEdges = [...otherNodes, ...connectEdges]
  newNodes.map(node => {
    if (node.shape === "connect-edge") {
      graph.addEdge(node)
    } else {
      graph.addNode(node)
    }
  })

  // 同时更新graph里面的节点的x,y坐标
  graphData.map(item => {
    const node = graph.getCellById(item.id)
    const currentNode = afterConnectEdges.find(node => node.id === item.id)
    if (item.shape === "connect-edge") {
      node.setTarget({
        cell: currentNode.target.cell
      })
      node.setSource({
        cell: currentNode.source.cell
      })
    } else {
      node.position(currentNode.position.x, currentNode.position.y)
    }
  })
  // graph.fromJSON(afterConnectEdges)
}

/**
 * 删除节点，操作是去掉当前操作节点和该节点下一个网关节点，同时把连接这两个点的边去掉，再把边的target是该节点的边去掉（暂未考虑并行展示的点），
 * 再将source为该网关节点的边的source改成当前删除的操作节点的上一个节点（即该删除节点前面的网关节点），这里和添加节点的步骤保持一致
 */

// 删除节点
export const deleteNode = (nodeId: string, graph: any) => {
  const cells = graph.toJSON().cells
  // 将graphData里面按position.y排序
  // 获取所有节点
  const nodes = cells.filter(cell => cell.shape !== "connect-edge")

  const sortedCells = nodes.sort((a, b) => {
    return a.position.y - b.position.y
  })

  const graphData = [...sortedCells, ...cells.filter(cell => cell.shape === "connect-edge")]

  const deleteNodeIndex = graphData.findIndex(item => item.id === nodeId)
  //找到删除的这个点之后的一个网关节点
  const deleteNodeAfterNodeId = graphData.find(item => item?.source?.cell === nodeId)?.target?.cell

  // 连接该节点和下一个网关节点的边
  const deleteNodeAfterEdge = graphData.find(
    item => item.source?.cell === nodeId && item.target?.cell === deleteNodeAfterNodeId
  )

  // 以该节点为目标节点的边
  const deleteNodeBeforeEdge = graphData.find(item => item.target?.cell === nodeId)

  // 以该网关节点为起始节点的边
  const deleteNodeAfterEdgeId = graphData.find(item => item.source?.cell === deleteNodeAfterNodeId)?.id

  //再将新的graph里面source为deleteNodeAfterEdge的边的source改为nodeId前面一个网关节点
  const newGraph = graphData.map(item => {
    if (item?.source?.cell === deleteNodeAfterNodeId) {
      return {
        ...item,
        source: {
          cell: deleteNodeBeforeEdge?.source?.cell
        }
      }
    }
    return item
  })
  //新的graph为不含deleteNodeAfterNodeId,deleteNodeAfterEdgeId,deleteNodeBeforeEdgeId的graph，但此时graph里面节点的y坐标还没有更新
  const newGraphData = newGraph.filter(
    item =>
      item.id !== nodeId &&
      item.id !== deleteNodeAfterNodeId &&
      item.id !== deleteNodeAfterEdge?.id &&
      item.id !== deleteNodeBeforeEdge?.id
  )
  const newGraphDataWithY: any = []
  const deleteNodeAfterCells = graphData.slice(deleteNodeIndex + 2)?.filter(item => {
    return item?.shape !== "connect-edge"
  })
  deleteNodeAfterCells.map(item => {
    //只需要改变y坐标的节点
    if (item.shape === "connect-edge") {
      newGraphDataWithY.push(item)
    } else {
      //删除之后，删除的点之后的点的y坐标只需要在原来的基础上减去每次删除的两条边，和一个操作节点，网关节点的高度
      const deleteNodeAfterCellsHeight = operationNodeSize.height + edgeHeight * 2 + addNodeSize.height
      const height = item?.position?.y - deleteNodeAfterCellsHeight
      newGraphDataWithY.push({
        ...item,
        position: {
          x: item?.position?.x,
          y: height
        }
      })
    }
  })
  const deleteNodeAfterCellIds = newGraphDataWithY.map(item => item.id)
  const data = newGraphData.map(item => {
    if (deleteNodeAfterCellIds.includes(item.id)) {
      return {
        ...item,
        position: {
          x: item?.position?.x,
          y: newGraphDataWithY.find(node => node.id === item.id).position.y
        }
      }
    }
    return item
  })

  // 删除节点
  graph.removeNode(nodeId)
  let newAddEdge = {}

  graphData.map(item => {
    if (item?.source?.cell === deleteNodeAfterNodeId) {
      newAddEdge = {
        id: item.id,
        shape: "connect-edge",
        source: deleteNodeBeforeEdge?.source?.cell,
        target: item.target.cell
      }
    }
  })
  // 删除该节点之后的节点，删除的时候也会删除连接这个点的所有线
  graph.removeNode(deleteNodeAfterNodeId)
  // 删除边
  graph.removeEdge(deleteNodeAfterEdge?.id)
  graph.removeEdge(deleteNodeBeforeEdge?.id)
  graph.addEdge(newAddEdge)
  // 同时更新graph里面的节点的x,y坐标
  graph.toJSON().cells.map(item => {
    const node = graph.getCellById(item.id)
    const currentNode = data.find(node => node.id === item.id)
    if (item.shape === "connect-edge") {
      node.setTarget({
        cell: currentNode.target.cell
      })
      node.setSource({
        cell: currentNode.source.cell
      })
    } else {
      node.position(currentNode.position.x, currentNode.position.y)
    }
  })
  // graph.fromJSON(data)
}

/**
 * @description 格式化审批节点数据(将审批节点数据转换成后端需要的数据格式)
 * @param data
 * @returns task: 任务节点，目前只有审批节点
 * @returns lines: 连接节点的线
 * @returns events:事件节点，目前只有 结束节点
 * @returns start: 开始节点
 *
 */

// 在前端展示的时候，并不添加开始节点，在传给后端的时候才将开始节点first传给后端
// 开始节点前端id固定为 startNodeId ，所以在后端传给前端的时候，需要将这个节点去掉，将这个节点的下一个节点作为开始节点
export const formatApprovalNodeDataToEnd = (data: any) => {
  const startNode: any = { id: startNodeId, shape: "START_EVENT", x: 550, y: 60, width: 116, height: 32, data: {} }
  const endNode = data?.find(item => item.shape === "END_EVENT")
  const approvalNodes = data?.filter(item => item.shape === "USER_TASK")

  const firstNode = data?.find(item => item.shape === "APPLY_NODE")
  // 传给后端的节点，将startNode,tasks,endNode拼接起来,去掉gateway节点
  const nodes = [startNode, firstNode, ...approvalNodes, endNode]
  // 将这些节点都用一个线连起来
  const lines: any = []
  for (let i = 0; i < nodes.length - 1; i++) {
    lines.push({
      id: `edge_${i}`,
      sourceId: nodes[i].id,
      targetId: nodes[i + 1].id
    })
  }

  //开始节点start
  const start = {
    id: startNode.id,
    name: startNode.id,
    type: "START_EVENT",
    style: {
      x: startNode?.position?.x || startNode?.x,
      y: startNode?.position?.y || startNode?.y,
      ...startNodeSize
    }
  }

  // 审批节点tasks
  const tasks = approvalNodes.map(item => {
    let approver: Array<Record<string, string>> = []
    if (item.data.approverSetting === ApproveSettingEnum.USER) {
      approver = item.data.members
    } else if (item.data.approverSetting === ApproveSettingEnum.ROLE) {
      if (isObject(item.data.role)) {
        approver = [item.data.role]
      }
    }
    return {
      id: item.id,
      name: item.data.nodeName,
      type: "USER_TASK",
      style: {
        x: item?.position?.x || item?.x,
        y: item?.position?.y || item?.y,
        ...approvalNodeSize
      },
      assigneeConfig: {
        countersign: {
          approveStrategy: item.data.approveStrategy,
          passStrategy: item.data.passStrategy
        },
        assigneeNullStrategy: item.data.assigneeNullStrategy,
        //审批人设置
        type: item.data.approverSetting,
        approver: approver,
        button: item.data.approvalBtnOperation
          ?.filter(item => item.enable)
          .map(item => item.value)
          .join(","),
        rollback: item.data?.rollback,
        deptManager: item.data.approverSetting === ApproveSettingEnum.DEPT_MANAGER ? item.data?.deptManager : {}
      }
    }
  })

  // 结束节点events
  const end = {
    id: endNode.id,
    name: endNode.id,
    type: "END_EVENT",
    style: {
      x: endNode?.position?.x || endNode?.x,
      y: endNode?.position?.y || endNode?.y,
      ...endNodeSize
    }
  }

  return {
    start,
    tasks,
    lines,
    first: {
      id: firstNode.id,
      name: "申请节点",
      style: {
        x: firstNode?.position?.x || firstNode?.x,
        y: firstNode?.position?.y || firstNode?.y,
        width: 116,
        height: 40
      }
    },
    events: [end]
  }
}

// 格式化审批节点数据(将后端返回的数据转换成前端需要的数据格式)
export const formatApprovalNodeDataToFront = (data: any, graph: any) => {
  const { start, tasks, first, lines, events } = data
  const end = events?.find?.(item => item.type === "END_EVENT")
  // 因为first没有返回type
  const firstNode = {
    ...first,
    type: "APPLY_NODE"
  }
  const nodes = [firstNode, ...tasks, end]
  const nodesData = nodes.map((item: any) => {
    const config = {
      ...approvalNodeInitialData,
      nodeName: item.name,
      approveStrategy: item?.assigneeConfig?.countersign?.approveStrategy,
      passStrategy: item?.assigneeConfig?.countersign?.passStrategy,
      assigneeNullStrategy: item?.assigneeConfig?.assigneeNullStrategy,
      approverSetting: item?.assigneeConfig?.type,
      members: item?.assigneeConfig?.type === ApproveSettingEnum.USER ? item?.assigneeConfig?.approver : [],
      role: item?.assigneeConfig?.type === ApproveSettingEnum.ROLE ? item?.assigneeConfig?.approver[0] : {},
      approvalBtnOperation: approvalBtnInitialData.map(operation => ({
        ...operation,
        enable: item?.assigneeConfig?.button?.split(",")?.includes(operation.value)
      })),
      rollback: item?.assigneeConfig?.rollback,
      deptManager: item?.assigneeConfig?.deptManager
    }
    return {
      id: item.id,
      shape: item?.type,
      x: item.style?.x,
      y: item.style?.y,
      width: item.style?.width,
      height: item.style?.height,
      data: {
        graph,
        id: item.id,
        ...(item.type === "USER_TASK" ? config : {})
      }
    }
  })
  // 将nodesData里相邻的节点里加入一个网关节点
  const newNodesData: any = []
  nodesData.map((item, index) => {
    if (index === 0) {
      newNodesData.push(item)
    } else {
      const random = Math.floor(Math.random() * 1000 + 1)

      const gatewayNode = {
        shape: "gateway",
        id: `node_${random}`,
        x: 599,
        y: item.y - addNodeSize.height - edgeHeight,
        ...addNodeSize,
        data: {
          graph,
          id: `node_${random}`
        }
      }
      newNodesData.push(gatewayNode)
      newNodesData.push(item)
    }
  })
  // 在后端返回的lines的基础上遍历，用这条线的target,找到target对应节点前面一个节点A。
  // 新加一条线用这个找到的节点A当作source，用遍历的这条线的target当作新加的线的target，同时将遍历的这条线的target变成A

  const edges: any = []
  const realLines = lines.filter(item => item?.sourceId !== (start?.id || startNodeId))
  realLines.map(item => {
    const random = Math.floor(Math.random() * 1000 + 1)
    const targetIndex = newNodesData.findIndex(node => node.id === item.targetId)
    const beforeNodeId = newNodesData[targetIndex - 1]?.id
    const secondLine = {
      id: `edge_${random}`,
      shape: "connect-edge",
      source: {
        cell: beforeNodeId
      },
      target: {
        cell: item?.targetId
      }
    }
    const firstLine = {
      id: item?.id,
      shape: "connect-edge",
      source: {
        cell: item?.sourceId
      },
      target: {
        cell: beforeNodeId
      }
    }
    edges.push(firstLine)
    edges.push(secondLine)
  })
  return [...newNodesData, ...edges]
}
