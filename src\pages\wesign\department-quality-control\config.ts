import type { TableColumnItem, BaseOptionItem } from "@/types"

export const menuId = "/department-quality-control/medical-qc"

const qualityControlColumns: Array<TableColumnItem> = [
  { prop: "patientName", label: "姓名", minWidth: 200, must: true, showOverflowTooltip: false },
  { prop: "mrNo", label: "病案号", minWidth: 150, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 120, must: true },
  { prop: "visitId", label: "住院次数", minWidth: 120, sortable: true },
  { prop: "patientSex", label: "性别", minWidth: 120 },
  { prop: "inHospitalDatetime", label: "入院时间", minWidth: 180, sortable: true },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 120 },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 120 },
  { prop: "outHospitalDatetime", label: "出院时间", minWidth: 180, sortable: true },
  { prop: "doctorInCharge", label: "主治医师", minWidth: 100 },
  { prop: "residentDoctor", label: "住院医师", minWidth: 100 },
  { prop: "chargeNurse", label: "责任护士", minWidth: 100 },
  { prop: "repairCount", label: "返修次数", minWidth: 100 },
  { prop: "submitterName", label: "提交人", minWidth: 120 },
  { prop: "submitterTime", label: "提交时间", minWidth: 180, sortable: true },
  { prop: "repairCount", label: "返修次数", minWidth: 120 }
]

// 科室质控-待质控
export const waitingQualityControlColumns: Array<TableColumnItem> = [
  ...qualityControlColumns,
  { prop: "status", label: "质控状态", minWidth: 180 },
  { prop: "operation", label: "操作", width: 180, fixed: "right", must: true }
]

// 科室质控-已质控
export const qualityControlledColumns: Array<TableColumnItem> = [
  { prop: "patientName", label: "姓名", minWidth: 200, must: true, showOverflowTooltip: false },
  { prop: "mrNo", label: "病案号", minWidth: 150, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 120, must: true },
  { prop: "visitId", label: "住院次数", minWidth: 120, sortable: true },
  { prop: "patientSex", label: "性别", minWidth: 120 },
  { prop: "inHospitalDatetime", label: "入院时间", minWidth: 180, sortable: true },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 120 },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 120 },
  { prop: "outHospitalDatetime", label: "出院时间", minWidth: 180, sortable: true },
  { prop: "attendingDoctor", label: "主治医师", minWidth: 100 },
  { prop: "residentDoctor", label: "住院医师", minWidth: 100 },
  { prop: "chargeNurse", label: "责任护士", minWidth: 100 },
  { prop: "repairCount", label: "返修次数", minWidth: 100 },
  { prop: "submitterName", label: "提交人", minWidth: 120 },
  { prop: "submitterTime", label: "提交时间", minWidth: 180, sortable: true },
  { prop: "repairCount", label: "返修次数", minWidth: 120 },
  { prop: "qcDeptStatusEnum", label: "质控状态", minWidth: 180 },
  { prop: "operation", label: "操作", width: 140, fixed: "right", must: true }
]

// 医疗质控任务-待质控
export const waitingDeptQcTaskColumns: Array<TableColumnItem> = [
  ...qualityControlColumns,
  { prop: "operation", label: "操作", width: 100, fixed: "right", must: true }
]

// 医疗质控任务-已质控
export const DeptTaskQualityControlledColumns: Array<TableColumnItem> = [
  ...qualityControlColumns,
  { prop: "deptScoreStatus", label: "科室评分", minWidth: 110 },
  { prop: "qcDatetime", label: "质控时间", minWidth: 180, sortable: true },
  { prop: "operation", label: "操作", width: 160, fixed: "right", must: true }
]

export const visitIdOptions: Array<BaseOptionItem> = [
  {
    label: "大于",
    value: "greater_than"
  },
  {
    label: "小于",
    value: "less_than"
  },
  {
    label: "等于",
    value: "equal"
  }
]

// 医疗质控任务
export const medicalTaskTabsRouterList = [
  {
    label: "待质控",
    path: "/department-quality-control/medical-task/waiting-control"
  },
  {
    label: "已质控",
    path: "/department-quality-control/medical-task/controlled"
  }
]

// 医疗质控
export const medicalQcTabsRouterList = [
  {
    label: "待质控",
    path: "/department-quality-control/medical-qc/waiting-control"
  },
  {
    label: "已质控",
    path: "/department-quality-control/medical-qc/controlled"
  }
]

// 护理质控
export const nurseQcTabsRouterList = [
  {
    label: "待质控",
    path: "/department-quality-control/nurse-qc/waiting-control"
  },
  {
    label: "已质控",
    path: "/department-quality-control/nurse-qc/controlled"
  }
]

// 护理质控任务
export const nurseQcTaskTabsRouterList = [
  {
    label: "待质控",
    path: "/department-quality-control/nurse-task/waiting-control"
  },
  {
    label: "已质控",
    path: "/department-quality-control/nurse-task/controlled"
  }
]

// 分类校验table
export const sortCheckColumns = [
  { label: "序号", prop: "index", width: 60 },
  { label: "文书分类", prop: "name", width: 180 },
  { label: "状态", prop: "status", width: 60 },
  { label: "质控时间", prop: "qcDatetime", width: 180 }
]

export enum SortCheckStatusEnum {
  NORMAL = "NORMAL",
  HIATUS = "HIATUS"
}

// 缺失报告table
export const missingReportColumns = [
  { label: "序号", prop: "index", width: 60 },
  { label: "报告名称", prop: "name", minWidth: 120 },
  { label: "预计补交日期", prop: "predictDatetime", width: 160 },
  { label: "文书分类", prop: "mrClassCode", width: 120 },
  { label: "报告类型", prop: "type", width: 90 },
  { label: "缺失原因", prop: "reason", minWidth: 200 },
  { label: "状态", prop: "status", width: 80 }
]

export enum MissingReportStatusEnum {
  WAIT_SUBMIT = "WAIT_SUBMIT",
  COMPLETE_SUBMIT = "COMPLETE_SUBMIT"
}

// 病案评分table
export const medicalRecordRatingColumns: Array<TableColumnItem> = [
  { type: "selection", width: 50, fixed: "left" },
  { label: "评分项", prop: "name", showOverflowTooltip: false },
  { label: "评分级别", prop: "rejectLevel", width: 85 },
  { label: "评分标准", prop: "score", width: 85 }
]

// 评分级别枚举
export enum RejectLevelEnum {
  LEVEL_ONE = "LEVE_ONE",
  LEVEL_TWO = "LEVE_TWO",
  LEVEL_THREE = "LEVE_THREE"
}

// 交叉质控Table
export const crossQualityControlColumns: Array<TableColumnItem> = [
  { type: "selection", width: 50, fixed: "left" },
  { prop: "patientId", label: "患者编号", minWidth: 100, sortable: true, must: true },
  { prop: "visitId", label: "住院次数", minWidth: 90 },
  { prop: "patientName", label: "姓名", minWidth: 200, must: true },
  { prop: "mrNo", label: "病案号", minWidth: 150, sortable: true, must: true },
  { prop: "patientSex", label: "性别", minWidth: 60 },
  { prop: "inHospitalDatetime", label: "入院时间", minWidth: 170, sortable: true },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 100 },
  { prop: "outHospitalDatetime", label: "出院时间", minWidth: 170, sortable: true },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 100 },
  { prop: "doctorName", label: "主治医师", minWidth: 100 }
]

// 分配质控任务table
export const distributeColumns: Array<TableColumnItem> = [
  { type: "selection", width: 50, fixed: "left" },
  { prop: "realName", label: "质控员", minWidth: 100 },
  { prop: "jobId", label: "工号", minWidth: 100 },
  { prop: "deptName", label: "所属科室", minWidth: 100 }
]

// 进度查询table
export const processSearchColumns: Array<TableColumnItem> = [
  { prop: "patientId", label: "患者编号", minWidth: 100, sortable: true },
  { prop: "patientName", label: "姓名", minWidth: 100 },
  { prop: "mrNo", label: "病案号", minWidth: 150, sortable: true },
  { prop: "createDatetime", label: "分配时间", minWidth: 170, sortable: true },
  { prop: "qcType", label: "质控类型", minWidth: 120 },
  { prop: "qcOperator", label: "质控员", minWidth: 100 },
  { prop: "qcStatus", label: "质控状态", minWidth: 110 },
  { prop: "qcDatetime", label: "质控时间", minWidth: 180, sortable: true }
]

// 质控任务tab
export const qualityControlTaskRouterList = [
  { label: "待质控", path: "/department-quality-control/cross/waiting-control" },
  { label: "已质控", path: "/department-quality-control/cross/controlled" }
]

export const beforeDaysOptions: Array<BaseOptionItem> = [
  { label: "近7天", value: "7" },
  { label: "近14天", value: "14" },
  { label: "近1月", value: "30" },
  { label: "近3月", value: "90" },
  { label: "近6月", value: "180" }
]
