import { realNameValidator, phoneNumberValidator, jobIdValidator, IdNumberValidator } from "@/utils/form-validator"
import type { TableColumnItem, DialogFormConfigItem } from "@/types"
import type { FormRules } from "element-plus"

export const selectSearchTypeOption = [
  { label: "术语名称", value: "0" },
  { label: "拼音简码", value: "1" },
  { label: "编码", value: "2" }
]

export const userTableColumns: Array<TableColumnItem> = [
  { prop: "realName", label: "姓名", minWidth: 100 },
  { prop: "jobId", minWidth: 100, label: "工号", sortable: true },
  { prop: "phone", label: "联系电话", minWidth: 150 },
  { prop: "jobTitleName", label: "职称", minWidth: 120, sortable: true },
  { prop: "deptName", label: "所属科室", minWidth: 100 },
  { prop: "inside", label: "用户类型", minWidth: 100 },
  { prop: "expirationTime", label: "有效期", minWidth: 100 },
  { prop: "inpatientArea", label: "病区", minWidth: 100 },
  { prop: "specialties", label: "专业", minWidth: 100 },
  { prop: "sysUserStatus", label: "状态", minWidth: 100 },
  { prop: "operation", label: "操作", width: 180, fixed: "right" }
]

export type UserClassificationType = "allUser" | "unassignedUser" | "disableUser"

export type UserClassificationItem = {
  name: "全部用户" | "未分配用户" | "已禁用用户"
  type: UserClassificationType
  class: string
}

export const userClassificationList: Array<UserClassificationItem> = [
  { name: "全部用户", type: "allUser", class: "ri-user-fill" },
  { name: "未分配用户", type: "unassignedUser", class: "ri-pie-chart-fill" },
  { name: "已禁用用户", type: "disableUser", class: "ri-user-forbid-fill" }
]

export const addUserRules: FormRules = {
  realName: [
    { required: true, message: "请输入姓名", trigger: "blur" },
    { validator: realNameValidator, trigger: "blur" }
  ],
  jobId: [
    { required: true, message: "请输入工号", trigger: "blur" },
    { validator: jobIdValidator, trigger: "blur" }
  ],
  phone: [
    { required: true, message: "请输入联系电话", trigger: "blur" },
    { validator: phoneNumberValidator, trigger: "blur" }
  ],
  idCard: { validator: IdNumberValidator, trigger: "blur" }
}

export type DeptOptionItem = {
  wsid: string
  deptName: string
  deptCode: string
}

export const addUserDialogFormConfig: Array<DialogFormConfigItem> = [
  {
    type: "input",
    label: "姓名",
    prop: "realName",
    rules: [
      { required: true, message: "请输入姓名", trigger: "blur" },
      { validator: realNameValidator, trigger: "blur" }
    ]
  },
  {
    type: "input",
    label: "工号",
    prop: "jobId",
    rules: [
      { required: true, message: "请输入工号", trigger: "blur" },
      { validator: jobIdValidator, trigger: "blur" }
    ]
  },
  {
    type: "input",
    label: "联系电话",
    prop: "phone",
    rules: [
      { required: true, message: "请输入联系电话", trigger: "blur" },
      { validator: phoneNumberValidator, trigger: "blur" }
    ]
  },
  {
    type: "input",
    label: "身份证号",
    prop: "idCard",
    rules: [{ validator: IdNumberValidator, trigger: "blur" }]
  },
  {
    type: "select",
    label: "所属科室",
    prop: "deptCode",
    options: [],
    multiple: true,
    "multiple-limit": 3
  },
  {
    type: "select",
    label: "职称",
    prop: "jobTitleCode",
    options: []
  },
  {
    type: "select",
    label: "病区",
    prop: "inpatientAreaCode",
    options: []
  },
  {
    type: "select",
    label: "专业",
    prop: "specialtiesCode",
    options: []
  },
  {
    type: "select",
    label: "用户类型",
    prop: "inside",
    options: [
      { label: "内部用户", value: "INNER" },
      { label: "外部用户", value: "OUT" }
    ]
  },
  {
    type: "datetime",
    label: "有效期",
    prop: "expirationTime",
    disabledDate: date => {
      return date.getTime() < Date.now()
    },
    rules: [{ required: true, message: "请输入联系电话", trigger: "blur" }]
  }
]
