import type { TableColumnItem, DialogFormConfigItem } from "@/types"

export const roleTableColumns: Array<TableColumnItem> = [
  { prop: "roleName", label: "角色名称", minWidth: 100 },
  { prop: "remark", label: "角色描述", minWidth: 100 },
  { prop: "menuPermissionsCount", label: "菜单权限", minWidth: 100 },
  { prop: "butPermissionsCount", label: "操作权限", minWidth: 100 },
  { prop: "operation", label: "操作", width: 280, fixed: "right" }
]

export const drawerTableColumns: Array<TableColumnItem> = [
  { type: "index", prop: "index", label: "序号", minWidth: 60 },
  { prop: "realName", label: "姓名", minWidth: 100 },
  { prop: "jobId", label: "工号", minWidth: 100 },
  { prop: "phone", label: "联系电话", minWidth: 150 },
  { prop: "jobTitleName", label: "职称", minWidth: 100 },
  { prop: "deptName", label: "所属科室", minWidth: 100 },
  { prop: "operation", label: "操作", width: 100, fixed: "right" }
]

export const roleFormConfig: Array<DialogFormConfigItem> = [
  {
    type: "input",
    label: "角色名称",
    prop: "roleName",
    rules: [
      { required: true, message: "请输入角色名称", trigger: "blur" },
      { min: 1, max: 50, message: "输入字符应在1到50位之间", trigger: "blur" }
    ]
  },
  {
    type: "textarea",
    label: "角色描述",
    prop: "remark"
  }
]
