<template>
  <div class="common-search-container">
    <div class="custom-search-header">
      <span class="custom-search-header-title">打印工作量统计</span>
      <SearchContainer class="custom-search-header-config" @query-btn-click="handleData" @reset-btn-click="resetForm">
        <DaterangeFormItem v-model:model-value="formData.printRange" label="" />

        <el-form-item label="领取方式">
          <el-select v-model="formData.receiveType" clearable style="width: 140px">
            <el-option v-for="item in receiveTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>

        <el-form-item label="打印方案">
          <el-select v-model="formData.printTypeCode" clearable style="width: 140px">
            <el-option
              v-for="item in props.printPurposeOptions"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="是否开票">
          <el-select v-model="formData.printInvoice" clearable style="width: 140px">
            <el-option v-for="(item, index) in invoicingOptions" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </SearchContainer>
    </div>

    <div class="common-search-content custom-search-content">
      <el-button :icon="Download" @click="handleExport()">导出</el-button>
      <CommonTable
        :table-columns="workloadColumns"
        :request-api="getStatisticsWorkload"
        :request-params="filters"
        :data-callback="dataCallback"
      ></CommonTable>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive } from "vue"
import { Download } from "@element-plus/icons-vue"
import { SearchContainer, CommonTable, DaterangeFormItem } from "@/base-components"
import { Message, formatDate, downloadFile } from "@/utils"

import { getStatisticsWorkload, exportStatisticsWorkload } from "../../interface"
import { invoicingOptions, receiveTypeOptions } from "../../print-config"
import { workloadColumns } from "../config"

interface WorkloadStatisticProps {
  printPurposeOptions: Record<string, string | number>[]
}
const props = defineProps<WorkloadStatisticProps>()

/* ======================== 搜索相关数据及方法 ======================== */

interface FormState {
  //领取方式
  receiveType: string
  //打印方案
  printTypeCode: string
  //是否开票
  printInvoice: string
  // 时间
  printRange?: any
}

const formData = reactive<FormState>({
  receiveType: "",
  printTypeCode: "",
  printInvoice: "",
  printRange: []
})

const filters = reactive<Record<string, number | string>>({
  receiveType: "",
  printTypeCode: "",
  printInvoice: "",
  startDate: "",
  endDate: ""
})

// 搜索
const handleData = () => {
  filters.receiveType = formData.receiveType
  filters.printTypeCode = formData.printTypeCode
  filters.printInvoice = formData.printInvoice
  filters.startDate = formData.printRange[0] || ""
  filters.endDate = formData.printRange[1] || ""
}

// 重置查询
const resetForm = () => {
  formData.printTypeCode = ""
  formData.printInvoice = ""
  formData.receiveType = ""
  formData.printRange = []
  handleData()
}

/* ======================== 表格相关方法 ======================== */

// 导出
const handleExport = () => {
  exportStatisticsWorkload(filters).then(res => {
    downloadFile({ fileData: res.data, fileType: "application/vnd.ms-excel", fileName: "导出数据.xlsx" })
    Message.success("导出数据成功！")
  })
}

const dataCallback = (data: Array<Record<string, any>>) => {
  return data.map((item: Record<string, any>) => ({
    ...item,
    dateTime: formatDate(item.dateTime)
  }))
}
</script>

<style lang="less" scoped>
.custom-search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  .custom-search-header-title {
    font-size: 16px;
    font-weight: bold;
    color: rgba(10, 22, 51, 0.85);
  }
  .custom-search-header-config {
    background: #eff2f7;
    border-radius: 4px 4px 4px 4px;
    padding: 8px 20px;
    :deep(.el-form-item) {
      margin-bottom: 0;
    }
  }
}
.custom-search-content {
  box-shadow: none;
  padding: 0;
}
</style>
