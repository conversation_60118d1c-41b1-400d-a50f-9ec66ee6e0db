import type { SearchFormConfigItem, TableColumnItem, BaseOptionItem } from "@/types"

export enum PushStatusTypeEnum {
  ENABLE = "ENABLE", // 启用
  DISABLE = "DISABLE" // 禁用
}
export const statusOptions = [
  { value: "DISABLE", label: "已禁用" },
  { value: "ENABLE", label: "已启用" }
]

export enum PushMethodEnum {
  GET = "GET", // 立即发送
  POST = "POST" // 指定时间
}

// 消息推送搜索表单配置
export const formConfigSearchFormConfig: Array<SearchFormConfigItem> = [{ type: "input", label: "名称", prop: "name" }]

// 消息推送表格项
export const formConfigTableColumns: Array<TableColumnItem> = [
  { prop: "name", label: "名称", minWidth: 250 },
  { prop: "appScenario", label: "应用场景", minWidth: 250 },
  { prop: "appTypeName", label: "数据规范", minWidth: 180 },
  { prop: "status", label: "状态", minWidth: 180 },
  { prop: "operation", label: "操作", width: 180, fixed: "right" }
]

// 消息推送表格项
export const appScenarioOptions: Array<BaseOptionItem> = [
  { label: "住院病案首页编码", value: "HOME_PAGE_ENCODE" },
  { label: "住院病案首页HQMS上报", value: "HOME_PAGE_HQMS_DETAIL" },
  { label: "门急诊诊疗信息编码", value: "OUTPATIENT_SERVICE_ENCODE" },
  { label: "门急诊诊疗信息HQMS上报", value: "OUTPATIENT_SERVICE_HQMS" },
  { label: "医保结算清单编码", value: "MISL_ENCODE" },
  { label: "医保结算清单上报", value: "MISL_DETAIL" }
]
export const nameValidator = (_rule: any, value: string, callback: (err?: Error) => void) => {
  if (!value) callback()
  else if (/^[a-zA-Z0-9\u4e00-\u9fa5\\s\p{P}]{1,50}$/.test(value)) callback()
  else callback(new Error("支持数字，字母，限制50字符"))
}
