import { approvalStatusOptions } from "@/configs"
import type { TableColumnItem, SearchFormConfigItem, BaseOptionItem } from "@/types"

export const menuId = "/borrow/my-borrow"

export const tabsRouterList = [
  { label: "借阅查看", path: "/borrow/my-borrow/approved" },
  { label: "借阅记录", path: "/borrow/my-borrow/record" }
]

export const borrowApprovedSearchFormConfig: Array<SearchFormConfigItem> = [
  { type: "patient", label: "患者信息" },
  { type: "department", label: "出院科室", prop: "outHospitalDeptWsid" },
  { type: "daterange", label: "申请时间", prop: "applyDatetime" },
  { type: "daterange", label: "出院日期", prop: "outHospitalDatetime" }
]

export const borrowApprovedColumns: Array<TableColumnItem> = [
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "mrNo", label: "病案号", minWidth: 150, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 100, must: true },
  { prop: "visitId", label: "住院次数", minWidth: 120, sortable: true },
  { prop: "outHospitalDatetime", label: "出院日期", minWidth: 180, sortable: true },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 100 },
  { prop: "applyTypeName", label: "借阅类型", minWidth: 100, must: true },
  { prop: "timeLimitCh", label: "借阅时长", minWidth: 120, sortable: true, must: true },
  { prop: "applyDatetime", label: "申请时间", minWidth: 180, sortable: true },
  { prop: "approverDatetime", label: "审批时间", minWidth: 180, sortable: true },
  { prop: "operation", label: "操作", width: 180, fixed: "right", must: true }
]

export const borrowRecordSearchFormConfig: Array<SearchFormConfigItem> = [
  ...borrowApprovedSearchFormConfig,
  {
    type: "select",
    label: "审批状态",
    prop: "status",
    options: [
      { value: "", label: "全部" },
      { value: "0", label: "待审批" },
      { value: "1", label: "审批通过" },
      { value: "4", label: "审批超时" },
      { value: "2", label: "申请驳回" },
      { value: "3", label: "已过期" }
    ]
  }
]

export const borrowRecordColumns: Array<TableColumnItem> = [
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "mrNo", label: "病案号", minWidth: 150, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 100, must: true },
  { prop: "visitId", label: "住院次数", minWidth: 120, sortable: true },
  { prop: "outHospitalDatetime", label: "出院日期", minWidth: 180, sortable: true },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 100 },
  { prop: "applyTypeName", label: "借阅类型", minWidth: 100, must: true },
  { prop: "timeLimitCh", label: "借阅时长", minWidth: 120, sortable: true, must: true },
  { prop: "statusEnumName", label: "审批状态", minWidth: 100, must: true },
  { prop: "rejectReason", label: "驳回原因", minWidth: 120 },
  { prop: "applyDatetime", label: "申请时间", minWidth: 180, sortable: true },
  { prop: "approveLatestTime", label: "审批时限", minWidth: 180 },
  { prop: "overdueDatetime", label: "到期日期", minWidth: 180, sortable: true },
  { prop: "approverDatetime", label: "审批时间", minWidth: 180, sortable: true },
  { prop: "operation", label: "操作", width: 80, fixed: "right", must: true }
]

export const unitOptions = {
  HOUR: "小时",
  DAY: "天",
  MONTH: "月",
  YEAR: "年",
  FOREVER: "",
  UNKNOWN: "未知"
}
export const downloadPdfTypeOptions: Array<BaseOptionItem> = [
  {
    label: "整体一个PDF",
    value: "COMPOSE"
  },
  {
    label: "一个文书一个PDF",
    value: "ZIP"
  }
]
