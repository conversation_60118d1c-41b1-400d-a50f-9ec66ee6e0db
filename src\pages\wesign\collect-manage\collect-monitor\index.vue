<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="resetForm">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.complexSelect"
          v-model:filter-value="searchFormState.complexInput"
          label="患者信息"
        />

        <DaterangeFormItem v-model:model-value="searchFormState.executionTime" label="采集时间" />

        <CommonSelectFormItem
          v-model="searchFormState.mrClassCode"
          label="文档类型"
          :options="documentTypeOptions"
          filterable
        />

        <CommonSelectFormItem
          v-model="searchFormState.collectStatus"
          label="采集状态"
          :options="collectStatusOptions"
        />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="monitorTableRef"
        table-id="monitorTableIdent"
        :table-columns="monitorColumns"
        :request-api="getCollectMonitorList"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <template #header>
          <BatchOperationButton :icon="Operation" :disabled="!selectedRows.length" @click="handleBatchRecapture">
            批量重新采集
          </BatchOperationButton>
        </template>
        <template #collectStatusDesc="{ row }">
          <el-tag :type="row.collectStatusTag">{{ row.collectStatusDesc }}</el-tag>
        </template>
        <template #operation="{ row }">
          <TableButton
            :disabled="
              ![CollectStatusEnum.COLLECT_FAIL, CollectStatusEnum.COLLECT_WAIT].includes(row.collectStatusEnum)
            "
            :tooltip="row.collectStatusEnum === CollectStatusEnum.COLLECT_SUCCESS ? '采集已完成' : ''"
            @click="handleItemRecapture(row)"
          >
            重新采集
          </TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref, computed } from "vue"
import { Operation } from "@element-plus/icons-vue"
import {
  PageContainer,
  SearchContainer,
  CommonTable,
  TableButton,
  PatientFilterFormItem,
  DaterangeFormItem,
  BatchOperationButton,
  CommonSelectFormItem
} from "@/base-components"
import { TabsRouter } from "@/page-components"
import { useCommonOptions } from "@/hooks"
import { getAllDocTypes } from "@/interfaces"
import { useSystemStore } from "@/stores"
import { formatDatetime, Message, toastError } from "@/utils"
import { monitorColumns, collectStatusOptions, CollectStatusEnum, tabsRouterList } from "./config"
import { getCollectMonitorList, recaptureData } from "./interface"

const systemStore = useSystemStore()

// 获取所有病案类型选项
const { options: documentTypeOptions } = useCommonOptions({
  getOptionsApi: getAllDocTypes,
  labelAlias: "mrClassName",
  valueAlias: "mrClassCode"
})

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  complexInput: "",
  complexSelect: "patientName",
  executionTime: "",
  mrClassCode: "",
  collectStatus: ""
})

const searchParams = reactive({
  filters: ""
})

// 重置
const resetForm = () => {
  searchParams.filters = ""
  for (let prop in searchFormState) {
    searchFormState[prop] = ""
  }
  searchFormState.complexSelect = "patientName"
}

// 搜索
const handleQuery = () => {
  const { complexInput, complexSelect, mrClassCode, collectStatus, executionTime } = searchFormState
  const filterConditions: Array<string> = []
  if (complexInput && complexSelect) filterConditions.push(`${complexSelect}=${complexInput}`)
  if (mrClassCode) filterConditions.push(`mrClassCode=${mrClassCode}`)
  if (collectStatus) filterConditions.push(`collectStatus=${collectStatus}`)
  if (executionTime && executionTime.length === 2) {
    filterConditions.push(
      `executionTime>=${executionTime[0]}`,
      `executionTime<=${executionTime[1] + (1000 * 60 * 60 * 24 - 1)}`
    )
  }
  searchParams.filters = filterConditions.join(",")
}

/* ======================== 表格相关方法 ======================== */

const monitorTableRef = ref<InstanceType<typeof CommonTable>>()

// 当前表格中选中的行
const selectedRows = computed(() => monitorTableRef.value?.tableState?.selectedRows ?? [])

const dataCallback = (data: Array<Record<string, any>>) => {
  const collectStatusMap = new Map([
    [CollectStatusEnum.COLLECT_FAIL, { desc: "采集失败", tag: "danger" }],
    [CollectStatusEnum.COLLECT_WAIT, { desc: "待采集", tag: "warning" }],
    [CollectStatusEnum.COLLECT_SUCCESS, { desc: "采集成功", tag: "success" }]
  ])
  return data.map(item => ({
    ...item,
    executionTime: formatDatetime(item.executionTime),
    collectStatusDesc: collectStatusMap.get(item.collectStatusEnum)?.desc ?? "未知",
    collectStatusTag: collectStatusMap.get(item.collectStatusEnum)?.tag ?? ""
  }))
}

// 批量采集
const handleBatchRecapture = () => {
  const wsids = selectedRows.value.map(item => item.id)
  systemStore.showLoading("正在重新采集中，请稍等")
  recaptureData({ params: wsids })
    .then(() => Message.success("提交成功，后台将进行异步采集，请稍后查看"))
    .catch(err => toastError(err, "重新采集失败"))
    .finally(() => {
      systemStore.hideLoading()
      monitorTableRef.value?.refreshTableData()
    })
}

// 重新采集
const handleItemRecapture = row => {
  systemStore.showLoading("正在重新采集中，请稍等")
  recaptureData({ params: [row.id] })
    .then(() => Message.success("提交成功，后台将进行异步采集，请稍后查看"))
    .catch(err => toastError(err, "重新采集失败"))
    .finally(() => {
      systemStore.hideLoading()
      monitorTableRef.value?.refreshTableData()
    })
}
</script>
