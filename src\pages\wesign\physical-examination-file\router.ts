import type { RouteRecordRaw } from "vue-router"

const physicalExaminationFileRouter: RouteRecordRaw = {
  path: "/physical-examination-file",
  name: "PhysicalExaminationFile",
  redirect: "/physical-examination-file/list",
  meta: {
    title: "体检档案",
    icon: "ri-health-book-line",
    type: "single",
    showBreadcrumb: true
  },
  children: [
    {
      path: "/physical-examination-file/list",
      meta: { title: "体检档案" },
      component: () => import("./physical-examination-file-list/index.vue")
    },
    {
      path: "/physical-examination-file/detail",
      meta: { title: "体检档案详情", hideMenu: true },
      component: () => import("./physical-examination-file-detail/index.vue")
    }
  ]
}

export default physicalExaminationFileRouter
