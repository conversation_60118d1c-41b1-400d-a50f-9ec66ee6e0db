import axios from "./axios-instance"

/* ======================== 文件公共接口 ======================== */

// 获取图片
export function getImage(fileWsid: string) {
  return axios({
    method: "get",
    url: `/api/files/${fileWsid}`,
    responseType: "blob"
  }).then(res => {
    return URL.createObjectURL(res.data)
  })
}

// 通过wsid获取动态图片进行base64转换
export function getImageBase64(fileWsid: string): Promise<string> {
  return new Promise((resolve, reject) => {
    axios({
      method: "get",
      url: `/api/files/${fileWsid}`,
      responseType: "blob"
    })
      .then(res => {
        const img = new Image()
        img.src = URL.createObjectURL(res.data)
        img.onload = function () {
          const canvas = document.createElement("canvas")
          canvas.width = img.width
          canvas.height = img.height
          const ctx = canvas.getContext("2d")
          ctx?.drawImage(img, 0, 0, img.width, img.height)
          resolve(canvas.toDataURL())
        }
      })
      .catch(err => {
        reject(err)
      })
  })
}

// 查看图片(SVG)
export function viewImage(fileWsid: string) {
  return axios({
    method: "get",
    url: `/api/files/binary/${fileWsid}`,
    headers: {
      "Content-Type": "multipart/form-data"
    },
    responseType: "blob"
  }).then(res => {
    return URL.createObjectURL(res.data)
  })
}

// 获取图片
export function fragmentingFileApi(params: { fileWsid: string; range: string; blob: boolean }) {
  return axios({
    method: "get",
    url: `/api/files/binary/${params.fileWsid}?range=${params.range}`,
    responseType: params.blob ? "blob" : "json"
  })
}

// 文件重命名
export const fileRenameApi = (wsid: string, documentName: string) => {
  return axios({
    method: "post",
    url: `/api/document/bag/file-rename?wsid=${wsid}&documentName=${documentName}`
  })
}
