<template>
  <div class="final-quality-score-container">
    <div class="score-wrapper">
      <div class="score-title">终末质控平均得分</div>
      <div class="score-content">
        <span class="score">{{ scoreState.averageScore }}</span>
        <span class="score-unit">分</span>
      </div>
      <div class="same-period">
        <span class="same-period-title">去年同期</span>
        <span class="same-period-score">{{ scoreState.lastYearScores }}</span>
      </div>
      <div class="compare-ratio">
        <span class="compare-ratio-title">同比</span>
        <span
          class="compare-ratio-score"
          :style="{
            color: scoreRatio.color
          }"
        >
          {{ scoreRatio.value }}
          <i :class="scoreRatio.icon"></i>
        </span>
      </div>
    </div>
    <div class="vertical-line"></div>
    <div class="chart-container">
      <div class="chart-title">甲乙丙分布</div>
      <v-chart class="bar-chart" :option="options" autoresize></v-chart>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, computed, watch } from "vue"
import { PieChart } from "echarts/charts"
import { TooltipComponent, LegendComponent } from "echarts/components"
import { use } from "echarts/core"
import { CanvasRenderer } from "echarts/renderers"
import VChart from "vue-echarts"
import { getMedicalQualityReportLastYearApi } from "../../interface"
import { getMidnightMillis } from "../config"

use([TooltipComponent, LegendComponent, PieChart, CanvasRenderer])

const props = defineProps<{
  state: {
    outHospitalDeptWsid: string
    outHospitalDatetime: [number, number]
  }
}>()

const scoreState = reactive({
  averageScore: 0,
  lastYearScores: 0,
  levels: [] as Array<{ value: number; name: string }>
})

// 用今年的数据和去年的数据对比
const scoreRatio = computed(() => {
  if (scoreState.lastYearScores === 0 && scoreState.averageScore === 0) {
    return {
      value: `0%`,
      color: "#00a854",
      icon: "ri-arrow-down-line"
    }
  }
  if (scoreState.lastYearScores === 0 && scoreState.averageScore > 0) {
    return {
      value: `+100%`,
      color: "#d9001b",
      icon: "ri-arrow-up-line"
    }
  }
  if (scoreState.lastYearScores === 0 && scoreState.averageScore < 0) {
    return {
      value: `-100%`,
      color: "#00a854",
      icon: "ri-arrow-down-line"
    }
  }
  const ratio = ((scoreState.averageScore - scoreState.lastYearScores) / scoreState.lastYearScores) * 100
  if (ratio > 0) {
    return {
      value: `+${ratio.toFixed(2)}%`,
      color: "#d9001b",
      icon: "ri-arrow-up-line"
    }
  }
  return {
    value: `${ratio.toFixed(2)}%`,
    color: "#00a854",
    icon: "ri-arrow-down-line"
  }
})

watch(
  () => props.state,
  () => {
    getMedicalQualityReportLastYearApi({
      outHospitalDeptWsid: props.state.outHospitalDeptWsid,
      startDate: getMidnightMillis(props.state.outHospitalDatetime[0]),
      endDate: getMidnightMillis(props.state.outHospitalDatetime[1]) + 1000 * 60 * 60 * 24 - 1
    }).then(res => {
      const data = res.data.data
      scoreState.averageScore = data?.averageScore
      scoreState.lastYearScores = data?.lastYearScores
      scoreState.levels = data.levels.map(item => ({
        value: item.count,
        name: item.level
      }))
    })
  },
  {
    deep: true,
    immediate: true
  }
)

const options = computed(() => {
  return {
    tooltip: {
      trigger: "item",
      formatter: `{c}份`
    },
    legend: {
      left: "center",
      bottom: "1%",
      icon: "rect",
      itemWidth: 15,
      itemHeight: 15,
      orient: "vertical",
      formatter: name => {
        const data = scoreState.levels.find(item => item.name === name) as Record<string, any>
        const allCOunt = scoreState.levels.reduce((prev, cur) => prev + cur.value, 0)
        const percent = ((data.value / allCOunt) * 100).toFixed(2)
        return `${name}    ${percent}%`
      }
    },
    color: ["#FC5565", "#FEC82E", "#1BA1FC"],
    series: [
      {
        type: "pie",
        radius: ["40%", "70%"],
        center: ["50%", "30%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 2,
          borderColor: "#fff",
          borderWidth: 2
        },
        label: {
          show: false,
          position: "center"
        },
        labelLine: {
          show: false
        },
        data: scoreState.levels
      }
    ]
  }
})

defineExpose({
  scoreState
})
</script>

<style lang="less" scoped>
.final-quality-score-container {
  display: flex;
  // align-items: center;
  width: 430px;
  max-width: 430px;
  border: 1px solid #d4d4d4;
  border-radius: 4px;
  padding: 20px 20px 0 20px;
  box-sizing: border-box;
  color: #333;
  justify-content: space-between;

  .score-wrapper {
    min-width: 140px;
    padding-bottom: 20px;
    .score-title {
      font-weight: bold;
    }

    .score-content {
      margin: 50px 0;
      .score {
        font-size: 28px;
        font-weight: bold;
        margin-right: 8px;
      }
    }

    .same-period {
      margin-bottom: 15px;
      .same-period-title {
        margin-right: 20px;
      }
    }

    .compare-ratio {
      .compare-ratio-title {
        margin-right: 45px;
      }
      .compare-ratio-score {
        color: #d9001b;
      }
    }
  }

  .vertical-line {
    height: 80%;
    width: 0px;
    border: 1px solid rgba(215, 215, 215, 0.6);
  }

  .chart-container {
    min-width: 200px;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .chart-title {
      margin-bottom: 10px;
      font-weight: bold;
    }

    .bar-chart {
      width: 100%;
      height: 250px;
    }
  }
}
</style>
