<template>
  <el-drawer v-model="state.visible" v-loading="state.loading" title="借阅详情" size="700">
    <el-descriptions :column="2">
      <el-descriptions-item label="选择文件：" width="60%">
        {{ state.borrowDetail.fileCount }}
      </el-descriptions-item>
      <el-descriptions-item label="借阅时长：" width="40%">
        {{ state.borrowDetail.timeLimitUnit === "FOREVER" ? "永久" : state.borrowDetail.timeLimitCh }}
        {{ unitOptions[state.borrowDetail.timeLimitUnit] }}
      </el-descriptions-item>
      <el-descriptions-item label="借阅权限：" width="60%">
        <el-checkbox-group v-model="state.checkedPermissionList" style="display: inline-block">
          <el-checkbox :disabled="true" label="viewPermission">查看</el-checkbox>
          <el-checkbox :disabled="true" label="printPermission">打印</el-checkbox>
          <el-checkbox :disabled="true" label="exportPermission">导出</el-checkbox>
        </el-checkbox-group>
      </el-descriptions-item>
      <el-descriptions-item label="借阅类型：" width="40%">
        {{ state.borrowDetail.applyTypeName }}
      </el-descriptions-item>
      <el-descriptions-item label="借阅原因：" width="60%">
        {{ state.borrowDetail.applyReason || "--" }}
      </el-descriptions-item>
      <el-descriptions-item label="病人姓名：" width="40%">{{ state.patientName }}</el-descriptions-item>
      <el-descriptions-item label="通过类型：" width="60%">
        {{ state.borrowDetail.approveMethod === "DOCUMENT" ? "按文件" : "按页数" }}
      </el-descriptions-item>
      <!-- <el-descriptions-item v-show="state.borrowDetail.approveMethod === 'PAGE'" label="通过页数：">
        {{ state.borrowDetail.approveFilePage }}
      </el-descriptions-item> -->
    </el-descriptions>

    <div v-show="state.borrowDetail.approveMethod === 'DOCUMENT'" class="approval-tree">
      <div class="approval-tree-header">
        <div class="approval-tree-title">全部文件</div>
      </div>
      <div class="approval-tree-content">
        <CommonTree :data="state.borrowTree" style-type="approval">
          <template #title="{ node, data }">
            <div class="approval-tree-node">
              <OverflowTooltip :content="getNodeTooltipContent(node, data, false)" max-width="85%" />
              <!-- 被驳回 -->
              <el-tag v-if="data.statusEnum === 'AUDIT_REFUSE'" type="danger">驳回</el-tag>
              <!-- 审批通过 -->
              <el-tag v-if="data.statusEnum === 'AUDIT_PASS'" type="success">通过</el-tag>
            </div>
          </template>
        </CommonTree>
      </div>
    </div>

    <div v-show="state.borrowDetail.approveMethod === 'PAGE'" class="approval-tree">
      <div class="approval-tree-header">
        <div class="approval-tree-title">全部文件</div>
      </div>
      <div class="approval-tree-content">
        <CommonTree :data="state.pageBorrowTree" style-type="approval" />
      </div>
    </div>

    <ApprovalDetail :business-key="state.businessKey" />

    <template #footer>
      <div class="dialog-footer flex-center">
        <el-button type="primary" plain @click="close()">关闭</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { reactive } from "vue"
import { CommonTree, OverflowTooltip } from "@/base-components"
import { ApprovalDetail } from "@/page-components"
import { formatRecordTree, getNodeTooltipContent, toastError } from "@/utils"
import { getBorrowDetailApi } from "./interface"

const state = reactive({
  businessKey: "",
  loading: false,
  visible: false,
  patientName: "",
  borrowDetail: "" as any,
  borrowTree: [] as any[], // 按照文件目录审批的文件树
  pageBorrowTree: [] as any[], // 按照页数审批的文件树
  checkedPermissionList: [] as string[]
})

const unitOptions = {
  HOUR: "小时",
  DAY: "天",
  MONTH: "月",
  YEAR: "年",
  FOREVER: ""
}

// 获取借阅详情
const getBorrowDetail = id => {
  state.visible = true
  state.loading = true
  getBorrowDetailApi(id)
    .then(res => {
      const borrowDetail = res.data.data
      borrowDetail.treeInfo && (state.borrowTree = formatRecordTree(borrowDetail.treeInfo))
      borrowDetail.borrowItems && (state.pageBorrowTree = formatPageBorrowTree(borrowDetail.borrowItems))
      console.log(state.pageBorrowTree)
      state.businessKey = res.data.data.borrowWsid
      // 格式化数据
      const checkedPermissionList: string[] = []
      if (borrowDetail.viewPermission === "ENABLE") {
        checkedPermissionList.push("viewPermission")
      }
      if (borrowDetail.printPermission === "ENABLE") {
        checkedPermissionList.push("printPermission")
      }
      if (borrowDetail.exportPermission === "ENABLE") {
        checkedPermissionList.push("exportPermission")
      }
      state.checkedPermissionList = checkedPermissionList
      state.borrowDetail = borrowDetail
      state.loading = false
    })
    .catch(error => {
      state.loading = false
      toastError(error)
    })
}

// 格式化按页审批的列表
const formatPageBorrowTree = documentList => {
  documentList = documentList.filter(doc => doc.approveFilePage)
  for (let doc of documentList) {
    doc.type = "FILE"
    doc.name = doc.title
    doc.approveFilePage = doc.approveFilePage.split(",")
    doc.children = []
    for (let page of doc.approveFilePage) {
      if (page.includes("-")) {
        doc.children = doc.children.concat(getContinuousPage(doc))
      } else {
        doc.children.push({
          type: "PAGE",
          name: `第${page}页`,
          value: `${doc.wsid}_${page}`,
          wsid: `${doc.wsid}_${page}`
        })
      }
    }
  }
  return documentList
}

// 获取连续的页数 a-b形式的字符串
const getContinuousPage = (doc: any) => {
  const start = doc.approveFilePage[0]
  const end = doc.approveFilePage[2]
  const children: any[] = []
  for (let i = start; i <= end; i++) {
    children.push({
      type: "PAGE",
      name: `第${i}页`,
      value: `${doc.wsid}_${i}`,
      wsid: `${doc.wsid}_${i}`
    })
  }
  return children
}

/* ================ drawer ================ */

// 打开弹窗
const open = borrowInfo => {
  console.log(borrowInfo)
  state.patientName = borrowInfo.patientName
  state.visible = true
  getBorrowDetail(borrowInfo.id)
}

// 关闭弹窗
const close = () => {
  state.visible = false
}

defineExpose({ open, close })
</script>

<style lang="less" scoped>
.approval-tree-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: calc(100% - 40px);
  padding: 10px 20px;
  background: #f6f7f9;
  border-radius: 4px;
}

.approval-tree-content {
  padding: 10px 20px;
  max-height: 50vh;
  overflow-y: scroll;
}

.approval-tree-node {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
