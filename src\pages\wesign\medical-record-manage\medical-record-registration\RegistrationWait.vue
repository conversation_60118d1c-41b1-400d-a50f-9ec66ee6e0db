<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchForm
        :form-config="registrationSearchFormConfig"
        :form-state="searchFormState"
        @query-btn-click="handleQuery"
        @reset-btn-click="handleReset"
      />
    </template>

    <template #table>
      <CommonTable
        ref="commonTableRef"
        table-id="RegistrationWaitTable"
        :table-columns="registrationWaitTableColumns"
        :request-api="getRegistrationWaitListApi"
        :request-params="searchParams"
      >
        <template #inHospitalDatetime="{ row }">
          {{ formatDatetime(row.inHospitalDatetime) }}
        </template>
        <template #outHospitalDatetime="{ row }">
          {{ formatDatetime(row.outHospitalDatetime) }}
        </template>
        <template #operation="{ row }">
          <TableButton @click="showRegistrationDialog(row)">缺失登记</TableButton>
        </template>
      </CommonTable>
    </template>

    <!-- 登记弹窗 -->
    <DialogContainer v-model:visible="registrationState.dialogVisible" title="缺失报告登记" :width="810">
      <div v-loading="registrationState.loading" style="max-height: 70vh; overflow-y: auto">
        <el-form
          ref="registrationFormRef"
          :model="registrationFormState"
          :rules="registrationFormRules"
          label-width="120px"
        >
          <!-- 补交日期 -->
          <el-form-item prop="predictDatetime" label="预计补交日期：">
            <el-date-picker
              v-model="registrationFormState.predictDatetime"
              type="date"
              placeholder="开始日期"
              value-format="x"
              style="width: 100%"
            />
          </el-form-item>

          <!-- 病案类型 -->
          <CommonSelectFormItem
            v-model:model-value="registrationFormState.mrClassCode"
            label="文书分类："
            prop="mrClassCode"
            :options="curDeptRecordTypeOptions"
          />

          <!-- 报告类型 -->
          <el-form-item prop="type" label="报告类型：" required>
            <el-select v-model="registrationFormState.type">
              <el-option label="纸质" value="PAPER"></el-option>
              <el-option label="电子" value="ELECTRON"></el-option>
            </el-select>
          </el-form-item>

          <!-- 报告名称 -->
          <el-form-item prop="name" label="报告名称：">
            <el-input v-model="registrationFormState.name" placeholder="请输入报告名称" />
          </el-form-item>

          <!-- 缺失原因 -->
          <el-form-item prop="reason" label="缺失原因：">
            <el-input
              v-model="registrationFormState.reason"
              show-word-limit
              type="textarea"
              maxlength="100"
              placeholder="请输入缺失原因"
            />
          </el-form-item>

          <div class="flex-end" style="margin-bottom: 20px">
            <el-button type="primary" plain @click="addReport">添加</el-button>
          </div>
        </el-form>

        <!-- 缺失登记表格 -->
        <BaseTable
          :loading="registrationState.loading"
          :data="registrationState.tableData"
          :columns="missingReportTableColumns"
          border
        >
          <template #status="{ row }">
            <el-tag v-if="row.status === MissingReportStatusEnum.WAIT_SUBMIT" type="warning">待补交</el-tag>
            <el-tag v-else-if="row.status === MissingReportStatusEnum.COMPLETE_SUBMIT" type="success">已补交</el-tag>
          </template>
          <template #predictDatetime="{ row }">
            {{ formatDate(row.predictDatetime) }}
          </template>
          <template #mrClassCode="{ row }">
            {{ getRecordType(row.mrClassCode) }}
          </template>
          <template #type="{ row }">
            <span v-if="row.type === MissingReportTypeEnum.ELECTRON">电子</span>
            <span v-else-if="row.type === MissingReportTypeEnum.PAPER">纸质</span>
          </template>
          <template #operation="{ row }">
            <TableButton
              tooltip="已完成补交无法删除"
              :disabled="row.status === MissingReportStatusEnum.COMPLETE_SUBMIT"
              @click="handleDeleteReport(row)"
            >
              删除
            </TableButton>
          </template>
        </BaseTable>
      </div>
      <template #footer>
        <el-text type="danger" style="margin-right: 30px">添加和删除操作均在点击保存按钮后生效</el-text>
        <el-button plain :disabled="registrationState.confirmLoading" @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="registrationState.confirmLoading" @click="confirmRegister">保存</el-button>
      </template>
    </DialogContainer>
  </PageContainer>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue"
import {
  CommonTable,
  PageContainer,
  TableButton,
  DialogContainer,
  CommonSelectFormItem,
  BaseTable
} from "@/base-components"
import { TabsRouter, SearchForm } from "@/page-components"
import { MissingReportTypeEnum, MissingReportStatusEnum } from "@/configs"
import { useTableSearch, useCommonOptions } from "@/hooks"
import { getAllDocTypes } from "@/interfaces"
import { Message, SystemPrompt, formatDate, formatDatetime, toastError } from "@/utils"
import {
  tabsRouterList,
  registrationSearchFormConfig,
  registrationWaitTableColumns,
  registrationFormRules,
  missingReportTableColumns
} from "./config"
import {
  getRegistrationWaitListApi,
  getMissingReportListApi,
  submitMissingReportApi,
  getRecordTypeByDeptApi
} from "./interface"
import type { BaseOptionItem } from "@/types"

// 所有病案类型数据
let { getOptionLabel: getRecordType } = useCommonOptions({
  getOptionsApi: getAllDocTypes,
  labelAlias: "mrClassName",
  valueAlias: "mrClassCode"
})

// 当前科室的病案数据类型
let curDeptRecordTypeOptions = ref<BaseOptionItem[]>([])

/* ======================== 搜索相关数据及方法 ======================== */

const commonTableRef = ref()

const searchFormState = reactive({
  patientFilterProp: "patientName",
  patientFilterValue: "",
  doctorInCharge: "",
  outHospitalDatetime: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/* ======================== 登记相关 ======================== */

interface RegistrationState {
  dialogVisible: boolean // 弹窗显示
  selectedRow: any // 当前选择的病案
  loading: boolean // 确认loading
  tableData: Array<any> // 缺失报告表格信息
  confirmLoading: boolean
  modified: boolean // 是否已修改登记表格
}

const registrationState = reactive<RegistrationState>({
  dialogVisible: false,
  selectedRow: null,
  loading: false,
  tableData: [],
  confirmLoading: false,
  modified: false
})

const registrationFormRef = ref()

// form表单数据
const registrationFormState = reactive({
  predictDatetime: "", // 预计补交日期
  mrClassCode: "", // 文书分类
  type: "", // 报告类型
  name: "", // 报告名称
  reason: "" // 缺失原因
})

// 打开登记弹窗
const showRegistrationDialog = row => {
  registrationState.dialogVisible = true
  registrationState.loading = true
  registrationState.selectedRow = row
  Promise.all([getRecordTypeByDeptApi(row.outHospitalDeptWsid), getMissingReportListApi(row.wsid)])
    .then(res => {
      // 根据科室信息获取文书分类
      const { options } = useCommonOptions({
        optionsDataSource: res[0].data,
        labelAlias: "mrClassName",
        valueAlias: "mrClassCode"
      })
      curDeptRecordTypeOptions.value = options.value
      // 已经提交的缺失登记
      registrationState.tableData = res[1].data.data ?? []
    })
    .finally(() => {
      registrationState.loading = false
    })
}

// 点击添加缺失登记报告
const addReport = () => {
  for (let report of registrationState.tableData) {
    if (report.name === registrationFormState.name) {
      Message.error("重复的报告名称！")
      return
    }
  }
  registrationFormRef.value?.validate((valid: boolean) => {
    if (!valid) return
    registrationState.tableData.push({
      wsid: "",
      ...registrationFormState
    })
    registrationState.modified = true
    registrationFormRef.value?.resetFields()
  })
}

// 确认登记
const confirmRegister = () => {
  if (!registrationState.modified) {
    Message.warning("您尚未对数据进行操作")
    return
  }
  registrationState.confirmLoading = true
  submitMissingReportApi(
    registrationState.selectedRow?.wsid,
    registrationState.selectedRow?.inpNo,
    registrationState.tableData
  )
    .then(() => {
      Message.success("提交成功！")
      handleClose()
      commonTableRef.value.refreshTableData()
    })
    .catch(error => {
      toastError(error)
    })
    .finally(() => {
      registrationState.confirmLoading = false
    })
}

// 删除添加的报告
const handleDeleteReport = row => {
  SystemPrompt("确认删除该报告吗？点击保存后生效").then(() => {
    registrationState.tableData = registrationState.tableData.filter(item => item.name !== row.name)
    registrationState.modified = true
  })
}

// 关闭弹窗清空数据
const handleClose = () => {
  registrationState.dialogVisible = false
  registrationFormRef.value?.resetFields()
  registrationState.tableData = []
  registrationState.modified = false
}
</script>
