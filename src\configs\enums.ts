export enum UserTypeEnum {
  SUPER_ADMIN = "WSID_ROLE_SUPER_ADMIN", // 超级管理员
  ADMIN = "WSID_ROLE_ADMIN" // 管理员
}

export enum StatisticTypeEnum {
  DAY = "DAY",
  WEEK = "WEEK",
  MONTH = "MONTH",
  THREEMONTHS = "THREEMONTHS",
  SIXMONTHS = "SIXMONTHS",
  PASTYEAR = "PASTYEAR"
}

export enum MenuOperationEnum {
  View = "view", // 查看&详情
  Add = "add", // 新增
  Edit = "edit", // 修改&编辑
  Delete = "delete", // 删除
  Import = "import", // 导入
  Export = "export", // 导出
  History = "history", // 查看历史
  Borrow = "borrow", // 借阅
  Favorite = "favorite", // 收藏&取消收藏
  Upload = "upload", // 上传
  Verify = "verify", // 校验
  Toggle = "toggle", // 状态修改(禁用&启用)
  Approve = "approve", // 审批(通过&驳回)
  SignFor = "signFor", // 签收

  Trace = "trace", // 示踪(病案管理模块专有)
  Sync = "sync", // 同步(病案管理模块专有)
  Submit = "submit", // 提交(病案管理模块专有)
  MissingReport = "missingReport", // 缺失登记(病案管理模块专有)"
  Special = "special", // 特殊病案(病案管理模块专有)

  Cipher = "cipher", // 查看密钥(封存模块专有)
  Sealing = "sealing", // 封存&解封（封存模块专有）
  Append = "append", //追加（封存模块专有）
  Sign = "sign", //签名（封存模块专有）

  Catalog = "catalog", // 编目(编目模块专有)
  Assign = "assign", // 分配(编目模块专有)
  Progress = "progress", // 进度查询(编目模块专有),

  Execute = "execute", // 执行(采集模块专有)
  Collect = "collect", // 采集&重新采集(采集模块专有)

  QC = "qc", // 质控(质控模块专有)
  Distribute = "Distribute", //分配(质控模块专有)
  ProcessSearch = "ProcessSearch", //进度查询(质控模块专有)
  Track = "track", //质控轨迹

  Archive = "archive", // 归档(病案归档模块专有)

  Print = "print", // 打印,
  Authorize = "authorize", // 授权
  Regenerate = "regenerate", // 重新生成
  Resend = "resend", // 重新发送
  Printed = "printed", // 打印完成
  Mailed = "mailed", // 邮寄完成
  Take = "take", // 领取
  Reject = "reject", // 驳回
  Refund = "refund", // 退款
  Reconciliation = "reconciliation", // 对账明细
  Recall = "recall", // 召回
  Relate = "relate", // 关联操作

  Capture = "capture" // 拍摄
}

// 病案状态枚举
export enum MedicalRecordStatusEnum {
  COLLECTING = "COLLECTING", // 待回收 - 待提交
  WAIT_DEPT_QC = "WAIT_DEPT_QC", // 待科室质控 - 已提交（有交叉质控，待交叉质控完成）
  REPAIR = "REPAIR", // 返修 - 编目过程不合格可以发起返修，归档审核不通过
  WAIT_FINAL_QC = "WAIT_FINAL_QC", // 待终末质控 - 已编目
  WAIT_CATALOG = "WAIT_CATALOG", // 待编目 - 已科室质控
  WAIT_ARCHIVED = "WAIT_ARCHIVED", // 待归档 - 已终末质控
  ARCHIVED_YET = "ARCHIVED_YET", // 已归档
  SHELVES_YET = "SHELVES_YET", // 已上架
  RECALL = "RECALL" // 已召回
}

/* ================ 缺失相关 =================== */

// 缺失报告枚举
export enum MissingReportTypeEnum {
  ELECTRON = "ELECTRON", // 电子
  PAPER = "PAPER" // 纸质
}

// 缺失报告的状态
export enum MissingReportStatusEnum {
  WAIT_SUBMIT = "WAIT_SUBMIT", // 待补交
  COMPLETE_SUBMIT = "COMPLETE_SUBMIT" // 已补交
}

// 质控任务状态枚举
export enum QcStatusEnum {
  MEDICAL_PASS = "MEDICAL_PASS", //医疗质控通过
  NURSE_PASS = "NURSE_PASS", //护理质控通过
  DEPT_PASS = "DEPT_PASS", //科室质控通过
  CROSS_PASS = "CROSS_PASS", //交叉质控通过
  DEPT_FAIL = "DEPT_FAIL", //科室质控失败
  CROSS_FAIL = "CROSS_FAIL", //交叉质控失败
  WAIT = "WAIT", //未质控
  PASS = "PASS", //已质控
  FAIL = "FAIL", //质控失败
  NOT_PASS = "NOT_PASS", //质控不通过
  DELETE = "DELETE", //已删除
  UNDISTRIBUTED = "UNDISTRIBUTED", //未分配
  DEPT_FINAL_PASS = "DEPT_FINAL_PASS" // 医护质控通过
}

// 质控类型
export enum QualityControlTypeEnum {
  dept_qc = "dept-qc",
  cross_qc = "cross-qc",
  final_qc = "final-qc",
  DEPT_QC_MEDICAL = "DEPT_QC_MEDICAL", //科室医疗质控
  DEPT_QC_NURSE = "DEPT_QC_NURSE", //科室护理质控
  CROSS_QC_MEDICAL = "CROSS_QC_MEDICAL", //交叉医疗质控
  CROSS_QC_NURSE = "CROSS_QC_NURSE", //交叉护理质控
  FINAL_QC = "FINAL_QC", //终末质控
  DEPT_QC = "DEPT_QC" //科室医护质控
}

// 质控操作类型
export enum QualityControlOperatorTypeEnum {
  QC_MEDICAL_PASS = "QC_MEDICAL_PASS", //医疗质控通过
  QC_MEDICAL_SCORE = "QC_MEDICAL_SCORE", //医疗质控评分
  QC_MEDICAL_COMMENT = "QC_MEDICAL_COMMENT", // 医疗质控添加缺陷记录
  QC_MEDICAL_FAIL = "QC_MEDICAL_FAIL", //医疗质控不通过

  QC_NURSE_PASS = "QC_NURSE_PASS", //护理质控通过
  QC_NURSE_SCORE = "QC_NURSE_SCORE", //护理质控评分
  QC_NURSE_COMMENT = "QC_NURSE_COMMENT", // 护理质控添加缺陷记录
  QC_NURSE_FAIL = "QC_NURSE_FAIL", //护理质控不通过

  QC_FINAL_PASS = "QC_FINAL_PASS", //终末质控通过
  QC_FINAL_SCORE = "QC_FINAL_SCORE", //终末质控评分
  QC_FINAL_COMMENT = "QC_FINAL_COMMENT", // 终末质控添加缺陷记录
  QC_FINAL_FAIL = "QC_FINAL_FAIL", //终末质控不通过

  QC_DEPT_PASS = "QC_DEPT_PASS", //医护质控通过
  QC_DEPT_SCORE = "QC_DEPT_SCORE", //医护质控评分
  QC_DEPT_COMMENT = "QC_DEPT_COMMENT", // 医护质控添加缺陷记录
  QC_DEPT_FAIL = "QC_DEPT_FAIL" //医护质控不通过
}

// 搜索项type枚举
export enum QueryTermTypeEnum {
  Input = "INPUT", //输入框
  CountRange = "COUNT_RANGE", //最大值/最小值
  Daterange = "DATE_RANGE", //日期范围
  CodeSelect = "CODE_SELECT", //ICD编码搜索
  NameSelect = "NAME_SELECT", //ICD名称搜索
  RangeSelect = "RANGE_SELECT", //范围搜索 (数据规范)
  DeptSelect = "DEPT_SELECT" //部门选择
}

export enum FormItemType {
  BaseInput = "base_input",
  BaseTextarea = "base_textarea",
  BaseDate = "base_date",
  BaseRangeDate = "base_range_date",
  BaseRadio = "base_radio",
  BaseSelect = "base_select",
  BaseCheckbox = "base_checkbox",
  BaseNumber = "base_number",
  BaseAddress = "base_address", //地址
  BasePhone = "base_input",
  GroupHeadline = "group_headline",
  AnchorPoint = "group_anchor_point", //锚点标题
  //身份证组
  IdCardGroup = "id_card_group",
  DescText = "desc_text", //说明文字
  UserSign = "user_sign", //用户签名
  StaffSign = "staff_sign", //工作人员签名
  BloodDonationBarcode = "blood_donation_barcode", // 献血条码
  InformedConsentFormSign = "informed_consent_form_sign", // 知情同意书签名
  Table = "table" //表格
}

// 标准表单字段
export enum StandardFormItemType {
  STRING = "base_input",
  NUMBER = "base_number",
  DATE = "base_date",
  PHONE = "base_input",
  SINGLE_CHOICES = "base_select",
  MULTIPLE_CHOICES = "base_select",
  ADDRESS = "base_address"
}

// 日期格式化
export enum DateFormatEnum {
  Year = "YYYY",
  Month = "YYYY-MM",
  Date = "YYYY-MM-DD",
  Minutes = "YYYY-MM-DD HH:mm",
  Seconds = "YYYY-MM-DD HH:mm:ss"
}

// 动态表单字段查询类型
export enum QueryTypeEnum {
  REGULAR = "REGULAR", //固定字段查询
  CUSTOM = "CUSTOM" //自定义字段车查询
}

// 动态表单字段类型
export enum FieldTypeEnum {
  GENERAL = "GENERAL", //普通字段
  DATE = "DATE", //日期查询
  CHECKBOX = "CHECKBOX" //复选框查询
}

export enum MethodCodeEnum {
  EQ = "EQ",
  NE = "NE",
  GT = "GT",
  GE = "GE",
  LT = "LT",
  LE = "LE",
  "RANGE" = "RANGE",
  EQ_ONE = "EQ_ONE",
  NE_ONE = "NE_ONE",
  IN_ONE = "IN_ONE",
  ALL_IN = "ALL_IN",
  IN = "IN",
  NO_IN = "NO_IN",
  CUSTOM_DATE = "CUSTOM_DATE"
}

export enum MethodEnum {
  EQ = "等于",
  NE = "不等于",
  GT = "大于",
  GE = "大于等于",
  LT = "小于",
  LE = "小于等于",
  RANGE = "范围",
  // EQ_ONE = "等于任意一个",
  // NE_ONE = "不等于任意一个",
  // IN_ONE = "包含任意一个",
  // ALL_IN = "同时包含",
  IN = "包含",
  NO_IN = "不包含",
  CUSTOM_DATE = "时间自定义"
}
