import { Ref } from "vue"
import { cloneDeep } from "lodash-es"
import { Message, toastError } from "@/utils"

interface ITableOperationApiGroup<T> {
  saveApi: (data: T) => Promise<any>
  cancelSaveApi: (data: T) => Promise<any>
  checkApi: (data: T) => Promise<any>
  confirmApi: (data: T) => Promise<any>
}

const useCatalogTableOperation = <T extends Record<string, any>>(
  tables: Ref<T>,
  toBackEndData: Ref<T>,
  scenarioType: string,
  inpNo: Ref<string>,
  apiGroup: ITableOperationApiGroup<Record<string, any>>,
  loading: Ref<boolean>
) => {
  // 上、下移(todd 考虑置顶数据情况)
  const handleMove = (tableData, scope, type: string) => {
    tableData.splice(scope.$index, 1)
    const newIndex = type === "top" ? 0 : type === "up" ? scope.$index - 1 : scope.$index + 1
    tableData.splice(newIndex, 0, scope.row)
  }

  // 下拉框选中时更新
  const handleUpdateColumn = (tableData, scope, val, item, tableColumns) => {
    // console.log(`output->tableData`, tableData)
    // console.log(`output->scope`, scope)
    // console.log(`output->val`, val)
    // console.log(`output->item`, item)
    // console.log(`output->tableColumns`, tableColumns)
    // 下拉框选中时要同时改变有关联字段对应的值
    if (item.fieldLinkage) {
      tableData[scope.$index][item.prop] = val[item.optionKey]
      const fieldLinkageOptionKey = tableColumns.find(data => data.prop === item.fieldLinkage)?.optionKey
      // 关联的字段赋值
      tableData[scope.$index][item.fieldLinkage] = val[fieldLinkageOptionKey]
    }
  }

  // 添加、删除操作
  const handleOperation = (tableData, scope, type: "add" | "delete", tableId: string, formConfig) => {
    console.log(`output->tableData`, tableData)
    console.log(`output->tables.value`, tables.value)
    console.log(`output->tableId`, tableId)
    console.log(`output->formConfig`, formConfig)
    // 主诊断的table复制
    const cloneIndex = formConfig.find(item => item.prop === tableId).association ? 1 : 0

    //    cloneDeep(tables.value[tableId][0])
    const row = formConfig
      .find(item => item.prop === tableId)
      ?.tableFormConfig?.reduce((pre, next) => {
        return Object.assign(pre, { [next.prop]: "" })
      }, {})
    console.log(`output->tableData`, tableData)
    for (const key in row) {
      row[key] = ""
    }
    if (type === "add") {
      if (scope) tableData.splice(scope.$index, 0, cloneDeep(scope.row))
      else tableData.splice(tableData.length, 0, row)
    } else {
      tableData.splice(scope.$index, 1)
    }
  }

  /* ======================== 编目整体相关操作 ======================== */

  const { saveApi, cancelSaveApi, checkApi, confirmApi } = apiGroup

  // 保存(暂存)
  const save = () => {
    if (loading) loading.value = true
    console.log(`output->toBackEndData.value`, toBackEndData.value)

    return saveApi({
      scenarioType: scenarioType,
      appScenario: scenarioType,
      inpNo: inpNo.value,
      bodyData: toBackEndData.value,
      formData: toBackEndData.value,
      businessDataWsid: inpNo.value
    })
      .then(() => {
        Message.success("保存成功")
      })
      .catch(err => toastError(err, "保存失败"))
      .finally(() => {
        if (loading) loading.value = false
      })
  }

  // 取消保存
  const cancelSave = () => {
    if (loading) loading.value = true
    return cancelSaveApi({
      scenarioType: scenarioType,
      appScenario: scenarioType,
      inpNo: inpNo.value,
      businessDataWsid: inpNo.value
    })
      .then(res => {
        console.log(`output->res`, res)
        Message.success("操作成功")
      })
      .catch(err => toastError(err, "操作失败"))
      .finally(() => {
        if (loading) loading.value = false
      })
  }

  // 校验
  const check = () => {
    if (loading) loading.value = true
    return checkApi({
      scenarioType: scenarioType,
      inpNo: inpNo.value,
      bodyData: toBackEndData.value,
      formData: toBackEndData.value,
      businessDataWsid: inpNo.value,
      appScenario: scenarioType
    })
  }

  //完成编目
  const confirm = () => {
    if (loading) loading.value = true
    console.log(`output->toBackEndData.value`, JSON.stringify(toBackEndData.value))

    return confirmApi({
      scenarioType: scenarioType,
      appScenario: scenarioType,
      inpNo: inpNo.value,
      bodyData: toBackEndData.value,
      formData: toBackEndData.value,
      businessDataWsid: inpNo.value
    })
  }

  return {
    handleMove,
    handleUpdateColumn,
    handleOperation,

    save,
    cancelSave,
    check,
    confirm
  }
}

export default useCatalogTableOperation
