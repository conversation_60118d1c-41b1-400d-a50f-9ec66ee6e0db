import { approvalNodeInitialData } from "./config"

// 审批流程默认的节点数据
export const getDefaultProcessData = graph => {
  return [
    // {
    //   id:startNodeId,
    //   shape: "START_EVENT",
    //   x: 550,
    //   y: 0,
    //   width: 116,
    //   height: 32,
    //   data: {}
    // },
    // {
    //   shape: "gateway",
    //   id: "node_create1",
    //   x: 599,
    //   y: 62,
    //   width: 18,
    //   height: 18,
    //   data: {
    //     graph,
    //     id: "node_create1"
    //   }
    // },
    {
      shape: "APPLY_NODE",
      id: `node_1`,
      x: 550,
      y: 70,
      data: {
        graph,
        id: `node_1`,
        ...approvalNodeInitialData
      },
      zIndex: 1
    },
    {
      shape: "gateway",
      id: "node_2",
      x: 599,
      y: 140,
      width: 18,
      height: 18,
      data: {
        graph,
        id: "node_2"
      }
    },
    {
      shape: "USER_TASK",
      id: `node_349`,
      x: 493,
      y: 188,
      data: {
        graph,
        id: `node_349`,
        ...approvalNodeInitialData,
        nodeName: "审批节点1"
      },
      zIndex: 1
    },
    {
      data: { graph, id: "node_311" },
      id: "node_311",
      shape: "gateway",
      x: 599,
      y: 298,
      zIndex: 1
    },
    {
      shape: "END_EVENT",
      id: `node_create2`,
      x: 550,
      y: 336,
      data: {
        graph,
        id: "node_create2"
      }
    },
    {
      id: "edge_create349",
      shape: "connect-edge",
      source: "node_2",
      target: "node_349"
    },
    {
      id: "edge_311",
      shape: "connect-edge",
      source: "node_349",
      target: "node_311"
    },
    // {
    //   id: "edge_create475",
    //   shape: "connect-edge",
    //   source: "node_create1",
    //   target: "node_1"
    // },
    {
      id: "edge_770",
      shape: "connect-edge",
      source: "node_1",
      target: "node_2"
    },
    // {
    //   id: "edge_create0",
    //   shape: "connect-edge",
    //   source:startNodeId,
    //   target: "node_create1"
    // },
    {
      id: "edge1",
      shape: "connect-edge",
      source: "node_311",
      target: `node_create2`
    }
  ]
}

//边的高度
export const edgeHeight = 30

//操作节点宽度
const operationNodeWidth = 230
const operationNodeHeight = 80

export const operationNodeSize = {
  width: operationNodeWidth,
  height: operationNodeHeight
}

// 开始节点size
export const startNodeSize = {
  width: 116,
  height: 32
}

// 结束节点size
export const endNodeSize = {
  width: 116,
  height: 32
}

// 审批节点size
export const approvalNodeSize = {
  width: operationNodeWidth,
  height: operationNodeHeight
}

// 添加节点size
export const addNodeSize = {
  width: 18,
  height: 18
}

// 开始节点 id
export const startNodeId = "node_create0"

// 获取节点x坐标
export const getAbsolutePositionX = (sourceNode, targetWidth) => {
  const sourceNodePosition = sourceNode.position
  const sourceNodeSize = sourceNode.size
  const sourceNodeX = sourceNodePosition.x
  const sourceNodeWidth = sourceNodeSize.width
  const sourceNodeCenterX = sourceNodeX + sourceNodeWidth / 2
  const targetX = sourceNodeCenterX - targetWidth / 2
  return targetX
}
