<template>
  <PageContainer v-loading="loading" separate>
    <template #search>
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
          :filter-prop-options="filterPropOptions"
        />
        <DepartmentFormItem v-model:model-value="pageCountReportParams.outHospitalDeptWsid" label="出院科室" />
        <DepartmentFormItem v-model:model-value="pageCountReportParams.inHospitalDeptWsid" label="入院科室" />

        <DaterangeFormItem v-model:model-value="searchFormState.hospitalDateTime" label="出院时间" />
        <DaterangeFormItem v-model:model-value="searchFormState.archiveDateTime" label="归档时间" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="pageCountReportTableRef"
        table-id="pageCountReportTableIdent"
        :table-columns="pageCountReportColumn"
        :request-api="getPaperDetail"
        :request-params="searchParams"
      >
        <template #header>
          <!-- <el-button :icon="Download" @click="handleExport()">导出</el-button> -->
          <el-popover
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Export)"
            placement="bottom"
            width="210"
            trigger="hover"
          >
            <template #reference>
              <el-button :icon="Download">导出</el-button>
            </template>

            <PopoverButton
              :tip="`(当前选中${pageCountReportTableRef?.tableState.selectedRows.length}条数据)`"
              :center="false"
              @click="handleExport('selected')"
            >
              导出选中
            </PopoverButton>
            <el-tooltip effect="dark" content="单次最大数据导出量10000条" placement="bottom">
              <PopoverButton
                :center="false"
                :tip="`(共${pageCountReportTableRef?.tableState.total}条数据)`"
                @click="handleExport('all')"
              >
                导出全部
              </PopoverButton>
            </el-tooltip>
          </el-popover>
        </template>
        <template #archiveTime="{ row }">
          {{ formatDatetime(row.archiveTime) }}
        </template>
        <template #outHospitalDatetime="{ row }">
          {{ formatDatetime(row.outHospitalDatetime) }}
        </template>
        <template #inHospitalDatetime="{ row }">
          {{ formatDatetime(row.inHospitalDatetime) }}
        </template>
      </CommonTable>
    </template>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref, computed, watch } from "vue"
import { cloneDeep } from "lodash-es"
import { Download } from "@element-plus/icons-vue"
import {
  CommonTable,
  TableButton,
  PatientFilterFormItem,
  DaterangeFormItem,
  PageContainer,
  SearchContainer,
  DepartmentFormItem,
  PopoverButton
} from "@/base-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { useUserStore } from "@/stores"
import { formatDatetime, Message, downloadFile, toastError } from "@/utils"
import { pageCountReportColumn, filterPropOptions, menuId } from "./config"
import { getPaperDetail, exportPaperDetail } from "./interface"

const { hasOperationPermission } = useUserStore()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  patientFilterValue: "",
  patientFilterProp: "patientName",
  archiveDateTime: "",
  hospitalDateTime: ""
})

// 更新患者信息查询params
watch(
  () => searchFormState.patientFilterValue,
  val => {
    pageCountReportParams.patientName = ""
    pageCountReportParams.inpNo = ""
    pageCountReportParams.mrNo = ""
    pageCountReportParams[searchFormState.patientFilterProp] = val
  }
)

watch(
  () => searchFormState.archiveDateTime,
  val => {
    pageCountReportParams.archiveDateStart = val ? val[0] : ""
    pageCountReportParams.archiveDateEnd = val ? val[1] : ""
  }
)

watch(
  () => searchFormState.hospitalDateTime,
  val => {
    pageCountReportParams.outHospitalTimeStart = val ? val[0] : ""
    pageCountReportParams.outHospitalTimeEnd = val ? val[1] : ""
  }
)

const pageCountReportParams = reactive({
  patientName: "",
  inpNo: "",
  mrNo: "",
  outHospitalDeptWsid: "",
  inHospitalDeptWsid: "",
  outHospitalTimeStart: "",
  outHospitalTimeEnd: "",
  archiveDateStart: "",
  archiveDateEnd: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(
  {},
  "",
  {
    patientFilterProp: "patientName",
    archiveDateTime: [],
    patientName: "",
    archiveDateStart: "",
    archiveDateEnd: ""
  },
  pageCountReportParams,
  searchFormState
)

/* ======================== 表格相关方法 ======================== */

const pageCountReportTableRef = ref<InstanceType<typeof CommonTable>>()
const selectedRows = computed(() => pageCountReportTableRef.value?.tableState?.selectedRows ?? [])

const loading = ref(false)
const handleExport = (type: string) => {
  const params = cloneDeep(searchParams)
  if (type === "all") {
    if (!pageCountReportTableRef.value?.tableState.total) {
      return Message.warning("没有数据可以导出")
    }
    params.queryParams.inpNos = []
  } else {
    if (!selectedRows.value.length) return Message.warning("请至少勾选一条数据")
    params.queryParams.inpNos = selectedRows.value.map(item => item.inpNo) + ""
  }
  if (loading.value) return
  loading.value = true
  exportPaperDetail(params)
    .then(res => {
      downloadFile({ fileData: res.data, fileType: "application/vnd.ms-excel", fileName: "导出数据.xlsx" })
      Message.success("导出数据成功！")
      loading.value = false
    })
    .catch(err => {
      toastError(err, "导出失败")
      loading.value = false
    })
}
</script>

<style lang="less" scoped>
.process-header {
  display: flex;
  flex-direction: column;

  :deep(.el-form-item) {
    width: 100%;
  }
}

.process-container {
  height: 20px;
  display: flex;
  .process-label {
    margin-right: 8px;
  }
  .el-progress {
    width: 500px;
  }
}
</style>
