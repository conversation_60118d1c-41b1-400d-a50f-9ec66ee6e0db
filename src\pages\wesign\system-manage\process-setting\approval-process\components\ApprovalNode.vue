<template>
  <div class="approval-node-wrapper" click.stop>
    <div
      class="approval-node"
      :class="activeOperationBox ? 'active-node' : ''"
      @mouseenter="activeOperationBox = true"
      @mouseleave="activeOperationBox = false"
    >
      <div class="approval-node__content" @click="openDrawer">
        <div class="node-label">
          <img :src="SealIcon" class="operation-icon" alt="" />
          <span v-if="getNode().getData()?.nodeName" class="node-label__text">{{ getNode().getData()?.nodeName }}</span>
          <span v-else></span>
        </div>
        <div class="node-select">
          <span v-if="getNode().getData()?.approverSetting === 'USER'">
            {{
              getNode()
                .getData()
                .members?.map(item => item.name)
                ?.join(",") || "选择审批人"
            }}
          </span>

          <span v-if="getNode().getData()?.approverSetting === 'ROLE'">
            {{ getNode().getData()?.role?.name || "选择审批人" }}
          </span>

          <span v-if="getNode().getData()?.approverSetting === 'DEPT_MANAGER'">
            {{
              getNode().getData()?.deptManager?.filterName && getNode().getData()?.deptManager?.roleName
                ? `${getNode().getData()?.deptManager?.filterName}的${getNode().getData()?.deptManager?.roleName}`
                : "选择审批人"
            }}
          </span>

          <i class="ri-arrow-right-s-line"></i>
        </div>
      </div>

      <!-- 节点操作（编辑，删除） -->
      <div v-if="activeOperationBox" class="node-operation">
        <i class="ri-file-copy-line edit-node" @click="copyNode"></i>
        <el-popconfirm
          v-if="approvalNodesLength > 1"
          icon="el-icon-info"
          title="您确定要删除该填写节点吗？"
          :teleported="false"
          @confirm="handleDeleteNode"
        >
          <template #reference>
            <i class="ri-delete-bin-line delete-node"></i>
          </template>
        </el-popconfirm>
      </div>
    </div>

    <!-- 抽屉配置区 -->
    <el-drawer
      v-if="drawerVisible"
      v-model="drawerVisible"
      modal-class="custom-process-drawer"
      :append-to-body="true"
      :size="340"
      @close="saveApprovalConfig"
    >
      <template #header>
        <div>
          <span v-if="!editNodeName" style="height: 32px; display: inline-block">{{ state.nodeName }}</span>
          <el-input v-else v-model="state.nodeName" style="width: 220px" @blur="editNodeName = false" />
          <i
            class="ri-edit-2-line"
            style="cursor: pointer; padding-left: 4px"
            @click.stop="editNodeName = !editNodeName"
          ></i>
        </div>
      </template>

      <el-button-group class="button-group">
        <el-button
          :type="approvalNodeOperation === 'approver' ? 'primary' : 'default'"
          @click="approvalNodeOperation = 'approver'"
        >
          审批人
        </el-button>
        <el-button
          :type="approvalNodeOperation === 'approvalBtn' ? 'primary' : 'default'"
          @click="approvalNodeOperation = 'approvalBtn'"
        >
          审批按钮
        </el-button>
      </el-button-group>

      <!--审批人操作  -->
      <div v-if="approvalNodeOperation === 'approver'">
        <!-- 审批人设置 -->
        <div class="approver-setting">
          <div class="approver-setting__label">审批人设置</div>
          <el-radio-group v-model="state.approverSetting" class="approver-setting__radio" @change="updateApprover">
            <el-radio :label="ApproveSettingEnum.USER">指定成员</el-radio>
            <el-radio :label="ApproveSettingEnum.ROLE">指定角色</el-radio>
            <el-radio :label="ApproveSettingEnum.DEPT_MANAGER">部门主管</el-radio>
          </el-radio-group>

          <!-- 指定成员 -->
          <el-select
            v-if="state.approverSetting === ApproveSettingEnum.USER && drawerVisible"
            v-model="state.members"
            value-key="wsid"
            multiple
            clearable
            collapse-tags
            collapse-tags-tooltip
            style="width: 280px"
            placeholder="请选择"
            :max-collapse-tags="3"
          >
            <template #header>
              <el-input
                v-model="userState.searchValue"
                placeholder="成员姓名/工号"
                :prefix-icon="Search"
                clearable
              ></el-input>
            </template>
            <div v-infinite-scroll="scrollRenderUsers">
              <div v-for="item in userState.renderUsersData" :key="item.wsid">
                <el-option v-show="item.visible" :label="item.name" :value="item" />
              </div>
            </div>
          </el-select>

          <!-- 指定角色 -->
          <el-select
            v-if="state.approverSetting === ApproveSettingEnum.ROLE"
            v-model="state.role"
            v-loading="roleState.loading"
            value-key="wsid"
            clearable
            style="width: 280px"
            placeholder="请选择"
          >
            <template #header>
              <el-input
                v-model="roleState.searchValue"
                placeholder="角色名称"
                :prefix-icon="Search"
                clearable
                @input="getRolesData"
              ></el-input>
            </template>
            <el-option v-for="item in roleState.rolesData" :key="item.wsid" :label="item.name" :value="item" />
          </el-select>
        </div>

        <!-- 选择部门主管 -->
        <div v-if="state.approverSetting === ApproveSettingEnum.DEPT_MANAGER" class="approval-mode">
          <div class="approval-mode__label">选择部门主管</div>
          <div class="approval-mode_content">
            <el-select
              v-model="state.deptManager.filterCode"
              class="approval-mode_select"
              @change="updateDeptManagerCondition"
            >
              <el-option
                :label="deptManagerConditionName[DeptManagerConditionEnum.APPLY_USER]"
                :value="DeptManagerConditionEnum.APPLY_USER"
              />
              <el-option
                :label="deptManagerConditionName[DeptManagerConditionEnum.OUT_HOSPITAL_DEPT]"
                :value="DeptManagerConditionEnum.OUT_HOSPITAL_DEPT"
              />
            </el-select>
            <span>的</span>
            <el-select
              v-model="state.deptManager.roleWsid"
              class="approval-mode_select"
              @change="updateDeptManagerRole"
            >
              <el-option
                v-for="person in deptAdminOptions"
                :key="person.wsid"
                :label="person.roleName"
                :value="person.wsid"
              />
            </el-select>
          </div>
        </div>

        <!-- 多人审批方式 -->
        <!--如果审批人设置是指定用户，并且选择的用户低于2的话，多人审批方式不展示，否则展示 -->
        <div
          v-if="!(state.approverSetting === ApproveSettingEnum.USER && state.members.length < 2)"
          class="approval-mode"
        >
          <div class="approval-mode__label">多人审批方式</div>
          <div>
            <span>执行方式&nbsp;&nbsp;&nbsp;</span>
            <el-radio-group v-model="state.approveStrategy" class="approval-mode__radio">
              <el-radio :label="ApproveStrategyEnum.SYNC">顺序</el-radio>
              <el-radio :label="ApproveStrategyEnum.ASYNC">无序</el-radio>
            </el-radio-group>
          </div>
          <div>
            <span>执行结果&nbsp;&nbsp;&nbsp;</span>
            <el-radio-group v-model="state.passStrategy" class="approval-mode__radio">
              <el-radio :label="PassStrategyEnum.ALL_PASS">全部通过</el-radio>
              <el-radio :label="PassStrategyEnum.ONE_PASS">1人通过</el-radio>
            </el-radio-group>
          </div>
        </div>

        <!-- 审批人为空时 -->
        <!-- <div  class="approval-mode">
          <div class="approval-mode__label">审批人为空时&nbsp;&nbsp;&nbsp;</div>

          <el-radio-group v-model="state.assigneeNullStrategy">
            <el-row>
              <el-col :span="12">
                <el-radio :label="AssigneeNullStrategyEnum.AUTO_PASS">自动通过</el-radio>
              </el-col>
              <el-col :span="12">
                <el-radio :label="AssigneeNullStrategyEnum.ADMIN_ASSIGNEE">自动转交给流程负责人</el-radio>
              </el-col>
            </el-row>
          </el-radio-group>
        </div> -->
      </div>

      <!-- 审批按钮操作 -->
      <div v-if="approvalNodeOperation === 'approvalBtn'">
        <!-- 操作按钮 -->
        <div class="operation-btn">
          <div class="operation-btn__label">操作按钮</div>
          <BaseTable :data="state.approvalBtnOperation" :columns="approvalBtnOperationColumns" :border="true">
            <template #name="{ row }">
              <span>{{ row.name }}</span>
              <el-tooltip
                v-if="row.value === 'BTN_ROLLBACK'"
                content="通过选择前面的节点进行“回退”操作，在该节点重新进行审批"
              >
                <i class="ri-question-line"></i>
              </el-tooltip>
            </template>
            <template #enable="{ row }">
              <el-switch
                v-model="row.enable"
                :disabled="row.value === 'BTN_PASS'"
                active-color="#0c7ffc"
                inactive-color="#c0c4cc"
              />
            </template>
          </BaseTable>
        </div>
        <!-- 流程回退 -->
        <div v-if="state.approvalBtnOperation.find(item => item.value === 'BTN_ROLLBACK')?.enable" class="process-back">
          <div class="process-back__label">流程回退</div>
          <span>
            本节点可退回至
            <el-radio-group v-model="state.rollback.activitySet">
              <el-radio label="LAST">上一审批节点</el-radio>
              <el-radio label="SELECT">之前所有审批节点</el-radio>
            </el-radio-group>
          </span>
          <span>
            被回退的数据重新提交后
            <el-radio-group v-model="state.rollback.executeStrategy">
              <el-radio label="SEQUENCE">按流程顺序审批</el-radio>
              <el-radio label="SKIP">直接返回当前节点</el-radio>
            </el-radio-group>
          </span>
        </div>
      </div>

      <template #footer>
        <el-button size="small" @click="closeDrawer">取消</el-button>
        <el-button type="primary" size="small" @click="saveApprovalConfig">确定</el-button>
      </template>
    </el-drawer>
  </div>
</template>

<script lang="ts" setup>
import { ref, inject, reactive, watch, onMounted } from "vue"
import { cloneDeep, debounce } from "lodash-es"
import { Search } from "@element-plus/icons-vue"
import { BaseTable } from "@/base-components"
import SealIcon from "@/assets/svg/other/seal-icon.svg"
import { getRolesList, getFuzzyUsersInDeptsApi } from "@/interfaces"
import { Message, toastError } from "@/utils"
import { getDeptAdminApi } from "../../../interface"
import {
  deleteNode,
  addNodeFn,
  AddTypeEnum,
  PassStrategyEnum,
  ApproveStrategyEnum,
  DeptManagerConditionEnum,
  deptManagerConditionName,
  approvalBtnInitialData,
  AssigneeNullStrategyEnum,
  ApproveSettingEnum
} from "../config"

const approvalBtnOperationColumns = [
  { label: "操作按钮", prop: "name", minWidth: 115 },
  { label: "启用", prop: "enable", width: 80 }
]

const drawerVisible = ref(false)
/*===============节点配置=========================*/

const approvalNodeOperation = ref("approver")

const getNode: any = inject("getNode")
const nodeId = getNode().getData()?.id
const graph = getNode().getData()?.graph

interface ApproverProps {
  wsid: string
  name: string
}
const defaultState = {
  nodeName: "",
  // 审批人设置
  approverSetting: "",
  // 执行顺序
  approveStrategy: "",
  // 执行结果
  passStrategy: "",
  // 审批人为空时
  assigneeNullStrategy: "",
  // 成员，多选
  members: [] as Array<ApproverProps>,
  // 角色，单选
  role: {} as ApproverProps,
  approvalBtnOperation: cloneDeep(approvalBtnInitialData),
  rollback: {
    activitySet: "LAST", //回退节点选择
    executeStrategy: "SEQUENCE" //回退后执行策略
  },
  deptManager: {
    filterCode: "",
    filterName: "",
    roleWsid: "",
    roleName: ""
  }
}

//  审批节点个数,要保证至少有一个审批节点，所以审批节点在<=1时。不显示删除按钮
const approvalNodesLength = graph?.toJSON()?.cells?.filter((item: any) => item?.shape === "USER_TASK")?.length

// 当前节点
const cell = graph?.getCellById(nodeId)

const state = reactive(defaultState)

const openDrawer = () => {
  drawerVisible.value = true
  // 一次性获取所有用户
  getFuzzyUsersInDeptsApi({
    deptWsid: "",
    item: userState.searchValue
  })
    .then(res => {
      const users = res.data?.map(item => ({
        ...item,
        wsid: item.userWsid,
        name: item.realName
      }))
      userState.usersDataSource = users
    })
    .catch(err => {
      toastError(err, "获取用户失败")
      userState.usersDataSource = []
    })

  getRolesData()
  state.nodeName = getNode().getData()?.nodeName
  state.approveStrategy = getNode().getData()?.approveStrategy
  state.passStrategy = getNode().getData()?.passStrategy
  state.assigneeNullStrategy = getNode().getData()?.assigneeNullStrategy
  state.approverSetting = getNode().getData()?.approverSetting
  state.members = getNode().getData()?.members ?? []
  state.role = getNode().getData()?.role ?? {}
  state.approvalBtnOperation = cloneDeep(getNode().getData()?.approvalBtnOperation ?? approvalBtnInitialData)
  state.rollback = getNode().getData()?.rollback ?? defaultState.rollback
  state.deptManager = getNode().getData()?.deptManager ?? defaultState.deptManager
}

const closeDrawer = () => {
  userSelectVisible.value = false
  roleSelectVisible.value = false
  drawerVisible.value = false
  userState.searchValue = ""
  roleState.searchValue = ""
  approvalNodeOperation.value = "approver"
  for (let key in defaultState) {
    state[key] = defaultState[key]
  }
}

//是否能编辑节点名称，默认不可以编辑，此时显示节点名称，点击编辑按钮，可以编辑节点名称
const editNodeName = ref(false)

// 激活节点的操作框
const activeOperationBox = ref(false)

// 删除节点
const handleDeleteNode = () => {
  if (!nodeId || !graph) return
  deleteNode(nodeId, graph)
  Message.success("删除成功")
}

// 复制节点
const copyNode = () => {
  if (!nodeId) return
  addNodeFn(
    {
      type: "USER_TASK"
    },
    nodeId,
    graph,
    AddTypeEnum.COPY
  )
  Message.success("复制成功")
}

// graph.on("cell:changed", ({ cell, option }) => {
//   console.log("节点数据更新了:", cell, a)
// })

// 保存节点配置
const saveApprovalConfig = () => {
  if (!cell) return
  cell.setData(
    {
      ...cell.getData(),
      ...state
    },
    {
      overwrite: true
      // silent: true
    }
  )
  closeDrawer()
}

/* ===========================搜索角色=========================*/

// 角色选择框
const roleSelectVisible = ref(false)

const roleState = reactive({
  searchValue: "",
  rolesData: [] as Array<any>,
  loading: false
})

// 获取所有角色
const getRolesData = debounce(() => {
  if (!drawerVisible.value) return
  roleState.loading = true
  getRolesList({
    offset: 0,
    limit: 100,
    filters: roleState.searchValue ? `roleName=${roleState.searchValue}` : ""
  })
    .then(res => {
      const roles = res.data.data?.rows.map(item => ({
        wsid: item.wsid,
        name: item.roleName
      }))

      if (!state.role?.wsid) {
        roleState.rolesData = roles
        return
      }

      if (!roles.find((item: ApproverProps) => item.wsid === state.role?.wsid)) {
        roles.unshift(state.role)
      }

      roleState.rolesData = roles
    })
    .catch(err => {
      toastError(err)
      roleState.rolesData = []
    })
    .finally(() => {
      roleState.loading = false
    })
}, 500)

/*=============================用户搜索=============================*/
// 成员选择框
const userSelectVisible = ref(false)

const userState = reactive({
  searchValue: "",
  // 所有用户
  usersDataSource: [] as Array<any>,
  // 渲染的数据
  renderUsersData: [] as Array<any>,
  // 通过搜索过滤的数据
  filterUsersData: [] as Array<any>
})

// 滚动的时候渲染数据
const scrollRenderUsers = () => {
  const SCROLL_SIZE = 20
  const renderUserCount = userState.renderUsersData.length
  // 判断renderUsersData的长度是否大于等于filterUsersData的长度，如果大于等于的话，不再渲染,否则每次渲染20条数据
  if (renderUserCount >= userState.filterUsersData.length) return
  let nextRenderUsersData = userState.filterUsersData.slice(0, renderUserCount + SCROLL_SIZE).map(item => ({
    wsid: item.wsid,
    name: item.realName,
    visible: true
  }))

  // 查找当前已选中的用户是否在列表中，如果不在则追加到最前面
  state.members.forEach((member: ApproverProps) => {
    if (!nextRenderUsersData.find((user: ApproverProps) => user.wsid === member.wsid)) {
      nextRenderUsersData.unshift({ ...member, visible: false })
    }
  })
  userState.renderUsersData = nextRenderUsersData
}

watch(
  () => [userState.searchValue, userState.usersDataSource],
  val => {
    userState.filterUsersData = userState.usersDataSource.filter(
      item => item.jobId.includes(userState.searchValue) || item.realName.includes(userState.searchValue)
    )
    userState.renderUsersData = []
    scrollRenderUsers()
  },
  {
    immediate: true
  }
)

/*=============================================部门负责人=============================================*/
const getDeptAdmin = () => {
  getDeptAdminApi()
    .then(res => {
      deptAdminOptions.value = res.data.data
    })
    .catch(err => {
      toastError(err)
    })
}
const deptAdminOptions = ref<Array<Record<string, any>>>([])

onMounted(() => {
  getDeptAdmin()
})

// 更新部门主管条件
const updateDeptManagerCondition = (value: string) => {
  state.deptManager.filterName = deptManagerConditionName[value]
}

// 更新部门主管角色
const updateDeptManagerRole = (value: string) => {
  state.deptManager.roleName = deptAdminOptions.value.find(item => item.wsid === value)?.roleName
}

// 更新审批人
const updateApprover = (value: any) => {
  state.assigneeNullStrategy = AssigneeNullStrategyEnum.ADMIN_ASSIGNEE

  if (value === ApproveSettingEnum.USER) {
    state.members = []
    state.passStrategy = PassStrategyEnum.ALL_PASS
    state.approveStrategy = ApproveStrategyEnum.SYNC
  } else if (value === ApproveSettingEnum.DEPT_MANAGER) {
    state.deptManager.filterCode = ""
    state.deptManager.filterName = ""
    state.deptManager.roleWsid = ""
    state.deptManager.roleName = ""
  } else {
    state.role = {} as ApproverProps
  }
}
</script>

<style lang="less" scoped>
.approval-node-wrapper {
  .approval-node {
    height: 80px;
    box-shadow: rgba(0, 0, 0, 0.06) 0px 0px 10px 5px;
    background-color: #fff;
    border-radius: 4px;
    width: 230px;
    cursor: pointer;
    border: 1px solid transparent;

    &__content {
      height: 100%;
    }

    .node-label {
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: 500;
      color: #333;
      padding: 10px 10px 0 10px;
      .node-label__text {
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: inline-block;
      }

      .operation-icon {
        margin-right: 8px;
      }
    }

    .node-select {
      background-color: #f0f3f6;
      font-size: 14px;
      margin: 10px;
      padding: 4px 10px;
      border-radius: 4px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #81878e;
      cursor: pointer;
      span {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .node-operation {
      position: absolute;
      right: 0;
      top: -25px;
      // width: 40px;
      width: fit-content;
      height: 25px;

      i {
        padding: 2px;
        margin-left: 4px;
        border-radius: 4px;
        color: #fff;
        font-size: 12px;
      }

      .edit-node {
        background-color: #0c7ffc;
      }

      .delete-node {
        background-color: #0c7ffc;
      }
    }
  }

  .active-node {
    border: 1px solid #0c7ffc;
    box-shadow: 0px 2px 8px 0px rgba(51, 51, 51, 0.1);
  }

  .footer {
    width: 100%;
    position: absolute;
    bottom: 0;
    padding: 15px 20px;
    border-top: 1px solid #f3f3f3;
  }
}

.button-group {
  margin-bottom: 20px;
}

.operation-btn {
  padding-bottom: 20px;
  &__label {
    font-size: 14px;
    padding-bottom: 10px;
    font-weight: bold;
  }
}
.process-back {
  font-size: 14px;
  &__label {
    padding-bottom: 10px;
    font-weight: bold;
  }
}

.approver-setting {
  &__label {
    font-size: 14px;
    padding-bottom: 10px;
    font-weight: bold;
  }

  &__radio {
    margin-bottom: 10px;
  }
}

.approval-mode {
  font-size: 14px;

  &__label {
    padding-bottom: 10px;
    padding-top: 24px;
    font-weight: bold;
  }

  &__radio {
    margin-bottom: 10px;
  }
  &_select {
    width: 120px;
  }
  &_content {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
</style>

<style lang="less">
body {
  .custom-process-drawer {
    background-color: rgba(0, 0, 0, 0.3) !important;

    .el-drawer__header {
      margin-bottom: 7px !important;
    }
  }
}
</style>
