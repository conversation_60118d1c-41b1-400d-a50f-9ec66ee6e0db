<template>
  <PageContainer separate>
    <template #search>
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
          :filter-prop-options="filterPropOptions"
        />

        <CommonSelectFormItem
          v-model:model-value="searchFormState.status"
          label="订单状态"
          :options="orderStatusOptions"
        />

        <CommonSelectFormItem
          v-model:model-value="searchFormState.printInvoice"
          label="是否开票"
          :options="invoicingOptions"
        />

        <DaterangeFormItem v-model:model-value="searchFormState.createdDatetime" label="申请时间" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="orderTableRef"
        table-id="orderTableIdent"
        :table-columns="orderColumns"
        :request-api="getPrintList"
        :request-params="searchParams"
        :data-callback="dataCallback"
      >
        <template #header>
          <div style="margin-bottom: 12px">
            <BatchOperationButton
              type="primary"
              :icon="Printer"
              :disabled="!selectedRows.length"
              @click="handlePrintOrder('print')"
            >
              打印完成
            </BatchOperationButton>
            <BatchOperationButton
              type="info"
              :icon="Position"
              :disabled="!selectedRows.length"
              @click="handlePrintOrder('mail')"
            >
              邮寄完成
            </BatchOperationButton>
            <ComplexExportButton
              :table-info="orderTableRef?.tableState"
              :export-api="exportPrintList"
              filters-prefix="printWsids"
              :search-filters="searchParams.filters"
              :sorts="'-createdDatetime'"
            />
          </div>
        </template>
        <template #status="{ row }">
          <el-tag v-if="['WAIT_PRINT', 'WAITE_MAIL', 'WAITE_TAKE'].includes(row.status)" :type="tagStatus[row.status]">
            {{ status[row.status] }}
          </el-tag>
          <el-tag v-else type="info">
            {{ status[row.status] }}
          </el-tag>
        </template>

        <template #printCount="{ row }">
          <div class="print-count" @click="showPrintRecord(row)">{{ row.printCount }}</div>
        </template>

        <template #operation="{ row }">
          <TableButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Print)"
            :disabled="row.status !== 'WAIT_PRINT'"
            :tooltip="'当前状态不可打印'"
            @click="printItem(row)"
          >
            打印
          </TableButton>
        </template>
      </CommonTable>
    </template>
    <!-- 打印密码校验弹窗 -->
    <PrintPasswordDialog ref="printPasswordDialogRef" @confirm="confirm" />

    <!-- 订单记录弹窗 -->
    <OrderDetail
      :order-visible-dialog="orderVisibleDialog"
      :selected-row="selectedRow"
      @close="closeRecordDialog"
    ></OrderDetail>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref, computed, nextTick } from "vue"
import printJS from "print-js"
import { Position, Printer } from "@element-plus/icons-vue"
import {
  SearchContainer,
  CommonTable,
  PatientFilterFormItem,
  CommonSelectFormItem,
  DaterangeFormItem,
  BatchOperationButton,
  ComplexExportButton,
  TableButton,
  PageContainer
} from "@/base-components"
import { PrintPasswordDialog } from "@/page-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { getImage, getPrintPasswordStatus } from "@/interfaces"
import { useSystemStore, useUserStore } from "@/stores"
import { formatDatetime, extractErrorMsg, Message, toastError } from "@/utils"
import OrderDetail from "../components/OrderDetail.vue"
import { getPrintList, printComplete, mailComplete, exportPrintList, queryOrderFile } from "../interface"
import { invoicingOptions, filterPropOptions } from "../print-config"
import { orderColumns, orderStatusOptions, menuId } from "./config"

const { hasOperationPermission } = useUserStore()
const systemStore = useSystemStore()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  patientFilterValue: "",
  patientFilterProp: "patientName",
  createdDatetime: "",
  printInvoice: "",
  status: "WAIT_PRINT"
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/* ======================== 表格相关方法 ======================== */

const orderTableRef = ref<InstanceType<typeof CommonTable>>()

// 表格中被选中项
const selectedRows = computed(() => orderTableRef.value?.tableState?.selectedRows ?? [])

const receiveType = {
  ELECTRONIC_DISTRIBUTION: "电子分发",
  MAIL: "邮寄",
  OWN_TAKE: "自取"
}

const applySource = {
  SCENE_APPLY: "现场申请",
  WECHAT_APPOINTMENT: "微信预约",
  ALIPAY: "支付宝",
  HTML5_PAGE: "H5页面"
}

const status = {
  APPLICATION: "待审批",
  WAIT_PAYMENT: "待支付",
  WAIT_PRINT: "待打印",
  WAITE_MAIL: "待邮寄",
  WAITE_TAKE: "待取件",
  COMPLETE: "已完成",
  CANCELLATION_ORDER: "取消订单",
  APPROVE_REFUSE: "审批拒绝",
  REFUND_PAYMENT: "已退款",
  REFUND_APPLYING: "退款申请中",
  OVERDUE_CLAIM: "逾期未领取"
}
const tagStatus = {
  WAIT_PRINT: "",
  WAITE_MAIL: "success",
  WAITE_TAKE: "warning"
}

const dataCallback = (data: Array<Record<string, any>>) => {
  data.forEach(item => {
    item.receiveDatetime = formatDatetime(item.receiveDatetime)
    item.deliverGoodsDatetime = formatDatetime(item.deliverGoodsDatetime)
    item.payDatetime = formatDatetime(item.payDatetime)
    item.createdDatetime = formatDatetime(item.createdDatetime)

    item.receiveType = receiveType[item.receiveType]
    item.applySource = applySource[item.applySource]
    // item.status = status[item.status]
    item.printInvoice = item.printInvoice ? "是" : "否"
  })
  return data.map(item => ({
    ...item
  }))
}

// 打印完成、邮寄完成
const handlePrintOrder = (type: "print" | "mail") => {
  const obj = selectedRows.value.map(item => item.wsid)
  const fun = type === "print" ? printComplete(obj) : mailComplete(obj)
  systemStore.showLoading("正在处理")
  fun
    .then(() => {
      systemStore.hideLoading()
      orderTableRef.value?.refreshTableData()
      Message.success("操作成功")
    })
    .catch(err => {
      systemStore.hideLoading()
      Message.error(extractErrorMsg(err, "操作失败"))
    })
}

const printData = reactive({
  title: "",
  fileWsid: "",
  url: "",
  pdfDocuments: [{ pdfSrc: "" }]
})

const wsid = ref("")
const fileWsid = ref("")
const printPasswordDialogRef = ref()

// 单条数据打印
const printItem = async row => {
  systemStore.showLoading("加载中")
  wsid.value = row.wsid
  try {
    const status = (await getPrintPasswordStatus())?.data?.data?.status
    systemStore.hideLoading()
    if (status === "ENABLE") {
      printPasswordDialogRef.value?.openDialog()
    } else {
      printHandle()
    }
  } catch (err: any) {
    toastError(err)
  }
}

const confirm = data => {
  if (data) {
    printPasswordDialogRef.value?.close()
    printHandle()
  } else {
    Message.error("密码错误，请重试")
  }
}

const printHandle = () => {
  systemStore.showLoading("正在处理")
  queryOrderFile(wsid.value)
    .then(res => {
      systemStore.hideLoading()
      if (!res.data.data?.[0]?.fileWsid) {
        Message.warning("打印文件合成中，请稍后再试")
        return
      }
      printData.pdfDocuments = [{ pdfSrc: `/api/files/${res.data.data[0].fileWsid}` }]
      fileWsid.value = res.data.data[0].fileWsid
      getImage(fileWsid.value).then(data => {
        printJS(data)
        orderTableRef.value?.refreshTableData()
      })
    })
    .catch(err => {
      systemStore.hideLoading()
      Message.error(extractErrorMsg(err, "操作失败"))
    })
}

/* ======================== 订单记录详情 ======================== */

const selectedRow = ref()
const orderVisibleDialog = ref(false)
const showPrintRecord = row => {
  selectedRow.value = row
  nextTick(() => {
    orderVisibleDialog.value = true
  })
}
const closeRecordDialog = () => {
  orderVisibleDialog.value = false
}
</script>
<style scoped lang="scss">
.print-count {
  color: #409eff;
  cursor: pointer;
}
</style>
