import type { BaseOptionItem, TableColumnItem } from "@/types"

export const medicalInsuranceTabsConfig = [
  { label: "住院医保数据", path: "/medical-insurance-report/list" },
  { label: "导出记录", path: "/medical-insurance-report/export-record" }
]

export const medicalInsuranceDataTableColumns: Array<TableColumnItem> = [
  { type: "selection", width: 50, fixed: "left" },
  { prop: "mrNo", label: "病案号", minWidth: 180 },
  { prop: "patientName", label: "姓名", minWidth: 100 },
  { prop: "patientSex", label: "性别", minWidth: 100 },
  { prop: "inHospitalDatetime", label: "入院时间", minWidth: 180, sortable: true },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 100 },
  { prop: "outHospitalDatetime", label: "出院时间", minWidth: 180, sortable: true },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 100 },
  { prop: "reviewStatus", label: "审核状态", minWidth: 100 },
  { prop: "reviewInvalidCount", label: "审核问题数", minWidth: 100 },
  { prop: "operation", label: "操作", width: 180, fixed: "right", must: true }
]

// 医保导出记录
export const medicalInsuranceExportRecordTableColumns: Array<TableColumnItem> = [
  { type: "selection", width: 50 },
  { prop: "createdDatetime", label: "任务日期", minWidth: 300 },
  { prop: "fileNum", label: "文件数", minWidth: 100 },
  { prop: "recordNum", label: "总记录数", minWidth: 100 },
  { prop: "startDatetime", label: "任务开始时间", minWidth: 180, sortable: true },
  { prop: "endDatetime", label: "任务结束时间", minWidth: 180, sortable: true },
  { prop: "status", label: "任务状态", minWidth: 100 },
  { prop: "operation", label: "操作", width: 160, fixed: "right", must: true }
]

// 审核状态
export const reviewStatusOptions: Array<BaseOptionItem> = [
  { label: "待审核", value: "REVIEW_WAIT" },
  { label: "审核通过", value: "REVIEW_PASS" },
  { label: "审核未通过", value: "REVIEW_FAIL" }
]

export const getReviewsStatusTagType = (status: string) => {
  switch (status) {
    case "REVIEW_WAIT":
      return "warning"
    case "REVIEW_PASS":
      return "success"
    case "REVIEW_FAIL":
      return "danger"
    default:
      return "info"
  }
}

// 门急诊导出状态
export const exportStatusOptions = [
  { label: "进行中", value: "ONGOING", tagType: "warning" },
  { label: "失败", value: "FAIL", tagType: "danger" },
  { label: "成功", value: "SUCCESS", tagType: "success" }
]
