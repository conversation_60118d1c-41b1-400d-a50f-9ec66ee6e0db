import type { SearchFormConfigItem, TableColumnItem } from "@/types"

export const menuId = "/archive/third-party"

export const tableColumns: Array<TableColumnItem> = [
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "idCard", label: "身份证号", minWidth: 200, must: true },
  { prop: "mrNo", label: "病案号", minWidth: 150, must: true },
  { prop: "inpNo", label: "住院号", minWidth: 130, must: true },
  { prop: "visitId", label: "住院次数", minWidth: 100 },
  { prop: "patientSex", label: "性别", minWidth: 100 },
  { prop: "pageCount", label: "页数", minWidth: 100 },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 120 },
  { prop: "inHospitalDatetime", label: "入院时间", minWidth: 180 },
  { prop: "outHospitalDeptName", label: "出院科室", minWidth: 120 },
  { prop: "outHospitalDatetime", label: "出院时间", minWidth: 180 },
  { prop: "operation", label: "操作", width: 150, fixed: "right" }
]

export const searchFormConfig: Array<SearchFormConfigItem> = [
  { type: "patient", label: "患者信息" },
  { type: "department", label: "出院科室", prop: "outHospitalDeptWsid" },
  { type: "daterange", label: "出院时间", prop: "outHospitalDatetime" },
  { type: "visit", label: "住院次数" }
]
