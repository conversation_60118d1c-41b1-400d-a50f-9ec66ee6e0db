<!-- 修改密码弹窗 -->
<template>
  <DialogContainer
    title="提示"
    :width="450"
    :visible="state.printVisible"
    :confirm-callback="confirm"
    :cancel-callback="close"
    :close-callback="close"
    :confirm-loading="state.confirmLoading"
  >
    <template #default>
      <el-form ref="ruleFormRef" :model="state" label-position="top" :rules="rules" @submit.enter.prevent="confirm">
        <el-form-item required prop="password" label="已开启打印密码校验，请输入打印密码">
          <el-input v-model="state.password" placeholder="请输入打印密码" type="password" show-password></el-input>
        </el-form-item>
      </el-form>
    </template>
  </DialogContainer>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue"
import { DialogContainer } from "@/base-components"
import { checkPrintPassword } from "@/interfaces"
import { toastError, Message } from "@/utils"
import type { FormInstance, FormRules } from "element-plus"
const ruleFormRef = ref<FormInstance>()

const rules: FormRules = {
  password: [{ required: true, message: "请输入打印密码", trigger: "blur" }]
}

const state = reactive({
  printVisible: false,
  confirmLoading: false,
  password: ""
})

const openDialog = () => {
  state.printVisible = true
}

const close = () => {
  state.printVisible = false
  state.password = ""
}

const emits = defineEmits(["confirm"])

const confirm = async () => {
  ruleFormRef.value?.validate(async valid => {
    if (!valid) return
    try {
      if (state.confirmLoading) return
      state.confirmLoading = true
      const result = (await checkPrintPassword(state.password))?.data?.data
      emits("confirm", result)
      state.confirmLoading = false
    } catch (err: any) {
      toastError(err)
      state.confirmLoading = false
    }
  })
}

defineExpose({
  openDialog,
  close
})
</script>
