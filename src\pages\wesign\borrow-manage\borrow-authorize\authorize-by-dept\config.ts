import type { TableColumnItem } from "@/types"

// 科室授权表格字段
export const deptAuthorizationTableColumns: TableColumnItem[] = [
  { prop: "deptCode", label: "科室代码", minWidth: 100, must: true },
  { prop: "deptName", label: "科室名称", minWidth: 100, must: true },
  { prop: "secrecyGradeEnumName", label: "保密等级", minWidth: 100 },
  { prop: "authorizerName", label: "授权人", minWidth: 100 },
  { prop: "operationTime", label: "最后操作时间", minWidth: 150 },
  { prop: "operation", label: "操作", width: 140, fixed: "right", must: true }
]

// 科室已授权用户表格字段
export const deptAuthorizationUsersTableColumns: TableColumnItem[] = [
  { prop: "level", label: "保密等级", minWidth: 100 },
  { prop: "users", label: "已授权用户", minWidth: 120 }
]

// 科室已授权科室表格字段
export const deptAuthorizationDeptsTableColumns: TableColumnItem[] = [
  { prop: "level", label: "保密等级", minWidth: 100 },
  { prop: "depts", label: "已授权科室", minWidth: 100 }
]
