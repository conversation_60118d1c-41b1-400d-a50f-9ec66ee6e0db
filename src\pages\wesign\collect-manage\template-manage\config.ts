import type { TableColumnItem } from "@/types"

export const templateTableColumns: Array<TableColumnItem> = [
  { type: "selection", width: 50, fixed: "left" },
  { prop: "templateName", label: "模板名称", minWidth: 200 },
  { prop: "createTime", label: "创建时间", minWidth: 200 },
  { prop: "updateTime", label: "修改时间", minWidth: 200 },
  { prop: "status", label: "当前状态", minWidth: 100 },
  { prop: "operation", label: "操作", width: 355, fixed: "right" }
]
