import type { TableColumnItem, SearchFormConfigItem } from "@/types"

export const patientSearchFormConfig: Array<SearchFormConfigItem> = [
  { type: "patient", label: "患者信息" },
  { type: "department", label: "入院科室", prop: "inHospitalDeptWsid" },
  { type: "daterange", label: "入院日期", prop: "inHospitalDatetime" },
  { type: "visit", label: "住院次数" }
]

// table
export const patientTableColumns: TableColumnItem[] = [
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "patientSex", minWidth: 60, label: "性别", must: true },
  { prop: "mrNo", label: "病案号", minWidth: 150, sortable: true },
  { prop: "patientId", label: "患者编号", minWidth: 120 },
  { prop: "visitId", label: "住院次数", minWidth: 90 },
  { prop: "idCard", label: "身份证号", minWidth: 180 },
  { prop: "inHospitalDatetime", label: "入院日期", minWidth: 200, sortable: true },
  { prop: "inHospitalDeptName", label: "入院科室", minWidth: 100 },
  { prop: "inpatientArea", label: "病区", minWidth: 120 },
  { prop: "bedNumber", label: "病床", minWidth: 80, sortable: true },
  { prop: "diagnosis", label: "入院诊断", minWidth: 150 },
  { prop: "doctorInCharge", label: "主治医师", minWidth: 100 },
  { prop: "nurseLevel", label: "护理级别", minWidth: 120, sortable: true },
  { prop: "operation", label: "操作", width: 120, fixed: "right" }
]
