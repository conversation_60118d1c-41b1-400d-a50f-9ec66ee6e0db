import axios from "@/interfaces/axios-instance"

/**
 * @method POST
 * @desc   用户信息设置-修改密码
 */
export function updatePassword(obj) {
  const { oldPassword, newPassword } = obj
  return axios({
    method: "post",
    url: "/api/system/users/change-password",
    data: { oldPassword, newPassword }
  })
}

/**
 * @method PUT
 * @desc   用户信息设置-修改头像
 */
export function updateUserAvatar(obj) {
  const { file } = obj
  const formData = new FormData()
  formData.append("file", file)
  return axios({
    method: "put",
    url: "/api/system/users/change-avatar",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  })
}

/**
 * @method PUT
 * @desc   用户信息设置-修改手机号
 */
interface UpdatePhoneParam {
  smsCode: string
  oldPhone: string
  newPhone: string
}
export function updatePhone(obj: UpdatePhoneParam) {
  const { smsCode, oldPhone, newPhone } = obj
  return axios({
    method: "put",
    url: "/api/system/users/change-phone",
    data: {
      smsCode,
      oldPhone,
      newPhone
    }
  })
}

/**
 * @method POST
 * @desc   用户信息设置-修改打印密码
 */
export function updatePrintPassword(data) {
  return axios({
    method: "post",
    url: "/api/system/user-config/print/password",
    data: data
  })
}

/**
 * @method POST
 * @desc   用户信息设置-修改打印密码
 */
export function updatePrintPasswordStatus(status: string) {
  return axios({
    method: "post",
    url: "/api/system/user-config/print/password/status",
    data: {
      status: status
    }
  })
}

interface IUserHolidayData {
  holidayStartTime: number | null
  holidayEndTime: number | null
}

/**
 * @method POST
 * @desc   安全中心--用户设置休假日期
 */
export function updateUserHolidayApi(data: IUserHolidayData) {
  return axios({
    method: "post",
    url: "/api/system/users/set-holiday",
    data
  })
}
