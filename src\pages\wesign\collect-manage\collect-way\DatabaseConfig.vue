<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <CommonInputFormItem v-model="searchFormState.name" label="名称" />
        <CommonSelectFormItem v-model="searchFormState.triggerType" label="触发方式" :options="triggerTypeOptions" />
        <CommonSelectFormItem v-model="searchFormState.scene" label="业务类型" :options="sceneOptions" />
        <CommonSelectFormItem v-model="searchFormState.status" label="状态" :options="rowStatusOptions" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="commonTableRef"
        :table-columns="databaseTableColumns"
        :request-api="getDbList"
        :request-params="searchParams"
      >
        <template #header>
          <AddButton @click="addDataOrigin">新增</AddButton>
        </template>
        <template #triggerType="{ row }">
          {{ getTriggerTypeDesc(row.triggerType) }}
        </template>
        <template #scene="{ row }">
          {{ getSceneDesc(row.scene) }}
        </template>
        <template #dataType="{ row }">
          <span>{{ getDataTypeDesc(row.dataType) }}</span>
        </template>
        <template #querySql="{ row }">
          <el-popover
            placement="top-start"
            trigger="hover"
            :popper-style="{ background: 'rgba(0, 0, 0, 0.5)', width: 'auto', maxWidth: '600px' }"
          >
            <div class="tips-content">
              {{ row.querySql }}
            </div>
            <template #reference>
              <span class="sql-text">{{ row.querySql }}</span>
            </template>
          </el-popover>
        </template>
        <template #operation="{ row }">
          <TableButton @click="handleEdit(row)">编辑</TableButton>
          <TableButton v-if="row.triggerType !== 'BUSINESS'" @click="conduct(row)">执行一次</TableButton>
          <TableButton @click="handleRecord(row)">记录</TableButton>
          <TableButton @click="modifyStatus(row)">
            {{ row.status === "ENABLE" ? "禁用" : "启用" }}
          </TableButton>
          <TableButton @click="handleDelete(row)">删除</TableButton>
        </template>
      </CommonTable>
    </template>
  </PageContainer>

  <DialogContainer
    v-model:visible="visible"
    class="code-container-dialog"
    :title="actionType === 'edit' ? '修改数据库配置' : '添加数据库配置'"
    :width="1000"
    style="max-height: 70vh; overflow: auto"
  >
    <el-form
      ref="ruleFormRef"
      label-position="right"
      label-width="145px"
      label-suffix="："
      :model="formState"
      :rules="databaseFormRules"
    >
      <CommonInputFormItem v-model="formState.name" label="名称" prop="name" />

      <CommonRadioFormItem
        v-model="formState.triggerType"
        label="触发方式"
        prop="triggerType"
        :options="triggerTypeOptions"
      />

      <CommonSelectFormItem
        v-model="formState.systemWsid"
        label="厂商系统"
        prop="systemWsid"
        :options="systemVendorOptions"
      />

      <CommonSelectFormItem v-model="formState.scene" label="业务类型" prop="scene" :options="sceneOptions" />

      <el-form-item label="数据类型" prop="dataType">
        <template #label>
          <span>数据类型</span>
          <el-popover
            placement="right"
            width="800"
            trigger="click"
            :popper-style="{ background: 'rgba(0, 0, 0, 0.5)' }"
          >
            <el-table
              :data="dataTypeTableData"
              :loading="dataTypeTableLoading"
              style="width: 100%"
              :border="true"
              max-height="250"
            >
              <el-table-column prop="dataTypeEnumName" label="字段类型">
                <template #default="scope">
                  <span v-if="scope.row.dataTypeEnumName === 'RESULT_FIELD'">查询字段</span>
                  <span v-else>条件字段</span>
                </template>
              </el-table-column>
              <el-table-column prop="fieldName" label="字段名" />
              <el-table-column prop="typeCh" label="字段中文名" width="100px" />
              <el-table-column prop="fieldType" label="字段类型" />
              <el-table-column prop="required" label="是否必须">
                <template #default="scope">
                  <span v-if="scope.row.required">是</span>
                  <span v-else>否</span>
                </template>
              </el-table-column>
            </el-table>
            <template #reference>
              <i class="ri-question-fill" style="font-size: 18px"></i>
            </template>
          </el-popover>
          <span>：</span>
        </template>
        <el-select v-model="formState.dataType" placeholder="请选择数据类型" @change="changeDataType">
          <el-option
            v-for="item in formState.scene === 'IN_HOSPITAL' ? dataTypeOptions : filterDataTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <CommonSelectFormItem
        v-if="formState.dataType === 'MR_CONTENT'"
        v-model="formState.mrClassCode"
        label="文书分类"
        prop="mrClassCode"
        :options="mrClassOptions"
      />

      <CommonSelectFormItem
        v-model="formState.dataSourceWsid"
        label="数据源名称"
        prop="dataSourceWsid"
        :options="dataSourceTypeOptions"
      />

      <el-form-item ref="sqlLargeScreen" required label="SQL语句配置" prop="querySql" class="sql-item">
        <template #label>
          <span>SQL语句配置</span>
          <el-popover
            placement="right"
            width="800"
            trigger="click"
            :popper-style="{ background: 'rgba(0, 0, 0, 0.5)' }"
          >
            <div class="tips-content">
              <p>①sql返回列 必选包含必填字段 别名形式返回；</p>
              <p>
                ②sql的where 条件支持 【患者id】${patientId}
                ，【住院次】${patientId}，【患者编号】${patientId}，【身份证】${idCard}；例如： WHERE t.partent_id =
                '${patientId}' AND t.visit_id = '${visitId}'
              </p>
              <p>③sql支持批量查询的字段：【患者编号】${patientId} ；例如： WHERE t.inpNo in ('${inpNos}')</p>
              <p>④查询的文件，解析顺序base64->fileBlob->fileDownloadUrl->fileFtpPath</p>
            </div>
            <template #reference>
              <i class="ri-question-fill" style="font-size: 18px"></i>
            </template>
          </el-popover>
          <span>：</span>
        </template>
        <Codemirror
          v-model="formState.querySql"
          placeholder="请输入SQL语句配置"
          :tab-size="10"
          :extensions="extensions"
          :style="{
            maxHeight: isSqlFullscreen ? '100vh' : '150px',
            height: isSqlFullscreen ? '100vh' : 'auto',
            width: '100%'
          }"
        />
        <div ref="sqlFullScreen" class="fullscreen-btn">
          <i v-if="!isSqlFullscreen" title="全屏显示" class="ri-fullscreen-line" @click="sqlToggle"></i>
          <i v-else title="退出全屏" class="ri-fullscreen-exit-line" @click="sqlToggle"></i>
        </div>
      </el-form-item>

      <el-form-item>
        <p style="font-size: 12px">
          <span class="dialog-hint-red">提示：</span>
          您所配置的SQL语句，必须来源于数据类型字段
        </p>
      </el-form-item>

      <el-form-item ref="dataCleanLargeScreen" label="数据清洗" prop="sourceCode" class="sql-item">
        <Codemirror
          v-model="formState.sourceCode"
          placeholder="请输入"
          :tab-size="10"
          :extensions="dataExtensions"
          :style="{
            maxHeight: isDataCleanFullscreen ? '100vh' : '150px',
            height: isDataCleanFullscreen ? '100vh' : 'auto',
            width: '100%'
          }"
        />
        <div ref="dataCleanFullScreen" class="fullscreen-btn">
          <i v-if="!isDataCleanFullscreen" title="全屏显示" class="ri-fullscreen-line" @click="dataCleanToggle"></i>
          <i v-else title="退出全屏" class="ri-fullscreen-exit-line" @click="dataCleanToggle"></i>
        </div>
      </el-form-item>

      <CommonRadioFormItem
        v-model="formState.resultDataType"
        :options="resultDataTypeOptions"
        prop="resultDataType"
        label="返回数据类型"
        placeholder="请选择返回数据类型"
      />

      <CommonSelectFormItem
        v-if="formState.resultDataType === 'DATA_KEY_VALUE'"
        v-model="formState.templateWsid"
        label="关联模板"
        prop="templateWsid"
        :options="templateOptions"
      />

      <CommonSelectFormItem v-model="formState.charset" label="编码格式" prop="charset" :options="charsetOptions" />

      <CommonRadioFormItem
        v-if="formState.triggerType === 'TIME'"
        v-model="formState.extractType"
        label="采集模式"
        prop="extractType"
        :options="extractTypeOptions"
      />

      <template v-if="formState.extractType === 'INCREMENT'">
        <CommonInputFormItem
          v-model="formState.incrementFieldKey"
          label="自增key"
          prop="incrementFieldKey"
          placeholder="请输入自增key"
        />

        <CommonRadioFormItem
          v-model="formState.incrementFieldType"
          label="自增字段类型"
          prop="incrementFieldType"
          :options="incrementTypeOptions"
          @change="() => (formState.incrementLastValue = '')"
        />

        <CommonInputFormItem
          v-if="formState.incrementFieldType === 'NUMBER'"
          v-model="formState.incrementLastValue"
          label="最近自增值"
          prop="incrementLastValue"
          placeholder="请输入最近自增值"
        />

        <el-form-item v-if="formState.incrementFieldType === 'TIME'" label="最近自增值" prop="incrementLastValue">
          <el-date-picker
            v-model="formState.incrementLastValue"
            placeholder="请选择最近自增时间"
            type="datetime"
            value-format="x"
          />
        </el-form-item>

        <el-form-item label="增幅" prop="incrementDelta">
          <el-input v-model.trim="formState.incrementDelta" type="string" placeholder="请输入增幅">
            <template v-if="formState.incrementFieldType === 'TIME'" #append>秒</template>
          </el-input>
        </el-form-item>
      </template>

      <template v-if="formState.extractType === 'FIXED_TIME_RANGE'">
        <CommonInputFormItem
          v-model="formState.fixedTimeFieldKey"
          label="固定时间自增key"
          prop="fixedTimeFieldKey"
          placeholder="请输入自增key"
        />

        <CommonSelectFormItem
          v-model="formState.fixedTimeRange"
          :options="fixedTimeRangeOptions"
          label="时间范围"
          prop="fixedTimeRange"
          placeholder="请选择时间范围"
        />
      </template>

      <el-form-item v-if="formState.triggerType === 'TIME'" label="采集频率(cron)" prop="cron">
        <el-input v-model="formState.cron" placeholder="0 0/10 * * * ?" />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="closeDialog()">取消</el-button>
      <el-button :loading="testLoading" @click="handleTest">测试数据读取</el-button>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </template>
  </DialogContainer>

  <!-- 记录弹窗 -->
  <Record :record-visible-dialog="recordVisibleDialog" :selected-row="selectedRow" @close="closeRecordDialog"></Record>
</template>

<script setup lang="ts">
import { reactive, ref, nextTick } from "vue"
import { java } from "@codemirror/lang-java"
import { sql } from "@codemirror/lang-sql"
import { useFullscreen } from "@vueuse/core"
import { Codemirror } from "vue-codemirror"
import {
  SearchContainer,
  DialogContainer,
  CommonTable,
  AddButton,
  TableButton,
  PageContainer,
  CommonSelectFormItem,
  CommonInputFormItem,
  CommonRadioFormItem
} from "@/base-components"
import { TabsRouter } from "@/page-components"
import { rowStatusOptions } from "@/configs"
import { useCommonOptions, useTableSearch } from "@/hooks"
import useFormSetting from "@/hooks/useFormSetting_v2"
import { getAllTemplate, getMrClassList } from "@/interfaces"
import { useSystemStore } from "@/stores"
import { SystemPrompt, Message, extractErrorMsg, toastError } from "@/utils/message-tool"
import Record from "./components/record.vue"
import {
  tabsRouterList,
  databaseTableColumns,
  getDataTypeDesc,
  charsetOptions,
  databaseFormRules,
  dataTypeOptions,
  filterDataTypeOptions,
  myTheme,
  triggerTypeOptions,
  sceneOptions,
  resultDataTypeOptions,
  extractTypeOptions,
  getTriggerTypeDesc,
  getSceneDesc,
  fixedTimeRangeOptions,
  incrementTypeOptions
} from "./config"
import {
  getDbList,
  modifyIntegrations,
  addIntegrations,
  editIntegrations,
  getFieldDicts,
  getCollectSystemList,
  dbTestSelectOne,
  triggerJobApi,
  getAllDataSources,
  type IntegrationsData
} from "./interface"
import type { FormInstance } from "element-plus"

const systemStore = useSystemStore()

/* ======================== 页面加载时获取选项 ======================== */

// 获取所有厂商系统选项
const { options: systemVendorOptions } = useCommonOptions({
  getOptionsApi: getCollectSystemList,
  labelAlias: "systemName",
  valueAlias: "wsid"
})

// 获取所有关联模板选项
const { options: templateOptions } = useCommonOptions({
  getOptionsApi: getAllTemplate,
  labelAlias: "templateName",
  valueAlias: "templateWsid"
})

// 获取所有文书分类选项
const { options: mrClassOptions } = useCommonOptions({
  getOptionsApi: getMrClassList,
  labelAlias: "mrClassName",
  valueAlias: "mrClassCode"
})

// 获取所有数据源选项
const { options: dataSourceTypeOptions } = useCommonOptions({
  getOptionsApi: getAllDataSources,
  labelAlias: "name",
  valueAlias: "wsid"
})

/* ======================== 搜索 ======================== */

const searchFormState = reactive({
  name: "",
  status: "",
  triggerType: "",
  scene: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

/* ======================== 表格 ======================== */

const commonTableRef = ref<InstanceType<typeof CommonTable>>()

// 执行一次
function conduct(row) {
  systemStore.showLoading()
  triggerJobApi(row.wsid)
    .then(res => {
      systemStore.hideLoading()
      commonTableRef.value?.refreshTableData()
      if (res.data?.message) {
        Message.success(res.data.message)
      }
    })
    .catch(err => {
      systemStore.hideLoading()
      toastError(err, "操作失败")
    })
}
/* ======================== 弹窗编辑 ======================== */

const ruleFormRef = ref<FormInstance>()
const testLoading = ref(false)

const formInitialValue = {
  targetWsid: "",
  systemWsid: "", // 厂商系统
  dataType: "", // 数据类型
  mrClassCode: "", // 文书分类
  dataSourceWsid: "", // 数据源id
  name: "",
  querySql: "",
  cron: "", // 采集频率
  resultDataType: "FILE", // 返回数据类型
  templateWsid: "", // 关联模板
  charset: "", // 编码格式
  sourceCode: "",
  triggerType: "TIME", // 触发方式
  scene: "", // 业务类型
  extractType: "ALL", // 采集模式
  incrementFieldKey: "", // 自增key
  fixedTimeFieldKey: "", // 固定时间自增key
  incrementFieldType: "TIME", // 自增key类型
  incrementLastValue: "", // 自增key值
  incrementDelta: "", // 自增步长
  fixedTimeRange: "" // 时间范围
}

const { visible, actionType, formState, showForm, closeForm } = useFormSetting(formInitialValue)

// 测试数据库
const handleTest = () => {
  if (!ruleFormRef.value) return
  ruleFormRef.value.validate(valid => {
    if (!valid) return

    // 测试前判断数据源是否有效
    const dataSourceTypeItem = dataSourceTypeOptions.value.find(item => item.value === formState.dataSourceWsid)
    if (!dataSourceTypeItem) return Message.error("数据源不存在")

    testLoading.value = true
    dbTestSelectOne({ dataSourceWsid: formState.dataSourceWsid, querySql: formState.querySql })
      .then(() => Message.success("测试成功"))
      .catch(err => Message.error(extractErrorMsg(err, "测试失败")))
      .finally(() => (testLoading.value = false))
  })
}

const handleSave = () => {
  if (!ruleFormRef.value) return
  ruleFormRef.value?.validate(async valid => {
    if (!valid) return
    if (!systemVendorOptions.value.find(option => option.value === formState.systemWsid)) {
      return Message.error("该厂商系统已不存在或已被禁用，请重新选择")
    }
    if (!dataSourceTypeOptions.value.find(option => option.value === formState.dataSourceWsid)) {
      return Message.error("数据源不存在")
    }
    try {
      systemStore.showLoading("请稍后")
      const requestParams: IntegrationsData = {
        integrationWsid: formState.targetWsid,
        systemWsid: formState.systemWsid,
        triggerType: formState.triggerType,
        dataType: formState.dataType,
        mrClassCode: formState.dataType === "MR_CONTENT" ? formState.mrClassCode : "", // 数据类型为文书类型时，才传递mrClassCode参数
        cron: formState.cron,
        scene: formState.scene,
        extractType: formState.extractType,
        fixedTimeFieldKey: formState.fixedTimeFieldKey,
        incrementFieldKey: formState.incrementFieldKey,
        incrementFieldType: formState.incrementFieldType,
        incrementLastValue: formState.incrementLastValue,
        incrementDelta: formState.incrementDelta,
        fixedTimeRange: formState.fixedTimeRange,
        dbConfig: {
          name: formState.name,
          querySql: formState.querySql,
          dataSourceWsid: formState.dataSourceWsid,
          resultDataType: formState.resultDataType,
          templateWsid: formState.templateWsid,
          charset: formState.charset
        },
        sourceCode: formState.sourceCode
      }

      if (actionType.value === "edit") {
        await editIntegrations(requestParams)
        Message.success("修改成功")
      } else {
        await addIntegrations(requestParams)
        Message.success("添加成功")
      }
      systemStore.hideLoading()
      closeDialog()
      commonTableRef.value?.refreshTableData()
    } catch (error: any) {
      systemStore.hideLoading()
      toastError(error, "操作失败")
    }
  })
}

// 关闭编辑弹窗
function closeDialog() {
  closeForm()
  testLoading.value = false
}

/* ================= 数据类型 ==================== */

const dataTypeTableData = ref()
const dataTypeTableLoading = ref(false)

const changeDataType = () => {
  const type = formState.dataType || null
  dataTypeTableLoading.value = true
  getFieldDicts({
    filters: `type=${type}`
  })
    .then(res => {
      dataTypeTableData.value = res.data.data
      dataTypeTableLoading.value = false
    })
    .catch(() => {
      dataTypeTableLoading.value = false
    })
}

/* ================= 行操作 ==================== */

// 切换启用&禁用状态
const modifyStatus = row => {
  const nextStatus = row.status === "DISABLE" ? "ENABLE" : "DISABLE"
  if (nextStatus === "ENABLE") {
    modifyDataSourcesInfo(row.targetWsid, nextStatus)
  } else {
    SystemPrompt(`您确定要禁用“${row.name}”吗？`).then(() => {
      modifyDataSourcesInfo(row.targetWsid, nextStatus)
    })
  }
}

// 删除
function handleDelete(row) {
  SystemPrompt(`您确定要删除“${row.name}”吗？`).then(() => {
    modifyDataSourcesInfo(row.targetWsid, "DEL")
  })
}

const modifyDataSourcesInfo = (targetWsid: string, status: "DEL" | "ENABLE" | "DISABLE") => {
  systemStore.showLoading("请稍候")
  modifyIntegrations({ targetWsid, status })
    .then(() => {
      systemStore.hideLoading()
      const message = status === "DEL" ? "删除数据成功" : "修改状态成功"
      Message.success(`${message}`)
      commonTableRef.value?.refreshTableData()
    })
    .catch(error => [systemStore.hideLoading(), toastError(error, "操作失败")])
}

const handleEdit = row => {
  changeDataType()
  showForm("edit", row)
  // @ts-ignore
  formState.incrementLastValue = Number(formState.incrementLastValue)
}

const addDataOrigin = () => {
  changeDataType()
  showForm("add")
}

/* ======================== 记录弹窗 ======================== */
const recordVisibleDialog = ref(false)
const selectedRow = ref()
const handleRecord = row => {
  selectedRow.value = row
  nextTick(() => {
    recordVisibleDialog.value = true
  })
}
const closeRecordDialog = () => {
  recordVisibleDialog.value = false
}

/* ======================== 代码块 ======================== */

// SQL语句配置
const extensions = [sql(), myTheme]

const sqlLargeScreen = ref(null)
const sqlFullScreen = ref(null)
const { toggle: sqlToggle, isFullscreen: isSqlFullscreen } = useFullscreen(sqlLargeScreen)

// 数据清洗
const dataExtensions = [java(), myTheme]

const dataCleanLargeScreen = ref(null)
const dataCleanFullScreen = ref(null)
const { toggle: dataCleanToggle, isFullscreen: isDataCleanFullscreen } = useFullscreen(dataCleanLargeScreen)
</script>

<style lang="less" scoped>
:deep(.el-form-item) {
  margin-bottom: 20px !important;
}

:deep(.el-form-item__content) {
  flex-wrap: nowrap;
}

.sql-item {
  :deep(.el-form-item__content) {
    align-items: baseline;
    position: relative;
  }
}

.tips-content {
  color: #fff;
}
:deep(.cm-editor) {
  border: 1px solid #dcdfe6;
}

:deep(.cm-focused) {
  outline: none !important;
}

:deep(.cm-lineNumbers) {
  border-right: 1px solid #dcdfe6;
}

.code-container-dialog {
  :deep(.el-dialog__body) {
    max-height: 75vh;
    overflow-y: auto;
  }
}

.fullscreen-btn {
  position: absolute;
  cursor: pointer;
  right: 10px;
  top: 15px;
  line-height: 1;
  transform: translateY(-50%);
  i {
    opacity: 0.5;
    font-size: 20px;
    color: #333;
  }
  &:hover {
    i {
      opacity: 1;
    }
  }
}
.sql-text {
  width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
