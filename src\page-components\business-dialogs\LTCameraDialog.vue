<!-- 良田拍摄 -->
<template>
  <DialogContainer v-model:visible="visible" title="拍摄" :width="870" :close-callback="closeDialog">
    <div class="camera-root">
      <div class="preview-container">
        <div class="preview-wrapper">
          <!-- 当前预览图像 -->
          <ImageEditor
            v-if="previewState.preview"
            ref="ImageEditorRef"
            :image-base64="editBase"
            @save-image="handleEdit"
          />
          <EmptyContent v-else desc="请点击拍摄按钮拍摄当前图像" />

          <!-- 缩略图列表 -->
          <div
            v-if="previewState.previewList.length > 0"
            class="preview-thumb-list"
            :style="{ height: previewState.isShowThumb ? '100px' : '0px' }"
          >
            <i
              class="thumb-control"
              :class="previewState.isShowThumb ? 'ri-arrow-down-s-fill' : 'ri-arrow-up-s-fill'"
              @click="previewState.isShowThumb = !previewState.isShowThumb"
            ></i>
            <div class="thumb-list">
              <div
                v-for="thumb in previewState.previewList"
                :key="thumb.id"
                class="thumb-item"
                @click="previewState.preview = thumb"
              >
                <div v-if="thumb.id === previewState.preview?.id" class="check-line">
                  <i class="ri-check-line"></i>
                </div>

                <img :src="thumb.base64" />
              </div>
            </div>
          </div>
        </div>
        <!-- 操作区域 -->
        <div class="action-container">
          <div class="left-actions">
            <el-tooltip content="放大" placement="top">
              <img
                :class="!previewState.preview ? 'disabled' : ''"
                :src="ZoomInIcon"
                @click="() => ImageEditorRef?.zoomIn()"
              />
            </el-tooltip>
            <el-tooltip content="缩小" placement="top">
              <img
                :class="!previewState.preview ? 'disabled' : ''"
                :src="ZoomOutIcon"
                @click="() => ImageEditorRef?.zoomOut()"
              />
            </el-tooltip>
            <el-tooltip content="左旋转" placement="top">
              <img
                :class="!previewState.preview ? 'disabled' : ''"
                :src="RotateLeftIcon"
                @click="() => ImageEditorRef?.rotateLeft()"
              />
            </el-tooltip>
            <el-tooltip content="右旋转" placement="top">
              <img
                :class="!previewState.preview ? 'disabled' : ''"
                :src="RotateRightIcon"
                @click="() => ImageEditorRef?.rotateRight()"
              />
            </el-tooltip>
          </div>
          <div class="right-actions">
            <el-tooltip content="裁剪" placement="top">
              <img
                :src="CropIcon"
                :class="!previewState.preview ? 'disabled' : ''"
                @click="() => ImageEditorRef?.toggleCrop()"
              />
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <img :src="DeleteIcon" :class="!previewState.preview ? 'disabled' : ''" @click="handleDelete" />
            </el-tooltip>
          </div>
        </div>
      </div>

      <!-- 影响区域 -->
      <div class="camera-container">
        <img v-if="cameraData" :src="cameraData" class="camera-img" />
        <div v-else class="loading-container">
          <el-icon class="is-loading" size="24" color="#7bbcff"><Loading /></el-icon>
          <div class="loading-hint">高拍仪加载中，请稍候</div>
        </div>
      </div>
    </div>

    <template #footer>
      <el-button :loading="takeLoading" type="primary" @click="() => takePhoto(true)">拍摄</el-button>
      <el-button :disabled="takeLoading" :loading="submitLoading" type="primary" @click="handleSubmit">完成</el-button>
    </template>
  </DialogContainer>
</template>

<script setup lang="ts">
import { onBeforeUnmount, ref, reactive, computed, onMounted, nextTick } from "vue"
import { cloneDeep, debounce } from "lodash-es"
import { v4 as uuid } from "uuid"
import { Loading } from "@element-plus/icons-vue"
import { DialogContainer, EmptyContent } from "@/base-components"
import CropIcon from "@/assets/svg/camera/crop.svg"
import DeleteIcon from "@/assets/svg/camera/delete.svg"
import RotateLeftIcon from "@/assets/svg/camera/rotate-left.svg"
import RotateRightIcon from "@/assets/svg/camera/rotate-right.svg"
import ZoomInIcon from "@/assets/svg/camera/zoom-in.svg"
import ZoomOutIcon from "@/assets/svg/camera/zoom-out.svg"
import { base64ToBlob, Message, SystemAlert } from "@/utils"
import ImageEditor from "../ImageEditor.vue"

const ImageEditorRef = ref()

interface WebSocketMessage {
  function: string
  value?: string
  datacallback?: boolean
  device?: number
  resolution?: number
}

const props = defineProps({
  multiple: { type: Boolean, default: true },
  submitLoading: { type: Boolean, default: false },
  maxCount: { type: Number, default: 10 },
  defaultRotate: { type: Number, default: 90 }
})
const emits = defineEmits(["submit"])

const takeLoading = ref(false)
const visible = ref(false)
const cameraData = ref("")
const prefix = "data:image/jpeg;base64,"
let webSocket: WebSocket | null = null

const previewState = reactive({
  isShowThumb: true,
  preview: null as null | Record<string, any>,
  previewList: [] as Array<Record<string, any>>
})

// WebSocket连接状态
const isConnectLoading = ref(false)
const isShowError = ref(false)

onBeforeUnmount(() => {
  if (webSocket) {
    webSocket.close()
    webSocket = null
  }
})

const cameraLoading = ref(false)
function openDialog() {
  previewState.preview = null
  previewState.previewList = []
  cameraData.value = ""

  cameraLoading.value = true
  connectWebSocket()
}

function connectWebSocket() {
  if (!webSocket) {
    isConnectLoading.value = true
    webSocket = new WebSocket("ws://127.0.0.1:9000")
  } else {
    webSocket.send(JSON.stringify({ function: "InitDevs", device: 0, resolution: 0, datacallback: true }))
  }

  webSocket.onopen = function () {
    isShowError.value = false
    isConnecting()
    if (webSocket) {
      // 初始化设备
      webSocket.send(JSON.stringify({ function: "InitDevs", device: 0, resolution: 0, datacallback: true }))
    }
  }

  webSocket.onerror = function () {
    cameraLoading.value = false
    showError()

    if (webSocket) webSocket.close()
  }

  webSocket.onclose = function () {
    cameraLoading.value = false
    clearWebsocket()
  }

  webSocket.onmessage = function (event) {
    handleMessage(event)
  }
}

function handleMessage(event: MessageEvent) {
  // 只处理JSON格式的消息
  let data: WebSocketMessage
  try {
    data = JSON.parse(event.data)
  } catch {
    // 兼容老协议或非JSON消息
    return
  }

  isConnecting()

  // 初始化设备后打开摄像头
  if (data.function === "InitDevs" && data.datacallback) {
    webSocket &&
      webSocket.send(JSON.stringify({ function: "OpenCamera", device: 0, resolution: 0, datacallback: true }))
  } else if (data.function === "OpenCamera" && data.datacallback) {
    if (data.value === "failed") {
      showError()
    } else {
      isShowError.value = false
      isConnectLoading.value = false
      cameraLoading.value = false
      visible.value = true
    }
  }
  // 处理拍照结果
  else if (data.function === "ScanImage" && data.value) {
    let imageBase64 = data.value

    if (shouldSave.value) {
      const item = { base64: prefix + imageBase64, id: uuid() }
      previewState.preview = cloneDeep(item)
      editBase.value = prefix + imageBase64

      if (props.multiple) {
        previewState.previewList.unshift(cloneDeep(item))
      }
    } else if (previewState.preview) {
      previewState.preview.base64 = prefix + imageBase64
      editBase.value = prefix + imageBase64
    }
    takeLoading.value = false
  }
  // 处理实时预览图像
  if (data.function === "ImageCallback") {
    cameraData.value = prefix + data.value
  }
}

// 清除ws
function clearWebsocket() {
  isConnectLoading.value = false
  if (webSocket) {
    webSocket.close()
    webSocket = null
  }
}

// 判断高拍仪设备是否断开（如果3s内没有数据传输，则视为断开连接）
const isConnecting = debounce(() => disconnect(), 3000)
function disconnect() {
  if (visible.value) showError()
}

function closeDialog() {
  // 关闭摄像头预览
  if (webSocket?.readyState === 1) {
    webSocket?.send(JSON.stringify({ function: "CloseCamera", device: 0 }))
  }
  visible.value = false
}

const shouldSave = ref(false)
function takePhoto(isSave: boolean) {
  if (props.multiple && previewState.previewList.length >= props.maxCount)
    return Message.warning(`一次最多可以拍摄${props.maxCount}张图片`)
  shouldSave.value = isSave
  webSocket?.send(JSON.stringify({ function: "ScanImage", imagepath: "", colorize: 0, type: true }))
  takeLoading.value = true
}

function handleSubmit() {
  if (props.multiple) {
    if (previewState.previewList.length === 0) return Message.warning("暂无拍摄数据，请先进行拍摄")
    const files = previewState.previewList.map(item => {
      const blob = base64ToBlob(item.base64)
      if (blob)
        return {
          base64: item.base64,
          raw: new File([blob], "拍摄数据.jpeg", { type: "image/png" }),
          url: URL.createObjectURL(blob),
          name: item.id + "png"
        }
    })
    emits("submit", files)
  } else {
    if (!previewState.preview) return Message.warning("暂无拍摄数据，请先进行拍摄")
    const blob = base64ToBlob(previewState.preview?.base64)
    if (!blob) return
    const file = {
      base64: previewState.preview?.base64,
      raw: new File([blob], "拍摄数据.jpeg", { type: "image/png" }),
      url: URL.createObjectURL(blob),
      name: previewState.preview?.id + "png"
    }
    emits("submit", file)
  }
}

function showError() {
  if (isShowError.value) return
  isShowError.value = true
  SystemAlert("高拍仪连接失败，请检查高拍仪").then(() => {
    visible.value = false
    isShowError.value = false
    cameraLoading.value = false
    clearWebsocket()
  })
}

const editBase = ref("")
function handleEdit(base64) {
  if (props.multiple)
    previewState.previewList.forEach(item => {
      if (item.id === previewState.preview?.id) item.base64 = base64
    })
  else if (previewState.preview) previewState.preview.base64 = base64
}
function handleDelete() {
  previewState.previewList = previewState.previewList.filter(item => item.id !== previewState.preview?.id)
  previewState.preview = previewState.previewList.length > 0 ? previewState.previewList[0] : null
}

defineExpose({ openDialog, closeDialog, cameraLoading })
</script>

<style lang="less" scoped>
.camera-root {
  display: flex;
  gap: 20px;
  height: 600px;
  .preview-container {
    flex: 1;
    min-width: 0px;

    .action-container {
      display: flex;
      justify-content: space-between;
      margin-top: 15px;
      color: rgba(10, 22, 51, 0.6);
      .left-actions {
        display: flex;
        gap: 16px;
        img {
          cursor: pointer;
          width: 24px;
          height: 24px;
        }
        .disabled {
          cursor: not-allowed;
        }
      }
      .right-actions {
        display: flex;
        gap: 16px;
        img {
          width: 24px;
          height: 24px;
          cursor: pointer;
        }
        .disabled {
          cursor: not-allowed;
        }

        .is-disabled {
          cursor: not-allowed;
        }
      }
    }
  }
  .preview-wrapper {
    height: calc(100% - 40px);
    border: 1px solid rgba(10, 22, 51, 0.2);
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    .preview-thumb-list {
      position: absolute;
      bottom: 0px;
      width: 100%;
      height: 100px;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      gap: 10px;
      transition: height 0.2s;
      .thumb-control {
        position: absolute;
        background: rgba(0, 0, 0, 0.5);
        top: -20px;
        right: 14px;
        color: #fff;
        z-index: 999;
        width: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 20px;
        cursor: pointer;
      }
      .thumb-list {
        margin: 10px;
        width: 100%;
        display: flex;
        overflow-x: auto;
        gap: 10px;
        overflow-y: hidden;
        &::-webkit-scrollbar-thumb {
          background: #f3eeee80;
        }
      }
      .thumb-item {
        flex-shrink: 0;
        height: calc(100% - 5px);
        object-fit: contain;
        cursor: pointer;
        position: relative;
        width: 50px;
        object-fit: contain;
        display: block;
        justify-content: center;
        align-items: center;
        gap: 10px;
        .check-line {
          position: absolute;
          top: 0px;
          right: 0px;
          color: #fff;
          width: 25px;
          height: 25px;
          background: linear-gradient(225deg, #3860f4, #3860f4 50%, transparent 50%, transparent 100%);
          .ri-check-line {
            position: absolute;
            top: -3px;
            right: 0px;
            height: 14px;
          }
        }
        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
    }
  }
  .camera-container {
    flex: 1;
    min-width: 0px;
    border: 1px solid rgba(10, 22, 51, 0.2);
    border-radius: 4px;
    height: calc(100% - 40px);

    .camera-img {
      padding: 0px 16px;
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      object-fit: contain;
    }

    .loading-container {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .is-loading {
        height: 108px;
      }
      .loading-hint {
        color: #aaa;
      }
    }
  }
}

.preview {
  flex: 1;
  min-height: 0px;
  padding: 16px;
  box-sizing: border-box;
  max-width: 100%;
  object-fit: contain;
}
</style>
