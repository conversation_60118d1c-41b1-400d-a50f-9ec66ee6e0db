<template>
  <div class="container" :style="{ width: props.width }">
    <div class="header">
      <div class="title">{{ props.title }}</div>

      <!-- tab操作栏 -->
      <div class="tab-container">
        <!-- 类别选择 -->
        <div v-if="props.showType" class="type-container">
          <el-tabs v-model="activeName" class="tabs" @tab-click="handleClick">
            <el-tab-pane
              v-for="item in props.tabsData"
              :key="item.key"
              :label="item.value"
              :name="item.key"
            ></el-tab-pane>
          </el-tabs>

          <el-dropdown :teleported="false" @command="$event => command($event)">
            <el-button text :icon="h('i', { class: 'ri-arrow-down-s-line', style: { 'font-size': '16px' } })">
              更多
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="item in props.dropdownData"
                  :key="item.key"
                  :style="{
                    color: activeName === item.key ? '#3860f4' : ''
                  }"
                  :command="item.key"
                >
                  {{ item.value }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
        <!-- 日期选择框 -->
        <div v-if="props.showDate" class="date-picker">
          <el-radio-group v-model="date" @change="handleChange">
            <el-radio-button label="DAY">今日</el-radio-button>
            <el-radio-button label="WEEK">本周</el-radio-button>
            <el-radio-button label="MONTH">本月</el-radio-button>
          </el-radio-group>
          <el-date-picker
            v-model="selectedDateRange"
            type="daterange"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :shortcuts="shortcuts"
            value-format="x"
            :disabled-date="disabledDate"
            @change="changeDateRange"
          />
        </div>

        <!-- 刷新按钮 -->
        <el-button
          :icon="h('i', { class: 'ri-refresh-line', style: { 'font-size': '16px' } })"
          circle
          @click="refreshData"
        />
      </div>
    </div>

    <slot name="content"></slot>
  </div>
</template>

<script setup lang="ts">
import { ref, h, computed } from "vue"
import moment from "moment"
import { shortcuts } from "@/configs/options"

interface DropdownDataItem {
  value?: string
  key?: string
}

interface ChartContainerProp {
  width?: string | number
  showType?: boolean
  showDate?: boolean
  name?: string //
  title?: string //标题
  tabsData?: Array<DropdownDataItem> //tab数据
  dropdownData?: Array<DropdownDataItem> //下拉数据
}

const props = withDefaults(defineProps<ChartContainerProp>(), {
  width: 0,
  showType: true,
  showDate: true
})

const emits = defineEmits(["updateDate", "updateType", "refresh", "operation"])

/*========================= 日期范围数据及操作 ======================= */

// 选择日期范围
const selectedDateRange = ref<[number, number]>([Date.now(), Date.now()])

const disabledDate = computed(() => {
  return (time: Date) => {
    return time.getTime() > Date.now()
  }
})
const date = ref("DAY")
const dateFormatter = ref("DAY")
const dateType = ref("group")
const timeList = ref([] as string[]) //统计图x轴数据

const handleChange = val => {
  dateFormatter.value = val
  handleDateRangeChange(val, activeName.value)
}
// 选择今日/本周/本月
const handleDateRangeChange = (val: string, code?: string) => {
  let startTime = moment()
  activeName.value = code || ""
  switch (val) {
    case "DAY": {
      // 当日
      startTime = moment().startOf("day")
      timeList.value = getTimeList(24, startTime.toDate().getTime(), 3600000, "HH:mm")
      break
    }
    case "WEEK": {
      // 本周
      startTime = moment().startOf("isoWeek")
      timeList.value = getTimeList(7, startTime.toDate().getTime(), 3600000 * 24, "MM-DD")
      break
    }
    case "MONTH": {
      // 本月
      startTime = moment().startOf("month")
      const year = moment().startOf("month").toDate().getFullYear()
      const month = moment().startOf("month").toDate().getMonth()
      // 获取当前月份的天数
      const days = getMonthDays(year, month + 1)
      timeList.value = getTimeList(days, startTime.toDate().getTime(), 3600000 * 24, "MM-DD")
      break
    }
    default: {
      startTime = moment().startOf("day")
      timeList.value = getTimeList(24, startTime.toDate().getTime(), 3600000, "HH:mm")
      break
    }
  }
  selectedDateRange.value = [startTime.toDate().getTime(), new Date().getTime()]
  dateType.value = "group"
  emits("updateDate", val, dateType.value, val, timeList.value, selectedDateRange.value, props.name, activeName.value)
}

// 选择具体范围
const changeDateRange = val => {
  date.value = ""
  //获取某一天所在月份的第一天
  // const firstDay = moment(val[0]).startOf("month").toDate().getTime()
  // const days = (val[1] - firstDay) / (3600000 * 24)

  const days = (val[1] - val[0]) / (3600000 * 24)
  const years = Math.floor(days / 365)
  const months = Math.floor(days / 31)
  console.log(`output->days`, days)
  // 超过一年 x轴按年展示
  if (days / 365 > 1) {
    // date.value = "YEAR"
    dateFormatter.value = "YEAR"
    timeList.value = getTimeList(years, val[0], 3600000 * 24 * 365, "YYYY")
  } else if (days / 31 > 1) {
    dateFormatter.value = "OVERMONTH"
    // date.value = "OVERMONTH"
    //超过一个月 按月份展示
    timeList.value = getTimeList(months, val[0], 3600000 * 24 * 30, "YYYY-MM", true)
  } else if (months <= 1 && days > 0) {
    dateFormatter.value = "MONTH"
    timeList.value = getTimeList(Math.floor(days), val[0], 3600000 * 24, "MM-DD")
  } else if (days <= 0) {
    dateFormatter.value = "DAY"
    timeList.value = getTimeList(24, val[0], 3600000, "HH:mm")
  }

  dateType.value = "range"
  emits(
    "updateDate",
    date.value,
    dateType.value,
    dateFormatter.value,
    timeList.value,
    val,
    props.name,
    activeName.value
  )
}

// 获取某个月份的天数
const getMonthDays = (year, month) => {
  return new Date(year, month, 0).getDate()
}

/**
 * @length x轴的长度
 * @startTime x轴的开始值
 * @unit x轴间隔值
 * @format 日期格式
 * @isMonth 是否重新计算月份天数
 */
const getTimeList = (length: number, startTime: number, unit: number, format: string, isMonth?: boolean) => {
  let timeList = [] as string[]
  timeList = new Array(length)
  for (let i = 0; i <= length; i++) {
    if (isMonth) {
      //选择日期范围时，若超过一个月，需重新计算范围内每个月份的天数
      let monthDays = 0
      const year = new Date(startTime + monthDays * i).getFullYear()
      const month = new Date(startTime + monthDays * i).getMonth()
      monthDays = getMonthDays(year, month + 1) * 3600000 * 24
      timeList[i] = moment(startTime + monthDays * i).format(format)
    } else {
      timeList[i] = moment(startTime + unit * i).format(format)
    }
  }
  return timeList
}

/*========================= tab数据及操作 ======================= */

const activeName = ref("")

const handleClick = val => {
  const key = val.props ? val.props.name : val
  activeName.value = key
  emits("updateType", date.value, dateType.value, dateFormatter.value, timeList.value, "", props.name, key)
}

const command = val => {
  activeName.value = val
  emits("updateType", date.value, dateType.value, dateFormatter.value, timeList.value, "", props.name, val)
}

//刷新数据 回到默认（今日）状态
const refreshData = () => {
  date.value = "DAY"
  let startTime = moment().startOf("day")
  timeList.value = getTimeList(24, startTime.toDate().getTime(), 3600000, "HH:mm")
  emits(
    "updateDate",
    "DAY",
    "group",
    "DAY",
    timeList.value,
    [startTime.toDate().getTime(), new Date().getTime()],
    props.name,
    activeName.value
  )
}

defineExpose({
  refreshData,
  handleDateRangeChange,
  handleClick
})
</script>

<style lang="less" scoped>
.container {
  // background: #fff;
  // padding: 20px;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  .title {
    font-size: 16px;
    font-weight: bold;
  }

  .tab-container {
    display: flex;
    align-items: center;
  }
}

.type-container {
  width: 359px;
  height: 40px;
  background: #eff2f7;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-right: 10px;

  :deep(.el-tabs__header) {
    margin-bottom: 0;
  }

  :deep(.el-tabs__nav-wrap)::after {
    display: none !important;
  }
}

.date-picker {
  display: flex;
  align-items: center;
  width: 462px;
  margin-right: 10px;

  :deep(.el-radio-group) {
    margin-bottom: 0 !important;
    margin-right: 10px;
    flex-wrap: nowrap;
    padding: 5px 10px;
    background-color: #eff2f7;
    border-radius: 4px;
  }
  :deep(.el-input__wrapper) {
    width: 256px;
    height: 40px;
  }
  :deep(.el-radio-button__inner) {
    border: none;
    background-color: transparent;
    box-shadow: none;
    &:hover {
      color: #3860f4;
    }
  }
  :deep(.el-radio-group) {
    .is-active {
      background-color: #3860f4;
      border-radius: 4px;
      color: #fff;
      &:hover {
        .el-radio-button__inner {
          color: #fff;
        }
      }
    }
  }
}
.tabs {
  :deep(.el-tabs__item) {
    padding-left: 9px;
    &:hover {
      color: #3860f4;
    }
  }
  :deep(.el-tabs__item.is-active) {
    color: #3860f4;
    font-weight: 500;
  }
  :deep(.el-tabs__active-bar) {
    background-color: #3860f4;
    bottom: 4px;
  }
}
</style>
