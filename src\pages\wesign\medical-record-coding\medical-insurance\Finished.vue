<template>
  <PageContainer separate>
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchContainer @query-btn-click="handleQuery" @reset-btn-click="handleReset">
        <PatientFilterFormItem
          v-model:filter-prop="searchFormState.patientFilterProp"
          v-model:filter-value="searchFormState.patientFilterValue"
          label="患者信息"
        />

        <DepartmentFormItem v-model:model-value="searchFormState.outHospitalDeptWsid" label="出院科室" />

        <DaterangeFormItem v-model:model-value="searchFormState.outHospitalDatetime" label="出院时间" />

        <el-form-item label="主治医师">
          <el-input v-model="searchFormState.attendingDoctor" />
        </el-form-item>

        <DaterangeFormItem v-model:model-value="searchFormState.catalogTime" label="编码时间" />
      </SearchContainer>
    </template>

    <template #table>
      <CommonTable
        ref="catalogedTableRef"
        table-id="medicalInsuranceCatalogedTableIdent"
        :table-columns="catalogedControlColumns"
        :request-api="getCatalogListData"
        :request-params="catalogedParams"
        :data-callback="dataCallback"
      >
        <template #operation="{ row }">
          <TableButton v-if="hasOperationPermission(menuId, MenuOperationEnum.Verify)" @click="check(row)">
            校验
          </TableButton>
          <TableButton
            v-if="hasOperationPermission(menuId, MenuOperationEnum.Catalog)"
            :disabled="row.isArchive"
            @click="catalogItem(row)"
          >
            修改
          </TableButton>
        </template>
      </CommonTable>
    </template>

    <!-- 评分校验 -->
    <ScoreDrawer
      title="质控结果"
      :drawer-visible="scoreDrawerVisible"
      :close="closeScoreDrawer"
      :drawer-data="drawerData"
      :score-drawer-data="scoreDrawerData"
      :inp-no="inpNo"
      :total-score="totalScore"
      :update-drawer-data="updateDrawerData"
      :drawer-able="true"
    />
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref, computed } from "vue"
import { useRouter } from "vue-router"
import {
  SearchContainer,
  PatientFilterFormItem,
  CommonTable,
  TableButton,
  DaterangeFormItem,
  DepartmentFormItem,
  PageContainer
} from "@/base-components"
import { TabsRouter, ScoreDrawer } from "@/page-components"
import { getOutpatientValidateInfoApi } from "../interface"
import { catalogedControlColumns, tabsRouterList, menuId } from "./config"
import { getCatalogListData, getGradeData } from "./interface"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { useUserStore } from "@/stores"
import { formatDatetime } from "@/utils"

const { hasOperationPermission } = useUserStore()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFormState = reactive({
  patientFilterValue: "",
  patientFilterProp: "patientName",
  outHospitalDeptWsid: "",
  outHospitalDatetime: "",
  attendingDoctor: "",
  catalogTime: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFormState)

const catalogedParams = computed(() => {
  return { ...searchParams, catalogStatus: "CATALOGED", scenarioType: "MEDICAL_INSURANCE_IN" }
})

/* ======================== 表格相关方法 ======================== */

const dataCallback = (data: Array<Record<string, any>>) => {
  return data.map(item => ({
    ...item,
    inHospitalDatetime: formatDatetime(item.inHospitalDatetime),
    outHospitalDatetime: formatDatetime(item.outHospitalDatetime),
    catalogTime: formatDatetime(item.catalogTime)
  }))
}

const catalogedTableRef = ref()

const router = useRouter()
// 编目
const catalogItem = row => {
  router.push({
    path: "/catalog-preview",
    query: {
      appScenario: "MISL_ENCODE",
      businessDataWsid: row.inpNo,
      returnUrl: "/coding/medical-insurance/finished",
      actionType: "edit"
    }
  })
}

const scoreDrawerVisible = ref(false)
const drawerData = ref<Array<Record<string, any>>>([])
const scoreDrawerData = ref<Array<Record<string, any>>>([])
const pdfKey = ref()
const inpNo = ref("")
const totalScore = ref()
// 校验问题
const check = row => {
  scoreDrawerVisible.value = true
  inpNo.value = row.inpNo
  // getGradeData({
  getOutpatientValidateInfoApi({
    appScenario: "MISL_ENCODE",
    registerNo: row.inpNo
  }).then(res => {
    drawerData.value = res.data.data.deducts
    scoreDrawerData.value = drawerData.value
    // pdfKey.value = res.data.data.pdfKey
    totalScore.value = res.data.data.totalScore
  })
}

const closeScoreDrawer = () => {
  scoreDrawerVisible.value = false
}

const updateDrawerData = (val: string) => {
  scoreDrawerData.value = drawerData.value.filter(item => val.includes(item.controlLevelEnum))
}
</script>
