<!-- eslint-disable vue/no-v-html -->
<template>
  <span v-html="html"></span>
</template>

<script setup lang="ts">
import { computed } from "vue"

interface HightLightProps {
  value: string
  keyword: string
  color?: string
}
const props = withDefaults(defineProps<HightLightProps>(), {
  value: "",
  keyword: "",
  color: "#409eff"
})

const html = computed(() => {
  return props.keyword
    ? props.value.replaceAll(props.keyword, `<span style='color:${props.color}'>${props.keyword}</span>`)
    : props.value
})
</script>
