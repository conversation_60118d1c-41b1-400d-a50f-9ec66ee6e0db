<template>
  <PageContainer separate>
    <!-- 头部搜索 -->
    <template #search>
      <TabsRouter :tabs-config="tabsRouterList" />
      <SearchForm
        :form-config="sealingApplySearchFormConfig"
        :form-state="searchFilterForm"
        @query-btn-click="handleQuery"
        @reset-btn-click="handleReset"
      />
    </template>

    <!-- 封存申请表格 -->
    <template #table>
      <CommonTable
        ref="sealingApplyTableRef"
        table-id="sealingApplyTable"
        :table-columns="sealingTableColumns"
        :request-api="getSealingListApi"
        :request-params="searchParams"
      >
        <template #inHospitalDatetime="{ row }">
          <span>{{ formatDatetime(row.inHospitalDatetime) }}</span>
        </template>
        <template #outHospitalDatetime="{ row }">
          <span>{{ formatDatetime(row.outHospitalDatetime) }}</span>
        </template>
        <template #sealingStatus="{ row }">
          <span>{{ getSealingStatusDesc(row.sealingStatus) }}</span>
        </template>
        <template #operation="{ row }">
          <TableButton
            v-if="
              ['SEALED', 'REQUESTED_UNBLOCK'].includes(row.sealingStatus) &&
              hasOperationPermission(menuId, MenuOperationEnum.Sealing)
            "
            :disabled="row.sealingStatus === 'REQUESTED_UNBLOCK'"
            @click="showUnblockApplyDialog(row)"
          >
            解封
          </TableButton>
        </template>
      </CommonTable>
    </template>

    <!-- 解封弹窗 -->
    <DialogContainer
      v-model:visible="unblockState.dialogVisible"
      title="解封申请"
      :width="800"
      :close-callback="applyUnblockCancel"
    >
      <el-form
        ref="unblockFormRef"
        label-width="140px"
        :model="unblockState.form"
        :rules="unblockFormRules"
        class="sealing-apply-form"
      >
        <el-form-item label="申请人：" prop="applicant">
          <el-input v-model="unblockState.form.applicant" />
        </el-form-item>
        <el-form-item label="申请人证件号：" prop="applicantCertificatesNo">
          <el-input v-model="unblockState.form.applicantCertificatesNo" />
        </el-form-item>
        <el-form-item label="联系方式：" prop="applicantContent">
          <el-input v-model="unblockState.form.applicantContent" />
        </el-form-item>
        <el-form-item label="与患者关系" prop="relationship">
          <el-radio-group v-model="unblockState.form.relationship">
            <el-radio label="OWN">本人</el-radio>
            <el-radio label="AGENT">代理人</el-radio>
            <el-radio label="EXTEND">继承人</el-radio>
            <el-radio label="EXTEND_AGENT">继承代理人</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="解封原因：" prop="unlockReason">
          <el-input
            v-model="unblockState.form.unlockReason"
            class="result-textarea"
            :rows="2"
            type="textarea"
            placeholder="请输入..."
          />
        </el-form-item>
        <el-form-item label="身份证明材料上传：" prop="identificationMaterials">
          <div class="identification-materials-container">
            <UploadDocument ref="UploadDocumentRef" v-model:files="unblockState.form.file" />

            <div class="identification-materials-hint">1.本人：请上传或拍摄患者本人有效身份证明材料</div>
            <div class="identification-materials-hint">
              2.代理人：请上传或拍摄患者及代理人有效身份证明，关系证明和授权委托书
            </div>
            <div class="identification-materials-hint">
              3.继承人：请上传或拍摄患者死亡证明，继承人有效身份证明及患者与继承人关系的法定证明材料
            </div>
            <div class="identification-materials-hint">
              4.继承人代理人：请上传或拍摄患者死亡证明、死亡患者法定继承人及代理人有效身份证明，死亡
              死亡患者与继承人关系法定证明，代理人与继承人的关系证明及授权委托书
            </div>
          </div>
        </el-form-item>
      </el-form>

      <!-- dialog footer -->
      <template #footer>
        <div style="display: flex">
          <el-button
            :disabled="uploadLoading || unblockState.loading"
            :loading="UploadDocumentRef?.CameraDialogRef?.cameraLoading"
            type="primary"
            @click="UploadDocumentRef?.takePhoto()"
          >
            拍摄
          </el-button>
          <div style="flex: 1; min-width: 0px">
            <el-button :disabled="uploadLoading || unblockState.loading" @click="applyUnblockCancel">取消</el-button>
            <el-button :loading="uploadLoading || unblockState.loading" type="primary" @click="applyUnblockConfirm">
              确认
            </el-button>
          </div>
        </div>
      </template>
    </DialogContainer>
  </PageContainer>
</template>

<script setup lang="ts">
import { reactive, ref, computed } from "vue"
import { cloneDeep } from "lodash-es"
import { CommonTable, TableButton, DialogContainer, PageContainer } from "@/base-components"
import { SearchForm, TabsRouter } from "@/page-components"
import { MenuOperationEnum } from "@/configs"
import { useTableSearch } from "@/hooks"
import { useUserStore } from "@/stores"
import { Message, formatDatetime, toastError, SystemAlert } from "@/utils"
import UploadDocument from "../components/upload-document.vue"
import { getSealingListApi, handleUnlockSealingApi } from "../interface"
import {
  sealingTableColumns,
  getSealingStatusDesc,
  sealingApplySearchFormConfig,
  tabsRouterList,
  unblockFormRules
} from "./config"
import type { FormInstance, UploadUserFile } from "element-plus"

const { hasOperationPermission } = useUserStore()
let menuId = "/sealing/apply"

const sealingApplyTableRef = ref<InstanceType<typeof CommonTable>>()
const UploadDocumentRef = ref()

/* ======================== 搜索相关数据及方法 ======================== */

const searchFilterForm = reactive({
  patientFilterProp: "patientName",
  patientFilterValue: "",
  secrecyGrade: "",
  outHospitalDatetime: ""
})

const { searchParams, handleQuery, handleReset } = useTableSearch(searchFilterForm, "sealingStatus=SEALED")

/* ======================== 解封模块 ======================== */

const unblockFormRef = ref<FormInstance>()
interface FileType extends UploadUserFile {
  fileWsid?: string
  fileName?: string
  wsid?: string
  filePage?: number
  source?: string
}

// 初始化表单数据
const initForm = {
  wsid: "",
  applicant: "",
  applicantCertificatesNo: "",
  applicantContent: "",
  unlockReason: "",
  relationship: "OWN",
  file: [] as Array<FileType>
}

const unblockState = reactive({
  dialogVisible: false,
  form: cloneDeep(initForm),
  loading: false
})
// 展示解封弹窗
function showUnblockApplyDialog(row) {
  unblockState.dialogVisible = true
  unblockState.form.wsid = row.wsid
}

// 取消解封弹窗
function applyUnblockCancel() {
  unblockState.dialogVisible = false
  unblockState.form = cloneDeep(initForm)
}

// 发起解封申请
function applyUnblockConfirm() {
  unblockFormRef.value?.validate(valid => {
    if (!valid) return
    const params = cloneDeep(unblockState.form)
    const files = [] as Array<FileType>
    params.file.forEach(item => {
      if (item.status === "success")
        files.push({ name: item.name, fileWsid: item.wsid, fileName: item.name, filePage: item.filePage })
    })
    if (files.length < 3) return SystemAlert("至少上传三张证明材料")

    unblockState.loading = true
    params.file = files
    handleUnlockSealingApi(params)
      .then(() => {
        Message.success("操作成功")
        sealingApplyTableRef.value?.refreshTableData()

        unblockState.dialogVisible = false
      })
      .catch(err => {
        toastError(err)
      })
      .finally(() => {
        unblockState.loading = false
      })
  })
}

/* ===================上传身份材料==================== */

// 是否有文件正在上传
const uploadLoading = computed(() => {
  const haveUpload = unblockState.form.file.findIndex(item => item.status === "uploading")
  return haveUpload !== -1
})
</script>

<style lang="less" scoped>
.sealing-apply-form {
  max-height: 620px;
  overflow-y: auto;
  padding-right: 10px;

  :deep(.el-upload) {
    width: 88px;
    height: 124px;
  }
  :deep(.el-upload-list__item) {
    width: 88px;
    height: 124px;
  }

  .identification-materials-container {
    display: flex;
    flex-direction: column;
    font-size: 14px;
    color: #f56c6c;
    line-height: 1.5;

    .upload-file {
      position: relative;
      width: 100%;

      .upload-hover {
        position: absolute;
        width: 88px;
        height: 100%;
        top: 0px;
        left: 0px;
        opacity: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        column-gap: 5px;
        color: #fff;
        font-size: 24px;
        &:hover {
          opacity: 1;
          background: rgba(0, 0, 0, 0.2);
        }
        .el-icon {
          cursor: pointer;
        }
      }

      .upload-status {
        position: absolute;
        top: 5px;
        right: 5px;
        font-size: 14px;
      }
    }
  }
}

.bigger-image-container {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  .close-bigger-image {
    position: absolute;
    right: 20px;
    top: 20px;
    font-size: 28px;
    color: #fff;
    cursor: pointer;
  }
  img {
    width: 65%;
    height: 65%;
    object-fit: contain;
  }
}
</style>
