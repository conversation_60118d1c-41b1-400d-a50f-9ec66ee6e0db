import type { TableColumnItem } from "@/types"
import type { FormRules } from "element-plus"

export const secrecySettingColumns: Array<TableColumnItem> = [
  { prop: "secrecyName", label: "密级名称", minWidth: 150 },
  { prop: "secrecyValue", label: "密级等级值", minWidth: 150 },
  { prop: "describe", minWidth: 200, label: "描述" },
  { prop: "operation", label: "操作", width: 150, fixed: "right" }
]

const secrecyNameValidator = (_rule: any, value: string, callback: (err?: Error) => void) => {
  if (value === "") {
    callback(new Error("请输入密级名称"))
  } else {
    // 先判断是否超过10个字符
    if (value.length > 10) {
      callback(new Error("长度不能超过10字符"))
    } else {
      callback()
    }
  }
}

export const secrecyFormRules: FormRules = {
  secrecyName: [{ validator: secrecyNameValidator, trigger: "blur", required: true }],
  secrecyValue: [{ required: true, message: "请输入密级等级", trigger: "blur" }],
  describe: [{ required: false, message: "请输入描述", trigger: "blur" }]
}
