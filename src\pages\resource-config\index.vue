<template>
  <Spin :spinning="systemStore.pageLoading" :tip="loadingText">
    <PageContainer>
      <div class="table-container">
        <TreeTable
          v-if="treeTableVIsible"
          ref="treeTable"
          :table-columns="menuConfigTableColumns"
          :default-expand-all="defaultExpandAll"
          :data="menuConfigData"
          row-key="id"
        >
          <template #header>
            <el-button :icon="ArrowLeft" type="primary" plain @click="toReturn">返回</el-button>

            <AddButton @click="addMenu(0)">新增</AddButton>
            <TooltipButton
              type="primary"
              tooltip="将当前已修改配置同步至数据库"
              :icon="h('i', { class: 'ri-upload-cloud-2-line' })"
              @click="handleResourceUpdate"
            >
              更新配置
            </TooltipButton>

            <el-button :icon="ArrowRight" type="primary" plain @click="switchExpandAll(false)">全部折叠</el-button>

            <el-button :icon="ArrowDown" type="primary" plain @click="switchExpandAll(true)">全部展开</el-button>

            <el-button plain @click="batchDisableMenus(0)">禁用</el-button>
            <el-button type="primary" plain @click="batchDisableMenus(1)">启用</el-button>

            <!-- <TooltipButton
          type="primary"
          plain
          tooltip="将配置变更信息同步至数据库（适用于新功能上线）"
          :icon="h('i', { class: 'ri-arrow-up-down-line' })"
          @click="handleResourceSync"
        >
          同步配置
        </TooltipButton> -->

            <!-- <TooltipButton
              tooltip="将配置重置为初始状态"
              :icon="h('i', { class: 'ri-loop-right-line' })"
              @click="handleResourceReset"
            >
              重置配置
            </TooltipButton> -->
          </template>

          <template #status="{ row }">
            <el-tag :type="row.status ? 'success' : 'danger'">
              {{ row.status ? "已启用" : "已禁用" }}
            </el-tag>
          </template>
          <template #level="{ row }">
            <span v-if="row.level === 0">模块</span>
            <span v-else>{{ row.level === 1 ? "一级菜单" : "二级菜单" }}</span>
          </template>
          <template #icon="{ row }">
            <i :class="row.icon"></i>
          </template>
          <template #operation="{ row }">
            <TableButton @click="editMenu(row)">编辑</TableButton>
            <!-- <TableButton v-if="row.level === 2" @click="editMenu(row)">删除</TableButton> -->
            <TableButton @click="switchMenuStatus(row)">{{ row.status ? "禁用" : "启用" }}</TableButton>
            <!-- <TableButton v-if="row.level === 1" @click="showIconDialog(row)">设置图标</TableButton> -->
            <TableButton
              v-if="row.level === 1 && row.index !== 0"
              tooltip="当前菜单已是第一项"
              @click="moveMenu('up', row)"
            >
              上移
            </TableButton>
            <TableButton
              v-if="row.level === 1 && row.index !== menuConfigData.length - 1"
              tooltip="当前菜单已是最后一项"
              @click="moveMenu('down', row)"
            >
              下移
            </TableButton>
            <TableButton v-if="row.level === 0" @click="addMenu(1, row)">添加菜单</TableButton>
            <TableButton v-if="row.level === 1" @click="addMenu(2, row)">添加子菜单</TableButton>
            <TableButton
              :disabled="!row.operations.length"
              tooltip="当前菜单无关联操作"
              @click="openOperationDialog(row)"
            >
              设置操作权限
            </TableButton>
          </template>
        </TreeTable>
      </div>

      <!-- 设置图标弹窗 -->
      <DialogContainer
        v-model:visible="iconDialogState.visible"
        title="设置图标"
        :confirm-callback="changeIcon"
        :width="510"
      >
        <div class="menu-list">
          <div
            v-for="item in menuIconsList"
            :key="item.class"
            class="menu-item"
            :class="iconDialogState.selectedIcon === item.class ? 'check-item' : ''"
            @click="chooseIcon(item)"
          >
            <i :class="item.class"></i>
            <i v-if="iconDialogState.selectedIcon === item.class" class="ri-check-line check-status"></i>
          </div>
        </div>
      </DialogContainer>

      <!-- 设置操作权限弹窗 -->
      <DialogContainer
        v-model:visible="operationDialogState.visible"
        title="设置操作权限"
        :width="780"
        :no-footer="true"
      >
        <BaseTable
          class="operations-table"
          :columns="operationColumns"
          :data="operationDialogState.clickRow.operations"
        >
          <template #status="{ row }">
            <el-tag :type="row.status ? 'success' : 'danger'">
              {{ row.status ? "已启用" : "已禁用" }}
            </el-tag>
          </template>
          <template #operation="{ row }">
            <TableButton @click="switchOperationsStatus(row)">{{ row.status ? "禁用" : "启用" }}</TableButton>
          </template>
        </BaseTable>
      </DialogContainer>

      <!-- 新增菜单弹窗 -->
      <DialogContainer
        v-model:visible="menuVisible"
        :title="isAddMenu ? '新增' + addMenuTitle[addMenuLevel] : '编辑' + addMenuTitle[addMenuLevel]"
        :width="700"
        :confirm-callback="menuConfirm"
        :confirm-loading="menuConfirmLoading"
      >
        <el-form
          ref="menuFormRef"
          :model="menuFormData"
          label-position="right"
          :label-width="120"
          label-suffix="："
          :rules="formRules"
        >
          <el-form-item label="菜单名称" prop="name">
            <el-input v-model.trim="menuFormData.name" placeholder="请输入菜单名称" />
          </el-form-item>

          <el-form-item v-if="addMenuLevel === 0" label="菜单路径" prop="code">
            <el-input v-model.trim="menuFormData.code" placeholder="请输入菜单路径" />
          </el-form-item>
          <el-form-item v-else label="菜单路径" prop="code">
            <el-input v-model.trim="menuFormData.code" :disabled="!isAddMenu" placeholder="请输入菜单路径" />
          </el-form-item>

          <!-- 
          <el-form-item
            v-if="selectRow?.level === 2 || (selectRow?.level === 1 && !selectRow?.children.length)"
            label="文件路径"
            prop="filePath"
          >
            <el-input v-model.trim="menuFormData.filePath" placeholder="请输入文件路径" />
          </el-form-item> -->

          <!-- 一级菜单 -->
          <el-form-item v-if="addMenuLevel === 1" label="上级模块" prop="parentCode">
            <el-select v-model="menuFormData.parentCode" placeholder="请选择上级模块">
              <el-option
                v-for="option in parentGroupIdOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>

          <!-- 二级菜单 -->
          <el-form-item v-if="addMenuLevel === 2" label="上级模块" prop="parentGroupId">
            <el-select v-model="menuFormData.parentGroupId" placeholder="请选择上级模块" @change="selectGroup">
              <el-option
                v-for="option in parentGroupIdOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item v-if="addMenuLevel === 2" label="上级菜单" prop="parentCode">
            <el-select v-model="menuFormData.parentCode" placeholder="请选择上级模块">
              <el-option
                v-for="option in parentCodeList"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item v-if="addMenuLevel === 1" label="图标" prop="iconCode">
            <el-input placeholder="点击右侧按钮选择图标" :value="menuFormData.iconCode" class="input-with-select">
              <template #append>
                <el-button :icon="Tools" @click="setIcon" />
              </template>
            </el-input>
          </el-form-item>

          <!-- 是否隐藏菜单 -->
          <el-form-item label="隐藏菜单" prop="hideMenu">
            <el-radio-group v-model="menuFormData.hideMenu">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </DialogContainer>
    </PageContainer>
  </Spin>
</template>

<script setup lang="ts">
import { ref, computed, reactive, h, toRaw, onMounted, nextTick, watch } from "vue"
import { useRouter } from "vue-router"
import { Spin } from "ant-design-vue"
import { cloneDeep, set } from "lodash-es"
import { Tools, ArrowLeft, ArrowRight, ArrowDown } from "@element-plus/icons-vue"
import {
  PageContainer,
  TooltipButton,
  TreeTable,
  TableButton,
  DialogContainer,
  BaseTable,
  AddButton
} from "@/base-components"
import {
  updateSystemResourceConfig,
  getSystemResourceConfig,
  addMenuApi,
  editMenuApi,
  updateMenuStatusApi
} from "@/interfaces"
import { initMenuData, allMenuData } from "@/pages/wesign/sub-routers"
import { useSystemStore } from "@/stores"
import { SystemAlert, SystemPrompt, toastError } from "@/utils"
import { menuConfigTableColumns, menuIconsList, operationColumns, formRules } from "./config"

const router = useRouter()

const systemStore = useSystemStore()

let loadingTimer

const loadingText = ref("")

watch(
  () => systemStore.pageLoading,
  val => {
    if (!val) {
      clearInterval(loadingTimer)
      return
    }
    loadingText.value = systemStore.loadingText
    loadingTimer = setInterval(() => {
      loadingText.value = loadingText.value.endsWith("...") ? loadingText.value.slice(0, -3) : loadingText.value + "."
    }, 500)
  }
)

const toReturn = () => {
  router.push("/home")
}

const menuConfigData = ref(cloneDeep(systemStore.menus))

const menuCount = computed(() => menuConfigData.value.length)

// 切换菜单状态
function switchMenuStatus(row) {
  const nextStatus = row.status ? 0 : 1
  const nextStatusText = row.status ? "禁用" : "启用"
  SystemPrompt(`是否确认${nextStatusText}菜单"${row.name}"，该菜单关联子菜单及功能将会同步${nextStatusText}?`).then(
    () => {
      row.status = nextStatus
      row.children.forEach(item => {
        item.status = nextStatus
      })
      // 若二级菜单全部禁用，一级菜单也要随之禁用；若二级菜单有一项启用，一级菜单也要随之启用
      menuConfigData.value.forEach(menu => {
        const enabledChildMenus = menu.children.filter(item => item.status)
        if (menu.children.length) {
          if (enabledChildMenus.length) menu.status = 1
          else menu.status = 0
        }
      })
    }
  )
}

// 菜单顺序调整
function moveMenu(type: "up" | "down", row) {
  const currentIndex = menuConfigData.value.findIndex(item => item.id === row.id)
  const targetIndex = type === "up" ? currentIndex - 1 : currentIndex + 1
  menuConfigData.value.splice(currentIndex, 1)
  menuConfigData.value.splice(targetIndex, 0, row)
  menuConfigData.value.forEach((item, index) => {
    item.index = index
  })
}

/*========================= 设置图标 =========================*/

const iconDialogState = reactive({
  visible: false,
  activeMenu: null as null | Record<string, any>,
  selectedIcon: ""
})

// 在弹窗内选择图标
function chooseIcon(icon) {
  iconDialogState.selectedIcon = icon.class
}

// 打开弹窗
function showIconDialog(row) {
  iconDialogState.visible = true
  iconDialogState.selectedIcon = row.icon
  iconDialogState.activeMenu = row
}

// 点击确认修改图标
function changeIcon() {
  if (iconDialogState.activeMenu) iconDialogState.activeMenu.icon = iconDialogState.selectedIcon
  if (menuVisible.value) menuFormData.iconCode = iconDialogState.selectedIcon
  iconDialogState.visible = false
}

/*========================= 设置操作权限弹窗 =========================*/
const operationDialogState = reactive({
  visible: false,
  clickRow: {} as Record<string, any>
})

// 打开操作权限弹窗
function openOperationDialog(row) {
  operationDialogState.clickRow = row
  operationDialogState.visible = true
}

// 修改操作权限
function switchOperationsStatus(row) {
  SystemPrompt(`是否确认${row.status ? "禁用" : "启用"}操作"${row.name}"`).then(() => {
    row.status = row.status ? 0 : 1
    console.log(`output->menuConfigData.value`, menuConfigData.value)
  })
}
/*========================= table 顶部操作 =========================*/

const defaultExpandAll = ref(false)
const treeTableVIsible = ref(true)
const treeTable = ref()

// 展开/收起所有
const switchExpandAll = val => {
  defaultExpandAll.value = val
  treeTableVIsible.value = false
  nextTick(() => {
    treeTableVIsible.value = true
  })
}

const selectedRows = computed(() => treeTable.value?.tableState?.selectedRows ?? [])

// 批量禁用/启用
const batchDisableMenus = async status => {
  console.log(`output->selectedRows`, selectedRows.value)

  SystemPrompt(`是否确认${status === 0 ? "禁用" : "启用"}已勾选菜单`).then(async () => {
    try {
      await updateMenuStatusApi({
        codes: selectedRows.value?.map(item => item.path) ?? [],
        status
      })
      await getMenuData()
    } catch (err: any) {
      toastError(err, "操作失败")
    }
  })
}

// 更新配置
function handleResourceUpdate() {
  updateSystemResourceConfig(JSON.stringify(menuConfigData.value))
    .then(() => {
      SystemAlert("更新配置成功，需重新登录后生效", "success")
      systemStore.$patch({ menus: cloneDeep(toRaw(menuConfigData.value)) })
    })
    .catch(err => {
      toastError(err, "更新配置失败")
    })
}

// 重置配置
function handleResourceReset() {
  updateSystemResourceConfig(JSON.stringify(allMenuData))
    .then(() => {
      SystemAlert("重置配置成功，需重新登录后生效", "success")
      systemStore.$patch({ menus: cloneDeep(allMenuData) })
      menuConfigData.value = cloneDeep(allMenuData)
    })
    .catch(err => {
      toastError(err, "重置配置失败")
    })
}

// 同步配置
function handleResourceSync() {
  updateSystemResourceConfig(JSON.stringify(initMenuData))
    .then(() => {
      SystemAlert("同步配置成功，需重新登录后生效", "success")
      systemStore.$patch({ menus: cloneDeep(initMenuData) })
      menuConfigData.value = cloneDeep(initMenuData)
    })
    .catch(err => {
      toastError(err, "同步配置失败")
    })
}

const getMenuData = async () => {
  const jsonData = (await getSystemResourceConfig())?.data?.data ?? ""
  if (jsonData) {
    const resourceConfig = JSON.parse(jsonData)
    console.log(`output->resourceConfig`, resourceConfig)
    systemStore.$patch({ menus: resourceConfig })
    menuConfigData.value = cloneDeep(resourceConfig)
  } else {
    updateSystemResourceConfig(JSON.stringify(allMenuData))
    systemStore.$patch({ menus: cloneDeep(allMenuData) })
    menuConfigData.value = cloneDeep(allMenuData)
  }
}

onMounted(async () => {
  console.log(`output->initMenuData`, initMenuData)
  console.log(`output->allMenuData`, allMenuData)
  await getMenuData()
})

/*========================= 新增菜单或分组 =========================*/
// 模块分组列表
const parentGroupIdOptions = computed(() => {
  return cloneDeep(menuConfigData.value)
    .filter(item => item.level === 0)
    .map(item => {
      return {
        label: item.name,
        value: item.id
      }
    })
})

const menuVisible = ref(false)

const addMenuLevel = ref(0) //当前选中节点的level
const isAddMenu = ref(true)
const addMenuTitle = ["模块", "菜单", "子菜单"]
const selectRow = ref<Record<string, any> | undefined>({})
const menuConfirmLoading = ref(false)

const menuFormData = reactive({
  name: "",
  code: "",
  iconCode: "",
  parentCode: "", // 上级菜单
  parentGroupId: "", // 上级模块
  level: 0,
  path: "",
  // children: [],
  // operations: [],
  // functions: [],
  status: 1,
  filePath: "",
  showIndex: 0,
  ext: {},
  hideMenu: 0
})

const addMenu = (level: number, row?: Record<string, any>) => {
  console.log(`output->row`, row)
  selectRow.value = row || {}
  isAddMenu.value = true
  menuVisible.value = true
  addMenuLevel.value = level

  menuFormData.name = ""
  menuFormData.code = ""
  menuFormData.path = ""
  menuFormData.filePath = ""
  menuFormData.iconCode = ""
  menuFormData.parentCode = ""
  menuFormData.parentGroupId = ""
  menuFormData.level = level
  menuFormData.status = 1

  if (level === 0) {
    menuFormData.showIndex = menuConfigData.value.length - 1
    menuFormData.ext = { type: "group" }
  } else {
    menuFormData.showIndex = row?.children?.length - 1 > 0 ? row?.children?.length - 1 : 0
  }
}

const editMenu = row => {
  console.log(`output->row`, row)
  selectRow.value = row
  isAddMenu.value = false
  menuVisible.value = true
  addMenuLevel.value = row.level
  for (const key in menuFormData) {
    menuFormData[key] = row[key]
  }
  menuFormData.showIndex = row.index
  menuFormData.code = row.path
  menuFormData.iconCode = row.icon
  // 自动补全上级模块和上级菜单
  if (row.level === 2) {
    menuConfigData.value.forEach(group => {
      group?.children?.forEach(level1 => {
        level1?.children?.forEach(level2 => {
          if (level2.id === row.id) {
            menuFormData.parentGroupId = group.id
            selectGroup(group.id)
          }
        })
      })
    })
  }
}

const setIcon = () => {
  iconDialogState.visible = true
  iconDialogState.selectedIcon = selectRow.value?.icon
}

// 二级菜单编辑: 选择模块后 获取模块下的菜单
const parentCodeList = ref<Array<Record<string, any>>>([])
const selectGroup = val => {
  console.log(`output->val`, val)
  parentCodeList.value = menuConfigData.value
    .find(item => item.id === val)
    ?.children?.map(item => {
      return {
        label: item.name,
        value: item.id
      }
    })
  console.log(`output->parentCodeList.value`, parentCodeList.value)
}

const menuFormRef = ref()

// 确认添加菜单节点
const menuConfirm = async () => {
  menuFormData.path = menuFormData.code
  menuFormRef.value?.validate(async valid => {
    if (!valid) return
    console.log(`output->menuFormData`, menuFormData)
    menuConfirmLoading.value = true
    const handle = isAddMenu.value ? addMenuApi : editMenuApi
    try {
      await handle(menuFormData)
      await getMenuData()
      menuVisible.value = false
      menuConfirmLoading.value = false
    } catch (error: any) {
      toastError(error, "保存失败，请重试")
      menuConfirmLoading.value = false
    }
    console.log(`output->menuConfigData.value`, menuConfigData.value)
  })
}
</script>

<style lang="less" scoped>
.common-page-container {
  height: 100vh;
  :deep(.el-dialog__body) {
    padding: 10px 20px;
  }
  .menu-list {
    display: flex;
    flex-wrap: wrap;
    column-gap: 10px;
    row-gap: 10px;
    .menu-item {
      width: 56px;
      height: 56px;
      border: 1px solid #909399;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 6px;
      position: relative;
      cursor: pointer;
      i {
        font-size: 36px;
        color: #909399;
      }

      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }
    }

    .check-item {
      background: rgba(12, 127, 252, 0.04);
      border-color: #0c7ffc;
      .check-status {
        font-size: 18px;
        position: absolute;
        right: 0px;
        top: 0px;
        color: #0c7ffc;
      }
    }
  }

  .operations-table {
    height: 300px !important;
  }
}

.table-container {
  overflow-y: auto;
  height: calc(100vh - 72px);
}
</style>
