<template>
  <el-form-item :label="props.label">
    <el-date-picker
      v-model="dateRangeValue"
      type="daterange"
      unlink-panels
      range-separator="至"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      :shortcuts="shortcuts"
      :clearable="props.isClearable"
      value-format="x"
    />
  </el-form-item>
</template>

<script setup lang="ts">
import { computed } from "vue"
import { shortcuts } from "@/configs"
import type { DateModelType } from "element-plus"

const props = withDefaults(
  defineProps<{
    label: string
    modelValue: string | [DateModelType, DateModelType]
    isClearable?: boolean
    popperOptions?: Record<string, any>
  }>(),
  {
    isClearable: true,
    popperOptions: () => {
      return {}
    }
  }
)

const emits = defineEmits(["update:modelValue"])

const dateRangeValue = computed({
  get: () => props.modelValue,
  set: val => emits("update:modelValue", val)
})
</script>
