<!-- icd名称搜索 -->
<template>
  <el-form-item :label="label">
    <el-select
      v-model="selectValue"
      :loading="loading"
      :placeholder="`请选择${label}`"
      :filterable="true"
      :filter-method="getFilterOptions"
    >
      <el-option
        v-for="option in selectOptions"
        :key="option.code"
        :label="getLabel(option)"
        :value="props.type === 'name' ? option.name : option.code"
      />
    </el-select>
  </el-form-item>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue"
import { getIcdCmListApi, getTumourApi, getIcdListApi, getIcdLevelConfigApi } from "@/interfaces"
import { BaseOptionItem } from "@/types"
import { toastError } from "@/utils"

const props = defineProps<{
  label: string
  modelValue: string
  versionType: string
  type: string
}>()

const emits = defineEmits(["update:modelValue"])

const selectValue = computed({
  get: () => props.modelValue,
  set: val => {
    emits("update:modelValue", val)
  }
})

function getLabel(option) {
  return props.type === "name" ? `${option.name}(${option.code})` : `${option.code}(${option.name})`
}
interface OptionType extends BaseOptionItem {
  code: string
  name: string
}
const loading = ref(false)
const selectOptions = ref<Array<OptionType>>([])

onMounted(() => {
  changeSelectVisible(true)
})

async function changeSelectVisible(visible) {
  if (visible) {
    getIcdVersion()
  }
}
const searchValue = ref("")
// ICD可查询数据
function getFilterOptions(query) {
  if (searchValue.value !== query) {
    searchValue.value = query
    getIcdVersion()
  }
}

function getIcdVersion() {
  loading.value = true
  getIcdLevelConfigApi({ type: props.versionType })
    .then(res => {
      const versionId = res.data.data[0]?.id || ""
      getIcdOptions(versionId)
    })
    .catch(err => {
      if (err.code !== "ERR_CANCELED") {
        loading.value = false
        toastError(err, "ICD版本查询失败")
      }
    })
}

const requestApi = computed(() => {
  if (props.versionType === "ICD-10") return getIcdListApi
  else if (props.versionType === "ICD-9-CM-3") return getIcdCmListApi
  else if (props.versionType === "ICD-O") return getTumourApi
  else return null
})

function getIcdOptions(icdVersionId) {
  const params: Record<string, any> = {
    limit: 100,
    offset: 0,
    filters: `icdVersionId=${icdVersionId}`
  }
  if (searchValue.value) {
    if (props.type === "name") params.filters += `,queryName=${searchValue.value}`
    else params.code = searchValue.value
  }
  if (requestApi.value) {
    requestApi
      .value(params)
      .then(res => {
        selectOptions.value = res.data.data.rows || []
      })
      .catch(err => {
        if (err.code !== "ERR_CANCELED") toastError(err, "获取ICD_10数据失败")
      })
      .finally(() => {
        loading.value = false
      })
  } else {
    loading.value = false
  }
}
</script>
