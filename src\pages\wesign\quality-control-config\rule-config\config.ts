import { EditorView } from "@codemirror/view"
import type { TableColumnItem } from "@/types"
import type { FormRules } from "element-plus"
export const menuId = "/quality-control-config/rule-config"

export const ruleConfigColumns: Array<TableColumnItem> = [
  { prop: "checkTypeEnum", label: "检查类型", minWidth: 100 },
  { prop: "name", label: "规则名称", minWidth: 200 },
  { prop: "ruleExpression", label: "规则表达式", minWidth: 300 },
  { prop: "qcPointEnum", label: "质控点", minWidth: 300 },
  { prop: "score", label: "规则扣分", minWidth: 100 },
  { prop: "statusText", label: "状态", minWidth: 100 },
  { prop: "operation", label: "操作", width: 200, fixed: "right" }
]

export const checkTypeOptions = [
  { value: "INTEGRITY", label: "完整性" },
  { value: "TATIONALITY", label: "合理性" },
  { value: "TIMELINESS", label: "时效性" },
  { value: "NORMATIVENESS", label: "规范性" },
  { value: "CONSISTENCE", label: "一致性" },
  { value: "LOGICALITY", label: "逻辑性" }
]

export const typeOptions = [
  {
    value: "FIRST_HOME_PAGE",
    label: "住院病案首页"
  },
  {
    value: "MEDICAL_INSURANCE",
    label: "医保编目"
  },
  {
    value: "OUT_PATIENT_FIRST_HOME_PAGE",
    label: "门急诊病案首页"
  }
]

export const qcPointOptions = [
  { value: "HOME_PAGE_CATALOG", label: "住院首页编码" },
  { value: "HOME_PAGE_SUBMIT", label: "住院病案提交" },
  { value: "HOME_PAGE_HQMS_REVIEW", label: "住院首页HQMS审核" },
  { value: "OUT_PATIENT_HOME_PAGE_CATALOG", label: "门急诊诊疗信息页编码" },
  { value: "OUT_PATIENT_HOME_PAGE_HQMS_REVIEW", label: "门急诊诊疗信息页HQMS审核" },
  { value: "MEDICAL_INSURANCE_CATALOG", label: "医保结算清单编码" },
  { value: "MEDICAL_INSURANCE_REVIEW", label: "医保结算清单审核" }
]

export const getQcPointDesc = value => {
  switch (value) {
    case "HOME_PAGE_CATALOG":
      return "住院首页编码"
    case "HOME_PAGE_SUBMIT":
      return "住院病案提交"
    case "HOME_PAGE_HQMS_REVIEW":
      return "住院首页HQMS审核"
    case "OUT_PATIENT_HOME_PAGE_CATALOG":
      return "门急诊诊疗信息页编码"
    case "OUT_PATIENT_HOME_PAGE_HQMS_REVIEW":
      return "门急诊诊疗信息页HQMS审核"
    case "MEDICAL_INSURANCE_CATALOG":
      return "医保结算清单编码"
    case "MEDICAL_INSURANCE_REVIEW":
      return "医保结算清单审核"
    default:
      return ""
  }
}

// 规则扣分校验
const ruleScoreValidator = (_rule: any, value: string, callback: (err?: Error) => void) => {
  if (value === "") {
    callback(new Error("请输入规则扣分"))
  } else {
    const reg = /^[+]?(0|([1-9]\d*))(\.\d{1})?$/
    if (reg.test(value)) {
      callback()
    } else {
      callback(new Error("请输入最多1位小数的扣分值"))
    }
  }
}

export const ruleConfigFormRules: FormRules = {
  name: [{ required: true, message: "请输入规则名称", trigger: "blur" }],
  type: [{ required: true, message: "请选择质控类型", trigger: "blur" }],
  qcPoint: [{ required: true, message: "选择质控点", trigger: "blur" }],
  ruleExpression: [{ required: true, message: "请输入规则表达式", trigger: "blur" }],
  promptContent: [{ required: false, message: "请输入提示内容", trigger: "blur" }],
  // consult: [{ required: true, message: "请输入参考", trigger: "blur" }],
  checkType: [{ required: true, message: "请选择检查类型", trigger: "blur" }],
  controlLevel: [{ required: true, message: "请选择控制级别", trigger: "blur" }],
  qcScoreStandardWsid: [{ required: true, message: "请选择评分项", trigger: "blur" }],
  score: [{ required: true, validator: ruleScoreValidator, trigger: "blur" }]
}

export const myTheme = EditorView.theme(
  {
    // 输入的字体颜色
    "&": {
      color: "#0052D9",
      backgroundColor: "#FFFFFF"
    },
    ".cm-content": {
      caretColor: "#0052D9"
    },
    // 激活背景色
    ".cm-activeLine": {
      backgroundColor: "#FFFFDD"
    },
    // 激活序列的背景色
    ".cm-activeLineGutter": {
      backgroundColor: "#FAFAFA"
    },
    //光标的颜色
    "&.cm-focused .cm-cursor": {
      borderLeftColor: "#0052D9"
    },
    // 选中的状态
    "&.cm-focused .cm-selectionBackground, ::selection": {
      backgroundColor: "#0052D9",
      color: "#dcdfe6"
    },
    // 左侧侧边栏的颜色
    ".cm-gutters": {
      backgroundColor: "#FFFFFF",
      color: "#ddd", //侧边栏文字颜色
      border: "none"
    }
  },
  { dark: true }
)
