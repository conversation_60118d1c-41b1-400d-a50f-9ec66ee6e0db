<template>
  <div class="custom-card common-box-shadow">
    <div class="custom-card-header">
      <div v-if="title" class="custom-card-title" :class="{ 'custom-card-title--sign': sign }">
        <div v-if="allowedReturn" class="return" @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回
          <span class="line"></span>
        </div>
        <div class="title-content">
          {{ title }}
          <slot name="title"></slot>
        </div>
      </div>
      <slot name="header"></slot>
    </div>
    <div class="custom-card-body">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router"
const router = useRouter()
const props = defineProps({
  sign: { type: Boolean, default: false },
  title: { type: String, default: "" },
  allowedReturn: { type: Boolean, default: false },
  returnUrl: { type: String, default: "" },
  titleStyle: { type: Object, default: () => ({}) }
})

const goBack = () => {
  // location.href = props.returnUrl
  router.push({
    path: props.returnUrl
  })
}
</script>

<style lang="less" scoped>
.custom-card {
  width: 100%;
  height: 100%;
}

.custom-card-header {
  display: flex;
  justify-content: space-between;
  height: 56px;
  line-height: 56px;
  padding: 0 16px;
  border-bottom: 1px solid rgb(225 226 230);
  box-sizing: border-box;
  align-items: center;
}

.custom-card-title {
  position: relative;
  font-size: 16px;
  font-weight: bold;
  color: #0a1633;
  display: flex;
  align-items: center;

  .return {
    // position: relative;
    // z-index: 10;
    font-weight: 500;
    font-size: 14px;
    color: #666666;
    display: flex;
    align-items: center;
    cursor: pointer;

    .line {
      width: 0px;
      height: 12px;
      border: 1px solid #ced0d6;
      margin: 0 12px;
    }
  }

  &--sign::before {
    content: "";
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: -16px;
    width: 4px;
    height: 18px;
    background-color: var(--el-color-primary);
    border: 2px;
    border-radius: 0 4px 4px 0;
  }
}

.custom-card-body {
  overflow-y: auto;
  height: calc(100% - 56px);
  padding: 16px;
  box-sizing: border-box;
}
</style>
