import type { TableColumnItem } from "@/types"

export const menuId = "/print/writeoff"

export const writeoffColumns: Array<TableColumnItem> = [
  { prop: "orderCode", label: "订单单号", minWidth: 200, must: true },
  { prop: "patientName", label: "姓名", minWidth: 100, must: true },
  { prop: "patientId", label: "患者编号", minWidth: 120, must: true },
  { prop: "patientIdCard", label: "身份证号", minWidth: 180, must: true },
  { prop: "patientOutHospitalDatetime", label: "出院日期", minWidth: 180, sortable: true },
  { prop: "duplicateNum", label: "复印份数", minWidth: 120, sortable: true },
  { prop: "pageCount", label: "总页数", minWidth: 120, sortable: true },
  { prop: "orderAmount", label: "订单金额", minWidth: 120, sortable: true },
  { prop: "createdDatetime", label: "申请时间", minWidth: 180, sortable: true, must: true },
  { prop: "operation", label: "操作", width: 100, fixed: "right", must: true }
]
