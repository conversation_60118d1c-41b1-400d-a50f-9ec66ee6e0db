import axios from "@/interfaces/axios-instance"

/**
 * @method GET
 * @desc   病案封存-申请列表
 */
export function getSealingListApi(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/document/bag/sealing/list`,
    params
  })
}

/**
 * @method POST
 * @desc   病案文档-病案解封申请
 */
export function handleUnlockSealingApi(obj) {
  const {
    wsid = "",
    applicant = "",
    applicantCertificatesNo = "",
    applicantContent = "",
    unlockReason = "",
    relationship = "OWN",
    file = []
  } = obj
  return axios({
    method: "post",
    url: `/api/sealed/document-bag/sealing/unlock`,
    data: {
      wsid,
      applicant,
      applicantCertificatesNo,
      applicantContent,
      unlockReason,
      relationship,
      file
    }
  })
}

/**
 * @method POST
 * @desc   病案封存-发出封存申请
 */
export function handleLockSealingApi(obj) {
  const {
    applicant = "",
    applicantCertificatesNo = "",
    applicantContent = "",
    unlockType = "LIMIT_TIME",
    unlockDatetime = "",
    additionalCases = true,
    lockReason = "",
    wsid = "",
    file = [],
    relationship = "OWN",
    signFileBytes = undefined
  } = obj

  return axios({
    method: "post",
    url: `/api/sealed/document-bag/sealing/lock`,
    data: {
      wsid,
      applicant,
      applicantCertificatesNo,
      applicantContent,
      unlockType,
      unlockDatetime,
      additionalCases,
      lockReason,
      file,
      relationship,
      signFileBytes
    }
  })
}

/**
 * @method GET
 * @desc   病案封存--待审批列表
 */
export function getSealingApprovalListApi(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/sealed/document-bag/sealing/wait-approve/list`,
    params
  })
}

/**
 * @method GET
 * @desc   病案封存--已审批列表
 */
export function getSealedApprovalListApi(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/sealed/document-bag/sealing/approved/list`,
    params
  })
}

/**
 * @method GET
 * @desc   病案文档-解封详情查询
 */
export function getSealingDetailApi(obj) {
  const { id } = obj
  return axios({
    method: "get",
    url: `/api/sealed/document-bag/sealing/detail/${id}`
  })
}

/**
 * @method POST
 * @desc   病案文档-病案封存/解封审批
 */
export function handleApprovalSealingApi(obj) {
  const { id, status = "", secretKey = "", processTaskId = "", desc = "" } = obj
  return axios({
    method: "post",
    url: `/api/sealed/document-bag/sealing/approval`,
    data: {
      id,
      status,
      secretKey,
      processTaskId,
      desc
    }
  })
}

/**
 * @method GET
 * @desc   病案封存-封存历史列表查询
 */
export function getSealingHistoryListApi(params: IPaginationRequestParams) {
  return axios({
    method: "get",
    url: `/api/sealed/document-bag/sealing/approval/history-list`,
    params
  })
}

interface ExportSealHistoryParams {
  id: string
  type: string
}
/**
 * @method GET
 * @desc   病案封存-下载封存历史列表
 */
export const exportSealHistoryApi = (params: ExportSealHistoryParams) => {
  return axios({
    method: "get",
    url: "/api/sealed/document-bag/sealing/history/download-file",
    params,
    responseType: "arraybuffer"
  })
}

/**
 * @method GET
 * @desc   病案封存-打印封存历史列表
 */
export const printSealHistoryApi = (id: string) => {
  return axios({
    method: "get",
    url: `/api/sealed/document-bag/sealing/history/print`,
    params: { id },
    responseType: "arraybuffer"
  })
}

/**
 * @method GET
 * @desc   病案封存-封存秘钥查看
 */
export function getSealingKetApi(obj) {
  const { id = "" } = obj
  return axios({
    method: "get",
    url: `/api/sealed/document-bag/sealing/show-key/${id}`
  })
}

/**
 * @method POST
 * @desc   病案封存-上传文件
 */
export function uploadSealingFile(obj) {
  const { file, flag = false } = obj
  const formData = new FormData()
  formData.append("file", file)
  formData.append("flag", flag)
  return axios({
    method: "post",
    url: `/api/sealed/document-bag/sealing/upload-file`,
    data: formData
  })
}

/**
 * @method POST
 * @desc   病案封存-获取辅助资料pdf
 */
export function getSealSupportFileApi(params: Record<string, any>) {
  return axios({
    method: "get",
    url: `/api/sealed/file/support`,
    params,
    responseType: "arraybuffer"
  })
}

/**
 * @method POST
 * @desc   病案审批-辅助材料追加
 */
export function appendSealingFile(obj) {
  const { file, id } = obj
  return axios({
    method: "post",
    url: `/api/sealed/document-bag/sealing/file/${id}`,
    data: {
      file
    }
  })
}

/**
 * @method POST
 * @desc   病案审批-重新签名
 */
export function resignSealedRecordApi(id) {
  return axios({
    method: "post",
    url: `/api/sealed/document-bag/sealing/retry-file/${id}`
  })
}

/**
 * @method post
 * @desc   病案审批-封存/解封回退
 */
export function backApprovalNodeApi(data) {
  const { processTaskId = "", processTarActId = "", desc = "" } = data
  return axios({
    method: "post",
    url: `/api/sealed/document-bag/sealing/approval/rollback`,
    data: {
      processTaskId,
      processTarActId,
      desc
    }
  })
}

// 申请发起签署
export const applySignApi = (data: Record<string, any>) => {
  return axios({
    method: "post",
    url: "/api/sealed/sign/apply",
    data: data
  })
}

// 查询封存签署结果
export const checkSealSignResultApi = (envelopeWsid: string) => {
  return axios({
    method: "get",
    url: "/api/sealed/sign/apply/result",
    params: { envelopeWsid }
  })
}

// 检查封存申请是否开启了签名
export const getSealingConfigApi = () => {
  return axios({
    method: "get",
    url: "/api/system/system-config/sealed"
  })
}
